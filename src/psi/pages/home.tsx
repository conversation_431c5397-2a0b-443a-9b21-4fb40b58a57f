import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
// import classNames from 'classnames';
import { Row, Col, Image, Spin, message } from 'antd';
import { checkPermission } from '@/utils/permission';
import { Context, Icon } from '@/components';
import { getPsiStockTotalNum, getPsiQueryAppList, updatePsiAppSort } from '@/apis';
import type { GetPsiQueryAppListItem } from '@/apis';
import { user } from '@/store';

import {
  InventoryChart,
  DataReportChart,
  SalesChart,
  PsiAllFunctionDrawer,
} from '../container/index';
import { getPageUrl } from '../utils/page-url';
import styles from './home.module.less';

function PsiHome() {
  const navigate = useNavigate();
  const [showStockTotalLoading, setShowStockTotalLoading] = useState(false);
  const [stockTotal, setStockTotal] = useState(0);

  const [allFuncVisible, setAllFuncVisible] = useState(false);
  const [showFuncLoading, setShowFuncLoading] = useState(false);
  const [funcData, setFuncData] = useState([] as GetPsiQueryAppListItem[]);
  const [recentlyUsedData, setRecentlyUsedData] = useState([] as GetPsiQueryAppListItem[]);

  useEffect(() => {
    setShowFuncLoading(true);
    getPsiQueryAppList()
      .then((res) => {
        const filterArr =
          user?.company?.status !== 1
            ? [
                'AV_001_006',
                'AV_001_007',
                'AV_001_005',
                'AV_001_002',
                'AV_001_001',
                'M_001_001',
                'AV_001_003',
                'AV_001_004',
                'AV_001_008',
              ]
            : [];
        const funcList: GetPsiQueryAppListItem[] = res.list?.filter(
          (item) => !filterArr.includes(item.permissionCode)
        );
        // const funcList: GetPsiQueryAppListItem[] = res.list || [];
        setFuncData([...funcList].sort((a, b) => a.sort - b.sort));
        setRecentlyUsedData(
          [...funcList].sort((a, b) => Number(b.updateTime || 0) - Number(a.updateTime || 0))
        );
      })
      .finally(() => {
        setShowFuncLoading(false);
      });
  }, []);

  useEffect(() => {
    setShowStockTotalLoading(true);
    getPsiStockTotalNum()
      .then((res) => {
        setStockTotal(res.stockTotal || 0);
      })
      .finally(() => {
        setShowStockTotalLoading(false);
      });
  }, []);

  const onUpdateAllFuncVisible = (val = false) => {
    setAllFuncVisible(val);
  };

  const onClickMoreRecentlyUsed = () => {
    setAllFuncVisible(true);
  };

  const onClickUsedItem = (item: GetPsiQueryAppListItem) => {
    if (!checkPermission(item.permissionCode)) {
      message.error('暂无权限');
      return;
    }
    const url = getPageUrl(item.permissionCode);
    if (!url) {
      message.error('找不到当前功能的链接');
      return;
    }
    updatePsiAppSort({ id: item.id })
      .then()
      .finally(() => {
        navigate(url);
      });
  };

  return (
    <Context
      className={styles.psiHome}
      head={
        <Context.Head
          title="进销存"
          // quickFilter={
          //   <div className={styles.headDesc}>
          //     显示：<span className={styles.headDescSpan}>首页</span>
          //   </div>
          // }
        />
      }
    >
      <div>
        <Row gutter={[20, 0]}>
          <Col md={12} xs={24}>
            <Spin spinning={showFuncLoading}>
              <div className={styles.recentlyUsed}>
                <div className={styles.recentlyUsedTitle}>
                  <div className={styles.recentlyUsedTitleText}>最近使用</div>
                  <Icon
                    name="right"
                    size={24}
                    color="#999EB2"
                    className={styles.recentlyUsedTitleIcon}
                    onClick={onClickMoreRecentlyUsed}
                  />
                </div>
                <div className={styles.recentlyUsedContent}>
                  {recentlyUsedData.map((item, index) =>
                    index < 6 ? (
                      <div
                        className={styles.recentlyUsedItem}
                        onClick={() => {
                          onClickUsedItem(item);
                        }}
                        role="presentation"
                        key={item.id}
                        // style={{
                        //   marginLeft:
                        //     index + 1 === 3 * (index + 1) - 1 ? `calc((100% - 395px) / 2)` : 0,
                        //   marginRight:
                        //     index + 1 === 3 * (index + 1) - 1 ? `calc((100% - 394px) / 2)` : 0,
                        // }}
                      >
                        <Image width={48} src={item.appIcon} preview={false} />
                        <div className={styles.recentlyUsedItemText}>{item.appName}</div>
                      </div>
                    ) : (
                      ''
                    )
                  )}
                </div>
              </div>
            </Spin>
            <SalesChart />
          </Col>
          <Col md={12} xs={24}>
            <Spin spinning={showStockTotalLoading}>
              <div className={styles.inventory}>
                <Image
                  width={60}
                  src="https://img.huahuabiz.com/user_files/1660186351587996091/1660112999461167.png"
                  preview={false}
                />
                <div className={styles.inventoryText}>
                  <div className={styles.inventoryTextNum}>{stockTotal}</div>
                  <div className={styles.inventoryTextDesc}>库存总数量</div>
                </div>
                {/* <div className={styles.inventoryTrend}>
                <i
                  className={classNames(
                    'ech-icon ech-icon__down-solid',
                    styles.inventoryTrendUpIcon,
                    styles.iconRotate
                  )}
                />
                <div>777</div>
              </div> */}
              </div>
            </Spin>
            <InventoryChart />
          </Col>
        </Row>
        <DataReportChart />
      </div>
      <PsiAllFunctionDrawer
        visible={allFuncVisible}
        options={funcData}
        onUpdateVisible={onUpdateAllFuncVisible}
      />
    </Context>
  );
}

export default PsiHome;
