.psiHome {
  background-color: transparent !important;
  overflow-x: hidden;

  :global {
    .ant-spin-nested-loading {
      min-height: inherit !important;
    }
  }

  .cardWrap {
    background-color: @white;
    border-radius: 18px;
    box-shadow: @box-shadow-base;
    width: 100%;
    padding: 20px 0 0;
  }

  .recentlyUsed {
    .cardWrap();

    min-height: 255px;
    margin-bottom: 20px;

    &Title {
      font-size: 18px;
      display: flex;
      width: 100%;
      min-height: 25px;
      padding: 0 20px;
      align-items: center;

      &Text {
        flex: 1;
      }

      &Icon {
        cursor: pointer;
      }
    }

    &Content {
      display: flex;
      flex-wrap: wrap;
      // padding-top: 6px;
      padding: 6px 0 0;

      .recentlyUsedItem {
        display: flex;
        flex-direction: column;
        width: 33.3%;
        align-items: center;
        cursor: pointer;
        padding: 10px 0;
        border-radius: 16px;

        &Text {
          font-size: 12px;
          margin-top: 8px;
        }

        &:hover {
          // background-color: rgba(217, 238, 255, 0.5);
          .recentlyUsedItemText {
            color: #006eff;
          }
        }
        // &:nth-child(3n-1) {
        //   margin-left: calc((100% - 395px) / 2);
        //   margin-right: calc((100% - 395px) / 2);
        // }
      }
    }
  }

  .inventory {
    .cardWrap();

    display: flex;
    min-height: 153px;
    margin-bottom: 16px;
    padding: 0 20px 0 25px;
    justify-content: center;
    align-items: center;

    &Text {
      min-width: 181px;
      padding: 20px;
      flex: 1;

      &Num {
        font-size: 38px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      &Desc {
        color: @text-colors[secondary];
        font-size: 14px;
      }
    }

    &Trend {
      color: @red;
      font-size: 16px;
      display: flex;
      align-items: center;

      &UpIcon {
        font-size: 32px;
      }
    }
  }
}

.headDesc {
  color: @text-colors[secondary];
  font-size: 18px;
  padding: 8px;
  // margin-left: 32px;
  &Span {
    color: @text-colors[primary];
  }
}

.iconRotate {
  margin-bottom: 10px;
  transform: rotate(180deg);
}
