/*
 * @Author: chen<PERSON><PERSON> <EMAIL>
 * @Date: 2022-07-19 17:01:57
 * @LastEditors: chenyuhao <EMAIL>
 * @LastEditTime: 2022-08-01 14:56:12
 * @FilePath: \website\src\psi\components\card.tsx
 */
import { ReactNode } from 'react';
import isNumber from 'lodash/isNumber';
import styles from './card.module.less';

interface CardProps {
  children?: ReactNode | string;
  height?: string | number | undefined;
  width?: string | number | undefined;
  style?: object;
}

function Card({ children, height, width, style }: CardProps) {
  const createStyle = (h: string | number | undefined, w: string | number | undefined) => {
    const newH = isNumber(h) ? `${h}px` : h;
    const newW = isNumber(w) ? `${w}px` : w;

    return { height: newH, width: newW, ...style };
  };
  return (
    <div className={styles.CardWrap} style={createStyle(height, width)}>
      {children}
    </div>
  );
}

Card.defaultProps = {
  children: '',
  height: '100%',
  width: '100%',
  style: {},
};

export default Card;
