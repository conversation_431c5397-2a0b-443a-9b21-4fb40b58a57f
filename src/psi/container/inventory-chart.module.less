.title {
  font-size: 18px;
  font-weight: 500;
  height: 45px;
  padding-top: 20px;
  padding-left: 20px;
}

.chartWrap {
  display: flex;
  flex-direction: row;
}

.chartBox {
  margin-top: 20px;
}

.chartBoxName {
  color: #888b98;
  font-size: 12px;
  text-align: center;
}

.chartLegend {
  display: flex;
  width: calc(100% - 170px);
  margin-top: 28px;
  padding-left: 24px;
  justify-content: center;
  flex-direction: column;

  &Item {
    display: flex;
    height: 33px;
    padding-right: 20px;
    justify-content: center;
    align-items: center;

    &:last-child {
      margin-bottom: 0;
    }

    &Dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    &Text {
      flex: 1;
      color: @text-colors[secondary];
      width: 0;

      &Text {
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

.noData {
  display: flex;
  height: calc(100% - 45px);
  justify-content: center;
  flex-direction: column;
  align-items: center;

  &Image {
    margin-bottom: 10px;
    margin-left: 50px;
  }
}

@media (min-width: 1150px) {
  .chartLegendItemNum {
    display: none;
  }
}

@media (min-width: 1370px) {
  .chartLegendItemNum {
    display: block;
  }
}
