import { useEffect, useMemo, useState, useCallback } from 'react';
import type {
  ComposeOption,
  LegendComponentOption,
  PieSeriesOption,
  TitleComponentOption,
  ToolboxComponentOption,
  TooltipComponentOption,
} from 'echarts';
import { Spin } from 'antd';

import { stockHomeSkuNum } from '@/apis';
import { Echarts, EchartsProps } from '@/components';
import { useEcharts } from '@/hooks';
import Card from '../components/card';
import styles from './inventory-chart.module.less';

type EChartsOption = ComposeOption<
  | TitleComponentOption
  | ToolboxComponentOption
  | TooltipComponentOption
  | LegendComponentOption
  | PieSeriesOption
>;

interface OptionDataItem {
  value: number;
  itemValue: number;
  name: number | string;
}

function InventoryChart() {
  const echartsRef = useEcharts();
  const [showLoading, setShowLoading] = useState(false);
  const legendColors = useMemo(() => ['#008CFF', '#16DBCC', '#FFBB38', '#FF82AC', '#BC82FF'], []);
  const [inventoryData, setInventoryData] = useState([] as OptionDataItem[]);

  useEffect(() => {
    setShowLoading(true);
    stockHomeSkuNum()
      .then((res) => {
        const numberArr = [40, 33, 28, 22, 20];
        // const numberArr = [40, 34, 28, 22, 16];
        const data = res.list.map((item, index) => ({
          value: numberArr[index],
          itemValue: item.totalNum,
          name: item.skuName,
          itemStyle: {
            color: legendColors[index],
          },
        }));
        setInventoryData(data);
        // setInventoryData([]);
      })
      .finally(() => {
        setShowLoading(false);
      });
  }, [legendColors]);

  const inventoryOption: EchartsProps['option'] = useCallback(
    () =>
      ({
        tooltip: {
          position: 'bottom',
          trigger: 'item',
          formatter(params) {
            // @ts-ignore
            return `<span style="color:#040919">数量 ${params.data.itemValue}</span><br /> <span style="color:#888b98">${params.data.name}</span>`;
          },
        },
        // legend: {
        //   left: 'center',
        //   top: 'bottom',
        //   icon: 'circle',
        // },
        grid: {
          width: '100%',
          height: '304px',
          containLabel: true,
        },
        series: [
          {
            name: '库存前五商品',
            type: 'pie',
            radius: [29, 76],
            center: ['50%', '50%'],
            roseType: 'radius',
            itemStyle: {
              // borderRadius: 5,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            data: inventoryData,
          },
        ],
      } as EChartsOption),
    [inventoryData]
  );

  return (
    <div className={styles.stock}>
      <Spin spinning={showLoading}>
        <Card height={299}>
          <div className={styles.title}>库存查询</div>
          {inventoryData.length ? (
            <div className={styles.chartWrap}>
              <div className={styles.chartBox}>
                <Echarts
                  ref={echartsRef}
                  id="psiInventoryChart"
                  option={inventoryOption}
                  style={{ height: '170px', width: '170px' }}
                />
                <div className={styles.chartBoxName}>库存前五商品</div>
              </div>

              <div className={styles.chartLegend}>
                {inventoryData.map((item, index) => (
                  <div className={styles.chartLegendItem} key={`${item.name}${Math.random()}`}>
                    <div
                      className={styles.chartLegendItemDot}
                      style={{ background: legendColors[index] }}
                    />
                    <div className={styles.chartLegendItemText} title={`${item.name}`}>
                      <div className={styles.chartLegendItemTextText}>{item.name}</div>
                    </div>
                    <div className={styles.chartLegendItemNum} title={`${item.itemValue}`}>
                      <div className={styles.chartLegendItemNum}>{item.itemValue}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className={styles.noData}>
              <img
                className={styles.noDataImage}
                src="https://img.huahuabiz.com/user_files/1659422966009985620/no-data.c0069b13.png"
                alt=""
              />
              <div className={styles.noDataText}>暂无库存信息</div>
            </div>
          )}
        </Card>
      </Spin>
    </div>
  );
}

export default InventoryChart;
