.allFunction {
  border-radius: 18px;
  background-color: white;
  width: 100%;
  padding: 20px 10px 0;

  &Item {
    width: 100%;

    &Content {
      display: flex;
      flex-wrap: wrap;

      &Item {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        margin-bottom: 24px;
        cursor: pointer;
        padding: 10px 0;
        border-radius: 16px;

        &Text {
          margin-top: 12px;
        }

        &:hover {
          background-color: rgb(217 238 255 / 50%);
        }
      }
    }
  }
}
