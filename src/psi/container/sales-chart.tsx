import { useEffect, useMemo, useState } from 'react';

import { Image, Spin } from 'antd';

import { getOrderPsiSellData } from '@/apis';
import type { GetOrderPsiSellDataRes } from '@/apis';
import { formatPrice, formatPriceToNum } from '@/utils/utils';
import { SecondaryTab } from '@/components';
import Card from '../components/card';

import styles from './sales-chart.module.less';

interface SecondaryTabOptionItem {
  label: string;
  value: number | string;
  url?: string;
  active?: boolean;
}

function SalesChart() {
  const [showLoading, setShowLoading] = useState(false);
  // filter
  const tabOption = useMemo(
    () => [
      { label: '今日', value: '1' },
      { label: '本周', value: '2' },
    ],
    []
  );
  const [activeValue, setActiveValue] = useState<number | string>('1');

  const onTabChange = (val: SecondaryTabOptionItem) => {
    setActiveValue(val.value);
  };

  const [salesState, setSalesState] = useState({
    todayData: {
      sellAmount: '0',
      buyAmount: '0',
      forPayerAmount: '0',
      forPayeeAmount: '0',
    },
    weekData: {
      sellAmount: '0',
      buyAmount: '0',
      forPayerAmount: '0',
      forPayeeAmount: '0',
    },
  } as GetOrderPsiSellDataRes);

  useEffect(() => {
    setShowLoading(true);
    getOrderPsiSellData({ type: +activeValue })
      .then((res) => {
        const { todayData, weekData } = res;
        setSalesState({
          todayData: {
            sellAmount: String(formatPriceToNum(todayData.sellAmount)) ?? '0',
            buyAmount: String(formatPriceToNum(todayData.buyAmount)) ?? '0',
            forPayerAmount: String(formatPriceToNum(todayData.forPayerAmount)) ?? '0',
            forPayeeAmount: String(formatPriceToNum(todayData.forPayeeAmount)) ?? '0',
          },
          weekData: {
            sellAmount: String(formatPriceToNum(weekData.sellAmount)) ?? '0',
            buyAmount: String(formatPriceToNum(weekData.buyAmount)) ?? '0',
            forPayerAmount: String(formatPriceToNum(weekData.forPayerAmount)) ?? '0',
            forPayeeAmount: String(formatPriceToNum(weekData.forPayeeAmount)) ?? '0',
          },
        });
      })
      .finally(() => {
        setShowLoading(false);
      });
  }, [activeValue]);

  const salesItemData = useMemo(() => {
    const salesData = activeValue === '1' ? salesState.todayData : salesState.weekData;
    return [
      {
        label: '销售金额',
        value: salesData.sellAmount,
        imageUrl: 'https://img.huahuabiz.com/user_files/1658719313431268941/165854830971780.png',
      },
      {
        label: '待付款',
        value: salesData.forPayerAmount,
        imageUrl: 'https://img.huahuabiz.com/user_files/1658719472106113170/1658548314550694.png',
      },
      {
        label: '采购金额',
        value: salesData.buyAmount,
        imageUrl: 'https://img.huahuabiz.com/user_files/1658719480179348042/1658548312148220.png',
      },
      {
        label: '待收款',
        value: salesData.forPayeeAmount,
        imageUrl: 'https://img.huahuabiz.com/user_files/1658719440611979981/1658548305792641.png',
      },
    ];
  }, [salesState, activeValue]);

  return (
    <Spin spinning={showLoading}>
      <div className={styles.salesChart}>
        <Card height={191}>
          <div className={styles.chartTitle}>
            <div className={styles.chartTitleText}>销售数据</div>
            <div className={styles.chartTitleFilter}>
              <SecondaryTab
                options={tabOption}
                value={activeValue}
                size="small"
                onClickItem={onTabChange}
              />
            </div>
          </div>
          <div className={styles.salesInfo}>
            {salesItemData.map((item) => (
              <div className={styles.salesInfoItem} key={item.label}>
                <Image preview={false} src={item.imageUrl} width={36} />
                <div className={styles.salesInfoItemText}>
                  <div className={styles.salesInfoItemTextLabel}>{item.label}</div>
                  <div className={styles.salesInfoItemTextValue}>￥{formatPrice(item.value)}</div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>
    </Spin>
  );
}

export default SalesChart;
