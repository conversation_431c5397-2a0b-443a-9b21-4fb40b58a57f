.dataReportChart {
  width: 100%;

  .chartTitle {
    display: flex;
    align-items: center;
    padding: 20px 20px 0;

    &Text {
      flex: 1;
      font-size: @font-size-xl;
    }
  }

  .chartType {
    width: 100%;
    padding: 16px 20px 0;
  }

  .noData {
    display: flex;
    width: 100%;
    height: calc(100% - 45px);
    justify-content: center;
    flex-direction: column;
    align-items: center;

    &Image {
      // margin-bottom: 10px;
      // margin-left: 50px;
    }

    &Text {
    }
  }
}
