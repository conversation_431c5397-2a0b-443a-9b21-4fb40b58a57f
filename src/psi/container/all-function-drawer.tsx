/*
 * @Author: chenyuhao <EMAIL>
 * @Date: 2022-07-22 10:11:17
 * @LastEditors: chenyuhao <EMAIL>
 * @LastEditTime: 2022-08-06 11:48:14
 * @FilePath: \website\src\psi\container\all-function-drawer.tsx
 */
// import { useState, useEffect } from 'react';
import { Image, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import { Drawer } from '@/components';
import { updatePsiAppSort } from '@/apis';
// import { getPsiQueryAppList } from '@/apis';
import type { GetPsiQueryAppListItem } from '@/apis';
import { checkPermission } from '@/utils/permission';

import { getPageUrl } from '../utils/page-url';
import styles from './all-function-drawer.module.less';

interface PsiAllFunctionDrawerProps {
  visible: boolean;
  onUpdateVisible: MultipleParamsFn<[val: boolean]>;
  options: GetPsiQueryAppListItem[];
}

function PsiAllFunctionDrawer({ visible, onUpdateVisible, options }: PsiAllFunctionDrawerProps) {
  const navigate = useNavigate();
  // const [showLoading, setShowLoading] = useState(false);
  // const [funcData, setFuncData] = useState([] as GetPsiQueryAppListItem[]);
  // useEffect(() => {
  //   if (!visible) {
  //     return;
  //   }
  //   setShowLoading(true);

  //   getPsiQueryAppList()
  //     .then((res) => {
  //       setFuncData(res.list);
  //     })
  //     .finally(() => {
  //       setShowLoading(false);
  //     });
  // }, [visible]);

  const onClose = () => {
    onUpdateVisible(false);
  };

  const onClickFuncItem = (item: GetPsiQueryAppListItem) => {
    if (!checkPermission(item.permissionCode)) {
      message.error('暂无权限');
      return;
    }
    const url = getPageUrl(item.permissionCode);
    if (!url) {
      message.error('找不到当前功能的链接');
      return;
    }
    updatePsiAppSort({ id: item.id })
      .then()
      .finally(() => {
        navigate(url);
      });
  };

  return (
    // <Spin spinning={showLoading}>
    <Drawer
      visible={visible}
      title="全部功能"
      onClose={() => {
        onClose();
      }}
    >
      {/* <div className={styles.allFunction}>
          {funcData.map((item) => (
            <div className={styles.allFunctionItem} key={item.groupName}>
              <div className={styles.allFunctionItemContent}>
                {item?.list.map((child) => (
                  <div
                    className={styles.allFunctionItemContentItem}
                    onClick={() => {
                      onClickFuncItem(child.appCode);
                    }}
                    role="presentation"
                    key={child.appCode}
                  >
                    <Image preview={false} width={48} src={child.appIcon} />
                    <div className={styles.allFunctionItemContentItemText}>{child.appName}</div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div> */}
      {options?.length ? (
        <div className={styles.allFunction}>
          <div className={styles.allFunctionItem}>
            <div className={styles.allFunctionItemContent}>
              {options.map((item) => (
                <div
                  className={styles.allFunctionItemContentItem}
                  onClick={() => {
                    onClickFuncItem(item);
                  }}
                  role="presentation"
                  key={item.id}
                >
                  <Image preview={false} width={48} src={item.appIcon} />
                  <div className={styles.allFunctionItemContentItemText}>{item.appName}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        ''
      )}
    </Drawer>
    // </Spin>
  );
}

export default PsiAllFunctionDrawer;
