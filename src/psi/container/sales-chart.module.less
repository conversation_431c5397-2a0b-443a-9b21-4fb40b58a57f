.salesChart {
  width: 100%;

  .chartTitle {
    display: flex;
    align-items: center;
    padding: 20px 20px 0;

    &Text {
      flex: 1;
      font-size: 18px;
    }
  }

  .salesInfo {
    display: flex;
    width: 100%;
    margin-top: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    flex-wrap: wrap;

    &Item {
      display: flex;
      width: 50%;
      margin-bottom: 20px;
      align-items: center;

      &Text {
        font-size: 12px;
        margin-left: 8px;

        &Label {
          color: @text-colors[secondary];
          margin-bottom: 2px;
        }
      }
    }
  }

  .noData {
    display: flex;
    width: 100%;
    height: calc(100% - 45px);
    justify-content: center;
    flex-direction: column;
    align-items: center;

    &Image {
      // margin-bottom: 10px;
      // margin-left: 50px;
    }

    &Text {
    }
  }
}
