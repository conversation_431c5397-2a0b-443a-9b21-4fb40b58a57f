/*
 * @Author: chenyuhao <EMAIL>
 * @Date: 2022-07-25 16:40:55
 * @LastEditors: chenyuhao <EMAIL>
 * @LastEditTime: 2022-10-25 16:12:04
 * @FilePath: \website\src\psi\utils\page-url.ts
 */
const pagesObject = {
  // stock_manager: '/stock',
  AV_001_006: '/stock/query',
  R_001_002: '/indent/list?orderStatus=0&region=0&open=0',
  R_001_001: '/indent/list?orderStatus=0&region=1&open=0',
  AV_001_005: '/stock/warn',
  AV_001_002: '/stock/enter',
  AV_001_001: '/stock/out',
  AV_001_007: '/stock/warehouse',
  M_001_001: '/goods/manage/index',
  AV_001_003: '/stock/allot',
  AV_001_004: '/stock/taking',
  AV_001_008: '/stock/receive',
  R_001_022: '/indent/list?stockType=2&region=0',
  R_001_021: '/indent/list?stockType=1&region=1',
};

const defaultFunctionData = [
  {
    groupName: '订单',
    groupId: 1,
    list: [
      {
        appIcon: 'https://img.huahuabiz.com/user_files/1635227019206430115/purchase%20.png',
        appName: '采购订单',
        appCode: 'buy_order',
        appId: 11,
      },
      {
        appIcon: 'https://img.huahuabiz.com/user_files/1635227034345188398/sell.png',
        appName: '销售订单',
        appCode: 'sell_order',
        appId: 12,
      },
    ],
  },
  {
    groupName: '库存',
    groupId: 2,
    list: [
      {
        appIcon: 'https://img.huahuabiz.com/PC/static/icon/stockSelect.png',
        appName: '库存查询',
        appCode: 'scm_stock_select',
        appId: 21,
      },
      {
        appIcon: 'https://img.huahuabiz.com/PC/static/icon/stockforcewarn.png',
        appName: '库存预警',
        appCode: 'stock_warn',
        appId: 22,
      },
      {
        appIcon: 'https://img.huahuabiz.com/PC/static/img/icons/inbound.png',
        appName: '入库单',
        appCode: 'stock_warehouse',
        appId: 23,
      },
      {
        appIcon: 'https://img.huahuabiz.com/PC/static/img/icons/outbound.png',
        appName: '出库单',
        appCode: 'stock_out',
        appId: 24,
      },
    ],
  },
  {
    groupName: '仓库',
    groupId: 3,
    list: [
      {
        appIcon: 'https://img.huahuabiz.com/PC/static/icon/storeManagement.png',
        appName: '仓库列表',
        appCode: 'stock_warehouse',
        appId: 31,
      },
    ],
  },
  {
    groupName: '商品',
    groupId: 4,
    list: [
      {
        appIcon: 'https://img.huahuabiz.com/PC/static/img/icons/goodsWarehouse.png',
        appName: '商品管理',
        appCode: 'goods_manage',
        appId: 41,
      },
    ],
  },
];

// @ts-ignore
const getPageUrl = (key: string) => pagesObject[key];

export { pagesObject, getPageUrl, defaultFunctionData };
