import { followUser, SearchCmsTrendResult } from '@/apis';
import { Ava<PERSON>, Button } from 'antd';
import { MouseEventHandler } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './content-item.module.less';
// eslint-disable-next-line no-unused-vars
export type SetItemFn = (data: {
  key: string;
  id: number;
  data: Record<string, unknown>;
  createName?: string;
}) => void;
export interface TopicItemProp {
  item: SearchCmsTrendResult;
  type: string;
  onJoin?: SetItemFn;
  onClick?: MouseEventHandler;
}

const stopPropagation: MouseEventHandler = (e) => {
  e.stopPropagation();
  e.preventDefault();
};
function ContentItem({ item, onJoin, onClick, type }: TopicItemProp) {
  const navigate = useNavigate();
  const handleDetail: MouseEventHandler = (e) => {
    navigate(`/home?trendsId=${item.id}`);
    onClick?.(e);
  };
  const handleFocus: MouseEventHandler = (e) => {
    if (!onJoin) return;
    stopPropagation(e);
    followUser({
      followId: item.createUser,
    }).then((res) => {
      if (res) {
        const param = {
          key: 'ContentItem',
          id: item.id,
          createName: type === 'cmsTrendsVoContentList' ? item.createName : '',
          data: { isJoin: !item.isJoin },
        };
        onJoin(param);
      }
    });
  };
  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    <div className={styles.topic} onClick={handleDetail}>
      <div className={styles.topicWrapper}>
        <Avatar size={32} src={item.createUserAvatar} className={styles.avatar} />
        <div className={styles.topicInfo}>
          <span className={styles.topicTile}>
            {type === 'cmsTrendsVoArticleList' ? item.title : item.createName}
          </span>
          <span className={styles.topicNum}>{item.content || '-'}</span>
        </div>
      </div>
      {item.isJoin ? (
        <Button
          size="small"
          shape="round"
          onClick={(e) => {
            stopPropagation(e);
          }}
        >
          已关注
        </Button>
      ) : (
        <Button size="small" danger shape="round" onClick={handleFocus}>
          关注
        </Button>
      )}
    </div>
  );
}
ContentItem.defaultProps = {
  onJoin: undefined,
  onClick: undefined,
};

export default ContentItem;
