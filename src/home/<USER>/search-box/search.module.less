@import 'styles/mixins/mixins';
@import '../add-friend/friend.module.less';

.searchBox {
  position: relative;

  &::after {
    content: '';
    display: block;
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
  }
}

.box {
  top: 64px;

  :global {
    .ant-modal-content {
      border-radius: @border-radius-base;
      background-color: #fff;
    }

    .ant-modal-close-x {
      font-size: @font-size-xl;
      height: 62px;
      line-height: 62px;
    }

    .ant-modal-body {
      padding: 0;
    }
  }
}

.head {
  padding: 24px 24px 0;
  //border-bottom: 1px solid #e5e5e6;
  position: relative;
}

.logo {
  display: inline-block;
  width: 48px;
  height: 48px;
  vertical-align: top;
}

.logoDesc {
  color: #b1b3be;
  font-size: @font-size-sm;
  line-height: 17px;
  margin-top: 9px;

  &::before,
  &::after {
    content: '';
    display: inline-block;
    width: 34px;
    margin: 0 4px;
    border-top: 1px solid #b1b3be;
    vertical-align: middle;
  }
}

.input {
  //width: 90%;
  margin-top: 10px;
  padding: 16px 0;
  border-color: transparent !important;
  box-shadow: none !important;

  :global {
    &,
    .ant-input {
      font-size: @font-size-lg;
      background-color: transparent;
    }

    .ant-input-prefix {
      margin-right: 13px;

      i {
        font-size: 21px !important;
      }
    }

    .ant-input-clear-icon {
      font-size: 18px !important;
    }
  }
}

.menu {
  color: @text-color-secondary;
  font-size: @font-size-lg;
  font-weight: 500;
  min-height: 10px;
  line-height: 22px;
  border: none;
  background-color: transparent;

  &:global {
    &:not(.ant-menu-dark) {
      > .ant-menu-item,
      > .ant-menu-submenu {
        padding: 18px 14px 15px;
        top: 0;

        &::after {
          height: 3px;
          right: 14px;
          left: 14px;
          transition: none;
          background: transparent;
        }
      }

      > .ant-menu-item:hover,
      > .ant-menu-submenu:hover,
      > .ant-menu-item-selected,
      > .ant-menu-submenu-selected {
        &::after {
          border-radius: 4px 4px 0 0;
          border-bottom: none;
          background: #f90400;
        }
      }
    }
  }
}

.hideMenu {
  :global {
    .ant-menu-item {
      display: none;
    }
  }
}

.body {
  height: 476px;
  padding: 16px 12px 0;
  border-top: 1px solid #f5f6fa;
  overflow-x: hidden;
  overflow-y: auto;
}

.group {
  margin-bottom: 16px;
  padding-bottom: 16px;
  position: relative;

  &::after {
    content: '';
    display: block;
    border-top: 1px solid #f5f6fa;
    position: absolute;
    right: 12px;
    bottom: 0;
    left: 12px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.groupTitle {
  color: @text-color-secondary;
  line-height: 20px;
  padding: 0 12px 8px;
}

.error {
  color: @text-color-secondary;
  padding-top: 200px;
  text-align: center;
}

.loadMore {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  text-align: center;
}

.drawer {
  :global {
    .ant-drawer-content {
      background: none;
    }

    .ant-drawer-header {
      display: none;
    }

    .ant-drawer-body {
      padding: 0;
    }
  }
}
