import {
  searchAll,
  searchChatGroups,
  SearchChatGroupsParams,
  SearchCmsArticle,
  searchCmsTopic,
  SearchCmsTrend,
  searchFunctions,
  searchProducts,
  searchShops,
  searchUsers,
  SearchUsersParams,
} from '@/apis';
import { CancelToken } from '@/utils/http';

export interface menuItemProp {
  key: string;
  label: string;
  search: MultipleParamsFn<[params: any, token?: CancelToken], Promise<any>>;
  infinite?: boolean;
}

// eslint-disable-next-line import/no-mutable-exports
let menus: menuItemProp[] = [
  { key: 'all', label: '全部', search: searchAll },
  {
    key: 'userList',
    label: '联系人',
    search: (params: SearchUsersParams, cancelToken?: CancelToken) =>
      searchUsers(params, cancelToken).then(({ list, nonList = [] }) => ({
        list: list.concat(nonList),
      })),
  },
  {
    key: 'groupList',
    label: '群聊',
    search: (params: SearchChatGroupsParams, cancelToken?: CancelToken) =>
      searchChatGroups(params, cancelToken).then(({ list, nonList = [] }) => ({
        list: list.concat(nonList),
      })),
  },
];
if (import.meta.env.BIZ_APP_PLATFORM_NO === '1') {
  menus = [
    ...menus,
    { key: 'cmsTopicVoList', label: '社区', search: searchCmsTopic, infinite: true },
    { key: 'cmsTrendsVoContentList', label: '动态', search: SearchCmsTrend, infinite: true },
    { key: 'cmsTrendsVoArticleList', label: '文章', search: SearchCmsArticle, infinite: true },
  ];
} else {
  menus = [
    ...menus,
    { key: 'funcList', label: '功能', search: searchFunctions },
    { key: 'shopList', label: '店铺', search: searchShops, infinite: true },
    { key: 'productList', label: '商品', search: searchProducts, infinite: true },
  ];
}

export { menus };
