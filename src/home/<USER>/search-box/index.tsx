import { HTMLAttributes, PropsWithChildren, useCallback, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import classNames from 'classnames';
// import loadable from '@loadable/component';
import { layout } from '@/store';
import Box from './box';
import styles from './search.module.less';

// const Box = loadable(() => import('./box'));

function SearchBox({
  children,
  className,
  ...props
}: PropsWithChildren<HTMLAttributes<HTMLDivElement>>) {
  const location = useLocation();
  const [renderComponent, setRender] = useState(layout.showSearch);
  const onOpen = useCallback(() => {
    setRender(true);
    layout.search();
  }, []);
  const afterClose = useCallback(() => {
    setRender(false);
  }, []);

  useEffect(() => {
    if (renderComponent) {
      layout.closeSearch();
    }
  }, [location]); // eslint-disable-line

  const classes = classNames(styles.searchBox, className);
  return (
    <div {...props} role="button" tabIndex={0} className={classes} onClick={onOpen}>
      {children}
      {renderComponent && <Box afterClose={afterClose} />}
    </div>
  );
}

SearchBox.displayName = 'SearchBox';

export default SearchBox;
