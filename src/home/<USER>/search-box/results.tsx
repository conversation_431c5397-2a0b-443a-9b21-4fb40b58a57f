import { H<PERSON><PERSON>ttributes, Mouse<PERSON>vent<PERSON><PERSON><PERSON>, useMemo, useRef, useState } from 'react';
import { <PERSON><PERSON>, Spin } from 'antd';
import { SearchChatGroupResult } from '@/apis';
import List, { ListItem, MoreClickEventHandler, SearchListType } from './list';
import { SearchItem } from './item';
import { SetItemFn } from './topic-item';
import AddGroupInfo, { GroupInfoInstance } from './add-group-info';
import AddGroupFooter from './add-group-footer';
import styles from './search.module.less';

export interface SearchGroup {
  key: SearchListType;
  label: string;
  items: SearchItem[];
}

export interface SearchResultsProps extends HTMLAttributes<HTMLDivElement> {
  keys: string[];
  groups: SearchGroup[];
  items: ListItem[];
  searchKey: string;
  loading?: boolean;
  max?: number;
  onMore?: MoreClickEventHandler;
  onSelected?: MouseEventHandler;
  onJoin?: SetItemFn;
}

function Results({
  keys,
  groups,
  items,
  loading,
  max = 3,
  onMore,
  onSelected,
  onJoin,
  searchKey,
  ...props
}: SearchResultsProps) {
  const [group, setGroup] = useState(null as null | SearchChatGroupResult);
  const infoEl = useRef(null as unknown as GroupInfoInstance);
  const children = useMemo(() => {
    const onAddGroup = (item: SearchChatGroupResult) => {
      setGroup(item);
    };

    if (keys[0] === 'all') {
      return groups.length === 0 ? (
        <div className={styles.error}>没有找到结果</div>
      ) : (
        groups.map((item) => (
          <div key={item.key} className={styles.group}>
            <h4 className={styles.groupTitle}>
              与 &quot;{searchKey}&quot; 相关的
              {item.label}
            </h4>
            <List
              type={item.key}
              items={item.items}
              max={max}
              onMore={onMore}
              onItemClick={onSelected}
              onAddGroup={onAddGroup}
              onJoin={onJoin}
            />
          </div>
        ))
      );
    }
    return items.length === 0 ? (
      <div className={styles.error}>没有找到结果</div>
    ) : (
      <List
        items={items}
        type={keys[0] as SearchListType}
        onItemClick={onSelected}
        onAddGroup={onAddGroup}
        onJoin={onJoin}
      />
    );
  }, [keys, items, onSelected, onJoin, groups, max, onMore, searchKey]);

  const onCancel = () => {
    setGroup(null);
  };

  return (
    <Spin spinning={loading}>
      <div {...props} className={styles.body}>
        {loading ? null : children}
      </div>
      <Modal
        centered
        title="加入群聊"
        visible={!!group}
        width={350}
        footer={<AddGroupFooter instance={infoEl} info={group} onCancel={onCancel} />}
        className={styles.dialog}
        onCancel={onCancel}
      >
        <AddGroupInfo ref={infoEl} info={group} />
      </Modal>
    </Spin>
  );
}

Results.displayName = 'SearchResults';

Results.defaultProps = {
  loading: undefined,
  max: undefined,
  onMore: undefined,
  onSelected: undefined,
  onJoin: undefined,
};

export default Results;
