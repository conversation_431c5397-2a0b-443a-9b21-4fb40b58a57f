import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Avatar, Col, Form, Input, Row } from 'antd';
import { AddToChatGroupProps, SearchChatGroupResult } from '@/apis';
import { Icon } from '@/components';
import { user } from '@/store';
import ChatTag from '../../components/chat-tag';
import styles from '../add-friend/friend.module.less';

export interface GroupInfoInstance {
  getValues: () => AddToChatGroupProps;
  success: () => void;
}

export interface GroupInfoProps {
  info: null | SearchChatGroupResult;
}

const AddGroupInfo = forwardRef<GroupInfoInstance, GroupInfoProps>(({ info }, ref) => {
  const [success, setSuccess] = useState(false);
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    getValues: () => form.getFieldsValue(),
    success: () => {
      setSuccess(true);
    },
  }));

  useEffect(() => {
    setSuccess(false);
  }, [info]);

  if (!info) {
    return null;
  }

  let children;
  if (info.joinCheck) {
    children = (
      <div className={styles.box}>
        {success ? (
          <Row wrap={false} align="middle">
            <Col flex="none">
              <Icon name="tick-outline" size={22} color="#3296fa" />
            </Col>
            <Col flex="auto" className="pl-2" style={{ color: '#999', fontSize: '12px' }}>
              你的入群请求已经发送成功，请耐心等待群主确认。
            </Col>
          </Row>
        ) : (
          <>
            <Form.Item initialValue={info.id} name="groupId" hidden>
              <Input />
            </Form.Item>
            <Form.Item
              label="验证消息"
              name="remark"
              initialValue={`我是${user.nickname}`}
              className={styles.formItem}
            >
              <Input.TextArea style={{ height: '62px', resize: 'none' }} />
            </Form.Item>
          </>
        )}
      </div>
    );
  }

  return (
    <Form form={form} className="px-5">
      <div className={styles.box}>
        <div className={styles.nickname}>
          <h3 className={styles.title}>{info.name}</h3>
          <ChatTag value={info} />
          <p className={styles.groupNo}>群号：{info.groupNum}</p>
        </div>
        <Avatar src={info.avatar} size={60} />
      </div>
      <div className={styles.box}>
        {info.nickname ? (
          <div className={styles.info}>
            <span className={styles.label}>群主</span>
            <span>{info.nickname}</span>
          </div>
        ) : null}
        {'groupCount' in info ? (
          <div className={styles.info}>
            <span className={styles.label}>成员</span>
            <span>{info.groupCount}人</span>
          </div>
        ) : null}
      </div>
      {children}
    </Form>
  );
});

export default AddGroupInfo;
