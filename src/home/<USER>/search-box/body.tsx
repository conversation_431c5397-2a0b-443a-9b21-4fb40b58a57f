import { H<PERSON><PERSON>ttributes, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UIEvent, useRef, useState } from 'react';
import classnames from 'classnames';
import loadable from '@loadable/component';
import { useMemoizedFn, useThrottleFn } from 'ahooks';
import { SearchChatGroupResult } from '@/apis';
import { SetItemFn } from '@@/home/<USER>/search-box/topic-item';
import { EMPTY_FN } from '@/utils/const';
import styles from './search.module.less';
import List, { ListItem, MoreClickEventHandler, SearchListType } from './list';
import { SearchGroup } from './results';

export interface BodyProps extends Omit<HTMLAttributes<HTMLDivElement>, 'children' | 'onLoad'> {
  searchKey?: string;
  selectedKey: string;
  groups: SearchGroup[];
  items: ListItem[];
  loading?: boolean;
  loadMore?: boolean;
  onMore?: MoreClickEventHandler;
  onSelected?: MouseEventHandler;
  onJoin?: SetItemFn;
  onLoad?: () => void;
}

const AddGroup = loadable(() => import('./add-group'));

function Body({
  searchKey,
  selectedKey,
  groups,
  items,
  loading,
  loadMore,
  className,
  onMore,
  onSelected,
  onJoin,
  onLoad,
  ...props
}: BodyProps) {
  const prevTop = useRef(0);
  const [group, setGroup] = useState<SearchChatGroupResult | null>(null);
  const onSetGroup = useMemoizedFn((item?: SearchChatGroupResult) => {
    setGroup(item || null);
  });
  const { run: onScroll } = useThrottleFn(
    (e: UIEvent<HTMLDivElement>) => {
      const el = e.target as HTMLDivElement;
      if (el) {
        if (
          onLoad &&
          !loading &&
          !loadMore &&
          selectedKey !== 'all' &&
          prevTop.current < el.scrollTop &&
          el.scrollHeight - 200 < el.clientHeight + el.scrollTop
        ) {
          onLoad();
        }
        prevTop.current = el.scrollTop;
      }
    },
    { wait: 200 }
  );

  let children;
  if (selectedKey === 'all') {
    if (groups.length) {
      children = groups.map((item) => (
        <div key={item.key} className={styles.group}>
          <h4 className={styles.groupTitle}>
            与 &quot;{searchKey || ''}&quot; 相关的 {item.label}
          </h4>
          <List
            type={item.key}
            items={item.items}
            max={3}
            onMore={onMore}
            onJoin={onJoin}
            onAddGroup={onSetGroup}
            onItemClick={onSelected}
          />
        </div>
      ));
    }
  } else if (items.length) {
    children = (
      <List
        type={selectedKey as SearchListType}
        items={items}
        onJoin={onJoin}
        onAddGroup={onSetGroup}
        onItemClick={onSelected}
      />
    );
  }
  if (!loading) {
    if (!children) {
      children = <div className={styles.error}>没有找到结果</div>;
    } else if (selectedKey !== 'all') {
      children = (
        <>
          {children}
          <div className={`py-3 ${styles.loadMore}`}>{loadMore ? '加载中' : '已经到底了'}</div>
        </>
      );
    }
  }

  const classes = classnames(className, styles.body);
  return (
    <div {...props} className={classes} onScroll={onScroll}>
      {children}
      {group && <AddGroup group={group} onCancel={onSetGroup} />}
    </div>
  );
}

Body.displayName = 'SearchBoxBody';
Body.defaultProps = {
  loading: false,
  loadMore: false,
  searchKey: '',
  onJoin: EMPTY_FN,
  onMore: EMPTY_FN,
  onSelected: EMPTY_FN,
  onLoad: undefined,
};

export default Body;
