import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Input, Menu, Modal, ModalProps, Spin } from 'antd';
import { observer } from 'mobx-react-lite';
import { useDebounceFn, useMemoizedFn } from 'ahooks';
import { layout } from '@/store';
import { Icon } from '@/components';
import { nextTick } from '@/utils/utils';
import classNames from 'classnames';
import { CancelTokenSource } from 'axios';
import { getCancelToken } from '@/utils/http';
import { menus } from './util';
import { SearchGroup } from './results';
import { ListItem } from './list';
import { SetItemFn } from './topic-item';
import Body, { BodyProps } from './body';
import styles from './search.module.less';

let logoIcon = 'https://img.huahuabiz.com/user_files/2022527/1653641500877191.png';
if (import.meta.env.BIZ_APP_PLATFORM_NO === '1') {
  logoIcon = 'https://img.huahuabiz.com/user_files/2022530/1653873166579291.png';
} else if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
  logoIcon = 'https://img.huahuabiz.com/user_files/202338/1678258026072134.png';
}

const Box = observer(({ afterClose }: ModalProps) => {
  const searchKey = useRef('');
  const pager = useRef({ page: -1, total: 1 });
  const cancelToken = useRef<CancelTokenSource | null>();
  const [hideMenu, setHideMenu] = useState(true);
  const [loading, setLoading] = useState(false);
  const [loadMore, setLoadMore] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState(['all']);
  const [groups, setGroups] = useState<SearchGroup[]>([]);
  const [items, setItems] = useState<ListItem[]>([]);
  const currentMenu = useMemo(() => {
    const index = Math.max(
      0,
      menus.findIndex((menu) => menu.key === selectedKeys[0])
    );
    return menus[index];
  }, [selectedKeys]);
  // 关闭模态框
  const onCancel = useCallback((e?: MouseEvent) => {
    layout.closeSearch();
    if (!import.meta.env.SSR) {
      window.hidePopover();
    }
    e?.stopPropagation();
  }, []);
  const { run: searchFn } = useDebounceFn(
    (search = searchKey.current) => {
      // 取消上一次的请求
      cancelToken.current?.cancel('cancel');

      // 发起请求

      // 生成新令牌
      cancelToken.current = getCancelToken();
      const params: any = { search };
      if (selectedKeys[0] !== 'all') {
        params.pageSize = 20;
        params.pageNo = Math.max(1, pager.current.page + 1);
      }
      const clean = () => {
        cancelToken.current = null;
        setLoading(false);
        setLoadMore(false);
      };
      return currentMenu
        .search(params, cancelToken.current.token)
        .then((res) => {
          if (selectedKeys[0] === 'all') {
            const list: SearchGroup[] = [];
            menus.forEach(({ key, label }) => {
              const menuItems = res[key];
              if (menuItems && menuItems.length !== 0) {
                list.push({ key, label, items: menuItems } as SearchGroup);
              }
            });
            setGroups(list);
          } else {
            pager.current = { page: params.pageNo || 1, total: res.pagination?.total || 1 };
            setItems((prevState) => (params.pageNo === 1 ? res.list : [...prevState, ...res.list]));
          }
          clean();
        })
        .catch((e) => {
          if (e && e.message === 'cancel') {
            clean();
          }
        });
    },
    {
      wait: 500,
    }
  );
  // 搜索
  const onCompositionEnd = useMemoizedFn((e: any) => {
    nextTick(() => {
      const keyword = (e.currentTarget || e.target).value;
      setHideMenu(!keyword);
      searchKey.current = keyword;
      pager.current = { page: 0, total: 1 };
      setLoading(true);
      searchFn();
    });
  });
  const onChange: ChangeEventHandler<HTMLInputElement> = useMemoizedFn((e) => {
    // @ts-ignore
    if (e.nativeEvent && e.nativeEvent.isComposing) {
      return;
    }
    onCompositionEnd(e);
  });
  // 选择菜单
  const onSelectedMenu = useMemoizedFn(({ key }: { key: string }) => {
    setSelectedKeys([key]);
  });
  const onMore: BodyProps['onMore'] = useMemoizedFn((e) => {
    setSelectedKeys([e.key]);
  });
  const onUpdate: SetItemFn = useMemoizedFn((e) => {
    if (e.key) {
      const selected = items.find((item) => item.id === e.id);
      if (selected) {
        setItems((prevState) =>
          prevState.map((item) => {
            const current = item.id === selected.id ? { ...item, ...e.data } : item;
            if (e.createName && 'createName' in current && current.createName === e.createName) {
              current.isJoin = 1;
            }
            return current;
          })
        );
      }
    }
  });
  const onLoad: BodyProps['onLoad'] = useMemoizedFn(() => {
    const tmp = pager.current;
    if (tmp.page < tmp.total) {
      setLoadMore(true);
      searchFn();
    }
  });

  useEffect(() => {
    if (
      searchKey.current &&
      (selectedKeys[0] !== 'all' || (selectedKeys[0] === 'all' && groups.length === 0))
    ) {
      pager.current = { page: 0, total: 1 };
      if (items.length) {
        setItems([]);
      }
      setLoading(true);
      searchFn();
    } else {
      setLoading(false);
    }
  }, [searchFn, selectedKeys]); // eslint-disable-line

  let logoDesc = 'Echronos Business Circle';
  if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
    logoDesc = '优盟平台';
  }

  const menuClass = classNames(styles.menu, { [styles.hideMenu]: hideMenu });
  return (
    <Modal
      visible={layout.showSearch}
      width={824}
      closable={false}
      footer={null}
      afterClose={afterClose}
      className={styles.box}
      onCancel={onCancel}
    >
      <div className={styles.head}>
        <div className="text-center">
          <img src={logoIcon} alt="LOGO" className={styles.logo} />
          <p className={styles.logoDesc}>{logoDesc}</p>
        </div>
        <Input
          autoFocus
          allowClear
          placeholder="搜索"
          prefix={<Icon name="search" size={18} />}
          maxLength={50}
          className={styles.input}
          onChange={onChange}
          onCompositionEnd={onCompositionEnd}
        />
        <Menu
          selectedKeys={selectedKeys}
          mode="horizontal"
          className={menuClass}
          items={menus}
          onClick={onSelectedMenu}
        />
      </div>
      <Spin spinning={loading}>
        <Body
          items={items}
          groups={groups}
          hidden={hideMenu}
          loading={loading}
          loadMore={loadMore}
          searchKey={searchKey.current}
          selectedKey={selectedKeys[0]}
          onLoad={onLoad}
          onMore={onMore}
          onSelected={onCancel}
          onJoin={onUpdate}
        />
      </Spin>
    </Modal>
  );
});

Box.displayName = 'SearchBoxModal';

export default Box;
