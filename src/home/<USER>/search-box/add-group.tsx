import { useRef } from 'react';
import { Modal } from 'antd';
import { SearchChatGroupResult } from '@/apis';
import AddGroupFooter from './add-group-footer';
import AddGroupInfo, { GroupInfoInstance } from './add-group-info';
import styles from './search.module.less';

export interface AddGroupProps {
  group: SearchChatGroupResult | null;
  onCancel: () => void;
}

function AddGroup({ group, onCancel }: AddGroupProps) {
  const infoEl = useRef<GroupInfoInstance>(null);

  return (
    <Modal
      centered
      title="加入群聊"
      visible={!!group}
      width={350}
      footer={<AddGroupFooter instance={infoEl} info={group} onCancel={onCancel} />}
      className={styles.dialog}
      onCancel={onCancel}
    >
      <AddGroupInfo ref={infoEl} info={group} />
    </Modal>
  );
}

export default AddGroup;
