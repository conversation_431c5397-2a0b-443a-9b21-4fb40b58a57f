import { SearchCmsTopicResult, saveConcernTopic } from '@/apis';
import { Avatar, Button } from 'antd';
import { MouseEventHandler } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './topic-item.module.less';
// eslint-disable-next-line no-unused-vars
export type SetItemFn = (data: {
  key: string;
  id: number;
  data: Record<string, unknown>;
  createName?: string;
}) => void;
const stopPropagation: MouseEventHandler = (e) => {
  e.stopPropagation();
  e.preventDefault();
};

export interface TopicItemProp {
  item: SearchCmsTopicResult;
  onJoin?: SetItemFn;
  onClick?: MouseEventHandler;
}
function TopicItem({ item, onJoin, onClick }: TopicItemProp) {
  const navigate = useNavigate();

  const handleJoin: MouseEventHandler = (e) => {
    if (!onJoin) return;
    stopPropagation(e);
    saveConcernTopic({
      topicId: item.id,
    }).then((res) => {
      if (res) {
        const param = {
          key: 'TopicItem',
          id: item.id,
          data: { isJoin: !item.isJoin },
        };
        onJoin(param);
      }
    });
  };
  const handleDetail: MouseEventHandler = (e) => {
    navigate(`/renovation/page?bid=${item.id}&code=deco_community&pageId=`);
    onClick?.(e);
  };
  return (
    // eslint-disable-next-line jsx-a11y/no-static-element-interactions
    <div className={styles.topic} onClick={handleDetail}>
      <div className={styles.topicWrapper}>
        <Avatar size={32} src={item.topicHead} className={styles.avatar} />
        <div className={styles.topicInfo}>
          <span className={styles.topicTile}>{item.topicName}</span>
          <span className={styles.topicNum}>
            动态:{item.trendstopic || 0} | 成员：{item.memberCount}
          </span>
        </div>
      </div>
      {item.isJoin ? (
        <Button
          size="small"
          shape="round"
          type="ghost"
          onClick={(e) => {
            stopPropagation(e);
          }}
        >
          已加入
        </Button>
      ) : (
        <Button size="small" shape="round" danger onClick={handleJoin}>
          加入
        </Button>
      )}
    </div>
  );
}

TopicItem.defaultProps = {
  onJoin: undefined,
  onClick: undefined,
};

export default TopicItem;
