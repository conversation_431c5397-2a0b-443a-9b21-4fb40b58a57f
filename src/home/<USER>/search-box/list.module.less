@import 'styles/mixins/mixins';

.list {
  margin-bottom: 0;
  padding: 0;
  list-style: none;
  border-radius: @border-radius-base;
  background-color: #fff;
}

.more {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  display: inline-block;
  line-height: 15px;
  margin: 8px 12px 0;
  padding: 0;
  border: 1px solid transparent;
  background-color: transparent;
  cursor: pointer;
}

.item {
  display: flex;
  padding: 12px;
  align-items: center;

  &:hover {
    border-radius: @border-radius-base;
    background-color: #f5f6fa;
  }
}

.itemAvatar {
  .flex-column(32px);
}

.itemBody {
  .text-overflow();

  color: @text-color;
  display: inline-block;
  width: 90%;
}

.itemBtn {
  justify-self: flex-end;
}

.prodImg {
  .flex-column(70px);
}

.prodBody {
  .flex-column();

  padding: 0 16px;
  overflow: hidden;
}

.prodTitle,
.prodMeta {
  .text-overflow();
}

.prodTitle {
  line-height: 18px;
  margin-bottom: 0;
}

.prodMeta {
  color: #999;
  font-size: @font-size-sm;
  line-height: 15px;
  margin-top: 10px;
  margin-bottom: 0;
}

.prodLink {
  color: @text-color;
  margin-left: 4px;
  padding: 0;
  border-color: transparent;
  background-color: transparent;
  cursor: pointer;
}

.desc {
  color: #999;
  font-size: @font-size-sm;
  margin-top: 4px;
}
