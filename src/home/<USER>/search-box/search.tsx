import {
  MouseEventHandler,
  forwardRef,
  useEffect,
  useMemo,
  useRef,
  useState,
  useImperativeHandle,
  ChangeEvent,
} from 'react';
import { Input, InputRef, Menu } from 'antd';
import classNames from 'classnames';
import debounce from 'lodash/debounce';
import isFunction from 'lodash/isFunction';
import { Icon } from '@/components';
import { SearchAllResult } from '@/apis';
import { CancelTokenSource, getCancelToken } from '@/utils/http';
import { menus } from './util';
import Results, { SearchGroup } from './results';
import { ListItem, SearchListType } from './list';
import { SetItemFn } from './topic-item';
import styles from './search.module.less';

export interface GlobalSearchProps {
  selectedKeys: string[];
  // eslint-disable-next-line no-unused-vars
  onClickMenu?: (key: string[]) => void;
  onCancel?: MouseEventHandler;
}

export interface GlobalSearchInstance {
  focus: () => void;
  clear: () => void;
}

let logoIcon = 'https://img.huahuabiz.com/user_files/2022527/1653641500877191.png';
if (import.meta.env.BIZ_APP_PLATFORM_NO === '1') {
  logoIcon = 'https://img.huahuabiz.com/user_files/2022530/1653873166579291.png';
} else if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
  logoIcon = 'https://img.huahuabiz.com/user_files/202338/1678258026072134.png';
}

const Search = forwardRef<GlobalSearchInstance, GlobalSearchProps>(
  ({ selectedKeys, onClickMenu, onCancel }, ref) => {
    const [searchKey, setSearchKey] = useState('');
    const [loading, setLoading] = useState(false);
    const [groups, setGroups] = useState([] as SearchGroup[]);
    const [items, setItems] = useState([] as ListItem[]);
    const current = useMemo(() => {
      const index = menus.findIndex((menu) => menu.key === selectedKeys[0]);
      return index === -1 ? menus[0] : menus[index];
    }, [selectedKeys]);
    const inputEl = useRef(null as unknown as InputRef);
    const cancelSource = useRef(null as null | CancelTokenSource);
    const searchFn = useMemo(
      () =>
        debounce((search: string, pageSize?: number) => {
          if (cancelSource.current) {
            cancelSource.current.cancel('cancel');
          }

          cancelSource.current = getCancelToken();
          current
            .search({ search, pageSize }, cancelSource.current.token)
            // @ts-ignore
            .then((res) => {
              if ('list' in res) {
                setItems(res.list);
              } else {
                const result = res as SearchAllResult;
                const list = [];
                for (let i = 1; i < menus.length; i += 1) {
                  const { key, label } = menus[i] as {
                    label: string;
                    key: SearchListType;
                  };
                  if (result[key] && result[key].length !== 0) {
                    list.push({ label, key, items: result[key] });
                  }
                }
                // @ts-ignore
                setGroups(list);
              }
              setLoading(false);
              cancelSource.current = null;
            })
            .catch((e: { message: string }) => {
              if (e.message !== 'cancel') {
                setLoading(false);
                cancelSource.current = null;
              }
            });
        }, 100),
      [current]
    );

    const onSetKey = ({ key }: { key: string }) => {
      if (isFunction(onClickMenu)) {
        onClickMenu([key]);
      }
    };
    const onUpdateJoin: SetItemFn = (e) => {
      if (e.key !== '') {
        const index = items.findIndex((item) => item.id === e.id);
        if (index !== -1) {
          const item = {
            ...items[index],
            ...e.data,
          };
          const newItems = [...items];
          newItems.splice(index, 1, item);
          if (e.createName) {
            const allItems = newItems.map((nitem) => {
              const citem = nitem;
              if ('createName' in citem && citem.createName === e.createName) {
                citem.isJoin = 1;
              }
              return citem;
            });
            setItems(allItems);
          } else {
            setItems(newItems);
          }
        }
      }
    };
    const handleSearch = (e: unknown) => {
      if (groups.length !== 0) {
        setGroups([]);
      }
      if (items.length !== 0) {
        setItems([]);
      }
      setLoading(true);

      const key = (
        (e as ChangeEvent<HTMLInputElement>).currentTarget || (e as CompositionEvent).target
      ).value;
      setSearchKey(key);
      searchFn(key);
    };

    useImperativeHandle(ref, () => ({
      focus: () => {
        inputEl.current.focus();
      },
      clear: () => {
        setItems([]);
        setGroups([]);
        setSearchKey('');
        try {
          cancelSource.current?.cancel('cancel');
        } catch (e) {
          console.warn(e);
        }
      },
    }));

    useEffect(() => {
      if (searchKey !== '') {
        if (selectedKeys[0] !== 'all' || (selectedKeys[0] === 'all' && groups.length === 0)) {
          setLoading(true);
          searchFn(searchKey);
        }
      }
    }, [selectedKeys, searchFn]); // eslint-disable-line

    let logoDesc = 'Echronos Business Circle';
    if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
      logoDesc = '优盟平台';
    }

    const menuClass = classNames(styles.menu, { [styles.hideMenu]: !searchKey });
    return (
      <>
        <div className={styles.head}>
          <div className="text-center">
            <img src={logoIcon} alt="LOGO" className={styles.logo} />
            <p className={styles.logoDesc}>{logoDesc}</p>
          </div>
          <Input
            ref={inputEl}
            value={searchKey}
            placeholder="搜索"
            prefix={<Icon name="search" size={18} />}
            maxLength={50}
            className={styles.input}
            onChange={handleSearch}
            allowClear
          />
          <Menu
            selectedKeys={selectedKeys}
            mode="horizontal"
            className={menuClass}
            onClick={onSetKey}
          >
            {menus.map((menu) => (
              <Menu.Item key={menu.key} className={styles.menuItem}>
                {menu.label}
              </Menu.Item>
            ))}
          </Menu>
        </div>
        <Results
          loading={loading}
          groups={groups}
          items={items}
          keys={selectedKeys}
          searchKey={searchKey}
          style={{ display: searchKey ? '' : 'none' }}
          onMore={onSetKey}
          onSelected={onCancel}
          onJoin={onUpdateJoin}
        />
      </>
    );
  }
);

Search.displayName = 'GlobalSearch';

Search.defaultProps = {
  onClickMenu: undefined,
  onCancel: undefined,
};

export default Search;
