import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON><PERSON>, Button } from 'antd';
import classNames from 'classnames';
import isFunction from 'lodash/isFunction';
import {
  SearchUserResult,
  SearchFunctionResult,
  SearchChatGroupResult,
  SearchShopResult,
  RecommendFriend,
  createFunctionSession,
} from '@/apis';
import { Icon } from '@/components';
import codeToUrls from '@/utils/function-code-to-urls';
import { isColleague, isFriend } from '../../utils/relation';
import styles from './list.module.less';
import ChatTag from '../../components/chat-tag';
import addFriend from '../add-friend';

export type SearchItem =
  | SearchUserResult
  | SearchFunctionResult
  | SearchChatGroupResult
  | SearchShopResult;

// eslint-disable-next-line no-unused-vars
export type AddGroupHandler = (group: SearchChatGroupResult) => void;

export interface SearchItemProps {
  item: SearchItem | RecommendFriend;

  onClick?: MouseEventHandler;
  onAddGroup?: AddGroupHandler;
}

const isHnc: boolean = import.meta.env.BIZ_APP_PLATFORM_NO === '1';
const stopPropagation: MouseEventHandler = (e) => {
  e.stopPropagation();
  e.preventDefault();
};

function Item({ item, onClick, onAddGroup }: SearchItemProps) {
  let url;
  let title;
  let avatar;
  let btn;
  let tag;

  if ('avatar' in item) {
    avatar = item.avatar;
    if ('relation' in item) {
      title = ('remark' in item && item.remark) || (isHnc && item.userName) || item.nickname;
      if (isFriend(item) || isColleague(item)) {
        url = `/imc/0?userId=${item.id}`;
      }
      if (isFriend(item)) {
        btn = (
          <Button
            icon={<Icon name="message" className="mr-1" />}
            shape="round"
            size="small"
            danger
            className={styles.itemBtn}
          >
            聊天
          </Button>
        );
      } else {
        btn = (
          <Button
            icon={<Icon name="plus" className="mr-1" />}
            shape="round"
            size="small"
            danger
            className={styles.itemBtn}
            onClick={(e) => {
              addFriend(item.id);
              stopPropagation(e);
            }}
          >
            好友
          </Button>
        );
      }
      tag = <ChatTag value={item} />;
    } else if ('groupNum' in item) {
      title = item.name || item.nickname;
      if (item.joinType) {
        url = `/imc/${item.sessionId}`;
        btn = (
          <Button
            icon={<Icon name="message" className="mr-1" />}
            shape="round"
            size="small"
            danger
            className={styles.itemBtn}
          >
            群聊
          </Button>
        );
      } else {
        btn = (
          <Button
            icon={<Icon name="plus" className="mr-1" />}
            shape="round"
            size="small"
            danger
            className={styles.itemBtn}
            onClick={(e) => {
              if (isFunction(onAddGroup)) {
                onAddGroup(item);
              }
              stopPropagation(e);
            }}
          >
            群聊
          </Button>
        );
      }
      tag = <ChatTag value={item} />;
    } else {
      title = item.name;
      url = `/shop-home?bid=${item.id}&code=deco_shop`;
    }
  } else {
    title = item.funcName;
    avatar = item.picRound;
    url = codeToUrls[item.coding] || item.url;
  }

  const bodyClass = classNames('px-2', styles.itemBody);
  let content = (
    <>
      <Avatar src={avatar} size={32} className={styles.itemAvatar} />
      <div className={bodyClass}>
        <span>{title}</span>
        {tag}
        {'desc' in item && item.desc ? <p className={styles.desc}>{item.desc}</p> : null}
      </div>
      {btn}
    </>
  );
  if (url) {
    content = (
      <Link
        to={url}
        className={styles.item}
        onClick={(e) => {
          if ('coding' in item && item.coding) {
            createFunctionSession(item.coding);
          }
          onClick?.(e);
        }}
      >
        {content}
      </Link>
    );
  } else if ('relation' in item) {
    content = (
      <div data-user-popper={item.id} className={styles.item}>
        {content}
      </div>
    );
  } else {
    content = <div className={styles.item}>{content}</div>;
  }
  return <li title={title}>{content}</li>;
}

Item.defaultProps = {
  onClick: undefined,
  onAddGroup: undefined,
};

export default Item;
