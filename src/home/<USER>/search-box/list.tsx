import { MouseEventHandler, useMemo } from 'react';
import isNumber from 'lodash/isNumber';
import {
  SearchProductResult,
  SearchCmsTopicResult,
  SearchCmsTrendResult,
  SearchCmsArticleResult,
} from '@/apis';
import Item, { SearchItem, AddGroupHandler } from './item';
import ProductItem from './product-item';
import styles from './list.module.less';
import TopicItem, { SetItemFn } from './topic-item';
import ContentItem from './content-item';

export type SearchListType =
  | 'userList'
  | 'groupList'
  | 'funcList'
  | 'shopList'
  | 'productList'
  | 'cmsTopicVoList'
  | 'cmsTrendsVoContentList'
  | 'cmsTrendsVoArticleList';

// eslint-disable-next-line no-unused-vars
export type MoreClickEventHandler = (e: { key: string }) => void;

export type ListItem =
  | SearchItem
  | SearchProductResult
  | SearchCmsTopicResult
  | SearchCmsTrendResult
  | SearchCmsArticleResult;

export interface SearchListProps {
  items: ListItem[];
  type: SearchListType;
  max?: number;
  onMore?: MoreClickEventHandler;
  onItemClick?: MouseEventHandler;
  onAddGroup?: AddGroupHandler;
  onJoin?: SetItemFn;
  // list: ListItem[];
}

function List({ type, items, max, onMore, onItemClick, onJoin, onAddGroup }: SearchListProps) {
  const list = useMemo(
    () => (isNumber(max) && max > 0 ? items.slice(0, Math.min(max, items.length)) : items),
    [max, items]
  );
  let innerList;
  if (type === 'productList') {
    innerList = list.map((item: ListItem) => (
      <ProductItem key={item.id} item={item as SearchProductResult} onClick={onItemClick} />
    ));
  } else if (type === 'cmsTopicVoList') {
    innerList = list.map((item: ListItem) => (
      <TopicItem
        key={item.id}
        onJoin={onJoin}
        onClick={onItemClick}
        item={item as SearchCmsTopicResult}
      />
    ));
  } else if (type === 'cmsTrendsVoContentList' || type === 'cmsTrendsVoArticleList') {
    innerList = list.map((item: ListItem) => (
      <ContentItem
        key={item.id}
        type={type}
        onJoin={onJoin}
        onClick={onItemClick}
        item={item as SearchCmsArticleResult}
      />
    ));
  } else {
    innerList = list.map((item) => (
      <Item key={item.id} item={item as SearchItem} onClick={onItemClick} onAddGroup={onAddGroup} />
    ));
  }
  return (
    <ul className={styles.list}>
      {list && innerList}
      {max && items.length > max ? (
        <li>
          <button
            type="button"
            className={styles.more}
            onClick={() => {
              if (typeof onMore === 'function') {
                onMore({ key: type });
              }
            }}
          >
            查看全部
          </button>
        </li>
      ) : null}
    </ul>
  );
}

List.displayName = 'SearchList';

List.defaultProps = {
  max: undefined,
  onMore: undefined,
  onItemClick: undefined,
  onAddGroup: undefined,
  onJoin: undefined,
};

export default List;
