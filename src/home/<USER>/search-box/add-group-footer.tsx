import { MutableRefObject, useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Col, Row } from 'antd';
import { addToChatGroup, SearchChatGroupResult } from '@/apis';
import { GroupInfoInstance } from './add-group-info';

export interface AddGroupFooterProps {
  info: SearchChatGroupResult | null;
  instance: MutableRefObject<GroupInfoInstance | null>;
  onCancel?: () => void;
}

function AddGroupFooter({ info, instance, onCancel }: AddGroupFooterProps) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    setLoading(false);
    setSuccess(false);
  }, [info]);

  if (!info) {
    return null;
  }

  const addToGroup = () => {
    if (!loading && instance.current) {
      setLoading(true);
      const values = instance.current!.getValues();
      if (!values.groupId) {
        values.groupId = info.id;
      }
      return addToChatGroup(values)
        .then(() => {
          instance.current!.success();
          setSuccess(true);
        })
        .finally(() => {
          setLoading(false);
        });
    }
    return null;
  };
  if (success) {
    return (
      <Row>
        <Col span={24} className="px-1">
          <Button shape="round" size="large" type="primary" danger block onClick={onCancel}>
            完成
          </Button>
        </Col>
      </Row>
    );
  }
  return (
    <Row>
      {info.joinCheck ? (
        <>
          <Col flex="auto" className="px-1">
            <Button shape="round" size="large" block onClick={onCancel}>
              取消
            </Button>
          </Col>
          <Col flex="auto" className="px-1">
            <Button
              disabled={loading}
              shape="round"
              size="large"
              type="primary"
              danger
              block
              onClick={addToGroup}
            >
              下一步
            </Button>
          </Col>
        </>
      ) : (
        <Col flex="auto" className="px-1">
          <Button
            disabled={loading}
            shape="round"
            size="large"
            type="primary"
            danger
            block
            onClick={() => {
              addToGroup()?.then(() => {
                navigate(`/imc/${info.sessionId}`);
              });
            }}
          >
            进群聊天
          </Button>
        </Col>
      )}
    </Row>
  );
}

AddGroupFooter.defaultProps = {
  onCancel: undefined,
};

export default AddGroupFooter;
