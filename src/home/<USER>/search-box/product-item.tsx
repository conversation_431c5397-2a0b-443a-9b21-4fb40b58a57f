import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Avatar } from 'antd';
import { SearchProductResult } from '@/apis';
import { Icon, Price } from '@/components';
import styles from './list.module.less';

export interface ProductItemProps {
  item: SearchProductResult;

  onClick?: MouseEventHandler;
}

function ProductItem({ item, onClick }: ProductItemProps) {
  const navigate = useNavigate();
  const meta = useMemo(
    () =>
      item.standards
        ? item.standards.map((standard) => `${standard.name}:${standard.value}`).join(',')
        : '',
    [item.standards]
  );

  return (
    <li title={item.name}>
      <Link
        to={`/shop/goods/detail/?shopSkuId=${item.id}&skuId=${item.skuId}`}
        className={styles.item}
        onClick={onClick}
      >
        <Avatar src={item.images} size={70} shape="square" className={styles.prodImg} />
        <div className={styles.prodBody}>
          <h4 className={styles.prodTitle}>{item.name}</h4>
          <p title={meta} className={styles.prodMeta}>
            {meta}
          </p>
          <p title={item.shopName} className={styles.prodMeta}>
            <span>{item.shopName}</span>
            <button
              type="button"
              className={styles.prodLink}
              onClick={(e) => {
                // navigate(`/shop/store/${item.shopId}`);
                navigate(`/shop-home?bid=${item.shopId}&code=deco_shop`);
                e.preventDefault();
              }}
            >
              <span>进店</span>
              <Icon name="right" size={16} />
            </button>
          </p>
        </div>
        <Price value={item.costPrice || item.marketPrice || 0} separate />
      </Link>
    </li>
  );
}

ProductItem.defaultProps = {
  onClick: undefined,
};

export default ProductItem;
