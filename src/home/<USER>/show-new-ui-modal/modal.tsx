import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>back } from 'react';
import { <PERSON>ton, Modal as AntdModal, ModalProps } from 'antd';
import { getDisposableCode } from '@/apis';
import styles from './modal.module.less';
import doubleRightPng from '../../assets/img/double-right.png';

function Modal(props: ModalProps) {
  const onToNewVersion: MouseEventHandler<HTMLElement> = useCallback((e) => {
    if (!import.meta.env.SSR) {
      const toURL = import.meta.env.BIZ_APP_TO_NEW_VERSION_URL || '';
      let host = toURL.replace(/^https?:\/\//, '');
      const index = host.indexOf('/');
      let next = '/';
      if (index !== -1) {
        next = host.substring(index);
        host = host.substring(0, index);
      }
      const protocol = toURL.includes('https:') ? 'https:' : 'http:';

      getDisposableCode({
        clientId: `web-${host}`,
        redirectUri: host,
      }).then((res) => {
        if (res && res.code) {
          window.location.href = `${protocol}//${host}/auth/authorization?clientId=web-${host}&next=${encodeURIComponent(
            next
          )}&authorization=${res.code}`;
        }
      });
    }
    e.preventDefault();
    e.stopPropagation();
  }, []);

  return (
    <AntdModal {...props} centered width={668} footer={null} className={styles.modal}>
      <h1 className={styles.title}>新版echOS已全网发布</h1>
      <Button type="primary" size="large" className={styles.btn} onClick={onToNewVersion}>
        立即体验
        <img src={doubleRightPng} alt="" className={styles.btnImg} />
      </Button>
      <div className={styles.imgBox} />
    </AntdModal>
  );
}

export default Modal;
