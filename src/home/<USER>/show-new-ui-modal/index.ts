import { user } from '@/store';
import { modalPopup } from '@/utils/popup';
import Modal from './modal';

function showNewUiModal() {
  if (import.meta.env.SSR) {
    return;
  }

  if (user.isLogin()) {
    const cacheKey = '_SHOW_NEW_UI_MODAL_';
    if (!localStorage.getItem(cacheKey)) {
      modalPopup(Modal, {});
      localStorage.setItem(cacheKey, '1');
    }
  }
}

export default showNewUiModal;
