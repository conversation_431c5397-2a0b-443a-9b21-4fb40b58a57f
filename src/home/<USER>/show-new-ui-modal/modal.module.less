.modal {
  :global {
    .ant-modal-body {
      background: url('https://img.huahuabiz.com/user_files/2023911/1694421198227308.png') no-repeat
        center / cover;
      text-align: center;
    }
  }
}

.title {
  font-size: 40px;
  font-weight: 600;
  margin-top: 31px;
}

.btn {
  font-size: @font-size-base !important;
  display: flex;
  width: 100px !important;
  height: 36px !important;
  margin: 32px auto;
  justify-content: center;
  align-items: center;

  &Img {
    width: 11px;
    margin-left: 6px;
  }
}

.imgBox {
  width: 546px;
  height: 188px;
  margin: 0 auto -16px;
  border: 5px solid @text-color;
  border-bottom: none;
  border-radius: 18px 18px 0 0;
  background: url('https://img.huahuabiz.com/user_files/2023913/1694592001211786.png') no-repeat
    center / cover;
  overflow: hidden;
}
