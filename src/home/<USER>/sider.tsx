import { forwardRef, RefObject, useEffect, useRef, useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { observer } from 'mobx-react-lite';
import { Dropdown, Menu, Badge } from 'antd';
import { Avatar, Icon, Search } from '@/components';
import { layout, sessions, user } from '@/store';
import {
  GetMemberBannerResult,
  getMemberBanners,
  getTodolistNum,
  createFunctionSession,
} from '@/apis';
import debounce from 'lodash/debounce';
import classNames from 'classnames';
import { useMemoizedFn, useMount } from 'ahooks';
import styles from './sider.module.less';
import { SearchBox, createChatGroup, SearchAndAddFriend } from '../containers';
import SessionList, { SessionListInstance } from '../containers/session-list';
import {
  SwitchCompany,
  SwitchCompanyInstance,
  QrCode,
  QrCodeInstance,
  AvatarDropdown,
} from './components';
import calendar from '../assets/img/sider/calendar.png';
import todo from '../assets/img/sider/todo.png';
import dashboard from '../assets/img/sider/dashboard.png';

declare global {
  interface Window {
    companyRef: RefObject<SwitchCompanyInstance | undefined>;
  }
}

const createSession = debounce((typeCode: string) => {
  createFunctionSession(typeCode).catch(() => createFunctionSession(typeCode));
}, 150);

const btnMenuItems = [
  { key: 'createGroup', icon: <Icon name="message" />, label: '发起群聊' },
  { key: 'addUser', icon: <Icon name="add-user" />, label: '添加好友' },
];

const Sider = forwardRef<SessionListInstance>((_, ref) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [banners, setBanners] = useState<GetMemberBannerResult[]>([]);
  const initialedRef = useRef(false);
  const qrCodeEl = useRef(null as unknown as QrCodeInstance);
  const switchRef = useRef<SwitchCompanyInstance>(null);
  const [upcomingNumber, setUpComingNumber] = useState(0);
  const onMoreMenu = useMemoizedFn(({ key }) => {
    if (key === 'addUser') {
      layout.toggleShowFriend(true);
    } else {
      createChatGroup({
        onSuccess: (session) => {
          navigate(`/imc/${session.id}`);
        },
      });
    }
  });

  useMount(() => {
    if (!import.meta.env.SSR) {
      window.companyRef = switchRef;
    }
  });

  useEffect(() => {
    if (user.isLogin()) {
      getMemberBanners({ requestPort: 3 }).then((res) => {
        setBanners(res.banners || []);
      });

      getTodolistNum({ disposeStatus: 0 }).then((res) => {
        setUpComingNumber(res.totalNum);
      });
    }
  }, [user.member]); // eslint-disable-line

  // 监听路由变化
  useEffect(() => {
    // 如果不是页面初始化时进入而且路由有变化
    // 当侧边栏正在显示时,隐藏侧边栏
    if (initialedRef.current && layout.showAside) {
      layout.hide();
    }
    initialedRef.current = true;
  }, [location]);

  const btnMenu = <Menu items={btnMenuItems} onClick={onMoreMenu} />;

  const usernameClass = classNames(styles.username, {
    [styles.hideSwitchCompany]: import.meta.env.BIZ_APP_PLATFORM_NO === '1',
  });
  const searchBoxClass = classNames(styles.searchBox, {
    'mb-0': import.meta.env.BIZ_APP_PLATFORM_NO === '1',
  });

  return (
    <aside className={styles.sider}>
      <div className={styles.body}>
        <div id="siderHead" className={styles.header}>
          <div className={styles.userInfo}>
            <AvatarDropdown companies={user.companies}>
              <Avatar
                src={`${user.avatar}?x-image-process=image/resize,m_pad,w_48,h_48,limit_1/sharpen,100`}
                size={48}
                className={styles.avatar}
              >
                {(user.nickname || '').substring(0, 2)}
              </Avatar>
            </AvatarDropdown>
            <div className={styles.userInfoBody}>
              <h3 className={usernameClass}>{user.nickname}</h3>
              {import.meta.env.BIZ_APP_PLATFORM_NO !== '1' && (
                <SwitchCompany
                  ref={switchRef}
                  companyId={user.companyId}
                  companyName={user.companyName}
                  companies={user.companies}
                />
              )}
            </div>
            <Dropdown overlay={btnMenu} placement="bottom">
              <Icon name="plus-outline" size={18} style={{ cursor: 'pointer' }} />
            </Dropdown>
          </div>
          <SearchBox className={searchBoxClass}>
            <Search disabled size="small" placeholder="搜索" className={styles.search} />
          </SearchBox>
          {import.meta.env.BIZ_APP_PLATFORM_NO !== '1' && (
            <ul className={styles.filterBar}>
              <li className={styles.filterBarItem}>
                <Link
                  to="/user/calendar/home"
                  onClick={() => {
                    createSession('schedule');
                  }}
                >
                  <img src={calendar} alt="日程" />
                  <span>日程</span>
                </Link>
              </li>
              <li className={styles.filterBarItem}>
                <Link
                  to="/admin/upcoming"
                  onClick={() => {
                    createSession('backlog');
                  }}
                >
                  <img src={todo} alt="待办" />
                  <span>待办</span>
                </Link>
                <Badge count={upcomingNumber} offset={[0, -15]} />
              </li>
              <li className={styles.filterBarItem}>
                <Link
                  to="/shop/home"
                  onClick={() => {
                    createSession('homePage');
                  }}
                >
                  <img src={dashboard} alt="桌面" />
                  <span>桌面</span>
                </Link>
              </li>
            </ul>
          )}
        </div>
        <SessionList
          ref={ref}
          menuId={layout.menuId}
          selectId={sessions.session.id}
          height={layout.rect.height}
          banners={banners}
          list={sessions.sessions}
          loading={sessions.loading}
          setBanners={setBanners}
        />
      </div>
      <QrCode ref={qrCodeEl} />
      <SearchAndAddFriend
        visible={layout.showFriend}
        onCancel={() => {
          layout.toggleShowFriend(false);
        }}
      />
    </aside>
  );
});

export default observer(Sider);
