import { HTMLAttributes } from 'react';
import classNames from 'classnames';
import { SessionTypes } from '@/utils/session';
import { Session, user } from '@/store';
import { GroupTypes, isColleague, isStranger } from '../../utils/relation';
import styles from './chat-tag.module.less';
import colleague from './imgs/colleague.png';
import stranger from './imgs/stranger.png';
import inner from './imgs/inner.png';
import full from './imgs/full.png';
import department from './imgs/department.png';
import anonymous from './imgs/anonymous.png';
import customer from './imgs/customer.png';
import cooperation from './imgs/cooperation.png';
import customerService from './imgs/customer-service.png';
import proprietary from './imgs/proprietary.png';
import shielding from './imgs/shielding.png';
import helper from './imgs/helper.png';

export interface ChatTagProps extends HTMLAttributes<HTMLSpanElement> {
  value: null | Session | { relation: number } | { groupType: number };
}

function ChatTag({ value, className, ...others }: ChatTagProps) {
  if (value) {
    let img;
    let isStrangerTag = false;

    // 华南城商服仅返回客户标签
    if (
      import.meta.env.BIZ_APP_PLATFORM_NO === '1' &&
      'groupType' in value &&
      value.groupType === GroupTypes.CUSTOMER
    ) {
      img = <img src={customer} alt="客户" />;
    } else if ('groupType' in value && value.groupType === GroupTypes.COOPERATION) {
      img = <img src={cooperation} alt="合作" />;
    } else if (import.meta.env.BIZ_APP_PLATFORM_NO !== '1') {
      if ('sessionType' in value) {
        if (value.sessionType === SessionTypes.CUSTOMER_SERVICE && value.createUser === user.id) {
          img = <img src={customerService} alt="客服" />;
        } else if (value.sessionType === SessionTypes.SHOP && value.selfBusiness) {
          img = <img src={proprietary} alt="自营" />;
        } else if (value.isShield) {
          img = <img src={shielding} alt="屏蔽" />;
        } else if (value.useStatus && (value.openType === 1 || value.openType === 3)) {
          isStrangerTag = true;
          img = <img src={helper} alt="小助手" />;
        }
      }
      if (!img) {
        if ('relation' in value && value.relation) {
          if (isColleague(value)) {
            img = <img src={colleague} alt="同事" />;
          } else if (isStranger(value)) {
            isStrangerTag = true;
            img = <img src={stranger} alt="陌生人" />;
          }
        } else if ('groupType' in value) {
          switch (value.groupType) {
            case GroupTypes.INNER:
              img = <img src={inner} alt="内部" />;
              break;
            case GroupTypes.ALL_USER:
              img = <img src={full} alt="全员" />;
              break;
            case GroupTypes.DEPARTMENT:
              img = <img src={department} alt="部门" />;
              break;
            case GroupTypes.ANONYMOUS:
              img = <img src={anonymous} alt="匿名" />;
              break;
            case GroupTypes.CUSTOMER:
              img = <img src={customer} alt="客户" />;
              break;
            default:
          }
        }
      }
    }

    if (img) {
      const classes = classNames(
        'ml-1',
        styles.tag,
        { [styles.stranger]: isStrangerTag },
        className
      );
      return (
        <span className={classes} {...others}>
          {img}
        </span>
      );
    }
  }
  return null;
}

export default ChatTag;
