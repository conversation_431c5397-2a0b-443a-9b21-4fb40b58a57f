import { ComponentType, Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON>allback, useEffect, useState } from 'react';
import { Col, message, Row } from 'antd';
import type { AntdIconProps } from '@ant-design/icons/lib/components/AntdIcon';
import { Context } from '@/components';
import copy from 'copy-to-clipboard';
import styles from './demo.module.less';

function Demo() {
  const [components, setComponents] = useState<
    { name: string; component: ComponentType<AntdIconProps> }[]
  >([]);
  const onClick: MouseEventHandler<HTMLButtonElement> = useCallback((e) => {
    let el = e.target as HTMLElement | null;
    while (el && el.tagName !== 'BUTTON') {
      el = el.parentElement;
    }
    if (el) {
      const name = el.getAttribute('data-name');
      if (name) {
        copy(`<${name} />`, { format: 'text/plain' });
        message.success('复制成功');
      }
    }
  }, []);

  useEffect(() => {
    const icons = Object.values(import.meta.glob('/components/icon/!(index|demo).tsx'));
    Promise.all(icons.map((importFn) => importFn()))
      .then((res) =>
        res
          .map((component) => component && (component as { default: any }).default)
          .filter((item) => item)
          .map((component) => ({ name: component.displayName, component }))
      )
      .then(setComponents);
  }, []);

  return (
    <Context>
      <Row>
        {components.map(({ name, component: Component }) => (
          <Col key={name} xs={12} sm={8} md={6} lg={4} className="px-3 py-3">
            <button type="button" data-name={name} className={styles.iconItem} onClick={onClick}>
              <div className={styles.icon}>
                <Component size={20} />
              </div>
              <div className="mt-3">&lt;{name}&nbsp;/&gt;</div>
            </button>
          </Col>
        ))}
      </Row>
    </Context>
  );
}

export default Demo;
