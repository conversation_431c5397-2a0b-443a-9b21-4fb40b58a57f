import { useMemo } from 'react';
import { RecommendFriendResult } from '@/apis';
import Item from '../search-box/item';
import styles from './search-and-add-friend.module.less';

export interface RecommendFriendsProps {
  current: string;
  friends: RecommendFriendResult[];
  // eslint-disable-next-line no-unused-vars
  onMore: (friend: RecommendFriendResult) => void;
  onClick: () => void;
}

function RecommendFriends({ friends, current, onMore, onClick }: RecommendFriendsProps) {
  const list: (RecommendFriendResult & { isMore?: boolean })[] = useMemo(() => {
    const showFriend = friends.find((friend) => friend.key === current);
    if (showFriend) {
      return [showFriend];
    }
    return friends.map((friend) => ({
      ...friend,
      list: friend.list.slice(0, 3),
      isMore: friend.list.length > 3,
    }));
  }, [current, friends]);

  return (
    <>
      <h4 className={styles.title}>好友推荐</h4>
      <div className={styles.box}>
        {list.map((friend) => (
          <div key={friend.key} className={styles.group}>
            <h5 className={styles.groupTitle}>{friend.title}</h5>
            <ul className={styles.list}>
              {friend.list.map((item) => (
                <Item key={item.id} item={item} onClick={onClick} />
              ))}
              {friend.isMore ? (
                <li>
                  <button
                    type="button"
                    className={styles.more}
                    onClick={() => {
                      onMore(friend);
                    }}
                  >
                    查看全部
                  </button>
                </li>
              ) : null}
            </ul>
          </div>
        ))}
      </div>
    </>
  );
}

export default RecommendFriends;
