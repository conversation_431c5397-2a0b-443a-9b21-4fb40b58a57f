@import '../search-box/list.module.less';

.modal {
  :global {
    .ant-modal-body {
      padding: 0;
    }
  }
}

.bar {
  padding: 20px 30px;
}

.barBack {
  line-height: 33px;
  padding: 0 12px;
  border: 1px solid transparent;
  border-right-color: #f5f6fa;
  background-color: transparent;
  cursor: pointer;
}

.select {
  margin-top: 6px;
  vertical-align: top;

  & + & {
    margin-left: 8px;
  }

  &:global {
    &:not(.ant-select-customize-input) {
      > .ant-select-selector {
        height: 25px;
        border-radius: 13px;
        border-color: #f3f3fc;
        background-color: #f3f3fc;

        .ant-select-selection-search-input {
          height: 23px;
        }
      }
    }
  }

  :global {
    .ant-select-selector {
      > .ant-select-selection-item {
        line-height: 23px;
        text-align: left;
      }
    }
  }
}

.body {
  border-top: 1px solid #f5f6fa;
}

.title {
  padding: 12px 16px 0;
}

.box {
  height: 470px;
}

.group {
  padding: 0 8px;
}

.groupTitle {
  color: #999;
  font-size: @font-size-sm;
  padding: 12px 8px 4px;
}

.error {
  color: #999;
  padding-top: 8px;
  text-align: center;
}
