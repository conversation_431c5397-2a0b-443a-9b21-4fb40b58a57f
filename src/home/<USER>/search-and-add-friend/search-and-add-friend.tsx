import { useEffect, useRef, useState } from 'react';
import { Col, Modal, Row, Spin } from 'antd';
import { getRecommendFriends, searchUsers, RecommendFriendResult, SearchUserResult } from '@/apis';
import { Icon, Search } from '@/components';
import Item from '../search-box/item';
import RecommendFriends from './recommend-friends';
import styles from './search-and-add-friend.module.less';

export interface SearchAndAddFriendProps {
  visible: boolean;
  onCancel: () => void;
}

function SearchAndAddFriend({ visible, onCancel }: SearchAndAddFriendProps) {
  const init = useRef(false);
  const [loading, setLoading] = useState(false);
  const [current, setCurrent] = useState('');
  const [friends, setFriends] = useState([] as RecommendFriendResult[]);
  const [search, setSearch] = useState({
    search: '',
    industryId: '' as '' | number,
    provinceId: '' as '' | number,
  });
  const [results, setResults] = useState([] as SearchUserResult[]);
  const handleCancel = () => {
    setLoading(false);
    setCurrent('');
    setSearch({ search: '', provinceId: '', industryId: '' });
    onCancel();
  };

  useEffect(() => {
    if (visible && !init.current) {
      init.current = true;
      setLoading(true);
      getRecommendFriends()
        .then((res) => {
          setFriends(res.list);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [friends, visible]);

  useEffect(() => {
    if (search.search) {
      setLoading(true);
      searchUsers({ ...search, type: 2 }, true)
        .then((res) => {
          setResults([...res.list, ...(res.nonList || [])]);
          setLoading(false);
        })
        .catch((e) => {
          if (e.message !== 'cancel') {
            setLoading(false);
          }
        });
    } else {
      setLoading(false);
    }
  }, [search]);

  return (
    <Modal
      visible={visible}
      centered
      title="添加好友"
      footer={null}
      width={750}
      className={styles.modal}
      onCancel={handleCancel}
    >
      <Row className={styles.bar} style={{ paddingLeft: current ? 0 : '' }}>
        {current ? (
          <Col span={2}>
            <button
              type="button"
              className={styles.barBack}
              onClick={() => {
                setCurrent('');
              }}
            >
              <Icon name="left" size={22} />
            </button>
          </Col>
        ) : null}
        <Col span={current ? 22 : 24}>
          <Search
            value={search.search}
            placeholder="请输入手机号码"
            onSearch={(searchKey) => {
              setSearch({
                ...search,
                search: searchKey,
              });
            }}
          />
        </Col>
      </Row>
      <Spin spinning={loading}>
        <div className={styles.body}>
          {search.search ? (
            <>
              <h4 className={styles.title}> 与“{search.search}”相关的联系人</h4>
              <div className={styles.box}>
                <ul className={styles.list}>
                  {results.length === 0 && !loading ? (
                    <li>
                      <p className={styles.error}>暂无搜索结果~</p>
                    </li>
                  ) : null}
                  {results.map((result) => (
                    <Item key={result.id} item={result} onClick={onCancel} />
                  ))}
                </ul>
              </div>
            </>
          ) : (
            <RecommendFriends
              current={current}
              friends={friends}
              onClick={onCancel}
              onMore={(friend) => {
                setCurrent(friend.key);
              }}
            />
          )}
        </div>
      </Spin>
    </Modal>
  );
}

export default SearchAndAddFriend;
