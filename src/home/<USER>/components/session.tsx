import { HTMLAttributes, memo, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Badge, message as toast } from 'antd';
import classNames from 'classnames';
import { MessageType, MessageTypeTexts } from '@echronos/plugin-im';
import { areEqual } from 'react-window';
import isString from 'lodash/isString';
import { HistoryMessageResult } from '@/apis';
import { Avatar, Modal } from '@/components';
import { Session, user } from '@/store';
import { SessionTypes } from '@/utils/session';
import codeToUrl from '@/utils/function-code-to-urls';
import LinkCircleOutlined from '@/components/icon/link-circle';
import styles from './session.module.less';
import FormatDate from '../../components/format-date';
import ChatTag from '../../components/chat-tag';
import CustomerService from './tools/customer-service';

const showStrMessageTypes = [
  MessageType.WITHDRAW,
  MessageType.TEXT,
  MessageType.NOTICE,
  MessageType.SYSTEM,
];

export interface SessionItemData {
  shopId: number;
  selectedId: number;
  activeId: number;
  companyId: number;
}

export interface SessionItemProps extends HTMLAttributes<HTMLElement> {
  session: Session;
  data: SessionItemData;
}

function getMessage(
  message: HistoryMessageResult
): null | Pick<HistoryMessageResult, 'msg' | 'msgType'> {
  if (message && message.msgType === MessageType.TEMPLATE) {
    let msg;
    try {
      msg = isString(message.msg) ? JSON.parse(message.msg) : message.msg;
    } catch (e) {
      console.warn(e);
      msg = {};
    }
    return { msg, msgType: message.msgType };
  }
  return message;
}

const renovationBrandTypeCodes = ['floorCommentDetails', 'replyCommentDetails'];

const SessionItem = memo<SessionItemProps>(({ session, data, className, onClick, ...props }) => {
  const navigate = useNavigate();
  const lastMessage = useMemo(() => getMessage(session.lastMessage), [session.lastMessage]);
  const to = useMemo(() => {
    if (session.sessionType === SessionTypes.FUNCTION) {
      if (session.typeCode === 'myShop') {
        return `/shop-home?bid=${data.shopId}&code=deco_shop`;
      }
      if (renovationBrandTypeCodes.includes(session.typeCode)) {
        const args =
          lastMessage && lastMessage.msg && !isString(lastMessage.msg)
            ? (lastMessage.msg.args as Record<string, unknown>)
            : null;
        const url = `/renovation/brand?albumId=${args?.albumId}&commentId=${args?.reviewId}`;
        if (args?.companyId && args.companyId !== data.companyId) {
          return { url, companyId: args.companyId };
        }
        return url;
      }
      if (session.useStatus) {
        if (session.openType === 1 || session.openType === 3) {
          return `/imc/${session.id}`;
        }
        return codeToUrl[session.typeCode];
      }
      return null;
    }
    switch (session.sessionType) {
      case SessionTypes.CHAT:
      case SessionTypes.GROUP:
      case SessionTypes.CUSTOMER_SERVICE:
        return `/imc/${session.id}`;
      case SessionTypes.SHOP:
        // return `/shop/store/${session.typeId}`;
        return `/shop-home?bid=${session.typeId}&code=deco_shop`;
      default:
        return (session.typeTagValue as string) || import.meta.env.BIZ_APP_HOME_URL;
    }
  }, [data.companyId, data.shopId, lastMessage, session.id]); // eslint-disable-line react-hooks/exhaustive-deps
  let handleClick: MouseEventHandler<HTMLElement> | undefined = !to
    ? (e) => {
        e.preventDefault();
        // eslint-disable-next-line no-console
        if (session.sessionType === SessionTypes.FUNCTION && !session.useStatus) {
          toast.error('该应用已停用，暂时无法使用');
        } else {
          toast.info('Web暂不支持此功能，请在App上打开');
        }
      }
    : onClick;

  let time;
  if (session.lastMsgTime) {
    time = <FormatDate time={session.lastMsgTime} className={styles.time} />;
  }

  let message;
  if (lastMessage) {
    const at = session.at && session.readNum !== 0 && (
      <span style={{ color: '#ff943e' }}>[有人@你]</span>
    );
    let messageText;
    if (isString(lastMessage)) {
      messageText = lastMessage;
    } else if (showStrMessageTypes.includes(lastMessage.msgType)) {
      messageText = lastMessage.msg;
    } else {
      if (lastMessage.msgType === MessageType.TEMPLATE) {
        messageText = (lastMessage.msg as Record<string, unknown>)?.title;
      }
      if (!messageText) {
        messageText = `[${MessageTypeTexts[lastMessage.msgType] || '未知消息'}]`;
      }
    }
    message = (
      <p className={styles.message}>
        {at}
        {messageText}
      </p>
    );
  }

  let tool;
  if (session.sessionType === SessionTypes.SHOP) {
    tool = <CustomerService session={session} />;
  } else if (
    session.useStatus &&
    (session.openType === 1 || session.openType === 3) &&
    codeToUrl[session.typeCode]
  ) {
    tool = (
      <button
        type="button"
        className={styles.tool}
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          navigate(codeToUrl[session.typeCode]);
        }}
      >
        <LinkCircleOutlined />
        进应用
      </button>
    );
  }

  const tag = ChatTag({ value: session });

  const children = (
    <>
      <Badge count={session.readNum} dot={session.isDisturb as boolean}>
        <Avatar
          src={`${session.avatar}?x-image-process=image/resize,m_fixed,h_200,w_200,limit_1/sharpen,100`}
          primaryKey={session.id}
          shape={session.sessionType === SessionTypes.FUNCTION ? 'square' : 'circle'}
          size={44}
          alt={session.name}
          fitContain={session.sessionType === SessionTypes.SHOP}
          style={{ objectFit: 'contain' }}
        />
      </Badge>
      <div className={styles.itemBody}>
        <h4 title={session.name} className={styles.title}>
          <span className={styles.name} style={{ maxWidth: tag ? '105px' : '' }}>
            {session.remark || session.name}
          </span>
          {tag}
        </h4>
        {time}
        {message || tool ? (
          <div className={styles.footer}>
            {message}
            {tool}
          </div>
        ) : null}
      </div>
    </>
  );

  const classes = classNames(styles.item, className, {
    [styles.top]: session.isTop,
    [styles.active]: data.activeId === session.id,
    [styles.selected]: data.selectedId === session.id,
  });
  if (to) {
    if (isString(to)) {
      return (
        <Link
          {...props}
          to={to}
          target={session.openType === 4 ? '_blank' : '_self'}
          className={classes}
          onClick={handleClick}
        >
          {children}
        </Link>
      );
    }
    handleClick = (e) => {
      Modal.confirm({
        title: '提示',
        content: '该店铺不属于当前公司，是否切换公司？',
        onOk: () => {
          user.switchCompany(to.companyId as number).finally(() => {
            navigate(to.url);
          });
        },
        onCancel: () => {
          navigate(to.url);
        },
      });
      onClick?.(e);
    };
  }
  return (
    <div {...props} role="button" tabIndex={0} className={classes} onClick={handleClick}>
      {children}
    </div>
  );
}, areEqual);

export default SessionItem;
