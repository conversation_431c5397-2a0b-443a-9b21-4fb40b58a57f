import { PropsWithChildren, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Avatar, Dropdown, Menu } from 'antd';
import type { MenuProps } from 'antd';
import { CompanyItemState, user } from '@/store';
import vuex from '@/entry/main/vuex';
import { createServiceSession } from '@/apis';
import { useMemoizedFn } from 'ahooks';
import QrCode, { QrCodeInstance } from './qr-code';
import styles from '../sider.module.less';

export interface ClickMenuEvent {
  key: string;
  data?: CompanyItemState;
}

export interface AvatarDropdownProps {
  companies: CompanyItemState[];
}

function AvatarDropdown({ companies, children }: PropsWithChildren<AvatarDropdownProps>) {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const qrCodeEl = useRef(null as unknown as QrCodeInstance);
  const list = useMemo(
    () => companies.filter((company) => company.nature !== 1 && company.isAdmin),
    [companies]
  );
  // 切换公司
  const onToggleOrganization: MenuProps['onClick'] = useMemoizedFn(({ key }) => {
    const id = Number(key);
    if (user.companyId === id) {
      navigate(`/company-admin/home/<USER>/${user.memberId}`);
      return;
    }
    if (loading) return;
    setLoading(true);
    user
      .switchCompany(id)
      .then((res) => {
        const { companyId, memberId } = res.user;
        setTimeout(() => {
          navigate(`/company-admin/home/<USER>/${memberId}`);
        }, 1000);
        vuex.commit('init');
      })
      .finally(() => {
        setLoading(false);
      });
  });
  // 菜单项
  const items: MenuProps['items'] = useMemo(() => {
    const companyItems: MenuProps['items'] = [];
    if (import.meta.env.BIZ_APP_PLATFORM_NO !== '1') {
      companyItems.push({ key: 'company', label: '加入的企业' });
      if (list.length > 0) {
        companyItems.push({
          key: 'console',
          label: '企业控制台',
          popupClassName: styles.subMenu,
          className: styles.subMenuList,
          children: [
            {
              type: 'group',
              label: '请选择你要管理的企业',
              className: styles.subMenuGroup,
              children: list.map((item) => ({
                key: item.id,
                label: (
                  <>
                    <Avatar src={item.logo} size={40} />
                    <span className={styles.comName}>{item.name}</span>
                  </>
                ),
              })),
            },
          ],
          onClick: onToggleOrganization,
        });
      }
    }
    return [
      { key: 'userinfo', label: '个人信息' },
      { key: 'qrcode', label: '我的二维码' },
      { key: 'collection', label: '收藏' },
      { key: 'myCommunityHome', label: '我的社区主页' },
      { type: 'divider' },
      ...companyItems,
      { key: 'security', label: '账户安全' },
      { key: 'contactCustom', label: '联系客服' },
      { type: 'divider' },
      { key: 'logout', label: '退出登录' },
    ];
  }, [list, onToggleOrganization]);
  // 点击头像
  const onClickAvatar: MenuProps['onClick'] = useMemoizedFn(({ key }) => {
    switch (key) {
      case 'userinfo':
        navigate('/user/mycenter/userinfo');
        break;
      case 'qrcode':
        qrCodeEl.current.show();
        break;
      case 'collection':
        navigate('/my/collection');
        break;
      case 'myCommunityHome':
        navigate('/huahua-circle?code=deco_personal');
        break;
      case 'company':
        navigate('/user/company/join');
        break;
      case 'security':
        navigate('/user/mycenter/safetycenter');
        break;
      case 'contactCustom':
        createServiceSession().then((res) => {
          navigate(`/imc/${res.sessionId}`);
        });
        break;
      case 'logout':
        user.logout().then((to) => {
          if (to.indexOf('http') === -1) {
            navigate(to);
          }
          vuex.commit('clean');
        });
        break;
      default:
    }
  });

  return (
    <>
      <Dropdown
        disabled={loading}
        overlay={<Menu items={items} className={styles.dropdown} onClick={onClickAvatar} />}
        trigger={['click']}
      >
        {children}
      </Dropdown>

      <QrCode ref={qrCodeEl} />
    </>
  );
}

export default AvatarDropdown;
