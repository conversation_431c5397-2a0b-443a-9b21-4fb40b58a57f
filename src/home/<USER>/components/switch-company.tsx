import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Avatar, Button, Dropdown, Menu, MenuProps } from 'antd';
import isNaN from 'lodash/isNaN';
import { Icon } from '@/components';
import { CompanyItemState, user } from '@/store';
import vuex from '@/entry/main/vuex';
import { useMemoizedFn } from 'ahooks';
import isNumber from 'lodash/isNumber';
import addTeam from '../../assets/img/add-team.png';
import styles from '../sider.module.less';
import { createTeam } from '../../containers';

export interface SwitchCompanyProps {
  companyId: number;
  companyName: string;
  companies: CompanyItemState[];
}

export interface SwitchCompanyInstance {
  switchCompany: MultipleParamsFn<[opt: number | { key: string | number }, toHome?: boolean]>;
  createTeam: SimpleFn;
}

/**
 * 选择公司
 * @param companyId
 * @param companyName
 * @param companies
 * @constructor
 */
const SwitchCompany = forwardRef<SwitchCompanyInstance, SwitchCompanyProps>(
  ({ companyId, companyName, companies }, ref) => {
    const navigate = useNavigate();
    const [loading, setLoading] = useState(false);
    const items: MenuProps['items'] = useMemo(
      () =>
        companies.map((company) => ({
          key: company.id,
          className: company.id === companyId ? styles.activeMenu : '',
          label: (
            <>
              <Avatar src={company.logo} size={40} />
              <span className={styles.comName}>{company.name}</span>
            </>
          ),
        })),
      [companies, companyId]
    );
    const onMenuClick: SwitchCompanyInstance['switchCompany'] = useMemoizedFn(
      (opt, toHome = true) => {
        const id = isNumber(opt) ? opt : +opt.key;
        if (!isNaN(id) && !loading) {
          setLoading(true);
          user
            .switchCompany(id)
            .then((res) => {
              const { pathname, search } = window.location;
              // 切换公司删除分销管理下存储的tenantId
              localStorage.removeItem('dis_tenantId');
              // 如果是这个页面就不要重定向，并且告诉iframe切换组织了;
              if ((pathname + search).indexOf('/huahua-circle?code=huahuaquanzi') !== -1) {
                window.iframeApp?.sendMessage('checkoutCompany', {
                  token: `${res.tokenType} ${res.accessToken}`,
                  companyResp: res.user.companyResp,
                });
              } else {
                if (toHome) {
                  navigate(import.meta.env.BIZ_APP_HOME_URL);
                }
                vuex.commit('init');
              }
            })
            .finally(() => {
              setLoading(false);
            });
        }
      }
    );
    const onClickCreateTeam = useMemoizedFn(() => {
      createTeam({
        onSuccess: () => {
          navigate(import.meta.env.BIZ_APP_HOME_URL);
          vuex.commit('init');
        },
        goCompanyAuth: () => {
          navigate('/user/company/auth?addTeam=1');
        },
      });
    });

    useEffect(() => {
      setLoading(true);
      user.getCompanies().finally(() => {
        setLoading(false);
      });
    }, []);

    useImperativeHandle(
      ref,
      () => ({ switchCompany: onMenuClick, createTeam: onClickCreateTeam }),
      [onMenuClick, onClickCreateTeam]
    );

    const menu = (
      <div className={styles.companyBox}>
        <h3 className={styles.menuName}>切换身份</h3>
        <Menu items={items} onClick={onMenuClick} />
        <div className={styles.menuBtnBox}>
          <Button block shape="round" className={styles.menuBtn} onClick={onClickCreateTeam}>
            <img src={addTeam} alt="" />
            创建团队/企业
          </Button>
        </div>
      </div>
    );

    return (
      <Dropdown disabled={loading} overlay={menu} trigger={['click']}>
        <p className={styles.companyNameBox}>
          {loading && <Icon name="loading" className="mr-1" />}
          <span className={styles.companyName} style={{ marginRight: '4px' }}>
            {companyName}
          </span>
          <Icon name="down" className={styles.companyNameDown} />
        </p>
      </Dropdown>
    );
  }
);

export default SwitchCompany;
