@import 'styles/mixins/mixins';

.qrCode {
  :global {
    .ant-modal-body {
      padding: 50px 50px 14px;
    }

    .ant-modal-footer {
      border-top: none;
      padding-bottom: 30px;
    }
  }
}

.box {
  color: #999;
  padding: 26px 26px 16px;
  box-shadow: 0 6px 16px 0 rgb(0 0 0 / 10%);
  border-radius: @border-radius-sm;
}

.head {
  display: flex;
  align-items: center;
}

.headBody {
  .text-overflow();
  .flex-column(238px);

  padding-left: 16px;
}

.title {
  color: @text-color;
  font-size: @font-size-lg;
  font-weight: 600;
}

.body {
  height: 328px;
  padding: 42px 16px 30px;
}
