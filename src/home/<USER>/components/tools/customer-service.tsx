import { useNavigate } from 'react-router-dom';
import { Session } from '@/store';
import styles from '../session.module.less';
import serviceIcon from '../../../assets/img/icon/service.png';

export interface CustomerServiceProps {
  session: Session;
}

function CustomerService({ session }: CustomerServiceProps) {
  const navigate = useNavigate();

  return (
    <div
      tabIndex={0}
      role="button"
      className={styles.tool}
      onClick={(e) => {
        navigate(`/imc/0?shopId=${session.typeId}`);
        e.preventDefault();
      }}
    >
      <img src={serviceIcon} alt="客服" />
      <span>客服</span>
    </div>
  );
}

CustomerService.displayName = 'CustomerServiceTool';

export default CustomerService;
