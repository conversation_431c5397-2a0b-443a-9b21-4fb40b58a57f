@import 'styles/mixins/mixins';

.bannerGroup {
  padding: 0 16px 16px;
}

.bannerItem {
  position: relative;
}

.bannerClose {
  color: #ababae;
  font-size: @font-size-xl;
  padding: 8px;
  position: absolute;
  top: 0;
  right: 0;
}

.error {
  color: @text-color-secondary;
  padding-top: 16px;
  text-align: center;
}

.menu {
  width: 108px;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 8%) !important;
  position: fixed;
}

.session {
  margin: 0;
  padding: 0;
  border-radius: 0 0 18px 18px;
  list-style: none;
  overflow: hidden auto;
}

.item {
  display: flex;
  padding: 12px 16px;
  align-items: center;
  position: relative;
  cursor: pointer;

  &:hover {
    background-color: #eaebf2;
  }

  :global {
    .ant-badge-dot {
      top: 4px;
      right: 4px;
    }
  }
}

.active {
  &,
  &:hover {
    background-color: #eaebf2;
  }

  &::before {
    content: '';
    display: block;
    width: 3px;
    border-radius: 2px;
    background-color: @primary-color;
    position: absolute;
    top: 11px;
    bottom: 11px;
    left: 0;
  }
}

.selected {
  padding: 11px 15px;
  border: 1px solid @primary-text-colors[click];
}

.top {
  background-color: #f5f6fa;
}

.itemBody {
  .flex-column();

  padding-left: 8px;
}

.title {
  display: inline-block;
  max-width: 150px;
  height: 20px;
  line-height: 20px;
  margin-bottom: 0;
  vertical-align: top;
}

.name {
  .text-overflow();

  display: inline-block;
  max-width: 100%;
  vertical-align: top;
}

.time {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  line-height: 20px;
  float: right;
}

.footer {
  line-height: 14px;
  margin-top: 4px;
}

.message {
  .text-overflow();

  color: @text-color-secondary;
  font-size: @font-size-sm;
  display: inline-block;
  width: 145px;
  margin: 0;
}

//.icon {
//  width: 14px;
//  height: 14px;
//  object-fit: contain;
//  float: right;
//  vertical-align: top;
//}

.tool {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  display: inline-block;
  line-height: 14px;
  padding: 0;
  border: 0 solid transparent;
  background-color: transparent;
  cursor: pointer;
  float: right;
  vertical-align: top;

  &:hover {
    color: @color24;
  }

  :global {
    .anticon,
    img {
      width: 12px;
      height: 12px;
      margin-top: 1px;
      margin-right: 4px;
      vertical-align: top;
    }
  }
}
