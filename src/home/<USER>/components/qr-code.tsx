import { forwardRef, useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Avatar, Button, Col, message, Modal, Row, Spin } from 'antd';
import { observer } from 'mobx-react-lite';
import { useMemoizedFn } from 'ahooks';
import copy from 'copy-to-clipboard';
import { generateQrCode } from '@/apis';
import { user } from '@/store';
import { download } from '@/utils/file';
import styles from './qr-code.module.less';

export interface QrCodeInstance {
  show(): void;
  hide(): void;
  download(): void;
}

const QrCode = observer(
  forwardRef<QrCodeInstance>((_, ref) => {
    const [visible, setVisible] = useState(false);
    const [qrCode, setQrCode] = useState('');
    const copyRef = useRef(null as unknown as HTMLDivElement);
    const onCopy = useMemoizedFn(() => {
      if (qrCode) {
        copy(`<img src="${qrCode}" alt="二维码" />`, { format: 'text/html' });
        message.success('复制成功');
      }
    });
    const onDownload = useMemoizedFn(() => {
      if (qrCode) {
        download(qrCode, '二维码.png');
      }
    });
    const onCancel = useCallback(() => {
      setVisible(false);
    }, []);

    useImperativeHandle(
      ref,
      () => ({
        hide: onCancel,
        download: onDownload,
        show: () => {
          setVisible(true);
        },
      }),
      [onCancel, onDownload]
    );

    useEffect(() => {
      if (visible && !qrCode) {
        generateQrCode(2, user.id).then((res) => {
          setQrCode(`data:image/jpg;base64,${res.pictureBase}`);
        });
      }
    }, [qrCode, visible]);

    return (
      <Modal
        title="我的二维码"
        width={440}
        visible={visible}
        footer={
          <Row>
            <Col span={9} offset={3} className="px-2">
              <Button block disabled={!qrCode} shape="round" size="large" onClick={onDownload}>
                保存图片
              </Button>
            </Col>
            <Col span={9} className="px-2">
              <Button
                block
                disabled={!qrCode}
                shape="round"
                size="large"
                type="primary"
                onClick={onCopy}
              >
                复制图片
              </Button>
            </Col>
          </Row>
        }
        className={styles.qrCode}
        onCancel={onCancel}
      >
        <Spin spinning={!qrCode}>
          <div className={styles.box}>
            <div className={styles.head}>
              <Avatar src={user.avatar} size={50} />
              <div className={styles.headBody}>
                <h3 className={styles.title}>{user.nickname}</h3>
                <p className="mt-2">{user.companyName}</p>
              </div>
            </div>
            <div ref={copyRef} className={styles.body}>
              {qrCode && <img src={qrCode} alt="二维码" />}
            </div>
            <p className="text-center">使用echOS扫描二维码，添加我为联系人</p>
          </div>
        </Spin>
      </Modal>
    );
  })
);

export default QrCode;
