import { <PERSON><PERSON><PERSON><PERSON>and<PERSON> } from 'react';
import { NavLink } from 'react-router-dom';
import { Badge } from 'antd';
import isNumber from 'lodash/isNumber';
import styles from './navbar-item.module.less';

export interface NavbarItemProps {
  id: string;
  icon: string;
  isActive?: boolean;
  title?: string;
  activeIcon?: string;
  url?: string;
  count?: number | null;
  onClick?: MouseEventHandler<HTMLAnchorElement>;
}

function NavbarItem({
  id,
  icon,
  isActive,
  title,
  activeIcon,
  url,
  count,
  onClick,
}: NavbarItemProps) {
  let child;
  if (!activeIcon) {
    child = (
      <img
        src={`${icon}?x-image-process=image/resize,m_pad,w_32,h_32,limit_1/sharpen,100`}
        alt={title}
        className={styles.onlyImg}
      />
    );
  } else {
    const iconClass = title ? styles.img : styles.onlyImg;
    child = (
      <div className={styles.itemBody}>
        {activeIcon && (
          <img src={activeIcon} alt={title} className={`${styles.activeImg} ${iconClass}`} />
        )}
        <img src={icon} alt={title} className={iconClass} />
        {title && <h5 className={styles.title}>{title}</h5>}
      </div>
    );
    if (url) {
      child = (
        <NavLink to={url} title={title} onClick={onClick}>
          {child}
        </NavLink>
      );
    }
  }
  if (isNumber(count)) {
    child = (
      <Badge count={count} className={styles.badge}>
        {child}
      </Badge>
    );
  }
  return (
    <li data-navbar={id} className={isActive ? styles.active : styles.item}>
      {child}
    </li>
  );
}

NavbarItem.displayName = 'NavbarItem';

NavbarItem.defaultProps = {
  isActive: false,
  title: '',
  activeIcon: '',
  url: '',
  count: null,
  onClick: undefined,
};

export default NavbarItem;
