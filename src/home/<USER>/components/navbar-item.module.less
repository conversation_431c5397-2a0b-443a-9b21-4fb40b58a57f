@import 'styles/mixins/mixins';

.item {
  .flex-column();

  display: flex;
  height: 60px;
  padding: 7px;
  justify-content: center;
  position: relative;
  align-items: center;
  background-color: #fff;
}

.active:extend(.item) {
  .itemModify {
    display: none;
  }

  .title {
    color: #f90400;
  }

  .activeImg {
    display: inline-block;

    & + img {
      display: none;
    }
  }
}

.itemBody {
  width: 44px;
  text-align: center;
}

.title {
  .text-overflow();

  color: #888b98;
  font-size: 12px;
  line-height: 17px;
  margin-top: 5px;
  margin-bottom: 0;
  text-align: center;
}

.img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.onlyImg {
  width: 32px;
  height: 32px;
  object-fit: contain;
  border-radius: 50%;
}

.activeImg {
  display: none;
}

.badge {
  :global {
    .ant-badge-count {
      top: 4px;
      right: 7px;
    }
  }
}

.screen-sm({
  .item {
    .flex-row(78px);
    width: 60px;
    height: auto;
    padding: 8px;
  }

  .active {
    padding: 0;
    text-align: right;
    background-color: transparent;

    &::before,
    &::after {
      content: '';
      display: block;
      height: ((78px - 62px) / 2px);
      background-color: #fff;
      position: absolute;
      right: 0;
      left: 0;
    }

    &::before {
      top: 0;
    }

    &::after {
      bottom: 0;
    }

    .itemBody {
      width: 60px;
      height: 62px;
      line-height: 62px;
      padding-left: 4px;
      background: url('../../assets/img/navbar-item-active.png') no-repeat 0 center / contain;
    }

    .title {
      display: none;
    }

    .badge {
      :global {
        .ant-badge-count {
          top: 22px;
          right: 14px;
        }
      }
    }
  }
});
