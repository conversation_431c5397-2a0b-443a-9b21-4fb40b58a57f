import {
  CSSProperties,
  forwardRef,
  H<PERSON><PERSON><PERSON>ri<PERSON><PERSON>,
  MouseEventHandler,
  useC<PERSON>back,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Carousel, Menu, MenuProps, Spin } from 'antd';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import { VariableSizeList as List } from 'react-window';
import isNaN from 'lodash/isNaN';
import { Icon } from '@/components';
import {
  createFunctionSession,
  GetMemberBannerResult,
  removeSessionOfList,
  SessionResult,
  setTopStateOfSession,
} from '@/apis';
import { layout, Session, sessions } from '@/store';
import { SessionTypes } from '@/utils/session';
import CloseCircleFilled from '@ant-design/icons/CloseCircleFilled';
import { useMemoizedFn } from 'ahooks';
import { EMPTY_FN } from '@/utils/const';
import styles from './session.module.less';
import SessionItem, { SessionItemData } from './session';

type BannerEventHandler = MultipleParamsFn<[banner: GetMemberBannerResult]>;

export interface SessionGroupProps extends HTMLAttributes<HTMLDivElement> {
  height: number;
  banners: GetMemberBannerResult[];
  data: SessionItemData;
  onClickBanner?: BannerEventHandler;
  onCloseBanner?: BannerEventHandler;
}

export interface SessionGroupInstance {
  scrollToNoReading: () => void;
  resetAfter: MultipleParamsFn<[index: number, shouldForceUpdate?: boolean]>;
}

const SessionGroup = forwardRef<SessionGroupInstance, SessionGroupProps>(
  ({ banners, height, data, className, onClickBanner, onCloseBanner, ...props }, ref) => {
    const listRef = useRef(null as unknown as List);
    const outerRef = useRef(null as unknown as HTMLDivElement);
    const [submitting, setSubmitting] = useState('');
    const [session, setSession] = useState(null as null | SessionResult);
    const [menuStyle, setMenuStyle] = useState({ display: 'none' } as CSSProperties);
    const sessionItems: MenuProps['items'] = useMemo(
      () => [
        {
          key: 'toTop',
          label: (
            <>
              {submitting === 'toTop' && <Icon name="loading" className="mr-1" />}
              {session && session.isTop ? '取消' : '会话'}置顶
            </>
          ),
        },
        { type: 'divider' },
        {
          key: 'remove',
          label: <>{submitting === 'remove' && <Icon name="loading" className="mr-1" />}删除会话</>,
        },
      ],
      [session, submitting]
    );
    const handleClickSession: MouseEventHandler<HTMLElement> = useCallback(
      (e) => {
        const idx = +(e.currentTarget.getAttribute('data-idx') || '');
        if (!isNaN(idx) && idx !== -1) {
          const item = sessions.sessions[banners.length === 0 ? idx : idx - 1];
          const { sessionType } = item;
          const isFunction = sessionType === SessionTypes.FUNCTION;
          sessions.select(item);
          if (isFunction || sessionType === SessionTypes.APP || sessionType === SessionTypes.SHOP) {
            if (!isFunction) {
              sessions.toggleTopState(item.id, item.isTop);
            }
            if (isFunction && item.typeCode) {
              createFunctionSession(item.typeCode);
            }
          }
        }
      },
      [banners]
    );
    const closeMenu = useCallback(() => {
      setSubmitting('');
      setMenuStyle({ display: 'none' });
      layout.setMenu();
    }, []);
    const onClickContextMenu: MenuProps['onClick'] = useMemoizedFn(({ key }) => {
      if (session) {
        if (key === 'toTop') {
          const isTop = session.isTop ? 0 : 1;
          setSubmitting('toTop');
          setTopStateOfSession(session.id, isTop)
            .then(() => {
              sessions.toggleTopState(session.id, isTop);
            })
            .finally(closeMenu);
        } else {
          setSubmitting('remove');
          removeSessionOfList(session.id)
            .then(() => {
              sessions.remove(session.id);
            })
            .finally(closeMenu);
        }
      } else {
        closeMenu();
      }
    });
    const items =
      banners.length > 0 ? [{ id: 'banners' }, ...sessions.sessions] : sessions.sessions;
    const handleContextMenu: MouseEventHandler<HTMLDivElement> = (e) => {
      e.preventDefault();

      let el = e.target as null | HTMLElement;
      while (el) {
        if (el.classList.contains(styles.session)) {
          return;
        }
        if (el.hasAttribute('data-idx')) {
          break;
        }
        el = el.parentElement;
      }
      if (el) {
        const index = parseInt(el.getAttribute('data-idx') || '0', 10);
        const selectedSession = items[index] as Session;
        if (selectedSession.id !== 0 && selectedSession.sessionType !== SessionTypes.SHOP) {
          layout.setMenu(selectedSession.id);
          setSession(selectedSession);
          const rect = outerRef.current.getBoundingClientRect();
          setMenuStyle({
            top: `${e.pageY - layout.rect.top + 10}px`,
            left: `${Math.min(
              rect.right - layout.rect.left - 90,
              e.pageX - layout.rect.left + 10
            )}px`,
          });
        } else {
          closeMenu();
        }
      }
    };
    const scrollToNoReading = () => {
      const $outer = outerRef.current;
      if ($outer && $outer.children[0]) {
        // 获取外部容器信息
        const { top } = $outer.getBoundingClientRect();
        const $el = $outer.children[0];
        let startIndex = -1;
        for (let i = 0; i < $el.children.length; i += 1) {
          const rect = $el.children[i].getBoundingClientRect();
          if (rect.bottom > top) {
            startIndex = +($el.children[i].getAttribute('data-idx') || 0) + 1;
            break;
          }
        }
        let dogIndex = -1;
        const findIndex = (start: number, end: number) => {
          for (let i = start; i < end; i += 1) {
            if ((items[i] as Session).readNum > 0) {
              if (!(items[i] as Session).isShield) {
                return i;
              }
              if (dogIndex === -1) {
                dogIndex = i;
              }
            }
          }
          return -1;
        };
        let index = findIndex(startIndex, items.length);
        if (index === -1) {
          index = findIndex(0, startIndex);
          if (index === -1) {
            index = Math.max(items[0].id === 'banners' ? 1 : 0, dogIndex);
          }
        }
        listRef.current.scrollToItem(index, 'start');
      }
    };

    useEffect(() => {
      if (import.meta.env.SSR) {
        return EMPTY_FN;
      }
      const onClick = (e: MouseEvent) => {
        let el = e.target as null | HTMLElement;
        for (let i = 0; i < 3; i += 1) {
          if (el) {
            if (el.classList.contains(styles.menu)) {
              return;
            }
            el = el.parentElement;
          }
        }
        closeMenu();
      };
      document.addEventListener('click', onClick, false);
      return () => {
        document.removeEventListener('click', onClick, false);
      };
    }, [closeMenu]);

    useImperativeHandle(ref, () => ({
      scrollToNoReading,
      resetAfter: (index, shouldForceUpdate) => {
        listRef.current?.resetAfterIndex(index, shouldForceUpdate);
      },
    }));

    const classes = classNames(styles.session, className);
    return (
      <Spin spinning={sessions.loading}>
        {!sessions.loading && items.length === 0 ? (
          <div {...props} className={styles.error}>
            暂时没有相关会话
          </div>
        ) : (
          <div {...props} className={classes} onContextMenu={handleContextMenu}>
            <List
              ref={listRef}
              outerRef={outerRef}
              width="100%"
              height={height}
              itemData={items}
              itemCount={items.length}
              itemSize={(idx) => (items[idx].id === 'banners' ? 121 : 68)}
            >
              {({ index, style }) =>
                items[index].id === 'banners' ? (
                  <div className={styles.bannerGroup} style={style}>
                    <Carousel autoplay>
                      {banners.map((tmp) => (
                        <div
                          key={tmp.coding}
                          role="button"
                          tabIndex={0}
                          className={styles.bannerItem}
                          onClick={() => {
                            if (onClickBanner) {
                              onClickBanner(tmp);
                            }
                          }}
                        >
                          <img src={tmp.image} alt={tmp.coding} />
                          <CloseCircleFilled
                            className={styles.bannerClose}
                            onClick={(e) => {
                              e.stopPropagation();
                              if (onCloseBanner) {
                                onCloseBanner(tmp);
                              }
                              if (banners.length === 1) {
                                setTimeout(() => {
                                  listRef.current.resetAfterIndex(0);
                                });
                              }
                            }}
                          />
                        </div>
                      ))}
                    </Carousel>
                  </div>
                ) : (
                  <SessionItem
                    data-idx={index}
                    session={items[index] as Session}
                    style={style}
                    data={data}
                    onClick={handleClickSession}
                  />
                )
              }
            </List>

            <Menu
              items={sessionItems}
              disabled={submitting !== ''}
              selectable={false}
              className={styles.menu}
              style={menuStyle}
              onClick={onClickContextMenu}
            />
          </div>
        )}
      </Spin>
    );
  }
);

SessionGroup.defaultProps = {
  onClickBanner: undefined,
  onCloseBanner: undefined,
};

export default observer(SessionGroup);
