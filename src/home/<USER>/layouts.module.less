@import 'styles/mixins/mixins';

@base-width: 375px;

.layout {
  width: 100%;
  min-width: 320px;
  height: 100%;
  min-height: 480px;
  background: rgba(#fff, 0.6);
  position: relative;
}

.wrapper {
  background: url('https://img.huahuabiz.com/static/img/wallpaper.png') no-repeat center / cover;
}

.aside {
  display: flex;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: -100%;
  z-index: 1000;
  flex-flow: column-reverse;

  &.show {
    right: 0;
    left: 0;
  }
}

.main {
  width: 100%;
  height: 100%;
}

.footer {
  .text-overflow();

  color: #999;
  font-size: @font-size-sm;
  display: none;
  line-height: 24px;
  padding: 0 68px;
  text-align: center;
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9;

  a {
    color: #999;
  }
}

.dialog {
  :global {
    .ant-modal-footer {
      border-top: none;
    }
  }
}

.screen-sm({
  .layout {
    width: @base-width * 2;
    margin: 0 auto;
    padding-left: @base-width;
    border-radius: 18px;
    box-shadow: 8px 16px 48px 0 rgba(36, 55, 151, 0.3);
    backdrop-filter: blur(37px);
  }

  .aside {
    width: @base-width;
    flex-flow: row;
    border-radius: 18px 0 0 18px;
    overflow: hidden;
    left: 0;
    z-index: 0;
  }

  .main {
    padding: 24px;
  }

  .footer {
    display: block;
  }

  .wrapper {
    padding-top: 10px;
    padding-bottom: 22px;
  }
});

.toNewVersion {
  color: @primary-color;
  font-weight: 600;
  display: flex;
  width: 141px;
  height: 28px;
  padding: 4px 8px;
  justify-content: center;
  position: fixed;
  right: -106px;
  bottom: 33px;
  z-index: 2050;
  transition-duration: 0.3s;
  border: 0 solid transparent;
  background: rgb(@white 0.6);
  border-radius: 14px 0 0 14px;
  cursor: pointer;

  > img {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }

  > span {
    visibility: hidden;
  }

  &:hover {
    right: 0;

    > span {
      visibility: visible;
    }
  }
}

@media screen and (min-width: (@base-width * 2) + 96) {
  .layout {
    width: @base-width * 4;
    max-width: 100%;
  }

  .wrapper {
    padding-right: 48px;
    padding-left: 48px;
  }
}

.screen-lg({
  .layout {
    width: @base-width * 4;
    max-width: @base-width * 4;
  }
});
