@import 'styles/mixins/mixins';

.sider {
  width: 100%;
  min-width: 291px;
  height: 100%;

  .screen-sm({
    padding: 24px 0;
  });
}

.body {
  height: 100%;
  background-color: #fff;

  .screen-sm({
    border-radius: 18px;
    box-shadow: 0 4px 20px 0 rgba(57, 61, 72, 0.08);
  });
}

.header {
  padding: 16px;
}

.userInfo {
  display: flex;
  align-items: flex-start;
}

.avatar {
  //padding: 1px;
  cursor: pointer;
}

.userInfoBody {
  .flex-column();
  .text-overflow();

  padding: 2px 8px;
}

.username,
.companyName {
  max-width: 200px;
  .text-overflow();
}

.username {
  font-size: 18px;
  font-weight: 600;
  line-height: 25px;
  margin-bottom: 0;
}

.hideSwitchCompany {
  line-height: 44px;
}

.companyNameBox {
  display: flex;
  align-items: center;
}

.companyName {
  color: #888b98;
  font-size: 12px;
  display: inline-block;
  max-width: 160px;
  line-height: 17px;
  margin-top: 2px;
  margin-bottom: 0;
  cursor: pointer;
  user-select: none;
  .text-overflow;
}

.companyNameDown {
  margin-top: 2px;
}

.filterBar {
  display: flex;
  margin: 0 -2px;
  padding: 0;
  justify-content: space-between;
  list-style: none;
}

.filterBarItem {
  // .text-overflow();
  white-space: nowrap;
  font-size: 12px;
  line-height: 22px;
  padding: 4px 2px 0;
  cursor: pointer;

  a {
    color: @text-color;
    font-size: 12px;
  }

  img {
    width: 22px;
    height: 22px;
    margin-right: 2px;
    vertical-align: top;
  }
}

.dropdown {
  width: 240px;
  border-radius: 12px;

  :global {
    .ant-dropdown-menu-submenu-expand-icon {
      display: none;
    }
  }
}

.subMenu,
.companyBox {
  :global {
    .ant-menu,
    .ant-dropdown-menu {
      width: 100%;
      max-height: 224px;
      margin: 0;
      padding: 0 8px;
      overflow-y: auto;
      border: none;
      box-shadow: none;
    }

    .ant-menu-item,
    .ant-dropdown-menu-item {
      display: list-item;
      height: 54px;
      line-height: 54px;
      padding: 7px;
      border-radius: 6px;

      &:hover {
        background-color: #f5f6fa;
      }
    }

    .ant-avatar {
      vertical-align: top;
    }
  }
}

.subMenu {
  width: 300px;
}

.subMenuGroup {
  :global {
    .ant-dropdown-menu-item-group-title {
      color: @text-color;
      font-size: 16px;
      font-weight: 500;
      padding: 16px 16px 4px;
    }

    .ant-dropdown-menu-item-group-list {
      max-height: 180px !important;
      padding-right: 8px;
      padding-left: 8px;
      overflow-y: auto;
    }

    .ant-dropdown-menu-item-group-list {
      margin: 0;
    }
  }
}

.comName {
  .text-overflow();

  color: @text-color;
  display: inline-block;
  width: 77%;
  line-height: 40px;
  margin-left: 8px;
  vertical-align: top;
}

.searchBox {
  margin-top: 16px;
  margin-bottom: 20px;
}

.search {
  :global {
    &,
    .ant-input {
      padding-top: 8px;
      padding-bottom: 8px;
      cursor: pointer;
    }
  }
}

.companyBox {
  width: 278px;
  border-radius: @border-radius-base;
  background-color: #fff;
  box-shadow: 0 6px 16px 0 rgb(0 0 0 / 10%);
}

.activeMenu {
  padding: 6px;
  border: 1px solid @primary-color;
}

.menuItem {
  &,
  &:hover {
    background: none;
  }
}

.menuName {
  font-size: 16px;
  padding: 14px 16px 4px;
}

.menuBtnBox {
  padding: 7px 12px 16px;
}

.menuBtn {
  height: 36px;
  margin-top: 5px;

  img {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 4px;
    vertical-align: middle;
  }
}
