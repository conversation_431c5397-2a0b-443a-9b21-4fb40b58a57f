import { MessageType, MessageTypeTexts } from '@echronos/plugin-im';
import { user } from '@/store';
import { transformMessageText } from '@/utils/session';
import { HTMLAttributes } from 'react';

export interface SessionMessageProps extends HTMLAttributes<HTMLParagraphElement> {
  message?: Record<string, unknown> | null;
  at?: 1 | 2 | null;
}

function SessionMessage({ message, at, ...props }: SessionMessageProps) {
  if (message && message.msg) {
    let html = '';
    if (at) {
      html = `<span style={{ color: '#ff943e' }}>[${at === 1 ? '有人@你' : '@所有人'}]</span>`;
    }
    const { msgType } = message;
    if (msgType === MessageType.WITHDRAW) {
      html += user.id === message.msgFromId ? '你撤回了一条信息' : (message.msg as string);
    } else if (msgType === MessageType.TEXT || msgType === MessageType.NOTICE) {
      html += transformMessageText(message.msg as string);
    } else {
      html += `[${MessageTypeTexts[msgType as number] || '未知消息'}]`;
    }

    // eslint-disable-next-line react/no-danger
    return <p {...props} dangerouslySetInnerHTML={{ __html: html }} />;
  }
  return null;
}

SessionMessage.defaultProps = {
  message: null,
  at: null,
};

export default SessionMessage;
