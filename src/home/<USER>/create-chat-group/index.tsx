import isFunction from 'lodash/isFunction';
import { createChatGroup as createGroup, CreateChatGroupProps, SessionResult } from '@/apis';
import { user } from '@/store';
import { message } from 'antd';
import CreateForm, { CreateFormInstance } from './create-form';
import selectUsers, { SelectUsersConfig } from '../select-users';

let createChatGroupInstance: CreateFormInstance;

function createChatGroup(
  // eslint-disable-next-line no-unused-vars
  config: SelectUsersConfig & { onSuccess?: (session: SessionResult) => void }
) {
  let isFirst = true;

  function ref(instance: CreateFormInstance) {
    createChatGroupInstance = instance;
  }

  const defaults = config.defaults || [];
  defaults.unshift({
    id: user.id,
    label: user.nickname,
    avatar: user.avatar,
    memberId: user.member.id || 0,
  });

  return selectUsers({
    ...config,
    defaults,
    min: 3,
    max: 500,
    closable: false,
    title: config.title || '创建群聊',
    content: <CreateForm ref={ref} />,
    allowCustomer: true,
    allowDistribution: true,
    onlyDistribution: 0,
    onChange: (values) => {
      if (isFirst) {
        isFirst = false;
        return;
      }
      createChatGroupInstance.setSelected(values);
    },
    onConfirm: (instance, close) => {
      // 获取所有邀请的成员以及部门
      const allUserList = instance.getList();
      const selected = instance.getValues();
      const formData = createChatGroupInstance.getValues();
      if (!formData.companyId && (formData.groupType === 9 || formData.groupType === 8)) {
        message.error(`请选择${formData.groupType === 9 ? '合作' : '客户'}群归属组织`);
        return;
      }
      instance.confirmLoading();
      createGroup({
        ...formData,
        userIds: allUserList
          .filter((item) => item.type === 'users' && item?.accountType !== 1024)
          .map((every) => every.id),
        groupIds: selected.groups,
        orgIds: selected.departments,
        queryAgentList: allUserList
          .filter((item) => item.type === 'users' && item?.accountType === 1024)
          .map((every) => ({
            ...every,
            name: every.label,
          })),
      } as CreateChatGroupProps)
        .then((res: SessionResult) => {
          if (isFunction(config.onSuccess)) {
            config.onSuccess(res);
          }
          close();
        })
        .finally(() => {
          instance.confirmLoading(false);
        });
    },
  });
}

export default createChatGroup;
