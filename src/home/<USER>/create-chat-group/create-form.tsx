import { forwardRef, ReactNode, useImperativeHandle, useState } from 'react';
import { Form, Input, Select, Switch } from 'antd';
import { useMemoizedFn } from 'ahooks';
import isNull from 'lodash/isNull';
import isUndefined from 'lodash/isUndefined';
import { getCompaniesForInfo, GetCompanyForInfoResult } from '@/apis';
import { user } from '@/store';
import { GroupTypes } from '../../utils/relation';
import styles from './create-form.module.less';
import { SelectUsersValues } from '../select-users/util';

export interface CreateFormValues {
  name?: string;
  groupType?: number;
  isSearch?: 0 | 1;
  joinCheck?: 0 | 1;
  seeHistory?: 0 | 1;
  companyId?: number;
}

export interface CreateFormInstance {
  getValues: () => CreateFormValues;
  // eslint-disable-next-line no-unused-vars
  setSelected: (selected: SelectUsersValues) => void;
}

const CreateForm = forwardRef<CreateFormInstance>((_, ref) => {
  const [form] = Form.useForm<CreateFormValues>();
  const companyId = Form.useWatch('companyId', form);
  const [cooperation, setCooperation] = useState(false);
  const [companies, setCompanies] = useState<GetCompanyForInfoResult[] | null>(null);
  const [innerCompanies, setInnerCompanies] = useState<unknown[]>([]);

  const setCompanyList = (list?: GetCompanyForInfoResult[] | null) => {
    if (isNull(list)) {
      setCompanies(null);
      setInnerCompanies([]);
    } else {
      let companyList: GetCompanyForInfoResult[];
      if (isUndefined(list)) {
        companyList = user.companies.filter((company) => company.nature !== 1);
      } else {
        companyList = list;
        setInnerCompanies(list.map((item) => item.id));
      }
      setCompanies(companyList);
    }
  };

  const changeGroupType = useMemoizedFn((value: number) => {
    switch (value) {
      case GroupTypes.COOPERATION:
      case GroupTypes.CUSTOMER:
        setCompanyList();
        form.setFieldsValue({
          groupType: value,
          isSearch: 0,
          joinCheck: 1,
          seeHistory: 1,
          companyId: undefined,
        });
        break;
      case GroupTypes.INNER:
        if (companies && innerCompanies.length) {
          setCompanies(companies.filter((company) => innerCompanies.includes(company.id)));
        }
        form.setFieldsValue({
          companyId,
          groupType: GroupTypes.INNER,
          isSearch: 0,
          joinCheck: 0,
          seeHistory: 1,
        });
        break;
      default:
        setCompanyList(null);
        form.setFieldsValue({
          groupType: GroupTypes.COMMON,
          isSearch: 1,
          joinCheck: 0,
          seeHistory: 1,
          companyId: undefined,
        });
    }
    const tempCooperation = value === GroupTypes.COOPERATION;
    if (tempCooperation !== cooperation) {
      setCooperation(tempCooperation);
    }
  });
  const setSelected: CreateFormInstance['setSelected'] = useMemoizedFn(
    ({ selected, num, items }) => {
      const groupType = form.getFieldValue('groupType');
      if (num > 1) {
        const users = [...selected.users];
        if (selected.customers.length > 0) {
          items.forEach((item) => {
            if (item.type === 'customers') {
              item.members.forEach((member) => {
                users.push(member.id);
              });
            }
          });
        }
        getCompaniesForInfo({
          userIds: selected.users,
          groupIds: selected.groups,
          orgIds: selected.departments,
        }).then(({ list }) => {
          if (list.length !== 0) {
            if (!groupType || groupType === GroupTypes.COMMON || groupType === GroupTypes.INNER) {
              setCompanyList(list);
              if (import.meta.env.BIZ_APP_PLATFORM_NO !== '1') {
                changeGroupType(GroupTypes.INNER);
                form.setFieldsValue({ companyId: list[0].id });
              }
            } else {
              setInnerCompanies(list.map((item) => item.id));
            }
          } else if (
            (!groupType || groupType === GroupTypes.INNER) &&
            import.meta.env.BIZ_APP_PLATFORM_NO !== '1'
          ) {
            changeGroupType(GroupTypes.CUSTOMER);
          }
        });
      } else {
        // 内部群清空公司相关所有信息
        // 非内部群清空允许选择内部群的公司ID列表
        if (groupType === GroupTypes.INNER) {
          setCompanyList(null);
        } else if (innerCompanies.length !== 0) {
          setInnerCompanies([]);
        }

        // 社区型独立站默认选择普通群
        // 非社区型独立站默认选择客户群
        if (import.meta.env.BIZ_APP_PLATFORM_NO === '1') {
          changeGroupType(GroupTypes.COMMON);
        } else {
          changeGroupType(GroupTypes.CUSTOMER);
        }
      }
    }
  );

  useImperativeHandle(
    ref,
    () => ({
      setSelected,
      getValues: () => form.getFieldsValue(),
    }),
    [form, setSelected]
  );

  const items: ReactNode[] = [];
  // 不在社区型独立站中时
  if (import.meta.env.BIZ_APP_PLATFORM_NO !== '1') {
    items.push(
      <Form.Item key="groupType" label="群类型" name="groupType" className={styles.cell}>
        <Select placeholder="选择群类型" onSelect={changeGroupType}>
          <Select.Option value={GroupTypes.CUSTOMER}>客户群</Select.Option>
          {/* <Select.Option value={GroupTypes.COOPERATION}>合作群</Select.Option> */}
          <Select.Option value={GroupTypes.COMMON}>普通群</Select.Option>
          {innerCompanies.includes(companyId) && (
            <Select.Option value={GroupTypes.INNER}>内部群</Select.Option>
          )}
        </Select>
      </Form.Item>
    );
    if (companies) {
      items.push(
        <Form.Item key="company" label="归属组织" name="companyId" className={styles.cell}>
          <Select placeholder="选择归属组织">
            {companies.map((company) => (
              <Select.Option key={company.id} value={company.id}>
                {company.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
      );
    }
  }

  return (
    <Form form={form} layout="vertical" className={styles.form}>
      <Form.Item label="群名称" name="name" initialValue="" className={styles.cell}>
        <Input placeholder="取个群名称方便后续搜索" maxLength={30} showCount />
      </Form.Item>
      {items}
      <div className={styles.cell}>
        <div className={styles.cellLabel}>
          <h4>群可被搜索</h4>
          <p className={styles.desc}>仅内部成员可以通过群号或群名搜索并加入该群</p>
        </div>
        <Form.Item
          name="isSearch"
          valuePropName="checked"
          initialValue={false}
          className={styles.cellValue}
        >
          <Switch />
        </Form.Item>
      </div>
      <div className={styles.cell}>
        <div className={styles.cellLabel}>
          <h4>入群验证</h4>
          <p className={styles.desc}>新成员入群需要通过群主的验证</p>
        </div>
        <Form.Item
          name="joinCheck"
          valuePropName="checked"
          initialValue={false}
          className={styles.cellValue}
        >
          <Switch disabled={cooperation} />
        </Form.Item>
      </div>
      <div className={styles.cell}>
        <div className={styles.cellLabel}>
          <h4>新成员可查看聊天记录</h4>
          <p className={styles.desc}>新成员入群可查看最近200条聊天记录</p>
        </div>
        <Form.Item
          name="seeHistory"
          valuePropName="checked"
          initialValue={false}
          className={styles.cellValue}
        >
          <Switch />
        </Form.Item>
      </div>
    </Form>
  );
});

CreateForm.displayName = 'CreateChatGroupForm';

export default CreateForm;
