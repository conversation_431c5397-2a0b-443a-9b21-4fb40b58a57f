@import 'styles/mixins/mixins';

.navbar {
  .flex-row(61px);

  border-top: 1px solid #f5f6f7;
  overflow: hidden;
}

.body {
  width: 100%;
  height: 100%;
  //box-shadow: 0 4px 20px 0 rgba(57, 61, 72, 0.08);
}

.list {
  display: flex;
  align-items: center;
  width: 100%;
  margin: 0;
  list-style: none;
}

.screen-sm({
  .navbar {
    .flex-column(84px);
    max-height: none;
    padding-right: 24px;
    border-top: none;
  }

  .body {
    display: flex;
    flex-flow: column;

    &::before,
    &::after {
      .flex-row();
      content: '';
      display: block;
      min-height: 16px;
      background-color: #fff;
    }
  }

  .list {
    flex-flow: column;
    justify-content: center;
  }

  .badge {
    :global {
      .ant-badge-count {
        top: 27px;
      }
    }
  }
});
