import { Cmd, CmdType, Message, MessageType } from '@echronos/plugin-im';
import type { SessionResult } from '@/apis';
import { sessions, user } from '@/store';
import { notice } from '@/utils/utils';
import { initPermission } from '@/utils/permission';
import { SessionTypes } from '@/utils/session';
import { Modal } from 'antd';
import { SessionGroupInstance } from '../layouts/components';
import { UserRelations, GroupTypes } from './relation';

/**
 * 处理 IM 命令
 * @param cmd
 */
export function handleImCmd(cmd: Cmd) {
  switch (cmd.type) {
    // 更新回话未读数
    case CmdType.UPDATE_SESSION_DOT:
      sessions.setAttrs(cmd.sessionId, { readNum: cmd.num });
      break;
    // 撤回消息
    case CmdType.WITHDRAW:
      if (sessions.session.id !== cmd.sessionId) {
        const session = sessions.find(cmd.sessionId);
        if (session) {
          sessions.setAttrs(cmd.sessionId, {
            lastMessage: {
              ...session.lastMessage,
              msgType: MessageType.WITHDRAW,
              msg: `${cmd.content?.fromName}撤回了一条信息`,
            },
          });
        }
      }
      break;
    // 添加回话
    case CmdType.ADD_SESSION:
      if (!sessions.find(cmd.sessionId)) {
        sessions.update(cmd.sessionId);
      }
      break;
    // 更新回话
    case CmdType.UPDATE_SESSION:
      sessions.update(cmd.sessionId).then(() => {
        sessions.toggleTopState(cmd.sessionId);
      });
      break;
    // 删除会话命令
    case 19:
      sessions.remove(cmd.sessionId);
      break;
    // 更新权限列表和会话列表
    case 20:
      initPermission(true);
      sessions.init(true);
      break;
    // 新增会话
    case 23:
      sessions.getOrAddSession(cmd.sessionId);
      break;
    default: {
      if (!cmd.sessionId) {
        return;
      }
      let text: string | null;
      switch (cmd.type) {
        // 解散圈聊
        case CmdType.DISBAND_CHAT_GROUP:
          text = '此群聊已被管理员解散';
          break;
        // 退出圈聊
        case CmdType.EXIT_CHAT_GROUP:
          text = '您已退出群聊';
          break;
        // 入口移除
        case CmdType.REMOVE_CHAT_GROUP:
          text = '您已被管理员移出群聊';
          break;
        default:
          text = null;
      }
      if (text) {
        const session = sessions.find(cmd.sessionId);
        if (session) {
          if (sessions.hasSelected(session)) {
            Modal.info({
              title: '提示',
              content: `${text}，会话入口将被删除`,
              okText: '我知道了',
              centered: true,
              onOk: () => {
                sessions.remove(cmd.sessionId);
              },
            });
          } else {
            sessions.remove(session.id);
          }
        }
      }
    }
  }
}

/**
 * 消息提示
 * @param session
 * @param message
 */
function messageNotice(session: SessionResult, message: Message) {
  if (
    // 当前网址不是社区型独立站, 或者满足一下两点
    // 1. 私聊会话的会话双方在社区型独立站中不是同事关系
    // 2. 群聊会话不是下列中的任意一种:
    //  2.1 组织全员群聊
    //  2.2 组织部门群聊
    //  2.3 组织内部群聊
    //  2.4 匿名群聊
    (import.meta.env.BIZ_APP_PLATFORM_NO !== '1' ||
      (session.relation !== UserRelations.COLLEAGUE &&
        session.groupType !== GroupTypes.INNER &&
        session.groupType !== GroupTypes.ALL_USER &&
        session.groupType !== GroupTypes.DEPARTMENT &&
        session.groupType !== GroupTypes.ANONYMOUS)) &&
    // 会话没有被屏蔽或免打扰
    !(session.isShield || session.isDisturb) &&
    // 消息不是系统消息
    message.type !== MessageType.SYSTEM &&
    // 消息不是当前用户发送的
    message.userId !== user.id
  ) {
    let title = session.name || message.sessionTitle || message.nickname;
    const options = {
      avatar: session.avatar || message.sessionIcon || message.avatar,
      body: message.content as string,
    };

    // 模板消息
    if (message.type === MessageType.TEMPLATE) {
      const content = (message.content || {}) as Record<string, unknown>;
      // 模板标题
      title = content.title as string;
      // 获取华华生意圈 logo
      options.avatar = `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/static/img/logo.png`;
      // 获取模板介绍
      const desc = ((content.data || []) as { type: string; value: string }[]).find(
        (item) => item.type === 'desc'
      );
      options.body = (desc && desc.value) || message.toString();
    } else if (message.type !== MessageType.TEXT && message.type !== MessageType.NOTICE) {
      options.body = `[${message.toString()}]`;
    }

    // 如果回话标题存在
    // 并且不是通知消息,添加发送者昵称
    if (message.sessionTitle && message.type !== MessageType.NOTICE) {
      options.body = `${message.nickname}：${options.body}`;
    }
    return notice(title, options);
  }
  return Promise.reject();
}

/**
 * 处理 IM 消息
 * @param message
 * @param list
 */
export function handleImMessage(message: Message, list?: SessionGroupInstance) {
  // 获取/添加回话
  return sessions.getOrAddSession(message.sessionId).then((session) => {
    // 回话存在
    if (session && session.sessionType !== SessionTypes.SHOP) {
      let readNum = session.readNum || 0;
      if (message.userId !== user.id) {
        // 判断回话是否选中,选中将未读数置 0
        if (sessions.hasSelected(session)) {
          sessions.sendClearCount(session);
          readNum = 0;
        } else {
          const { lastMessage } = session;
          if (!lastMessage || lastMessage.sMsgId !== message.id) {
            readNum += 1;
          }
        }
      }

      // 判断是否被 @
      let at = false;
      const { externals } = message;
      if (externals && externals.length && session.id !== sessions.activeId) {
        for (let i = 0; i < externals.length; i += 1) {
          if (externals[i].value === 0 || externals[i].value === user.id) {
            at = true;
          }
        }
      }

      // 更新回话
      const index = sessions.setAttrs(session.id, {
        at,
        readNum,
        lastMsgTime: message.sendTime ? message.sendTime.getTime() : Date.now(),
        lastMessage: {
          msg: message.content,
          msgType: message.type,
        },
      });
      // 回话置顶
      sessions.toggleTopState(session.id, session.isTop);

      // 判断是否需要更新长列表
      if (list && index !== -1) {
        list.resetAfter(index);
      }

      // 如果消息不选择性隐藏
      // 进入消息提示
      if (message.status !== 5) {
        return messageNotice(session, message);
      }
    }
    return Promise.reject();
  });
}
