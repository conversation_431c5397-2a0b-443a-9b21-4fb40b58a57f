import {
  CSSProperties,
  forwardRef,
  useCallback,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { Link } from 'react-router-dom';
import { But<PERSON>, Col, message, Row, Spin } from 'antd';
import debounce from 'lodash/debounce';
import { Avatar, dialog, Icon } from '@/components';
import {
  editUserNickname,
  editUserRemarkById,
  userCardInfo,
  UserCardInfoResult,
  OrganizationList,
  removeUserFriend,
} from '@/apis';
import { layout, sessions, user } from '@/store';
import classNames from 'classnames';
import { isFriend } from '../../utils/relation';
import styles from './popover.module.less';
import ChatTag from '../../components/chat-tag';
import CompanyInfo from './company-info';
import EditAttr from './edit-attr';
import addFriend from '../add-friend';

export interface PopoverInstance {
  show: MultipleParamsFn<
    [userId: number, el: HTMLElement, groupId?: number, allowSendMessage?: boolean]
  >;
  hide: () => void;
}

const Popover = forwardRef<PopoverInstance>((_, ref) => {
  const [position, setPosition] = useState(null as null | { x: number; y: number; send: boolean });
  const [loading, setLoading] = useState(true);
  const [info, setInfo] = useState(null as null | UserCardInfoResult);
  const [companies, setCompanies] = useState([] as OrganizationList[]);
  const isCurrentUser = useMemo(() => !!info && info.userId === user.id, [info]);
  const style = useMemo(() => {
    if (position) {
      const offsetWidth = 336;
      const offsetHeight = 605;
      return {
        display: 'block',
        top: Math.min(
          window.innerHeight - offsetHeight,
          Math.max(0, position.y - offsetHeight / 2)
        ),
        left: Math.max(0, position.x - offsetWidth - 10),
      };
    }
    return {};
  }, [position]);
  const addOrRemoveFriend = useMemo(
    () =>
      debounce(() => {
        if (!info) return;
        setPosition(null);
        if (isFriend(info)) {
          const instance = dialog({
            title: '删除好友',
            content: (
              <div className="text-center">
                <p className="mb-0">您确定删除好友？删除后与对方的</p>
                <p className="mb-0">会话记录会清空。</p>
              </div>
            ),
            centered: true,
            width: 330,
            onConfirm: () => {
              instance.update({ okButtonProps: { icon: <Icon name="loading" /> } });
              return removeUserFriend(info.userId).then((result) => {
                message.success('好友删除成功');
                setInfo({
                  ...info,
                  relation: info.relation - 1,
                });

                if (result && result.sessionId) {
                  sessions.remove(result.sessionId);
                }
              });
            },
          });
        } else {
          addFriend(info.userId);
        }
      }, 150),
    [info]
  );
  const onToImc = useCallback(() => {
    layout.closeSearch();
    if (import.meta.env.SSR) {
      window.hidePopover();
    }
  }, []);

  useImperativeHandle(ref, () => ({
    show: (id, el, groupId, allowSend = false) => {
      // if (!info || info.userId !== id) {
      setLoading(true);
      const promise = Promise.all([
        userCardInfo({
          groupId,
          otherUserId: id,
        }).then((res) => {
          setInfo(res);
          setCompanies(res.organizationList || []);
        }),
      ]);
      promise.finally(() => {
        setLoading(false);
      });
      // }
      setTimeout(() => {
        const rect = el.getBoundingClientRect();
        setPosition({ x: rect.x, y: rect.y, send: allowSend });
      });
    },
    hide: () => {
      setPosition(null);
    },
  }));

  let containerClasses = classNames(styles.container, {
    [styles.shortContainer]: import.meta.env.BIZ_APP_PLATFORM_NO === '1',
  });
  const bodyClasses = classNames(styles.body, {
    [styles.shortBody]: import.meta.env.BIZ_APP_PLATFORM_NO === '1',
  });
  let containerStyle: CSSProperties | undefined;
  if (import.meta.env.BIZ_APP_PLATFORM_NO === '1') {
    containerStyle = { height: '320px' };
  } else if (info && info.groupNickName) {
    containerClasses = '';
    containerStyle = { height: '624px' };
  }
  return (
    <div data-userinfo="" className={styles.userinfo} style={style}>
      <Spin spinning={loading}>
        <div className={containerClasses} style={containerStyle}>
          {loading || !info ? null : (
            <>
              <div
                className={
                  info && info.groupNickName && info.companyName ? styles.head : styles.headCenter
                }
              >
                <Avatar src={info.avatar} size={50} primaryKey={info.userId} />
                <div className={`px-2 ${styles.info}`}>
                  <div
                    title={info.nickname}
                    className={info && (info.groupNickName || info.companyName) ? 'mb-1' : ''}
                  >
                    <h3 className={styles.nickname}>{info.nickname}</h3>
                    <ChatTag value={info} className="ml-1" />
                  </div>
                  {info.groupNickName ? (
                    <div className={`mb-1 ${styles.companyName}`}>群昵称：{info.groupNickName}</div>
                  ) : null}
                  {import.meta.env.BIZ_APP_PLATFORM_NO !== '1' && (
                    <p className={`mb-0 ${styles.companyName}`}>{info.companyName}</p>
                  )}
                </div>
                {!isCurrentUser && (
                  <button type="button" className={styles.addUser} onClick={addOrRemoveFriend}>
                    <Icon name={isFriend(info) ? 'remove-user' : 'add-user'} size={18} />
                  </button>
                )}
              </div>
              <div className={bodyClasses}>
                {import.meta.env.BIZ_APP_PLATFORM_NO !== '1' &&
                  companies.map((company, index) => (
                    <CompanyInfo key={`${company.phone + index}`} info={company} index={index} />
                  ))}
                <div className={`mb-2 ${styles.box}`}>
                  <h4 className={styles.boxTitle}>个人信息</h4>
                  <div className={styles.boxBody}>
                    <div className="mb-5">
                      <div className={styles.label}>昵称</div>
                      {isCurrentUser ? (
                        <EditAttr
                          value={info.userNickName}
                          onUpdate={(value) =>
                            editUserNickname(value).then(() => {
                              setInfo({ ...info, userNickName: value });
                            })
                          }
                        />
                      ) : (
                        <div className="mt-1">{info.userNickName}</div>
                      )}
                    </div>
                    {!isCurrentUser && (
                      <div>
                        <div className={styles.label}>备注</div>
                        <EditAttr
                          value={info.remark}
                          className="mt-1"
                          onUpdate={(remark) =>
                            editUserRemarkById(info.userId, remark).then(() => {
                              setInfo({ ...info, remark });
                              const session = sessions.findUser(info.userId);
                              if (session) {
                                if (!import.meta.env.SSR) {
                                  // eslint-disable-next-line no-underscore-dangle
                                  window.__store.commit('setSessionAttr', {
                                    id: session.id,
                                    data: { remark },
                                  });
                                }
                              }
                            })
                          }
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <Row className={styles.footer}>
                <Col className={styles.footerCol}>
                  <Link to={`/imc/0?userId=${info.userId}`}>
                    <Button block type="primary" shape="round" size="large" onClick={onToImc}>
                      发送消息
                    </Button>
                  </Link>
                </Col>
              </Row>
            </>
          )}
        </div>
      </Spin>
    </div>
  );
});

Popover.displayName = 'UserinfoPopover';

export default Popover;
