import { useState } from 'react';
import { UserByIdResult, editUserRemarkById } from '@/apis';
import { Input } from 'antd';
import { Icon } from '@/components';
import styles from './popover.module.less';

export interface PersonalInfoProps {
  info: UserByIdResult;
  // eslint-disable-next-line no-unused-vars
  setAttr: (key: string, value: string | number | boolean) => void;
}

function PersonalInfo({ info, setAttr }: PersonalInfoProps) {
  const [isEdit, setIsEdit] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  return (
    <div className={`mb-2 ${styles.box}`}>
      <h4 className="px-4 py-4 mb-0">个人信息</h4>
      <div className={styles.boxBody}>
        <div className="px-4 py-3">
          <div className={styles.label}>昵称</div>
          <div className="mt-1">{info.nickname}</div>
        </div>
        <div className="px-4 py-3">
          <div className={styles.label}>备注</div>
          <div className="mt-1">
            {isEdit ? (
              <Input
                defaultValue={info.remark}
                autoFocus
                disabled={submitting}
                suffix={submitting ? <Icon name="loading" /> : null}
                onBlur={(e) => {
                  if (e.target.value === '') {
                    setIsEdit(false);
                  } else {
                    setSubmitting(true);
                    editUserRemarkById(info.userId, e.target.value)
                      .then(() => {
                        setAttr('remark', e.target.value);
                      })
                      .finally(() => {
                        setSubmitting(false);
                        setIsEdit(false);
                      });
                  }
                }}
              />
            ) : (
              <>
                <span className="mr-1">{info.remark || '未设置'}</span>
                <Icon
                  name="edit1"
                  size={16}
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    setIsEdit(true);
                  }}
                />
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default PersonalInfo;
