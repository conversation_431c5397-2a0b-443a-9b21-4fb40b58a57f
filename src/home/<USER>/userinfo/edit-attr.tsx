import { HTMLAttributes, useState } from 'react';
import { Input } from 'antd';
import { Icon } from '@/components';

export interface EditAttrProps extends HTMLAttributes<HTMLDivElement> {
  value: string;
  // eslint-disable-next-line no-unused-vars
  onUpdate: (value: string) => Promise<void>;
}

function EditAttr({ value, onUpdate, ...props }: EditAttrProps) {
  const [isEdit, setIsEdit] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  return (
    <div {...props}>
      {isEdit ? (
        <Input
          defaultValue={value}
          autoFocus
          disabled={submitting}
          suffix={submitting ? <Icon name="loading" /> : <span />}
          onBlur={(e) => {
            if (e.target.value === value) {
              setSubmitting(false);
            } else {
              setSubmitting(true);
              onUpdate(e.target.value).finally(() => {
                setIsEdit(false);
                setSubmitting(false);
              });
            }
          }}
        />
      ) : (
        <>
          <span className="mr-1">{value || '未设置'}</span>
          <Icon
            name="edit1"
            size={16}
            style={{ cursor: 'pointer' }}
            onClick={(e) => {
              setIsEdit(true);
              e.stopPropagation();
            }}
          />
        </>
      )}
    </div>
  );
}

export default EditAttr;
