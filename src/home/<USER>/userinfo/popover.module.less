@import 'styles/mixins/mixins';

.userinfo {
  display: none;
  width: 300px;
  min-height: 200px;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 40%);
  position: fixed;
  top: 0;
  z-index: 3001;

  .screen-xs({
    width: 336px;
  });
}

.head,
.headCenter {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f5f6fa;

  //:global {
  //  .ant-avatar {
  //    border: 1px solid #e5e5e6;
  //  }
  //}
}

.headCenter {
  align-items: center;
}

.info {
  .flex-column(186px);

  .screen-xs({
    .flex-column(222px);
  });
}

.nickname,
.companyName {
  .text-overflow();
}

.nickname {
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
  max-width: 128px;
  margin-bottom: 0;
  vertical-align: middle;

  .screen-xs({
    max-width: 150px;
  });
}

.companyName {
  color: @text-color-secondary;
}

.addUser {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 1px solid #fff;
  cursor: pointer;
}

.body {
  height: 260px;
  padding: 0 16px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;

  .screen-xs({
    height: 447px;
  });
}

.shortBody {
  .screen-xs({
    height: 240px;
  });
}

.box {
  border-top: 1px solid #f5f6fa;
  padding-top: 20px;

  &:first-child {
    border-top: none;
  }
}

.boxTitle {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
  line-height: 22px;
  padding-bottom: 20px;
}

.label {
  color: #888b98;
  font-size: 12px;
}

.footer {
  padding: 16px 10px;
}

.footerCol {
  .flex-column();

  padding: 0 6px;
}

.togglePhone {
  color: #5e9ff8;
  font-size: 12px;
  margin-left: 8px;
  cursor: pointer;
}

.container {
  height: 605px;
}

.shortContainer {
  height: 400px;
}
