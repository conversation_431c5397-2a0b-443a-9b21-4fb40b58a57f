import { useState } from 'react';
import { OrganizationList } from '@/apis';
import { formatPhone } from '@/utils/utils';
import styles from './popover.module.less';

export interface CompanyInfoProps {
  info: OrganizationList;
  index: number;
}

function CompanyInfo({ info, index }: CompanyInfoProps) {
  const [showMobile, setShowMobile] = useState(false);
  const member = info;

  return (
    <div className={styles.box}>
      {index === 0 ? <h4 className={styles.boxTitle}>组织信息</h4> : null}
      <div className={styles.boxBody}>
        <div className="mb-5">
          <div className={styles.label}>企业/组织</div>
          <div className="mt-1">{info.companyName}</div>
        </div>
        {member && member.nickName ? (
          <div className="mb-5">
            <div className={styles.label}>企业昵称</div>
            <div className="mt-1">{member.nickName}</div>
          </div>
        ) : null}
        {info.phone ? (
          <div className="mb-5">
            <div className={styles.label}>电话</div>
            <div className="mt-1">
              <span>+86-{showMobile ? info.phone : formatPhone(info.phone)}</span>
              <span
                tabIndex={0}
                role="button"
                className={styles.togglePhone}
                onClick={() => {
                  setShowMobile(!showMobile);
                }}
              >
                {showMobile ? '隐藏' : '显示'}
              </span>
            </div>
          </div>
        ) : null}
        {member &&
          member.deptList.map((org) => (
            <div key={org.id} className="mb-5">
              <div className={styles.label}>部门</div>
              <div className="mt-1">{org.orgName}</div>
            </div>
          ))}
        {info.position ? (
          <div className="mb-5">
            <div className={styles.label}>职位</div>
            <div className="mt-1">{info.position}</div>
          </div>
        ) : null}
        {member ? (
          <div className="mb-5">
            <div className={styles.label}>性别</div>
            <div className="mt-1">{member.sex === 0 ? '男' : '女'}</div>
          </div>
        ) : null}
      </div>
    </div>
  );
}

export default CompanyInfo;
