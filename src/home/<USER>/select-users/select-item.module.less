@import 'styles/mixins/mixins';

.group {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;

  .list {
    display: block;
  }

  .item {
    display: flex;
    margin-left: 0 !important;
    padding: 12px 16px;
    user-select: none;
    align-items: center;

    &:hover {
      background-color: #f5f6fa;
    }
  }
}

.item {
  :global {
    .empty-block,
    .ant-checkbox {
      top: 0;

      & + span {
        .flex-column();

        display: block;
        padding-right: 0;
      }
    }

    .ant-checkbox-disabled {
      & + span {
        color: @text-color;
      }
    }
  }
}

.content {
  display: flex;
  align-items: center;
  height: 32px;
}

.avatar {
  .flex-column(32px);

  & + .info {
    padding-left: 8px;
  }
}

.info {
  .flex-column();
}

.label,
.memberNum,
.tag {
  line-height: 18px;
  vertical-align: top;
}

.label,
.description {
  .text-overflow();
}

.label {
  display: inline-block;
  max-width: 100px;
  vertical-align: middle;
}

.description {
  color: @text-colors[secondary];
  font-size: @font-size-sm;
  max-width: 245px;
  line-height: 14px;
}

.moreTree {
  color: #008cff;
  font-size: @font-size-sm;
  display: inline-block;
  line-height: 16px;
  padding: 8px 0;
  cursor: pointer;
  user-select: none;
  float: right;

  &::before {
    content: '';
    display: inline-block;
    width: 16px;
    height: 16px;
    background: url('./img/more-tree.png') no-repeat center / cover;
    vertical-align: top;
  }
}

.disabledTree {
  color: #d8d9de;
  cursor: not-allowed;

  &::before {
    background-image: url('./img/more-tree-disabled.png');
  }
}

.moreIcon {
  line-height: 32px;
  padding-left: 8px;
  float: right;
}
