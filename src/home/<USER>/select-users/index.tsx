import { Col, Modal, ModalProps, Row, Skeleton } from 'antd';
import loadable from '@loadable/component';
import isFunction from 'lodash/isFunction';
import { modalPopup } from '@/utils/popup';
import { forwardRef } from 'react';
import type { BodyInstance, BodyProps } from './body';
import styles from './select-users.module.less';

export type SelectUsersProps = BodyProps &
  Pick<ModalProps, 'zIndex' | 'maskClosable' | 'title' | 'afterClose'> & {
    // eslint-disable-next-line react/require-default-props
    visible?: boolean;
  };

export type SelectUsersInstance = BodyInstance;

export type SelectUsersConfig = SelectUsersProps & {
  onConfirm?: MultipleParamsFn<
    [instance: SelectUsersInstance, close: () => void],
    void | Promise<void>
  >;
};

const SelectUsersBody = loadable(() => import('./body'), {
  fallback: (
    <Row className={styles.body}>
      <Col span={12} className="px-4 pt-4">
        <Skeleton loading active />
      </Col>
      <Col span={12} className="px-4 pt-4">
        <Skeleton loading active />
      </Col>
    </Row>
  ),
});

const SelectUsers = forwardRef<SelectUsersInstance, SelectUsersProps>(
  ({ visible, title, maskClosable, afterClose, zIndex, ...props }, ref) => (
    <Modal
      centered
      visible={visible}
      title={title}
      zIndex={zIndex}
      afterClose={afterClose}
      maskClosable={maskClosable}
      width={730}
      footer={null}
      closable={false}
      className={styles.selectUsers}
      onCancel={props.onCancel}
    >
      <SelectUsersBody {...props} ref={ref} />
    </Modal>
  )
);

/**
 * 选择用人员
 * @param config
 */
function selectUsers(config: SelectUsersConfig) {
  let instance: SelectUsersInstance;

  function ref(ins: SelectUsersInstance) {
    instance = ins;
  }

  const popupInstance = modalPopup(
    SelectUsers,
    { ...config, visible: true },
    {
      ref,
      config: (conf, close) => {
        const currentConfig = conf;

        currentConfig.onOk = (e) => {
          if (isFunction(config.onOk)) {
            config.onOk(e);
          }
          if (isFunction(config.onConfirm)) {
            const promise = config.onConfirm(instance, close);
            if (promise) {
              promise.finally(close);
            }
          } else {
            close();
          }
        };
        return currentConfig;
      },
    }
  );

  return { destroy: popupInstance.destroy };
}

export default selectUsers;
