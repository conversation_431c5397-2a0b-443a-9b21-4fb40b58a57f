import { HTMLAttributes, useCallback, useRef, useState } from 'react';
import { Modal, Tabs, Radio, Spin, Checkbox } from 'antd';
import { Icon } from '@/components';
import styles from './filter-customer.module.less';
import getFilterCustomerConditions from '../../../../apis/get-filter-customer-conditions';

const { TabPane } = Tabs;

export interface FilterCustomerDefaultValue {
  type?: number;
  customerSource?: number;
  industryId?: number;
  tagId?: '';
}

export interface FilterCustomerProps extends HTMLAttributes<HTMLElement> {
  value: FilterCustomerDefaultValue;
  hideFilter?: boolean;
  // eslint-disable-next-line no-unused-vars
  onOk: (data: Record<string, unknown>) => void;
}

function FilterCustomer({ onOk, value, hideFilter, ...props }: FilterCustomerProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState(
    null as unknown as {
      key: string;
      id?: number;
      label: string;
      children: { id: number; name: string }[];
    }[]
  );
  const valueRef = useRef({} as Record<string, unknown>);
  const handleChangePane = useCallback(
    (activeKey: string) => {
      if (activeKey === '1' && !filters) {
        setLoading(true);
        getFilterCustomerConditions()
          .then((res) => {
            const list = [];
            if (res.sources) {
              list.push({ key: 'customerSource', label: '客户来源', children: res.sources });
            }
            if (res.industries) {
              list.push({ key: 'industryId', label: '所属行业', children: res.industries });
            }
            res.tags.forEach((tag) => {
              list.push({ key: 'tagId', id: tag.id, label: tag.name, children: tag.children });
            });
            setFilters(list);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    },
    [filters]
  );

  return (
    <>
      {!hideFilter && (
        <Icon
          {...props}
          name="filter"
          size={16}
          className={styles.icon}
          onClick={() => {
            setVisible(true);
          }}
        />
      )}
      <Modal
        visible={visible}
        title={null}
        destroyOnClose
        className={styles.modal}
        onCancel={() => {
          setVisible(false);
          valueRef.current = {};
        }}
        onOk={() => {
          onOk(valueRef.current);
          setVisible(false);
        }}
      >
        <Tabs defaultActiveKey="0" onChange={handleChangePane}>
          <TabPane tab="分组" key="0">
            <div className={styles.pane}>
              <div className={styles.group}>
                <Radio.Group
                  defaultValue={value.type || 0}
                  buttonStyle="solid"
                  onChange={(e) => {
                    valueRef.current.type = e.target.value;
                  }}
                >
                  <Radio.Button value={0} className={styles.btn}>
                    全部
                  </Radio.Button>
                  <Radio.Button value={1} className={styles.btn}>
                    我负责的
                  </Radio.Button>
                  <Radio.Button value={2} className={styles.btn}>
                    下属负责
                  </Radio.Button>
                </Radio.Group>
              </div>
            </div>
          </TabPane>
          <TabPane tab="筛选" key="1">
            <Spin spinning={loading}>
              <div className={styles.pane}>
                {filters
                  ? filters.map((item) => (
                      <div key={item.id || item.key} className={styles.group}>
                        <h4 className={styles.title}>{item.label}</h4>
                        {item.key === 'tagId' ? (
                          <Checkbox.Group
                            defaultValue={(value[item.key] || '').split(',').map((id) => +id)}
                            onChange={(checkedValue) => {
                              valueRef.current[item.key] = checkedValue.join(',');
                            }}
                          >
                            {item.children.map((child) => (
                              <Checkbox key={child.id} value={child.id} className={styles.button}>
                                {child.name}
                              </Checkbox>
                            ))}
                          </Checkbox.Group>
                        ) : (
                          <Radio.Group
                            defaultValue={value[item.key as 'customerSource' | 'industryId'] || ''}
                            buttonStyle="solid"
                            onChange={(e) => {
                              valueRef.current[item.key] = e.target.value;
                            }}
                          >
                            <Radio.Button value="" className={styles.btn}>
                              全部
                            </Radio.Button>
                            {item.children.map((child) => (
                              <Radio.Button key={child.id} value={child.id} className={styles.btn}>
                                {child.name}
                              </Radio.Button>
                            ))}
                          </Radio.Group>
                        )}
                      </div>
                    ))
                  : null}
              </div>
            </Spin>
          </TabPane>
        </Tabs>
      </Modal>
    </>
  );
}

FilterCustomer.defaultProps = {
  hideFilter: false,
};

export default FilterCustomer;
