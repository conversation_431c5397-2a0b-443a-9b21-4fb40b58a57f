import { HTMLAttributes, useEffect, useRef, useState } from 'react';
import { Spin } from 'antd';
import classNames from 'classnames';
import isArray from 'lodash/isArray';
import { Icon } from '@/components';
import {
  FormatSearchResultKey,
  formatSearchResults,
  SearchRelationCustomer,
  SearchRelationMember,
} from '@/utils/search';
import { useTranslation } from 'react-i18next';
import styles from './select-users.module.less';
import SelectList, { SelectedHandler } from './select-list';
import {
  formatSearchTypes,
  Item,
  SearchGroup,
  searchUsers,
  SelectedIds,
  getCooperation,
} from './util';

export type SearchHandler = MultipleParamsFn<
  [keyword: string, companyId?: number],
  | Item[]
  | SearchGroup
  | Promise<
      | Record<FormatSearchResultKey, SearchRelationMember[] | SearchRelationCustomer[]>
      | Item[]
      | SearchGroup
    >
>;

export interface SearchResultsProps extends HTMLAttributes<HTMLDivElement> {
  selected: SelectedIds;
  selectedItems: Item[];
  defaults: number[];
  disables?: Item[];
  searchKey: string;
  onlyUser?: boolean;
  companyMemberRadio?: boolean;
  userIdKey?: string;
  searchIsFilter?: null | 0 | 1;
  userLabelKey?: string;
  cooperation?: boolean;
  companyId?: number;
  selectSupplierMember?: boolean;
  isValidityDay?: boolean;
  selectSupplierCompanyAndMember?: boolean;
  selectCustomerCompanyAndMember?: boolean;
  search?: SearchHandler;
  onSelected?: SelectedHandler;
}

function SearchResults({
  selected,
  selectedItems,
  searchKey,
  defaults,
  disables,
  onlyUser,
  cooperation,
  companyId,
  userIdKey,
  companyMemberRadio,
  searchIsFilter,
  userLabelKey,
  selectSupplierMember,
  isValidityDay,
  selectSupplierCompanyAndMember,
  selectCustomerCompanyAndMember,
  search,
  onSelected,
  className,
  ...props
}: SearchResultsProps) {
  const [searching, setSearching] = useState(false);
  const [groups, setGroups] = useState([] as SearchGroup[]);
  const [error, setError] = useState('');
  const [listTitle, setListTitle] = useState('');
  const history = useRef([] as Array<Array<SearchGroup>>);
  const { t } = useTranslation();

  useEffect(() => {
    if (searchKey) {
      setSearching(true);
      setError('');
      let promise;
      if (search) {
        promise = search(searchKey, companyId);
        if (!(promise instanceof Promise)) {
          promise = Promise.resolve(promise);
        }
        promise = promise.then((results) => {
          if (isArray(results)) {
            return [{ label: '', list: results }];
          }
          if ('label' in results) {
            return [results];
          }
          return Promise.resolve(results).then(formatSearchResults).then(formatSearchTypes);
        });
      } else if (cooperation) {
        promise = getCooperation(3, searchKey).then((res) => {
          if (res.list.length === 0) {
            setError('没有找到结果');
          }
          return [
            {
              label: '',
              list: res.list,
            } as SearchGroup,
          ];
        });
      } else {
        promise = searchUsers(
          {
            companyId: companyId || undefined,
            search: searchKey,
            isFilter: searchIsFilter as 0 | 1 | null,
            isValidityDay,
          },
          userIdKey,
          userLabelKey
        );
      }
      promise
        .then((res) => {
          setGroups(res);
          setSearching(false);
          if (res.length === 0) {
            setError('没有找到结果');
          }
        })
        .catch((err) => {
          if (err.message !== 'cancel') {
            setSearching(false);
            setError(err.message);
          }
        });
    } else {
      setError('');
      setSearching(false);
    }
  }, [
    companyId,
    search,
    searchKey,
    cooperation,
    userIdKey,
    searchIsFilter,
    userLabelKey,
    isValidityDay,
  ]);

  const classes = classNames(styles.searchBox, className);
  return (
    <div {...props} className={classes}>
      <Spin spinning={searching}>
        {!!listTitle && (
          <div className={styles.head}>
            <Icon
              name="left"
              size={18}
              className={styles.back}
              onClick={() => {
                const item = history.current.pop();
                setListTitle('');
                if (item) {
                  setGroups(item);
                } else {
                  history.current = [];
                  setGroups([]);
                }
              }}
            />
            <div className={styles.headTitle}>{listTitle}</div>
          </div>
        )}
        {(error && <div className={styles.error}>{error}</div>) ||
          (!groups.length ? (
            <div />
          ) : (
            groups.map((group) => (
              <div key={group.label}>
                {group.label ? <div className={styles.searchTitle}>{group.label}</div> : null}
                <SelectList
                  key={group.label}
                  defaults={defaults}
                  list={group.list}
                  selected={selected}
                  selectedItems={selectedItems}
                  companyMemberRadio={companyMemberRadio}
                  onlyUser={onlyUser}
                  disables={disables}
                  selectSupplierCompanyAndMember={selectSupplierCompanyAndMember}
                  selectCustomerCompanyAndMember={selectCustomerCompanyAndMember}
                  selectSupplierMember={selectSupplierMember}
                  onSelected={onSelected}
                  onMore={(item) => {
                    if (item.type && (item.type === 'suppliers' || item.type === 'customers')) {
                      history.current.push(groups);
                      setListTitle(t('rq_search_results'));
                      setGroups([
                        {
                          label: `${t('rq_supplier_name')}：${item.label}`,
                          list: item.members,
                        },
                      ]);
                    }
                  }}
                />
              </div>
            ))
          ))}
      </Spin>
    </div>
  );
}

SearchResults.displayName = 'SearchUsersSearchResults';

SearchResults.defaultProps = {
  onlyUser: false,
  disables: [],
  companyId: undefined,
  onSelected: undefined,
  searchIsFilter: null,
  userLabelKey: undefined,
  userIdKey: 'userId',
  companyMemberRadio: false,
  search: undefined,
  isValidityDay: false,
  selectSupplierCompanyAndMember: false,
  selectCustomerCompanyAndMember: false,
  selectSupplierMember: false,
  cooperation: false,
};

export default SearchResults;
