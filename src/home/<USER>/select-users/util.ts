import {
  CompanyOrganizationBreadcrumbResult,
  CompanyOrganizationMemberResult,
  getAcceptChatCustomerList,
  GetChatGroupMemberResult,
  getChatGroupMembers,
  getCompanyOrganizations,
  GetCompanyOrganizationsProps,
  getDistributionInfo,
  getRecentChatList,
  getStrangersForUser,
  getUserChatGroups,
  getUserFriends,
  roleList,
  searchAllRelationUsers,
  SearchAllRelationUsersProps,
  UserFriendResult,
  getQueryCustomerForMe,
  getSelectSupplier,
  getAuthCustomer,
  getAuthSupplier,
} from '@/apis';
import { CancelTokenSource, getCancelToken } from '@/utils/http';
import { getDefaultAvatar } from '@/utils/utils';
import {
  SearchRelationCustomerResult,
  SearchRelationUserResult,
  SearchRelationSupplierResult,
  SearchRelationCustomersCompanyResult,
} from '@/utils/search';
import { nycbngShareContactsResponsesData } from '@/apis/user/get-distributor-info';
import i18n from '@/entry/i18n';
import { isColleague, isCompanyGroup } from '../../utils/relation';
import friends from './img/friends.png';
import groups from './img/groups.png';
import chats from './img/chats.png';
import strangers from './img/strangers.png';

export type SelectTypeKeys = 'friends' | 'groups' | 'chats' | 'strangers';

export type SelectItemKeys =
  | 'users'
  | 'groups'
  | 'departments'
  | 'customers'
  | 'roles'
  | 'distributions'
  | 'suppliers';

export type SelectOnlyDepartment = boolean | 'last';

export type SelectedIds = Record<SelectItemKeys, number[]>;

export interface SelectTypeItem {
  key: SelectTypeKeys;
  label: string;
  icon: string;
}

interface BaseItem {
  id: number;
  label: string;
  closable?: boolean;
  description?: string;
  hideMore?: boolean; // 是否隐藏下级
  memberId?: number;
}

export interface UserItem extends BaseItem {
  type?: 'users';
  avatar?: string;
  memberId?: number;
  userId?: number;
  relation?: number;
  phone?: string;
  position?: string;
  isInvitation?: number;
  companyId?: number;
  companyName?: string;
  customerId?: number;
  accountType?: number;
}

export interface GroupItem extends BaseItem {
  type: 'groups';
  avatar: string;
  userIds: number[];
  groupType: number;
}

export interface OrganizationItem extends BaseItem {
  type: 'departments';
  ancestors?: string; // 判断部门层级
  userIds: number[];
  members: UserItem[];
  hideCount?: boolean; // 隐藏成员数量
}

export interface CustomerItem extends BaseItem {
  type: 'customers';
  members: UserItem[];
  userIds: number[];
}

export interface SupplierItem extends BaseItem {
  type: 'suppliers';
  companyId: number;
  members: UserItem[];
  userIds: number[];
}

export interface RoleItem extends BaseItem {
  type: 'roles';
}

export interface DistributionItem extends BaseItem {
  type: 'distributions';
  members: UserItem[];
  userIds: number[];
}
export type Item =
  | UserItem
  | GroupItem
  | OrganizationItem
  | CustomerItem
  | RoleItem
  | DistributionItem
  | SupplierItem;

export type SelectGroup = { key: SelectItemKeys; list: Item[] };

// eslint-disable-next-line no-unused-vars
export type SelectItemHandler = (item: Item) => void;

export type SearchGroup = { label: string; list: Item[] };

export interface HistoryItem {
  key: string;
  label: string;
  type?: string;
  props?: Record<string, unknown>;
}

export interface SelectUsersValues {
  selected: SelectedIds;
  items: Item[];
  num: number;
}

/**
 * 所有用户可选择的类型
 */
export const allSelectTypes: SelectTypeItem[] = [
  { key: 'friends', label: i18n.t('marking_tool_my_friend'), icon: friends },
  { key: 'groups', label: i18n.t('marking_tool_my_group'), icon: groups },
  { key: 'chats', label: i18n.t('marking_tool_my_chat'), icon: chats },
  { key: 'strangers', label: i18n.t('marking_tool_my_stranger'), icon: strangers },
];

let tokenSource: null | CancelTokenSource;

/**
 * 更新请求取消 token
 */
function updateCancelToken() {
  if (tokenSource) {
    try {
      tokenSource.cancel('cancel');
    } catch (e) {
      console.warn(e);
    }
  }
  tokenSource = getCancelToken();
}

export function cancelToken() {
  tokenSource = null;
}

function formatUserItem(item: UserFriendResult) {
  const friend = {
    id: item.userId,
    relation: item.relation,
    avatar: item.avatar || getDefaultAvatar(item.userId),
    type: 'users',
  } as UserItem;
  if (isColleague(item)) {
    friend.label = item.remark || item.stageName || item.nickname;
    friend.description = item.companyName;
  } else {
    friend.label = item.remark || item.nickname;
  }
  return friend;
}

/**
 * 获取选择的类型数据
 * @param historyItem
 */
export function getSelectTypeData(historyItem: HistoryItem): Promise<Item[]> {
  updateCancelToken();
  switch (historyItem.key) {
    case 'friends':
      return getUserFriends(tokenSource!.token, false).then(({ list }) =>
        list.map((item) => formatUserItem(item))
      );
    case 'groups':
      if (historyItem.props && historyItem.props.id) {
        const getLabel = isCompanyGroup(historyItem.props as { groupType: number })
          ? (item: GetChatGroupMemberResult) =>
              item.remark || item.stageName || item.alias || item.nickname
          : (item: GetChatGroupMemberResult) => item.remark || item.alias || item.nickname;
        return getChatGroupMembers(historyItem.props.id as number).then(({ list }) =>
          list.map((item) => ({
            id: item.toUserId,
            label: getLabel(item),
            avatar: item.avatar || getDefaultAvatar(item.toUserId),
            type: 'users',
          }))
        );
      }
      return getUserChatGroups(0, tokenSource!.token, false).then(({ list }) =>
        list.map((item) => ({
          id: item.id,
          label: item.name,
          avatar: item.avatar || getDefaultAvatar(item.id),
          userIds: item.groupMemberUserIds,
          groupType: item.groupType,
          type: 'groups',
        }))
      );
    case 'chats':
      return getRecentChatList(tokenSource!.token, false).then(({ list }) =>
        list.map((item) => formatUserItem(item))
      );
    case 'strangers':
      return getStrangersForUser(tokenSource!.token, false).then(({ list }) =>
        list.map((item) => formatUserItem(item))
      );
    default:
      return Promise.reject();
  }
}

const searchUserTypes = [
  { key: 'colleagueSearches', label: i18n.t('supplier_qiyelianxiren') },
  { key: 'contactRespList', label: i18n.t('supplier_qiyelianxiren_note') },
  { key: 'lookingForList', label: i18n.t('supplier_qiyelianxiren_note2') },
  { key: 'recentContactList', label: i18n.t('supplier_lianxishijian') },
  { key: 'strangerList', label: i18n.t('supplier_lianxishijian_note') },
];

function formatOrganizationNickname(
  member: CompanyOrganizationMemberResult,
  isFirstShowUserName = false,
  userIdKey = 'userId',
  userLabelKey = 'nickname'
): UserItem {
  return {
    id: userIdKey === 'memberId' || member.isInvitation ? member.id : member.userId,
    label:
      // eslint-disable-next-line no-nested-ternary
      userLabelKey === 'realName'
        ? member.realName
        : isFirstShowUserName
        ? member.name || member.stageName || member.nickname
        : member.stageName || member.nickname || member.name,
    memberId: member.id,
    companyId: member.companyId,
    userId: member.userId,
    avatar: member.avatar,
    description: member.position,
    type: 'users',
    phone: member.phone,
    position: member.position,
    isInvitation: member.isInvitation,
    accountType: member.accountType,
  };
}

/**
 * 获取组织信息
 * @param data
 */
export function getOrganizations(
  data: Record<string, number>,
  isFirstShowUserName: boolean,
  userIdKey?: string,
  userLabelKey?: string
): Promise<{ breadcrumbs: CompanyOrganizationBreadcrumbResult[]; list: SelectGroup[] }> {
  updateCancelToken();
  return getCompanyOrganizations(
    data as GetCompanyOrganizationsProps,
    tokenSource!.token,
    false
  ).then((res) => ({
    breadcrumbs: res.breadcrumbs,
    list: [
      {
        key: 'departments',
        list: res.organizations.map((item) => ({
          id: item.id,
          label: item.orgName,
          userIds: item.userIds,
          ancestors: item.ancestors,
          type: 'departments',
          members: item.members.map((it) => formatOrganizationNickname(it, false)),
        })),
      },
      {
        key: 'users',
        list: res.members.map((item) =>
          formatOrganizationNickname(item, isFirstShowUserName, userIdKey, userLabelKey)
        ),
      },
    ],
  }));
}

/**
 * 格式化搜索类型
 * @param result
 */
export function formatSearchTypes(
  result: Record<
    string,
    | SearchRelationUserResult[]
    | SearchRelationCustomerResult[]
    | SearchRelationSupplierResult[]
    | SearchRelationCustomersCompanyResult[]
  >,
  defaultCompanyId?: number,
  userIdKey?: string,
  userLabelKey?: string
) {
  const userGroups: SearchGroup[] = [];
  searchUserTypes.forEach((type) => {
    if (result[type.key]) {
      userGroups.push({
        label: type.label,
        list: result[type.key].map((item) => ({
          id: userIdKey === 'memberId' && item.memberId ? item.memberId : item.id,
          label:
            // eslint-disable-next-line no-nested-ternary
            (userLabelKey === 'realName'
              ? item.realName
              : item.remark
              ? item.remark
              : item.nickname) as string,
          avatar: item.avatar,
          userId: item.id,
          memberId: item.memberId,
          companyId: type.key === 'colleagueSearches' ? defaultCompanyId : 0,
          relation: 'relation' in item ? item.relation : 0,
          type: 'users',
          phone: item.phone,
        })),
      });
    }
  });
  if (result.customerSupplierVOList) {
    userGroups.push({
      label: '供应商',
      list: (result.customerSupplierVOList as SearchRelationSupplierResult[]).map(
        (companyItem) => ({
          ...companyItem,
          members: companyItem.members.map((memberItem) => ({
            ...memberItem,
            id: userIdKey === 'memberId' ? memberItem.memberId : memberItem.id,
          })),
        })
      ),
    });
  }
  if (result.customerVOList) {
    userGroups.push({
      label: '客户',
      list: (result.customerVOList as SearchRelationCustomersCompanyResult[]).map(
        (companyItem) => ({
          ...companyItem,
          members: companyItem.members.map((memberItem) => ({
            ...memberItem,
            id: userIdKey === 'memberId' ? memberItem.memberId : memberItem.id,
          })),
        })
      ),
    });
  }
  return userGroups;
}

/**
 * 搜索用户
 * @param params
 */
export function searchUsers(
  params: SearchAllRelationUsersProps,
  userIdKey?: string,
  userLabelKey?: string
): Promise<SearchGroup[]> {
  updateCancelToken();
  return searchAllRelationUsers(params, tokenSource?.token).then((res) =>
    formatSearchTypes(res, params.companyId, userIdKey, userLabelKey)
  );
}

/**
 * 获取客户列表
 * @param data
 */
export function getCustomers(
  data: Record<string, unknown>,
  authCustomer?: boolean,
  userIdKey?: string
) {
  if (authCustomer) {
    return getAuthCustomer({
      customerType: 0,
      pageSize: 999,
      pageNo: 1,
      filterContact: data.filterContact as boolean,
    }).then((res) => ({
      ...res,
      list: res.list.map(
        (customerCompany) =>
          ({
            id: customerCompany.id,
            label: customerCompany.customerName,
            companyId: customerCompany.customerCompanyId,
            userIds: customerCompany.contactList.map(
              (customerMember) => customerMember.contactUserId
            ),
            type: 'customers',
            members: customerCompany.contactList
              .filter((customerMember) => customerMember.contactUserId)
              .map((customerMember) => ({
                id:
                  userIdKey === 'memberId'
                    ? customerMember.contactMemberId
                    : customerMember.contactUserId,
                label: customerMember.contactName,
                avatar: customerMember.avatar || getDefaultAvatar(customerMember.contactUserId),
                userId: customerMember.contactUserId,
                memberId: customerMember.contactMemberId,
                companyId: customerMember.contactCompanyId || customerCompany.customerCompanyId,
                companyName: customerCompany.customerName,
                phone: customerMember.phone,
                type: 'users',
              })),
          } as CustomerItem)
      ),
    }));
  }
  return getAcceptChatCustomerList(data as { companyId: number }).then((res) => ({
    ...res,
    list: res.list.map(
      (customer) =>
        ({
          id: customer.id,
          label: customer.customerName,
          members: (customer.contactList || []).map((contact) => ({
            id: userIdKey === 'memberId' ? contact.contactMemberId : contact.contactUserId,
            label: contact.contactName,
            avatar: contact.image || getDefaultAvatar(contact.contactUserId),
            memberId: contact.contactMemberId,
            userId: contact.contactUserId,
            type: 'users',
            phone: contact.phone,
            companyName: customer.customerName,
          })),
          userIds: (customer.contactList || []).map((contact) => contact.contactUserId),
          type: 'customers',
        } as CustomerItem)
    ),
  }));
}

/**
 * 查询我的客户(负责的客户、协作的客户)
 * @param type:1:负责的客户 / 2：协作的客户 / 3：负责的客户和协作的客户（搜索的时候用3）
 */
export function getCooperation(type: 1 | 2 | 3, keywords?: string) {
  return getQueryCustomerForMe({ keywords }).then((res) => {
    const map = {
      '1': res.responseList,
      '2': res.concertList,
      '3': res.responseList.concat(res.concertList),
    };

    const list = map[`${type}`];

    return {
      list: list.map((item) => ({
        id: item.id,
        label: item.customerName,
        // members: [],
        // userIds: [],
        type: 'users',
      })),
    };
  });
}

/**
 * 获取供应商
 */
export function getSupplier(
  companyId: number,
  selectSupplierCompany?: boolean,
  type?: 0 | 1 | 2,
  authSupplier?: boolean,
  userIdKey?: string,
  categoryIds?: number[],
  appointType?: 0 | 1 | 2,
  filterContact?: boolean,
  isValidityDay?: boolean
) {
  if (authSupplier) {
    return getAuthSupplier({
      companyId,
      pageNo: 1,
      pageSize: 999,
      type,
      filterContact,
      isValidityDay,
    }).then((res) => ({
      ...res,
      list: res.list.map((supplierCompany) => ({
        id: supplierCompany.id,
        label: supplierCompany.customerName,
        companyId: supplierCompany.customerCompanyId,
        hideMore: selectSupplierCompany || !supplierCompany.contactList.length,
        userIds: supplierCompany.contactList.map((supplierMember) => supplierMember.contactUserId),
        type: 'suppliers',
        members: supplierCompany.contactList
          .filter((supplierMember) => supplierMember.contactUserId)
          .map((supplierMember) => ({
            id:
              userIdKey === 'memberId'
                ? supplierMember.contactMemberId
                : supplierMember.contactUserId,
            label: supplierMember.contactName,
            avatar: supplierMember.avatar || getDefaultAvatar(supplierMember.contactUserId),
            userId: supplierMember.contactUserId,
            memberId: supplierMember.contactMemberId,
            companyId: supplierMember.contactCompanyId,
            companyName: supplierCompany.customerName,
            phone: supplierMember.phone,
            type: 'users',
          })),
      })),
    }));
  }
  return getSelectSupplier({
    companyId,
    pageNo: 1,
    pageSize: 999,
    customerCategory: 1,
    type,
    categoryIds,
    appointType,
    isValidityDay,
  }).then((res) => ({
    ...res,
    list: res.list.map(
      (customer) =>
        ({
          id: customer.id,
          label: customer.customerName,
          companyId: customer.customerCompanyId,
          members: (customer.contactList || [])
            .filter((contact) => !!contact.contactUserId)
            .map((contact) => ({
              id: userIdKey === 'memberId' ? contact.contactMemberId : contact.contactUserId,
              label: contact.contactName,
              avatar: contact.image || getDefaultAvatar(contact.contactUserId),
              memberId: contact.contactMemberId,
              userId: contact.contactUserId,
              companyId: contact.contactCompanyId,
              customerId: contact.customerId,
              companyName: customer.customerName,
              type: 'users',
            })),
          hideMore:
            selectSupplierCompany ||
            !customer.contactList.filter((contact) => !!contact.contactUserId).length,
          userIds: (customer.contactList || []).map((contact) => contact.contactUserId),
          type: 'suppliers',
        } as SupplierItem)
    ),
  }));
}

/**
 * 获取角色列表
 * @param data
 */
export function getRole() {
  return roleList({
    pageNo: 1,
    pageSize: 999,
  }).then(({ records }) =>
    records.map((role) => ({
      id: role.id,
      label: role.roleName,
      roleType: role.roleType,
      type: 'roles',
    }))
  );
}

/**
 * 获取分销列表
 * @param data
 */
export function getDistribution(data: Record<string, number>, distributionUserKey?: string) {
  return getDistributionInfo(data as { companyId: number }).then((res: any) => ({
    ...res,
    list: res.distributorInfoResponses.map(
      (distribution: {
        companyId: number;
        companyName: string;
        nycbngShareContactsResponses: nycbngShareContactsResponsesData[];
      }) =>
        ({
          id: distribution.companyId,
          label: distribution.companyName,
          members: (distribution.nycbngShareContactsResponses || []).map((dis) => ({
            id: distributionUserKey === 'memberId' ? dis.memberId : dis.userId,
            label: dis.nickname,
            avatar: dis.avatar,
            userId: dis.userId,
            memberId: dis.memberId,
            type: 'users',
          })),
          userIds: (distribution.nycbngShareContactsResponses || []).map((dis) => dis.userId),
          type: 'distributions',
        } as DistributionItem)
    ),
  }));
}
