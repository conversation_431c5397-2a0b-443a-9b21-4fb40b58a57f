/**
 * 用户公司的类型
 */
import { useMemo, useState } from 'react';
import { Avatar, Collapse } from 'antd';
import classNames from 'classnames';
import isFunction from 'lodash/isFunction';
import { useTranslation } from 'react-i18next';
import { GetUserCompanyInfoListResult } from '@/apis';
import { ShareCompanyType } from '@/apis/bidding/get-all-suppliers';
import styles from './user-type.module.less';
import up from './img/up.png';
import leftBottom from './img/left-bottom.png';
import organization from './img/organization.png';

interface ClickEvent {
  label: string;
  type?: string;
  props: { companyId?: number; id?: number; type?: number; isInvitation?: boolean };
}

export interface UserCompanyTypeProps {
  companies: GetUserCompanyInfoListResult[];
  companyId?: number;
  showCompanyId?: number;
  allowCustomer?: boolean;
  onlyDistribution?: number;
  allowRole?: boolean;
  onlyRole?: boolean;
  allowSupplier?: boolean;
  allowShareSupplier?: boolean;
  onlySupplier?: boolean;
  cooperation?: boolean;
  showInactiveMember?: boolean;
  shareSupplierItems?: ShareCompanyType[];
  // eslint-disable-next-line no-unused-vars
  onClick?: (data: ClickEvent) => void;
}

const { Panel } = Collapse;

function UserCompanyType({
  companies,
  companyId,
  showCompanyId,
  allowCustomer,
  onlyDistribution,
  allowRole,
  onlyRole,
  allowSupplier,
  allowShareSupplier,
  shareSupplierItems,
  onlySupplier,
  showInactiveMember,
  cooperation,
  onClick,
}: UserCompanyTypeProps) {
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState(companies.map((company) => `${company.company.id}`));
  const showCompanies = useMemo(() => {
    if (companyId || showCompanyId) {
      for (let i = 0; i < companies.length; i += 1) {
        if (companies[i].company.id === companyId || companies[i].company.id === showCompanyId) {
          return [companies[i]];
        }
      }
      return [];
    }
    return companies;
  }, [companies, companyId, showCompanyId]);
  const clickItem = (data: ClickEvent) => {
    if (isFunction(onClick)) {
      const copyData = data;
      if (showInactiveMember) {
        copyData.props.isInvitation = showInactiveMember;
      }
      onClick(copyData);
    }
  };

  return (
    <Collapse
      ghost
      activeKey={activeKey}
      onChange={(key) => {
        setActiveKey(key as string[]);
      }}
    >
      {showCompanies.map(({ company, orgList = [] }) => (
        <Panel
          key={company.id}
          showArrow={false}
          header={
            <>
              <Avatar src={company.logoUrl || organization} size={32} />
              <span className={styles.title}>{company.companyName}</span>
              <img
                src={up}
                alt=""
                className={classNames(styles.icon, {
                  [styles.active]: !activeKey.includes(`${company.id}`),
                })}
              />
            </>
          }
          className={styles.panel}
        >
          {!onlyRole && !onlySupplier && (
            <>
              {!onlyDistribution && (
                <div
                  role="button"
                  tabIndex={0}
                  className={styles.itemTree}
                  onClick={() => {
                    clickItem({ label: company.companyName, props: { companyId: company.id } });
                  }}
                >
                  <img src={leftBottom} alt="" className={styles.treeIcon} />
                  <span>{t('channel_selectstructure')}</span>
                </div>
              )}
              {orgList.map((org) => (
                <div
                  key={org.id}
                  role="button"
                  tabIndex={0}
                  className={styles.itemTree}
                  onClick={() => {
                    clickItem({
                      type: org.orgName === '分销组织' ? 'distributions' : undefined,
                      label: company.companyName,
                      props:
                        org.orgName === '分销组织' ? { companyId: company.id } : { id: org.id },
                    });
                  }}
                >
                  <img src={leftBottom} alt="" className={styles.treeIcon} />
                  <span>{org.orgName}</span>
                </div>
              ))}
            </>
          )}
          {allowCustomer && (
            <div
              role="button"
              tabIndex={0}
              className={styles.itemTree}
              onClick={() => {
                clickItem({
                  type: 'customer',
                  label: company.companyName,
                  props: { companyId: company.id },
                });
              }}
            >
              <img src={leftBottom} alt="" className={styles.treeIcon} />
              <span>{cooperation ? t('my_customer') : t('my_customer_role')}</span>
            </div>
          )}
          {allowSupplier && (
            <div
              role="button"
              tabIndex={0}
              className={styles.itemTree}
              onClick={() => {
                clickItem({
                  type: 'supplier',
                  label: t('my_supplier'),
                  props: { companyId: company.id },
                });
              }}
            >
              <img src={leftBottom} alt="" className={styles.treeIcon} />
              <span>{t('my_supplier')}</span>
            </div>
          )}
          {allowShareSupplier &&
            shareSupplierItems?.map((shareSupplierItem) => (
              <div
                key={shareSupplierItem.shareCompanyId}
                role="button"
                tabIndex={0}
                className={styles.itemTree}
                onClick={() => {
                  clickItem({
                    type: 'shareSupplier',
                    label: t('share_supplier'),
                    props: { companyId: shareSupplierItem.shareCompanyId },
                  });
                }}
              >
                <img src={leftBottom} alt="" className={styles.treeIcon} />
                <span>{`${t('share_supplier')}（${shareSupplierItem.shareCompanyName}）`}</span>
              </div>
            ))}
          {allowRole && (
            <div
              role="button"
              tabIndex={0}
              className={styles.itemTree}
              onClick={() => {
                clickItem({
                  type: 'roles',
                  label: t('my_customer_role_select'),
                  props: {
                    companyId: company.id,
                  },
                });
              }}
            >
              <img src={leftBottom} alt="" className={styles.treeIcon} />
              <span>{t('my_customer_role_select')}</span>
            </div>
          )}
          {cooperation && (
            <div
              role="button"
              tabIndex={0}
              className={styles.itemTree}
              onClick={() => {
                clickItem({
                  type: 'cooperation',
                  label: t('my_customer_cooperation'),
                  props: {
                    companyId: company.id,
                  },
                });
              }}
            >
              <img src={leftBottom} alt="" className={styles.treeIcon} />
              <span>{t('my_customer_cooperation')}</span>
            </div>
          )}
        </Panel>
      ))}
    </Collapse>
  );
}

UserCompanyType.defaultProps = {
  companyId: undefined,
  showCompanyId: undefined,
  allowCustomer: false,
  allowRole: false,
  onlyDistribution: 0,
  onlyRole: false,
  allowSupplier: false,
  allowShareSupplier: false,
  onlySupplier: false,
  cooperation: false,
  showInactiveMember: false,
  shareSupplierItems: [],
  onClick: undefined,
};

export default UserCompanyType;
