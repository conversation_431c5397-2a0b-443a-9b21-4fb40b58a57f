import { HTMLAttributes, useCallback, useMemo } from 'react';
import { Checkbox } from 'antd';
import isFunction from 'lodash/isFunction';
import find from 'lodash/find';
import uniq from 'lodash/uniq';
import SelectItem from './select-item';
import {
  Item,
  SelectedIds,
  SelectGroup,
  SelectItemHandler,
  SelectItemKeys,
  SelectOnlyDepartment,
} from './util';
import styles from './select-item.module.less';

export type SelectedHandler = MultipleParamsFn<
  [value: { key: SelectItemKeys; ids: number[]; items: Item[] }]
>;

export interface SelectListProps extends HTMLAttributes<HTMLDivElement> {
  list: Item[] | SelectGroup[];
  selected: SelectedIds;
  selectedItems: Item[];
  defaults: number[];
  disables?: Item[];
  closable?: boolean;
  onlyUser?: boolean;
  companyMemberRadio?: boolean;
  selectSupplierMember?: boolean;
  selectSupplierCompanyAndMember?: boolean;
  selectCustomerCompanyAndMember?: boolean;
  onlyDepartment?: SelectOnlyDepartment;
  hideMore?: boolean;
  onSelected?: SelectedHandler;
  onMore?: SelectItemHandler;
}

function SelectList({
  list,
  selected,
  selectedItems,
  defaults,
  disables,
  closable,
  onlyUser,
  onlyDepartment,
  hideMore,
  selectSupplierMember,
  selectSupplierCompanyAndMember,
  selectCustomerCompanyAndMember,
  companyMemberRadio,
  onSelected,
  onMore,
  ...props
}: SelectListProps) {
  const groups = useMemo(() => {
    let tempGroups =
      list[0] && 'key' in list[0]
        ? (list as SelectGroup[])
        : [
            {
              key: list[0] && 'type' in list[0] ? list[0].type : 'users',
              list: list as Item[],
            } as SelectGroup,
          ];
    if (onlyDepartment) {
      tempGroups = tempGroups.filter((group) => group.key === 'departments');
    }
    return tempGroups.filter((group) => group.list.length !== 0);
  }, [list, onlyDepartment]);

  const groupIds = useMemo(() => {
    const ids: Record<string, number[]> = {};
    groups.forEach((group) => {
      ids[group.key] = group.list.map((item) => item.id);
    });
    return ids;
  }, [groups]);

  const changeValue = useCallback(
    (checkboxValues: number[], group: SelectGroup) => {
      const selectedIds = selected[group.key];
      const listIds = groupIds[group.key];
      const ids: number[] = [];
      selectedIds.forEach((id) => {
        if (listIds.includes(id)) {
          if (checkboxValues.includes(id)) {
            ids.push(id);
          }
        } else {
          ids.push(id);
        }
      });
      checkboxValues.forEach((id) => {
        if (!ids.includes(id)) {
          ids.push(id);
        }
      });
      onSelected!({
        ids,
        key: group.key,
        items: group.list.filter(
          (item) => checkboxValues.includes(item.id) && !selectedIds.includes(item.id)
        ),
      });
    },
    [selected, groupIds, onSelected]
  );

  // 处理禁用
  const processDisabled = (group: SelectGroup, item: Item) => {
    // 一家公司只能选一个人
    if (companyMemberRadio && item.type === 'users') {
      const selectedItem = find(selectedItems, { type: 'users', companyId: item.companyId });
      if (selectedItem && item.id !== selectedItem.id) {
        return true;
      }
    }
    // let isDisabled = group.key !== 'users' || closable ? false : defaults.includes(item.id);
    // departments和users默认值都禁用
    let isDisabled = closable ? false : defaults.includes(item.id);
    if (disables?.length) {
      for (let i = 0; i < disables.length; i += 1) {
        const disItem = disables[i];
        if (group.key === (disItem?.type || 'users') && disItem.id === item.id) {
          isDisabled = true;
          break;
        }
      }
    }
    return isDisabled;
  };
  return (
    <div {...props} className={styles.group}>
      {groups.map((group) => (
        <Checkbox.Group
          key={group.key}
          value={uniq(selected[group.key])}
          className={styles.list}
          onChange={(value) => {
            if (isFunction(onSelected)) {
              changeValue(value as number[], group);
            }
          }}
        >
          {group.list.map((item) => (
            <SelectItem
              key={item.id}
              item={item}
              hideMore={hideMore}
              isItem={
                item.type &&
                ((selectSupplierMember && item.type === 'suppliers') ||
                  (onlyUser &&
                    item.type !== 'users' &&
                    (selectSupplierCompanyAndMember ? item.type !== 'suppliers' : true) &&
                    (selectCustomerCompanyAndMember ? item.type !== 'customers' : true)))
              }
              keepMore={
                item.type &&
                ((selectCustomerCompanyAndMember && item.type === 'customers') ||
                  (selectSupplierCompanyAndMember && item.type === 'suppliers'))
              }
              isMore={onlyDepartment !== 'last' && !item.hideMore}
              disabled={processDisabled(group, item)}
              selected={selected[group.key]}
              onMore={onMore}
            />
          ))}
        </Checkbox.Group>
      ))}
    </div>
  );
}

SelectList.defaultProps = {
  disables: [],
  closable: false,
  onlyUser: false,
  onlyDepartment: false,
  hideMore: false,
  companyMemberRadio: false,
  selectSupplierMember: false,
  selectSupplierCompanyAndMember: false,
  selectCustomerCompanyAndMember: false,
  onSelected: () => {},
  onMore: () => {},
};

export default SelectList;
