import { MouseEvent<PERSON>andler, useMemo, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { observer } from 'mobx-react-lite';
import { createFunctionSession } from '@/apis';
import { layout, sessions } from '@/store';
import { useMemoizedFn } from 'ahooks';
import Item, { NavbarItemProps } from './components/navbar-item';
import styles from './navbar.module.less';
// 图片
import messageOff from '../assets/img/navbar/message-off.png';
import messageOn from '../assets/img/navbar/message-on.png';
import communityOff from '../assets/img/navbar/community-off.png';
import communityOn from '../assets/img/navbar/community-on.png';
import contactsOff from '../assets/img/navbar/contacts-off.png';
import contactsOn from '../assets/img/navbar/contacts-on.png';
import cartOff from '../assets/img/navbar/cart-off.png';
import cartOn from '../assets/img/navbar/cart-on.png';
import myOff from '../assets/img/navbar/my-off.png';
import myOn from '../assets/img/navbar/my-on.png';

export interface NavbarProps {
  logo: string | undefined;
  onDoubleClick: MouseEventHandler<HTMLElement>;
}

let logoIcon = 'https://img.huahuabiz.com/user_files/2022527/1653641500877191.png';
if (import.meta.env.BIZ_APP_PLATFORM_NO === '1') {
  logoIcon = 'https://img.huahuabiz.com/user_files/2022530/1653873166579291.png';
} else if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
  logoIcon = 'https://img.huahuabiz.com/user_files/202338/1678258026072134.png';
}

const items: NavbarItemProps[] = [
  {
    id: 'home',
    title: '消息',
    icon: messageOff,
    activeIcon: messageOn,
    url: import.meta.env.BIZ_APP_HOME_URL || '/',
  },
  {
    id: 'circle',
    title: '圈子',
    icon: communityOff,
    activeIcon: communityOn,
    url:
      import.meta.env.BIZ_APP_PLATFORM_NO === '1'
        ? '/home?is-group=1'
        : '/huahua-circle?code=huahuaquanzi',
  },
  {
    id: 'logo',
    icon: logoIcon,
  },
  {
    id: 'mailList',
    title: '通讯录',
    icon: contactsOff,
    activeIcon: contactsOn,
    url: '/shop/mail/list',
  },
  import.meta.env.BIZ_APP_PLATFORM_NO === '1'
    ? {
        id: 'my',
        title: '我的',
        icon: myOff,
        activeIcon: myOn,
        url: '/user/mycenter/userinfo',
      }
    : {
        id: 'cart',
        title: '购物车',
        icon: cartOff,
        activeIcon: cartOn,
        url: '/cart',
      },
];

function Navbar({ logo, onDoubleClick }: NavbarProps) {
  const navigate = useNavigate();
  const doubleInfo = useRef({ prev: 0, effect: 0, interval: null as null | number });
  const itemList = useMemo(() => {
    if (!logo) {
      return items;
    }
    return items.map((item) => (item.id === 'logo' ? { ...item, icon: logo } : item));
  }, [logo]);
  const onClick: MouseEventHandler<HTMLElement> = useMemoizedFn((e) => {
    const now = Date.now();
    const { current } = doubleInfo;
    if (current.interval) {
      clearTimeout(current.interval);
      current.interval = null;
    }

    // 如果有记录上次的点击时间,并且两次点击不超过 200 毫秒,触发双击事件
    if (current.prev && now < current.prev + 200) {
      // 如果双击事件被触发过,且在触发时间 500 秒内不再次触发事件
      if (!current.effect || now > current.effect + 500) {
        current.prev = 0;
        current.effect = now;
        onDoubleClick(e);
      }
    } else if (!import.meta.env.SSR) {
      current.prev = now;
      current.interval = setTimeout(() => {
        let el = (e.target || e.currentTarget) as HTMLElement | null;
        while (el && el.parentElement) {
          const href = el.getAttribute('href');
          if (href) {
            const { location } = window;
            navigate(href.replace(`${location.protocol}//${location.host}`, ''));
            break;
          } else {
            el = el.parentElement;
          }
        }
        current.interval = null;
      }, 200) as unknown as number;
    }
    e.preventDefault();
  });

  return (
    <nav className={styles.navbar}>
      <div className={styles.body}>
        <ul className={styles.list}>
          {itemList.map((item) =>
            item.id === 'home' ? (
              <Item
                {...item}
                key={item.id}
                isActive={layout.nav === item.id}
                count={sessions.noReading}
                onClick={onClick}
              />
            ) : (
              <Item
                {...item}
                key={item.id}
                isActive={layout.nav === item.id}
                onClick={() => {
                  if (item.id) {
                    createFunctionSession(item.id);
                  }
                }}
              />
            )
          )}
        </ul>
      </div>
    </nav>
  );
}

export default observer(Navbar);
