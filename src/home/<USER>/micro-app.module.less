@import 'components/context/context.module.less';

.micro {
  composes: context;
  overflow: auto;

  //& > * {
  //  &:first-child {
  //    .flex-row(100%);
  //  }
  //}

  &,
  & > * {
    width: 100%;
    height: 100%;
    -webkit-overflow-scrolling: touch;

    &:first-child {
      .flex-row(100%);
    }
  }

  :global {
    .ech-content,
    .biz-content {
      padding: 0 !important;
    }
  }
}

.spin {
  max-height: none !important;
}
