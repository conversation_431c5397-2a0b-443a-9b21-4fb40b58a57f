import { Outlet, Navigate, useLocation } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { user } from '@/store';
import { getLogoutRedirectURL } from '@/utils/auth';
import useLayout from '@/hooks/use-layout';
import LayoutContext from './layout-context';

const ignoreRoutes = ['/home', '/shop-home', '/renovation/goods-category'];

export default function Layouts() {
  const location = useLocation();

  useLayout();

  if (!user.isLogin()) {
    if (ignoreRoutes.includes(location.pathname)) {
      return (
        <>
          <Helmet>
            <title>{import.meta.env.BIZ_APP_TITLE}</title>
          </Helmet>
          <Outlet />
        </>
      );
    }
    const path = getLogoutRedirectURL();
    if (path.indexOf('http') !== -1) return null;
    return <Navigate to={path} />;
  }

  return (
    <LayoutContext>
      <Helmet>
        <title>{import.meta.env.BIZ_APP_TITLE}</title>
      </Helmet>
      <Outlet />
    </LayoutContext>
  );
}
