import {
  forward<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>psWithC<PERSON>dren,
  useCallback,
  useEffect,
  useRef,
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';
import debounce from 'lodash/debounce';
import classNames from 'classnames';
import { observer } from 'mobx-react-lite';
import { Message, Cmd } from '@echronos/plugin-im';
import { dialog } from '@/components';
import { layout, sessions, user } from '@/store';
import { setLogout } from '@/utils/http';
import { im } from '@/utils/imc';
import isNumber from 'lodash/isNumber';
import { getDisposableCode, sendFileUpdatedNotice } from '@/apis';
import { SessionTypes } from '@/utils/session';
import { message } from 'antd';
import { EMPTY_FN } from '@/utils/const';
import checkUserWhitelist from '@/apis/system/check-user-whitelist';
import showNewUiModal from '../containers/show-new-ui-modal';
import { PopoverInstance, UserPopover } from '../containers';
import styles from './layouts.module.less';
import Navbar from './navbar';
import Sider from './sider';
import Footer from './footer';
import { handleImCmd, handleImMessage } from '../utils/layout';
import { SessionListInstance } from '../containers/session-list';
import backOldPng from '../assets/img/back-old.png';

const Aside = observer(
  forwardRef<SessionListInstance, { onDoubleClick: MouseEventHandler<HTMLElement> }>(
    (props, ref) => {
      const asideClass = classNames(styles.aside, { [styles.show]: layout.showAside });
      return (
        <section className={asideClass}>
          <Navbar {...props} logo={user.personalization.logoUrl} />
          <Sider ref={ref} />
        </section>
      );
    }
  )
);

function LayoutContext({ children }: PropsWithChildren<unknown>) {
  const navigate = useNavigate();
  const siderRef = useRef(null as unknown as SessionListInstance);
  const popoverRef = useRef(null as unknown as PopoverInstance);
  const layoutEl = useRef(null as unknown as HTMLElement);
  const [inWhitelist, setInWhitelist] = useState(import.meta.env.DEV);
  const onDoubleClick = useCallback(() => {
    siderRef.current?.scrollToNoReading();
  }, []);
  const onToNewVersion: MouseEventHandler<HTMLElement> = useCallback((e) => {
    if (!import.meta.env.SSR) {
      const toURL = import.meta.env.BIZ_APP_TO_NEW_VERSION_URL || '';
      let host = toURL.replace(/^https?:\/\//, '');
      const index = host.indexOf('/');
      let next = '/';
      if (index !== -1) {
        next = host.substring(index);
        host = host.substring(0, index);
      }
      const protocol = toURL.includes('https:') ? 'https:' : 'http:';

      getDisposableCode({
        clientId: `web-${host}`,
        redirectUri: host,
      }).then((res) => {
        if (res && res.code) {
          window.location.href = `${protocol}//${host}/auth/authorization?clientId=web-${host}&next=${encodeURIComponent(
            next
          )}&authorization=${res.code}`;
        }
      });
    }
    e.preventDefault();
    e.stopPropagation();
  }, []);

  useEffect(() => {
    if (import.meta.env.SSR) {
      return EMPTY_FN;
    }

    const root = document.getElementById('root');
    const popover = (e: MouseEvent) => {
      let el = e.target as HTMLElement | null;
      while (el) {
        if (el.hasAttribute('data-userinfo')) {
          return;
        }
        if (el.hasAttribute('data-user-popper')) {
          break;
        }
        if (el.hasAttribute('data-group-popper')) {
          break;
        }
        if (el.tagName === 'BODY') {
          el = null;
          break;
        }
        el = el.parentElement;
      }
      if (el) {
        const id = el.getAttribute('data-user-popper');
        if (id && id !== '0') {
          const groupId = +(el.getAttribute('data-group-popper') || '0');
          popoverRef.current.show(+id, el, groupId, el.getAttribute('data-user-send') !== 'false');
          return;
        }
      }
      popoverRef.current.hide();
    };
    const handleMessage = (e: MessageEvent) => {
      if (e.origin === window.origin) {
        const { data = {} } = e;
        if (data.type === 'send-office-notice' && data.fileId) {
          dialog({
            title: '提示',
            width: 355,
            content: <div className="text-center">文档已保存，是否通知成员？</div>,
            cancelText: '不用通知',
            okText: '通知',
            className: styles.dialog,
            onOk: () => {
              sendFileUpdatedNotice({ fileId: data.fileId, sessionId: data.sessionId });
            },
          });
        }
      }
    };

    document.addEventListener('click', popover, false);
    window.addEventListener('message', handleMessage, false);
    window.userPopover = (userId, el) => {
      if (isNumber(userId)) {
        popoverRef.current.show(userId, el);
      } else {
        popoverRef.current.show(userId.userId, el, userId.groupId);
      }
    };
    window.hidePopover = () => {
      popoverRef.current?.hide();
    };
    if (root) {
      root.className = styles.wrapper;
    }

    return () => {
      document.removeEventListener('click', popover, false);
      window.removeEventListener('message', handleMessage, false);
      window.userPopover = () => {};
      window.hidePopover = () => {};
      if (root) {
        root.className = '';
      }
    };
  }, []);
  useEffect(() => {
    if (import.meta.env.SSR) {
      return EMPTY_FN;
    }

    const resize = debounce(() => {
      const el = layoutEl.current;
      if (el) {
        const rect = el.getBoundingClientRect();

        let usableWidth = rect.width;
        let usableHeight = rect.height;
        if (window.innerWidth >= 768) {
          usableHeight -= 24 * 2;
          usableWidth -= (el.children[0] as HTMLElement).offsetWidth;
        }

        layout.setRect({
          usableHeight,
          usableWidth: usableWidth - 24 * 2,
          width: rect.width,
          height: rect.height,
          top: rect.y,
          left: rect.x,
        });
      }
    }, 100);
    resize();
    window.addEventListener('resize', resize, false);
    setLogout(() => {
      user.logout().then((to) => {
        navigate(to);
      });
    });

    const handleMessage = (e: { type: 'cmd'; data: Cmd } | { type: 'message'; data: Message }) => {
      if (e.type === 'cmd') {
        handleImCmd(e.data);
      } else if (e.type === 'message') {
        handleImMessage(e.data, siderRef.current).then((notify) => {
          if (notify) {
            notify.addEventListener(
              'click',
              () => {
                if (typeof window !== 'undefined') {
                  window.focus();
                  setTimeout(() => {
                    const session = sessions.find(e.data.sessionId);
                    if (session && session.sessionType === SessionTypes.SHOP) {
                      navigate(`/shop-home?bid=${session.typeId}&code=deco_shop`);
                    } else {
                      navigate(`/imc/${e.data.sessionId}`);
                    }
                  }, 0);
                }
                notify.close();
              },
              false
            );
          }
        });
      }
    };
    const handleError = (e: { type: string; message: string }) => {
      switch (e?.type) {
        case 'auth:fail':
          user.logout().then((to) => {
            navigate(to);
          });
          break;
        case 'op:5':
          if (e.message) {
            message.error(e.message);
          }
          break;
        default:
      }
    };
    if (im) {
      im.on('message', handleMessage);
      im.on('error', handleError);
    }

    return () => {
      setLogout(null);
      window.removeEventListener('resize', resize, false);

      if (im) {
        im.off('message', handleMessage);
        im.off('error', handleError);
      }
    };
  }, []); // eslint-disable-line
  if (import.meta.env.BIZ_APP_PLATFORM_NO === '0') {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      if (user.isLogin()) {
        checkUserWhitelist().then(setInWhitelist);
      }
      showNewUiModal();
    }, []);
  }

  return (
    <>
      <section ref={layoutEl} id="layout" className={styles.layout}>
        <Aside ref={siderRef} onDoubleClick={onDoubleClick} />
        <main className={styles.main}>{children}</main>
        <Footer />
      </section>
      <UserPopover ref={popoverRef} />
      {import.meta.env.BIZ_APP_PLATFORM_NO === '0' && inWhitelist && (
        <button type="button" className={styles.toNewVersion} onClick={onToNewVersion}>
          <img src={backOldPng} alt="" />
          <span>体验新版echOS</span>
        </button>
      )}
    </>
  );
}

export default observer(LayoutContext);
