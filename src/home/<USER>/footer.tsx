import { PropsWithChildren } from 'react';
import styles from './layouts.module.less';

function Footer({ children }: PropsWithChildren<unknown>) {
  const date = new Date();
  return (
    <footer className={styles.footer}>
      {children}
      <span>
        @版权所有&nbsp;&copy;&nbsp;{import.meta.env.BIZ_APP_FOOTER_START_YEAR}-{date.getFullYear()}
        .&nbsp;
        {import.meta.env.BIZ_APP_FOOTER_TITLE}&nbsp;
      </span>
      <a target="_blank" rel="noreferrer" href="https://beian.miit.gov.cn/">
        {import.meta.env.BIZ_APP_FOOTER_RECORD_NUMBER}
      </a>
      &nbsp;
      {import.meta.env.BIZ_APP_PLATFORM_NO === '1' ? '技术支持 华世界网络科技（深圳）有限公司' : ''}
    </footer>
  );
}

export default Footer;
