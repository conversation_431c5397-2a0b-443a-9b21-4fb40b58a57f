import { CSSProperties } from 'react';
import { observer } from 'mobx-react-lite';
import dayjs from 'dayjs';
import { sessions } from '@/store';

export interface FormatDateProps {
  time: number;

  className?: string;
  style?: CSSProperties;
}

const FormatDate = observer(({ time, ...props }: FormatDateProps) => {
  const date = dayjs(time);
  if (date.isSame(sessions.date, 'year')) {
    if (date.isSame(sessions.date, 'day')) {
      return <span {...props}>{date.format('HH:mm')}</span>;
    }
    return <span {...props}>{date.format('MM/DD')}</span>;
  }
  return <span {...props}>{date.format('YY/MM')}</span>;
});

export default FormatDate;
