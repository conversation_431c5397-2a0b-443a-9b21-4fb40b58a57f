import isFunction from 'lodash/isFunction';
import { modalPopup } from '@/utils/popup';
import CreateTeam, { CreateTeamProps } from './create-team';

function createTeam(config: CreateTeamProps = {}) {
  const instance = modalPopup(CreateTeam, {
    ...config,
    onSuccess: () => {
      if (isFunction(config.onSuccess)) {
        config.onSuccess();
      }
      // instance.destroy();
    },
    goCompanyAuth: () => {
      if (isFunction(config.goCompanyAuth)) {
        config.goCompanyAuth();
      }
      instance.destroy();
    },
  });
  return instance;
}

export default createTeam;
