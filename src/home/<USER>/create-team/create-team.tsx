import { useEffect, useRef, useState } from 'react';
import { Button, Cascader, Form, FormInstance, Input, message, Modal, ModalProps } from 'antd';
import { useMemoizedFn, useThrottleFn } from 'ahooks';
import { createCompanyTeam, GetCategoryResult, getCompanyCategories } from '@/apis';
import { user } from '@/store';
import { Icon } from '@/components';
import { setBlockInstance } from '@/utils/http';
import i18n from '@/entry/i18n';
import addTeamSuccess from './imgs/add-team-success.png';
import styles from './create-team.module.less';

export type CreateTeamProps = ModalProps & {
  hideAuth?: boolean;
  onSuccess?: () => void;
  goCompanyAuth?: () => void;
};

// let globalCategories = null as null | GetCategoryResult[];

function CreateTeam({ hideAuth, onSuccess, goCompanyAuth, ...props }: CreateTeamProps) {
  const [categories, setCategories] = useState<GetCategoryResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(props.visible);
  const [addSuccess, setAddSuccess] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const fromRef = useRef(null as unknown as FormInstance);
  const promiseRef = useRef<Promise<unknown> | null>(null);

  const { run: handleFinish } = useThrottleFn(
    () => {
      if (!categories) return;
      const fields = fromRef.current.getFieldsValue();
      const { industry } = fields;
      const companyName = (fields.companyName || '').trim();
      const realName = (fields.realName || '').trim();
      if (!industry || !companyName || !realName) {
        message.error(
          // eslint-disable-next-line no-nested-ternary
          !companyName
            ? i18n.t('supplier_please')
            : !industry
            ? i18n.t('supplier_industry')
            : i18n.t('paymentManage_pleaseInputRealName')
        );
        return;
      }
      const parent = categories.find((cate) => cate.value === industry[0]);
      if (!parent) return;
      const child = (parent.children || []).find((cate) => cate.value === industry[1]);
      if (!child) return;
      setLoading(true);
      promiseRef.current = new Promise<void>((resolve) => {
        createCompanyTeam({
          companyName,
          realName,
          industry: `${parent.label}/${child.label}`,
        })
          .then((res) => {
            if (hideAuth) {
              setVisible(false);
            }
            setAddSuccess(true);
            const blockPromise = user.switchCompany(res.companyId);
            setBlockInstance(blockPromise);
            return blockPromise;
          })
          .then(onSuccess)
          .then(() => user.getCompanies())
          .finally(() => {
            setLoading(false);
            setTimeout(resolve, 1000);
          });
      });
    },
    { wait: 3000 }
  );
  const onTo = useMemoizedFn(() => {
    if (goCompanyAuth) {
      if (promiseRef.current) {
        setSubmitting(true);
        promiseRef.current.finally(() => {
          goCompanyAuth();
          setSubmitting(false);
        });
      } else {
        goCompanyAuth();
      }
    }
  });

  const onClose = () => {
    setVisible(false);
  };

  useEffect(() => {
    if (categories.length) return;
    getCompanyCategories().then(({ list }) => {
      const foo = list.map((item) => {
        if (item.children && item.children.length > 0) {
          item.children.forEach((child) => {
            // eslint-disable-next-line no-param-reassign
            child.children = undefined;
          });
        }
        return item;
      });
      setCategories(foo);
    });
  }, []); // eslint-disable-line

  let contentNode;
  if (!addSuccess) {
    contentNode = (
      <>
        <div className={styles.headerIcon}>
          <Icon
            name="close"
            className={styles.icon}
            role="button"
            onClick={() => {
              setVisible(false);
            }}
          />
        </div>
        <h3 className={styles.title}>{i18n.t('channel_team_info_tip')}</h3>
        <Form ref={fromRef}>
          <div>
            <Form.Item
              label={i18n.t('supplier_team')}
              name="companyName"
              className={styles.formItem}
            >
              <Input maxLength={50} placeholder={i18n.t('supplier_please')} />
            </Form.Item>
            <Form.Item
              label={i18n.t('supplier_hangye')}
              name="industry"
              className={styles.formItem}
            >
              <Cascader
                options={categories || []}
                placeholder={i18n.t('supplier_industry')}
                loading={!categories}
                showSearch
              />
            </Form.Item>
            <Form.Item
              label={i18n.t('supplier_zhngshi')}
              name="realName"
              className={styles.formItem}
              extra={i18n.t('supplier_please_name')}
            >
              <Input maxLength={50} placeholder={i18n.t('paymentManage_pleaseInputRealName')} />
            </Form.Item>
          </div>
          <Button
            loading={loading}
            shape="round"
            type="primary"
            htmlType="submit"
            onClick={handleFinish}
            className={styles.btn}
          >
            {i18n.t('supplier_chaugnjian')}
          </Button>
        </Form>
      </>
    );
  } else if (!hideAuth) {
    contentNode = (
      <div className={styles.success}>
        <img src={addTeamSuccess} className={styles.img} alt="" />
        <div className={styles.label}>{i18n.t('supplier_chuangjian')}</div>
        <div className={styles.text}>{i18n.t('supplier_kaishi')}</div>
        <div role="button" tabIndex={0} onClick={onClose}>
          <Button
            shape="round"
            type="primary"
            loading={submitting}
            className={styles.successBtn}
            onClick={onTo}
          >
            {i18n.t('go_to_company_certification')}
          </Button>
        </div>
        <div role="button" tabIndex={0} className={styles.tip} onClick={onClose}>
          {i18n.t('later_certification')}
        </div>
      </div>
    );
  }

  // @ts-ignore
  return (
    <Modal
      {...props}
      visible={visible}
      centered
      width={620}
      footer={null}
      closable={false}
      className={styles.form}
    >
      {contentNode}
    </Modal>
  );
}

CreateTeam.defaultProps = {
  hideAuth: false,
  onSuccess: () => {},
  goCompanyAuth: () => {},
};

export default CreateTeam;
