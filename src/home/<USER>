import { RouteObject } from 'react-router-dom';
import { lazy, Redirect } from '@/components';
import MicroApp from './pages/micro-app';

const microApp = <MicroApp />;

/**
 * 路由列表
 */
const routes: RouteObject[] = [
  {
    path: 'admin/*',
    element: microApp,
  },
  {
    path: 'finance/*',
    element: microApp,
  },
  {
    path: 'order/*',
    element: microApp,
  },
  {
    path: 'shop/*',
    element: microApp,
  },
  {
    path: 'user/*',
    element: microApp,
  },
  {
    path: 'imc/*',
    element: microApp,
  },
  {
    path: '',
    // element: lazy(() => import('./pages/index')),
    element: <Redirect to={import.meta.env.BIZ_APP_HOME_URL || '/'} />,
  },
];

// if (import.meta.env.DEV) {
//   routes.push({ path: 'demo', element: lazy(() => import('./pages/demo')) });
// }

export default routes;

// 模块根目录
export const basePath = '/';

// 布局组件
export const layout = lazy(() => import('./layouts/layouts'));

// 是默认模块
export const isDefault = true;
