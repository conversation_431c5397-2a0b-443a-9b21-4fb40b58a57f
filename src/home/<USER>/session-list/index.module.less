@import 'styles/mixins/mixins';

.sessions {
  border-radius: 0 0 18px 18px;
  overflow: hidden;
}

.empty {
  color: @text-color-secondary;
  width: 100%;
  min-height: 50px;
  text-align: center;
}

.item {
  padding: 4px 8px;
}

.session {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: @border-radius-base;
  overflow: hidden;
  position: relative;

  &:hover {
    background-color: rgb(@light-blue 0.3);
  }

  &:active {
    background-color: rgb(@light-blue 0.5);
  }
}

.topping {
  &::after {
    content: '';
    display: block;
    width: 9px;
    height: 9px;
    background: url('../../assets/img/session-top-icon.png') no-repeat center / cover;
    position: absolute;
    top: 3px;
    right: 3px;
  }
}

.checked {
  background-color: @background-colors[normal];
}

.menuChecked {
  padding: 7px;
  border: 1px solid @primary-color;
}

.avatar {
  border: 1px solid @border-color-light;
}

.body {
  padding-left: 8px;
  overflow: hidden;
}

.row {
  display: flex;

  & + & {
    margin-top: 4px;
  }
}

.title {
  color: @text-color;
  font-size: @font-size-base;
  line-height: 20px;
}

.date {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  line-height: 20px;
  padding-left: 8px;
}

.desc {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  line-height: 14px;
}

.titleBox {
  display: flex;
  overflow: hidden;
}

.body,
.titleBox,
.desc {
  .flex-column();
}

.title,
.desc {
  .text-overflow();

  margin: 0;
}

.at {
  color: #ff943e;
}

.btn {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  line-height: 14px;
  padding: 0 0 0 8px;
  border: 0 solid transparent;
  background-color: transparent;
  cursor: pointer;

  &:hover {
    color: @primary-color;
  }

  > span {
    display: inline-block;
    vertical-align: top;
  }

  &Icon {
    width: 12px;
    height: 12px;
    line-height: 12px;
    margin-top: 1px;
    margin-right: 4px;

    > img {
      display: block;
      width: 100%;
      height: 100%;
    }
  }
}

.banner {
  height: 113px;
  position: relative;

  &Close {
    color: #ababae;
    font-size: 18px;
    padding: 8px;
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
  }
}

.menu {
  position: absolute;
  box-shadow: @box-shadow-base !important;
}
