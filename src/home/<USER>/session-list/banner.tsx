import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SetStateAction, useCallback } from 'react';
import { Carousel, CarouselProps } from 'antd';
import { GetMemberBannerResult, removeMemberBanner } from '@/apis';
import CloseCircleFilled from '@ant-design/icons/CloseCircleFilled';
import { createTeam } from '@@/home/<USER>';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.less';

export interface BannerProps extends Omit<CarouselProps, 'autoplay'> {
  resetAfterIndex: MultipleParamsFn<[index: number]>;
  banners: GetMemberBannerResult[];
  setBanners: Dispatch<SetStateAction<GetMemberBannerResult[]>>;
}

function Banner({ banners, resetAfterIndex, setBanners, ...props }: BannerProps) {
  const navigate = useNavigate();
  const onClick: MouseEventHandler<HTMLDivElement> = useCallback(
    (e) => {
      let el = (e.target || e.currentTarget) as HTMLElement | null;
      if (el && !el.hasAttribute('data-code')) {
        el = el.parentElement;
      }
      if (el) {
        const code = el.getAttribute('data-code');
        if (code === 'teamAndAdvisors') {
          createTeam({
            onSuccess: () => {
              navigate(import.meta.env.BIZ_APP_HOME_URL);
            },
            goCompanyAuth: () => {
              navigate('/user/company/auth?addTeam=1');
            },
          });
        }
      }
    },
    [navigate]
  );
  const onClose: MouseEventHandler<HTMLElement> = useCallback(
    (e) => {
      if (banners) {
        let el = (e.target || e.currentTarget) as HTMLElement | null;
        while (el) {
          if (el.hasAttribute('data-code')) {
            break;
          }
          el = el.parentElement;
        }
        if (el) {
          const code = el.getAttribute('data-code');
          if (code) {
            setBanners((prevState) => prevState.filter((item) => item.coding !== code));
            removeMemberBanner(code).catch(() => removeMemberBanner(code));
          }
        }
        if (banners.length === 1) {
          setTimeout(() => {
            resetAfterIndex(0);
          }, 0);
        }
      }
      e.preventDefault();
      e.stopPropagation();
    },
    [banners, resetAfterIndex, setBanners]
  );

  return (
    <Carousel {...props} autoplay>
      {banners.map((banner, index) => (
        <div
          key={banner.coding}
          role="button"
          tabIndex={index}
          data-code={banner.coding}
          className={styles.banner}
          onClick={onClick}
        >
          <img src={banner.image} alt={banner.coding} />
          <CloseCircleFilled className={styles.bannerClose} onClick={onClose} />
        </div>
      ))}
    </Carousel>
  );
}

export default Banner;
