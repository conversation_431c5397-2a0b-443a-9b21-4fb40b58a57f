import {
  CSSProperties,
  forwardRef,
  H<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  MouseEventHandler,
  use<PERSON><PERSON>back,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import classNames from 'classnames';
import loadable from '@loadable/component';
import {
  GetMemberBannerResult,
  removeSessionOfList,
  SessionResult,
  setTopStateOfSession,
} from '@/apis';
import { layout, Session, sessions } from '@/store';
import { Menu, MenuProps, Spin } from 'antd';
import type { Status } from 'ahooks/es/useExternal';
import { useExternal, useMemoizedFn } from 'ahooks';
import { Icon } from '@/components';
import { SessionTypes } from '@/utils/session';
import { EMPTY_FN } from '@/utils/const';
import styles from './index.module.less';
import { ListInstance, ListProps } from './list';

export interface SessionListProps extends Omit<ListProps, 'items'>, HTMLAttributes<HTMLDivElement> {
  list: Session[];
  loading: boolean;
  banners: GetMemberBannerResult[];
}

const List = loadable(() => import('./list'));

export type SessionListInstance = ListInstance;

const SessionList = forwardRef<SessionListInstance, SessionListProps>(
  ({ list, loading, banners, height, selectId, menuId, setBanners, className, ...props }, ref) => {
    const [listHeight, setHeight] = useState(height);
    const customStyle = useMemo(() => ({ height: `${listHeight}px` }), [listHeight]);
    const items: ListProps['items'] = useMemo(
      () => (!banners.length ? list : [{ typeCode: 'BANNER_NOTICE', data: banners }, ...list]),
      [banners, list]
    );

    const wrapEl = useRef<HTMLDivElement>(null);
    const [submitting, setSubmitting] = useState('');
    const [session, setSession] = useState(null as null | SessionResult);
    const [menuStyle, setMenuStyle] = useState({ display: 'none' } as CSSProperties);
    const sessionItems: MenuProps['items'] = useMemo(
      () => [
        {
          key: 'toTop',
          label: (
            <>
              {submitting === 'toTop' && <Icon name="loading" className="mr-1" />}
              {session && session.isTop ? '取消' : '会话'}置顶
            </>
          ),
        },
        { type: 'divider' },
        {
          key: 'remove',
          label: <>{submitting === 'remove' && <Icon name="loading" className="mr-1" />}删除会话</>,
        },
      ],
      [session, submitting]
    );
    const closeMenu = useCallback(() => {
      setSubmitting('');
      setMenuStyle({ display: 'none' });
      layout.setMenu();
    }, []);
    const onClickContextMenu: MenuProps['onClick'] = useMemoizedFn(({ key }) => {
      if (session) {
        if (key === 'toTop') {
          const isTop = session.isTop ? 0 : 1;
          setSubmitting('toTop');
          setTopStateOfSession(session.id, isTop)
            .then(() => {
              sessions.toggleTopState(session.id, isTop);
            })
            .finally(closeMenu);
        } else {
          setSubmitting('remove');
          removeSessionOfList(session.id)
            .then(() => {
              sessions.remove(session.id);
            })
            .finally(closeMenu);
        }
      } else {
        closeMenu();
      }
    });
    const onContextMenu: MouseEventHandler<HTMLDivElement> = useCallback(
      (e) => {
        e.preventDefault();

        let el = e.target as HTMLElement | null;
        while (el) {
          if (el.classList.contains(styles.sessions)) {
            return;
          }
          if (el.hasAttribute('data-idx')) {
            break;
          }
          el = el.parentElement;
        }
        if (el) {
          const index = parseInt(el.getAttribute('data-idx') || '0', 10);
          const selectedSession = items[index] as Session;
          if (selectedSession.id !== 0 && selectedSession.sessionType !== SessionTypes.SHOP) {
            layout.setMenu(selectedSession.id);
            setSession(selectedSession);
            const rect = wrapEl.current?.getBoundingClientRect();
            if (rect) {
              setMenuStyle({
                top: `${e.pageY - layout.rect.top + 10}px`,
                left: `${Math.min(
                  rect.right - layout.rect.left - 90,
                  e.pageX - layout.rect.left + 10
                )}px`,
              });
            }
          } else {
            closeMenu();
          }
        }
      },
      [closeMenu, items]
    );

    let status: Status = 'ready';
    if (import.meta.env.PROD) {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      status = useExternal(
        `${
          import.meta.env.BIZ_ORIGIN_PUBLIC_URL
        }/static/vendor/react-window/v1.8.7/index-prod.umd.js`
      );
    }

    useEffect(() => {
      if (import.meta.env.SSR) {
        return EMPTY_FN;
      }
      const headEl = document.querySelector('#siderHead') as HTMLElement;
      let headHeight = 0;
      if (headEl) {
        headHeight = headEl.offsetHeight;
      }
      setHeight(Math.max(10, height - 48 - headHeight));

      const onClick = (e: MouseEvent) => {
        let el = e.target as null | HTMLElement;
        for (let i = 0; i < 3; i += 1) {
          if (el) {
            if (el.classList.contains(styles.menu)) {
              return;
            }
            el = el.parentElement;
          }
        }
        closeMenu();
      };
      document.addEventListener('click', onClick, false);
      return () => {
        document.removeEventListener('click', onClick, false);
      };
    }, [closeMenu, height]);

    let child;
    if (loading) {
      child = <div className={styles.empty} style={customStyle} />;
    } else if (!items.length) {
      child = (
        <div className={styles.empty} style={customStyle}>
          <p className="pt-4 px-4">暂时没有相关会话</p>
        </div>
      );
    } else if (status === 'ready') {
      child = (
        <List
          ref={ref}
          items={items}
          height={listHeight}
          selectId={selectId}
          menuId={menuId}
          setBanners={setBanners}
        />
      );
    }

    return (
      <div
        {...props}
        ref={wrapEl}
        className={classNames(className, styles.sessions)}
        style={customStyle}
        onContextMenu={onContextMenu}
      >
        <Spin spinning={loading || status === 'loading'}>{child}</Spin>

        <Menu
          items={sessionItems}
          disabled={submitting !== ''}
          selectable={false}
          className={styles.menu}
          style={menuStyle}
          onClick={onClickContextMenu}
        />
      </div>
    );
  }
);

SessionList.displayName = 'SessionList';

export default SessionList;
