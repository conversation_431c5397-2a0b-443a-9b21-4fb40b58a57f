import {
  forwardRef,
  MouseEvent<PERSON><PERSON><PERSON>,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
} from 'react';
import { VariableSizeList, VariableSizeListProps } from 'react-window';
import { Session, sessions } from '@/store';
import { createFunctionSession, GetMemberBannerResult } from '@/apis';
import isNaN from 'lodash/isNaN';
import { SessionTypes } from '@/utils/session';
import { useDebounceFn, useMemoizedFn } from 'ahooks';
import Banner, { BannerProps } from './banner';
import SessionItem from './session';
import styles from './index.module.less';

export interface ListProps extends Pick<BannerProps, 'setBanners'> {
  items: (Session | { typeCode: 'BANNER_NOTICE'; data: GetMemberBannerResult[] })[];
  height: number;
  selectId: number;
  menuId: number;
}

export interface ListInstance {
  scrollToNoReading: () => void;
  resetAfter: MultipleParamsFn<[index?: number, shouldForceUpdate?: boolean]>;
}

const List = forwardRef<ListInstance, ListProps>(
  ({ items, height, selectId, menuId, setBanners }, ref) => {
    const listRef = useRef<VariableSizeList>(null);
    const outerRef = useRef<HTMLDivElement>(null);
    const onClickSession: MouseEventHandler<HTMLElement> = useCallback(
      (e) => {
        const idx = +(e.currentTarget.getAttribute('data-idx') || '');
        if (!isNaN(idx) && idx !== -1) {
          const item = sessions.sessions[items[0].typeCode === 'BANNER_NOTICE' ? idx - 1 : idx];
          const { sessionType } = item;
          const isFunction = sessionType === SessionTypes.FUNCTION;
          sessions.select(item);
          if (isFunction || sessionType === SessionTypes.APP || sessionType === SessionTypes.SHOP) {
            if (!isFunction) {
              sessions.toggleTopState(item.id, item.isTop);
            } else if (item.typeCode) {
              createFunctionSession(item.typeCode);
            }
          }
        }
      },
      [items]
    );

    const { run: resetAfterIndex } = useDebounceFn(
      (index: number, shouldForceUpdate?: boolean) => {
        listRef.current?.resetAfterIndex(index, shouldForceUpdate);
      },
      { wait: 500 }
    );

    const itemSize = useMemoizedFn((index: number) =>
      items[index].typeCode === 'BANNER_NOTICE' ? 121 : 68
    );
    const render: VariableSizeListProps['children'] = useCallback(
      ({ index, data, style }) => {
        const item = data[index];
        return (
          <div className={styles.item} style={style}>
            {item.typeCode === 'BANNER_NOTICE' ? (
              <Banner
                resetAfterIndex={resetAfterIndex}
                banners={item.data}
                setBanners={setBanners}
              />
            ) : (
              <SessionItem
                data-idx={index}
                session={item}
                checked={selectId === item.id}
                menuChecked={menuId === item.id}
                onClick={onClickSession}
              />
            )}
          </div>
        );
      },
      [menuId, selectId] // eslint-disable-line
    );

    useEffect(() => {
      resetAfterIndex(0);
    }, [items, resetAfterIndex]);
    useImperativeHandle(
      ref,
      () => ({
        scrollToNoReading: () => {
          const $outer = outerRef.current;
          if ($outer && $outer.children[0]) {
            // 获取外部容器信息
            const { top } = $outer.getBoundingClientRect();
            const $el = $outer.children[0];
            let startIndex = -1;
            for (let i = 0; i < $el.children.length; i += 1) {
              const rect = $el.children[i].getBoundingClientRect();
              if (rect.bottom > top) {
                startIndex = +($el.children[i].children[0].getAttribute('data-idx') || 0) + 1;
                break;
              }
            }
            let dogIndex = -1;
            const findIndex = (start: number, end: number) => {
              for (let i = start; i < end; i += 1) {
                if ((items[i] as Session).readNum > 0) {
                  if (!(items[i] as Session).isShield) {
                    return i;
                  }
                  if (dogIndex === -1) {
                    dogIndex = i;
                  }
                }
              }
              return -1;
            };
            let index = findIndex(startIndex, items.length);
            if (index === -1) {
              index = findIndex(0, startIndex);
              if (index === -1) {
                index = Math.max(items[0].typeCode === 'BANNER_NOTICE' ? 1 : 0, dogIndex);
              }
            }
            listRef.current?.scrollToItem(index, 'start');
          }
        },
        resetAfter: (index, shouldForceUpdate) => {
          listRef.current?.resetAfterIndex(index || 0, shouldForceUpdate);
        },
      }),
      [items]
    );

    return (
      <VariableSizeList
        ref={listRef}
        outerRef={outerRef}
        itemData={items}
        itemSize={itemSize}
        height={height}
        itemCount={items.length}
        width="100%"
      >
        {render}
      </VariableSizeList>
    );
  }
);

export default List;
