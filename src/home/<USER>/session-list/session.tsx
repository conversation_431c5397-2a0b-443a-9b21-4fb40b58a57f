import { HTMLAttributes, useMemo } from 'react';
import { Avatar } from '@/components';
import { Session as SessionItem, sessions } from '@/store';
import classNames from 'classnames';
import { SessionTypes } from '@/utils/session';
import { Badge } from 'antd';
import dayjs from 'dayjs';
import { MessageType, MessageTypeTexts } from '@echronos/plugin-im';
import isString from 'lodash/isString';
import { Link } from 'react-router-dom';
import codeToUrl from '@/utils/function-code-to-urls';
import LinkCircleOutlined from '@/components/icon/link-circle';
import CsOutlined from '@/components/icon/cs';
import ChatTag from '../../components/chat-tag';
import useLink from './use-link';
import Btn from './button';
import styles from './index.module.less';

export interface SessionProps extends HTMLAttributes<HTMLElement> {
  checked: boolean;
  menuChecked: boolean;
  session: SessionItem;
}

const showStrMessageTypes = [
  MessageType.WITHDRAW,
  MessageType.TEXT,
  MessageType.NOTICE,
  MessageType.SYSTEM,
];

function Session({ checked, session, menuChecked, className, onClick, ...props }: SessionProps) {
  const [avatar, formatDate] = useMemo(() => {
    let showDate: string | null = null;
    if (session.lastMsgTime) {
      const date = dayjs(session.lastMsgTime);
      if (date.isSame(sessions.date, 'day')) {
        showDate = `${date.hour() < 12 ? '上' : '下'}午 ${date.format('hh:mm')}`;
      } else if (date.isSame(sessions.date, 'year')) {
        showDate = date.format('MM月DD日');
      } else {
        showDate = date.format('YY年MM月');
      }
    }
    return [
      `${session.avatar}?x-image-process=image/resize,m_pad,w_42,h_42,limit_1/sharpen,100`,
      showDate,
    ];
  }, [session.avatar, session.lastMsgTime]);
  const lastMessage = useMemo(() => {
    const lastMsg = session.lastMessage;
    if (lastMsg) {
      if (isString(lastMsg)) {
        return lastMsg;
      }
      const type = lastMsg.msgType;
      if (type === MessageType.TEMPLATE) {
        let msg: Record<string, unknown>;
        try {
          const content = lastMsg.msg;
          msg = isString(content) ? JSON.parse(content) : content;
        } catch (e) {
          console.warn(e);
          msg = {};
        }
        return msg?.title;
      }
      if (showStrMessageTypes.includes(type)) {
        return lastMsg.msg;
      }
      return `[${MessageTypeTexts[type] || '未知消息'}]`;
    }
    return null;
  }, [session.lastMessage]);
  const isShop = session.sessionType === SessionTypes.SHOP;
  const isFunction = session.sessionType === SessionTypes.FUNCTION;
  const [to, handleClick] = useLink(session, onClick);

  let tool;
  if (isShop) {
    tool = (
      <Btn to={`/imc/0?shopId=${session.typeId}`} icon={<CsOutlined />}>
        客服
      </Btn>
    );
  } else if (
    session.useStatus &&
    (session.openType === 1 || session.openType === 3) &&
    codeToUrl[session.typeCode]
  ) {
    tool = (
      <Btn to={codeToUrl[session.typeCode]} icon={<LinkCircleOutlined />}>
        进应用
      </Btn>
    );
  }
  const children = (
    <>
      <Badge count={session.readNum} dot={session.isDisturb as boolean}>
        <Avatar
          src={avatar}
          size={44}
          fitContain={isShop}
          alt={session.name}
          shape={isFunction ? 'square' : 'circle'}
          className={styles.avatar}
        />
      </Badge>
      <div className={styles.body}>
        <div className={styles.row}>
          <div className={styles.titleBox}>
            <h4 className={styles.title}>{session.name}</h4>
            <ChatTag value={session} />
          </div>
          {formatDate && <div className={styles.date}>{formatDate}</div>}
        </div>
        {(lastMessage || tool) && (
          <div className={styles.row}>
            <p className={styles.desc}>
              {session.at && session.readNum !== 0 && <span className={styles.at}>[有人@你]</span>}
              {lastMessage}
            </p>
            {tool}
          </div>
        )}
      </div>
    </>
  );
  const classes = classNames(className, styles.session, {
    [styles.checked]: checked,
    [styles.menuChecked]: menuChecked,
    [styles.topping]: session.isTop,
  });

  return isString(to) ? (
    <Link
      {...props}
      to={to}
      target={session.openType === 4 ? '_blank' : '_self'}
      className={classes}
      onClick={handleClick}
    >
      {children}
    </Link>
  ) : (
    <div
      {...props}
      role="button"
      tabIndex={0}
      title={session.name}
      className={classes}
      onClick={handleClick}
    >
      {children}
    </div>
  );
}

Session.displayName = 'SessionItem';

export default Session;
