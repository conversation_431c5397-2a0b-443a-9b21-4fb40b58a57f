import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import isString from 'lodash/isString';
import { Modal } from '@/components';
import { Session, user } from '@/store';
import { SessionTypes } from '@/utils/session';
import codeToUrl from '@/utils/function-code-to-urls';

export type LinkTo = string | null | { url: string; companyId: number };

const renovationBrandTypeCodes = ['floorCommentDetails', 'replyCommentDetails'];

function useLink(
  session: Session,
  clickFn?: MouseEventHandler
): [to: LinkTo, onClick: MouseEventHandler<HTMLElement>] {
  const isFunction = session.sessionType === SessionTypes.FUNCTION;
  const navigate = useNavigate();
  const to: LinkTo = useMemo(() => {
    if (isFunction) {
      if (session.typeCode === 'myShop') {
        return `/shop-home?bid=${user.shop.id}&code=deco_shop`;
      }

      if (renovationBrandTypeCodes.includes(session.typeCode)) {
        const { lastMessage } = session;
        const args =
          lastMessage && lastMessage.msg && !isString(lastMessage.msg)
            ? (lastMessage.msg.args as Record<string, unknown>)
            : null;
        const url = `/renovation/brand?albumId=${args?.albumId}&commentId=${args?.reviewId}`;
        if (args?.companyId && args.companyId !== user.companyId) {
          return { url, companyId: args.companyId as number };
        }
        return url;
      }

      if (session.useStatus) {
        if (session.openType === 1 || session.openType === 3) {
          return `/imc/${session.id}`;
        }
        return codeToUrl[session.typeCode];
      }
      return null;
    }
    switch (session.sessionType) {
      case SessionTypes.CHAT:
      case SessionTypes.GROUP:
      case SessionTypes.CUSTOMER_SERVICE:
        return `/imc/${session.id}`;
      case SessionTypes.SHOP:
        // return `/shop/store/${session.typeId}`;
        return `/shop-home?bid=${session.typeId}&code=deco_shop`;
      default:
        return (session.typeTagValue as string) || import.meta.env.BIZ_APP_HOME_URL;
    }
  }, [session.id]); // eslint-disable-line
  const onClick: MouseEventHandler<HTMLElement> = useCallback(
    (e) => {
      if (!to) {
        e.preventDefault();
        if (isFunction && !session.useStatus) {
          message.error('该应用已停用，暂时无法使用');
        } else {
          message.info('Web暂不支持此功能，请在App上打开');
        }
      } else {
        if (!isString(to)) {
          Modal.confirm({
            title: '提示',
            content: '该店铺不属于当前公司，是否切换公司？',
            onOk: () => {
              user.switchCompany(to.companyId as number).finally(() => {
                navigate(to.url);
              });
            },
            onCancel: () => {
              navigate(to.url);
            },
          });
        }
        clickFn?.(e);
      }
    },
    [clickFn, to] // eslint-disable-line
  );

  return [to, onClick];
}

export default useLink;
