import { H<PERSON><PERSON>ttributes, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>actNode, useCallback } from 'react';
import { To, useNavigate } from 'react-router-dom';
import classNames from 'classnames';
import styles from './index.module.less';

export interface ButtonProps extends HTMLAttributes<HTMLButtonElement> {
  to?: To | null;
  icon?: ReactNode;
}

function Button({ icon, to, children, className, onClick, ...props }: ButtonProps) {
  const navigate = useNavigate();
  const handleClick: MouseEventHandler<HTMLButtonElement> = useCallback(
    (e) => {
      if (to) {
        navigate(to);
      }
      e.preventDefault();
      e.stopPropagation();
      onClick?.(e);
    },
    [navigate, onClick, to]
  );

  return (
    <button
      {...props}
      type="button"
      className={classNames(className, styles.btn)}
      onClick={handleClick}
    >
      {icon && <span className={styles.btnIcon}>{icon}</span>}
      <span>{children}</span>
    </button>
  );
}

Button.displayName = 'SessionItemButton';
Button.defaultProps = {
  to: null,
  icon: null,
};

export default Button;
