import { useEffect, useMemo } from 'react';
import { Spin } from 'antd';
import { observer } from 'mobx-react-lite';
import { layout } from '@/store';
import microApp from '../store/micro-app';
import styles from './micro-app.module.less';

declare global {
  interface Window {
    startMicroApp?: (() => void) | true;
  }
}

function MicroApp() {
  const style = useMemo(
    () => ({ height: `${layout.rect.usableHeight}px` }),
    [layout.rect.usableHeight] // eslint-disable-line
  );

  // 功能过渡适配
  if (import.meta.env.BASE_TRANSITION && !import.meta.env.SSR) {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    useEffect(() => {
      microApp.start();
      if (typeof window.startMicroApp === 'function') {
        window.startMicroApp();
      } else {
        window.startMicroApp = true;
      }
    }, []);
  }

  return (
    <Spin spinning={microApp.loading} className={styles.spin}>
      <div id="microApp" data-context className={styles.micro} style={style} />
    </Spin>
  );
}

export default observer(MicroApp);
