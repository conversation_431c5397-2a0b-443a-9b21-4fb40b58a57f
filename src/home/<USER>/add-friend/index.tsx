import { Button } from 'antd';
import isNumber from 'lodash/isNumber';
import { userAddFriend, UserCardInfoResult } from '@/apis';
import { dialog } from '@/components';
import FriendInfo, { FriendInfoInstance } from './friend-info';
import styles from './friend.module.less';

const commonConfig = {
  width: 350,
  centered: true,
  className: styles.dialog,
};

function addFriend(user: number | UserCardInfoResult, prefix?: string) {
  let infoInstance: FriendInfoInstance;

  function ref(instance: FriendInfoInstance) {
    if (!infoInstance) {
      infoInstance = instance;
    }
  }

  const instance = dialog({
    ...commonConfig,
    title: '添加好友',
    content: (
      <FriendInfo
        ref={ref}
        userInfo={user}
        prefix={prefix}
        onFinished={() => {
          instance.update({ okButtonProps: { disabled: false } });
        }}
      />
    ),
    onConfirm: (close) => {
      instance.update({ confirmLoading: true });
      userAddFriend(infoInstance.getValues())
        .then(() => {
          infoInstance.success();
          instance.update({
            footer: (
              <Button block type="primary" shape="round" onClick={close}>
                完成
              </Button>
            ),
          });
        })
        .catch(() => {
          instance.update({ confirmLoading: false });
        });
    },
  });

  if (isNumber(user)) {
    instance.update({ okButtonProps: { disabled: true } });
  }
}

export default addFriend;
