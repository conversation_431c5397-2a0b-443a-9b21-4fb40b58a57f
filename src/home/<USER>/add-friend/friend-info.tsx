import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { Avatar, Col, Form, Input, Row, Spin } from 'antd';
import isNumber from 'lodash/isNumber';
import isFunction from 'lodash/isFunction';
import { UserAddFriendProps, UserCardInfoResult } from '@/apis';
import { Icon } from '@/components';
import { user } from '@/store';
import ChatTag from '../../components/chat-tag';
import styles from './friend.module.less';
import userCardInfo from '../../../../apis/user-card-info';
import { isColleague } from '../../utils/relation';

export interface FriendInfoInstance {
  getValues: () => UserAddFriendProps;
  success: () => void;
}

export interface FriendInfoProps {
  userInfo: number | UserCardInfoResult;
  prefix?: string;
  onFinished?: MultipleParamsFn<[result: UserCardInfoResult]>;
}

const FriendInfo = forwardRef<FriendInfoInstance, FriendInfoProps>(
  ({ userInfo, prefix, onFinished }, ref) => {
    const [loading, setLoading] = useState(false);
    const [info, setInfo] = useState(isNumber(userInfo) ? null : userInfo);
    const [success, setSuccess] = useState(false);
    const [form] = Form.useForm();

    useEffect(() => {
      if (isNumber(userInfo)) {
        setLoading(true);
        userCardInfo({ otherUserId: userInfo })
          .then((result) => {
            if (result && result.userId) {
              setTimeout(() => {
                setInfo(result);
                if (isFunction(onFinished)) {
                  onFinished(result);
                }
              }, 100);
            }
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }, [onFinished, userInfo]);

    useImperativeHandle(ref, () => ({
      getValues: () => form.getFieldsValue(),
      success: () => {
        setSuccess(true);
      },
    }));

    let children;
    if (loading || !info) {
      children = <div style={{ height: '200px' }} />;
    } else {
      const address = info.organizationList?.[0]?.companyAddr;
      const nickname = isColleague(info)
        ? info.remark || info.nickname || info.userNickName
        : info.remark || info.userNickName;
      children = (
        <>
          <div className={styles.box}>
            <div className={styles.nickname}>
              <h3 className={styles.title}>{nickname}</h3>
              <ChatTag value={info} />
            </div>
            <Avatar src={info.avatar} size={60} />
          </div>
          <div className={styles.box}>
            <div className={styles.info}>
              <span className={styles.label}>昵称</span>
              <span>{info.userNickName}</span>
            </div>
            {info.companyName && info.companyNature !== 1 ? (
              <div className={styles.info}>
                <span className={styles.label}>公司</span>
                <span>{info.companyName}</span>
              </div>
            ) : null}
            {address ? (
              <div className={styles.info}>
                <span className={styles.label}>所在地</span>
                <span>{address}</span>
              </div>
            ) : null}
          </div>
          <div className={styles.box}>
            {success ? (
              <Row wrap={false}>
                <Col flex="none">
                  <Icon name="tick-outline" size={22} color="#3296fa" />
                </Col>
                <Col flex="auto" className="pl-2" style={{ color: '#999', fontSize: '12px' }}>
                  你的好友添加请求已经发送成功，请耐心等待对方确认。
                </Col>
              </Row>
            ) : (
              <>
                <Form.Item initialValue={info.userId} name="friendId" hidden>
                  <Input />
                </Form.Item>
                <Form.Item
                  label="验证消息"
                  name="checkInfo"
                  initialValue={`我是${prefix ? `${prefix.trim()}的` : ''}${user.nickname}`}
                  className={styles.formItem}
                >
                  <Input.TextArea style={{ height: '62px', resize: 'none' }} />
                </Form.Item>
                <div className="pt-3" />
                <Form.Item
                  label="备注姓名"
                  name="remarks"
                  initialValue=""
                  className={styles.formItem}
                >
                  <Input placeholder="选填" />
                </Form.Item>
              </>
            )}
          </div>
        </>
      );
    }

    return (
      <Form form={form} className="px-5">
        <Spin spinning={loading}>{children}</Spin>
      </Form>
    );
  }
);

FriendInfo.defaultProps = {
  prefix: '',
  onFinished: undefined,
};

export default FriendInfo;
