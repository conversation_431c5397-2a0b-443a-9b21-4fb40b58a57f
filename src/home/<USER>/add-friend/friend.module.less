@import 'styles/mixins/mixins';

.dialog {
  :global {
    .ant-modal-body {
      padding: 0;
    }
  }
}

.box {
  padding: 16px 0;
  border-bottom: 1px solid #f5f6f7;

  &:last-child {
    border-bottom: none;
  }

  :global {
    .ant-spin-nested-loading {
      .ant-spin-blur {
        visibility: hidden;
      }
    }
  }
}

.nickname {
  display: inline-block;
  width: 240px;
  vertical-align: middle;
}

.title {
  .text-overflow();

  display: inline-block;
  max-width: 200px;
  line-height: 18px;
  margin-bottom: 0;
  vertical-align: top;
}

.info {
  font-size: 12px;
  line-height: 16px;
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }
}

.label {
  color: #999;
  display: inline-block;
  width: 56px;
  padding-right: 20px;
  text-align: right;
}

.formItem {
  margin-bottom: 0;

  :global {
    .ant-form-item-label {
      & > label {
        color: #999;
        font-size: 12px;
        height: 30px;
        line-height: 30px;
        padding-right: 12px;

        &::after {
          display: none;
        }
      }
    }

    .ant-input {
      font-size: 12px;
    }
  }
}

.groupNo {
  color: #999;
  font-size: 12px;
  margin-top: 4px;
  margin-bottom: 0;
  padding-right: 8px;
}
