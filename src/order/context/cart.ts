import { createContext, Dispatch } from 'react';
import difference from 'lodash/difference';
import type { CheckboxProps } from 'antd';
import type { CartGoods, CartItem } from '@/apis';
import { EMPTY_FN } from '@/utils/const';

export interface CartState {
  loading: boolean;
  groups: CartItem[];
  checks: number[];
}

export type CartReducerAction =
  | { type: 'loading' }
  | { type: 'set' | 'init'; payload: CartItem[] }
  | { type: 'delete'; payload: number[] }
  | { type: 'setCheck'; payload: number[] }
  | { type: 'editGoods'; payload: [rowIndex: number, colIndex: number, data: Partial<CartGoods>] };

export interface CartContextValue {
  checks: number[];
  dispatch: Dispatch<CartReducerAction>;
  toggleChecks: MultipleParamsFn<[checks: number[], isAdd?: boolean]>;
  removeGoodsList: MultipleParamsFn<[cartNoList: number]>;
  getCart: MultipleParamsFn<[values?: number[]]>;
  onChangeCheck: CheckboxProps['onChange'];
}

export function reducer(state: CartState, action: CartReducerAction): CartState {
  switch (action.type) {
    case 'set':
    case 'init':
      return {
        loading: false,
        groups: action.payload,
        checks: action.type === 'set' ? state.checks : [],
      };
    case 'editGoods': {
      const { payload } = action;
      let { groups } = state;
      const goodsList = groups[payload[0]]?.cartProductVOList;
      if (goodsList && goodsList[payload[1]]) {
        goodsList.splice(payload[1], 1, {
          ...goodsList[payload[1]],
          ...payload[2],
        });
        groups = [...groups];
      }
      return { ...state, groups };
    }
    case 'setCheck':
      return { ...state, checks: action.payload };
    case 'delete':
      return {
        ...state,
        groups: state.groups
          .map((group) => {
            const list = group.cartProductVOList.filter(
              (item) => action.payload.indexOf(item.cartNo) === -1
            );
            return list.length > 0 ? { ...group, cartProductVOList: list } : null;
          })
          .filter((group) => group !== null) as CartItem[],
        checks: difference(state.checks, action.payload),
      };
    case 'loading':
      return { ...state, loading: true };
    default:
      return state;
  }
}

const context = createContext<CartContextValue>({
  checks: [],
  dispatch: EMPTY_FN,
  toggleChecks: EMPTY_FN,
  removeGoodsList: EMPTY_FN,
  getCart: EMPTY_FN,
  onChangeCheck: EMPTY_FN,
});

export default context;
