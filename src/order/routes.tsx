import { RouteObject } from 'react-router-dom';
import { lazy } from '@/components';

// 已迁移到 transact 应用
const routes: RouteObject[] = [
  // {
  //   path: '/indent',
  //   element: lazy(() => import('./pages/home/<USER>')),
  // },
  // {
  //   path: '/group',
  //   element: lazy(() => import('./pages/group/index')),
  // },
  // {
  //   path: '/distribution/order',
  //   element: lazy(() => import('./pages/distribution/index')),
  // },
  // {
  //   path: '/indent/management',
  //   element: lazy(() => import('./pages/management/index')),
  //   children: [
  //     {
  //       path: '/indent/management/order-type',
  //       element: lazy(() => import('./pages/management/order-type/order-type')),
  //     },
  //   ],
  // },
  // {
  //   path: '/indent/production',
  //   element: lazy(() => import('./pages/production/index')),
  // },
  // {
  //   path: '/indent/list',
  //   element: lazy(() => import('./pages/list/index')),
  // },
  // {
  //   path: '/cart',
  //   element: lazy(() => import('./pages/cart/index')),
  // },
];

if (import.meta.env.DEV) {
  routes.push({
    path: '/indent/icon',
    element: lazy(() => import('./pages/home/<USER>')),
  });
}

export default routes;
