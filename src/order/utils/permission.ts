import { testPerm } from '@/utils/permission';

const sell: Record<number, string | string[]> = {
  1606: 'R_001_001_005', // 销售订单:锁价审批
  1604: 'R_001_001_005', // 销售订单:修改锁价时间
  1311: 'R_001_001_006', // 销售订单:货物分批
  1800: 'R_001_001_007', // 销售订单:暂估交易
  1321: 'R_001_001_007_001', // 销售订单:同意结算申请
  1322: 'R_001_001_007_002', // 销售订单:拒接结算申请
  1008: ['R_001_001_008', 'R_001_001_021'], // 销售订单:上传发票
  2020: 'R_001_001_009', // 销售订单:订单补录（生产排期）
  1003: 'R_001_001_010', // 销售订单:接单
  1004: 'R_001_001_011', // 销售订单:备货
  1005: 'R_001_001_012', // 销售订单:发货
  4001: 'R_001_001_012', // 销售订单:发货通知
  1211: 'R_001_001_013', // 销售订单:安装完成
  1215: 'R_001_001_014', // 销售订单:结算验收
  1220: 'R_001_001_015', // 销售订单:质保结束
  1701: 'R_001_001_016', // 销售订单:结算价格
  1709: 'R_001_001_017', // 销售订单:改价
  1120: 'R_001_001_018', // 销售订单:到货验收数量审批
  1504: 'R_001_001_019', // 销售订单:订单调整审批
  1301: 'R_001_005_002', // 分单管理:操作分单
  1009: 'R_001_001_020', // 销售订单:核对发票
  1101: 'R_001_001_022', // 销售订单:更新发货信息
  1910: 'R_001_007', // 销售订单:生产订单
};

const buy: Record<number, string | string[]> = {
  1401: 'R_001_002_005', // 采购订单:取消订单
  1601: 'R_001_002_006', // 采购订单:申请锁价
  1605: 'R_001_002_006', // 采购订单:撤销锁价申请
  1311: 'R_001_002_007', // 采购订单:货物分批
  1800: 'R_001_002_008', // 采购订单:暂估交易
  1501: 'R_001_002_009', // 采购订单:订单调整
  1009: ['R_001_002_010', 'R_001_002_016'], // 采购订单:发票
  4002: 'R_001_002_012', // 采购订单:验收通知
  1111: 'R_001_002_012', // 采购订单:到货验收
  1112: 'R_001_002_012', // 采购订单:上传到货验收附件
  1213: 'R_001_002_013', // 采购订单:安装验收
  1708: 'R_001_002_014', // 采购订单:结算价格审批
  1710: 'R_001_002_015', // 采购订单:改价审核
  1008: 'R_001_002_017', // 采购订单:上传发票
  1102: 'R_001_002_018', // 采购订单:更新收货信息
  1119: 'R_001_002_021', // 采购订单:发货数量差审批
};

const production: Record<number, string> = {
  2021: 'R_001_007_001', // 生产订单:生产完成
  2022: 'R_001_007_002', // 生产订单:打印
};

export const buyRecord: Record<number, string | string[]> = {
  1001: 'R_001_010_002', // 采购订单补录:付款
  1111: 'R_001_010_003', // 采购订单补录:验收
  1213: 'R_001_010_004', // 采购订单补录:安装验收
  1709: 'R_001_010_005', // 采购订单补录:改价
  1501: 'R_001_010_006', // 采购订单补录:订单调整
  1505: 'R_001_010_007', // 采购订单补录:订单调整记录
  1311: 'R_001_010_008', // 采购订单补录:货物分批
  1403: 'R_001_010_009', // 采购订单补录:取消订单
};

export const sellRecord: Record<number, string | string[]> = {
  1002: 'R_001_009_002', // 销售订单补录:收款
  2020: 'R_001_009_003', // 销售订单补录:生产排期
  1004: 'R_001_009_004', // 销售订单补录:备货
  1005: 'R_001_009_005', // 销售订单补录:发货
  1211: 'R_001_009_006', // 销售订单补录:安装完成
  1215: 'R_001_009_007', // 销售订单补录:结算验收
  1220: 'R_001_009_008', // 销售订单补录:质保期结束
  1709: 'R_001_009_009', // 销售订单补录:改价
  1501: 'R_001_009_010', // 销售订单补录:订单调整
  1505: 'R_001_009_011', // 销售订单补录:订单调整记录
  1311: 'R_001_009_012', // 销售订单补录:货物分批
  1403: 'R_001_009_013', // 销售订单补录:取消订单
};

export default function checkPerm(
  orderFuncType: number, // 0：线上商城，1：手工补录（进销存），2：询报价，3:应用（应用购买），4:商城补录，5：分销订单，6：分销垫资订单 -1: 生产
  orderProperty: number, // 1买入订单 2售出订单
  orderType: number, // 0正常订单 1暂估订单
  actionCode: number
) {
  let permCode: string | string[] = '';
  if (orderFuncType === 0 || orderFuncType === 3) {
    if (orderType && (actionCode === 1701 || actionCode === 1708)) {
      // 销售订单:暂估交易:结算价格
      if (actionCode === 1701) permCode = 'R_001_001_007_003';
      // 采购订单:暂估交易:结算价格审批
      if (actionCode === 1708) permCode = 'R_001_002_008_006';
    } else if (orderProperty === 1) {
      permCode = buy[actionCode];
    } else if (orderProperty === 2) {
      permCode = sell[actionCode];
    }
  } else if (orderFuncType === 4) {
    if (orderProperty === 1) {
      permCode = buyRecord[actionCode];
    } else if (orderProperty === 2) {
      permCode = sellRecord[actionCode];
    }
  } else if (orderFuncType === -1) {
    permCode = production[actionCode];
  }
  if (!permCode) return true;
  return testPerm(permCode);
}
