import type {
  CartGoods,
  CartGoodsService,
  CartGoodsServiceItem,
  CartPackageGoods,
  GoodsDetailResult,
} from '@/apis';

/**
 * 获取商品服务
 * @param goods
 * @param format
 * @param getIndex
 */
export const getGoodsServices = <T = CartGoodsServiceItem>(
  goods: CartGoods | CartPackageGoods,
  format?: MultipleParamsFn<[data: CartGoodsServiceItem], T>,
  getIndex?: MultipleParamsFn<[item: CartGoodsServiceItem], number>
): T[] => {
  const services: T[] = [];
  goods.serviceProductVOS?.forEach((serviceGroup) => {
    serviceGroup.serviceStandardProductVOS.forEach((item) => {
      const value = format ? format(item) : (item as T);
      if (getIndex) {
        const index = getIndex(item);
        if (index >= 0) {
          services[index] = value;
          return;
        }
      }
      services.push(value);
    });
  });
  return services;
};

/**
 * 格式化详情
 * @param detail
 * @param item
 * @param values
 */
export const formatGoodsDetail = (
  detail: GoodsDetailResult,
  item: Omit<CartGoods, 'packageCartProductVOS'>,
  values: { payWayNo: number; selectUnit: string; service: number[]; meta: number[] }
) => {
  const number = `${detail.minimum || +(item.number || '1')}`;
  let serviceProductVOS: CartGoodsService[] | undefined;
  if (values.service && values.service.length !== 0) {
    serviceProductVOS = [];
    detail.serviceStandardList?.forEach((service, index) => {
      const standard = service.standardDetailList.find((tmp) => tmp.id === values.service[index]);
      if (standard) {
        // @ts-ignore
        serviceProductVOS.push({
          groupName: service.groupName,
          serviceStandardProductVOS: [
            {
              shopSkuId: standard.id,
              skuId: standard.skuId,
              serviceNumber: number,
              serviceProductPrice: `${standard.marketPrice}`,
              serviceName: standard.name,
            },
          ],
        });
      }
    });
  }

  return {
    ...item,
    serviceProductVOS,
    number,
    packageNumber: number,
    shopSkuId: detail.shopSkuId,
    skuId: detail.skuId,
    productName: detail.name,
    productUnit: detail.unit,
    payWayNo: detail.currentPayWayNo,
    productMainPic: detail.imageList[0],
    productMinimumSalesUnit: detail.saleGroup || item.productMinimumSalesUnit,
    productSpecificationList: detail.choiceList.map((choice, index) => ({
      name: choice.name,
      value:
        choice.value.find((choiceItem: { id: any }) => choiceItem.id === values.meta[index])
          ?.title || '',
    })),
  } as unknown as CartPackageGoods;
};

/**
 * 获取单位名称
 * @param id
 * @param unitList
 */
export const getUnitName = (id: string | number, unitList?: GoodsDetailResult['unitList']) => {
  if (id !== '' && unitList && unitList.length > 0) {
    const unit = unitList.find((item) => item.id === id);
    if (unit) {
      return unit.title;
    }
  }
  return '';
};
