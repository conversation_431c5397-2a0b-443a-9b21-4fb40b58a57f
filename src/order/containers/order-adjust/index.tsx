import { StockGoodsListResult } from '@/apis';
import {
  getOrderTrimCart,
  postOrderCartAddProduct,
  postOrderTrimApply,
  postOrderTrimDelete,
  TrimCartResult,
} from '@/apis/order';
import { Drawer, Icon } from '@/components';
import { Button, message, Modal } from 'antd';
import { Stepper } from 'antd-mobile';
import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { DrawerRefType } from '@/components/drawer/drawer';
import classNames from 'classnames';
import GoodsItem from '../../components/goods-card/index';
import OrderAddGoods from '../order-addgoods';
import SelectGoods from '../select-goods/index';
import up from '../../assets/img/up.png';
import down from '../../assets/img/down.png';
import styles from './index.module.less';

interface OrderAdjustProps {
  visible: boolean;
  adjustId: number;
  nycbngOrgId: string;
  adjustName: string;
  distributionCompanyId?: number;
  orderNo?: number;
  onClose: () => void;
  getSalesList: () => void;
}

function OrderAdjust({
  visible,
  onClose,
  adjustName,
  distributionCompanyId,
  adjustId,
  nycbngOrgId,
  orderNo,
  getSalesList,
}: PropsWithChildren<OrderAdjustProps>) {
  const { confirm } = Modal;
  const drawerRef = useRef<DrawerRefType>(null);
  const [trimCartList, setTrimCartList] = useState<TrimCartResult[]>([]);
  const [selectGoodsList, setSelectGoodsList] = useState<StockGoodsListResult[]>([]);
  const [priceFront, setPriceFront] = useState(0);
  const [priceQueen, setPriceQueen] = useState(0);
  const [showSelectGoods, setShowSelectGoods] = useState(false);
  const [showOrderAddGoods, setShowOrderAddGoods] = useState(false);
  // 表单改变
  const formChange = () => {
    if (!drawerRef.current?.getIsChange()) {
      drawerRef.current?.setIsChange(true);
    }
  };
  const getPrice = (list: TrimCartResult[]) => {
    let price = list.reduce(
      (value, item) => value + Number(item.firstNum) * Number(item.productPrice),
      0
    );
    price = list.reduce(
      (value, item) => value + Number(item.number) * Number(item.productPrice),
      price
    );
    setPriceQueen(price);
  };

  const updateNum = (e: number, item: TrimCartResult) => {
    // eslint-disable-next-line no-restricted-globals
    if (isNaN(e)) return;
    let digit = e;
    if (digit < 0 - +item.canTrimNumber) {
      digit = 0 - +item.canTrimNumber;
    }
    const list = trimCartList.map((m) => ({
      ...m,
      number: m.shopSkuId === item.shopSkuId ? String(digit) : m.number,
    }));
    setTrimCartList(list);
    getPrice(list);
  };

  const getOrderList = () => {
    getOrderTrimCart({ orderNo: adjustId }, nycbngOrgId).then((res) => {
      const list = res.trimCartProductVOList.map((m) => ({
        ...m,
        firstNum: m.canDelete ? 0 : Number(m.number),
        number: m.canDelete ? m.number : '0',
        changeNum: m.canDelete
          ? Number(m.number) * Number(m.productPrice)
          : 0 * Number(m.productPrice),
        newShopNum: m.number,
      }));
      const price = list.reduce(
        (value, item) =>
          value + Number(item.canDelete ? 0 : item.firstNum) * Number(item.productPrice),
        0
      );
      setPriceFront(price);
      setTrimCartList(list);
      getPrice(list);
    });
  };

  const endNum = (e: number, item: TrimCartResult) => {
    // eslint-disable-next-line no-restricted-globals
    if (isNaN(e)) return;
    const list = trimCartList.map((m) => ({
      ...m,
      number: m.shopSkuId === item.shopSkuId ? String(e - (m.firstNum ? m.firstNum : 0)) : m.number,
    }));
    setTrimCartList(list);
    getPrice(list);
  };

  const postUpdateList = () => {
    const trimProductParams = trimCartList.map((m) => ({
      shopSkuId: m.shopSkuId,
      skuId: m.skuId,
      number: Number(m.number),
      relateUniqueNo: m.relateUniqueNo,
      remark: m.remark || '',
    }));
    postOrderTrimApply({ orderNo: adjustId, trimProductParams }, nycbngOrgId).then(() => {
      getSalesList();
      message.success('提交成功');
      onClose();
    });
  };

  const deleteIcon = (trimSnapNo: number, skuId: number) => {
    confirm({
      title: '提示',
      content: `确定移除此商品`,
      icon: null,
      width: 290,
      centered: true,
      className: styles.showModal,
      getContainer: document.querySelector('.adjust-drawer') as HTMLElement,
      onOk() {
        postOrderTrimDelete({ trimSnapNo }, nycbngOrgId).then(() => {
          message.success('删除成功');
          setSelectGoodsList(selectGoodsList.filter((f) => f.skuId !== skuId));
          getOrderList();
        });
      },
      onCancel() {},
    });
  };

  useEffect(() => {
    if (visible && adjustId) {
      getOrderList();
    }
  }, [adjustId, visible]); // eslint-disable-line

  return (
    <Drawer
      ref={drawerRef}
      title={<div style={{ paddingLeft: '44px' }}>订单调整</div>}
      visible={visible}
      onClose={() => {
        onClose();
      }}
      push={false}
      extra={
        <div
          className={styles.extra}
          role="button"
          tabIndex={0}
          onClick={() => {
            if (adjustName === '线上商城') {
              setShowOrderAddGoods(true);
            } else {
              setShowSelectGoods(true);
            }
          }}
        >
          添加商品
        </div>
      }
      footer={
        <>
          {/* <div className={styles.tip}>数量为0的商品，调整后将自动从生成的订单中移除</div> */}
          <div className={styles.footer}>
            <div>
              <div className={styles.totalAmount}>
                合计：<span className={styles.red}>￥</span>
                <span className={styles.money}>{priceQueen.toFixed(2)}</span>
              </div>
              <div className={styles.changeMoney}>
                <span className={styles.margin}>改前:￥{priceFront.toFixed(2)}</span>
                {priceQueen > priceFront ? (
                  <span className={styles.red}>+￥{(priceQueen - priceFront).toFixed(2)}</span>
                ) : null}
                {priceQueen < priceFront ? (
                  <span className={styles.green}>-￥{(priceFront - priceQueen).toFixed(2)}</span>
                ) : null}
              </div>
            </div>
            <div className={styles.btn}>
              {trimCartList.some((e) => e.canDelete) ||
              trimCartList
                .filter((item) => !item.canDelete)
                .some((itemE) => itemE.number !== '0') ? (
                <Button type="primary" role="button" tabIndex={0} onClick={postUpdateList}>
                  确定调整
                </Button>
              ) : (
                <Button>确定调整</Button>
              )}
              <div className={styles.btnText}>确认后需卖家审批</div>
            </div>
          </div>
        </>
      }
    >
      <div className="adjust-drawer">
        {trimCartList.length > 0
          ? trimCartList.map((item) => (
              <div className={styles.card}>
                <div className={styles.goodsItem}>
                  <div className={styles.goodsCard}>
                    <GoodsItem
                      orderType="sales"
                      img={item.productMainPic}
                      productName={item.productName}
                      productSpecificationList={item.productSpecificationList}
                      productPrice={item.productPrice}
                      productUnit={item.productUnit}
                      number={item.canDelete ? item.newShopNum : String(item.firstNum)}
                      isOrder
                    />
                  </div>
                  {item.canDelete && <div className={styles.addGoods}>新增</div>}
                  {item.canDelete && (
                    <div
                      className={styles.iconShow}
                      role="button"
                      tabIndex={0}
                      onClick={() => {
                        deleteIcon(item.trimSnapNo, item.skuId);
                      }}
                    >
                      <Icon name="close-circle2" size={20} />
                    </div>
                  )}
                </div>
                <div className={styles.cardFooter}>
                  {item.changeNum === Number(item.productPrice) * Number(item.number) && (
                    <div className={styles.text}>调整数量</div>
                  )}
                  {item.changeNum > Number(item.productPrice) * Number(item.number) && (
                    <div className={styles.text}>
                      <span className="mr-2">调整数量</span>
                      <img src={down} alt="" />
                    </div>
                  )}
                  {item.changeNum < Number(item.productPrice) * Number(item.number) && (
                    <div className={styles.text}>
                      <span className="mr-2">调整数量</span>
                      <img src={up} alt="" />
                    </div>
                  )}
                  <Stepper
                    // max={Number(item.canTrimNumber)}
                    min={0 - Number(item.canTrimNumber)}
                    digits={item.priceNum}
                    step={Number(item.productMinimumSalesUnit) || 1}
                    value={Number(item.number)}
                    onChange={(e) => {
                      updateNum(e, item);
                      formChange();
                    }}
                    onBlur={(e) => {
                      updateNum(Number(e.target.value), item);
                    }}
                  />
                </div>
                <div className={classNames(styles.cardFooter, styles.cardFooterS)}>
                  <div className={styles.text}>最终数量</div>
                  <Stepper
                    min={0}
                    digits={item.priceNum}
                    step={Number(item.productMinimumSalesUnit) || 1}
                    value={Number((+(item.firstNum || 0) + +item.number).toFixed(item.priceNum))}
                    onChange={(e) => {
                      endNum(e, item);
                    }}
                  />
                </div>
              </div>
            ))
          : null}
      </div>

      <SelectGoods
        visible={showSelectGoods}
        // selectGoodsList={selectGoodsList}
        isAdjust
        selectGoodsList={[]}
        distributionCompanyId={distributionCompanyId}
        onClose={() => {
          setShowSelectGoods(false);
        }}
        orderNo={orderNo}
        confirm={(arr: StockGoodsListResult[]) => {
          const trimCartListSkuId = trimCartList.map((m) => m.shopSkuId);
          const productList = arr
            .filter((f) => !trimCartListSkuId.includes(f.shopSkuId))
            .map((m) => ({
              number: m.number,
              productPrice: m.marketPrice,
              skuId: m.skuId,
              shopSkuId: m.id,
            }));

          postOrderCartAddProduct({ orderNo: adjustId, productList }, nycbngOrgId).then(() => {
            setShowSelectGoods(false);
            setSelectGoodsList(arr);
            getOrderList();
          });
        }}
      />
      <OrderAddGoods
        visible={showOrderAddGoods}
        goodsId={adjustId}
        onClose={() => {
          setShowOrderAddGoods(false);
        }}
        confirm={(arr) => {
          const productList = arr.map((m) => ({
            number: Number(m.number),
            skuId: m.skuId,
            shopSkuId: m.shopSkuId,
          }));
          postOrderCartAddProduct({ orderNo: adjustId, productList }, nycbngOrgId).then(() => {
            setShowOrderAddGoods(false);
            getOrderList();
          });
        }}
      />
    </Drawer>
  );
}

OrderAdjust.defaultProps = {
  distributionCompanyId: 0,
  orderNo: 0,
};

export default OrderAdjust;
