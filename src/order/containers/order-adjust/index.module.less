.extra {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.text {
  color: #888b98;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.lastNum {
  width: 135px;
  height: 28px;
  line-height: 28px;
  border-radius: 10px;
  text-align: center;
  background: #f5f5f5;
}

.showModal {
  :global {
    // .ant-modal-confirm-body .ant-modal-confirm-title,
    // .ant-modal-confirm-content {
    //   text-align: center;
    // }

    // .ant-modal-confirm-btns {
    //   width: 100%;
    //   display: flex;
    //   justify-content: space-between;
    // }
  }
}

.card {
  margin-bottom: 20px;
  padding: 0 20px 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .adm-stepper-middle {
      border: none;
    }

    .adm-stepper {
      width: 135px;
      border-radius: 10px;
    }

    .adm-button:not(.adm-button-default).adm-button-fill-none {
      color: black;
    }

    .adm-stepper .adm-stepper-input,
    .adm-button:not(.adm-button-default).adm-button-fill-none {
      height: 36px;
      background: #f5f6fa;
    }
  }

  .goodsItem {
    position: relative;
    border-bottom: 1px solid #f3f3f3;

    .goodsCard {
      padding: 20px 0;
    }

    .addGoods {
      color: #fff;
      font-size: 12px;
      width: 44px;
      height: 21px;
      line-height: 21px;
      position: absolute;
      top: 24px;
      left: 7px;
      border-radius: 10px;
      text-align: center;
      background: rgb(0 0 0 / 60%);
    }

    .iconShow {
      font-size: 18px;
      position: absolute;
      top: 2px;
      right: -15px;
      opacity: 0.2;
      cursor: pointer;
    }
  }

  .cardFooter {
    display: flex;
    margin-top: 16px;
    justify-content: space-between;
    align-items: center;
  }

  .cardFooterS {
    :global {
      button.adm-button.adm-button-primary.adm-button-fill-none.adm-button-shape-rectangular.adm-stepper-plus,
      button.adm-button.adm-button-primary.adm-button-fill-none.adm-button-shape-rectangular.adm-stepper-minus {
        display: none;
      }
    }
  }
}

.tip {
  color: #f9ae08;
  width: 100%;
  height: 36px;
  line-height: 36px;
  padding-left: 20px;
  background: #fef3da;
}

.footer {
  display: flex;
  width: 100%;
  padding: 20px;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 45%) 12%,
    rgb(255 255 255 / 72%) 25%,
    rgb(255 255 255 / 90%) 36%,
    rgb(255 255 255 / 90%) 36%
  );

  .totalAmount {
    margin-bottom: 4px;
  }

  .money {
    color: #ea1c26;
    font-size: 20px;
  }

  .red {
    color: #ea1c26;
  }

  .green {
    color: #05d380;
  }

  .changeMoney {
    font-size: 12px;
  }

  .margin {
    margin-right: 8px;
  }

  .btnText {
    color: #888b98;
    font-size: 12px;
    margin: 8px 0 10px;
  }
}
