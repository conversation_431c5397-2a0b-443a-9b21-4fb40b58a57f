import {
  BatchProductsResult,
  getOrderPreDetail,
  postOrderAddBatch,
  postOrderModifyBatch,
  PriceProductsResult,
} from '@/apis/order';
import { Drawer, Price } from '@/components';
import { Button, message, Modal } from 'antd';
import { Stepper } from 'antd-mobile';
import Big from 'big.js';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDebounceFn } from 'ahooks';
import GoodsItem from '../../components/goods-card/index';
import styles from './edit.module.less';

interface EditPartialProps {
  visible: boolean;
  listBatchProducts: BatchProductsResult[];
  title: string;
  orderNo: number;
  batchNo: number;
  nycbngOrgId: string;
  onClose: () => void;
  getBatchList: () => void;
}

function EditPartial(props: EditPartialProps) {
  const { confirm } = Modal;
  const {
    visible,
    onClose,
    listBatchProducts,
    title,
    orderNo,
    batchNo,
    nycbngOrgId,
    getBatchList,
  } = props;
  const [list, setList] = useState<BatchProductsResult[]>([]);
  const [btnLoading, setBtnLoading] = useState(false);
  const detailBatchProductsStr = useRef('');

  const totalAmount = useMemo(() => {
    let total = 0;
    // if (title === '修改批次') {
    //   const mount = new Big(0);
    //   list.forEach((item) => {
    //     const x = new Big(item.batchNum || 0).times(item.productPrice).toNumber();
    //     total += mount.plus(x).toNumber();
    //   });
    //   return total;
    // }
    const mount = new Big(0);
    list.forEach((item) => {
      total += mount.plus(new Big(item.batchNum || 0).times(item.productPrice)).toNumber();
    });
    return total;
  }, [list]); // eslint-disable-line

  const { run: onSubmit } = useDebounceFn(
    () => {
      if (title === '添加批次') {
        if (list.every((item) => !item.batchNum)) {
          message.warning('请增加分批商品数量');
          return;
        }
        postOrderAddBatch(
          {
            orderNo,
            productDetailParams: list.map((item) => ({
              orderProductNo: item.orderProductNo,
              batchNum: item.batchNum,
            })),
          },
          nycbngOrgId || ''
        ).then(() => {
          message.success('添加成功');
          getBatchList();
          onClose();
        });
      } else {
        setBtnLoading(true);
        postOrderModifyBatch(
          {
            batchNo,
            productDetailParams: list.map((item) => ({
              orderProductNo: item.orderProductNo,
              batchNum: item.batchNum || 0,
            })),
          },
          nycbngOrgId || ''
        )
          .then(() => {
            message.success('编辑成功');
            getBatchList();
            onClose();
          })
          .finally(() => {
            setBtnLoading(false);
          });
      }
    },
    { wait: 500 }
  );

  const onCloseDrawer = () => {
    if (JSON.stringify(list) !== detailBatchProductsStr.current) {
      confirm({
        title: '提示',
        content: '您编辑的内容尚未保存，确定要离开吗?',
        cancelText: '离开',
        okText: '继续编辑',
        icon: null,
        width: 290,
        centered: true,
        getContainer: document.querySelector('.adjust-drawer') as HTMLElement,
        onCancel() {
          onClose();
        },
      });
      return;
    }
    onClose();
  };

  useEffect(() => {
    if (visible) {
      const batchList = listBatchProducts.map((item) => ({
        ...item,
        batchNum: 0,
      }));
      setList(batchList);
      detailBatchProductsStr.current = JSON.stringify(batchList);
    }
    if (title === '修改批次') {
      getOrderPreDetail({ orderNo, isOrderDetail: 0 }, nycbngOrgId).then((res) => {
        const batchProducts: PriceProductsResult[] = [];
        listBatchProducts.forEach((item) => {
          res.batchProducts.forEach((it: PriceProductsResult) => {
            if (item.orderProductNo === it.orderProductNo) {
              const i = it;
              i.batchNumber = item.batchNumber;
              i.batchNum = Number(item.batchNumber);
              batchProducts.push(i);
            }
          });
        });
        setList(batchProducts);
        detailBatchProductsStr.current = JSON.stringify(batchProducts);
      });
    }
  }, [visible, title]); // eslint-disable-line

  return (
    <Drawer
      title={title}
      visible={visible}
      onClose={onCloseDrawer}
      footer={
        <div className={styles.footer}>
          <div>
            <div className={styles.partialText}>
              合计：
              <span className={styles.money}>
                ￥<Price value={totalAmount} format symbol="" />
              </span>
            </div>
            <div className={styles.textSize}>
              已选 {list.filter((item) => item.batchNum !== 0).length}种 商品
            </div>
          </div>
          <Button type="primary" loading={btnLoading} onClick={onSubmit}>
            确定
          </Button>
        </div>
      }
    >
      <div className="adjust-drawer">
        {list?.map((item) => (
          <div className={styles.card} key={item.skuId}>
            <div className={styles.goodsItem}>
              <GoodsItem
                orderType="sales"
                productName={item.productName}
                img={item.productMainPic}
                productPrice={item.productPrice}
                number={item.number}
                productUnit={item.productUnit}
                productSpecificationList={item.productSpecificationList}
              />
            </div>
            <div className={styles.noBatch}>
              <div>未分批数量</div>
              <div>
                {title === '修改批次'
                  ? new Big(item.noBatchNumber)
                      .plus(item.batchNumber)
                      .minus(item.batchNum || 0)
                      .toNumber()
                  : item.noBatchNumber}
              </div>
            </div>
            <div className={styles.stepper}>
              <div className={styles.gray}>
                {/* 已选数量：{title === '添加批次' ? item.batchedNumber : item.batchNumber}/
                {Number(item.noBatchNumber) + Number(item.batchedNumber)} */}
                本次分批数量
              </div>
              <Stepper
                min={0}
                max={
                  title === '修改批次'
                    ? Number(item.noBatchNumber) + Number(item.batchNumber)
                    : Number(item.noBatchNumber)
                }
                digits={item.productUnitNum}
                value={item.batchNum}
                onChange={(value) => {
                  setList(
                    list.map((it) => {
                      if (item.skuId === it.skuId) {
                        return {
                          ...it,
                          batchNum: value,
                        };
                      }
                      return it;
                    })
                  );
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </Drawer>
  );
}

EditPartial.defaultProps = {
  skuId: null,
};

export default EditPartial;
