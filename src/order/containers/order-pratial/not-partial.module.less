.noBatchDrawer {
  overflow: visible;
  transition: none !important;
  transform: none !important;

  :global {
    .ant-drawer-mask {
      background-color: rgb(0 0 0 / 45%) !important;
    }

    .ant-drawer-content {
      background: #f5f6fa;
    }

    .ant-drawer-title {
      padding-left: 0 !important;
    }

    .ant-drawer-content-wrapper {
      transition: none !important;
    }
  }
}

.deleteIcon {
  color: #999eb2;
  font-size: 24px;
  transform: rotate(45deg);
}

.card {
  padding: 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .goodsItem {
    padding-bottom: 16px;

    &:last-child {
      padding-bottom: 0;
    }
  }

  .cardFooter {
    display: flex;
    margin-top: 40px;
    justify-content: flex-end;
    align-items: baseline;
  }

  .money {
    color: #040919;
    font-size: 24px;
  }
}

.footer {
  padding: 20px;

  .btn {
    width: 100%;
  }
}
