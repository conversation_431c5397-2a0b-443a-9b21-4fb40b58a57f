// .partialDrawer {
//   :global {
//     .ant-drawer-title {
//       padding-left: 64px;
//     }
//   }
// }

.extra {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.card {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .title {
    color: #040910;
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 12px;
    font-family: PingFangSC-Medium;
  }

  .tip {
    margin-bottom: 12px;
  }

  .textBtn {
    color: #008cff;
    font-size: 12px;
    margin-left: 8px;
    cursor: pointer;
  }

  .goodsItem {
    padding-top: 16px;

    &:last-child {
      padding-bottom: 0;
    }
  }
}

.state {
  color: #ea1c26;
  font-size: 12px;
}

.total {
  color: #040919;
  font-size: 24px;
}

.text {
  color: #888b98;
}

.partialText {
  margin-bottom: 4px;
}

.header {
  display: flex;
  padding-bottom: 16px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f3f3;
  // margin-bottom: 16px;

  .cardTitle {
    color: #132948;
    font-size: 16px;
    font-weight: 600;
  }
}

.combined {
  margin-top: 40px;
  text-align: right;
}

.btnS {
  display: flex;
  margin-top: 24px;
  justify-content: flex-end;

  .btn {
    margin-left: 16px;
  }
}

.footer {
  display: flex;
  width: 100%;
  padding: 20px;
  justify-content: space-between;
  align-items: center;

  .money {
    color: #ea1c26;
  }
}

.textSize {
  color: #888b98;
  font-size: 12px;
  cursor: pointer;
}
