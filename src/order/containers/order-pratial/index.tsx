import { <PERSON><PERSON>, <PERSON><PERSON>, Price } from '@/components';
import { PropsWithChildren, useEffect, useState } from 'react';
import Empty from '@/components/empty/index';
import { Button, message, Modal } from 'antd';
import {
  BatchesResult,
  BatchProductsResult,
  getOrderPartialList,
  getOrderPreDetail,
  postOrderDeleteBatch,
} from '@/apis/order';
import BigNumber from 'big.js';
import GoodsItem from '../../components/goods-card';
import EditPartial from './edit-pratial';
import NotPartial from './not-partial';
import styles from './index.module.less';

interface GoodsPartialProps {
  visible: boolean;
  onClose: () => void;
  updateWidth: () => void;
  orderNo: number;
  nycbngOrgId: string;
}

function GoodsPartial({
  visible,
  onClose,
  updateWidth,
  orderNo,
  nycbngOrgId,
}: PropsWithChildren<GoodsPartialProps>) {
  const [showEditPartial, setShowEditPartial] = useState(false);
  const [detailBatchProducts, setDetailBatchProducts] = useState<BatchProductsResult[]>([]);
  const [listBatchProducts, setListBatchProducts] = useState<BatchProductsResult[]>([]);
  const [batches, setBatches] = useState<BatchesResult[]>([]);
  const [batchNo, setBatchNo] = useState(0);
  const [batchTotalPrice, setBatchTotalPrice] = useState('');
  const [bePartial, setBePartial] = useState<number>(0);
  const [title, setTitle] = useState('');
  const [showNotPartial, setShowNotPartial] = useState(false);

  const getBatchList = () => {
    getOrderPartialList({ orderNo, isOrderDetail: 0 }, nycbngOrgId).then((res) => {
      setBatches(res.batches);
      // let total = 0;
      // res.batches.forEach((item) => {
      //   item.batchProducts.forEach((it) => {
      //     total += Number(it.noBatchNumber) || 1 * Number(it.productPrice);
      //   });
      // });
      setBatchTotalPrice(res.batchTotalPrice);
    });
  };

  const getBatchDetailList = () => {
    getOrderPreDetail({ orderNo, isOrderDetail: 0 }, nycbngOrgId).then((res) => {
      setDetailBatchProducts(res.batchProducts);
      const total = new BigNumber(0);
      let val = 0;
      res.batchProducts.forEach((item: { noBatchNumber: string; productPrice: string }) => {
        val += total
          .plus(new BigNumber(Number(item.noBatchNumber)).times(item.productPrice))
          .toNumber();
      });
      setBePartial(val);
    });
  };

  const onEdit = (no: number, i: BatchesResult) => {
    setListBatchProducts(i.batchProducts);
    setBatchNo(no);
    setTitle('修改批次');
    setShowEditPartial(true);
  };

  const onDeleteBatch = (value: number) => {
    Modal.confirm({
      title: '提示',
      icon: '',
      centered: true,
      content: '确认删除此分批订单',
      onOk: () => {
        postOrderDeleteBatch(
          {
            batchNo: value,
          },
          nycbngOrgId || ''
        ).then(() => {
          message.success('移除成功');
          getBatchList();
          getBatchDetailList();
        });
      },
    });
  };

  const onShowMore = (val: number) => {
    setBatches(
      batches.map((item) => {
        if (item.batchNo === val) {
          return {
            ...item,
            showMore: !item.showMore,
          };
        }
        return {
          ...item,
        };
      })
    );
  };

  const toBatch = () => {
    setTitle('添加批次');
    setShowEditPartial(true);
  };

  useEffect(() => {
    if (visible) {
      getBatchList();
      getBatchDetailList();
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      visible={visible}
      title={<div style={{ paddingLeft: '64px' }}>货物分批</div>}
      className={styles.partialDrawer}
      onClose={() => {
        onClose();
        updateWidth();
      }}
      push={false}
      extra={
        batches.length > 0 && bePartial ? (
          <div
            className={styles.extra}
            role="button"
            tabIndex={0}
            onClick={() => {
              setTitle('添加批次');
              setShowEditPartial(true);
            }}
          >
            添加批次
          </div>
        ) : (
          <div style={{ width: '65px' }} />
        )
      }
    >
      {batches.length === 0 ? (
        <Empty
          type="content"
          message="暂未设置分批"
          description={
            <>
              <div>当前订单暂未设置分批，将进行整单发货</div>
              <div>如需分批发货，请点击“添加批次”</div>
            </>
          }
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
          }}
        >
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setTitle('添加批次');
              setShowEditPartial(true);
            }}
          >
            添加批次
          </Button>
        </Empty>
      ) : null}
      {batches.length ? (
        <div className={styles.card}>
          <div className={styles.title}>商品已分{batches?.length}批</div>
          {/* <div className={styles.tip}>待分批商品需分批后才能发货</div> */}
          <div className={styles.partialText}>
            <span className={styles.text}>已分批总金额：</span>
            <span>￥{batchTotalPrice}</span>
          </div>
          <div>
            <span className={styles.text}>未分批总金额：</span>
            ￥<Price value={bePartial} symbol="" format color="#040919" />
            {bePartial ? (
              <span
                className={styles.textBtn}
                role="button"
                tabIndex={0}
                onClick={() => {
                  setShowNotPartial(true);
                }}
              >
                未分批商品
                <Icon name="right" />
              </span>
            ) : null}
          </div>
        </div>
      ) : null}
      {batches.map((item) => (
        <div className={styles.card} key={item.batchNo}>
          <div className={styles.header}>
            <div className={styles.cardTitle}>批次 {item.indexNo}</div>
            <div className={styles.state}>{item.batchStatus}</div>
          </div>
          {item.batchProducts
            .filter((each, index) => (item.showMore ? index >= 0 : index >= 0 && index <= 2))
            .map((it) => (
              <div className={styles.goodsItem}>
                <GoodsItem
                  orderType="sales"
                  productName={it.productName}
                  img={it.productMainPic}
                  productPrice={it.productPrice}
                  number={it.batchNumber}
                  productUnit={it.productUnit}
                  productSpecificationList={it.productSpecificationList}
                />
              </div>
            ))}
          {item.batchProducts.length > 3 ? (
            <div
              role="button"
              tabIndex={item.batchNo}
              className={styles.textSize}
              onClick={() => {
                onShowMore(item.batchNo);
              }}
            >
              <span>全部{item.batchProducts.length}种商品</span>
              <Icon name="down" />
            </div>
          ) : null}
          <div className={styles.combined}>
            共 {item.batchProducts.length} 种货品 总金额：¥
            <Price
              value={item.batchTotalPriceStr}
              symbol=""
              format
              color="#040919"
              className={styles.total}
              separate
            />
            {/* <span className={styles.total}>{item.batchTotalPriceStr}</span> */}
          </div>
          <div className={styles.btnS}>
            {item.canDelete ? (
              <Button
                type="default"
                onClick={() => {
                  onDeleteBatch(item.batchNo);
                }}
              >
                删除
              </Button>
            ) : null}
            {item.canModify ? (
              <Button
                type="primary"
                className={styles.btn}
                onClick={() => {
                  onEdit(item.batchNo, item);
                }}
              >
                修改
              </Button>
            ) : null}
          </div>
        </div>
      ))}
      <EditPartial
        visible={showEditPartial}
        listBatchProducts={title === '添加批次' ? detailBatchProducts : listBatchProducts}
        title={title}
        orderNo={orderNo}
        batchNo={batchNo}
        nycbngOrgId={nycbngOrgId}
        getBatchList={() => {
          getBatchList();
          getBatchDetailList();
        }}
        onClose={() => {
          setShowEditPartial(false);
        }}
      />
      <NotPartial
        visible={showNotPartial}
        orderNo={orderNo}
        onClose={() => {
          setShowNotPartial(false);
        }}
        nycbngOrgId={nycbngOrgId}
        toBatch={toBatch}
      />
    </Drawer>
  );
}

export default GoodsPartial;
