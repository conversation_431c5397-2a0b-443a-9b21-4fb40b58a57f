import { BatchProductsResult, getOrderPreDetail } from '@/apis';
import { Drawer, Icon, Price } from '@/components';
import { Button } from 'antd';
import { PropsWithChildren, useEffect, useState } from 'react';
import GoodsCard from '../../components/goods-card';
import styles from './not-partial.module.less';

interface NotPartialProps {
  visible: boolean;
  orderNo: number;
  onClose: () => void;
  toBatch: () => void;
  nycbngOrgId: string;
}

function NotPartial({
  visible,
  orderNo,
  onClose,
  toBatch,
  nycbngOrgId,
}: PropsWithChildren<NotPartialProps>) {
  const [list, setList] = useState<BatchProductsResult[]>([]);
  const [totalAmount, setTotalAmount] = useState<number>(0);

  const getList = () => {
    getOrderPreDetail({ orderNo, isOrderDetail: 0 }, nycbngOrgId).then((res) => {
      setList(res.batchProducts);
      let total = 0;
      res.batchProducts.forEach((item: { noBatchNumber: string; productPrice: string }) => {
        total += Number(item.noBatchNumber) * Number(item.productPrice);
      });
      setTotalAmount(total);
    });
  };

  useEffect(() => {
    if (visible) {
      getList();
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      title="未分批商品清单"
      visible={visible}
      placement="bottom"
      getContainer={false}
      width={375}
      height={562}
      closable={false}
      onClose={onClose}
      mask={visible}
      className={styles.noBatchDrawer}
      extra={<Icon name="close" className={styles.deleteIcon} onClick={onClose} />}
      footer={
        <div className={styles.footer}>
          <Button
            type="primary"
            className={styles.btn}
            onClick={() => {
              toBatch();
              onClose();
            }}
          >
            去分批
          </Button>
        </div>
      }
    >
      <div className={styles.card}>
        {list.map((item) => (
          <div className={styles.goodsItem}>
            <GoodsCard
              orderType="sales"
              img={item.productMainPic}
              productName={item.productName}
              productSpecificationList={item.productSpecificationList}
              productPrice={item.productPrice}
              number={item.noBatchNumber}
              productUnit={item.productUnit}
            />
          </div>
        ))}
        <div className={styles.cardFooter}>
          <div>共{list.length}种商品&nbsp;总金额：</div>
          <Price value={totalAmount} className={styles.money} separate />
        </div>
      </div>
    </Drawer>
  );
}

export default NotPartial;
