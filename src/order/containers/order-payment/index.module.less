.drawer {
  :global {
    & .ant-spin-nested-loading,
    & .ant-spin-nested-loading .ant-spin-container {
      height: 100%;
    }
  }
}

.extra {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.titleTip {
  color: #888b98;
  font-size: 12px;
  padding: 16px 0;

  & .text {
    color: #008cff;
    cursor: pointer;
  }
}

.card,
.checkedCard {
  display: flex;
  margin-bottom: 20px;
  padding: 16px;
  justify-content: space-between;
  align-items: center;
  border-radius: 18px;
  border: 1px solid #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;
  cursor: pointer;

  .icon {
    color: #008cff;
  }

  .title {
    color: #040919;
    font-size: 16px;
    display: flex;
    margin-bottom: 8px;
    align-items: center;
    font-family: PingFangSC-Medium;
  }

  .context {
    color: #888b98;
  }
}

.list {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.search {
  margin-top: 5px;
  padding-left: 5px;
}

.checkedCard {
  position: relative;
  border: 1px solid #008cff;

  &::before {
    content: '';
    width: 42px;
    height: 39px;
    position: absolute;
    top: -1px;
    right: -1px;
    background-image: url('../../img/selected-mark.png');
  }
}

.footer {
  width: 100%;
  padding: 20px;

  :global {
    .ant-btn {
      width: 100%;
    }

    .ant-btn-default {
      background-color: #c6ccd8;
      color: #fff;
    }
  }
}

.endMessage {
  color: #888b98;
  font-size: 12px;
}
