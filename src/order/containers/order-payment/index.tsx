import { Drawer, Empty, Search } from '@/components';
import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { Button, Spin, message, Divider } from 'antd';
import { getPayWayList, PayWayList } from '@/apis/order';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Link } from 'react-router-dom';
import { DrawerRefType } from '@/components/drawer/drawer';
import styles from './index.module.less';
import defaultImg from '../../img/default.png';

interface OrderPaymentProps {
  visible: boolean;
  id?: number;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  getPayItem: (payItem?: PayWayList[]) => void;
}

function OrderPayment({ visible, onClose, getPayItem, id }: PropsWithChildren<OrderPaymentProps>) {
  const drawerRef = useRef<DrawerRefType>(null);
  const [payWayList, setPayWayList] = useState<PayWayList[]>([]);
  const [cateNo, setCateNo] = useState(true);
  const [loading, setLoading] = useState(false);
  const paramsData = useRef({
    pageSize: 10,
    pageNo: 1,
    searchKey: '',
  });

  // 表单改变
  const formChange = (chooseId: number) => {
    if (!drawerRef.current?.getIsChange()) {
      drawerRef.current?.setIsChange(true);
    }
    if (chooseId === id) {
      drawerRef.current?.setIsChange(false);
    }
  };

  const onPayment = (ids: number) => {
    payWayList.forEach((item) => {
      const it = item;
      it.isEdit = false;
      if (item.id === ids) {
        it.isEdit = true;
      }
    });
    setPayWayList([...payWayList]);
  };

  const onsubmit = () => {
    if (!payWayList.some((item) => item.isEdit)) {
      return message.error('请选择付款比例');
    }
    const pay = payWayList.filter((item) => item.isEdit);
    getPayItem(pay);
    onClose();
    return false;
  };

  const getPayMentList = (isSearch?: number) => {
    setLoading(true);
    getPayWayList({
      ...paramsData.current,
      searchKey: isSearch ? paramsData.current.searchKey : '',
    })
      .then((res) => {
        const list = res.list.map((item) => ({
          ...item,
          isEdit: false,
        }));
        setCateNo(
          paramsData.current.pageNo <= Math.floor(res.pagination.count / 10) &&
            res.list.length <= 10
        );
        if (isSearch) {
          setPayWayList(list);
        } else {
          setPayWayList(payWayList.concat(list));
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const loadMore = () => {
    paramsData.current.pageNo += 1;
    getPayMentList();
  };

  const onRefresh = () => {
    paramsData.current.pageNo = 1;
    getPayMentList(1);
  };

  useEffect(() => {
    if (visible) {
      if (id && payWayList.length) {
        setPayWayList(
          payWayList.map((item) => ({
            ...item,
            isEdit: item.id === id,
          }))
        );
        return;
      }
      if (!payWayList.length) getPayMentList();
    }
  }, [visible, id]); // eslint-disable-line

  return (
    <Drawer
      ref={drawerRef}
      title="选择付款方式"
      visible={visible}
      className={styles.drawer}
      onClose={() => {
        setPayWayList(
          payWayList.map((item) => ({
            ...item,
            isEdit: false,
          }))
        );
        onClose();
      }}
      extra={
        payWayList.length ? (
          <Link
            to="/admin/pay/way/operate?type=add&source=order"
            target="_blank"
            className={styles.extra}
          >
            新建
          </Link>
        ) : null
      }
      footer={
        payWayList.length ? (
          <div className={styles.footer}>
            <Button
              type={payWayList.some((item) => item.isEdit) ? 'primary' : 'default'}
              onClick={onsubmit}
            >
              确定
            </Button>
          </div>
        ) : null
      }
    >
      <Spin tip="Loading..." spinning={loading}>
        <div
          className={styles.list}
          id="paymentList"
          // style={{
          //   height: `${document.querySelector('.ant-drawer-body')?.scrollHeight}px`,
          // }}
        >
          <div className={styles.search}>
            <Search
              placeholder="搜索付款方式名称"
              value={paramsData.current.searchKey}
              onSearch={(value) => {
                paramsData.current = {
                  ...paramsData.current,
                  searchKey: value,
                  pageNo: 1,
                };
                getPayMentList(1);
              }}
            />
          </div>
          <div className={styles.titleTip}>
            新建付款方式后，请
            <span className={styles.text} role="button" tabIndex={0} onClick={onRefresh}>
              点此刷新
            </span>
            数据
          </div>
          {payWayList.length > 0 ? (
            <InfiniteScroll
              dataLength={payWayList.length}
              hasMore={cateNo}
              loader={
                <div className="text-center">
                  <Spin tip="加载中..." />
                </div>
              }
              endMessage={
                <div className={styles.divider}>
                  <Divider plain>
                    <span className={styles.endMessage}>加载到底了</span>
                  </Divider>
                </div>
              }
              next={loadMore}
              scrollableTarget="paymentList"
            >
              {payWayList.map((item) => (
                <div
                  className={item.isEdit ? styles.checkedCard : styles.card}
                  role="button"
                  tabIndex={0}
                  key={item.id}
                  onClick={() => {
                    onPayment(item.id);
                    formChange(item.id);
                  }}
                >
                  <div>
                    <div className={styles.title}>
                      <span>{item.name}</span>
                      {item.isDefault ? (
                        <img src={defaultImg} alt="" style={{ marginLeft: '8px' }} />
                      ) : null}
                    </div>
                    <div className={styles.context}>{item.payWayDesc}</div>
                  </div>
                  {/* {item.isEdit ? <Icon name="tick" className={styles.icon} /> : null} */}
                </div>
              ))}
            </InfiniteScroll>
          ) : (
            <Empty
              type="content"
              message="暂无付款方式"
              style={{
                position: 'fixed',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
              }}
            >
              {paramsData.current.searchKey ? null : (
                <Link to="/admin/pay/way/operate?type=add" target="_blank">
                  <Button type="primary" size="small">
                    去新增
                  </Button>
                </Link>
              )}
            </Empty>
          )}
        </div>
      </Spin>
    </Drawer>
  );
}

OrderPayment.defaultProps = {
  id: 0,
};

export default OrderPayment;
