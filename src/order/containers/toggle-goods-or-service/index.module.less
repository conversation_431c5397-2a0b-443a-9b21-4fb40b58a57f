@import 'styles/mixins/mixins';

.wrapper {
  display: inline-block;
  max-width: 100%;
  cursor: pointer;
}

.inner {
  display: inline-flex;
  max-width: 100%;
  line-height: 16px;
  padding: 4px 8px;
  border-radius: @border-radius-xxs;
  background-color: @background-colors[normal];
}

.metaText,
.serviceText,
.icon {
  color: @text-color-secondary;
  display: inline-block;
  vertical-align: top;
}

.serviceText,
.metaText {
  font-size: @font-size-sm;
}

.metaText {
  .flex-column();
  .text-overflow();
}

.icon {
  font-size: @font-size-lg;
  margin-left: 4px;
}

.content {
  width: 433px;
  max-width: 100%;

  :global {
    .ant-popover-arrow {
      display: none;
    }

    .ant-popover-inner {
      margin-top: -16px;
    }
  }
}

.form {
  display: inline-block;
  width: 100%;
  min-height: 100px;
}

.info {
  display: flex;
}

.infoBody {
  .flex-column();

  overflow: hidden;
}

.image {
  display: flex;
  width: 110px;
  height: 110px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: @border-radius-xs;
}
