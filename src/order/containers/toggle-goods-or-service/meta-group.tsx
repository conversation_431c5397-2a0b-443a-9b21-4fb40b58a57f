import { Form, FormItemProps, Radio, Tooltip } from 'antd';
import styles from './meta-group.module.less';

export interface MetaGroupProps extends Omit<FormItemProps, 'noStyle'> {
  group: {
    name: string;
    value: { id: number | string; title?: string; tooltip?: string }[];
  };
  disabled?: number[] | string[] | null;
}

function MetaGroup({ group, disabled, ...props }: MetaGroupProps) {
  return (
    <div className={styles.row}>
      <div className={styles.label}>{group.name}</div>
      <Form.Item {...props} noStyle>
        <Radio.Group className={styles.content}>
          {group.value.map((item) => {
            const radio = (
              <Radio
                key={item.id}
                value={item.id}
                disabled={!!disabled && (disabled as Array<string | number>).includes(item.id)}
                className={styles.item}
              >
                {item.title || item.id}
              </Radio>
            );
            return item.tooltip ? (
              <Tooltip key={item.id} title={item.tooltip}>
                <span>{radio}</span>
              </Tooltip>
            ) : (
              radio
            );
          })}
        </Radio.Group>
      </Form.Item>
    </div>
  );
}

MetaGroup.defaultProps = {
  disabled: null,
};

export default MetaGroup;
