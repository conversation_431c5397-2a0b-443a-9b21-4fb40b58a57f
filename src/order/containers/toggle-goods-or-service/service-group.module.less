@import 'styles/mixins/mixins';

.row {
  display: flex;
  margin-bottom: 8px;
}

.label {
  .flex-row(77px);
  .text-overflow();

  color: @text-color-secondary;
  font-size: @font-size-sm;
  line-height: 17px;
  padding: 4px 4px 4px 12px;
  word-break: break-all;
}

.content {
  .flex-column();
}

.item {
  margin-bottom: 8px;
  margin-left: 0 !important;
  padding-right: 4px;
  padding-left: 4px;

  :global {
    .ant-radio {
      display: none;

      + span {
        color: @text-color-secondary;
        font-size: @font-size-sm;
        display: flex;
        align-items: center;
        max-width: 308px;
        line-height: 16px;
        padding: 3px 8px;
        border: 1px solid @color18;
        border-radius: @border-radius-xxs;
        text-align: left;
      }
    }

    .ant-radio-checked {
      + span {
        color: @red;
        border-color: @red;
      }
    }
  }
}

.price {
  display: inline-block;
  white-space: nowrap;

  &::before {
    content: '';
    display: inline-block;
    height: 16px;
    margin-right: 8px;
    margin-left: 8px;
    border-left: 1px solid #b1b3be;
    vertical-align: top;
  }
}
