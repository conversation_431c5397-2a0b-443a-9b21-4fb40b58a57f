import { HTMLAttributes, ReactNode } from 'react';
import classNames from 'classnames';
import styles from './index.module.less';

export interface MetaProps extends HTMLAttributes<HTMLDivElement> {
  meta?: ReactNode;
}

function MetaInner({ meta, children, className, ...props }: MetaProps) {
  const classes = classNames(className, styles.inner);
  return (
    <div {...props} className={classes}>
      <span className={styles.metaText}>{meta}</span>
      {children}
    </div>
  );
}

MetaInner.defaultProps = {
  meta: null,
};

export default MetaInner;
