@import 'styles/mixins/mixins';

.row {
  display: flex;

  & + & {
    margin-top: 26px;
  }
}

.content {
  .flex-column();

  overflow: hidden;
}

.label {
  .flex-column(76px);

  padding-right: 8px;
  padding-left: 12px;
  text-align: justify;
  text-align-last: justify;
  word-break: break-all;
}

.value {
  flex: 1;
}

.group {
  border-radius: 10px;
  padding: 8px;
  background: #f5f6fa;
  padding-bottom: 0;
}

.item {
  margin-right: 0;
  margin-bottom: 4px;
  padding-right: 3px;
  padding-left: 3px;

  :global {
    .ant-radio {
      display: none;

      + span {
        .text-overflow();

        font-size: @font-size-sm;
        min-width: 64px;
        max-width: 200px;
        line-height: 1;
        padding: 3px 8px;
        border: 1px solid @color18;
        border-radius: @border-radius-xxs;
        text-align: center;
      }
    }

    .ant-radio-checked {
      + span {
        border-color: #ec2d56;
        background: url('../../assets/img/meta-check.png') no-repeat right bottom / 14px;
      }
    }
  }
}
