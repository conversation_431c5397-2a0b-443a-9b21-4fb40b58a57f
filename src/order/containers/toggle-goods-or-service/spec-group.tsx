import { useState, useEffect } from 'react';
import { Form, FormInstance, FormItemProps, Radio, Tooltip } from 'antd';
import styles from './meta-group.module.less';

type GoodsChoiceItemValueOption = {
  title: string;
  id: number | string;
  disabled?: boolean;
  checked?: boolean;
  groupId?: number;
  tooltip?: string;
  childrenList?: GoodsChoiceItemValueOption[];
};
export interface SpecGroupProps extends Omit<FormItemProps, 'noStyle'> {
  group: {
    name: string;
    value: GoodsChoiceItemValueOption[];
  };
  disabled?: number[] | string[] | null;
  checkedGroupIds?: number[];
  // checkedIds: number[];
  specIds: number[];
  currId: number | null;
  // currGroupId: number | null;
  getForm: () => FormInstance<any>;
  onFormValueChangeFn: MultipleParamsFn<[updateValue: any, values: any]>;
}

function SpecGroup({
  group,
  disabled,
  checkedGroupIds,
  // checkedIds,
  specIds,
  currId,
  // currGroupId,
  getForm,
  onFormValueChangeFn,
  ...props
}: SpecGroupProps) {
  const [currentId, setCurrentId] = useState<number | null>(null);
  const [currentGroupId, setCurrentGroupId] = useState<number | null>(null);
  const [groupIds, setGroupIds] = useState<number[]>([]);
  const [isGroup] = useState(group.value.map((item) => item.id).filter((item) => !item).length > 0);

  const onRadioGroupChange = (val: number) => {
    const form = getForm();
    // @ts-ignore
    const metaIndex = props?.name[1];
    const meta = form.getFieldValue('meta');
    meta[metaIndex] = val;
    form.setFieldsValue({ meta: [...meta] });
    onFormValueChangeFn([...meta], form.getFieldsValue());
  };

  useEffect(() => {
    setCurrentId(currId);
  }, [currId]);

  useEffect(() => {
    const allGroupIds = group.value.map((item) => item.groupId).filter((item) => item);
    if (!allGroupIds.length) {
      setCurrentGroupId(null);
      return;
    }
    for (let x = 0; x < group.value.length; x += 1) {
      const valItem = group.value[x];
      if (valItem?.childrenList?.length) {
        for (let y = 0; y < (valItem?.childrenList?.length || 0); y += 1) {
          const child = valItem.childrenList[y];
          if (child.id === currId) {
            setCurrentGroupId(child.groupId || -1);
            break;
          }
        }
      }
    }
  }, [group, currId]);

  // useEffect(() => {
  //   setIsGroup(group.value.map((item) => item.id).filter((item) => !item).length > 0);
  //   console.log('setIsGroup');
  // }, [group.value]);
  // useEffect(() => {
  //   setCurrentGroupId(currGroupId);
  // }, [currGroupId]);

  useEffect(() => {
    setGroupIds([...(checkedGroupIds || [])]);
  }, [checkedGroupIds]);

  return (
    <div className={styles.row}>
      <div className={styles.label}>{group.name}</div>
      <div className={styles.value}>
        <Form.Item {...props} noStyle style={{ display: 'none' }}>
          <Radio.Group className={styles.content}>
            {specIds.map((item) => (
              <Radio key={item} value={item} className={styles.item} />
            ))}
          </Radio.Group>
        </Form.Item>
        <Radio.Group
          className={styles.content}
          value={isGroup ? currentGroupId : currentId}
          onChange={(e) => {
            const val = e.target.value;
            if (isGroup) {
              if (currentGroupId) {
                const i = groupIds.indexOf(currentGroupId);
                if (i !== -1) {
                  groupIds.splice(i, 1);
                }
              }
              groupIds.push(val);
              setCurrentGroupId(val);
              setGroupIds([...groupIds]);
            } else {
              setCurrentId(val);
              onRadioGroupChange(val);
            }
          }}
        >
          {group.value.map((item) => {
            const radio = item.id ? (
              <Radio
                key={item.id}
                value={item.id}
                disabled={!!disabled && (disabled as Array<string | number>).includes(item.id)}
                className={styles.item}
              >
                {item.title || item.id}
              </Radio>
            ) : (
              <Radio
                key={item.groupId}
                value={item.groupId}
                disabled={false}
                className={styles.item}
              >
                {item.title || item.id}
              </Radio>
            );
            return item.tooltip ? (
              <Tooltip key={item.id} title={item.tooltip}>
                <span>
                  {radio}
                  {item.tooltip}
                </span>
              </Tooltip>
            ) : (
              radio
            );
          })}
        </Radio.Group>
        {isGroup && (
          <div className={styles.group}>
            <Radio.Group
              className={styles.content}
              value={currentId}
              onChange={(e) => {
                const val = e.target.value;
                setCurrentId(val);
                onRadioGroupChange(val);
              }}
            >
              {group.value.map(
                (item) =>
                  item?.groupId &&
                  groupIds?.includes(item.groupId) &&
                  item.childrenList?.map((childItem) => {
                    const radio = (
                      <Radio
                        key={childItem.id}
                        value={childItem.id}
                        disabled={
                          childItem.id
                            ? !!disabled &&
                              (disabled as Array<string | number>).includes(childItem.id)
                            : false
                        }
                        className={styles.item}
                      >
                        {childItem.title || childItem.id}
                      </Radio>
                    );
                    return childItem.tooltip ? (
                      <Tooltip key={childItem.id} title={childItem.tooltip}>
                        <span>{radio}</span>
                      </Tooltip>
                    ) : (
                      radio
                    );
                  })
              )}
            </Radio.Group>
          </div>
        )}
      </div>
    </div>
  );
}

SpecGroup.defaultProps = {
  disabled: null,
  checkedGroupIds: [],
};

export default SpecGroup;
