import { H<PERSON><PERSON>ttributes, Mouse<PERSON>vent, ReactNode, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Form, FormProps, Popover, Spin } from 'antd';
import DownOutlined from '@/components/icon/down';
import { useMemoizedFn, useRequest } from 'ahooks';
import {
  CartGoods,
  getGoodsDetail,
  GetGoodsDetailParams,
  getPackageGoodsDetail,
  GetPackageGoodsDetailParams,
  GetPackageGoodsDetailResult,
  GoodsDetailResult,
  GoodsSkuItem,
} from '@/apis';
import classNames from 'classnames';
import { getGoodsServices, getUnitName } from '../../utils/cart';
import useGoods from '../../hooks/use-goods';
import MetaInner from './meta-inner';
import MetaGroup, { MetaGroupProps } from './meta-group';
import SpecGroup from './spec-group';
import ServiceGroup from './service-group';
import styles from './index.module.less';

export interface ToggleGoodsOrServiceProps extends HTMLAttributes<HTMLElement> {
  item: Omit<CartGoods, 'packageCartProductVOS'>;
  suffix?: ReactNode;
  updateSku?: MultipleParamsFn<
    [
      data: GoodsDetailResult,
      item: Omit<CartGoods, 'packageCartProductVOS'>,
      values: { payWayNo: number; selectUnit: string }
    ],
    Promise<unknown> | void
  >;
}

function ToggleGoodsOrService({
  item,
  suffix,
  updateSku,
  children,
  className,
  ...props
}: ToggleGoodsOrServiceProps) {
  const [form] = Form.useForm();
  const { data, loading, run } = useRequest(
    (params: GetGoodsDetailParams): Promise<GoodsDetailResult | GetPackageGoodsDetailResult> => {
      const param = { ...params };
      if (item.packageId && item.groupId) {
        param.groupId = item.groupId;
        param.packageId = item.packageId;
      }
      return item.packageId
        ? getPackageGoodsDetail(param as GetPackageGoodsDetailParams).then((res) => ({
            ...res,
            allSkuList: res.allStandardVOList,
            shopSkuId: res.id,
            minimum: res.quantity,
          }))
        : getGoodsDetail(param as GetGoodsDetailParams);
    },
    { manual: true }
  );
  const [goodsValue, disabledList, checkedGroupIds] = useGoods(data);

  const serviceRef = useRef<number[] | null>(null);
  const [visible, setVisible] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [isAllowSwitch, setIsAllowSwitch] = useState(true);

  const { unitGroup, payWayGroup } = useMemo(() => {
    let tmpUnit: MetaGroupProps['group'] | null = null;
    let tmpPayWay: MetaGroupProps['group'] | null = null;
    if (data) {
      const { unitList, showUnitFlag } = data as GoodsDetailResult;
      if (showUnitFlag) {
        tmpUnit = {
          name: '单位',
          value: unitList && unitList.length > 0 ? unitList : [{ id: '', title: data.unit }],
        };
      }

      if (data.payWayNo && data.payWayNo.length > 0) {
        tmpPayWay = {
          name: '付款方式',
          value: data.payWayNo.map((payWayItem) => ({
            id: payWayItem.id,
            title: payWayItem.title,
            tooltip: payWayItem.payWayDesc,
          })),
        };
      }
    }
    return { unitGroup: tmpUnit, payWayGroup: tmpPayWay };
  }, [data]);
  const meta = useMemo(
    () =>
      (item.productSpecificationList || [])
        .map((metaItem) => `${metaItem.name}：${metaItem.value}`)
        .join('；'),
    [item.productSpecificationList]
  );
  const onClose = useMemoizedFn((e?: MouseEvent<HTMLElement>) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setVisible(false);
    serviceRef.current = null;
  });

  const onFormValueChangeFn = (updateValue: any, values: any) => {
    if (!data || updateValue.service) {
      return;
    }
    const metaToSkuList =
      (data as GoodsDetailResult).enableList || (data as GoodsDetailResult).allSkuList;
    if (metaToSkuList && values.meta) {
      let metaCase = values.meta.join('-');
      const selectUnit = getUnitName(values.unit, (data as GoodsDetailResult).unitList);
      let allowItem: GoodsSkuItem | undefined;
      if (selectUnit && selectUnit !== data!.unit) {
        metaCase += `-${values.unit}`;
        allowItem = (data as GoodsDetailResult).enableList.find(
          (skuItem) => skuItem.standardCase === metaCase
        );
      } else {
        allowItem = (data as GoodsDetailResult).allSkuList.find(
          (skuItem) => skuItem.standardCase === metaCase
        );
      }
      setIsAllowSwitch(updateValue.payWayNo ? true : Boolean(allowItem?.shopSkuId));
      if (
        allowItem &&
        (updateValue.unit ||
          allowItem.shopSkuId !== (data as GoodsDetailResult).shopSkuId ||
          updateValue.payWayNo)
      ) {
        const payWayNoObj =
          allowItem.shopSkuId !== (data as GoodsDetailResult).shopSkuId
            ? {}
            : { payWayNo: values.payWayNo };
        const params: GetGoodsDetailParams = {
          shopSkuId: allowItem.shopSkuId,
          ...payWayNoObj,
        };
        if (params.shopSkuId === (data as GoodsDetailResult).shopSkuId) {
          params.selectUnit = selectUnit;
        }
        serviceRef.current = values.service;
        run(params);
      }
    }
  };

  const onFormChange: FormProps['onValuesChange'] = useMemoizedFn((updateValue, values) => {
    onFormValueChangeFn(updateValue, values);
  });

  const onFinish: FormProps['onFinish'] = useMemoizedFn((values) => {
    if (!data || !updateSku) {
      return;
    }
    const promise = updateSku(data as GoodsDetailResult, item, values);
    if (promise) {
      setSubmitting(true);
      promise
        .then(() => {
          onClose();
        })
        .finally(() => {
          setSubmitting(false);
        });
    } else {
      onClose();
    }
  });

  const getForm = () => form;

  useEffect(() => {
    if (visible && (!data || (data as GoodsDetailResult).shopSkuId !== item.shopSkuId)) {
      run({
        shopSkuId: item.shopSkuId,
        selectUnit: item.productUnit,
        ...(item?.payWayNo ? { payWayNo: String(item.payWayNo) } : {}),
      });
    }
  }, [item.shopSkuId, visible]); // eslint-disable-line

  useEffect(() => {
    if (data && goodsValue && visible) {
      const serviceGroup = data.serviceStandardList?.map((group) =>
        group.standardDetailList.map((service) => service.id)
      );
      form.setFieldsValue({
        ...goodsValue,
        service:
          serviceRef.current ||
          getGoodsServices(
            item as CartGoods,
            (standard) => standard.shopSkuId,
            (service) => {
              if (serviceGroup) {
                return serviceGroup.findIndex((group) => group.includes(service.shopSkuId));
              }
              return -1;
            }
          ),
      });
    }
  }, [goodsValue, form, item, visible, data]);

  const content = (
    <Spin spinning={loading}>
      <Form form={form} className={styles.form} onValuesChange={onFormChange} onFinish={onFinish}>
        {data && (
          <>
            <div className={styles.info}>
              <div className={`pr-3 pt-1 ${styles.infoBody}`}>
                {data.choiceList.map((choice, index) =>
                  // 判断是否是规组的数据
                  checkedGroupIds.length ? (
                    <SpecGroup
                      key={choice.name}
                      name={['meta', index]}
                      specIds={choice.value
                        .map(
                          (valueItem) =>
                            Number(valueItem.id) ||
                            (valueItem.childrenList || []).map((child) => Number(child.id))
                        )
                        .flat()}
                      // checkedIds={goodsValue?.meta.map((metaItem) => Number(metaItem)) || []}
                      currId={goodsValue?.meta.map((metaItem) => Number(metaItem))[index] || null}
                      disabled={disabledList[index]}
                      group={choice}
                      checkedGroupIds={checkedGroupIds}
                      getForm={getForm}
                      onFormValueChangeFn={onFormValueChangeFn}
                    />
                  ) : (
                    <MetaGroup
                      key={choice.name}
                      name={['meta', index]}
                      disabled={disabledList[index]}
                      group={choice}
                    />
                  )
                )}
                {unitGroup && <MetaGroup name="unit" group={unitGroup} />}
                {payWayGroup && <MetaGroup name="payWayNo" group={payWayGroup} />}
              </div>
              <div className={styles.image}>
                <img src={data.imageList[0]} alt="" />
              </div>
            </div>
            {data.serviceStandardList && data.serviceStandardList.length > 0 && (
              <>
                <h4 className="py-5 px-3">商品服务</h4>
                {data.serviceStandardList.map((service, index) => (
                  <ServiceGroup
                    key={service.groupName}
                    name={['service', index]}
                    label={service.groupName}
                    options={service.standardDetailList}
                  />
                ))}
              </>
            )}
            <div className="pt-3 pb-5 px-4 text-center">
              <Button size="small" shape="round" className="mr-2" onClick={onClose}>
                取消
              </Button>
              <Button
                size="small"
                shape="round"
                type="primary"
                htmlType="submit"
                loading={submitting}
                disabled={!isAllowSwitch}
              >
                确定
              </Button>
            </div>
          </>
        )}
      </Form>
    </Spin>
  );

  let inner = children;
  if (!inner) {
    inner = (
      <MetaInner meta={meta}>
        {suffix && <span className={styles.serviceText}>{suffix}</span>}
        <DownOutlined className={styles.icon} />
      </MetaInner>
    );
  }

  const classes = classNames(className, styles.wrapper);
  return (
    <Popover
      content={content}
      placement="bottomLeft"
      trigger="click"
      visible={visible}
      overlayClassName={styles.content}
      onVisibleChange={setVisible}
    >
      <span {...props} className={classes}>
        {inner}
      </span>
    </Popover>
  );
}

ToggleGoodsOrService.defaultProps = {
  suffix: undefined,
  updateSku: undefined,
};

export default ToggleGoodsOrService;
