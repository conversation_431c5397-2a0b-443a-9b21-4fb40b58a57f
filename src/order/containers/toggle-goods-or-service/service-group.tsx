import { Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>actNode, useRef } from 'react';
import { Form, FormItemProps, Radio } from 'antd';
import { useMemoizedFn, useMount } from 'ahooks';
import styles from './service-group.module.less';

export interface ServiceGroupProps extends Omit<FormItemProps, 'noStyle'> {
  label: ReactNode;
  options: { id: number; name: string; marketPrice: number }[];
}

function ServiceGroup({ label, options, ...props }: ServiceGroupProps) {
  const form = Form.useFormInstance();
  const emptyRef = useRef<HTMLElement>(null);
  const currentRef = useRef<string | number>();
  const onClick: MouseEventHandler<HTMLInputElement> = useMemoizedFn((e) => {
    const { value } = e.target as HTMLInputElement;
    // eslint-disable-next-line eqeqeq
    if (currentRef.current == value) {
      // @ts-ignore
      emptyRef.current?.input.click();
    }
    currentRef.current = value;
  });

  useMount(() => {
    const { name } = props;
    if (form && name) {
      setTimeout(() => {
        currentRef.current = form.getFieldValue(name);
      });
    }
  });

  return (
    <div className={styles.row}>
      <div className={styles.label}>{label}</div>
      <Form.Item {...props} noStyle>
        <Radio.Group className={styles.content}>
          <Radio ref={emptyRef} value="" className="hidden" />
          {options.map((option) => (
            <Radio key={option.id} value={option.id} className={styles.item} onClick={onClick}>
              <span className={styles.name}>{option.name}</span>
              <span className={styles.price}>¥ {option.marketPrice}</span>
            </Radio>
          ))}
        </Radio.Group>
      </Form.Item>
    </div>
  );
}

export default ServiceGroup;
