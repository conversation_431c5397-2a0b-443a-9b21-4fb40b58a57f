.pay {
  :global {
    .ant-collapse-borderless {
      border-radius: 18px;
      background: none;
    }

    .ant-select.ant-select-single.ant-select-show-arrow {
      width: 100%;
    }

    .ant-form-item-control-input-content {
      border-bottom: 1px solid #f3f3f3;
    }

    .ant-collapse-item {
      width: 343px;
      margin-bottom: 20px;
      border-bottom: none;
      background: #fff;
      border-radius: 18px !important;
      box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
    }

    .ant-picker.ant-picker-borderless {
      width: 100%;
    }

    .ant-collapse > .ant-collapse-item > .ant-collapse-header {
      line-height: 40px;
    }

    .ant-collapse-header {
      color: #040919;
      font-size: 16px;
      font-weight: 500;
      height: 64px;
    }

    .ant-input:placeholder-shown,
    .ant-collapse-borderless > .ant-collapse-item:last-child .ant-collapse-header,
    .ant-picker-input > input:placeholder-shown {
      font-size: 16px;
    }

    .ant-collapse-content > .ant-collapse-content-box {
      padding: 20px;
    }

    .ant-input,
    .ant-picker {
      padding: 6px 0 16px;
    }
  }

  .font {
    color: #b1b3be;
    font-size: 16px;
    padding: 6px 0 16px;
  }

  .red {
    color: #ea1c26;
  }

  .gray {
    color: #888b98;
    padding-bottom: 12px;
  }

  .upload {
    margin-bottom: 16px;
  }

  .noteTitle {
    color: #888b98;
    font-size: 12px;
  }
}

.textarea {
  height: 84px !important;
  margin-top: 12px;
}

.footer {
  padding: 20px;

  .btn {
    width: 100%;
  }
}

.card {
  padding: 0 20px 16px;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;

  .goodsItem {
    padding-bottom: 20px;
    border-bottom: 1px solid #f3f3f3;
  }
}

.cardFooter {
  display: flex;
  margin-top: 16px;
  justify-content: space-between;
  align-items: center;

  :global {
    .adm-stepper {
      width: 135px;
      border-radius: 10px;
    }

    .adm-stepper-middle {
      border: none;
    }

    .adm-button:not(.adm-button-default).adm-button-fill-none {
      color: black;
    }
  }

  .text {
    font-family: PingFangSC-Regular;
    color: #888b98;
  }
}

.warehouse {
  padding-bottom: 16px;
}

.footer {
  display: flex;
  width: 100%;
  padding: 20px;
  justify-content: space-between;
  align-items: center;

  .footerText {
    font-size: 12px;
  }
}

.noBorder {
  :global {
    .ant-form-item-control-input-content {
      border-bottom: none;
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-input,
    .ant-picker {
      padding: 6px 0;
    }
  }
}
