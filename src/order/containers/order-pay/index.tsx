import { Drawer } from '@/components';
import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { Button, Collapse, message, Select, Spin } from 'antd';
import {
  getProductionGoodsList,
  OrderSupplementParams,
  postOrderSupplement,
  postSupplementOrderPoint,
  ProductionGoodsListResult,
  stockWarehouseList,
  WarehouseListResult,
} from '@/apis';
import { PayInfoListResult } from '@/apis/order/post-supplement-order-point';
import { Stepper } from 'antd-mobile';
import { DrawerRefType } from '@/components/drawer/drawer';
import styles from './index.module.less';
import FormCard from './form-card';
import GoodsItem from '../../components/goods-card/index';
// OrderSupplementParams, postOrderSupplement,
interface OrderPayProps {
  visible: boolean;
  onClose: () => void;
  payFinish: () => void;
  flowId?: number;
  orderNo?: number | undefined;
  batchNoId?: number | undefined;
  actionCodeId?: number | undefined;
  distributionCompanyId?: number;
  nycbngOrgId: string;
}

interface payInfoResult {
  name: string;
  payNo: string;
  payPriceStr: string;
  payeeCompanyName: string;
  voucherUrls: Array<string>;
  voucherNo: string;
  remark: string;
}

function OrderPay({
  visible,
  onClose,
  flowId,
  orderNo,
  payFinish,
  batchNoId,
  actionCodeId,
  distributionCompanyId,
  nycbngOrgId,
}: PropsWithChildren<OrderPayProps>) {
  const { Panel } = Collapse;
  const drawerRef = useRef<DrawerRefType>(null);
  const [payList, setPayList] = useState<payInfoResult[]>([]);
  const [initPayList, setInitPayList] = useState<payInfoResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [payOrPayee, setPayOrPayee] = useState(0);
  const [cacheSubmitFunS, setCacheSubmitFunS] = useState(
    [] as unknown as Array<() => payInfoResult>
  );
  const [goodsList, setGoodsList] = useState<ProductionGoodsListResult[]>([]);
  const [warehouseList, setWarehouseList] = useState<WarehouseListResult[]>([]);

  const data = useRef({
    flowSerialNo: 0,
    warehouseId: 0,
  });

  const params = useRef({
    pageSize: 10,
    pageNo: 1,
    total: 0,
  });

  // 表单改变
  const formChange = () => {
    if (!drawerRef.current?.getIsChange()) {
      drawerRef.current?.setIsChange(true);
    }
  };

  const getProductionGoods = () => {
    if (orderNo && actionCodeId === 2020) {
      setLoading(true);
      getProductionGoodsList({ orderNo, batchNo: batchNoId }, '').then((res) => {
        setGoodsList(
          res.list.map((item) => ({
            ...item,
            goodsNumber: 0,
          }))
        );
        setLoading(false);
      });
    }
  };

  const addOrder = () => {
    const cacheSubmit = cacheSubmitFunS.map((item) => item());
    setLoading(false);
    if (!cacheSubmit.every((e) => e.voucherNo) && payOrPayee === 1) {
      message.warning('请填写凭证单号');
      setLoading(false);
      return;
    }
    if (!cacheSubmit.every((e) => e.voucherUrls.length) && payOrPayee === 1) {
      message.warning('请选择凭证附件');
      setLoading(false);
      return;
    }
    if (actionCodeId === 2020 && !goodsList.every((e) => e.goodsNumber)) {
      message.warning('生产数量不能为0');
      setLoading(false);
      return;
    }
    if ((actionCodeId === 1005 || actionCodeId === 1111) && !data.current.warehouseId) {
      message.warning('请选择仓库');
      setLoading(false);
      return;
    }
    const list: OrderSupplementParams = {
      orderNo,
      paymentType: payOrPayee === 1 ? 1 : 0,
      flowSerialNo: flowId,
      batchNo: batchNoId,
      suppleEnterPayParamList: cacheSubmit.map((m) => {
        if (payOrPayee === 1) {
          return {
            payNo: m.payNo,
            voucherNo: m.voucherNo,
            voucherUrls: m.voucherUrls,
            remark: m.remark,
          };
        }
        return {
          payNo: m.payNo,
          remark: m.remark,
        };
      }),
      saveProductionDetailList:
        actionCodeId === 2020
          ? goodsList.map((item) => ({
              productDetailNo: item.productDetailNo,
              number: item.goodsNumber ? item.goodsNumber : 0,
            }))
          : [],
    };
    if (actionCodeId === 1005 || actionCodeId === 1111) {
      list.warehouseId = data.current.warehouseId;
    }
    postOrderSupplement(list, nycbngOrgId)
      .then(() => {
        setLoading(false);
        payFinish();
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getSubmitFun = (fun: () => payInfoResult) => {
    setCacheSubmitFunS((funS: Array<() => payInfoResult>) => [...funS, fun]);
  };

  const getWarehouseList = () => {
    stockWarehouseList({
      pageNo: params.current.pageNo,
      pageSize: params.current.pageSize,
      companyId: distributionCompanyId,
    }).then((res) => {
      params.current.pageNo += 1;
      params.current.total = res.pagination.total;
      setWarehouseList(warehouseList.concat(res.list));
    });
  };

  const getSupplement = () => {
    const dataParams = {
      orderNo,
      flowSerialNo: flowId,
      batchNo: batchNoId || null,
    };
    postSupplementOrderPoint(dataParams, nycbngOrgId)
      .then((res) => {
        const list = res.payInfoList.map((m: PayInfoListResult) => ({
          ...m,
          voucherUrls: [],
          voucherNo: '',
          remark: '',
        }));
        setPayList(list);
        setInitPayList(list);
        setPayOrPayee(res.payOrPayee);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    setWarehouseList([]);
    if (visible) {
      setLoading(true);
      setTimeout(() => {
        getSupplement();
        getProductionGoods();
        getWarehouseList();
        setCacheSubmitFunS([]);
      }, 500);
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      ref={drawerRef}
      className={styles.pay}
      title={`订单${payOrPayee === 1 ? '支付' : '收款'}`}
      visible={visible}
      onClose={() => {
        setCacheSubmitFunS([]);
        setPayList(initPayList);
        onClose();
      }}
      footer={
        <div className={styles.footer}>
          <Button
            type="primary"
            className={styles.btn}
            role="button"
            tabIndex={0}
            onClick={() => {
              setLoading(true);
              setTimeout(() => {
                addOrder();
              }, 500);
            }}
          >
            确定
          </Button>
        </div>
      }
    >
      <Spin tip="Loading..." spinning={loading}>
        <Collapse
          expandIconPosition="right"
          bordered={false}
          defaultActiveKey={[0, 1, 2, 3, 4, 5, 6, '生产排期', '发货/验收']}
        >
          {payList.map((item, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <Panel header={item.name} key={index}>
              <FormCard
                payItem={item}
                payOrPayee={payOrPayee}
                formChange={formChange}
                pullSubmitFun={getSubmitFun}
              />
            </Panel>
          ))}
          {actionCodeId === 2020 && (
            <Panel header="生产排期" key="生产排期">
              {goodsList.map((item) => (
                <div className={styles.card} style={{ boxShadow: '0' }} key={item.productDetailNo}>
                  <div className={styles.goodsItem}>
                    <GoodsItem
                      orderType="inventory"
                      img={item.productMainPic}
                      productName={item.productName}
                      productUnit={item.productUnit}
                      stock={item.stockNumber}
                      productSpecificationList={item.productSpecificationList}
                    />
                  </div>
                  <div className={styles.cardFooter}>
                    <div className={styles.text}>生产数量</div>
                    <Stepper
                      min={0}
                      max={999999999}
                      onChange={(value) => {
                        const it = item;
                        it.goodsNumber = value;
                        formChange();
                      }}
                    />
                  </div>
                </div>
              ))}
            </Panel>
          )}
          {(actionCodeId === 1005 || actionCodeId === 1111) && (
            <Panel header="发货/验收" key="发货/验收">
              <div>
                <div className={styles.warehouse}>仓库</div>
                <Select
                  options={warehouseList.map((item) => ({
                    label: item.warehouseName,
                    value: item.id,
                  }))}
                  onChange={(id) => {
                    data.current.warehouseId = id;
                    formChange();
                  }}
                  onPopupScroll={() => {
                    if (params.current.pageNo > params.current.total) return;
                    getWarehouseList();
                  }}
                />
              </div>
            </Panel>
          )}
        </Collapse>
      </Spin>
    </Drawer>
  );
}

OrderPay.defaultProps = {
  flowId: 0,
  orderNo: 0,
  batchNoId: null,
  actionCodeId: null,
  distributionCompanyId: null,
};

export default OrderPay;
