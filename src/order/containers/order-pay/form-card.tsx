import { SimpleUploadInstance, Upload } from '@/components';
import { Form, Input } from 'antd';
import classNames from 'classnames';
import { ChangeEvent<PERSON><PERSON><PERSON>, PropsWithChildren, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import styles from './index.module.less';

interface payInfoResult {
  name: string;
  payNo: string;
  payPriceStr: string;
  payeeCompanyName: string;
  voucherUrls: Array<string>;
  voucherNo: string;
  remark: string;
}

interface formProps {
  payItem: payInfoResult;
  payOrPayee: number;
  formChange: () => void;
  // eslint-disable-next-line no-unused-vars
  pullSubmitFun: (fun: () => payInfoResult) => void;
}

const initItem = {
  name: '',
  payNo: '',
  payPriceStr: '',
  payeeCompanyName: '',
  voucherUrls: [],
  voucherNo: '',
  remark: '',
};

function FormCard({
  payItem,
  pullSubmitFun,
  payOrPayee,
  formChange,
}: PropsWithChildren<formProps>) {
  const uploadRef = useRef(null as unknown as SimpleUploadInstance);
  const [cardItem, setCardItem] = useState<payInfoResult>(initItem);
  const [searchParams] = useSearchParams({ orderStatus: '0', region: '0', open: '0' });
  const [form] = Form.useForm();

  //   const dateChange = (item: payInfoResult, date: any) => {
  //     const list = {
  //       ...item,
  //       payTime: String(new Date(date).getTime()),
  //     };
  //     setCardItem(list);
  //   };

  const submitData = () => ({
    ...payItem,
    ...form.getFieldsValue(),
    voucherUrls: uploadRef.current
      ? uploadRef.current
          .getFileList()
          .map((m) => m.url)
          .map((file) => file.split('.com/')[1])
      : [],
  });

  const voucherInput: ChangeEventHandler<HTMLInputElement> = (e) => {
    const voucherNo = e.target.value
      .match(/[a-zA-Z0-9_\u4e00-\u9fa5]/g)
      ?.join('')
      .replace(' ', '');
    if (voucherNo) {
      setCardItem({
        ...cardItem,
        voucherNo,
      });
      form.setFieldsValue({ voucherNo });
    } else {
      setCardItem({
        ...cardItem,
        voucherNo: '',
      });
      form.setFieldsValue({ voucherNo: '' });
    }
  };

  useEffect(() => {
    setCardItem({ ...payItem });
    pullSubmitFun(submitData);
  }, [payItem]); // eslint-disable-line

  return (
    <Form layout="vertical" className={styles.form} form={form}>
      <Form.Item label={`${payOrPayee === 2 ? '付' : '收'}款方`}>
        <div className={styles.font}>{cardItem?.payeeCompanyName}</div>
      </Form.Item>
      <Form.Item label="款项金额">
        <div className={classNames(styles.font, styles.red)}>¥ {cardItem?.payPriceStr}</div>
      </Form.Item>
      {searchParams.get('region') === '0' ? (
        <>
          <Form.Item name="voucherNo" label="凭证单号">
            <Input
              bordered={false}
              placeholder="请输入凭证单号"
              autoComplete="off"
              value={cardItem?.voucherNo}
              onChange={voucherInput}
            />
          </Form.Item>
          <Form.Item label="凭证附件">
            <div className={styles.gray}>
              温馨提示：支持线下转账凭证、票据凭证、银行承兑汇票（文件名或选择任何文件）
            </div>
            <div className={styles.upload}>
              <Upload
                listType="mixin"
                ref={uploadRef}
                maxCount={5}
                multiple
                accept=".xsl, .xlsx, .png, .pdf, .docx, .jpg, .jpeg"
              />
            </div>
          </Form.Item>
        </>
      ) : null}
      {/* <Form.Item name="payTime" label="付款时间">
        <DatePicker
          bordered={false}
          onChange={(date) => {
            dateChange(cardItem, date);
          }}
        />
      </Form.Item> */}

      <div>
        <span>备注</span>
        <span className={styles.noteTitle}>（{cardItem?.remark.length}/200）</span>
      </div>
      <div className={styles.noBorder}>
        <Form.Item name="remark">
          <Input.TextArea
            bordered={false}
            className={styles.textarea}
            placeholder="请输入"
            maxLength={200}
            autoSize={{ minRows: 3, maxRows: 5 }}
            onChange={(e) => {
              setCardItem({
                ...cardItem,
                remark: e.target.value,
              });
              formChange();
            }}
          />
        </Form.Item>
      </div>
    </Form>
  );
}

export default FormCard;
