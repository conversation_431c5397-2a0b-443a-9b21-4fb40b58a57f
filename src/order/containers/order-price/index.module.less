.card {
  margin-bottom: 20px;
  padding: 0 0 20px 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-input-number-input {
      height: 36px;
    }
  }

  .goodsItem {
    padding: 20px 0;
    border-bottom: 1px solid #f3f3f3;
  }

  .goodsFooter {
    padding-right: 20px;
  }
}

.unitPrice {
  display: flex;
  margin-top: 16px;
  justify-content: space-between;
  align-items: center;
}

.cardFooter {
  display: flex;
  margin-top: 16px;
  justify-content: space-between;
  align-items: center;

  :global {
    .ant-input-number-affix-wrapper {
      width: 117px;
      border-radius: 10px;
      background: #f5f6fa;
    }

    .ant-input-number {
      height: 36px;
      line-height: 36px;
      background: #f5f6fa;
    }

    .ant-input-number-affix-wrapper-borderless,
    .ant-input-number-affix-wrapper-borderless:hover {
      background-color: #f5f6fa;
    }
  }
}

.fontGray {
  color: #888b98;
}

.footer {
  display: flex;
  padding: 20px;
  justify-content: space-between;
  align-items: center;

  .text {
    color: #888b98;
    font-size: 12px;
    margin-top: 4px;
  }

  .fontColor {
    color: #040919;
  }
}
