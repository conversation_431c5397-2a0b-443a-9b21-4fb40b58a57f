import {
  getOrderPriceBatch<PERSON>ist,
  PriceProductsResult,
  postOrderChangePrice,
  BatchesPriceResult,
} from '@/apis';
import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import { Button, InputNumber, message } from 'antd';
import { Timeout } from 'ahooks/es/useRequest/src/types';
import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import GoodsItem from '../../components/goods-card/index';
import styles from './index.module.less';

interface ModifyPriceProps {
  visible: boolean;
  orderNo: number;
  nycbngOrgId: string;
  onClose: () => void;
  getOrderList: () => void;
  updateWidth: () => void;
}

// eslint-disable-next-line no-unused-vars
function useDebounce(fn: (args: unknown) => void, delay: number) {
  const refTimer = useRef<Timeout>();
  return function f(...args: any) {
    if (refTimer.current) {
      clearTimeout(refTimer.current);
    }
    refTimer.current = setTimeout(() => {
      fn(args);
    }, delay);
  };
}

function ModifyPrice({
  visible,
  orderNo,
  nycbngOrgId,
  onClose,
  getOrderList,
  updateWidth,
}: PropsWithChildren<ModifyPriceProps>) {
  const drawerRef = useRef<DrawerRefType>(null);
  const [batches, setBatches] = useState<BatchesPriceResult[]>([]);
  const [batchProducts, setBatchProducts] = useState<PriceProductsResult[]>([]);
  const [btnLoading, setBtnLoading] = useState(false);
  const params = useRef({
    haveBatch: false,
  });
  // 表单改变
  const formChange = () => {
    if (!drawerRef.current?.getIsChange()) {
      drawerRef.current?.setIsChange(true);
    }
  };

  const getPriceList = () => {
    getOrderPriceBatchList({ orderNo }, nycbngOrgId).then((res) => {
      setBatches(res.batches);
      params.current.haveBatch = res.haveBatch;
      setBatchProducts(
        res.batches[0].batchProducts.map((item) => ({
          ...item,
          // newPrice: 0,
        }))
      );
    });
  };

  const onSubmit = () => {
    if (batchProducts.every((item) => !item.newPrice)) return message.warning('价格不能为0');
    const data = {
      haveBatch: params.current.haveBatch,
      batches: batches.map((item) => ({
        batchNo: item.batchNo,
        orderNo: item.orderNo,
        // saveOrdChgPriceProdList: batchProducts.map((it) => ({
        //   productDetailNo: it.orderProductNo,
        //   batchDetailNo: it.batchDetailNo,
        //   changePrice: String(it.newPrice),
        // })),
        saveOrdChgPriceProdList: batchProducts
          .filter((it) => it.newPrice)
          .map((i) => ({
            productDetailNo: i.orderProductNo,
            batchDetailNo: i.batchDetailNo,
            changePrice: String(i.newPrice),
          })),
      })),
    };
    setBtnLoading(true);
    postOrderChangePrice(data, nycbngOrgId)
      .then(() => {
        message.success('改价申请成功');
        updateWidth();
        getOrderList();
        onClose();
      })
      .finally(() => {
        setBtnLoading(false);
      });
    return null;
  };

  useEffect(() => {
    if (visible) {
      getPriceList();
    }
  }, [visible]); // eslint-disable-line
  return (
    <Drawer
      ref={drawerRef}
      title="改价"
      visible={visible}
      onClose={() => {
        onClose();
      }}
      footer={
        <div className={styles.footer}>
          <div>
            <div className={styles.fontColor}>
              修改{batchProducts.filter((item) => item.newPrice).length}种的单价
            </div>
            <div className={styles.text}>共{batchProducts.length}种商品</div>
          </div>
          <Button
            type="primary"
            loading={btnLoading}
            disabled={!batchProducts.length}
            onClick={useDebounce(onSubmit, 500)}
          >
            确定修改
          </Button>
        </div>
      }
    >
      {batchProducts.map((item) => (
        <div className={styles.card} key={item.brandId}>
          <div className={styles.goodsItem}>
            <GoodsItem
              orderType="sales"
              price
              img={item.productMainPic}
              productName={item.productName}
              productPrice={item.productPrice}
              number={item.number}
              productUnit={item.productUnit}
              productSpecificationList={item.productSpecificationList}
            />
          </div>
          <div className={styles.goodsFooter}>
            <div className={styles.unitPrice}>
              <div className={styles.fontGray}>单价</div>
              <div>￥ {item.productPrice}</div>
            </div>
            <div className={styles.cardFooter}>
              <div className={styles.fontGray}>改为</div>
              <InputNumber
                value={item.newPrice}
                placeholder="输入金额"
                min={0}
                max={99999999999}
                prefix={<span>￥</span>}
                controls={false}
                bordered={false}
                onChange={(value) => {
                  setBatchProducts(
                    batchProducts.map((it) => {
                      if (item.orderProductNo === it.orderProductNo) {
                        return {
                          ...it,
                          newPrice: value || 0,
                        };
                      }
                      return it;
                    })
                  );
                  formChange();
                }}
              />
            </div>
          </div>
        </div>
      ))}
    </Drawer>
  );
}

export default ModifyPrice;
