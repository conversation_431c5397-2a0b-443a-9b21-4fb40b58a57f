.orderCreate {
  :global {
    .ant-modal-confirm .ant-modal-body {
      padding: 20px;
    }

    .ant-form-item {
      margin-bottom: 0;
    }
    // .ant-form-item-extra {
    //   display: none;
    // }
    // .ant-row ant-form-item {
    //   margin-bottom: 0;
    // }
  }
}

.card {
  display: flex;
  margin-bottom: 20px;
  padding: 10px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.selectTypeItem {
  color: #888b98;
  font-size: 12px;
  display: flex;
  justify-content: center;
  flex: 1;
  align-items: center;
  flex-direction: column;
  cursor: pointer;

  &:first-child {
    color: #008cff;
  }
}

.selectTypeItemNo:first-child {
  color: #888b98;
}

.selectTypeItemIcon {
  font-size: 22px;
  margin-bottom: 6px;
}

.selectTypeItemImg {
  width: 22px;
  height: 22px;
  margin-bottom: 12px;
}

.footer {
  display: flex;
  height: 86px;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
}

.totalPrice {
  color: #ea1c26;
  font-size: 20px;
}

.totalNum {
  color: #888b98;
  font-size: 12px;
  margin-top: 4px;
}

.more {
  cursor: pointer;
}

.cardBox {
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  margin-bottom: 20px;
}

.cell {
  display: flex;
  width: 100%;
  height: 54px;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
}

.cellFoo {
  display: flex;
  width: 100%;
  margin-bottom: 14px;
  padding: 0 12px;
  justify-content: space-between;
  align-items: center;
}

.formCell {
  margin: 0 10px;
  border-bottom: 1px solid #f3f3f3;

  :global {
    .ant-form-item {
      margin-bottom: 10px;
    }
  }
}

.formCell:last-child {
  border-bottom: none;
}

.icon,
.placeholder {
  color: #b1b3be;
  font-size: 16px;
}
