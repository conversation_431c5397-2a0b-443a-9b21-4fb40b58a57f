import { useRef, useState } from 'react';
import { message, Input } from 'antd';
import { FileListItem, SimpleUploadInstance, Upload } from '@/components';
import classnames from 'classnames';
import styles from './index.module.less';

export interface FileInfoProps {
  attachmentUrls: FileListItem[];
  remark: string;
}

interface OrderCreateFileProps {
  // eslint-disable-next-line no-unused-vars
  onChangeFile: (info: FileInfoProps) => void;
}

function OrderCreateFile({ onChangeFile }: OrderCreateFileProps) {
  const uploadRef = useRef(null as unknown as SimpleUploadInstance);
  const [fileListNum, setFileListNum] = useState(0);
  const fileInfo = useRef<FileInfoProps>({
    attachmentUrls: [],
    remark: '',
  });
  const [remarkVal, setRemarkVal] = useState('');

  return (
    <div className={styles.card}>
      <div className={styles.title}>
        附件 <span className={styles.fileNum}>({fileListNum}/5)</span>
      </div>
      <div className={classnames(styles.fileNum, 'mb-2')}>
        可上传的类型PDF\DOC\XSL\图片，单个文件限20M
      </div>
      <Upload
        ref={uploadRef}
        listType="mixin"
        maxCount={5}
        multiple
        accept=".xsl, .xlsx, .png, .pdf, .docx, .jpg, .jpeg"
        beforeUpload={(e, uploadList) => {
          if (uploadList.length > 5) {
            message.error('最多上传5个附件');
            return false;
          }
          return true;
        }}
        onFileListChange={(e) => {
          setFileListNum(e.length);
        }}
        onChange={() => {
          fileInfo.current.attachmentUrls = uploadRef.current.getFileList();
          onChangeFile({ ...fileInfo.current });
        }}
      />
      <div className={styles.remark}>
        <div className="mb-1">
          备注<span className={styles.fileNum}>({remarkVal.length}/200)</span>
        </div>
        <Input
          maxLength={200}
          bordered={false}
          placeholder="请输入"
          onChange={(e) => {
            setRemarkVal(e.target.value);
            fileInfo.current.remark = e.target.value;
            onChangeFile({ ...fileInfo.current });
          }}
        />
      </div>
    </div>
  );
}

export default OrderCreateFile;
