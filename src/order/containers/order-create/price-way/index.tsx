import { useState, useEffect } from 'react';
import { Drawer, Icon } from '@/components';
import classnames from 'classnames';
import { Modal } from 'antd';
import { PackageGoodsListResult, ReplenishShopVOList, StockGoodsListResult } from '@/apis';
import styles from './index.module.less';

interface PriceWayProps {
  visible: boolean;
  priceWay: number;
  goodsList: StockGoodsListResult[];
  createGoods?: ReplenishShopVOList[];
  packageList: PackageGoodsListResult[];
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onConfirm: (id: number) => void;
}

function PriceWay({
  visible,
  priceWay,
  onClose,
  goodsList,
  createGoods,
  packageList,
  onConfirm,
}: PriceWayProps) {
  const [priceWayList, setPriceWayList] = useState([
    {
      title: '按折扣定价',
      describe: '市场价x折扣(百分比)=最终价格',
      checked: true,
      id: 1,
    },
    {
      title: '按一口价定价',
      describe: '直接定一个固定价格',
      checked: false,
      id: 2,
    },
    {
      title: '按减金额定价',
      describe: '市场价-优惠金额=最终价格',
      checked: false,
      id: 3,
    },
  ]);

  const onSelect = (id: number) => {
    priceWayList.forEach((item) => {
      const items = item;
      items.checked = items.id === id;
    });
    setPriceWayList([...priceWayList]);
  };

  const onOk = () => {
    const arr = priceWayList.filter((item) => item.checked);
    const { id } = arr[0];
    let isVal = false;
    if (goodsList.length) {
      isVal =
        goodsList.some((item) => item.priceValue) || packageList.some((item) => item.priceValue);
    }
    if (createGoods?.length) {
      isVal = createGoods.some((shop) => shop.replenishPlanSkuVOS.some((item) => item.priceValue));
    }
    if (id !== priceWay && isVal) {
      Modal.confirm({
        title: '提示',
        content: '切换定价方式，将清空已设置的单价，确定切换吗？',
        icon: null,
        width: 290,
        zIndex: 9999,
        centered: true,
        okText: '确定',
        cancelText: '取消',
        className: styles.showModal,
        getContainer: document.querySelector('.order-create') as HTMLElement,
        onOk() {
          onConfirm(id);
          onClose();
        },
      });
    } else {
      onConfirm(id);
      onClose();
    }
  };

  useEffect(() => {
    if (visible) {
      priceWayList.forEach((item) => {
        const items = item;
        items.checked = items.id === priceWay;
      });
      setPriceWayList([...priceWayList]);
    }
  }, [priceWay, visible]); // eslint-disable-line

  return (
    <Drawer
      visible={visible}
      title="商品的定价方式"
      width={375}
      height={425}
      placement="bottom"
      getContainer={false}
      closeIcon={null}
      mask={visible}
      extra={<Icon name="close" className={styles.close} onClick={onClose} />}
      footer={<Drawer.Footer okText="确定" showCancel={false} onOk={onOk} />}
    >
      {priceWayList.map((item) => (
        <div
          role="button"
          tabIndex={item.id}
          key={item.id}
          className={classnames(styles.card, { [styles.activeCard]: item.checked })}
          onClick={() => onSelect(item.id)}
        >
          <div className={styles.title}>{item.title}</div>
          <div className={styles.describe}>{item.describe}</div>
        </div>
      ))}
    </Drawer>
  );
}

PriceWay.defaultProps = {
  createGoods: [],
};

export default PriceWay;
