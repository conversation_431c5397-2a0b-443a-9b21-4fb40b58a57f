.close {
  color: #999eb2;
  font-size: 20px;
  cursor: pointer;
}

.card {
  margin-bottom: 20px;
  padding: 12px;
  border-radius: 18px;
  background: #fff;
  cursor: pointer;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  &:last-child {
    margin-bottom: 0;
  }
}

.activeCard {
  position: relative;
  border: 1px solid #008cff;

  &::before {
    content: '';
    width: 42px;
    height: 39px;
    position: absolute;
    top: -1px;
    right: -1px;
    background-image: url('https://img.huahuabiz.com/user_files/2023129/1674955996990231.png');
  }
}

.title {
  font-size: 16px;
  margin-bottom: 8px;
}

.describe {
  color: #888b98;
  font-size: 12px;
}
