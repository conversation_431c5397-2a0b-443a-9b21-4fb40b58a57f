import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, message, DrawerProps, Form, Select, Switch } from 'antd';
import { Drawer, Icon, Price } from '@/components';
import {
  postAddOrder,
  orderEnterCreate,
  PackageGoodsListResult,
  ReplenishShopVOList,
} from '@/apis';
import BigNumber from 'big.js';
import dayjs from 'dayjs';
import { testPerm } from '@/utils/permission';
import debounce from 'lodash/debounce';
import { drawerPopup } from '@/utils/popup';
import classNames from 'classnames';
import { user } from '@/store';
import { ReplenishSkuVOList } from '@/apis/psi/stock-replenish-get-order';
import OrderCreateInfo from './order-create-info';
import OrderCreateGoodsItem from './order-create-goods-item';
import OrderCreateFile, { FileInfoProps } from './order-create-file';
import PriceWay from './price-way';
import BatchSetup from './batch-setup';
import styles from './stock-order-create.module.less';
import OrderProject from '../order-project';

interface OrderCreateProps extends DrawerProps {
  orderType: number; // 0 采购 1 销售
  orderTypeNum: number; // 1 订单 2 退货单
  replenishOrderNo: number;
  onClose: () => void;
  onConfirm: () => void;
}

function StockOrderCreate({
  orderTypeNum,
  orderType,
  replenishOrderNo,
  onClose,
  onConfirm,
  ...props
}: OrderCreateProps) {
  const getCode = (type: number) => {
    switch (type) {
      case 1:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_016' : 'R_001_010_012';
        }
        return orderType ? 'R_001_021_005' : 'R_001_022_005';
      case 2:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_017' : 'R_001_010_013';
        }
        return orderType ? 'R_001_021_006' : 'R_001_022_006';
      case 3:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_018' : 'R_001_010_014';
        }
        return orderType ? 'R_001_021_007' : 'R_001_022_007';
      case 4:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_019' : 'R_001_010_015';
        }
        return orderType ? 'R_001_021_008' : 'R_001_022_008';
      default:
        return '';
    }
  };

  const [selectList, setSelectList] = useState<
    {
      id: number;
      name: string;
      iconName: string;
      isChecked: boolean;
      code: string;
    }[]
  >([]);
  const [selectGoodsList, setSelectGoodsList] = useState<ReplenishShopVOList[]>([]);
  const [selectGoodsPackageList, setSelectGoodsPackageList] = useState<PackageGoodsListResult[]>(
    []
  );
  // 项目
  const [showOrderProject, setShowOrderProject] = useState(false);

  // 定价方式
  const [showPriceWay, setShowPriceWay] = useState(false);
  const [priceWay, setPriceWay] = useState(1);

  // 批量设置
  const [showBatchSetup, setShowBatchSetup] = useState(false);

  const isProject = useRef(false);

  const orderParams = useRef({
    crmCorfId: 0,
    buyerOrSellerComId: 0,
    orderTime: 0,
    warehouseId: 0,
    payWayNo: 0,
    projectId: 0,
    productList: [],
    attachmentUrls: [],
    remark: '',
    checkShopId: 0,
  });
  const warehouseNo = useRef('');
  const [showSubmitBtn, setShowSubmitBtn] = useState(false);

  const onSeleteType = (id: number) => {
    if (
      (id === 2 || id === 3) &&
      selectGoodsList.length === 0 &&
      selectGoodsPackageList.length === 0
    ) {
      message.error('请先选择商品');
      return;
    }
    switch (id) {
      case 2:
        setShowPriceWay(true);
        return;
      case 3:
        setShowBatchSetup(true);
        return;
      case 4:
        selectList.splice(3, 1);
        setSelectList([...selectList]);
        break;
      default:
        break;
    }
  };

  const getFinaPrice = (priceVal: number, val: number, wey?: number) => {
    let price = new BigNumber(0);
    switch (wey || priceWay) {
      case 1:
        price = price
          .plus(priceVal || 0)
          .times(val || 0)
          .times(0.01);
        break;
      case 2:
        price = price.plus(val);
        break;
      case 3:
        price = price.plus(priceVal || 0).minus(val || 0);
        break;
      default:
        break;
    }
    return price.toNumber();
  };

  /**
   * @description 价格改变
   * @param val
   */
  const onChangePrice = (val: number, id: number, skuId: number) => {
    selectGoodsList.forEach((shop) => {
      shop.replenishPlanSkuVOS.forEach((item) => {
        const items = item;
        if (items.shopSkuId === id && item.skuId === skuId) {
          items.priceValue = val;
          items.finalPrice = getFinaPrice(Number(item.marketPrice), val);
        }
      });
    });
    setSelectGoodsList([...selectGoodsList]);
  };

  /**
   * @description 批量设置
   */
  const onBatchSetup = (price: number) => {
    selectGoodsList.forEach((shop) => {
      shop.replenishPlanSkuVOS.forEach((item) => {
        const items = item;
        if (priceWay === 3) {
          items.priceValue = price > Number(item.marketPrice) ? Number(item.marketPrice) : price;
        } else {
          items.priceValue = price;
        }
        items.quantity = item.quantity;
        items.finalPrice = getFinaPrice(Number(item.marketPrice), items.priceValue);
      });
    });
    setSelectGoodsList([...selectGoodsList]);
  };

  /**
   * @description 选择方式
   */
  const onSelectPriceWay = (e: number) => {
    setPriceWay(e);
    switch (e) {
      case 1:
        selectList[1].name = '按折扣定价';
        break;
      case 2:
        selectList[1].name = '按一口价定价';
        break;
      case 3:
        selectList[1].name = '按减金额定价';
        break;
      default:
        break;
    }
    selectGoodsList.forEach((shop) => {
      shop.replenishPlanSkuVOS.forEach((item) => {
        const items = item;
        items.priceValue = null;
        items.finalPrice = 0;
      });
    });
    selectGoodsPackageList.forEach((item) => {
      const items = item;
      items.priceValue = null;
      items.finalPrice = 0;
    });
    setSelectList([...selectList]);
  };

  const onChangePayWay = (val: string, id: number) => {
    selectGoodsList.forEach((shop) => {
      shop.replenishPlanSkuVOS.forEach((item) => {
        const items = item;
        if (item.shopSkuId === id) {
          items.haveSetPayWayNo = val;
        }
      });
    });
    setSelectList([...selectList]);
  };

  /**
   * @description 获取订单信息
   */
  const onChangeOrderInfo = (e: any, val: boolean, warehouse: string) => {
    isProject.current = val;
    orderParams.current = {
      ...orderParams.current,
      ...e,
    };
    warehouseNo.current = warehouse || '';
  };

  /**
   * @description 获取附件信息
   */
  const onChangeFile = (e: FileInfoProps) => {
    orderParams.current = {
      ...orderParams.current,
      // @ts-ignore
      attachmentUrls: e.attachmentUrls.map((item) => ({
        fileName: item.name,
        size: item.size,
        filePath: item.url,
        fileType: item.type?.indexOf('image') !== -1 ? 0 : 1,
      })),
      remark: e.remark,
    };
  };

  /**
   * @description 生成订单
   */
  const onSubmit = debounce(() => {
    if (!orderParams.current.orderTime) {
      message.warning('请选择开单时间');
      return;
    }
    const replenishList: ReplenishSkuVOList[] = [];
    selectGoodsList.forEach((f) => {
      f.replenishPlanSkuVOS.forEach((fFoo) => {
        replenishList.push(fFoo);
      });
    });
    const chooseProject = replenishList.some((s) => s.isProject && !s.projectId);
    if (chooseProject) {
      message.warning('请选择项目');
      return;
    }
    if (selectGoodsList.length === 0 && selectGoodsPackageList.length === 0) {
      message.warning('请选择商品');
      return;
    }
    if (selectGoodsList.some((shop) => shop.replenishPlanSkuVOS.some((item) => !item.quantity))) {
      message.warning('请填写商品数量');
      return;
    }
    if (
      selectGoodsList.some((shop) => shop.replenishPlanSkuVOS.some((item) => item.finalPrice < 0))
    ) {
      message.warning('商品单价不能小于0');
      return;
    }
    if (
      selectGoodsList.some((shop) =>
        shop.replenishPlanSkuVOS.some(
          (item) =>
            Number(item.finalPrice) < Number(item.floorPriceStr) && Number(item.floorPriceStr)
        )
      )
    ) {
      message.warning('商品单价不能小于底价');
      return;
    }
    const fn = orderTypeNum === 1 ? postAddOrder : orderEnterCreate;
    const goodsArr: any[] = [];
    selectGoodsList.forEach((shop) => {
      shop.replenishPlanSkuVOS.forEach((item) => {
        goodsArr.push({
          shopSkuId: item.shopSkuId,
          skuId: item.skuId,
          number: item.quantity,
          productPrice: item.priceValue?.toFixed(priceWay === 1 ? 0 : 6),
          productUnit: item.auxiliaryUnit || item.unit || item.unitName,
          pricingMethod: priceWay,
          payWayNo: item.haveSetPayWayNo,
          crmCorfId: item.supplierId,
          buyerOrSellerComId: item.supplierCompanyId,
          projectId: item.isProject ? item.projectId : 0,
        });
      });
    });
    const params = {
      orderEnterType: 1,
      supplementEnterType: orderType ? 1 : 2,
      orderTime: dayjs(orderParams.current.orderTime).valueOf(),
      projectId: orderParams.current.projectId,
      productList: [...goodsArr],
      payWayNo: 1,
      crmCorfId: 1,
      buyerOrSellerComId: 1,
      replenishOrderNo,
    };
    setShowSubmitBtn(true);
    // @ts-ignore
    fn(params, '')
      .then(() => {
        message.success('新建成功');
        onClose();
        setSelectGoodsList([]);
        setSelectGoodsPackageList([]);
        onConfirm();
      })
      .finally(() => {
        setShowSubmitBtn(false);
      });
  }, 300);

  /**
   * @description 获取商品总价
   */
  const totalNum = useMemo(() => {
    let num = new BigNumber(0);
    selectGoodsList.forEach((shop) => {
      shop.replenishPlanSkuVOS.forEach((item) => {
        num = num.plus(BigNumber(Number(item.finalPrice || 0)).times(item.quantity || 0));
      });
    });
    return num.toNumber();
  }, [selectGoodsList]);

  /**
   * @description 获取商品总数
   */
  const goodsNum = useMemo(() => {
    let num: number = selectGoodsList.length;
    selectGoodsPackageList.forEach((item) => {
      num += item.skuList.length;
    });
    return num;
  }, [selectGoodsList, selectGoodsPackageList]);

  const footer = (
    <div className={styles.footer}>
      <div>
        合计：
        <span className={styles.totalPrice}>
          <Price value={totalNum} symbol="" format />
        </span>
        <div className={styles.totalNum}>共{goodsNum}种商品</div>
      </div>
      <Button disabled={showSubmitBtn} type="primary" onClick={onSubmit}>
        {orderTypeNum === 1 ? '生成补录订单' : `生成${orderType ? '销售退' : '采购退'}货单`}
      </Button>
    </div>
  );

  useEffect(() => {
    if (props.visible) {
      const initSelectData = [
        {
          id: 1,
          name: '添加商品',
          iconName: 'plus-outline',
          isChecked: false,
          code: getCode(1),
        },
        {
          id: 2,
          name: '按折扣定价',
          iconName: 'share',
          isChecked: false,
          code: getCode(2),
        },
        {
          id: 3,
          name: '批量设置',
          iconName: 'control',
          isChecked: false,
          code: getCode(3),
        },
        {
          id: 4,
          name: '附件备注',
          iconName: 'clip',
          isChecked: false,
          code: getCode(4),
        },
      ];
      setPriceWay(1);
      setSelectList([...initSelectData]);
      const arr = JSON.parse(localStorage.getItem('ORDERCREATE') || '');
      if (arr) {
        arr.forEach((f: ReplenishShopVOList) => {
          f.replenishPlanSkuVOS.forEach((fFoo) => {
            // eslint-disable-next-line no-param-reassign
            fFoo.isProject = false;
            // eslint-disable-next-line no-param-reassign
            fFoo.projectId = 0;
            // eslint-disable-next-line no-param-reassign
            fFoo.projectName = '';
          });
        });
      }
      setSelectGoodsList(arr || []);
    }
  }, [props.visible]); // eslint-disable-line

  return (
    <Drawer
      onClose={onClose}
      {...props}
      title="新建订单"
      className={styles.orderCreate}
      footer={footer}
      push={false}
    >
      <div className="order-create">
        <OrderCreateInfo
          isStockOrderCreate
          orderType={orderType}
          orderTypeNum={orderTypeNum}
          showOrder={false}
          onChangeOrderInfo={onChangeOrderInfo}
          onChangeOrderCustomer={() => {}}
          onChangePayWay={() => {}}
        />
        <div className={styles.card}>
          {selectList
            .filter((item, index) => index !== 0)
            .map((item) => (
              <div
                role="button"
                tabIndex={item.id}
                className={classNames(styles.selectTypeItem, styles.selectTypeItemNo)}
                onClick={() => {
                  if (!testPerm(item.code)) {
                    return;
                  }
                  onSeleteType(item.id);
                }}
              >
                {item.id === 2 ? (
                  <img
                    className={styles.selectTypeItemImg}
                    src="https://img.huahuabiz.com/user_files/2023210/1675996841631934.png"
                    alt=""
                  />
                ) : (
                  <Icon name={item.iconName} className={styles.selectTypeItemIcon} />
                )}
                <div>{item.name}</div>
              </div>
            ))}
        </div>
        {selectGoodsList.map((shop) =>
          shop.replenishPlanSkuVOS.map((item) => (
            <>
              <div className={styles.cardBox}>
                <div className={styles.cell}>
                  <span>供货商</span>
                  <span>{shop.supplierName}</span>
                </div>
                <div className={styles.formCell}>
                  <Form layout="vertical">
                    <Form.Item
                      label="付款方式"
                      required
                      extra={<div className={styles.formItemLine} />}
                    >
                      <Select
                        value={Number(item.haveSetPayWayNo) || null}
                        bordered={false}
                        placeholder="请选择付款方式"
                        fieldNames={{
                          label: 'name',
                          value: 'payWayNo',
                        }}
                        options={item.replenishPayWayVOList}
                        onChange={(e) => onChangePayWay(`${e}`, item.shopSkuId)}
                        suffixIcon={<Icon name="down" size={16} />}
                      />
                    </Form.Item>
                  </Form>
                </div>
                <div className={styles.cell}>
                  <span>项目</span>
                  <span>
                    <Switch
                      checked={item.isProject}
                      onChange={(e) => {
                        let code = '';
                        if (orderTypeNum === 1) {
                          code = orderType ? 'R_001_009_015' : 'R_001_010_011';
                        } else {
                          code = orderType ? 'R_001_021_004' : 'R_001_022_004';
                        }
                        if (!testPerm(code)) {
                          return;
                        }
                        const selectGoodsListFoo = selectGoodsList.map((m) => {
                          if (m.shopId === shop.shopId) {
                            return {
                              ...m,
                              replenishPlanSkuVOS: m.replenishPlanSkuVOS.map((mFoo) => ({
                                ...mFoo,
                                isProject: mFoo.supplierId === item.supplierId ? e : mFoo.isProject,
                              })),
                            };
                          }
                          return m;
                        });
                        setSelectGoodsList(selectGoodsListFoo);
                        isProject.current = e;
                      }}
                    />
                  </span>
                </div>
                {item.isProject && (
                  <div className={styles.formCell}>
                    <Form layout="vertical">
                      <Form.Item
                        label="项目选择"
                        required
                        extra={<div className={styles.formItemLine} />}
                      >
                        <div
                          role="button"
                          tabIndex={0}
                          className={styles.cellFoo}
                          onClick={() => {
                            orderParams.current.crmCorfId = item.supplierId;
                            orderParams.current.checkShopId = shop.shopId;
                            setShowOrderProject(true);
                          }}
                        >
                          <span className="label">
                            <span className={styles.placeholder}>
                              {!item.projectId ? (
                                <span className={styles.placeholder}>选择项目</span>
                              ) : (
                                <span>{item.projectName}</span>
                              )}
                            </span>
                          </span>
                          <Icon name="right" className={styles.icon} />
                        </div>
                      </Form.Item>
                    </Form>
                  </div>
                )}
              </div>
              <OrderCreateGoodsItem
                isPackage={false}
                key={item.shopSkuId}
                id={item.shopSkuId}
                skuId={item.skuId}
                name={item.skuName}
                image={item.images}
                standard={JSON.parse(item.standardJson)}
                unit={item.auxiliaryUnit || item.unitName}
                number={item.quantity}
                stock={item.stock}
                floorPrice={Number(item.floorPriceStr)}
                saleGroup={item.saleGroupStr}
                priceWay={priceWay}
                marketPrice={Number(item.marketPrice)}
                priceValue={item.priceValue}
                finalPrice={item.finalPrice}
                onDelete={() => {}}
                onConfirmPrice={(val, id) => onChangePrice(val, id, item.skuId)}
                onConfirmUnit={() => {}}
                onChangeNum={() => {}}
                goodsServers={[]}
                onChangeServer={() => {}}
                isNum={false}
                isDelete={false}
              />
            </>
          ))
        )}
        {selectList.length === 3 && <OrderCreateFile onChangeFile={onChangeFile} />}
        <PriceWay
          visible={showPriceWay}
          priceWay={priceWay}
          onClose={() => setShowPriceWay(false)}
          onConfirm={onSelectPriceWay}
          goodsList={[]}
          createGoods={selectGoodsList}
          packageList={selectGoodsPackageList}
        />
        <BatchSetup
          visible={showBatchSetup}
          priceWay={priceWay}
          onClose={() => setShowBatchSetup(false)}
          isNum={false}
          onConfirm={onBatchSetup}
        />
        <OrderProject
          visible={showOrderProject}
          companyId={user.companyId}
          customerId={orderParams.current.crmCorfId || ''}
          onClose={() => setShowOrderProject(false)}
          getProject={(e) => {
            orderParams.current = {
              ...orderParams.current,
              projectId: e.id,
            };

            const selectGoodsListFoo = selectGoodsList.map((m) => {
              if (m.shopId === orderParams.current.checkShopId) {
                return {
                  ...m,
                  replenishPlanSkuVOS: m.replenishPlanSkuVOS.map((mFoo) => {
                    const isShop = orderParams.current.crmCorfId === mFoo.supplierId;
                    return {
                      ...mFoo,
                      projectId: isShop ? e.id : mFoo.projectId,
                      projectName: isShop ? e.projectName : mFoo.projectName,
                    };
                  }),
                };
              }
              return m;
            });
            setSelectGoodsList(selectGoodsListFoo);
          }}
        />
      </div>
    </Drawer>
  );
}

export const stockOrderCreate = (props: OrderCreateProps) => {
  drawerPopup(StockOrderCreate, props);
};

export default stockOrderCreate;
