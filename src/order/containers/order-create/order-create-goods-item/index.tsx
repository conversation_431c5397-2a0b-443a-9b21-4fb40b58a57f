import { useMemo, useState } from 'react';
import {
  formulaConvertUnitManyConvert,
  FormulaConvertUnitManyConvertResult,
  getOrderUnitConversion,
  OrderUnitResult,
  StandardItem,
  PackageSkuListResult,
  CartGoodsService,
  PrefectureData,
} from '@/apis';
import { Select, Tooltip, InputNumber } from 'antd';
import { Icon } from '@/components';
import { Stepper } from 'antd-mobile';
import isString from 'lodash/isString';
import BigNumber from 'big.js';
import GoodsService from '../../../components/goods-service';
import styles from './index.module.less';

interface OrderCreateGoodsItemProps {
  isNum?: boolean;
  isDelete?: boolean;
  isPackage: boolean;
  id: number;
  skuId?: number;
  image: string;
  name: string;
  introduce?: string;
  standard?: StandardItem[] | string;
  minimum?: string;
  saleGroup?: string;
  saleGroupStr?: string;
  productMinimumSalesUnit?: string;
  unit?: string;
  floorPrice?: number;
  auxiliaryFloorPrice?: number;
  stock?: string;
  stockNumber?: string;
  unitTemplateId?: number;
  number: number;
  priceWay?: number;
  marketPrice: number;
  productMarketPrice?: number;
  priceValue?: number | null;
  finalPrice?: number | null;
  goodsServers?: CartGoodsService[];
  skuList?: PackageSkuListResult[];
  // eslint-disable-next-line no-unused-vars
  onDelete: (id: number) => void;
  // eslint-disable-next-line no-unused-vars
  onConfirmPrice: (val: number, id: number) => void;
  // eslint-disable-next-line no-unused-vars
  onConfirmUnit: (val: OrderUnitResult, id: number) => void;
  // eslint-disable-next-line no-unused-vars
  onChangeNum: (val: number, id: number, saleGroup?: string, event?: string) => void;
  // eslint-disable-next-line no-unused-vars
  onChangeServer: (servers: CartGoodsService[], id: number) => void;
  // eslint-disable-next-line react/require-default-props
  prefectureInfo?: PrefectureData;
}

function OrderCreateGoodsItem({
  isNum,
  isDelete,
  isPackage,
  id,
  skuId,
  image,
  name,
  standard,
  introduce,
  minimum,
  saleGroup,
  saleGroupStr,
  productMinimumSalesUnit,
  unit,
  floorPrice,
  auxiliaryFloorPrice,
  stock,
  stockNumber,
  unitTemplateId,
  number,
  priceWay,
  marketPrice,
  productMarketPrice,
  priceValue,
  finalPrice,
  goodsServers,
  skuList,
  onDelete,
  onConfirmPrice,
  onConfirmUnit,
  onChangeNum,
  onChangeServer,
  prefectureInfo,
}: OrderCreateGoodsItemProps) {
  const [unitList, setUnitList] = useState<FormulaConvertUnitManyConvertResult[]>([]);

  const standardVal = useMemo(() => {
    let str = '';
    if (isString(standard)) {
      str = standard;
    } else {
      standard?.forEach((item, index) => {
        str += `${item.name}: ${item.value}${index === standard.length - 1 ? '' : ','}`;
      });
    }
    return str;
  }, [standard]);

  /**
   * @description 获取单位
   */
  const getUnitList = () => {
    formulaConvertUnitManyConvert({
      templateId: unitTemplateId || 0,
      mainUnitName: unit || '',
    }).then((res) => {
      setUnitList(res.list);
    });
  };

  /**
   * @description 切换单位
   */
  const onChangeUnit = (val: string) => {
    getOrderUnitConversion({
      skuId,
      productMarketPrice: marketPrice,
      productMinimumQuantity: minimum,
      productMinimumSalesUnit: saleGroupStr,
      productUnit: val,
      productUnitBefore: unit,
      number,
      stockNumber: stockNumber || stock,
      floorPrice,
    }).then((res) => {
      onConfirmUnit(res, id);
    });
  };

  const priceType = useMemo(() => {
    if (prefectureInfo && prefectureInfo.calculateType) {
      switch (prefectureInfo.calculateType) {
        case 1:
          return 3;
        case 2:
          return 1;
        case 5:
          return 2;
        default:
          return '';
      }
    }
    return priceWay;
  }, [prefectureInfo, priceWay]);

  return (
    <div className={styles.card}>
      <div>
        <div className={styles.goodsBox}>
          <div className={styles.goodsItem}>
            <img className={styles.image} src={image} alt="" />
            <div className={styles.goodsInfo}>
              <div>
                <div className={styles.name}>{name}</div>
                {isString(standard) ? (
                  standard
                ) : (
                  <div
                    title={standard?.length ? standardVal : introduce}
                    className={styles.standard}
                  >
                    {standard?.length
                      ? standard.map((item) => (
                          <span key={item.name + item.value} className={styles.standardItem}>
                            {item.name}: {item.value}
                            <span>，</span>
                          </span>
                        ))
                      : introduce}
                  </div>
                )}
              </div>
              {isPackage ? (
                <div className={styles.packagesPrice}>￥{productMarketPrice || marketPrice}</div>
              ) : (
                <>
                  <div className={styles.baseFont}>
                    最小销售单元：{Number(productMinimumSalesUnit || saleGroup)}
                    {unit}
                  </div>
                  <div className={styles.baseFont}>
                    <span className="mr-2">
                      底价:
                      <span>￥{Number(auxiliaryFloorPrice || floorPrice)}</span>
                    </span>
                    <span>
                      库存:
                      <span>{Number(stockNumber || stock || 0)}</span>
                    </span>
                  </div>
                </>
              )}
            </div>
          </div>
          {goodsServers &&
            goodsServers.length > 0 &&
            goodsServers.some((server) => server.serviceStandardProductVOS.length) && (
              <div
                role="button"
                tabIndex={0}
                className={styles.goodsServer}
                onClick={() => onChangeServer(goodsServers, id)}
              >
                <GoodsService
                  number={
                    !isPackage
                      ? BigNumber(number || 0)
                          .div(productMinimumSalesUnit || saleGroup || 0)
                          .toNumber()
                      : 0
                  }
                  services={goodsServers}
                />
              </div>
            )}
        </div>
        <div className={styles.content}>
          {!isPackage && (
            <div className={styles.cell}>
              <div className={styles.label}>单位</div>
              <Select
                placeholder="请选择"
                bordered={false}
                value={unit}
                suffixIcon={<Icon name="down" />}
                disabled={!unitTemplateId}
                options={unitList.map((each) => ({
                  label: each.unitName,
                  value: each.unitName,
                }))}
                onFocus={() => {
                  if (unitTemplateId && unit) {
                    getUnitList();
                  }
                }}
                onChange={(e) => onChangeUnit(e)}
              />
            </div>
          )}
          <div className={styles.cell}>
            <div className={styles.label}>单价</div>
            {priceType === 1 && (
              <div className={styles.value}>
                <div className={styles.price}>
                  <Tooltip title={productMarketPrice || marketPrice}>
                    ￥{productMarketPrice || marketPrice}
                  </Tooltip>
                </div>
                <span className="mr-1">x</span>
                <InputNumber
                  value={Number(priceValue?.toFixed(0)) || undefined}
                  placeholder="折扣值"
                  min={0}
                  max={100}
                  controls={false}
                  bordered={false}
                  onChange={(e) => onConfirmPrice(Number(e), id)}
                  disabled={prefectureInfo && prefectureInfo.priceFactor > 0}
                />
                <span className="mr-1">%</span>
                <span className="mr-1">=</span>
                <span className={styles.total}>
                  <Tooltip
                    title={finalPrice && priceValue ? `￥${finalPrice}` : '最终价格'}
                    placement="top"
                  >
                    {finalPrice && priceValue ? `￥${finalPrice}` : '最终价格'}
                  </Tooltip>
                </span>
              </div>
            )}
            {priceType === 2 && (
              <div className={styles.value}>
                <span className="mr-1">一口价：￥</span>
                <InputNumber
                  value={Number(priceValue?.toFixed(6)) || undefined}
                  min={0}
                  max={999999999}
                  placeholder="输入金额"
                  bordered={false}
                  controls={false}
                  onChange={(e) => onConfirmPrice(Number(e), id)}
                  disabled={prefectureInfo && prefectureInfo.priceFactor > 0}
                />
                <span className={styles.marketPrice}>
                  市场价：{productMarketPrice || marketPrice}
                </span>
              </div>
            )}
            {priceType === 3 && (
              <div className={styles.value}>
                <span className="mr-1">￥{productMarketPrice || marketPrice}</span>
                <span className="mr-1">-</span>
                <InputNumber
                  value={Number(priceValue?.toFixed(6)) || undefined}
                  min={0}
                  max={productMarketPrice || marketPrice}
                  placeholder="优惠金额"
                  bordered={false}
                  controls={false}
                  onChange={(e) => onConfirmPrice(Number(e), id)}
                  disabled={prefectureInfo && prefectureInfo.priceFactor > 0}
                />
                <span className="mr-1">=</span>
                <Tooltip
                  title={
                    (finalPrice && priceValue) ||
                    priceValue === (productMarketPrice || marketPrice) ||
                    priceValue === 0
                      ? `￥${finalPrice}`
                      : '最终价格'
                  }
                >
                  <span className={styles.total}>
                    {(finalPrice && priceValue) ||
                    priceValue === (productMarketPrice || marketPrice) ||
                    priceValue === 0
                      ? `￥${finalPrice}`
                      : '最终价格'}
                  </span>
                </Tooltip>
              </div>
            )}
          </div>
          <div className={styles.cell}>
            <div className={styles.label}>数量</div>
            {isPackage && !isNum && (
              <Stepper
                value={number}
                min={Number(minimum || 0)}
                max={99999999}
                step={Number(productMinimumSalesUnit || saleGroup || 1)}
                digits={0}
                onChange={(e) => onChangeNum(e, id)}
              />
            )}
            {isNum ? (
              <Stepper
                value={number}
                min={Number(minimum || 0)}
                max={99999999}
                step={Number(productMinimumSalesUnit || saleGroup || 1)}
                onChange={(e) => onChangeNum(e, id, productMinimumSalesUnit || saleGroup)}
                onBlur={(e) =>
                  onChangeNum(
                    Number(e.target.value),
                    id,
                    productMinimumSalesUnit || saleGroup,
                    'blur'
                  )
                }
              />
            ) : (
              number
            )}
          </div>
        </div>
        {isDelete && (
          <Icon name="close-circle2" className={styles.delete} onClick={() => onDelete(id)} />
        )}
      </div>
      {skuList && skuList?.length > 0 && (
        <div>
          <div className={styles.selectNum}>
            <div className={styles.label}>商品包内容</div>
          </div>
          {skuList?.length &&
            skuList.map((item) => (
              <div key={item.shopSkuId} className={styles.packageItem}>
                <div className={styles.goods}>
                  <div className={styles.imgBox}>
                    <img
                      className={styles.img}
                      src={
                        item.image || 'https://img.huahuabiz.com/default/image/default_holder.png'
                      }
                      alt=""
                    />
                    {item.isGift > 0 && <span className={styles.giftTag}>赠品</span>}
                  </div>
                  <div className={styles.goodsInfo}>
                    <div>
                      <div className="mb-2">{item.name}</div>
                      <div className={styles.standardPackage}>
                        {item.standardList.map((meta) => (
                          <span className="mr-1">{meta.value}</span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
                {item.serverGoods &&
                  item.serverGoods.length > 0 &&
                  item.serverGoods.some((server) => server.serviceStandardProductVOS.length) && (
                    <div
                      className={styles.packageServer}
                      role="button"
                      tabIndex={0}
                      onClick={() => onChangeServer(item.serverGoods, item.shopSkuId)}
                    >
                      <GoodsService services={item.serverGoods} />
                    </div>
                  )}
              </div>
            ))}
        </div>
      )}
    </div>
  );
}

OrderCreateGoodsItem.defaultProps = {
  isNum: true,
  isDelete: true,
  skuId: 0,
  introduce: '',
  standard: [],
  minimum: '',
  unit: '',
  saleGroup: '',
  saleGroupStr: '',
  productMinimumSalesUnit: '',
  floorPrice: 0,
  auxiliaryFloorPrice: 0,
  stock: '',
  stockNumber: '',
  unitTemplateId: 0,
  productMarketPrice: 0,
  priceWay: 0,
  priceValue: null,
  finalPrice: null,
  goodsServers: [],
  skuList: [],
};

export default OrderCreateGoodsItem;
