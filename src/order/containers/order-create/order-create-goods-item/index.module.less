@import 'styles/mixins/mixins';
// stylelint-disabled

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  overflow: hidden;
  position: relative;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.goodsBox {
  //margin-bottom: 16px;
}

.goodsItem {
  display: flex;
  padding: 20px 0;
}

.goodsServer {
  margin-bottom: 20px;
  padding-left: 120px;
}

.image {
  width: 108px;
  height: 108px;
  margin-right: 12px;
  border-radius: 10px;
}

.baseFont {
  color: #888b98;
  font-size: 12px;
}

.standard {
  width: 177px;
  .baseFont();
  .text-overflow(1);
}

.standardItem:last-child span {
  display: none;
}

.content {
  border-top: 1px solid #f3f3f3;
  padding: 8px 0 20px;
}

.cell {
  display: flex;
  width: 100%;
  height: 36px;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    margin-bottom: 0;
  }

  :global {
    .adm-stepper {
      width: 170px;
      height: 36px;
      border-radius: 10px;
      background-color: #f5f6fa;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      color: #000;
    }
  }
}

.label {
  width: 50px;
  white-space: nowrap;
}

.value {
  display: flex;
  align-items: center;
  flex: 1;

  & .price {
    font-size: 14px;
    width: 70px;
    margin-right: 4px;
    text-align: right;
    cursor: pointer;
    .text-overflow();
  }

  & .total {
    color: #ea1c26;
    max-width: 80px;
    cursor: pointer;
    .text-overflow();
  }

  & .marketPrice {
    color: #999eb2;
    font-size: 12px;
    margin-left: 4px;
    text-decoration: line-through;
  }

  :global {
    .ant-input-number {
      width: 80px;
      margin-right: 4px;
    }

    .ant-input-number-input {
      width: 80px;
      height: 36px;
      padding: 0 10px;
      background: #f5f6fa;
    }
  }
}

.delete {
  font-size: 18px;
  position: absolute;
  top: 6px;
  right: 6px;
  opacity: 0.2;
  cursor: pointer;
}

.goods {
  display: flex;
  padding: 8px 0;
}

.imgBox {
  position: relative;
}

.img {
  width: 80px;
  height: 80px;
  margin-right: 10px;
  border-radius: 6px;
}

.giftTag {
  color: #fff;
  font-size: 10px;
  display: inline-block;
  width: 32px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
  opacity: 0.8;
  border-radius: 0 12px;
  background: rgb(249 174 8 / 80%);
}

.goodsInfo {
  flex: 1;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.name {
  width: 150px;
  margin-bottom: 2px;
  .text-overflow(2);
}

.standardPackage {
  color: #888b98;
  font-size: 12px;
}

.packagesPrice {
  font-size: 12px;
}

.checkbox {
  display: flex;
  align-items: center;
}

.packageItem:last-child {
  margin-bottom: 10px;
}

.packageServer {
  padding-left: 92px;
}
