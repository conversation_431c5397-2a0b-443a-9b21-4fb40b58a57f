import { useEffect, useMemo, useRef, useState } from 'react';
import { Form, Select, Switch, message } from 'antd';
import { DatePicker, Icon } from '@/components';
import { useRequest } from 'ahooks';
import {
  supplierListMy,
  SupplierListMyResult,
  getCustomerPageAllCustomerList,
  AllCustomerListResult,
  PayWayList,
  stockWarehouseList,
  StockWarehouseResult,
  orderTypeList,
  OrderTypeListData,
} from '@/apis';
import { user } from '@/store';
import OrderPayment from '@@/order/containers/order-payment';
import OrderProject from '@@/order/containers/order-project';
import dayjs, { Dayjs } from 'dayjs';
import { testPerm } from '@/utils/permission';
import classNames from 'classnames';
import styles from './index.module.less';

const initPayway = {
  id: 0,
  isDefault: 0,
  isEdit: false,
  isHadUsed: false,
  name: '',
  payWayDesc: '',
  payWayNo: 0,
};

interface InfoProps {
  crmCorfId: number;
  buyerOrSellerComId: number;
  orderTime: number;
  warehouseId: number;
  payWayNo: number;
  projectId: number;
  orderTypeId: number;
}

interface OrderCreateInfoProps {
  isStockOrderCreate?: boolean;
  showOrder?: boolean;
  orderType: number;
  orderTypeNum: number;
  // eslint-disable-next-line no-unused-vars
  onChangeOrderInfo: (info: InfoProps, isProject: boolean, warehouseNo: string) => void;
  // eslint-disable-next-line no-unused-vars
  onChangeOrderCustomer: (id: number) => void;
  onChangePayWay: () => void;
}

function OrderCreateInfo({
  isStockOrderCreate,
  orderType,
  orderTypeNum,
  onChangeOrderInfo,
  onChangeOrderCustomer,
  showOrder,
  onChangePayWay,
}: OrderCreateInfoProps) {
  const [form] = Form.useForm();
  const formInfo = useRef<InfoProps>({
    crmCorfId: 0,
    buyerOrSellerComId: 0,
    orderTime: 0,
    payWayNo: 0,
    projectId: 0,
    warehouseId: 0,
    orderTypeId: 0,
  });

  // 客户
  const [customerList, setCustomerList] = useState<AllCustomerListResult[]>([]);
  const customerParams = useRef({
    pageNo: 1,
    pageSize: 10,
    keywords: '',
    // companyId: user.companyId,
    customerType: 0,
    filterContact: false,
    filterPublic: true,
  });
  const customerTotalPages = useRef(0);

  // 供应商
  const [supplierList, setSupplierList] = useState<SupplierListMyResult[]>([]);
  const supplierParams = useRef({
    pageNo: 1,
    pageSize: 10,
    keyword: '',
    companyId: user.companyId,
  });
  const supplierTotalPages = useRef(0);

  // 仓库
  const [warehouseList, setWarehouseList] = useState<StockWarehouseResult[]>([]);
  const warehouseParams = useRef({
    pageNo: 1,
    pageSize: 10,
    companyId: user.companyId,
  });
  const warehouseTotalPages = useRef(0);
  const warehouseNo = useRef('');

  const orderTypeParams = useRef({
    isPaging: false,
    keyword: '',
  });
  const [orderTypes, setOrderTypes] = useState<OrderTypeListData[]>([]);

  // 付款比例
  const [showOrderPayment, setShowOrderPayment] = useState(false);
  // @ts-ignore
  const [paywayInfo, setPaywayInfo] = useState<PayWayList>(initPayway);

  // 项目
  const [isProject, setIsProject] = useState(false);
  const [showOrderProject, setShowOrderProject] = useState(false);
  const [projectInfo, setProjectInfo] = useState<{
    projectId: number;
    projectName: string;
  }>({
    projectId: 0,
    projectName: '',
  });

  /**
   * @description 获取客户列表
   */
  const { run: runCustomer } = useRequest(getCustomerPageAllCustomerList, {
    manual: true,
    defaultParams: [{ ...customerParams.current }],
    onSuccess: (result) => {
      if (customerParams.current.pageNo === 1) {
        setCustomerList(result.list);
      } else {
        setCustomerList([...customerList, ...result.list]);
      }
      customerParams.current.pageNo += 1;
      customerTotalPages.current = result.pagination.total;
    },
  });

  /**
   * @description 获取供应商
   */
  const { run: runSupplier } = useRequest(supplierListMy, {
    manual: true,
    defaultParams: [{ ...supplierParams.current }],
    onSuccess: (result) => {
      if (supplierParams.current.pageNo === 1) {
        setSupplierList(result.list);
      } else {
        setSupplierList([...supplierList, ...result.list]);
      }
      supplierParams.current.pageNo += 1;
      supplierTotalPages.current = result.pagination.total;
    },
  });

  /**
   * @description 获取仓库列表
   */
  const { run: runWarehouse } = useRequest(stockWarehouseList, {
    manual: true,
    defaultParams: [{ ...warehouseParams.current }],
    onSuccess: (result) => {
      if (supplierParams.current.pageNo === 1) {
        setWarehouseList(result.list);
      } else {
        setWarehouseList([...warehouseList, ...result.list]);
      }
      warehouseParams.current.pageNo += 1;
      warehouseTotalPages.current = result.pagination.total;
    },
  });

  /**
   * @description 获取订单类型列表
   */
  const { run: runOrderType } = useRequest(orderTypeList, {
    manual: true,
    defaultParams: [{ isPaging: false, keyword: '' }],
    onSuccess: (res) => {
      setOrderTypes([...res.list]);
    },
  });

  const disabledDateStart = (current: Dayjs) => current && current > dayjs().endOf('day');

  const warehouseTitle = useMemo(() => {
    if (orderTypeNum === 1) {
      return orderType ? '出' : '入';
    }
    return orderType ? '入' : '出';
  }, [orderType, orderTypeNum]);

  useEffect(() => {
    if (orderType) {
      runCustomer({ ...customerParams.current });
    } else {
      runSupplier({ ...supplierParams.current });
    }
    runWarehouse({ ...warehouseParams.current });
    runOrderType({ ...orderTypeParams.current });
  }, [orderType, runCustomer, runSupplier, runWarehouse, runOrderType]);

  return (
    <div
      className={classNames(styles.card, {
        [styles.cardFoo]: !showOrder,
      })}
    >
      <Form
        form={form}
        layout="vertical"
        onValuesChange={(changedValues) => {
          const formValue = form.getFieldsValue();
          let arr: any[] = [];
          if (orderType) {
            arr = customerList.filter((item) => item.id === formValue.crmCorfId);
          } else {
            arr = supplierList.filter((item) => item.id === formValue.crmCorfId);
          }
          formInfo.current = {
            ...formValue,
            payWayNo: formInfo.current.payWayNo,
            projectId: formInfo.current.projectId,
            buyerOrSellerComId:
              changedValues.crmCorfId && arr.length
                ? arr[0].customerCompanyId
                : formInfo.current.buyerOrSellerComId || 0,
          };
          const wareArr: StockWarehouseResult[] = warehouseList.filter(
            (item) => item.id === formInfo.current.warehouseId
          );
          if (wareArr && wareArr.length) {
            warehouseNo.current = wareArr[0].warehouseNo;
          }
          onChangeOrderInfo(formInfo.current, isProject, warehouseNo.current);
        }}
      >
        {!isStockOrderCreate && (
          <Form.Item
            label={orderType ? '客户' : '供应商'}
            name="crmCorfId"
            required
            extra={<div className={styles.formItemLine} />}
          >
            <Select
              className={styles.formItem}
              bordered={false}
              // @ts-ignore
              options={orderType ? customerList : supplierList}
              placeholder={`选择${orderType ? '客户' : '供应商'}`}
              allowClear
              showSearch
              fieldNames={{
                label: 'customerName',
                value: 'id',
              }}
              suffixIcon={<Icon name="down" size={16} />}
              filterOption={false}
              onPopupScroll={() => {
                if (orderType) {
                  if (customerParams.current.pageNo > customerTotalPages.current) return;
                  runCustomer({ ...customerParams.current });
                } else {
                  if (supplierParams.current.pageNo > supplierTotalPages.current) return;
                  runSupplier({ ...supplierParams.current });
                }
              }}
              onSearch={(e) => {
                if (orderType) {
                  customerParams.current.pageNo = 1;
                  runCustomer({ ...customerParams.current, keywords: e });
                } else {
                  supplierParams.current.pageNo = 1;
                  runSupplier({ ...supplierParams.current, keyword: e });
                }
              }}
              onChange={(e) => {
                if (orderType) {
                  customerParams.current = {
                    ...customerParams.current,
                    pageNo: 1,
                    keywords: '',
                  };
                  runCustomer({ ...customerParams.current });
                  runWarehouse({ ...warehouseParams.current, customerId: e });
                  if (form.getFieldValue(['warehouseId'])) {
                    form.setFieldsValue({
                      warehouseId: undefined,
                    });
                  }
                } else {
                  supplierParams.current = {
                    ...supplierParams.current,
                    pageNo: 1,
                    keyword: '',
                  };
                  runSupplier({ ...supplierParams.current });
                }
                onChangeOrderCustomer(e);
              }}
            />
          </Form.Item>
        )}
        <Form.Item
          label="开单时间"
          name="orderTime"
          required
          extra={<div className={styles.formItemLine} />}
        >
          <DatePicker
            placeholder="选择开单时间"
            className={styles.formItem}
            bordered={false}
            disabledDate={disabledDateStart}
          />
        </Form.Item>
        {orderTypeNum === 2 && !isStockOrderCreate && (
          <Form.Item
            label={`${warehouseTitle}库仓库`}
            name="warehouseId"
            required
            extra={<div className={styles.formItemLine} />}
          >
            <Select
              className={styles.formItem}
              bordered={false}
              options={warehouseList}
              placeholder={`选择${warehouseTitle}库仓库`}
              allowClear
              showSearch
              fieldNames={{
                label: 'warehouseName',
                value: 'id',
              }}
              suffixIcon={<Icon name="down" size={16} />}
              filterOption={false}
              onPopupScroll={() => {
                if (warehouseParams.current.pageNo > warehouseTotalPages.current) return;
                runWarehouse({ ...warehouseParams.current });
              }}
            />
          </Form.Item>
        )}
        {!isStockOrderCreate && (
          <Form.Item
            name="payWayNo"
            label="付款方式"
            required
            extra={<div className={styles.formItemLine} />}
          >
            <div
              role="button"
              tabIndex={0}
              className={styles.cell}
              onClick={() => {
                let code = '';
                if (orderTypeNum === 1) {
                  code = orderType ? 'R_001_009_014' : 'R_001_010_010';
                } else {
                  code = orderType ? 'R_001_021_003' : 'R_001_022_003';
                }
                if (!testPerm(code)) {
                  return;
                }
                setShowOrderPayment(true);
              }}
            >
              <div className="label">
                {!paywayInfo.name.length ? (
                  <span className={styles.placeholder}>选择付款方式</span>
                ) : (
                  <span>{paywayInfo.name}</span>
                )}
              </div>
              <Icon name="right" className={styles.icon} />
            </div>
          </Form.Item>
        )}
        {orderTypeNum === 1 && showOrder && (
          <Form.Item
            label="订单类型"
            name="orderTypeId"
            extra={<div className={styles.formItemLine} />}
          >
            <Select
              className={styles.formItem}
              bordered={false}
              options={orderTypes}
              placeholder="选择订单类型"
              allowClear
              showSearch
              fieldNames={{
                label: 'orderTypeName',
                value: 'id',
              }}
              suffixIcon={<Icon name="down" size={16} />}
              filterOption={false}
              onSearch={(e) => {
                orderTypeParams.current = {
                  ...orderTypeParams.current,
                  keyword: e,
                };
                runOrderType({ ...orderTypeParams.current });
              }}
              onChange={() => {
                orderTypeParams.current = {
                  ...orderTypeParams.current,
                  keyword: '',
                };
                runOrderType({ ...orderTypeParams.current });
              }}
            />
          </Form.Item>
        )}
        {showOrder && (
          <Form.Item
            name="projectId"
            label={
              <div className={styles.formItemLabel}>
                <span>项目</span>
                <Switch
                  checked={isProject}
                  onChange={(e) => {
                    let code = '';
                    if (orderTypeNum === 1) {
                      code = orderType ? 'R_001_009_015' : 'R_001_010_011';
                    } else {
                      code = orderType ? 'R_001_021_004' : 'R_001_022_004';
                    }
                    if (!testPerm(code)) {
                      return;
                    }
                    setIsProject(e);
                    onChangeOrderInfo(formInfo.current, e, warehouseNo.current);
                  }}
                />
              </div>
            }
            className={!isProject ? styles.formItemProject : ''}
          >
            {isProject && (
              <>
                <div className={styles.formLabel}>项目选择</div>
                <div
                  role="button"
                  tabIndex={0}
                  className={styles.cell}
                  onClick={() => {
                    if (!formInfo.current.crmCorfId) {
                      message.warning(`请先选择${orderType ? '客户' : '供应商'}`);
                      return;
                    }
                    setShowOrderProject(true);
                  }}
                >
                  <span className="label">
                    <span className={styles.placeholder}>
                      {!projectInfo.projectId ? (
                        <span className={styles.placeholder}>选择项目</span>
                      ) : (
                        <span>{projectInfo.projectName}</span>
                      )}
                    </span>
                    <span />
                  </span>
                  <Icon name="right" className={styles.icon} />
                </div>
              </>
            )}
          </Form.Item>
        )}
      </Form>
      <OrderPayment
        visible={showOrderPayment}
        onClose={() => setShowOrderPayment(false)}
        getPayItem={(e) => {
          const info = e && e.length ? e[0] : initPayway;
          // @ts-ignore
          setPaywayInfo(info);
          formInfo.current = {
            ...formInfo.current,
            payWayNo: info.payWayNo,
          };
          onChangeOrderInfo(formInfo.current, isProject, warehouseNo.current);
          onChangePayWay();
        }}
      />
      <OrderProject
        visible={showOrderProject}
        companyId={user.companyId}
        customerId={formInfo.current.crmCorfId || ''}
        onClose={() => setShowOrderProject(false)}
        getProject={(e) => {
          setProjectInfo({
            projectId: e.id,
            projectName: e.projectName,
          });
          formInfo.current = {
            ...formInfo.current,
            projectId: e.id,
          };
          onChangeOrderInfo(formInfo.current, isProject, warehouseNo.current);
        }}
      />
    </div>
  );
}

OrderCreateInfo.defaultProps = {
  isStockOrderCreate: false,
  showOrder: true,
};

export default OrderCreateInfo;
