.card {
  margin-bottom: 20px;
  padding: 16px 8px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-form-item-label {
      label {
        width: 100%;
        padding: 0 12px;
      }

      .ant-form-item-required {
        padding: 0;
      }
    }

    .ant-select-status-error.ant-select:not(.ant-select-disabled, .ant-select-customize-input)
      .ant-select {
      border-color: transparent !important;
    }

    .ant-select-status-error.ant-select:not(.ant-select-disabled, .ant-select-customize-input)
      .ant-select-selector {
      border-color: transparent !important;
    }

    .ant-select-selection-item,
    .ant-picker-input > input {
      color: #040919 !important;
    }

    .ant-picker {
      width: 100%;
    }
  }
}

.cardFoo {
  :global {
    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-form-item-extra {
      display: none;
    }
  }
}

.formItem {
  font-size: 16px;

  :global {
    & .ant-picker-input > input {
      font-size: 16px;
    }
  }
}

.formItemLine {
  width: calc(100% - 24px);
  height: 1px;
  margin: 12px 12px 16px;
  background-color: #f3f3f3;
}

.cell {
  font-size: 16px;
  display: flex;
  padding: 0 12px;
  justify-content: space-between;
  cursor: pointer;

  & .icon,
  .placeholder {
    color: #b1b3be;
    font-size: 16px;
  }
}

.formItemLabel {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.formItemProject {
  :global {
    .ant-form-item-control-input {
      display: none;
    }
  }
}

.formLabel {
  padding: 4px 12px 16px;
  position: relative;

  &::before {
    content: '*';
    color: red;
    position: absolute;
    top: 2px;
    left: 3px;
  }
}
