import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, message, Modal, Dropdown, Menu } from 'antd';
import { Drawer, Icon, Price } from '@/components';
import {
  StockGoodsListResult,
  OrderUnitResult,
  postAddOrder,
  orderEnterCreate,
  PackageGoodsListResult,
  orderCreateGoodsPrice,
  CartGoodsService,
  getOrderSettingInfo,
} from '@/apis';
import BigNumber from 'big.js';
import dayjs from 'dayjs';
import SelectServer from '@@/order/containers/select-goods-package/select-server';
import { testPerm } from '@/utils/permission';
import debounce from 'lodash/debounce';
import { useRequest } from 'ahooks';
import cloneDeep from 'lodash/cloneDeep';
import SelectGoods from '../select-goods-package';
import OrderCreateInfo from './order-create-info';
import OrderCreateGoodsItem from './order-create-goods-item';
import OrderCreateFile, { FileInfoProps } from './order-create-file';
import PriceWay from './price-way';
import BatchSetup from './batch-setup';
import { orderList } from './order-list';
import styles from './index.module.less';

interface OrderCreateProps {
  visible: boolean;
  onClose: () => void;
  orderType: number; // 0 采购 1 销售
  orderTypeNum: number; // 1 订单 2 退货单
  onConfirm: () => void;
}

function OrderCreate({
  visible,
  orderTypeNum,
  orderType,
  onClose,
  onConfirm,
  ...props
}: OrderCreateProps) {
  const getCode = (type: number) => {
    switch (type) {
      case 1:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_016' : 'R_001_010_012';
        }
        return orderType ? 'R_001_021_005' : 'R_001_022_005';
      case 2:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_017' : 'R_001_010_013';
        }
        return orderType ? 'R_001_021_006' : 'R_001_022_006';
      case 3:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_018' : 'R_001_010_014';
        }
        return orderType ? 'R_001_021_007' : 'R_001_022_007';
      case 4:
        if (orderTypeNum === 1) {
          return orderType ? 'R_001_009_019' : 'R_001_010_015';
        }
        return orderType ? 'R_001_021_008' : 'R_001_022_008';
      default:
        return '';
    }
  };

  const [selectList, setSelectList] = useState<
    {
      id: number;
      name: string;
      iconName: string;
      isChecked: boolean;
      code: string;
    }[]
  >([]);
  // 选择商品
  const [showSelectGoods, setShowSelectGoods] = useState(false);
  const [selectGoodsList, setSelectGoodsList] = useState<StockGoodsListResult[]>([]);
  const [selectGoodsPackageList, setSelectGoodsPackageList] = useState<PackageGoodsListResult[]>(
    []
  );

  const [showSelectServer, setShowSelectServer] = useState(false);
  const [serverList, setServerList] = useState<CartGoodsService[]>([]);
  const [goodsServerId, setGoodsServerId] = useState(0);
  const [goodsServerPackageId, setGoodsServerPackageId] = useState(0);
  const [isNegativeStock, setIsNegativeStock] = useState(false);

  // 定价方式
  const [showPriceWay, setShowPriceWay] = useState(false);
  const [priceWay, setPriceWay] = useState(1);

  // 批量设置
  const [showBatchSetup, setShowBatchSetup] = useState(false);

  const isProject = useRef(false);

  const orderParams = useRef({
    crmCorfId: 0,
    buyerOrSellerComId: 0,
    orderTime: 0,
    warehouseId: 0,
    payWayNo: 0,
    projectId: 0,
    productList: [],
    attachmentUrls: [],
    remark: '',
  });
  const warehouseNo = useRef('');
  const [showSubmitBtn, setShowSubmitBtn] = useState(false);

  const orderTitle = useMemo(() => {
    switch (orderType) {
      case 1:
        return '销售退货单';
      case 0:
        return '采购退货单';
      default:
        return '';
    }
  }, [orderType]);

  const onSeleteType = (id: number) => {
    if (
      (id === 2 || id === 3) &&
      selectGoodsList.length === 0 &&
      selectGoodsPackageList.length === 0
    ) {
      message.error('请先选择商品');
      return;
    }
    switch (id) {
      case 1:
        setShowSelectGoods(true);
        return;
      case 2:
        setShowPriceWay(true);
        return;
      case 3:
        setShowBatchSetup(true);
        return;
      case 4:
        selectList.splice(3, 1);
        setSelectList([...selectList]);
        break;
      default:
        break;
    }
  };

  const getFinaPrice = (priceVal: number, val: number, wey?: number) => {
    let price = new BigNumber(0);
    switch (wey || priceWay) {
      case 1:
        price = price
          .plus(priceVal || 0)
          .times(val || 0)
          .times(0.01);
        break;
      case 2:
        price = price.plus(val);
        break;
      case 3:
        price = price.plus(priceVal || 0).minus(val || 0);
        break;
      default:
        break;
    }
    return price.toNumber();
  };

  /**
   * @description 删除商品
   */
  const onDelete = (val: number, isPackage?: number) => {
    Modal.confirm({
      title: '提示',
      icon: '',
      centered: true,
      width: 290,
      okText: '确定',
      cancelText: '取消',
      getContainer: document.querySelector('.order-create') as HTMLElement,
      content: `确认删除此${isPackage ? '套餐包' : '商品'}`,
      onOk: () => {
        setSelectGoodsList([...selectGoodsList.filter((item) => item.id !== val)]);
        setSelectGoodsPackageList([...selectGoodsPackageList.filter((item) => item.id !== val)]);
      },
    });
  };

  /**
   * @description 值改变
   * @param val
   */
  const onChangePrice = (val: number, id: number) => {
    selectGoodsList.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.priceValue = val;
        items.finalPrice = getFinaPrice(item.productMarketPrice || item.marketPrice, val);
      }
    });
    selectGoodsPackageList.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.priceValue = val;
        items.finalPrice = getFinaPrice(Number(item.price), val);
      }
    });
    setSelectGoodsList([...selectGoodsList]);
    setSelectGoodsPackageList([...selectGoodsPackageList]);
  };

  const { run: getOrderInfo } = useRequest(getOrderSettingInfo, {
    manual: true,
    onSuccess: (res) => {
      setIsNegativeStock(Boolean(res.isNegativeStockEnable));
    },
  });

  /**
   * @description 切换单位
   */
  const onConfirmUnit = (val: OrderUnitResult, id: number) => {
    selectGoodsList.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.priceValue = null;
        items.number = Number(val.number);
        items.minimum = val.productMinimumQuantity;
        items.productMinimumSalesUnit = val.productMinimumSalesUnit;
        items.auxiliaryUnit = val.productUnit;
        items.auxiliaryFloorPrice = Number(val.floorPrice);
        items.productMarketPrice = Number(val.productMarketPrice);
        items.stockNumber = val.stockNumber;
        items.finalPrice = getFinaPrice(Number(val.productMarketPrice), Number(item.priceValue));
      }
    });
    setSelectGoodsList([...selectGoodsList]);
  };

  /**
   * @description 改变数量
   */
  const onChangeNum = (
    val: number,
    id: number,
    saleGroup?: string,
    event?: string,
    itemVals?: any
  ) => {
    if ((orderType === 1 && orderTypeNum === 1) || (orderType === 0 && orderTypeNum === 2)) {
      if (!isNegativeStock && val > Number(itemVals.stockNumber || itemVals.stock)) {
        selectGoodsList.forEach((item) => {
          const items = item;
          if (items.id === id) {
            items.number =
              Number(itemVals.stockNumber || itemVals.stock) > 0
                ? Math.floor(
                    Number(itemVals.stockNumber || itemVals.stock) /
                      Number(itemVals.productMinimumSalesUnit || itemVals.saleGroupStr || 1)
                  ) * Number(itemVals.productMinimumSalesUnit || itemVals.saleGroupStr || 1)
                : 0;
          }
        });
        setSelectGoodsList([...selectGoodsList]);
        message.warning('商品数量不能超过库存');
        return;
      }
    }
    if (
      Number(saleGroup) &&
      BigNumber(val || 0)
        .div(saleGroup || 0)
        .toNumber() %
        1 !==
        0
    ) {
      if (event === 'blur') {
        selectGoodsList.forEach((item) => {
          const items = item;
          if (items.id === id) {
            items.number = BigNumber(Math.ceil(val / Number(saleGroup || 1)))
              .times(Number(saleGroup || 1))
              .toNumber();
          }
        });
        setSelectGoodsList([...selectGoodsList]);
        message.error('请输入最小销售单元整数倍');
        return;
      }
    }
    selectGoodsList.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.number = val;
      }
    });
    selectGoodsPackageList.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.number = val;
        items.skuList.forEach((item1) => {
          if (item1.serverGoods && item1.serverGoods.length) {
            item1.serverGoods.forEach((item2) => {
              item2.serviceStandardProductVOS.forEach((server) => {
                const servers = server;
                servers.serviceNumber = String(
                  BigNumber(Number(item1.quantity || 0))
                    .div(Number(item1.saleGroup) || 1)
                    .times(item.number || 0)
                    .toNumber()
                );
              });
            });
          }
        });
      }
    });
    setSelectGoodsList([...selectGoodsList]);
    setSelectGoodsPackageList([...JSON.parse(JSON.stringify(selectGoodsPackageList))]);
  };

  /**
   * @description 改变服务
   */
  const onChangeServer = (arr: CartGoodsService[], id: number, packageId?: number) => {
    setServerList([...arr]);
    setGoodsServerId(id);
    setGoodsServerPackageId(packageId || 0);
    setShowSelectServer(true);
  };

  const onConfirmServer = (arr: CartGoodsService[]) => {
    selectGoodsList.forEach((item) => {
      const items = item;
      if (items.id === goodsServerId) {
        items.serverGoods = arr;
      }
    });
    selectGoodsPackageList.forEach((item) => {
      if (item.id === goodsServerPackageId) {
        item.skuList.forEach((each) => {
          const each1 = each;
          if (each1.shopSkuId === goodsServerId) {
            each1.serverGoods = arr.map((item1) => ({
              ...item1,
              serviceStandardProductVOS: item1.serviceStandardProductVOS.map((server) => ({
                ...server,
                serviceNumber: `${BigNumber(each.quantity || 0)
                  .div(Number(each.saleGroup) || 1)
                  .times(item.number || 0)
                  .toNumber()}`,
              })),
            }));
          }
        });
      }
    });
    setSelectGoodsList([...selectGoodsList]);
    setSelectGoodsPackageList([...selectGoodsPackageList]);
  };

  /**
   * @description 批量设置
   */
  const onBatchSetup = (price: number, number: number) => {
    selectGoodsList.forEach((item) => {
      const items = item;
      if (priceWay === 3) {
        items.priceValue =
          price > Number(item.productMarketPrice || item.marketPrice)
            ? item.productMarketPrice || item.marketPrice
            : price;
      } else {
        items.priceValue = price;
      }
      items.finalPrice = getFinaPrice(
        item.productMarketPrice || item.marketPrice,
        items.priceValue
      );
      items.number =
        Math.ceil(number / Number(item.productMinimumSalesUnit || item.saleGroup || 1)) *
        Number(item.productMinimumSalesUnit || item.saleGroup || 1);
    });
    selectGoodsPackageList.forEach((item) => {
      const items = item;
      items.priceValue = price > Number(item.price) ? Number(item.price) : price;
      items.number = number;
      items.finalPrice = getFinaPrice(Number(item.price), items.priceValue);
    });
    setSelectGoodsList([...selectGoodsList]);
    setSelectGoodsPackageList([...selectGoodsPackageList]);
  };

  /**
   * @description 选择商品带出价格
   */
  const getGoodsPrice = (
    val: StockGoodsListResult[],
    packageList: PackageGoodsListResult[],
    wey?: number,
    isChangePrice?: boolean
  ) => {
    orderCreateGoodsPrice({
      crmCorfId: orderParams.current.crmCorfId,
      supplementEnterType: orderType ? 1 : 2,
      orderFuncType: orderTypeNum === 1 ? 4 : 7,
      querySupplementPriceParams: [
        ...val.map((item) => ({
          skuId: item.skuId,
          pricingMethod: wey || priceWay,
        })),
        ...packageList.map((item) => ({
          packageId: item.id,
          pricingMethod: wey || priceWay,
        })),
      ],
    }).then((res) => {
      val.forEach((good) => {
        const goods = good;
        if (res.querySupplementPriceVOS.length && !goods.prefectureInfo) {
          res.querySupplementPriceVOS.forEach((item) => {
            if (item.skuId === goods.skuId && !goods.prefectureInfo) {
              goods.priceValue = item.pricingValue || null;
              goods.finalPrice = getFinaPrice(
                goods.productMarketPrice || goods.marketPrice,
                item.pricingValue,
                wey
              );
            }
          });
        } else if (goods.prefectureInfo && goods.prefectureInfo.priceFactor && !isChangePrice) {
          goods.priceValue = good.prefectureInfo.priceFactor;
          let type = 0;
          switch (goods.prefectureInfo.calculateType) {
            case 1:
              type = 3;
              break;
            case 2:
              type = 1;
              break;
            case 5:
              type = 2;
              break;
            default:
              break;
          }
          goods.finalPrice = getFinaPrice(
            goods.productMarketPrice || goods.marketPrice,
            goods.prefectureInfo.priceFactor,
            type
          );
        } else {
          goods.priceValue = null;
          goods.finalPrice = 0;
          // @ts-ignore
          goods.prefectureInfo = null;
        }
      });
      packageList.forEach((good) => {
        const goods = good;
        if (res.querySupplementPriceVOS.length) {
          res.querySupplementPriceVOS.forEach((item) => {
            if (item.packageId === goods.id) {
              goods.priceValue = item.pricingValue || null;
              goods.finalPrice = getFinaPrice(Number(goods.price), item.pricingValue);
            }
          });
        } else {
          goods.priceValue = null;
          goods.finalPrice = 0;
        }
      });
      setSelectGoodsList([...val]);
      setSelectGoodsPackageList([...packageList]);
    });
  };

  /**
   * @description 选择商品
   * @param goodsVal
   * @param packageList
   */
  const onSelectGoods = (
    goodsVal: StockGoodsListResult[],
    packageList: PackageGoodsListResult[]
  ) => {
    setShowSelectGoods(false);
    if (orderParams.current.crmCorfId) {
      getGoodsPrice(goodsVal, packageList);
    }
    setSelectGoodsList([...goodsVal]);
    setSelectGoodsPackageList([...packageList]);
  };

  /**
   * @description 选择方式
   */
  const onSelectPriceWay = (e: number) => {
    setPriceWay(e);
    switch (e) {
      case 1:
        selectList[1].name = '按折扣定价';
        break;
      case 2:
        selectList[1].name = '按一口价定价';
        break;
      case 3:
        selectList[1].name = '按减金额定价';
        break;
      default:
        break;
    }
    selectGoodsList.forEach((item) => {
      const items = item;
      items.priceValue = null;
      items.finalPrice = 0;
    });
    selectGoodsPackageList.forEach((item) => {
      const items = item;
      items.priceValue = null;
      items.finalPrice = 0;
    });
    setSelectList([...selectList]);
    // setSelectGoodsList([...selectGoodsList]);
    // setSelectGoodsPackageList([...selectGoodsPackageList]);
    if (orderParams.current.crmCorfId) {
      getGoodsPrice(selectGoodsList, selectGoodsPackageList, e, true);
    }
  };

  /**
   * @description 获取订单信息
   */
  const onChangeOrderInfo = (e: any, val: boolean, warehouse: string) => {
    isProject.current = val;
    orderParams.current = {
      ...orderParams.current,
      ...e,
    };
    warehouseNo.current = warehouse || '';
  };

  const onChangeOrderCustomer = (id: number) => {
    if (id && (selectGoodsList.length || selectGoodsPackageList.length)) {
      if (orderType === 1 && orderTypeNum === 1) {
        selectGoodsList.forEach((item) => {
          const items = item;
          if (item.prefectureInfo) {
            items.priceValue = null;
            items.finalPrice = 0;
            // @ts-ignore
            items.prefectureInfo = null;
          }
        });
        setSelectGoodsList(cloneDeep(selectGoodsList));
      } else {
        getGoodsPrice(selectGoodsList, selectGoodsPackageList, priceWay, true);
      }
    }
  };

  const onChangePayWay = () => {
    if (orderType === 1 && orderTypeNum === 1) {
      selectGoodsList.forEach((item) => {
        const items = item;
        if (item.prefectureInfo) {
          items.priceValue = null;
          items.finalPrice = 0;
          // @ts-ignore
          items.prefectureInfo = null;
        }
      });
      setSelectGoodsList(cloneDeep(selectGoodsList));
    }
  };

  /**
   * @description 获取附件信息
   */
  const onChangeFile = (e: FileInfoProps) => {
    orderParams.current = {
      ...orderParams.current,
      // @ts-ignore
      attachmentUrls: e.attachmentUrls.map((item) => ({
        fileName: item.name,
        size: item.size,
        fileUrl: item.url,
        fileType: item.type?.indexOf('image') !== -1 ? 0 : 1,
      })),
      remark: e.remark,
    };
  };

  /**
   * @description 生成订单
   */
  const onSubmit = debounce(() => {
    if (!orderParams.current.crmCorfId) {
      message.warning(`请选择${orderType ? '客户' : '供应商'}`);
      return;
    }
    if (!orderParams.current.orderTime) {
      message.warning('请选择开单时间');
      return;
    }
    if (!orderParams.current.warehouseId && orderTypeNum === 2) {
      message.warning(`请选择${orderType ? '入' : '出'}库仓库`);
      return;
    }
    if (!orderParams.current.payWayNo) {
      message.warning('请选择付款方式');
      return;
    }
    if (isProject.current && !orderParams.current.projectId) {
      message.warning('请选择项目');
      return;
    }
    if (selectGoodsList.length === 0 && selectGoodsPackageList.length === 0) {
      message.warning('请选择商品');
      return;
    }
    if (selectGoodsList.some((item) => !item.number)) {
      message.warning('请填写商品数量');
      return;
    }
    if (selectGoodsList.some((item) => !item.finalPrice)) {
      message.warning('商品单价不能小于0');
      return;
    }
    if (
      selectGoodsList.some(
        (item) =>
          item.finalPrice < Number(item.auxiliaryFloorPrice || item.floorPrice) &&
          Number(item.auxiliaryFloorPrice || item.floorPrice) > 0
      )
    ) {
      message.warning('商品单价不能小于底价');
      return;
    }
    if (selectGoodsPackageList.some((item) => !item.number)) {
      message.warning('请填写套餐包数量');
      return;
    }
    if (selectGoodsPackageList.some((item) => item.finalPrice < 0)) {
      message.warning('套餐包单价不能小于0');
      return;
    }
    const fn = orderTypeNum === 1 ? postAddOrder : orderEnterCreate;
    const goodsArr = selectGoodsList.map((item) => ({
      shopSkuId: item.id,
      skuId: item.skuId,
      number: item.number,
      productPrice: item.priceValue?.toFixed(priceWay === 1 ? 0 : 6),
      productUnit: item.auxiliaryUnit || item.unit,
      pricingMethod: priceWay,
      payWayNo: orderParams.current.payWayNo,
      orderServiceProductParams:
        item.serverGoods && item.serverGoods.length
          ? item.serverGoods
              .map((tag2: any) => tag2.serviceStandardProductVOS)
              .reduce((a: any, b: any) => a.concat(b))
              .map((service: any) => ({
                skuId: service.skuId,
                shopSkuId: service.shopSkuId,
                number: BigNumber(item.number || 0).div(
                  Number(item.productMinimumSalesUnit) || item.saleGroup || 1
                ),
              }))
          : [],
    }));
    const packageArr: any = [];
    selectGoodsPackageList.forEach((item) => {
      item.skuList.forEach((each) => {
        packageArr.push({
          skuId: each.skuId,
          shopSkuId: each.shopSkuId,
          number: each.quantity,
          packageId: item.id,
          productPrice: item.priceValue?.toFixed(priceWay === 1 ? 0 : 6),
          productUnit: each.unit,
          pricingMethod: priceWay,
          packageNumber: item.number,
          isGift: each.isGift,
          groupId: each.groupId,
          payWayNo: each.currentPayWayNo,
          orderServiceProductParams:
            each.serverGoods && each.serverGoods.length
              ? each.serverGoods
                  .map((tag2: any) => tag2.serviceStandardProductVOS)
                  .reduce((a: any, b: any) => a.concat(b))
                  .map((service: any) => ({
                    skuId: service.skuId,
                    shopSkuId: service.shopSkuId,
                    number: service.serviceNumber,
                  }))
              : [],
        });
      });
    });
    const params = {
      orderEnterType: 1,
      supplementEnterType: orderType ? 1 : 2,
      ...orderParams.current,
      orderTime: dayjs(orderParams.current.orderTime).valueOf(),
      productList: [...goodsArr, ...packageArr],
    };
    setShowSubmitBtn(true);
    // @ts-ignore
    fn(params, '')
      .then(() => {
        message.success('新建成功');
        onClose();
        setSelectGoodsList([]);
        setSelectGoodsPackageList([]);
        onConfirm();
      })
      .finally(() => {
        setShowSubmitBtn(false);
      });
  }, 300);

  /**
   * @description 获取商品的价格
   */

  const totalNum = useMemo(() => {
    let num = new BigNumber(0);
    selectGoodsList.forEach((item) => {
      num = num.plus(BigNumber(Number(item.finalPrice || 0)).times(item.number || 0));
      item.serverGoods?.forEach((each) => {
        each.serviceStandardProductVOS?.forEach((server) => {
          num = num.plus(
            BigNumber(Number(server.serviceProductPrice || 0)).times(
              BigNumber(item.number || 0).div(
                Number(item.productMinimumSalesUnit) || item.saleGroup || 1
              )
            )
          );
        });
      });
    });
    selectGoodsPackageList.forEach((item) => {
      num = num.plus(BigNumber(Number(item.finalPrice || 0)).times(item.number || 0));
      item.skuList.forEach((each1) => {
        if (each1.serverGoods && each1.serverGoods.length) {
          each1.serverGoods.forEach((each2) => {
            each2.serviceStandardProductVOS?.forEach((server) => {
              num = num.plus(
                BigNumber(Number(server.serviceProductPrice || 0)).times(
                  Number(server.serviceNumber || 0)
                )
              );
            });
          });
        }
      });
    });
    return num.toNumber();
  }, [selectGoodsList, selectGoodsPackageList]);

  const goodsNum = useMemo(() => {
    let num: number = selectGoodsList.length;
    selectGoodsPackageList.forEach((item) => {
      num += item.skuList.length;
    });
    return num;
  }, [selectGoodsList, selectGoodsPackageList]);

  const footer = (
    <div className={styles.footer}>
      <div>
        合计：
        <span className={styles.totalPrice}>
          <Price value={totalNum} symbol="" format />
        </span>
        <div className={styles.totalNum}>共{goodsNum}种商品</div>
      </div>
      <Button disabled={showSubmitBtn} type="primary" onClick={onSubmit}>
        {orderTypeNum === 1 ? '生成补录订单' : `生成${orderType ? '销售退' : '采购退'}货单`}
      </Button>
    </div>
  );

  useEffect(() => {
    if (visible) {
      const initSelectData = [
        {
          id: 1,
          name: '添加商品',
          iconName: 'plus-outline',
          isChecked: false,
          code: getCode(1),
        },
        {
          id: 2,
          name: '按折扣定价',
          iconName: 'share',
          isChecked: false,
          code: getCode(2),
        },
        {
          id: 3,
          name: '批量设置',
          iconName: 'control',
          isChecked: false,
          code: getCode(3),
        },
        {
          id: 4,
          name: '附件备注',
          iconName: 'clip',
          isChecked: false,
          code: getCode(4),
        },
      ];
      setPriceWay(1);
      setSelectList([...initSelectData]);
      setSelectGoodsList([]);
      setSelectGoodsPackageList([]);
      getOrderInfo();
    }
  }, [visible]); // eslint-disable-line

  const extra = (
    <Dropdown
      placement="bottomRight"
      overlay={
        <Menu
          onClick={() => {
            orderList({
              buyOrSeller: orderType ? 1 : 2,
              onConfirm: (arr) => {
                const hasIds = selectGoodsList.map((item) => item.skuId);
                const ids: number[] = [];
                selectGoodsList.forEach((item) => {
                  const items = item;
                  arr.forEach((each) => {
                    if (
                      item.skuId === each.skuId &&
                      !item.isAdd &&
                      (item.auxiliaryUnit || item.unit) === each.auxiliaryUnit
                    ) {
                      items.number = BigNumber(Number(item.number)).add(each.number).toNumber();
                    }
                    if (
                      item.skuId === each.skuId &&
                      (item.auxiliaryUnit || item.unit) !== each.auxiliaryUnit
                    ) {
                      ids.push(item.skuId);
                    }
                  });
                });
                const selectListArr = selectGoodsList.filter((item) => !ids.includes(item.skuId));
                const val = arr
                  .filter((item) => !hasIds.includes(item.skuId) || ids.includes(item.skuId))
                  .map((item) => ({
                    ...item,
                    id: item.shopSkuId,
                    skuId: item.skuId,
                    name: item.productName,
                    images: [item.productMainPic],
                    standardList: item.productSpecification,
                    marketPrice: Number(item.mainMarketPriceStr),
                    number: BigNumber(Math.ceil(item.number / Number(item.saleGroup || 1)))
                      .times(Number(item.saleGroup || 1))
                      .toNumber(),
                    unit: item.unit,
                    auxiliaryUnit: item.auxiliaryUnit,
                    unitTemplateId: item.unitTemplateId,
                    saleGroup: item.mainSaleGroupStr,
                    saleGroupStr: item.mainSaleGroupStr,
                    stock: item.stock,
                    isAdd: true,
                    productMinimumSalesUnit: Number(item.saleGroup),
                    productMarketPrice: Number(item.marketPrice),
                    floorPrice: item.mainFloorPriceStr,
                    auxiliaryFloorPrice: item.floorPrice,
                  }));
                // @ts-ignore
                setSelectGoodsList([...val, ...selectListArr]);
              },
            });
          }}
          items={[
            {
              key: '1',
              label: '导入新建',
            },
          ]}
        />
      }
    >
      <Icon size={20} name="zu13366" className={styles.more} />
    </Dropdown>
  );

  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      {...props}
      title={orderTypeNum === 1 ? '新建订单' : orderTitle}
      className={styles.orderCreate}
      footer={footer}
      push={false}
      extra={orderTypeNum === 2 && extra}
    >
      <div className="order-create">
        <OrderCreateInfo
          orderType={orderType}
          orderTypeNum={orderTypeNum}
          onChangeOrderInfo={onChangeOrderInfo}
          onChangeOrderCustomer={onChangeOrderCustomer}
          onChangePayWay={onChangePayWay}
        />
        <div className={styles.card}>
          {selectList.map((item) => (
            <div
              key={item.id}
              role="button"
              tabIndex={item.id}
              className={styles.selectTypeItem}
              onClick={() => {
                if (!testPerm(item.code)) {
                  return;
                }
                onSeleteType(item.id);
              }}
            >
              {item.id === 2 ? (
                <img
                  className={styles.selectTypeItemImg}
                  src="https://img.huahuabiz.com/user_files/2023210/1675996841631934.png"
                  alt=""
                />
              ) : (
                <Icon name={item.iconName} className={styles.selectTypeItemIcon} />
              )}
              <div>{item.name}</div>
            </div>
          ))}
        </div>
        {selectGoodsList.map((item) => (
          <OrderCreateGoodsItem
            isPackage={false}
            key={item.id}
            id={item.id}
            skuId={item.skuId}
            name={item.name}
            image={item.images || item.imagesList[0]}
            standard={item.standardList}
            minimum={item.minimum}
            saleGroup={item.saleGroup}
            saleGroupStr={item.saleGroupStr}
            productMinimumSalesUnit={item.productMinimumSalesUnit}
            unit={item.auxiliaryUnit || item.unit}
            floorPrice={item.floorPrice}
            auxiliaryFloorPrice={item.auxiliaryFloorPrice}
            stock={item.stock}
            stockNumber={item.stockNumber}
            unitTemplateId={item.unitTemplateId}
            number={item.number}
            priceWay={priceWay}
            marketPrice={item.marketPrice}
            productMarketPrice={item.productMarketPrice}
            priceValue={item.priceValue}
            finalPrice={item.finalPrice}
            onDelete={onDelete}
            onConfirmPrice={onChangePrice}
            onConfirmUnit={onConfirmUnit}
            onChangeNum={(val, id, saleGroup, event) =>
              onChangeNum(val, id, saleGroup, event, item)
            }
            goodsServers={item.serverGoods}
            onChangeServer={onChangeServer}
            prefectureInfo={item.prefectureInfo}
          />
        ))}
        {selectGoodsPackageList.map((item) => (
          <OrderCreateGoodsItem
            isPackage
            key={item.id}
            id={item.id}
            name={item.name}
            image={item.imageList[0]}
            introduce={item.introduce}
            marketPrice={+item.price}
            priceValue={item.priceValue}
            finalPrice={item.finalPrice}
            number={item.number}
            priceWay={priceWay}
            onDelete={(val) => onDelete(val, 1)}
            onConfirmPrice={onChangePrice}
            onConfirmUnit={onConfirmUnit}
            onChangeNum={onChangeNum}
            skuList={item.skuList}
            onChangeServer={(servers, id) => onChangeServer(servers, id, item.id)}
          />
        ))}
        {selectList.length === 3 && <OrderCreateFile onChangeFile={onChangeFile} />}
        <SelectGoods
          visible={showSelectGoods}
          confirm={onSelectGoods}
          onClose={() => setShowSelectGoods(false)}
          selectGoodsList={selectGoodsList}
          selectGoodsPackageList={selectGoodsPackageList}
          orderType={orderType}
          orderTypeNum={orderTypeNum}
          warehouseNo={warehouseNo.current}
          isInitData={visible}
          isNegativeStockEnable={
            (orderType === 1 && orderTypeNum === 1) || (orderType === 0 && orderTypeNum === 2)
          }
          targetCompanyId={
            orderParams.current.buyerOrSellerComId && orderType === 1 && orderTypeNum === 1
              ? `${orderParams.current.buyerOrSellerComId}`
              : ''
          }
          payWayNo={orderParams.current.payWayNo ? `${orderParams.current.payWayNo}` : ''}
        />
        <SelectServer
          visible={showSelectServer}
          serverList={serverList}
          goodsServerId={goodsServerId}
          onClose={() => setShowSelectServer(false)}
          onConfirm={onConfirmServer}
        />
        <PriceWay
          visible={showPriceWay}
          priceWay={priceWay}
          onClose={() => setShowPriceWay(false)}
          onConfirm={onSelectPriceWay}
          goodsList={selectGoodsList}
          packageList={selectGoodsPackageList}
        />
        <BatchSetup
          visible={showBatchSetup}
          priceWay={priceWay}
          onClose={() => setShowBatchSetup(false)}
          onConfirm={onBatchSetup}
        />
      </div>
    </Drawer>
  );
}

export default OrderCreate;
