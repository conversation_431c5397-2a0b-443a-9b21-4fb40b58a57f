import { useMemo, useState } from 'react';
import { Drawer, Icon } from '@/components';
import { Stepper } from 'antd-mobile';
import { InputNumber } from 'antd';

import styles from './index.module.less';

interface BatchSetupProps {
  visible: boolean;
  isNum?: boolean;
  priceWay: number;
  onClose: () => void;
  onConfirm: MultipleParamsFn<[price: number, number: number]>;
}

function BatchSetup({ visible, isNum, priceWay, onClose, onConfirm }: BatchSetupProps) {
  const [price, setPrice] = useState(0);
  const [number, setNumber] = useState(0);

  const priceWayLabel = useMemo(() => {
    switch (priceWay) {
      case 1:
        return '折扣';
      case 2:
        return '一口价';
      case 3:
        return '优惠金额';
      default:
        return '';
    }
  }, [priceWay]);

  const onOk = () => {
    onConfirm(price, number);
    onClose();
  };

  return (
    <Drawer
      visible={visible}
      title="批量设置"
      width={375}
      height={300}
      placement="bottom"
      getContainer={false}
      closeIcon={null}
      mask={visible}
      className={styles.drawerBox}
      extra={<Icon name="close" className={styles.close} style={{}} onClick={onClose} />}
      footer={<Drawer.Footer okText="确定" showCancel={false} onOk={onOk} />}
    >
      <div className={styles.card}>
        <div className={styles.label}>{priceWayLabel}</div>
        <InputNumber
          min={0}
          max={priceWay === 1 ? 100 : 999999999}
          type="number"
          controls={false}
          bordered={false}
          placeholder="请输入"
          addonBefore={priceWay !== 1 && <span className={styles.icon}>￥</span>}
          addonAfter={priceWay === 1 && <span className={styles.icon}>%</span>}
          onChange={(e) => setPrice(Number(e))}
        />
      </div>
      {isNum && (
        <div className={styles.card}>
          <div className={styles.label}>数量</div>
          <Stepper allowEmpty min={0} max={999999999} onChange={(e) => setNumber(Number(e))} />
        </div>
      )}
    </Drawer>
  );
}

BatchSetup.defaultProps = {
  isNum: true,
};

export default BatchSetup;
