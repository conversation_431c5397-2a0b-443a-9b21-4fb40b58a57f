.drawerBox {
  position: absolute;
}

.close {
  color: #999eb2;
  font-size: 20px;
  cursor: pointer;
}

.card {
  display: flex;
  margin-bottom: 20px;
  padding: 12px 20px;
  justify-content: space-between;
  align-items: center;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;

  &:last-child {
    margin-bottom: 0;
  }

  :global {
    .ant-input-number-group {
      width: 120px;
      height: 36px;
      line-height: 36px;
      border-radius: 10px;
      background: #f5f6fa;
    }

    .ant-input-number-input:placeholder-shown,
    .ant-input-number-input {
      height: 36px;
    }

    .ant-input-group-wrapper {
      width: 120px;
      border-radius: @border-radius-xxs;
      background: #f5f6fa;
    }

    .ant-input-number-group-addon {
      border: none;
      border-radius: 10px;
      background: #f5f6fa;
    }

    .ant-input-number-group .ant-input-number {
      border-radius: 10px;
    }

    .ant-input-group-addon {
      background: #f5f6fa;
    }

    .ant-input-group-addon {
      border: none;
    }

    .adm-stepper {
      width: 170px;
      height: 36px;
      border-radius: 10px;
      background-color: #f5f6fa;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      color: #000;
    }
  }
}

.label {
  font-size: @font-size-lg;
}
