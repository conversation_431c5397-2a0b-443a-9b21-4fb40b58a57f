.drawer {
  :global {
    .ant-drawer-body {
      display: flex;
      flex-direction: column;
    }
  }
}

.search {
  padding: 8px 0 12px;
}

.list {
  flex: 1;
  overflow: auto;
}

.spin {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }

    .ant-spin-spinning {
      top: 50% !important;
      transform: translate(0, -50%) !important;
    }
  }
}

.noData {
  display: flex;
  height: 100%;
  margin-top: -50px;
  justify-content: center;
  align-items: center;
}

.loader {
  padding-top: 20px;
  text-align: center;
}
