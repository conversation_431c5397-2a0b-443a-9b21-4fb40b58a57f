import { useEffect, useRef, useState } from 'react';
import { Drawer, Search, Empty } from '@/components';
import { DrawerProps, Spin, Divider, message } from 'antd';
import debounce from 'lodash/debounce';
import { orderReturnList, OrderReturnListResult, OrderProductVOListResult } from '@/apis';
import { useRequest } from 'ahooks';
import InfiniteScroll from 'react-infinite-scroll-component';
import OrderItem from '@/src/order/containers/sales-purchase-create/order-item';
import { drawerPopup } from '@/utils/popup';
import styles from './index.module.less';

export interface OrderListProps extends DrawerProps {
  onClose?: () => void;
  onConfirm: MultipleParamsFn<[goodsList: OrderProductVOListResult[]]>;
  buyOrSeller: number;
}

function OrderList({ onConfirm, buyOrSeller, onClose, ...props }: OrderListProps) {
  const [orderList, setOrderList] = useState<OrderReturnListResult[]>([]);
  const params = useRef({
    pageNo: 1,
    pageSize: 10,
    key: '',
    buyOrSeller,
  });
  const totalPages = useRef(0);
  const [hasSelectIds, setHasSelectIds] = useState<number[]>([]);
  const [goodsList, setGoodsList] = useState<OrderProductVOListResult[]>([]);

  const { run, loading } = useRequest(orderReturnList, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      totalPages.current = res.pagination.total;
      if (params.current.pageNo === 1) {
        setOrderList(res.list);
      } else {
        setOrderList([...orderList, ...res.list]);
      }
      params.current.pageNo += 1;
    },
  });

  const onSearch = debounce((e: string) => {
    params.current.key = e;
    params.current.pageNo = 1;
    run({ ...params.current });
  }, 300);

  const onLoadMore = () => {
    if (!(params.current.pageNo <= totalPages.current)) return;
    run({ ...params.current });
  };

  const onSelectOrder = (val: boolean, orderNo: number) => {
    if (val) {
      setHasSelectIds([orderNo]);
      setGoodsList(orderList.filter((item) => item.orderNo === orderNo)[0].orderProductVOList);
    } else {
      setHasSelectIds([]);
      setGoodsList([]);
    }
  };

  const onOk = debounce(() => {
    if (!hasSelectIds.length) {
      message.warning('请选择订单');
    }
    onConfirm(goodsList);
    onClose?.();
  }, 300);

  useEffect(() => {
    run({ ...params.current });
  }, [run]);

  const footer = (
    <Drawer.Footer okText="确定" disabled={!hasSelectIds.length} showCancel={false} onOk={onOk} />
  );

  return (
    <Drawer
      onClose={onClose}
      title={`选择${buyOrSeller === 1 ? '销售' : '采购'}订单`}
      {...props}
      className={styles.drawer}
      footer={footer}
    >
      <div className={styles.search}>
        <Search placeholder="请输入订单号" onSearch={onSearch} />
      </div>
      <div id="list" className={styles.list}>
        <Spin spinning={params.current.pageNo === 1 && loading} wrapperClassName={styles.spin}>
          {orderList.length > 0 ? (
            <InfiniteScroll
              dataLength={orderList.length}
              hasMore={params.current.pageNo < totalPages.current}
              loader={
                <div className={styles.loader}>
                  <Spin tip="加载中..." />
                </div>
              }
              next={onLoadMore}
              scrollableTarget="list"
              endMessage={
                orderList.length > 0 && (
                  <div className={styles.divider}>
                    <Divider plain>
                      <span className={styles.endMessage}>加载到底了</span>
                    </Divider>
                  </div>
                )
              }
            >
              {orderList.map((item) => (
                <OrderItem
                  checked={hasSelectIds.includes(item.orderNo)}
                  key={item.orderNo}
                  orderTitle={item.buyerOrSellerCompanyName}
                  orderStatusStr={item.orderStatusStr}
                  orderNumber={item.orderNumber}
                  totalPrice={item.totalPayment}
                  productCatCount={item.quantityKind}
                  goodsList={item.orderProductVOList.map((goods) => ({
                    image: goods.productMainPic,
                    name: goods.productName,
                    standard: goods.productSpecification,
                    price: goods.productPrice,
                    number: goods.number,
                    unit: goods.auxiliaryUnit || goods.unit,
                  }))}
                  onSelectOrder={(val) => onSelectOrder(val, item.orderNo)}
                />
              ))}
            </InfiniteScroll>
          ) : (
            <div className={styles.noData}>
              <Empty />
            </div>
          )}
        </Spin>
      </div>
    </Drawer>
  );
}

OrderList.defaultProps = {
  onClose: () => {},
};

export const orderList = (props: OrderListProps) => {
  drawerPopup(OrderList, props);
};

export default orderList;
