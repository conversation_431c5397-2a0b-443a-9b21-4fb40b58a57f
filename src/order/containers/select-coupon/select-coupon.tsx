import { useEffect, useState } from 'react';
import { Drawer, Empty } from '@/components';
import { DrawerProps, Checkbox, Spin } from 'antd';
import { mcsCouponList, McsCouponListResult } from '@/apis';
import { useRequest } from 'ahooks';
import dayjs from 'dayjs';
import { isFunction } from 'lodash';
import styles from './select-coupon.module.less';

export interface SelectCouponProps extends DrawerProps {
  companyId: number;
  shopSkuIdSet: number[];
  couponCode: string;
  coupons: string[];
  onConfirm: MultipleParamsFn<[couponCode: string, denomination: string]>;
}

const activityCouponPng = 'https://img.huahuabiz.com/user_files/2023717/1689572949959787.png';
const shopCouponPng = 'https://img.huahuabiz.com/user_files/2023714/1689302657540393.png';

function SelectCoupon({
  companyId,
  shopSkuIdSet,
  couponCode,
  coupons,
  onConfirm,
  ...props
}: SelectCouponProps) {
  const [list, setList] = useState<McsCouponListResult[]>([]);
  const [currentInfo, setCurrentInfo] = useState({
    couponCode: couponCode || '',
    denomination: '',
  });

  const { run, loading } = useRequest(mcsCouponList, {
    manual: true,
    defaultParams: [{ companyIds: [companyId], shopSkuIdSet }],
    onSuccess: (res) => {
      const arr = res.list.filter(
        (item) =>
          !coupons.includes(item.couponCode) || (item.couponCode === couponCode && couponCode)
      );
      arr.forEach((item) => {
        if (item.couponCode === currentInfo.couponCode) {
          setCurrentInfo({
            couponCode: currentInfo.couponCode,
            denomination: item.denomination,
          });
        }
      });
      setList([...arr]);
    },
  });

  const onSelectCoupon = (val: boolean, code: string, denomination: string) => {
    setCurrentInfo({
      couponCode: val ? code : '',
      denomination: val ? denomination : '',
    });
  };

  const onOk = () => {
    onConfirm(currentInfo.couponCode, currentInfo.denomination);
    if (isFunction(props.onClose)) {
      props.onClose();
    }
  };

  useEffect(() => {
    run({ companyIds: [companyId], shopSkuIdSet });
  }, [companyId, shopSkuIdSet, run]);

  const footer = (
    <Drawer.Footer
      okText="确定"
      showCancel={false}
      // disabled={!currentInfo.couponCode.length}
      onOk={onOk}
    />
  );

  return (
    <Drawer {...props} title="优惠券列表" footer={footer}>
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        {list.length > 0 &&
          list.map((item) => (
            <div className={styles.item} key={item.id}>
              <img
                className={styles.itemTitle}
                src={item.source === 1 ? activityCouponPng : shopCouponPng}
                alt=""
              />
              <div className={styles.content}>
                <div className={styles.contentLeft}>
                  <div className={styles.price}>¥{item.denomination}</div>
                  <div>无门槛</div>
                </div>
                <div>
                  <div className={styles.contentRight}>{item.name}</div>
                  <div>{dayjs(item.expireTime).format('YYYY.MM.DD  hh.mm.ss')}前有效</div>
                </div>
              </div>
              <div className={styles.checkBox}>
                <Checkbox
                  className={styles.checked}
                  checked={currentInfo.couponCode === item.couponCode}
                  onChange={(e) =>
                    onSelectCoupon(e.target.checked, item.couponCode, item.denomination)
                  }
                />
              </div>
            </div>
          ))}
        {list.length === 0 && (
          <div className={styles.noData}>
            <Empty />
          </div>
        )}
      </Spin>
    </Drawer>
  );
}

export default SelectCoupon;
