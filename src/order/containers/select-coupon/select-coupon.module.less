.spin {
  height: 100%;

  :global {
    .ant-spin-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
    }
  }
}

.item {
  display: flex;
  height: 90px;
  margin-bottom: 16px;
  padding: 0 16px 0 18px;
  justify-content: space-between;
  position: relative;
  align-items: center;
  border-radius: 8px;
  background-color: #ffeee9;

  &::after {
    content: '';
    width: 12px;
    height: 12px;
    position: absolute;
    right: 40px;
    bottom: -6px;
    background-color: #f5f6fa;
    border-radius: 50%;
  }

  &::before {
    content: '';
    width: 12px;
    height: 12px;
    position: absolute;
    top: -6px;
    right: 40px;
    background-color: #f5f6fa;
    border-radius: 50%;
  }
}

.itemTitle {
  width: 46px;
  height: 18px;
  position: absolute;
  top: 0;
  left: 0;
}

.content {
  color: #ff5923;
  display: flex;
  align-items: center;
}

.contentLeft {
  text-align: center;
  margin-right: 28px;
}

.contentRight {
  margin-bottom: 10px;
}

.price {
  font-size: 22px;
  font-weight: 600;
}

.checkBox {
  display: flex;
  height: 40px;
  padding-left: 12px;
  align-items: center;
  border-left: 1px dashed #ff5923;
}

.checked {
  :global {
    .ant-checkbox-checked,
    .ant-checkbox-inner {
      border-radius: 50%;
    }

    .ant-checkbox-checked .ant-checkbox-inner {
      background-color: #ff5923;
      border-color: #ff5923;
    }
  }
}

.noData {
  display: flex;
  height: 100%;
  margin-top: -50px;
  justify-content: center;
  align-items: center;
}
