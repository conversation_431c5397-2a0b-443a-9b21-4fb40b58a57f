@import 'styles/mixins/mixins';

.drawer {
  :global {
    & .ant-spin-nested-loading,
    & .ant-spin-nested-loading .ant-spin-container {
      height: 100%;
    }
  }
}

.search {
  margin: 5px 0 16px;
}

.list {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto;
}

.card,
.isEdit {
  margin-bottom: 20px;
  padding: 20px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
  background: #fff;
  border: 1px solid #fff;
  cursor: pointer;

  .title {
    color: #040910;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
  }
}

.isEdit {
  position: relative;
  border: 1px solid #008cff;

  &::before {
    content: '';
    width: 42px;
    height: 39px;
    position: absolute;
    top: -1px;
    right: -1px;
    background-image: url('../../img/selected-mark.png');
  }
}

.item {
  display: flex;
  padding: 4px 0;

  .itemLabel {
    color: #888b98;
  }

  .head {
    width: 210px;
    .text-overflow();
  }
}

.footer {
  padding: 0 20px 24px;

  .btn {
    width: 100%;
  }
}

.endMessage {
  color: #888b98;
  font-size: 12px;
}
