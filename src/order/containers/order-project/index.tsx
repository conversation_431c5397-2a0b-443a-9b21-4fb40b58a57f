import { getProjectList, ListResult } from '@/apis/measure';
import { Drawer, Empty, Search } from '@/components';
import { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spin } from 'antd';
import dayjs from 'dayjs';
import { DrawerRefType } from '@/components/drawer/drawer';
import styles from './index.module.less';

interface OrderProjectProps {
  visible: boolean;
  ids?: number;
  companyId: number;
  customerId?: number | string;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  getProject: (item: ListResult) => void;
}

function OrderProject(props: OrderProjectProps) {
  const drawerRef = useRef<DrawerRefType>(null);
  const { visible, onClose, getProject, ids, companyId, customerId } = props;
  const [projectList, setProjectList] = useState<ListResult[]>([]);
  const [cateNo, setCateNo] = useState(true);
  const params = useRef({
    pageSize: 10,
    pageNo: 1,
    companyId,
    projectName: '',
  });

  const getList = (isSearch?: number) => {
    getProjectList({ ...params.current, customerId }).then((res) => {
      const list = res.list.map((item) => ({
        ...item,
        isEdit: false,
      }));
      setCateNo(
        params.current.pageNo <= Math.floor(res.pagination.count / 10) && res.list.length <= 10
      );
      if (isSearch) {
        setProjectList(list.map((item) => ({ ...item, isEdit: item.id === ids })));
      } else {
        setProjectList(
          projectList.concat(list).map((item) => ({ ...item, isEdit: item.id === ids }))
        );
      }
    });
  };

  // 表单改变
  const formChange = (chooseId: number) => {
    if (!drawerRef.current?.getIsChange()) {
      drawerRef.current?.setIsChange(true);
    }
    if (chooseId === ids) {
      drawerRef.current?.setIsChange(false);
    }
  };

  const selectedProject = (id: number) => {
    const arr = projectList.map((item) => ({
      ...item,
      isEdit: item.id === id,
    }));
    setProjectList([...arr]);
  };

  const onsubmit = () => {
    projectList.forEach((item) => {
      if (item.isEdit) {
        getProject(item);
        onClose();
      }
    });
  };

  const loadMore = () => {
    params.current.pageNo += 1;
    getList();
  };

  useEffect(() => {
    if (visible) {
      getList();
    } else {
      setProjectList([]);
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      ref={drawerRef}
      title="选择项目"
      visible={visible}
      onClose={onClose}
      footer={
        projectList.length && (
          <div className={styles.footer}>
            <Button type="primary" className={styles.btn} onClick={onsubmit}>
              确定
            </Button>
          </div>
        )
      }
      className={styles.drawer}
    >
      <div
        id="list"
        // style={{ height: `${document.querySelector('.ant-drawer-body')?.scrollHeight}px` }}
        className={styles.list}
      >
        <Search
          placeholder="输入项目名称"
          value={params.current.projectName}
          className={styles.search}
          onSearch={(value) => {
            params.current = {
              pageNo: 1,
              pageSize: 10,
              companyId,
              projectName: value,
            };
            getList(1);
          }}
        />
        {projectList.length ? (
          <InfiniteScroll
            dataLength={projectList.length}
            hasMore={cateNo}
            loader={
              <div className="text-center">
                <Spin tip="加载中..." />
              </div>
            }
            endMessage={
              <div className={styles.divider}>
                <Divider plain>
                  <span className={styles.endMessage}>加载到底了</span>
                </Divider>
              </div>
            }
            next={loadMore}
            scrollableTarget="list"
          >
            {projectList.map((item) => (
              <div
                className={item.isEdit ? styles.isEdit : styles.card}
                role="button"
                tabIndex={0}
                key={item.areaName}
                onClick={() => {
                  selectedProject(item.id);
                  formChange(item.id);
                }}
              >
                <div className={styles.title}>{item.projectName}</div>
                <div className={styles.item}>
                  <div className={styles.itemLabel}>项目编号：</div>
                  <div>{item.projectNumber}</div>
                </div>
                <div className={styles.item}>
                  <div className={styles.itemLabel}>项目类型：</div>
                  <div className={styles.head}>{item.projectType}</div>
                </div>
                <div className={styles.item}>
                  <div className={styles.itemLabel}>客户名称：</div>
                  <div>{item.clientName}</div>
                </div>
                <div className={styles.item}>
                  <div className={styles.itemLabel}>项目负责人：</div>
                  <div className={styles.head}>
                    {item.userDetailsVos.map((it, index) => (
                      <span key={it.memberId}>
                        {it.name}
                        {index === item.userDetailsVos.length - 1 ? null : <span>、</span>}
                      </span>
                    ))}
                  </div>
                </div>
                <div className={styles.item}>
                  <div className={styles.itemLabel}>开始时间：</div>
                  <div>{item.startTime ? dayjs(item.startTime).format('YYYY-MM-DD') : null}</div>
                </div>
                <div className={styles.item}>
                  <div className={styles.itemLabel}>结束时间：</div>
                  <div>{item.endTime ? dayjs(item.endTime).format('YYYY-MM-DD') : null}</div>
                </div>
                <div className={styles.item}>
                  <div className={styles.itemLabel}>地址：</div>
                  <div>
                    {item.provinceName}
                    {item.cityName}
                    {item.areaName}
                    {item.address}
                  </div>
                </div>
              </div>
            ))}
          </InfiniteScroll>
        ) : (
          <Empty
            type="content"
            message="暂无相关内容"
            description="新建项目后才能选择"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
            }}
          />
        )}
      </div>
    </Drawer>
  );
}

OrderProject.defaultProps = {
  ids: 0,
  customerId: '',
};

export default OrderProject;
