import { getOrderTrimVail, listVailResult } from '@/apis/order';
import { Drawer } from '@/components';
import { Button } from 'antd';
import { Stepper } from 'antd-mobile';
import { PropsWithChildren, useEffect, useState } from 'react';
import GoodsItem from '../../components/goods-card/index';
import styles from './index.module.less';

interface OrderAddGoodsProps {
  visible: boolean;
  goodsId: number;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  confirm: (arr: listVailResult[]) => void;
}

function OrderAddGoods({
  visible,
  onClose,
  confirm,
  goodsId,
}: PropsWithChildren<OrderAddGoodsProps>) {
  const [trimParams] = useState({ pageSize: 999, pageNo: 1, orderNo: goodsId, searchKey: '' });
  const [trimList, setTrimList] = useState<listVailResult[]>([]);
  const [trimListSelect, setTrimListSelect] = useState<listVailResult[]>([]);
  const [priceQueen, setPriceQueen] = useState(0);

  const getPrice = (list: listVailResult[]) => {
    const price = list.reduce(
      (value, item) => value + Number(item.number) * Number(item.productPrice),
      0
    );
    setPriceQueen(price);
  };

  const updateNum = (e: number, item: listVailResult) => {
    const list = trimList.map((m) => {
      if (m.shopSkuId === item.shopSkuId) {
        return {
          ...m,
          number: e ? String(e) : item.number,
        };
      }
      return { ...m };
    });
    setTrimList(list);
    setTrimListSelect(list.filter((f) => f.number !== '0'));
    getPrice(list);
  };

  useEffect(() => {
    if (visible) {
      getOrderTrimVail(trimParams).then((res) => {
        setTrimList(res.list);
      });
    }
  }, [visible]); // eslint-disable-line
  return (
    <Drawer
      title="添加商品"
      visible={visible}
      onClose={() => {
        onClose();
      }}
      footer={
        <div className={styles.footer}>
          <div>
            <div>
              合计：<span className={styles.fontRed}>￥{priceQueen.toFixed(2)}</span>
            </div>
            <div className={styles.fontGray}>已选{trimListSelect.length}种商品</div>
          </div>
          <Button
            type="primary"
            role="button"
            tabIndex={0}
            onClick={() => {
              confirm(trimListSelect);
            }}
          >
            确定
          </Button>
        </div>
      }
    >
      <div className={styles.tip}>以下商品来源于当前订单店铺，可从该店铺继续添加商品</div>
      {trimList.map((item) => (
        <div className={styles.card} key={item.number}>
          <div className={styles.goodsItem}>
            <GoodsItem
              orderType="sales"
              img={item.productMainPic}
              productName={item.productName}
              productSpecificationList={item.productSpecificationList}
              productPrice={item.productPrice}
              productUnit={item.productUnit}
              number={item.productMinimumSalesUnit}
            />
          </div>
          <div className={styles.cardFooter}>
            <div className={styles.textNumber}>输入数量</div>
            <Stepper
              max={99999999}
              digits={item.priceNum}
              step={Number(item.productMinimumSalesUnit ? item.productMinimumSalesUnit : 1)}
              value={Number(item.number)}
              onChange={(e) => {
                updateNum(e, item);
              }}
              onBlur={(e) => {
                updateNum(Number(e.target.value), item);
              }}
            />
          </div>
        </div>
      ))}
    </Drawer>
  );
}

export default OrderAddGoods;
