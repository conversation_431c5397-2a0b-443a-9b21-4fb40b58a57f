.tip {
  color: #888b98;
  font-size: 12px;
  margin: 16px 0;
}

.card {
  margin-bottom: 20px;
  padding: 0 20px 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .goodsItem {
    border-bottom: 1px solid #f3f3f3;
  }
}

.cardFooter {
  display: flex;
  margin-top: 16px;
  justify-content: space-between;
  align-items: center;

  :global {
    .adm-stepper-middle {
      border: none;
    }

    .adm-stepper {
      width: 135px;
      border-radius: 10px;
    }

    .adm-button:not(.adm-button-default).adm-button-fill-none {
      color: black;
    }
  }

  .textNumber {
    color: #888b98;
  }
}

.footer {
  display: flex;
  padding: 20px;
  justify-content: space-between;
  align-items: center;

  .fontRed {
    color: #ea1c26;
  }

  .fontGray {
    color: #888b98;
    font-size: 12px;
    margin-top: 4px;
  }
}
