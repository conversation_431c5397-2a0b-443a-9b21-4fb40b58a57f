import { stockWarehouseList, WarehouseListResult } from '@/apis';
import {
  getOrderAllActionPoints,
  OrderHandingBtnListResult,
  postSupplementOrderPoint,
  ResultItem,
  // postOrderSupplement,
  postOrderCancel,
  postOrderOperatePoint,
} from '@/apis/order';
import { Drawer, Icon, Price } from '@/components';
import { Popover, Modal, Button, Select, message, Spin } from 'antd';
import {
  forwardRef,
  Fragment,
  PropsWithChildren,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { useNavigate } from 'react-router-dom';
import classNames from 'classnames';
import { testPerm } from '@/utils/permission';
import { buyRecord, sellRecord } from '../../utils/permission';
import OrderPay from '../order-pay';
import styles from './index.module.less';

interface orderProps {
  visible: boolean;
  orderNumber: string;
  orderNo: number;
  nycbngOrgId: string;
  type: string | null;
  distributionCompanyId?: number;
  customerCompanyId?: number;
  onClose: () => void;
  onOrderAdjust: () => void;
  onGoodsPartial: () => void;
  getOrderList: () => void;
  onChangePrice: () => void;
  // eslint-disable-next-line no-unused-vars
  onScheduling: (orderNo: number, batchNo?: number, onScheduling?: number) => void;
}

interface btnS {
  // isAllActionBtn: boolean;
  isBatchesActionPointBtn: boolean;
}

export interface widthRefType {
  getOrderAction: () => void;
}
// 自定义组件需要通过forwardRef hook 向外面暴露ref
const OrderDealWith = forwardRef<widthRefType, PropsWithChildren<orderProps>>(
  (
    {
      visible,
      orderNumber,
      orderNo,
      type,
      nycbngOrgId,
      distributionCompanyId,
      customerCompanyId,
      onClose,
      onOrderAdjust,
      onGoodsPartial,
      getOrderList,
      onChangePrice,
      onScheduling,
    },
    ref
  ) => {
    const params = useRef({
      pageSize: 10,
      pageNo: 1,
      total: 0,
    });

    const data = useRef({
      flowSerialNo: 0,
      warehouseId: 0,
      showPopover: false,
      batchNo: 0,
    });
    const { confirm } = Modal;
    const navigate = useNavigate();
    const [orderList, setOrderList] = useState<ResultItem[][]>([]);
    const [PopoverBtn, setPopoverBtn] = useState<OrderHandingBtnListResult[]>([]);
    const [showWarehouse, setShowWarehouse] = useState(false);
    const [warehouseList, setWarehouseList] = useState<WarehouseListResult[]>([]);
    const [showOrderPay, setShowOrderPay] = useState(false);
    const [flowId, setFlowId] = useState<number>(0);
    const [actionCodeId, setActionCodeId] = useState<number | undefined>(0);
    const [batchNoId, setBatchNoId] = useState<number | undefined>();
    const [hovered, setHovered] = useState(false);
    const [loading, setLoading] = useState(false);
    const [btn, setBtn] = useState<btnS[][]>([]);
    const [isHaveBatchPerm, setIsHaveBatchPerm] = useState(0);

    const getOrderAction = () => {
      setOrderList([]);
      setLoading(true);
      getOrderAllActionPoints({ orderNo }, nycbngOrgId)
        .then((res) => {
          const list: ResultItem[][] = [];
          setIsHaveBatchPerm(res.isHaveBatchPerm);
          res.allActionList.forEach((item) => {
            for (let i = 0; i < list.length; i += 1) {
              if (list[i].length > 0) {
                for (let j = 0; j < list[i].length; j += 1) {
                  if (list[i][j].uiGroupId === item.uiGroupId) {
                    list[i].push(item);
                    return;
                  }
                }
              }
            }
            list.push([item]);
          });

          const btnList = list.map((btnItem) =>
            btnItem.map(() => ({
              // isAllActionBtn: btnItem.every((item) => !!item.actionPointList.length),
              isBatchesActionPointBtn: btnItem.every((item) => !!item.actionPointList.length)
                ? true
                : btnItem.every((item) => {
                    const foo = item.batchesActionPointList.filter(
                      (itemE) => !itemE.batchActionPointList.filter((f) => f.actionPointList.length)
                    );
                    return !!foo.length;
                  }),
            }))
          );
          setBtn(btnList);
          setOrderList(list);
          setPopoverBtn(res.orderHandingBtnList);
          setLoading(false);
        })
        .finally(() => {
          setLoading(false);
        });
    };

    useImperativeHandle(ref, () => ({
      getOrderAction,
    }));

    const getWarehouseList = () => {
      stockWarehouseList({
        companyId: distributionCompanyId || undefined,
        customerCompanyId: customerCompanyId || undefined,
        functionType: 1,
      }).then((res) => {
        params.current.pageNo += 1;
        params.current.total = res.pagination.total;
        setWarehouseList(res.list);
        setShowWarehouse(true);
      });
    };

    const onOrderPoint = () => {
      if (!data.current.warehouseId) {
        message.warning('请选择仓库');
        return;
      }
      postSupplementOrderPoint(
        {
          flowSerialNo: data.current.flowSerialNo,
          orderNo,
          warehouseId: data.current.warehouseId,
          batchNo: data.current.batchNo || null,
        },
        nycbngOrgId
      )
        .then(() => {
          postOrderOperatePoint(
            {
              orderNo,
              flowSerialNo: data.current.flowSerialNo,
              warehouseId: data.current.warehouseId,
              batchNo: data.current.batchNo || null,
            },
            nycbngOrgId
          )
            .then((e) => {
              if (e.processInstanceId) {
                navigate('/admin/process/initiate');
                return;
              }
              if (e.msg) {
                message.success(e.msg);
              }
              data.current.warehouseId = 0;
              getOrderAction();
              setShowWarehouse(false);
            })
            .catch((e) => {
              message.error(e.message);
            });
        })
        .catch((e) => {
          message.error(e.message);
        });
    };

    const showModal = (
      name: string,
      flowSerialNo?: number,
      batchNo?: number,
      actionCode?: number,
      isSetGray?: number,
      isInOa?: number
    ) => {
      if (actionCode) {
        let permCode: string | string[] = '';
        if (type === '1') {
          permCode = sellRecord[actionCode];
        } else if (type === '0') {
          permCode = buyRecord[actionCode];
        }
        if (permCode && !testPerm(permCode)) return;
      }
      if (isSetGray) return;
      if (
        actionCode === 2020 ||
        actionCode === 1005 ||
        actionCode === 1111 ||
        actionCode === 1001 ||
        actionCode === 1002 ||
        isInOa
      ) {
        postSupplementOrderPoint(
          {
            flowSerialNo,
            orderNo,
            batchNo: batchNo || null,
          },
          nycbngOrgId
        )
          .then((res) => {
            if (res.processInstanceId) {
              navigate('/admin/process/initiate');
              return;
            }
            if (res.isNeedPayPayee) {
              setFlowId(flowSerialNo || 0);
              setBatchNoId(batchNo);
              setActionCodeId(actionCode);
              setShowOrderPay(true);
              return;
            }
            if (actionCode === 2020) {
              onScheduling(orderNo, batchNo, flowSerialNo);
            } else {
              data.current.flowSerialNo = flowSerialNo || 0;
              data.current.batchNo = batchNo || 0;
              getWarehouseList();
            }
          })
          .catch((e) => {
            if (e.code === 2060) {
              navigate(`/finance/gathering/orderPaymen?orderNumber=${orderNumber}`);
            }
            if (e.code === 2050) {
              confirm({
                content: `此订单需要分销商垫资，是否跳转财务管理处垫资`,
                width: 290,
                centered: true,
                getContainer: document.querySelector('.ant-drawer-wrapper-body') as HTMLElement,
                onOk() {
                  navigate(`/finance/payment/orderPaymen?orderNumber=${orderNumber}`);
                },
              });
            } else {
              message.error(e.message);
            }
          });
        return;
      }
      Modal.confirm({
        title: '提示',
        content: `是否${name}`,
        icon: null,
        width: 290,
        centered: true,
        className: styles.showModal,
        cancelText: '取消',
        okText: '确定',
        getContainer: '#order-dealwith .ant-drawer-wrapper-body',
        onOk() {
          if (actionCode && flowSerialNo) {
            postSupplementOrderPoint(
              {
                flowSerialNo,
                orderNo,
                batchNo: batchNo || null,
              },
              nycbngOrgId
            )
              .then((res) => {
                if (res.processInstanceId) {
                  navigate('/admin/process/initiate');
                  return;
                }
                if (res.isNeedPayPayee) {
                  setFlowId(flowSerialNo);
                  setBatchNoId(batchNo);
                  setActionCodeId(0);
                  setShowOrderPay(true);
                } else {
                  postOrderOperatePoint(
                    {
                      orderNo,
                      flowSerialNo,
                      batchNo: batchNo || null,
                    },
                    nycbngOrgId
                  )
                    .then((e) => {
                      if (e.processInstanceId) {
                        navigate('/admin/process/initiate');
                        return;
                      }
                      if (e.msg) {
                        message.success(e.msg);
                      }
                      getOrderAction();
                    })
                    .catch((e) => {
                      message.error(e.message);
                    });
                }
              })
              .catch((e) => {
                if (e.code === 2050) {
                  Modal.confirm({
                    content: `此订单需要分销商垫资，是否跳转财务管理处垫资`,
                    width: 290,
                    centered: true,
                    cancelText: '取消',
                    okText: '确定',
                    getContainer: '#order-dealwith .ant-drawer-wrapper-body',
                    onOk() {
                      navigate(`/finance/payment/orderPaymen?orderNumber=${orderNumber}`);
                    },
                  });
                } else {
                  message.error(e.message);
                }
              });
          }
          if (name === '取消订单') {
            postOrderCancel(
              {
                orderNo,
                cancelReasonId: 1,
                isPutBack: 0,
              },
              nycbngOrgId
            ).then(() => {
              onClose();
              message.success('取消成功');
              // @ts-ignore
              getOrderList();
            });
          }
        },
        onCancel() {},
      });
    };

    const onHiddenBtn = (code: number, name: string) => {
      let permCode: string | string[] = '';
      if (type === '1') {
        permCode = sellRecord[code];
      } else if (type === '0') {
        permCode = buyRecord[code];
      }
      if (permCode && !testPerm(permCode)) return;
      setHovered(false);
      switch (code) {
        case 1501:
          onOrderAdjust();
          break;
        case 1311:
          onGoodsPartial();
          break;
        case 1403:
          showModal(name);
          break;
        case 1709:
          onChangePrice();
          break;
        case 1505:
          navigate(
            `/order/change/record?orderNo=${orderNo}&current=${
              type === '1' ? 'sell' : 'buy'
            }&nycbngOrgId=${nycbngOrgId}`
          );
          break;
        default:
          break;
      }
    };

    const handleHoverChange = (boole: boolean) => {
      setHovered(boole);
    };

    useEffect(() => {
      if (visible) {
        getOrderAction();
      }
    }, [visible]); // eslint-disable-line

    return (
      <Drawer
        id="order-dealwith"
        title="订单处理"
        visible={visible}
        push={false}
        onClose={() => {
          onClose();
          getOrderList();
        }}
        extra={
          PopoverBtn.length ? (
            <Popover
              visible={hovered}
              trigger="hover"
              onVisibleChange={handleHoverChange}
              overlayClassName={styles.popover}
              content={
                <div className={styles.popoverBtn}>
                  {PopoverBtn.map((item) => (
                    <div
                      className={styles.items}
                      key={item.actionCode}
                      role="button"
                      tabIndex={0}
                      onClick={() => {
                        onHiddenBtn(item.actionCode, item.actionName);
                      }}
                    >
                      {item.actionName}
                    </div>
                  ))}
                </div>
              }
            >
              <Icon name="zu13366" className={styles.font} />
            </Popover>
          ) : null
        }
      >
        <Modal
          visible={showWarehouse}
          className={styles.modal}
          title="选择仓库"
          getContainer={false}
          width="312px"
          centered
          onCancel={() => {
            setShowWarehouse(false);
          }}
          onOk={onOrderPoint}
        >
          <Select
            options={warehouseList.map((item) => ({
              label: item.warehouseName,
              value: item.id,
            }))}
            onChange={(id) => {
              data.current.warehouseId = id;
            }}
            onPopupScroll={() => {
              if (params.current.pageNo > params.current.total) return;
              getWarehouseList();
            }}
          />
        </Modal>
        <Spin tip="Loading..." spinning={loading}>
          <div className={styles.title}>
            <div className="mb-5">订单编号：{orderNumber}</div>
            {/* <div className={styles.prompt}>可操作以下任意节点，填写补录信息</div> */}
          </div>
          <div className={styles.orderList}>
            {orderList.map((item, itemIndex) => (
              <>
                {item.map((it, itIndex) => (
                  <div
                    className={it.batchesActionPointList.length ? styles.batchBigCard : styles.card}
                  >
                    <div
                      className={
                        btn[itemIndex][itIndex].isBatchesActionPointBtn
                          ? styles.item
                          : styles.completeItem
                      }
                      key={it.uiGroupId}
                    >
                      <div className={it.groupName === '分批处理' ? styles.batchTip : styles.tip}>
                        <div
                          className={
                            btn[itemIndex][itIndex].isBatchesActionPointBtn
                              ? styles.nodeTitle
                              : styles.completeNodeTitle
                          }
                        >
                          <span className={it.groupName === '分批处理' ? styles.batch : ''}>
                            {it.groupName}
                          </span>
                          <span className={it.groupName === '分批处理' ? styles.batchFont : ''}>
                            {it.groupName === '分批处理' ? `(${it.groupDescription})` : null}
                          </span>
                        </div>
                        {it.groupName === '分批处理' ? (
                          <div
                            className={styles.batchFont}
                            role="button"
                            tabIndex={0}
                            onClick={() => {
                              if (!isHaveBatchPerm) {
                                message.error('暂无权限，请联系公司管理员开通！');
                                return;
                              }
                              onHiddenBtn(1311, '货物分批');
                            }}
                          >
                            详情
                            <Icon name="right" />
                          </div>
                        ) : null}
                      </div>
                      {it.batchesActionPointList.map((i) => (
                        <Fragment key={i.batchNo}>
                          <div className={styles.batchHeader}>
                            <span className={styles.batchTitle}>
                              {i.batchIndex === 0 ? '未分批商品' : `批次${i.batchIndex}`}
                            </span>
                            <span className={styles.viceTitle}>
                              （共{i.batchProCatNum}种&nbsp;&nbsp;&nbsp;
                              <Price
                                value={i.batchTotalPriceStr}
                                format
                                color={it.actionPointList.length ? '#EA1c26' : '#040919'}
                              />
                              ）
                            </span>
                          </div>
                          {i.batchActionPointList.map((e) => (
                            <div className={styles.batchCard}>
                              <div className={styles.cardTitle}>{e.groupName}</div>
                              <div className={styles.cardContext}>
                                <span>{e.groupDescription}</span>
                                {e.groupTotalPriceStr && (
                                  <Price value={e.groupTotalPriceStr} format color="#888b98" />
                                )}
                              </div>
                              <div className={styles.batchBtn}>
                                {e.actionPointList.map((m, index) => (
                                  <Button
                                    type={
                                      e.actionPointList.length > 1 && index === 0
                                        ? 'default'
                                        : 'primary'
                                    }
                                    size="small"
                                    style={{ marginLeft: '10px' }}
                                    className={classNames(
                                      e.actionPointList.length > 1 && index === 0
                                        ? styles.btnBorder
                                        : '',
                                      m.isSetGray ? styles.btnIsSetGray : ''
                                    )}
                                    onClick={() => {
                                      showModal(
                                        m.actionName,
                                        m.flowSerialNo,
                                        i.batchNo,
                                        m.actionCode,
                                        m.isSetGray,
                                        m.isInOa
                                      );
                                    }}
                                  >
                                    {m.actionName}
                                  </Button>
                                ))}
                              </div>
                            </div>
                          ))}
                        </Fragment>
                      ))}
                      <div
                        className={styles.context}
                        style={!it.actionPointList.length ? {} : { marginBottom: '16px' }}
                      >
                        <div>{it.groupName === '分批处理' ? null : it.groupDescription}</div>
                        {Number(it.groupTotalPriceStr) ? (
                          <Price
                            value={it.groupTotalPriceStr}
                            format
                            color={it.actionPointList.length ? '#EA1c26' : '#040919'}
                          />
                        ) : null}
                      </div>
                      <div className={styles.btnBlock}>
                        {it.actionPointList.map((i, idx) => (
                          <Button
                            type={
                              idx === 0 && it.actionPointList.length > 1 ? 'default' : 'primary'
                            }
                            size="small"
                            className={classNames(
                              styles.btn,
                              i.isSetGray ? styles.btnIsSetGray : ''
                            )}
                            onClick={() => {
                              showModal(
                                i.actionName,
                                i.flowSerialNo,
                                undefined,
                                i.actionCode,
                                i.isSetGray,
                                i.isInOa
                              );
                            }}
                            key={i.isSetGray}
                          >
                            {i.actionName}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </>
            ))}
          </div>
        </Spin>

        <OrderPay
          visible={showOrderPay}
          flowId={flowId}
          orderNo={orderNo}
          batchNoId={batchNoId}
          actionCodeId={actionCodeId}
          nycbngOrgId={nycbngOrgId || ''}
          distributionCompanyId={distributionCompanyId}
          onClose={() => {
            setActionCodeId(0);
            setShowOrderPay(false);
          }}
          payFinish={() => {
            getOrderAction();
            setShowOrderPay(false);
          }}
        />
      </Drawer>
    );
  }
);

OrderDealWith.defaultProps = {
  distributionCompanyId: undefined,
  customerCompanyId: undefined,
};

export default OrderDealWith;
