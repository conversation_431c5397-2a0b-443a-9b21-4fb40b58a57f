.popover {
  padding-top: 0 !important;
  top: 80px !important;

  :global {
    .ant-modal-confirm .ant-modal-body {
      padding: 20px;
    }

    .ant-popover-arrow {
      display: none;
    }
  }
}

.popoverBtn {
  text-align: center;

  .items {
    padding: 8px;
    cursor: pointer;

    &:hover {
      border-radius: 10px;
      background: rgb(217 238 255 / 30%);
    }
  }
}

.font {
  font-size: 24px;
}

.title {
  padding-left: 46px;
  text-align: left;

  .prompt {
    color: #888b98;
    font-size: 12px;
    margin: 8px 0 16px;
  }
}

.orderList {
  padding: 0 16px 0 46px;

  .batchHeader,
  .completeBatchHeader {
    margin-bottom: 12px;
    padding-top: 12px;
    position: relative;

    &:last-child {
      margin-bottom: 0;
      position: relative;
    }

    &::after {
      content: '';
      width: 1px;
      height: calc(100%);
      position: absolute;
      top: -17px;
      left: -42px;
      background: #b1b3be;
    }
  }

  .completeBatchHeader {
    &::after {
      content: '';
      width: 1px;
      height: calc(100%);
      position: absolute;
      top: -17px;
      left: -42px;
      background: #008cff;
    }
  }

  .batchTitle {
    color: #040919;
    font-size: 16px;
    margin-right: 8px;
    margin-bottom: 12px;
  }

  .viceTitle {
    font-size: 12px;
  }

  .batchCard {
    margin-bottom: 12px;
    padding: 20px 16px;
    border-radius: 12px;
    background: #f5f6fa;

    &:last-child {
      margin-bottom: 24px;
    }
  }

  .cardTitle {
    margin-bottom: 8px;
  }

  .cardContext {
    color: #888b98;
    display: flex;
    margin-bottom: 16px;
    justify-content: space-between;
  }

  .batchBtn {
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }

  .btnBorder {
    border: 1px solid #b1b3be;
  }
}

.card,
.batchBigCard {
  margin-bottom: 20px;
  padding: 20px 16px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
  background: #fff;

  &:last-child {
    .item,
    .completeItem {
      &::after {
        display: none;
      }
    }
  }
}

.batchBigCard {
  padding-bottom: 0;

  .completeItem {
    &::after {
      content: '';
      width: 1px;
      height: calc(100% + 40px);
      position: absolute;
      top: 16px;
      left: -42px;
      background: #008cff;
    }
  }
}

.completeItem,
.item {
  position: relative;

  &::after {
    content: '';
    width: 1px;
    height: calc(100% + 45px);
    position: absolute;
    top: 16px;
    left: -42px;
    background: #008cff;
  }
}

.item {
  &::after {
    top: 17px;
    background: #b1b3be;
  }
}

.tip,
.batchTip {
  font-family: PingFangSC-Medium;
  font-size: 16px;
}

.batchTip {
  display: flex;
  padding-bottom: 16px;
  justify-content: space-between;
  position: relative;
  align-items: center;
  // margin-bottom: 16px;
  border-bottom: 1px solid #f5f6fa;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: -50px;
  //   width: 18px;
  //   height: 18px;
  //   background-image: url(../../img/point.png);
  // }
}

.batch {
  color: #040919;
  font-size: 16px;
  margin-right: 8px;
  font-family: PingFangSC-Medium;
}

.batchFont {
  font-size: 12px;
  cursor: pointer;
}

.context {
  display: flex;
  margin-top: 16px;
  justify-content: space-between;
  position: relative;

  // &::before {
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: -50px;
  //   width: 18px;
  //   height: 18px;
  //   background-image: url(../../img/point.png);
  // }
}

.nodeTitle,
.completeNodeTitle {
  position: relative;

  &::before {
    content: '';
    width: 16px;
    height: 16px;
    position: absolute;
    top: 0;
    left: -50px;
    background-image: url(../../img/point.png);
  }
}

.completeNodeTitle {
  &::before {
    width: 16px;
    height: 16px;
    background-image: url(../../img/completePoitn.png);
  }
}

.btnBlock {
  display: flex;
  justify-content: flex-end;
  position: relative;
  align-items: center;

  .btn {
    margin-left: 16px;
  }
}

.modal {
  :global {
    .ant-modal-header {
      border-bottom: none;
    }

    .ant-modal-footer {
      border-top: none;
    }

    .wrapClassName {
      position: relative;
    }

    .ant-select {
      width: 100%;
    }
  }
}

.btnIsSetGray {
  background: #c6ccd8 !important;
  color: #fff;
  cursor: no-drop;
  border: none !important;

  &:hover {
    background: #c6ccd8 !important;
    color: #fff !important;
  }
}
