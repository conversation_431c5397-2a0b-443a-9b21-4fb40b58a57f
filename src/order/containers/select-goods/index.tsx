import {
  PropsWithChildren,
  useEffect,
  useRef,
  useState,
  useCallback,
  useReducer,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Search, Drawer, Icon, Empty } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Button, Spin, Checkbox, Select, message, Modal, Divider } from 'antd';
import { Stepper } from 'antd-mobile';

import {
  getCategoryCustomeList,
  GetCategoryCustomeItemResult,
  stockGoodsList,
  StockGoodsListResult,
  formulaConvert,
  FormulaConvertDetaiResult,
  getOrderUnitConversion,
  formulaConvertManyconvert,
  GetCategoryCustomeListParams,
} from '@/apis';
import { StockGoodsListParams } from '@/apis/psi/stock-goods-list';
import classNames from 'classnames';
import BigNumber from 'big.js';
import cart from '../../assets/img/cart.png';
import GoodsItem from '../../components/goods-item';
import styles from './index.module.less';

const pageSize = 10;

export interface SelectGoodsRefType {
  setNotCloseConfirm: SimpleFn<boolean>;
}

interface SelectGoodsPropps {
  visible: boolean;
  distributionCompanyId?: number;
  orderNo?: number;
  // eslint-disable-next-line no-unused-vars
  confirm: (arr: StockGoodsListResult[]) => void | Promise<void>;
  onClose: () => void;
  selectGoodsList: StockGoodsListResult[];
  nycbngOrgId?: string;
  isAdjust?: boolean;
}

interface InitialState {
  customizeCategorySet: Array<number | null>;
  keyword: string;
  pageNo: number;
  totalPage: number;
  list: StockGoodsListResult[];
}

const initialState = {
  customizeCategorySet: [],
  keyword: '',
  pageNo: 1,
  totalPage: 1,
  list: [],
};

type GoodsAction =
  | {
      type: 'setPage';
      payload: { pageNo?: number; totalPage?: number };
    }
  | {
      type: 'setList';
      payload: StockGoodsListResult[];
    }
  | {
      type: 'setAll';
      payload: InitialState;
    };

const reducer = (state: InitialState, action: GoodsAction): InitialState => {
  switch (action.type) {
    case 'setPage':
      return { ...state, ...action.payload };
    case 'setList':
      return { ...state, list: action.payload };
    case 'setAll':
      return action.payload;
    default:
      return state;
  }
};

const SelectGoods = forwardRef<SelectGoodsRefType, PropsWithChildren<SelectGoodsPropps>>(
  (
    {
      visible,
      selectGoodsList,
      distributionCompanyId,
      confirm,
      onClose,
      orderNo,
      nycbngOrgId,
      isAdjust,
      ...props
    },
    ref
  ) => {
    const [categoryCustomes, setCategoryCustomes] = useState(
      null as null | GetCategoryCustomeItemResult[]
    );
    const [categoryCustomesTwo, setCategoryCustomesTwo] = useState(
      null as null | GetCategoryCustomeItemResult[]
    );
    const content = useRef(null as unknown as HTMLDivElement);
    const [selectIds, setSelectIds] = useState(Array<number | string | boolean>);
    const [selectCategoryId, setSelectCategoryId] = useState(null as null | number);
    const [selectCategoryIdTwo, setSelectCategoryIdTwo] = useState(null as null | number);
    const parentIdRef = useRef(0);
    const [units, setUnits] = useState<FormulaConvertDetaiResult[]>([]);
    const [state, dispatch] = useReducer(reducer, initialState);
    const [showMore, setShowMore] = useState(false);
    const hasSelectGoodsList = useRef<StockGoodsListResult[]>([]);
    const notCloseConfirm = useRef(false);

    const productSum = useMemo(() => {
      const hasIds = hasSelectGoodsList.current.map((each) => each.skuId);
      state.list.forEach((each) => {
        if (selectIds.includes(each.skuId)) {
          if (!hasIds.includes(each.skuId)) {
            hasSelectGoodsList.current.push(each);
          } else {
            for (let i = 0; i < hasSelectGoodsList.current.length; i += 1) {
              if (hasSelectGoodsList.current[i].skuId === each.skuId) {
                hasSelectGoodsList.current[i].number = each.number;
              }
            }
          }
        }
      });
      let count = new BigNumber(0);
      hasSelectGoodsList.current
        .filter((item) => selectIds.includes(item.skuId))
        .forEach((item) => {
          count = count.plus(item.number ? item.number : 0);
        });
      return count.toFixed(6);
    }, [state.list, selectIds]);

    const getCategoryCustomes = () => {
      const params: GetCategoryCustomeListParams = {
        grade: 1,
        pageNo: 1,
        pageSize: 9999,
      };
      if (distributionCompanyId) params.companyId = distributionCompanyId;
      getCategoryCustomeList({ ...params }).then((res) => {
        if (res.list.length) {
          res.list.unshift({
            id: null,
            categoryName: '全部',
          });
        }
        setCategoryCustomes(res.list);
      });
    };

    const selectCategory = (id: number | null) => {
      if (!id) {
        setSelectCategoryId(null);
        setCategoryCustomesTwo([]);
        return;
      }
      parentIdRef.current = id;
      setShowMore(false);
      setSelectCategoryId(id);
      const data: GetCategoryCustomeListParams = {
        pageNo: 1,
        pageSize: 9999,
        parentId: id || null,
      };
      if (distributionCompanyId) data.companyId = distributionCompanyId;
      getCategoryCustomeList(data).then((res) => {
        setCategoryCustomesTwo(res.list);
      });
    };

    const getGoods = useCallback(
      (argument: InitialState) => {
        const formData: StockGoodsListParams = {
          sort: 'desc',
          pageNo: argument.pageNo,
          pageSize,
          keyword: argument.keyword,
          customizeCategorySet: argument.customizeCategorySet,
          businessSourceType: 1,
        };
        if (distributionCompanyId) {
          formData.businessSourceType = 2;
          formData.distributionCompanyId = distributionCompanyId;
        }
        if (isAdjust) {
          formData.orderNo = orderNo;
        }
        stockGoodsList(formData, nycbngOrgId).then((res) => {
          if (hasSelectGoodsList.current.length) {
            for (let i = 0; i < res.list.length; i += 1) {
              hasSelectGoodsList.current.forEach((item1) => {
                if (res.list[i].skuId === item1.skuId) {
                  res.list[i].number = item1.number;
                }
              });
            }
          }
          dispatch({
            type: 'setAll',
            payload: {
              customizeCategorySet: argument.customizeCategorySet,
              keyword: argument.keyword,
              pageNo: argument.pageNo + 1,
              list: [...argument.list, ...res.list],
              totalPage: Math.ceil(res.pagination.count / pageSize),
            },
          });
        });
      },
      [distributionCompanyId, orderNo, nycbngOrgId] // eslint-disable-line
    );

    const loadMore = () => {
      if (!(state.pageNo <= state.totalPage)) return;
      getGoods(state);
    };

    const onChangeGoods = (e: any, skuId: number) => {
      const arr = JSON.parse(JSON.stringify(selectIds));
      if (e?.target.checked) {
        arr.push(skuId);
      } else {
        for (let i = 0; i < arr.length; i += 1) {
          if (skuId === arr[i]) {
            arr.splice(i, 1);
          }
        }
      }
      setSelectIds(arr);
    };

    // 获取单位
    const getUnit = (templateId: number, mainUnitName: string) => {
      formulaConvertManyconvert({
        templateId,
        mainUnitName,
      }).then((res) => {
        // @ts-ignore
        setUnits(res.list);
      });
    };

    // 切换单位
    const onChangeUnit = (item: StockGoodsListResult, e: string) => {
      getOrderUnitConversion({
        stockNumber: item.stockNumber || item.stock,
        floorPrice: item.floorPrice,
        productMarketPrice: item.marketPrice,
        productMinimumQuantity: item.minimum,
        productMinimumSalesUnit: item.saleGroupStr,
        skuId: item.skuId,
        number: item.number,
        productUnit: e, // 选中的单位
        productUnitBefore: item.auxiliaryUnit || item.unit, // 选则之前的单位
      }).then((res) => {
        dispatch({
          type: 'setList',
          payload: state.list.map((tmpItem) => {
            if (tmpItem.id !== item.id) {
              return tmpItem;
            }
            return {
              ...tmpItem,
              auxiliaryUnit: e,
              number: Number(res.number) || 0,
              stockNumber: res.stockNumber,
              productMinimumSalesUnit: res.productMinimumSalesUnit,
              productMinimumQuantity: res.productMinimumQuantity,
              auxiliaryFloorPrice: Number(res.floorPrice),
            };
          }),
        });
        hasSelectGoodsList.current = hasSelectGoodsList.current.map((it) => {
          if (item.id === it.id) {
            return {
              ...it,
              auxiliaryUnit: e,
              number: Number(res.number) || 0,
              stockNumber: res.stockNumber,
              market: Number(res.productMarketPrice),
              productMinimumSalesUnit: res.productMinimumSalesUnit,
              productMinimumQuantity: res.productMinimumQuantity,
              auxiliaryFloorPrice: Number(res.floorPrice) || it.floorPrice,
            };
          }
          return it;
        });
      });
    };

    // 切换数量
    const onChangeNum = (item: StockGoodsListResult, val: number, type: string) => {
      if (
        (item.saleGroupStr &&
          // @ts-ignore
          BigNumber(val).div(item.saleGroupStr) % 1 !== 0 &&
          !item.productMinimumSalesUnit) ||
        (item.productMinimumSalesUnit &&
          // @ts-ignore
          BigNumber(val).div(item.productMinimumSalesUnit) % 1 !== 0)
      ) {
        if (type === 'blur') {
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => {
              if (tmpItem.id !== item.id) {
                return tmpItem;
              }
              return {
                ...tmpItem,
                number:
                  Math.ceil(val / Number(item.productMinimumSalesUnit || item.saleGroupStr)) *
                    Number(item.productMinimumSalesUnit || item.saleGroupStr) || 0,
              };
            }) as StockGoodsListResult[],
          });
          return message.error('请输入最小销售单元整数倍');
        }
        // return false;
      }
      dispatch({
        type: 'setList',
        payload: state.list.map((tmpItem) => {
          if (tmpItem.id !== item.id) {
            return tmpItem;
          }
          return {
            ...tmpItem,
            number: val,
            num: val,
          };
        }),
      });
      return false;
    };

    const onCloseConfirm = () => {
      Modal.confirm({
        title: '提示',
        content: '您编辑的内容尚未保存，确定要离开吗?',
        cancelText: '离开',
        okText: '继续编辑',
        icon: null,
        width: 290,
        centered: true,
        getContainer: content.current as HTMLElement,
        onCancel() {
          onClose();
        },
      });
    };

    const onCloseDrawer = () => {
      if (notCloseConfirm.current) {
        onClose();
        return;
      }
      if (!selectGoodsList.length) {
        if (state.list.some((item) => item.number > 0)) {
          onCloseConfirm();
          return;
        }
        onClose();
      } else if (
        JSON.stringify(
          selectGoodsList.map((item) => ({
            skuId: item.skuId,
            number: item.number,
          }))
        ) !==
        JSON.stringify(
          state.list
            .filter((item) => selectIds.includes(item.skuId))
            .map((item1) => ({
              skuId: item1.skuId,
              number: item1.number,
            }))
        )
      ) {
        onCloseConfirm();
      } else {
        onClose();
      }
    };

    // 统计数量
    const computeNum = () => {
      const hasIds = hasSelectGoodsList.current.map((each) => each.skuId);
      state.list.forEach((each) => {
        if (selectIds.includes(each.skuId)) {
          if (!hasIds.includes(each.skuId)) {
            hasSelectGoodsList.current.push(each);
          } else {
            for (let i = 0; i < hasSelectGoodsList.current.length; i += 1) {
              if (hasSelectGoodsList.current[i].skuId === each.skuId) {
                hasSelectGoodsList.current[i].number = each.number;
                hasSelectGoodsList.current[i].num = each.num;
                hasSelectGoodsList.current[i].preferential = each.preferential;
              }
            }
          }
        }
      });
    };

    useImperativeHandle(
      ref,
      () => ({
        setNotCloseConfirm: (boolean) => {
          notCloseConfirm.current = boolean;
        },
      }),
      []
    );

    useEffect(() => {
      getCategoryCustomes();
      getGoods(initialState);
    }, [getGoods]); // eslint-disable-line

    useEffect(() => {
      if (visible) {
        if (selectGoodsList.length) {
          hasSelectGoodsList.current = JSON.parse(JSON.stringify(selectGoodsList));
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => {
              for (let i = 0; i < selectGoodsList.length; i += 1) {
                if (tmpItem.skuId === selectGoodsList[i].skuId) {
                  return {
                    ...tmpItem,
                    number: selectGoodsList[i].number || 0,
                    num: selectGoodsList[i].num || 0,
                    unit: selectGoodsList[i].auxiliaryUnit || selectGoodsList[i].unit,
                    preferential:
                      selectGoodsList[i].preferential || selectGoodsList[i].preferential,
                  };
                }
              }
              return {
                ...tmpItem,
                number: 0,
                num: 0,
              };
            }),
          });
          const ids = selectGoodsList.map((item) => item.skuId);
          // @ts-ignore
          setSelectIds(ids);
        } else if (state.list.length) {
          hasSelectGoodsList.current = [];
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => ({
              ...tmpItem,
              number: 0,
              num: 0,
            })),
          });
        }
      } else {
        state.keyword = '';
        state.pageNo = 1;
        state.list = [];
        getGoods(state);
        setSelectIds([]);
      }
    }, [visible]); // eslint-disable-line

    return (
      <Drawer
        visible={visible}
        {...props}
        getContainer={() => document.querySelector('#content') as HTMLElement}
        className={styles.selectGoods}
        width={375}
        title="添加商品"
        onClose={onCloseDrawer}
        footer={
          <div className={styles.footer}>
            <div className={styles.footerText}>
              <img className={styles.img} src={cart} alt="" />
              <span>总数量 </span>
              <span className={styles.num} title={productSum}>
                {productSum}
              </span>
            </div>
            <Button
              type="primary"
              onClick={() => {
                computeNum();
                const arr = hasSelectGoodsList.current.filter((item) =>
                  selectIds.includes(item.skuId)
                );
                if (!arr.length) return message.error('请选择商品');
                if (arr.some((item) => !item.number)) return message.error('请填写商品数量!');
                confirm(arr);
                return false;
              }}
            >
              选好了
            </Button>
          </div>
        }
      >
        <div className={styles.search}>
          <Search
            className={styles.searchInput}
            placeholder="搜索商品名称"
            defaultValue={state.keyword}
            onSearch={(searchKey) => {
              state.keyword = searchKey;
              state.pageNo = 1;
              state.list = [];
              getGoods(state);
            }}
          />
        </div>
        <div
          ref={content}
          className={styles.box}
          role="button"
          tabIndex={0}
          onClick={() => {
            if (showMore) {
              setShowMore(!showMore);
            }
          }}
        >
          <div className={styles.category}>
            {categoryCustomes
              ? categoryCustomes.map((item) => (
                  <div
                    role="button"
                    tabIndex={item.id || 0}
                    key={item.id}
                    title={item.categoryName}
                    className={classNames(
                      selectCategoryId === item.id ? styles.itemActive : styles.item
                    )}
                    onClick={() => {
                      computeNum();
                      state.customizeCategorySet = [item.id || null];
                      // state.keyword = '';
                      state.pageNo = 1;
                      state.list = [];
                      getGoods(state);
                      selectCategory(item.id);
                      setSelectCategoryIdTwo(null);
                    }}
                  >
                    {item.categoryName}
                  </div>
                ))
              : null}
          </div>
          <div className={styles.boxList}>
            {categoryCustomesTwo && categoryCustomesTwo.length > 0 ? (
              <div className={styles.categoryList}>
                {categoryCustomesTwo
                  ? categoryCustomesTwo.slice(0, 3).map((item) => (
                      // key={item.id}
                      <span
                        role="button"
                        key={item.id}
                        tabIndex={item.id || 0}
                        title={item.categoryName}
                        className={classNames(
                          selectCategoryIdTwo === item.id
                            ? styles.categoryItemActive
                            : styles.categoryItem,
                          'mb-4'
                        )}
                        onClick={() => {
                          computeNum();
                          state.pageNo = 1;
                          state.keyword = '';
                          state.list = [];
                          if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                            setSelectCategoryIdTwo(null);
                            state.customizeCategorySet = [parentIdRef.current];
                          } else {
                            setSelectCategoryIdTwo(item.id);
                            state.customizeCategorySet = [item.id];
                          }
                          getGoods(state);
                        }}
                      >
                        {item.categoryName}
                      </span>
                    ))
                  : null}
                {categoryCustomesTwo.length > 3 ? (
                  <span
                    role="button"
                    tabIndex={0}
                    className={styles.filter}
                    onClick={() => {
                      setShowMore(!showMore);
                    }}
                  >
                    <Icon name="down" color="#000" />
                  </span>
                ) : null}
              </div>
            ) : null}
            {showMore ? (
              <div className={styles.categoryMore}>
                <div className={styles.categoryBox}>
                  {categoryCustomesTwo
                    ? categoryCustomesTwo.slice(3, categoryCustomesTwo.length).map((item) => (
                        <span
                          role="button"
                          key={item.id}
                          tabIndex={item.id || 0}
                          className={classNames(
                            selectCategoryIdTwo === item.id
                              ? styles.categoryItemActive
                              : styles.categoryItem,
                            'mb-4'
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            computeNum();
                            state.pageNo = 1;
                            state.list = [];
                            if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                              setSelectCategoryIdTwo(null);
                              state.customizeCategorySet = [parentIdRef.current];
                            } else {
                              setSelectCategoryIdTwo(item.id);
                              state.customizeCategorySet = [item.id];
                            }
                            getGoods(state);
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))
                    : null}
                </div>
              </div>
            ) : null}
            <div
              className={classNames(state.list.length === 0 ? styles.noData : styles.list)}
              id="list"
            >
              {state.list.length > 0 ? (
                <InfiniteScroll
                  dataLength={state.list.length}
                  hasMore={state.pageNo <= state.totalPage}
                  loader={
                    <div className="text-center">
                      <Spin tip="加载中..." />
                    </div>
                  }
                  endMessage={
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>加载到底了</span>
                      </Divider>
                    </div>
                  }
                  next={loadMore}
                  scrollableTarget="list"
                >
                  <Checkbox.Group defaultValue={selectIds}>
                    {state.list.map((item) => (
                      <div className={styles.goodsItem} key={item.id}>
                        <div className={styles.goods}>
                          <GoodsItem
                            name={item.name}
                            img={item.images || (item.imagesList && item.imagesList[0])}
                            standard={item.standardList}
                            stock={item.stockNumber || item.stock}
                            saleGroup={item.productMinimumSalesUnit || item.saleGroupStr || '1'}
                            floorPrice={item.auxiliaryFloorPrice || item.floorPrice}
                            showStock
                          />
                          <Checkbox
                            value={item.skuId}
                            onChange={(e) => {
                              if (e.target.checked && item.unitTemplateId) {
                                formulaConvert(item.unitTemplateId || 0).then((res) => {
                                  // @ts-ignore
                                  setUnits(res.detailList);
                                });
                              }
                              onChangeGoods(e, item.skuId);
                            }}
                          />
                        </div>
                        {/* @ts-ignore */}
                        {selectIds.includes(item.skuId) ? (
                          <>
                            {/* {item.unitTemplateId ? ( */}
                            <div className={styles.goodsSelect}>
                              <div className={styles.label}>商品单位</div>
                              {item.unitTemplateId ? (
                                <Select
                                  placeholder="请选择"
                                  defaultValue={item.auxiliaryUnit || item.unit}
                                  bordered={false}
                                  className={styles.select}
                                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                                  options={units.map((each) => ({
                                    label: each.unitName,
                                    value: each.unitName,
                                  }))}
                                  onFocus={() => {
                                    if (item.unitTemplateId && item.unit) {
                                      getUnit(item.unitTemplateId, item.unit);
                                    }
                                  }}
                                  onChange={(e) => {
                                    onChangeUnit(item, e);
                                  }}
                                />
                              ) : (
                                <Select
                                  placeholder="请选择"
                                  defaultValue={item.auxiliaryUnit || item.unit}
                                  bordered={false}
                                  disabled
                                  className={styles.select}
                                />
                              )}
                            </div>
                            {/* ) : null} */}
                            <div className={styles.goodsSelect}>
                              <div className={styles.label}>商品数量</div>
                              <Stepper
                                min={
                                  Number(item.productMinimumQuantity) || Number(item.minimum) || 0
                                }
                                // max={99999999}
                                step={
                                  isAdjust
                                    ? 1
                                    : Number(item.productMinimumSalesUnit) ||
                                      Number(item.saleGroupStr) ||
                                      1
                                }
                                value={item.number}
                                defaultValue={
                                  Number(item.productMinimumQuantity) || Number(item.minimum)
                                }
                                onBlur={(e) => {
                                  onChangeNum(item, Number(e.target.value), 'blur');
                                }}
                                onChange={(e) => {
                                  onChangeNum(item, e, 'change');
                                }}
                                className={styles.inputNumber}
                              />
                            </div>
                          </>
                        ) : null}
                      </div>
                    ))}
                  </Checkbox.Group>
                </InfiniteScroll>
              ) : (
                <Empty />
              )}
            </div>
          </div>
        </div>
      </Drawer>
    );
  }
);

SelectGoods.defaultProps = {
  distributionCompanyId: undefined,
  orderNo: 0,
  nycbngOrgId: '',
  isAdjust: false,
};

export default SelectGoods;
