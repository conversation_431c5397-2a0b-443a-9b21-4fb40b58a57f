import { MouseEventH<PERSON>ler, ReactElement, useContext, useState } from 'react';
import { Button, Cascader, CascaderProps, Checkbox, CheckboxProps, Col, message, Row } from 'antd';
import LocationOutlined from '@/components/icon/location';
import { useMemoizedFn } from 'ahooks';
import {
  CartGoods,
  CartItem,
  CartPackageGoods,
  getProvinceCityCounty,
  toggleCollectionProduct,
} from '@/apis';
import { useNavigate } from 'react-router-dom';
import { Permission } from '@/containers';
import { getGoodsServices } from '../../utils/cart';
import CartContext from '../../context/cart';
import styles from './index.module.less';

export interface CartFooterProps {
  count: number;
  totalAmount: string;
  groups: CartItem[];
  onRemoveChecks: MouseEventHandler<HTMLButtonElement>;
}

function CartFooter({ count, totalAmount, groups, onRemoveChecks }: CartFooterProps) {
  const navigate = useNavigate();
  const { checks, dispatch, getCart } = useContext(CartContext);
  const [collecting, setCollecting] = useState(false);
  const [loading, setLoading] = useState(false);
  const [options, setOptions] = useState<CascaderProps<any>['options']>();
  const onCheckAll: CheckboxProps['onChange'] = useMemoizedFn((e) => {
    const checkCartNoList: number[] = [];
    if (e.target.checked) {
      groups.forEach((group) => {
        group.cartProductVOList.forEach((item) => {
          if (item.productStatus === 50) {
            checkCartNoList.push(item.cartNo);
          }
        });
      });
    }
    dispatch({ type: 'setCheck', payload: checkCartNoList });
  });
  const onAddCollect = useMemoizedFn(() => {
    if (checks.length === 0) {
      message.warn('请先选择商品');
    } else {
      setCollecting(true);
      const productId: number[] = [];
      groups.forEach((group) => {
        group.cartProductVOList.forEach((product) => {
          productId.push(product.shopSkuId);
        });
      });
      toggleCollectionProduct({ productId, bool: true })
        .then(() => {
          message.success('添加成功');
        })
        .finally(() => {
          setCollecting(false);
        });
    }
  });
  const onDropdownVisibleChange: CascaderProps<any>['onDropdownVisibleChange'] = useMemoizedFn(
    (e) => {
      if (e && !options) {
        setLoading(true);
        getProvinceCityCounty()
          .then((res) => {
            setOptions(res.list);
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  );
  const onSettlement: MouseEventHandler<HTMLButtonElement> = useMemoizedFn((e) => {
    e.stopPropagation();
    const settlementList: unknown[] = [];
    const formatGoods = (goods: CartPackageGoods | CartGoods): Record<string, unknown> => ({
      skuId: goods.skuId,
      shopSkuId: goods.shopSkuId,
      number: goods.number,
      payWayNo: goods.payWayNo,
      cartNo: goods.cartNo,
      productUnit: goods.productUnit,
      groupId: goods.groupId,
      isGift: goods.isGift,
      orderServiceProductParams: getGoodsServices(goods, (service) => ({
        shopSkuId: service.shopSkuId,
        skuId: service.skuId,
        number: service.serviceNumber,
      })),
    });
    groups.forEach((group) => {
      const itemMap = new Map<number, unknown[]>();
      group.cartProductVOList.forEach((goods) => {
        if (checks.includes(goods.cartNo)) {
          if (goods.productType === 2 && goods.packageCartProductVOS) {
            settlementList.push({
              shopId: group.shopId,
              deliveryMode: 0,
              gmallId: goods.gmallId,
              packageId: goods.packageId,
              packageNumber: +goods.number,
              productParamList: goods.packageCartProductVOS.map((item) => {
                const newItem = formatGoods(item);
                newItem.packProdBelongShopId = goods.packProdBelongShopId;
                newItem.packProdBelongCompanyId = goods.packProdBelongCompanyId;
                return newItem;
              }),
            });
          } else {
            const key = goods.gmallId ?? 0;
            const items = itemMap.get(key) || [];
            items.push(formatGoods(goods));
            if (!itemMap.has(key)) {
              itemMap.set(key, items);
            }
          }
        }
      });
      if (itemMap.size !== 0) {
        itemMap.forEach((items, gmallId) => {
          settlementList.push({
            gmallId,
            shopId: group.shopId,
            deliveryMode: 0,
            productParamList: items,
          });
        });
      }
    });
    localStorage.setItem('CARTLIST', JSON.stringify(settlementList));
    navigate('/indent/submit');
  });

  const dropdownRender = (menus: ReactElement) => (loading ? <div /> : menus);

  const hasCheckedGoods = checks.length !== 0;

  return (
    <Row className={styles.footer}>
      <Col xs={24} md={12} className="py-5 px-5">
        <Checkbox
          indeterminate={hasCheckedGoods && checks.length < count}
          checked={hasCheckedGoods && checks.length === count}
          onChange={onCheckAll}
        >
          全选
        </Checkbox>
        <Button size="small" type="ghost" shape="round" className="ml-5" onClick={onRemoveChecks}>
          删除
        </Button>
        <Button
          size="small"
          type="ghost"
          shape="round"
          loading={collecting}
          className="ml-5"
          onClick={onAddCollect}
        >
          添加到收藏夹
        </Button>
        <div className="mt-5">
          <LocationOutlined className={styles.location} />
          <span className="ml-3">配送至：</span>
          <Cascader
            loading={loading}
            options={options}
            placement="topLeft"
            placeholder="请选择配送地址"
            dropdownRender={dropdownRender}
            onDropdownVisibleChange={onDropdownVisibleChange}
            onChange={(values) => {
              getCart(values as number[]);
            }}
          />
        </div>
      </Col>
      <Col xs={24} md={12} className="py-5 px-5 text-right">
        <p className={styles.footerText}>
          总计:<span className={styles.price}>￥{totalAmount}</span>
          {/* <span className={styles.footerTooltip}>
                  <InfoOutlined />
                  &nbsp;含运费
                </span> */}
        </p>
        <div className="mt-5">
          {/* <Button type="dashed" shape="round">
            继续购物
          </Button> */}
          <Permission permission="J_001_003">
            <Button
              disabled={!checks.length}
              type="primary"
              shape="round"
              className="ml-5"
              onClick={onSettlement}
            >
              结算({checks.length})
            </Button>
          </Permission>
        </div>
      </Col>
    </Row>
  );
}

export default CartFooter;
