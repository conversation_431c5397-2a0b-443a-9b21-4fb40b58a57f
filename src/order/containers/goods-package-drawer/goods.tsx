import { HTMLAttributes, ReactNode, useMemo } from 'react';
import classNames from 'classnames';
import { CartPackageGoods } from '@/apis';
import BigNumber from 'big.js';
import ToggleGoodsOrService, { ToggleGoodsOrServiceProps } from '../toggle-goods-or-service';
import GoodsService from '../../components/goods-service';
import styles from './goods.module.less';

export interface GoodsProps
  extends HTMLAttributes<HTMLDivElement>,
    Pick<ToggleGoodsOrServiceProps, 'updateSku'> {
  goods: CartPackageGoods;
  number?: number;
  choice?: ReactNode;
}

function Goods({
  goods,
  number = 1,
  choice,
  updateSku,
  children,
  className,
  ...props
}: GoodsProps) {
  const [num, serviceNum] = useMemo(() => {
    const goodsNumber = new BigNumber(goods.number);
    return [
      goodsNumber.times(number).toNumber(),
      Math.ceil(
        goodsNumber
          .div(goods.productMinimumSalesUnit || 1)
          .times(number)
          .toNumber()
      ),
    ];
  }, [goods.number, goods.productMinimumSalesUnit, number]);

  const serviceList = goods.serviceProductVOS;
  let service;
  if (serviceList && serviceList.length !== 0) {
    service = <GoodsService number={serviceNum} services={serviceList} className="mt-4" />;
  }

  const classes = classNames(className, styles.goods, 'mb-4');
  return (
    <div {...props} className={classes}>
      {choice && <div className={styles.choice}>{choice}</div>}
      <div className={styles.imageBox}>
        <img src={goods.productMainPic} alt="" />
      </div>

      <div className={styles.body}>
        <h4 className={styles.title}>{goods.productName}</h4>
        <ToggleGoodsOrService
          item={goods}
          updateSku={updateSku}
          suffix={<span className={styles.suffix}>；共{goods.skuTotal}种</span>}
          className="mt-2"
        />
        <div className={styles.num}>
          搭配数量：{num}
          {goods.productUnit}
        </div>
        {service}
        {children}
      </div>
    </div>
  );
}

Goods.defaultProps = {
  choice: null,
  number: 1,
};

export default Goods;
