@import 'styles/mixins/mixins';

.goods {
  display: flex;
  padding: 16px;
  border-radius: @border-radius-base;
  background-color: @white;
}

.choice {
  display: flex;
  height: 80px;
  padding-right: 10px;
  align-items: center;
}

.imageBox {
  display: flex;
  width: 80px;
  height: 80px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: @border-radius-xs;
}

.body {
  .flex-column();

  padding-left: 8px;
  overflow: hidden;
}

.title {
  .text-overflow();

  font-size: @font-size-base;
  line-height: 21px;
}

.num {
  font-size: @font-size-sm;
  line-height: 18px;
  margin-top: 10px;
}

.btn {
  font-size: @font-size-sm;
  display: inline-block;
  line-height: 20px;
  padding: 0;
  border: 0 solid transparent;
  background-color: transparent;
}

.icon {
  color: @text-color-secondary;
  font-size: @font-size-lg;
  margin-top: 2px;
  vertical-align: top;
}

.suffix {
  color: @text-color;
}
