import {
  <PERSON><PERSON><PERSON>,
  MouseE<PERSON>Handler,
  MutableRefObject,
  SetStateAction,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Drawer } from '@/components';
import { Button, DrawerProps } from 'antd';
import { addGoodsToCart, CartGoods, CartPackageGoods, GoodsDetailResult } from '@/apis';
import { useMemoizedFn } from 'ahooks';
import RightOutlined from '@/components/icon/right';
import { EMPTY_FN } from '@/utils/const';
import { formatGoodsDetail, getGoodsServices } from '../../utils/cart';
import togglePackageGoods from './toggle-package-goods';
import styles from './goods.module.less';
import Goods, { GoodsProps } from './goods';

export interface GoodsPackageProps extends Omit<DrawerProps, 'title'> {
  goods: CartGoods;
  shopId: number;
  onReload?: () => void;
}

function GoodsPackageItem({
  goods,
  number,
  updateSku,
  setGoodsList,
  hasEdit,
}: GoodsProps & {
  setGoodsList: Dispatch<SetStateAction<CartPackageGoods[]>>;
  hasEdit: MutableRefObject<boolean>;
}) {
  const onClick: MouseEventHandler = useMemoizedFn((e) => {
    e.stopPropagation();
    togglePackageGoods({
      goods,
      onConfirm: (goodsItem) => {
        setGoodsList((prevState) =>
          prevState.map((state) =>
            state.cartNo === goods.cartNo ? { ...state, ...goodsItem } : state
          )
        );
        // eslint-disable-next-line no-param-reassign
        hasEdit.current = true;
      },
    });
  });
  return (
    <Goods goods={goods} number={number} updateSku={updateSku}>
      {goods.groupSpuTotal !== 0 && (
        <div className="mt-4 text-right">
          <button type="button" className={styles.btn} onClick={onClick}>
            可更换商品{goods.groupSpuTotal}种
            <RightOutlined className={styles.icon} />
          </button>
        </div>
      )}
    </Goods>
  );
}

function GoodsPackage({ goods, shopId, onClose, onReload, ...props }: GoodsPackageProps) {
  const hasEdit = useRef(false);
  const [goodsList, setGoodsList] = useState<CartPackageGoods[]>(goods.packageCartProductVOS || []);
  const [loading, setLoading] = useState(false);
  const [list, gifts] = useMemo(() => {
    const packageList: CartPackageGoods[] = [];
    const packageGifts: CartPackageGoods[] = [];
    goodsList.forEach((packageGoods) => {
      if (packageGoods.isGift) {
        packageGifts.push(packageGoods);
      } else {
        packageList.push(packageGoods);
      }
    });
    return [packageList, packageGifts];
  }, [goodsList]);
  const updateSku: GoodsProps['updateSku'] = useMemoizedFn(
    (detail: GoodsDetailResult, item, values) => {
      hasEdit.current = true;
      const newGoods = formatGoodsDetail(detail, item, values);
      setGoodsList((prevState) =>
        prevState.map((state) => (state.cartNo === item.cartNo ? newGoods : state))
      );
    }
  );
  const onConfirm: MouseEventHandler<HTMLButtonElement> = useMemoizedFn((e) => {
    if (hasEdit.current) {
      setLoading(true);
      addGoodsToCart({
        productType: 2,
        packageId: goods.packageId,
        packageNumber: +goods.number,
        cartProductParamList: goodsList.map((item) => ({
          shopId,
          shopSkuId: item.shopSkuId,
          skuId: item.skuId,
          payWayNo: item.payWayNo,
          productNumber: +item.number,
          productUnit: item.productUnit,
          groupId: item.groupId,
          isGift: item.isGift as 0 | 1,
          serviceProductParamList: getGoodsServices(item, (standard) => ({
            shopSkuId: standard.shopSkuId,
            skuId: standard.skuId,
            number: +standard.serviceNumber,
            productPrice: +standard.serviceProductPrice,
          })),
        })),
      })
        .then(() => {
          onReload?.();
          onClose?.(e);
        })
        .finally(() => {
          setLoading(false);
        });
    } else {
      onClose?.(e);
    }
  });

  const number = parseInt((goods.number || goods.packageNumber || '1') as string, 10);

  return (
    <Drawer
      {...props}
      title={`搭配商品（${goods.packageProductTotal}个）`}
      footer={
        <div className="px-5 pt-4 pb-5">
          <Button
            block
            loading={loading}
            type="primary"
            shape="round"
            className="mb-1"
            onClick={onConfirm}
          >
            确定
          </Button>
        </div>
      }
      onClose={onClose}
    >
      {list.map((item) => (
        <GoodsPackageItem
          key={item.cartNo}
          goods={item}
          hasEdit={hasEdit}
          number={number}
          updateSku={updateSku}
          setGoodsList={setGoodsList}
        />
      ))}
      {gifts.length !== 0 && (
        <>
          <h4 className="pt-2 pb-3">赠品</h4>
          {gifts.map((gift) => (
            <GoodsPackageItem
              key={gift.shopSkuId}
              goods={gift}
              hasEdit={hasEdit}
              number={number}
              updateSku={updateSku}
              setGoodsList={setGoodsList}
            />
          ))}
        </>
      )}
    </Drawer>
  );
}

GoodsPackage.defaultProps = {
  onReload: EMPTY_FN,
};

export default GoodsPackage;
