import { MouseEvent<PERSON>and<PERSON>, useState } from 'react';
import { Button, DrawerProps, Radio, RadioGroupProps, Spin } from 'antd';
import { Drawer } from '@/components';
import { CartPackageGoods, getGoodsPackageGroupGoodsList } from '@/apis';
import { drawerPopup } from '@/utils/popup';
import { useMemoizedFn, useMount } from 'ahooks';
import Goods, { GoodsProps } from './goods';
import { formatGoodsDetail } from '../../utils/cart';
import styles from './toggle-package-goods.module.less';

export interface TogglePackageGoodsProps extends DrawerProps {
  goods: CartPackageGoods;
  onConfirm?: MultipleParamsFn<[goods: CartPackageGoods]>;
}

function TogglePackageGoods({ goods, onConfirm, onClose, ...props }: TogglePackageGoodsProps) {
  const [value, setValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [list, setList] = useState<CartPackageGoods[]>();
  const onChange: RadioGroupProps['onChange'] = useMemoizedFn((e) => {
    setValue(e.target.value);
  });
  const onClick: MouseEventHandler<HTMLButtonElement> = useMemoizedFn((e) => {
    if (onConfirm && list) {
      const item = list.find((tmpItem) => tmpItem.shopSkuId === value);
      if (item) {
        onConfirm(item);
      }
    }
    if (onClose) {
      onClose(e);
    }
  });
  const updateSku: GoodsProps['updateSku'] = useMemoizedFn((detail, item, values) => {
    const newGoods = formatGoodsDetail(detail, item, values);
    setList((prevState) =>
      prevState?.map((state) =>
        state.shopSkuId === item.shopSkuId ? { ...state, ...newGoods } : state
      )
    );
  });

  useMount(() => {
    getGoodsPackageGroupGoodsList({
      packageId: goods.packageId,
      groupId: goods.groupId,
      productId: goods.productId || '',
    })
      .then((res) => {
        setList(
          res.list
            .filter((item) => item.id !== goods.shopSkuId)
            .map(
              (item) =>
                ({
                  shopSkuId: item.id,
                  skuId: item.skuId,
                  productMainPic: item.imageList[0],
                  productName: item.name,
                  productId: item.productId,
                  packageNumber: item.quantity,
                  productMinimumQuantity: item.quantity,
                  productMinimumSalesUnit: item.saleGroup,
                  productUnit: item.unit || goods.productUnit,
                  // groupSpuTotal: item.spuCount,
                  productSpecificationList: item.standardList,
                  number: item.quantity,
                  groupId: item.groupId,
                  isGift: item.isGift,
                  skuTotal: item.skuCount,
                  serviceProductVOS: [],
                } as unknown as CartPackageGoods)
            )
        );
      })
      .finally(() => {
        setLoading(false);
      });
  });

  return (
    <Drawer
      {...props}
      title="更换商品"
      footer={
        <div className="px-4 py-4">
          <Button type="primary" disabled={!value} shape="round" block onClick={onClick}>
            确定
          </Button>
        </div>
      }
      onClose={onClose}
    >
      <Spin spinning={loading}>
        <Radio.Group value={value} className={styles.group} onChange={onChange}>
          {list &&
            list.map((item) => (
              <Goods
                key={item.shopSkuId}
                updateSku={updateSku}
                choice={<Radio value={item.shopSkuId} />}
                goods={item}
              />
            ))}
        </Radio.Group>
      </Spin>
    </Drawer>
  );
}

TogglePackageGoods.defaultProps = {
  onConfirm: undefined,
};

export default function togglePackageGoods(props: Omit<TogglePackageGoodsProps, 'visible'>) {
  return drawerPopup<TogglePackageGoodsProps>(TogglePackageGoods, props);
}
