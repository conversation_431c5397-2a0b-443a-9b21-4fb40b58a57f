@import 'styles/mixins/mixins';

.search {
  margin-top: 2px;
  margin-bottom: 20px;
}

.card {
  width: 100%;
  margin-bottom: 20px;
  padding: 0 20px 8px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
}

.title {
  font-weight: 600;
  display: flex;
  padding: 20px 0;
  justify-content: space-between;
}

.open {
  color: #888b98;
  font-size: 12px;
  font-weight: 400;
  cursor: pointer;
}

.list {
  display: flex;
  max-height: 200px;
  overflow: auto;
  flex-wrap: wrap;
}

.nameItem {
  display: flex;
  width: calc((100% - 20px) / 3);
  height: 28px;
  margin-right: 10px;
  margin-bottom: 12px;
  padding: 0 10px;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  background: #f3f3f3;
  cursor: pointer;

  & .name {
    .text-overflow();
  }

  &:nth-child(3n) {
    margin-right: 0;
  }
}

.itemActive {
  background: #d9eeff;
  border: 1px solid #008cff;
  color: #008cff;
}

.empty {
  display: flex;
  align-items: center;
  height: calc(100% - 80px);
  margin-left: 30px;
}

.spin {
  width: 100%;
  height: 100%;

  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}
