import { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { Spin } from 'antd';
import { Drawer, Icon, Search, Empty } from '@/components';
import { orderGoodsStandardFilter, StandardArrResult } from '@/apis';
import classnames from 'classnames';
import cloneDeep from 'lodash/cloneDeep';
import styles from './select-goods-filter.module.less';

interface ArrProps {
  name: string;
  value: string[];
}

export interface SelectGoodsStandardProps {
  visible: boolean;
  businessSourceType: number;
  onClose: () => void;
  standardArr: ArrProps[];
  names: string[];
  onConfirm: MultipleParamsFn<[standard: ArrProps[], names: string[]]>;
}

function SelectGoodsStandard({
  visible,
  businessSourceType,
  standardArr,
  names,
  onClose,
  onConfirm,
}: SelectGoodsStandardProps) {
  const params = useRef({
    sort: 'desc',
    businessSourceType,
    customizeCategorySet: [],
  });
  const [standardList, setStandardList] = useState<StandardArrResult[]>([]);
  const standardListValue = useRef<StandardArrResult[]>([]);
  const [nameList, setNameList] = useState<string[]>([]);
  const nameListValue = useRef<string[]>([]);
  const [isNameMore, setIsNameMore] = useState(false);
  const [shopSkuNames, setShopSkuNames] = useState<string[]>([...names]);

  /**
   * @description 获取规格列表
   */
  const { run, loading } = useRequest(orderGoodsStandardFilter, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (result) => {
      result.standardList.forEach((item) => {
        const items = item;
        items.standards = [];
        standardArr.forEach((arr) => {
          if (arr.name === items.name) {
            items.standards = arr.value;
          }
        });
      });
      setStandardList(cloneDeep(result.standardList));
      standardListValue.current = cloneDeep(result.standardList);
      setNameList(result.shopSkuNames);
      nameListValue.current = cloneDeep(result.shopSkuNames);
    },
  });

  const onOpen = (name: string) => {
    standardList.forEach((item) => {
      const items = item;
      if (item.name === name) {
        items.isOpen = !item.isOpen;
      }
    });
    setStandardList([...standardList]);
  };

  const onSearch = (e: string) => {
    const arr = standardListValue.current.map((item) => ({
      ...item,
      value: item.value.filter((each) => each.includes(e)),
    }));
    setStandardList(JSON.parse(JSON.stringify(arr)));
    const nameArr = nameListValue.current.filter((item) => item.includes(e));
    setNameList(cloneDeep(nameArr));
  };

  const onSelect = (item: StandardArrResult, each: string) => {
    standardList.forEach((items) => {
      const items1 = items;
      if (items.name === item.name) {
        if (items1.standards && items1.standards.length > 0 && items1.standards.includes(each)) {
          items1.standards = items1.standards.filter((standardItem) => standardItem !== each);
        } else {
          items1.standards.push(each);
        }
      }
    });
    standardListValue.current.forEach((items) => {
      const items1 = items;
      if (items.name === item.name) {
        if (items1.standards && items1.standards.length > 0 && items1.standards.includes(each)) {
          items1.standards = items1.standards.filter((standardItem) => standardItem !== each);
        } else {
          items1.standards.push(each);
        }
      }
    });
    setStandardList(JSON.parse(JSON.stringify(standardList)));
  };

  const onSelectName = (name: string) => {
    if (shopSkuNames.includes(name)) {
      setShopSkuNames(cloneDeep(shopSkuNames.filter((item) => item !== name)));
    } else {
      shopSkuNames.push(name);
      setShopSkuNames(cloneDeep(shopSkuNames));
    }
  };

  const inInitData = () => {
    standardList.forEach((item) => {
      const items = item;
      items.standards = [];
    });
    setStandardList([...standardList]);
    standardListValue.current.forEach((item) => {
      const items = item;
      items.standards = [];
    });
    setShopSkuNames([]);
  };

  const onOk = () => {
    const arr: ArrProps[] = standardList
      .filter((item) => item.standards.length > 0)
      .map((each) => ({
        name: each.name,
        value: each.standards,
      }));
    onClose();
    onConfirm(arr, shopSkuNames);
  };

  useEffect(() => {
    if (visible) {
      setShopSkuNames(names);
      run({ ...params.current });
    }
  }, [visible, run, names]);

  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      title="筛选"
      zIndex={99999}
      footer={
        <Drawer.Footer
          okText="确定"
          showCancel
          cancelText="重置"
          onCancel={inInitData}
          onOk={onOk}
        />
      }
    >
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <div className={styles.search}>
          <Search placeholder="请输入关键字" onSearch={(e) => onSearch(e)} />
        </div>
        {standardList.some((item) => item.value.length) || nameList.length ? (
          <>
            {nameList.length > 0 && (
              <div className={styles.card}>
                <div className={styles.title}>
                  <div>名称</div>
                  <div
                    role="button"
                    tabIndex={0}
                    className={styles.open}
                    onClick={() => setIsNameMore(!isNameMore)}
                  >
                    {nameList.length > 6 &&
                      (isNameMore ? (
                        <>
                          <span>收起</span>
                          <Icon name="up" />
                        </>
                      ) : (
                        <>
                          <span>展开</span>
                          <Icon name="down" />
                        </>
                      ))}
                  </div>
                </div>
                <div className={styles.list}>
                  {nameList
                    .filter((item, idx) => (isNameMore ? item : idx < 6))
                    .map((each, idx1) => (
                      <div
                        key={`${idx1 + 1}`}
                        role="button"
                        tabIndex={idx1}
                        className={classnames(styles.nameItem, {
                          [styles.itemActive]: shopSkuNames.includes(each),
                        })}
                        onClick={() => onSelectName(each)}
                        title={each}
                      >
                        <div className={styles.name}>{each}</div>
                      </div>
                    ))}
                </div>
              </div>
            )}
            {standardList.map(
              (item, index) =>
                item.value.length > 0 && (
                  <div className={styles.card}>
                    <div className={styles.title}>
                      <div>{item.name}</div>
                      <div
                        role="button"
                        tabIndex={index}
                        className={styles.open}
                        onClick={() => onOpen(item.name)}
                      >
                        {item.value.length > 6 &&
                          (item.isOpen ? (
                            <>
                              <span>收起</span>
                              <Icon name="up" />
                            </>
                          ) : (
                            <>
                              <span>展开</span>
                              <Icon name="down" />
                            </>
                          ))}
                      </div>
                    </div>
                    <div className={styles.list}>
                      {item.value
                        .filter((each, idx) => (item.isOpen ? item : idx < 6))
                        .map((each, idx1) => (
                          <div
                            role="button"
                            tabIndex={idx1}
                            className={classnames(styles.nameItem, {
                              [styles.itemActive]:
                                item.standards &&
                                item.standards.length &&
                                item.standards.includes(each),
                            })}
                            onClick={() => onSelect(item, each)}
                            title={each}
                          >
                            <div className={styles.name}>{each}</div>
                          </div>
                        ))}
                    </div>
                  </div>
                )
            )}
          </>
        ) : (
          <div className={styles.empty}>
            <Empty message="暂无相关内容" />
          </div>
        )}
      </Spin>
    </Drawer>
  );
}

export default SelectGoodsStandard;
