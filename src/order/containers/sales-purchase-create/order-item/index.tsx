import { Checkbox } from 'antd';
import { Price } from '@/components';
import OrderItemGoods, { OrderItemGoodsProps } from '../order-item-goods';

import styles from './index.module.less';

interface OrderItemProps {
  checked?: boolean;
  orderTitle: string;
  orderStatusStr: string;
  orderNumber: string;
  goodsList: OrderItemGoodsProps[];
  totalPrice: string;
  productCatCount: number;
  onSelectOrder: MultipleParamsFn<[val: boolean]>;
}

function OrderItem({
  checked,
  orderTitle,
  orderStatusStr,
  orderNumber,
  goodsList,
  totalPrice,
  productCatCount,
  onSelectOrder,
}: OrderItemProps) {
  return (
    <div className={styles.orderItem}>
      <div className={styles.orderInfo}>
        <Checkbox
          className="mr-3"
          checked={checked}
          onChange={(e) => onSelectOrder(e.target.checked)}
        />
        <div>
          <div className={styles.orderInfoTop}>
            <span className={styles.orderTitle}>{orderTitle}</span>
            <span className={styles.orderTypeName}>{orderStatusStr}</span>
          </div>
          <div className={styles.orderNumber}>订单编号：{orderNumber}</div>
        </div>
      </div>
      {goodsList.map((item, index) => (
        <OrderItemGoods
          key={`${index + 1}`}
          image={item.image}
          name={item.name}
          standard={item.standard}
          price={item.price}
          number={item.number}
          unit={item.unit}
        />
      ))}
      <div className={styles.totalInfo}>
        共 {productCatCount} 种商品 总金额：
        <Price value={Number(totalPrice)} className={styles.totalPrice} />
      </div>
    </div>
  );
}

OrderItem.defaultProps = {
  checked: false,
};

export default OrderItem;
