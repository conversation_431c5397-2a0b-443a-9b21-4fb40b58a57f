@import 'styles/mixins/mixins';

.orderItem {
  margin-bottom: 20px;
  padding: 0 20px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
}

.orderInfo {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f3f3f3;
}

.orderInfoTop {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.orderTitle {
  flex: 1;
  font-size: 16px;
  line-height: 20px;
  margin-right: 12px;
  .text-overflow(1);
}

.orderTypeName {
  color: #008cff;
  font-size: 12px;
  line-height: 18px;
  padding: 2px 4px;
  background: #d9eeff;
  border-radius: 2px;
}

.orderNumber {
  color: #888b98;
  font-size: 12px;
}

.totalInfo {
  padding: 40px 0 20px;
  text-align: right;
}

.totalPrice {
  color: #040919;
  font-size: 24px;
}
