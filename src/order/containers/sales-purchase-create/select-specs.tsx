import { useEffect, useState } from 'react';
import { DrawerProps } from 'antd';
import { Drawer, Icon, Price } from '@/components';
import { PayWayListResult } from '@/apis';
import classNames from 'classnames';
import styles from './select-specs.module.less';

interface StandardProps {
  name: string;
  value: string;
  groupId: number;
}

interface SelectSpecsProps extends DrawerProps {
  onClose?: () => void;
  image: string;
  standard: StandardProps[];
  price: string;
  unit: string;
  currentPayWayNo: number;
  payWayList: PayWayListResult[];
  onConfirm: MultipleParamsFn<[payWayNo: number]>;
}

function SelectSpecs({
  onClose,
  image,
  standard,
  price,
  unit,
  currentPayWayNo,
  payWayList,
  onConfirm,
  ...props
}: SelectSpecsProps) {
  const [payWayNo, setPayWayNo] = useState(currentPayWayNo);

  useEffect(() => {
    if (props.visible) {
      setPayWayNo(currentPayWayNo);
    }
  }, [currentPayWayNo, props.visible]);

  const footer = (
    <Drawer.Footer showCancel={false} onText="确定" onOk={() => onConfirm(payWayNo)} />
  );

  return (
    <Drawer
      closable={false}
      className={styles.drawer}
      placement="bottom"
      onClose={onClose}
      {...props}
      title="选择付款方式"
      extra={<Icon className={styles.close} name="close" onClick={() => onClose?.()} />}
      footer={footer}
      maskClosable
    >
      <div className={styles.goodsInfo}>
        <img className={styles.goodsImg} src={image} alt="" />
        <div className={styles.goodsInfoRight}>
          <div>
            <Price value={price} color="#040919" className={styles.price} /> /{unit}
          </div>
          <div className={styles.hasSelect}>
            已选：
            {standard.map((item, index) => (
              <span className={styles.standardItem} key={`${item.name}_${item.value}_${index + 1}`}>
                {item.name}：{item.value}
                <span className={styles.punctuate}>; </span>
              </span>
            ))}
            单位：{unit}
          </div>
        </div>
      </div>
      <div className={styles.title}>付款比例</div>
      {payWayList.map((item) => (
        <div
          role="button"
          tabIndex={item.payWayNo}
          key={item.payWayNo}
          className={classNames(styles.value, {
            [styles.valueActive]: item.payWayNo === payWayNo,
          })}
          onClick={() => {
            setPayWayNo(item.payWayNo);
          }}
        >
          {item.payWayDesc}
        </div>
      ))}
    </Drawer>
  );
}

SelectSpecs.defaultProps = {
  onClose: () => {},
};

export default SelectSpecs;
