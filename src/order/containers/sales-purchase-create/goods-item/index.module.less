@import 'styles/mixins/mixins';

.goodsItem {
  display: flex;
  margin-bottom: 20px;
}

.checkbox {
  display: flex;
  align-items: center;
  height: 80px;
}

.goodsNameBox {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.goodsImg {
  width: 80px;
  height: 80px;
  margin: 0 12px;
  border-radius: 10px;
  vertical-align: middle;
}

.goodsInfo {
  flex: 1;
}

.goodsName {
  .text-overflow(1);
}

.standardBox {
  display: flex;
  justify-content: space-between;
}

.standard {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 8px;
}

.payWay {
  display: flex;
  width: 100%;
  height: 24px;
  line-height: 22px;
  margin-bottom: 8px;
  padding: 0 8px;
  justify-content: space-between;
  align-items: center;
  border-radius: 6px;
  background: rgb(177 179 190 / 10%);
  cursor: pointer;
}

.payWayDesc {
  .text-overflow(1);
}

.standardList {
  max-width: 180px;
  .text-overflow(1);
}

.standardItem:last-child {
  .punctuate {
    display: none;
  }
}

.delete {
  cursor: pointer;

  &:hover {
    color: #ea1c26;
  }
}

.goodsPrice {
  font-weight: 600;
  margin-right: 4px;
}

.goodsNum {
  color: #888b98;
  font-size: 12px;
}

.goodsMarketPrice {
  color: #888b98;
  font-size: 12px;
  margin-top: 8px;
  text-decoration: line-through;
}

.inputNumber {
  display: flex;
  align-items: center;
  margin-top: 12px;

  :global {
    .adm-stepper {
      width: 130px;
      height: 36px;
      border-radius: 10px;
      background: #f5f6fa;
    }

    .adm-stepper-input {
      width: 100%;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      color: #000;
    }
  }
}
