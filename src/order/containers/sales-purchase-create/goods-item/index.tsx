import { useMemo } from 'react';
import { Checkbox, Popconfirm } from 'antd';
import { Stepper } from 'antd-mobile';
import { Icon } from '@/components';
import { PayWayListResult } from '@/apis';
import styles from './index.module.less';

interface StandardProps {
  name: string;
  value: string;
  groupId: number;
}

interface GoodsItemProps {
  isChecked?: boolean;
  isChosen?: boolean;
  isDelete?: boolean;
  isNum?: boolean;
  image: string;
  name: string;
  standard: StandardProps[];
  currentPayWayNo: number;
  payWayList: PayWayListResult[];
  price: string;
  number: number;
  unit: string;
  isCirclePrice: boolean;
  marketPrice: string;
  onChangeGoods?: () => void;
  onSelectSpecs: () => void;
  onDeleteGoods?: () => void;
  productMinimumQuantity?: number;
  productMinimumSalesUnit?: number;
  onChangeNum?: MultipleParamsFn<[val: number, type: string]>;
}

function GoodsItem({
  isChecked,
  isChosen,
  isDelete,
  isNum,
  image,
  name,
  standard,
  currentPayWayNo,
  payWayList,
  price,
  number,
  unit,
  isCirclePrice,
  marketPrice,
  onChangeGoods,
  onSelectSpecs,
  onDeleteGoods,
  productMinimumQuantity,
  productMinimumSalesUnit,
  onChangeNum,
}: GoodsItemProps) {
  const payWayDesc = useMemo(() => {
    const arr =
      payWayList && payWayList.length
        ? payWayList.filter((item) => item.payWayNo === currentPayWayNo)
        : [];
    if (arr.length) {
      return arr[0].payWayDesc;
    }
    return '';
  }, [currentPayWayNo, payWayList]);

  return (
    <div className={styles.goodsItem}>
      {isChecked && (
        <div className={styles.checkbox}>
          <Checkbox checked={isChosen} onChange={onChangeGoods} />
        </div>
      )}
      <img className={styles.goodsImg} src={image} alt="" />
      <div className={styles.goodsInfo}>
        <div className={styles.goodsNameBox}>
          {isCirclePrice && (
            <img
              className="mr-1"
              src="https://img.huahuabiz.com/user_files/2023424/1682317682218940.png"
              alt=""
            />
          )}
          <span className={styles.goodsName}>{name}</span>
        </div>
        <div className={styles.standardBox}>
          <div className={styles.standard}>
            <span className={styles.standardList}>
              {standard.map((item, index) => (
                <span
                  className={styles.standardItem}
                  key={`${item.name}_${item.value}_${index + 1}`}
                >
                  {item.name}：{item.value}
                  <span className={styles.punctuate}>; </span>
                </span>
              ))}
            </span>
          </div>
        </div>
        {payWayList.length > 0 && (
          <div role="button" tabIndex={0} className={styles.payWay} onClick={onSelectSpecs}>
            <span className={styles.payWayDesc}>{payWayDesc}</span>
            <Icon name="down" />
          </div>
        )}
        <div>
          <span className={styles.goodsPrice}>¥ {price}</span>
          <span className={styles.goodsNum}>{isNum ? `/ ${unit}` : `X ${number}${unit}`}</span>
        </div>
        <div className={styles.goodsMarketPrice}>零售价¥{marketPrice}</div>
        {isNum && (
          <div className={styles.inputNumber}>
            <span className="mr-4">数量</span>
            <Stepper
              value={number}
              min={productMinimumQuantity}
              max={99999999}
              step={productMinimumSalesUnit}
              onChange={(value) => onChangeNum?.(value, '')}
              onBlur={(value) => onChangeNum?.(Number(value.target.value), 'blur')}
            />
          </div>
        )}
      </div>
      {isDelete && (
        <Popconfirm placement="topRight" title="确定删除该商品吗？" onConfirm={onDeleteGoods}>
          <Icon name="trash" size={18} className={styles.delete} />
        </Popconfirm>
      )}
    </div>
  );
}

GoodsItem.defaultProps = {
  isChecked: false,
  isChosen: false,
  isDelete: false,
  isNum: false,
  onChangeGoods: () => {},
  onDeleteGoods: () => {},
  productMinimumQuantity: 0,
  productMinimumSalesUnit: 1,
  onChangeNum: () => {},
};

export default GoodsItem;
