import { ProductSpecificationListResult } from '@/apis';
import isString from 'lodash/isString';
import styles from './index.module.less';

export interface OrderItemGoodsProps {
  image: string;
  name: string;
  standard: ProductSpecificationListResult[] | string;
  price: string;
  number: number;
  unit: string;
}

function OrderItemGoods({ image, name, standard, price, number, unit }: OrderItemGoodsProps) {
  return (
    <div className={styles.goodsItem}>
      <img className={styles.image} src={image} alt="" />
      <div className={styles.goodsItemRight}>
        <div>
          <div className={styles.name}>{name}</div>
          <div className={styles.standard}>
            {isString(standard)
              ? standard
              : standard &&
                standard.length > 0 &&
                standard.map((item, index) => (
                  <span
                    className={styles.standardItem}
                    key={`${item.name}_${item.value}_${index + 1}`}
                  >
                    {item.name}：{item.value}
                    <span className={styles.punctuate}>; </span>
                  </span>
                ))}
          </div>
        </div>
        <div>
          ¥ <span>{price}</span> x {number} {unit}
        </div>
      </div>
    </div>
  );
}

export default OrderItemGoods;
