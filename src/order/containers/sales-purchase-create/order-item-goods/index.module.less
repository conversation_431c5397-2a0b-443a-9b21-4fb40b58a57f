@import 'styles/mixins/mixins';

.goodsItem {
  display: flex;
  margin-top: 20px;
}

.goodsItemRight {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.image {
  width: 108px;
  height: 108px;
  margin-right: 12px;
  border-radius: 10px;
}

.name {
  width: 180px;
  .text-overflow(2);

  margin-bottom: 8px;
}

.standard {
  color: #888b98;
  font-size: 12px;
  width: 180px;
  .text-overflow(1);
}

.standardItem:last-child {
  .punctuate {
    display: none;
  }
}
