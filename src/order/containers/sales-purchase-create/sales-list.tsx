import { useEffect, useRef, useState } from 'react';
import { Drawer, Search, Empty } from '@/components';
import { DrawerProps, Spin, Divider, message } from 'antd';
import debounce from 'lodash/debounce';
import {
  salesPurchaseOrderList,
  SalesPurchaseOrderListResult,
  salesPurchaseSaveOrder,
} from '@/apis';
import { useRequest } from 'ahooks';
import InfiniteScroll from 'react-infinite-scroll-component';
import OrderItem from './order-item';
import { purchaseGoods } from './purchase-goods';
import styles from './sales-list.module.less';

export interface SalesListProps extends DrawerProps {
  onOk: () => void;
}

function SalesList({ onOk, ...props }: SalesListProps) {
  const [orderList, setOrderList] = useState<SalesPurchaseOrderListResult[]>([]);
  const params = useRef({
    pageNo: 1,
    pageSize: 10,
    key: '',
  });
  const totalPages = useRef(0);
  const [hasSelectIds, setHasSelectIds] = useState<number[]>([]);

  const { run, loading } = useRequest(salesPurchaseOrderList, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      totalPages.current = res.pagination.total;
      if (params.current.pageNo === 1) {
        setOrderList(res.list);
      } else {
        setOrderList([...orderList, ...res.list]);
      }
      params.current.pageNo += 1;
    },
  });

  const onSearch = debounce((e: string) => {
    params.current.key = e;
    params.current.pageNo = 1;
    run({ ...params.current });
  }, 300);

  const onLoadMore = () => {
    if (!(params.current.pageNo <= totalPages.current)) return;
    run({ ...params.current });
  };

  const onSelectOrder = (val: boolean, orderNo: number) => {
    if (val) {
      hasSelectIds.push(orderNo);
      setHasSelectIds([...hasSelectIds]);
    } else {
      setHasSelectIds([...hasSelectIds.filter((item) => item !== orderNo)]);
    }
  };

  const onConfirm = debounce(() => {
    if (!hasSelectIds.length) {
      message.warning('请选择订单');
      return;
    }
    salesPurchaseSaveOrder({ orderNoList: hasSelectIds }).then((res) => {
      purchaseGoods({ purchaseId: res.purchaseId, onConfirm: () => onOk() });
    });
  }, 300);

  useEffect(() => {
    run({ ...params.current });
  }, [run]);

  const footer = (
    <Drawer.Footer
      okText="按所选订单生成采购商品"
      disabled={!hasSelectIds.length}
      showCancel={false}
      onOk={onConfirm}
    />
  );

  return (
    <Drawer title="选择销售订单" {...props} className={styles.drawer} footer={footer}>
      <div className={styles.search}>
        <Search placeholder="请输入订单号" onSearch={onSearch} />
      </div>
      <div id="list" className={styles.list}>
        <Spin spinning={params.current.pageNo === 1 && loading} wrapperClassName={styles.spin}>
          {orderList.length > 0 ? (
            <InfiniteScroll
              dataLength={orderList.length}
              hasMore={params.current.pageNo < totalPages.current}
              loader={
                <div className={styles.loader}>
                  <Spin tip="加载中..." />
                </div>
              }
              next={onLoadMore}
              scrollableTarget="list"
              endMessage={
                orderList.length > 0 && (
                  <div className={styles.divider}>
                    <Divider plain>
                      <span className={styles.endMessage}>加载到底了</span>
                    </Divider>
                  </div>
                )
              }
            >
              {orderList.map((item) => (
                <OrderItem
                  checked={hasSelectIds.includes(item.orderNo)}
                  key={item.orderNo}
                  orderTitle={item.buyerCompanyName}
                  orderStatusStr={item.orderStatusStr}
                  orderNumber={item.orderNumber}
                  totalPrice={item.totalPayment}
                  productCatCount={item.productCatCount}
                  goodsList={item.orderProductList.map((goods) => ({
                    image: goods.productMainPic,
                    name: goods.productName,
                    standard: goods.productSpecificationList,
                    price: goods.productPrice,
                    number: goods.number,
                    unit: goods.unit,
                  }))}
                  onSelectOrder={(val) => onSelectOrder(val, item.orderNo)}
                />
              ))}
            </InfiniteScroll>
          ) : (
            <div className={styles.noData}>
              <Empty />
            </div>
          )}
        </Spin>
      </div>
    </Drawer>
  );
}

export default SalesList;
