@import 'styles/mixins/mixins';

.drawer {
  width: 375px !important;
  right: 0 !important;
  left: auto !important;

  :global {
    .ant-drawer-content-wrapper {
      height: 93% !important;
    }

    .ant-drawer-content {
      width: 375px !important;
      right: 0 !important;
      left: auto !important;
      box-shadow: none;
      background-color: #f5f6fa;
    }

    .ant-drawer-mask {
      height: 80% !important;
      border-radius: 12px;
      background: rgb(0 0 0 / 60%) !important;
    }
  }
}

.close {
  font-size: 20px;
  cursor: pointer;
}

.title {
  padding: 8px 0 12px;
}

.titleTotal {
  color: #888b98;
}

.card {
  display: flex;
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.image {
  width: 80px;
  height: 80px;
  margin-right: 12px;
  position: relative;
}

.img {
  width: 80px;
  height: 80px;
  border-radius: 10px;
}

.imgText {
  color: #fff;
  font-size: 12px;
  display: flex;
  width: 100%;
  height: 22px;
  justify-content: center;
  position: absolute;
  bottom: 0;
  left: 0;
  align-items: center;
  border-radius: 0 0 10px 10px;
  background: rgb(0 140 255 / 60%);
  backdrop-filter: blur(4px);
}

.goodsInfo {
  display: flex;
  width: 210px;
  justify-content: space-between;
  flex-direction: column;
}

.name {
  font-weight: 600;
  margin-bottom: 8px;
  .text-overflow(1);
}

.standard {
  color: #888b98;
  font-size: 12px;
  width: 210px;
  .text-overflow(1);
}

.standardItem:last-child {
  .punctuate {
    display: none;
  }
}

.noData {
  display: flex;
  height: 100%;
  margin-top: -50px;
  justify-content: center;
  flex-direction: column;
}

.price {
  font-weight: 600;
}

.unit {
  color: #888b98;
  font-size: 12px;
}
