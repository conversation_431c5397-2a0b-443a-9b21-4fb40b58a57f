import { useEffect, useMemo, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { DrawerProps, Select, Button, Checkbox } from 'antd';
import { Drawer, Icon, Empty } from '@/components';
import { drawerPopup } from '@/utils/popup';
import {
  salesPurchaseSupplierSku,
  PrePurchaseSkuListResult,
  SupplierSkuListResult,
  PurchaseSupplierListResult,
  salesPurchaseGoodsUpdate,
  SalesPurchaseSupplierSkuParams,
  salesPurchaseGoodsUpdateAll,
  salesPurchaseUpdatePayway,
  salesPurchaseSupplierDelete,
} from '@/apis';
import { testPerm } from '@/utils/permission';
import GoodsItem from './goods-item';
import { purchaseGoodsTotal } from './purchase-goods-total';
import SelectSpecs from './select-specs';
import PurchaseGoodsList from './purchase-goods-list';
import styles from './purchase-goods.module.less';

interface PurchaseGoodsProps extends DrawerProps {
  onClose?: () => void;
  purchaseId: number;
  onConfirm: () => void;
}

function PurchaseGoods({ purchaseId, onClose, onConfirm, ...props }: PurchaseGoodsProps) {
  const [showSelectSpecs, setShowSelectSpecs] = useState(false);
  const [showPurchaseGoodsList, setShowPurchaseGoodsList] = useState(false);
  const [checkedAll, setCheckedAll] = useState(false);
  const [goodsList, setGoodsList] = useState<PrePurchaseSkuListResult[]>([]);
  const [supplierList, setSupplierList] = useState<PurchaseSupplierListResult[]>([]);
  const [supplierSkuList, setSupplierSkuList] = useState<SupplierSkuListResult[]>([]);
  const params = useRef<SalesPurchaseSupplierSkuParams>({
    purchaseId,
    supplierComId: null,
  });
  const selectInfo = useRef<PrePurchaseSkuListResult>({
    isChosen: false,
    number: 0,
    productMainPic: '',
    productName: '',
    productPrice: '',
    productSpecificationList: [],
    purchaseProId: 0,
    unit: '',
    currentPayWayNo: 0,
    payWayList: [],
    skuId: 0,
    shopSkuId: 0,
    payWayResp: {
      payWayNo: 0,
    },
  });
  const [totalInfo, setTotalInfo] = useState({
    totalCount: 0,
    purchaseCount: 0,
    num: 0,
  });

  const { run } = useRequest(salesPurchaseSupplierSku, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      if (res.supplierSkuList.length) {
        setCheckedAll(res.supplierSkuList.every((item) => item.isInPurchase));
      }
      setGoodsList(res.prePurchaseSkuList);
      setSupplierList(res.purchaseSupplierList);
      setSupplierSkuList([...res.supplierSkuList]);
      setTotalInfo({
        totalCount: res.totalCount,
        purchaseCount:
          res.purchaseCount - res.supplierSkuList.filter((item) => item.isInPurchase).length,
        num: res.purchaseCount,
      });
    },
  });

  const onChangeSupplier = (val: number) => {
    params.current.supplierComId = val;
    run({ ...params.current });
  };

  const onPurchaseGoodsList = () => {
    setShowPurchaseGoodsList(true);
  };

  const onChangeGoodsAll = (val: boolean) => {
    if (val && params.current.supplierComId) {
      salesPurchaseGoodsUpdateAll({
        purchaseId,
        supplierComId: params.current.supplierComId || 0,
        chosenSkuList: supplierSkuList.map((item) => ({
          purchaseProId: item.purchaseProId,
          shopSkuId: item.shopSkuId,
          choseNumber: item.number,
          choseUnit: item.unit,
          payWayNo: item.currentPayWayNo,
        })),
      });
    } else {
      salesPurchaseSupplierDelete({
        purchaseId,
        supplierComId: Number(params.current.supplierComId),
      });
    }
    supplierSkuList.forEach((item) => {
      const items = item;
      items.isChosen = val;
      items.isInPurchase = val;
    });
    setTotalInfo({
      ...totalInfo,
      num:
        totalInfo.purchaseCount +
        supplierSkuList.filter((item) => item.isChosen || item.isInPurchase).length,
    });
    setSupplierSkuList([...supplierSkuList]);
    setCheckedAll(val);
  };

  const onChangeGoods = (val: PrePurchaseSkuListResult) => {
    salesPurchaseGoodsUpdate({
      choseNumber: val.number,
      choseUnit: val.unit,
      isChoose: val.isChosen ? 0 : 1,
      payWayNo: val.currentPayWayNo,
      purchaseId,
      purchaseProId: val.purchaseProId,
      skuId: val.skuId,
      shopSkuId: val.shopSkuId,
      supplierComId: params.current.supplierComId || 0,
    }).then(() => {
      supplierSkuList.forEach((item) => {
        const items = item;
        if (item.shopSkuId === val.shopSkuId) {
          if (item.isInPurchase) {
            items.isChosen = false;
            items.isInPurchase = false;
          } else {
            items.isChosen = !item.isChosen;
            items.isInPurchase = !item.isInPurchase;
          }
        }
      });
      setTotalInfo({
        ...totalInfo,
        num:
          totalInfo.purchaseCount +
          supplierSkuList.filter((item) => item.isChosen || item.isInPurchase).length,
      });
      setCheckedAll(supplierSkuList.every((item) => item.isChosen || item.isInPurchase));
      setSupplierSkuList([...supplierSkuList]);
    });
  };

  const onSelectSpecs = (val: PrePurchaseSkuListResult) => {
    if (!testPerm('R_001_023_002_001')) {
      return;
    }
    selectInfo.current = val;
    setShowSelectSpecs(true);
  };

  const onchangeGoodsTotal = () => {
    if (!testPerm('R_001_023_002')) {
      return;
    }
    purchaseGoodsTotal({
      purchaseId,
      onConfirm: () => onConfirm(),
      onClose: () => {
        run({ purchaseId, supplierComId: params.current.supplierComId });
      },
    });
  };

  const onCloseDrawer = () => {
    if (showSelectSpecs) {
      setShowSelectSpecs(false);
    } else if (showPurchaseGoodsList) {
      setShowPurchaseGoodsList(false);
    } else {
      onClose?.();
    }
  };

  const onSelectPayWayNo = (val: number, shopSkuId: number) => {
    salesPurchaseUpdatePayway({
      purchaseProId: selectInfo.current.purchaseProId,
      shopSkuId,
      payWayNo: val,
    }).then((res) => {
      supplierSkuList.forEach((item) => {
        const items = item;
        if (item.shopSkuId === shopSkuId) {
          items.currentPayWayNo = val;
          items.productPrice = res.productPrice;
          items.number = res.number;
        }
      });
      setSupplierSkuList([...JSON.parse(JSON.stringify(supplierSkuList))]);
    });
    setShowSelectSpecs(false);
  };

  const isDisabled = useMemo(
    () =>
      totalInfo.purchaseCount === 0 &&
      supplierSkuList.every((item) => !(item.isChosen || item.isInPurchase)),
    [supplierSkuList, totalInfo.purchaseCount]
  );

  const indeterminate = useMemo(
    () =>
      supplierSkuList.some((item) => item.isChosen || item.isInPurchase) &&
      !supplierSkuList.every((item) => item.isChosen || item.isInPurchase),
    [supplierSkuList]
  );

  useEffect(() => {
    run({ purchaseId });
  }, [purchaseId, run]);

  const footer = (
    <div className={styles.footer}>
      <div className={styles.footerLeft}>
        <div>
          <span className={styles.footerNumLabel}>采购种数：</span>
          <span className={styles.footerNumVal}>{totalInfo.num}</span>
        </div>
      </div>
      <Button disabled={isDisabled} type="primary" onClick={onchangeGoodsTotal}>
        选好了
      </Button>
    </div>
  );

  return (
    <Drawer onClose={onCloseDrawer} {...props} push={false} title="采购商品" footer={footer}>
      {goodsList.length > 0 && (
        <>
          <div className={styles.title}>预采清单</div>
          <div className={styles.goodsBox}>
            {goodsList
              .filter((item, index) => index < 6)
              .map((item) => (
                <div className={styles.goodsBoxNum} key={item.purchaseProId}>
                  <img
                    key={item.purchaseProId}
                    className={styles.goodsImg}
                    src={item.productMainPic}
                    alt=""
                  />
                  {supplierSkuList
                    .filter((goods) => goods.isChosen || goods.isInPurchase)
                    .map((goods) => goods.purchaseProId)
                    .includes(item.purchaseProId) ? (
                    <div className={styles.imgText}>已采购</div>
                  ) : null}
                </div>
              ))}
            <div
              role="button"
              tabIndex={0}
              className={styles.goodsBtn}
              onClick={onPurchaseGoodsList}
            >
              <div className={styles.goodsNum}>{goodsList.length}种商品</div>
              <Icon name="right" />
            </div>
          </div>
        </>
      )}
      <div className={styles.tip}>选供货商后，将匹配出预采清单里的商品，可添加下单</div>
      <div className={styles.goodsList}>
        <div className={styles.selectSupplier}>
          <div className={styles.selectSupplierLabel}>
            <Checkbox
              indeterminate={indeterminate}
              className="mr-1"
              onChange={(e) => onChangeGoodsAll(e.target.checked)}
              checked={checkedAll}
              disabled={!params.current.supplierComId}
            />
            供货商
          </div>
          <Select
            options={supplierList.map((item) => ({
              label: item.companyName,
              value: item.companyId,
            }))}
            bordered={false}
            allowClear
            showSearch
            placeholder="请选择供货商"
            suffixIcon={<Icon name="down" className={styles.inputIcon} />}
            className={styles.selectSupplierValue}
            onChange={(e) => onChangeSupplier(e)}
            onClear={() => setCheckedAll(false)}
            onFocus={() => testPerm('R_001_023_001')}
          />
        </div>
        <div className={styles.goodsListBox}>
          {supplierSkuList.length > 0
            ? supplierSkuList.map((item) => (
                <GoodsItem
                  isChecked
                  key={item.purchaseProId}
                  name={item.productName}
                  image={item.productMainPic}
                  number={item.number}
                  price={item.productPrice}
                  currentPayWayNo={item.currentPayWayNo}
                  payWayList={item.payWayList}
                  standard={item.productSpecificationList}
                  unit={item.unit}
                  isCirclePrice={item.isCirclePrice}
                  marketPrice={item.marketPrice}
                  isChosen={item.isChosen || item.isInPurchase}
                  onChangeGoods={() => onChangeGoods(item)}
                  onSelectSpecs={() => onSelectSpecs(item)}
                />
              ))
            : params.current.supplierComId && (
                <div className={styles.noData}>
                  <Empty
                    description={
                      <span>
                        <span>该供货商下没有预采清单中的商品，请</span>
                        <br />
                        <span>切换至其他供货商</span>
                      </span>
                    }
                  />
                </div>
              )}
        </div>
      </div>
      <SelectSpecs
        visible={showSelectSpecs}
        image={selectInfo.current.productMainPic}
        price={selectInfo.current.productPrice}
        standard={selectInfo.current.productSpecificationList}
        unit={selectInfo.current.unit}
        currentPayWayNo={selectInfo.current.currentPayWayNo}
        payWayList={selectInfo.current.payWayList}
        onClose={() => setShowSelectSpecs(false)}
        onConfirm={(val) => onSelectPayWayNo(val, selectInfo.current.shopSkuId)}
      />
      <PurchaseGoodsList
        purchaseId={purchaseId}
        visible={showPurchaseGoodsList}
        onClose={() => setShowPurchaseGoodsList(false)}
      />
    </Drawer>
  );
}

PurchaseGoods.defaultProps = {
  onClose: () => {},
};

export const purchaseGoods = (props: PurchaseGoodsProps) => {
  drawerPopup(PurchaseGoods, props);
};

export default purchaseGoods;
