import { useEffect, useRef, useState } from 'react';
import { Drawer, Empty } from '@/components';
import { DrawerProps, Popconfirm, message } from 'antd';
import { drawerPopup } from '@/utils/popup';
import { useRequest } from 'ahooks';
import BigNumber from 'big.js';
import {
  ChosenSkuListResult,
  salesPurchaseShopSku,
  SalesPurchaseShopSkuResult,
  salesPurchaseGoodsUpdate,
  PrePurchaseSkuListResult,
  salesPurchaseUpdateNum,
  salesPurchaseUpdatePayway,
  salesPurchaseSupplierDelete,
} from '@/apis';
import SelectSpecs from '@@/order/containers/sales-purchase-create/select-specs';
import { testPerm } from '@/utils/permission';
import GoodsItem from './goods-item';
import styles from './purchase-goods-total.module.less';

interface PurchaseGoodsTotalProps extends DrawerProps {
  onClose?: () => void;
  purchaseId: number;
  onConfirm: () => void;
}

function PurchaseGoodsTotal({ onClose, purchaseId, onConfirm, ...props }: PurchaseGoodsTotalProps) {
  const [showSelectSpecs, setShowSelectSpecs] = useState(false);
  const [shopGoodsList, setShopGoodsList] = useState<SalesPurchaseShopSkuResult[]>([]);
  const selectInfo = useRef<PrePurchaseSkuListResult>({
    isChosen: false,
    number: 0,
    productMainPic: '',
    productName: '',
    productPrice: '',
    productSpecificationList: [],
    purchaseProId: 0,
    unit: '',
    currentPayWayNo: 0,
    payWayList: [],
    skuId: 0,
    shopSkuId: 0,
    payWayResp: {
      payWayNo: 0,
    },
  });

  const { run } = useRequest(salesPurchaseShopSku, {
    manual: true,
    defaultParams: [{ purchaseId }],
    onSuccess: (res) => {
      setShopGoodsList(res.list);
    },
  });

  const onDeleteAll = (supplierComId: number) => {
    if (!testPerm('R_001_023_002_002')) {
      return;
    }
    salesPurchaseSupplierDelete({ purchaseId, supplierComId }).then(() => {
      message.warning('删除成功');
      run({ purchaseId });
    });
  };

  const onDeleteGoods = (supplierComId: number, goods: ChosenSkuListResult) => {
    salesPurchaseGoodsUpdate({
      isChoose: 0,
      purchaseId,
      purchaseProId: goods.purchaseProId,
      skuId: goods.skuId,
      supplierComId,
    }).then(() => {
      message.warning('删除成功');
      run({ purchaseId });
    });
  };

  const onChangeNumValue = (purchaseProId: number, updateNumber: number) => {
    salesPurchaseUpdateNum({ purchaseProId, updateNumber });
  };

  const onChangeNum = (
    val: number,
    type: string,
    supplierComId: number,
    info: ChosenSkuListResult
  ) => {
    if (val < info.productMinimumQuantity || val > 99999999) {
      return;
    }
    if (
      Number(info.productMinimumSalesUnit) &&
      BigNumber(val || 0)
        .div(info.productMinimumSalesUnit || 1)
        .toNumber() %
        1 !==
        0
    ) {
      if (type === 'blur') {
        let num = val;
        shopGoodsList.forEach((item) => {
          if (item.supplierComId === supplierComId) {
            item.chosenSkuList.forEach((good) => {
              const goods = good;
              if (goods.shopSkuId === info.shopSkuId) {
                num = BigNumber(Math.ceil(val / Number(info.productMinimumSalesUnit || 1)))
                  .times(Number(info.productMinimumSalesUnit || 1))
                  .toNumber();
                goods.number = num;
              }
            });
          }
        });
        onChangeNumValue(info.purchaseProId, num);
        setShopGoodsList([...shopGoodsList]);
        message.warning('请输入最小销售单元整数倍');
      }
    } else {
      shopGoodsList.forEach((item) => {
        if (item.supplierComId === supplierComId) {
          item.chosenSkuList.forEach((good) => {
            const goods = good;
            if (goods.shopSkuId === info.shopSkuId) {
              goods.number = val;
            }
          });
        }
      });
      onChangeNumValue(info.purchaseProId, val);
      setShopGoodsList([...shopGoodsList]);
    }
  };

  const onSelectPayWayNo = (val: number, shopSkuId: number) => {
    salesPurchaseUpdatePayway({
      purchaseProId: selectInfo.current.purchaseProId,
      shopSkuId,
      payWayNo: val,
    }).then((res) => {
      shopGoodsList.forEach((item) => {
        if (item.shopId === selectInfo.current.shopId) {
          item.chosenSkuList.forEach((good) => {
            const goods = good;
            if (goods.shopSkuId === shopSkuId) {
              goods.payWayResp.payWayNo = val;
              goods.productPrice = res.productPrice;
              goods.number = res.number;
              goods.productMinimumQuantity = res.productMinimumQuantity;
            }
          });
        }
      });
      setShopGoodsList(JSON.parse(JSON.stringify(shopGoodsList)));
    });
    setShowSelectSpecs(false);
  };

  const onSelectSpecs = (shopId: number, val: PrePurchaseSkuListResult) => {
    selectInfo.current = {
      shopId,
      ...val,
    };
    setShowSelectSpecs(true);
  };

  const onCloseDrawer = () => {
    if (showSelectSpecs) {
      setShowSelectSpecs(false);
    } else {
      onClose?.();
    }
  };

  const onSubmit = () => {
    if (!testPerm('R_001_023_002_003')) {
      return;
    }
    const data = shopGoodsList.map((item) => ({
      shopId: item.shopId,
      productParamList: item.chosenSkuList.map((goods) => ({
        shopSkuId: goods.shopSkuId,
        skuId: goods.skuId,
        payWayNo: goods.payWayResp.payWayNo,
        number: goods.number,
        productUnit: goods.unit,
      })),
    }));
    localStorage.setItem('CARTLIST', JSON.stringify(data));
    onConfirm();
  };

  // const totalNum = useMemo(() => {
  //   let num = 0;
  //   if (shopGoodsList.length) {
  //     shopGoodsList.forEach((item) => {
  //       num += item.chosenSkuList.length;
  //     });
  //   }
  //   return num;
  // }, [shopGoodsList]);

  useEffect(() => {
    run({ purchaseId });
  }, [purchaseId, run]);

  const footer = (
    <Drawer.Footer
      disabled={!shopGoodsList.length}
      showCancel={false}
      okText="立即下单"
      onOk={onSubmit}
    />
  );

  return (
    <Drawer
      onClose={onCloseDrawer}
      push={false}
      {...props}
      title="采购确认"
      className={styles.drawer}
      footer={footer}
    >
      {shopGoodsList.length > 0 ? (
        shopGoodsList.map((item) => (
          <div key={item.supplierComId} className={styles.shop}>
            <div className={styles.shopTitle}>
              <span className={styles.shopTitleName}>供货商：{item.supplierComName}</span>
              <Popconfirm
                placement="topRight"
                title="确定删除该供货商下的全部商品吗？"
                onConfirm={() => onDeleteAll(item.supplierComId)}
              >
                <span className={styles.delete}>删除全部</span>
              </Popconfirm>
            </div>
            {item.chosenSkuList &&
              item.chosenSkuList.length > 0 &&
              item.chosenSkuList.map((goods) => (
                <GoodsItem
                  isDelete
                  isNum
                  key={goods.shopSkuId}
                  image={goods.productMainPic}
                  isCirclePrice={goods.isCirclePrice}
                  marketPrice={goods.marketPrice}
                  name={goods.productName}
                  number={goods.number}
                  price={goods.productPrice}
                  currentPayWayNo={goods.payWayResp.payWayNo}
                  payWayList={goods.payWayList}
                  standard={goods.productSpecificationList}
                  unit={goods.unit}
                  onSelectSpecs={() => onSelectSpecs(item.shopId, goods)}
                  onDeleteGoods={() => onDeleteGoods(item.supplierComId, goods)}
                  productMinimumQuantity={goods.productMinimumQuantity}
                  productMinimumSalesUnit={goods.productMinimumSalesUnit}
                  onChangeNum={(val, type) => onChangeNum(val, type, item.supplierComId, goods)}
                />
              ))}
          </div>
        ))
      ) : (
        <div className={styles.noData}>
          <Empty />
        </div>
      )}
      <SelectSpecs
        visible={showSelectSpecs}
        image={selectInfo.current.productMainPic}
        price={selectInfo.current.productPrice}
        standard={selectInfo.current.productSpecificationList}
        unit={selectInfo.current.unit}
        currentPayWayNo={selectInfo.current.payWayResp.payWayNo}
        payWayList={selectInfo.current.payWayList}
        onClose={() => setShowSelectSpecs(false)}
        onConfirm={(val) => onSelectPayWayNo(val, selectInfo.current.shopSkuId)}
      />
    </Drawer>
  );
}

PurchaseGoodsTotal.defaultProps = {
  onClose: () => {},
};

export const purchaseGoodsTotal = (props: PurchaseGoodsTotalProps) => {
  drawerPopup(PurchaseGoodsTotal, props);
};

export default purchaseGoodsTotal;
