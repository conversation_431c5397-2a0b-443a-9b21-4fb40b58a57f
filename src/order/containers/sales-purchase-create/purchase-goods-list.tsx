import { Drawer, Icon, Empty } from '@/components';
import { DrawerProps } from 'antd';
import { useRequest } from 'ahooks';
import { PrePurchaseSkuListResult, salesPurchaseSupplierSku } from '@/apis';
import { useEffect, useRef, useState } from 'react';
import styles from './purchase-goods-list.module.less';

interface PurchaseGoodsListProps extends DrawerProps {
  visible: boolean;
  onClose?: () => void;
  purchaseId: number;
}

function PurchaseGoodsList({ visible, onClose, purchaseId, ...props }: PurchaseGoodsListProps) {
  const [goodsList, setGoodsList] = useState<PrePurchaseSkuListResult[]>([]);
  const params = useRef({
    purchaseId,
    supplierComId: 0,
  });

  const { run } = useRequest(salesPurchaseSupplierSku, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      setGoodsList(res.prePurchaseSkuList);
    },
  });

  useEffect(() => {
    if (visible) {
      run({ purchaseId });
    }
  }, [purchaseId, run, visible]);

  return (
    <Drawer
      visible={visible}
      closable={false}
      onClose={onClose}
      {...props}
      placement="bottom"
      title="预采清单"
      className={styles.drawer}
      extra={<Icon className={styles.close} name="close" onClick={() => onClose?.()} />}
    >
      {goodsList.length > 0 ? (
        <>
          <div className={styles.title}>
            <span className={styles.titleTotal}>预采清单商品共{goodsList.length}种，</span>
            已采购{goodsList.filter((item) => item.isChosen).length}种
          </div>
          {goodsList.map((item) => (
            <div className={styles.card} key={item.purchaseProId}>
              <div className={styles.image}>
                <img className={styles.img} src={item.productMainPic} alt="" />
                {Boolean(item.isChosen) && <div className={styles.imgText}>已采购</div>}
              </div>
              <div className={styles.goodsInfo}>
                <div>
                  <div className={styles.name}>{item.productName}</div>
                  <div className={styles.standard}>
                    <span className={styles.standardList}>
                      {item.productSpecificationList.length > 0 &&
                        item.productSpecificationList.map((each, index) => (
                          <span
                            className={styles.standardItem}
                            key={`${each.name}_${each.value}_${index + 1}`}
                          >
                            {each.name}：{each.value}
                            <span className={styles.punctuate}>; </span>
                          </span>
                        ))}
                    </span>
                  </div>
                </div>
                <div>
                  <span className={styles.price}>¥ {item.productPrice}</span>
                  <span className={styles.unit}>
                    X {item.number}
                    {item.unit}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </>
      ) : (
        <div className={styles.noData}>
          <Empty />
        </div>
      )}
    </Drawer>
  );
}

PurchaseGoodsList.defaultProps = {
  onClose: () => {},
};

export default PurchaseGoodsList;
