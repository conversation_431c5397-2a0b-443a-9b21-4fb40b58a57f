@import 'styles/mixins/mixins';

.drawer {
  width: 375px !important;
  right: 0 !important;
  left: auto !important;

  :global {
    .ant-drawer-content-wrapper {
      height: 93% !important;
    }

    .ant-drawer-content {
      width: 375px !important;
      right: 0 !important;
      left: auto !important;
      box-shadow: none;
      background-color: #f5f6fa;
    }

    .ant-drawer-mask {
      height: 80% !important;
      border-radius: 12px;
      background: rgb(0 0 0 / 60%) !important;
    }
  }
}

.close {
  font-size: 20px;
  cursor: pointer;
}

.goodsInfo {
  display: flex;
  padding: 8px 0 20px;
  border-bottom: 1px solid rgb(177 179 190 / 30%);
}

.goodsInfoRight {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.goodsImg {
  width: 80px;
  height: 80px;
  margin-right: 12px;
  border-radius: 10px;
}

.price {
  font-size: 20px;
}

.hasSelect {
  font-size: 12px;
  .text-overflow(2);
}

.standardList {
  padding-bottom: 24px;
  border-bottom: 1px solid rgb(177 179 190 / 30%);
}

.title {
  font-size: 16px;
  font-weight: 600;
  padding: 24px 0 8px;
}

.value {
  width: fit-content;
  min-width: 46px;
  max-width: 343px;
  height: 38px;
  line-height: 36px;
  margin-bottom: 8px;
  padding: 0 12px;
  text-align: center;
  border-radius: 10px;
  cursor: pointer;
  background-color: #fff;
  .text-overflow(1);
}

.valueActive {
  color: #008cff;
  border: 1px solid #008cff;
}
