@import 'styles/mixins/mixins';

.shop {
  margin-bottom: 20px;
  padding: 0 20px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.shopTitle {
  display: flex;
  padding: 20px 0;
  justify-content: space-between;
}

.shopTitleName {
  font-size: 16px;
  font-weight: 500;
  display: inline-block;
  width: 230px;
  .text-overflow(1);
}

.goods {
  display: flex;
  align-items: center;
}

.delete {
  color: #888b98;
  cursor: pointer;

  &:hover {
    color: #ea1c26;
  }
}

.noData {
  display: flex;
  height: 100%;
  margin-top: -50px;
  justify-content: center;
  align-items: center;
}
