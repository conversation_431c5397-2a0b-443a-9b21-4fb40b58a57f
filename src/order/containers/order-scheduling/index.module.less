.card {
  margin-bottom: 20px;
  padding: 0 20px 16px;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;

  .goodsItem {
    border-bottom: 1px solid #f3f3f3;
    padding: 20px 0;
  }
}

.cardFooter {
  display: flex;
  margin-top: 16px;
  justify-content: space-between;
  align-items: center;

  :global {
    .adm-stepper {
      width: 135px;
      border-radius: 10px;
    }

    .adm-stepper-middle {
      border: none;
    }

    .adm-button:not(.adm-button-default).adm-button-fill-none {
      color: black;
    }

    .adm-stepper .adm-stepper-input,
    .adm-button:not(.adm-button-default).adm-button-fill-none {
      height: 36px;
      background: #f5f6fa;
    }
  }

  .text {
    font-family: PingFangSC-Regular;
    color: #888b98;
  }
}

.footer {
  display: flex;
  width: 100%;
  padding: 20px;
  justify-content: space-between;
  align-items: center;

  .footerText {
    font-size: 12px;
  }
}
