import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { Drawer } from '@/components';
import { Stepper } from 'antd-mobile';
import { But<PERSON>, message, Spin } from 'antd';
import {
  getProductionGoodsList,
  ProductionGoodsListResult,
  postProductionOrderSave,
  postOrderOperatePoint,
} from '@/apis';
import { DrawerRefType } from '@/components/drawer/drawer';
import GoodsItem from '../../components/goods-card/index';
import styles from './index.module.less';

interface OrderSchedulingProps {
  visible: boolean;
  orderNo: number;
  batchNo?: number;
  orderFuncType: number;
  nycbngOrgId: string;
  updateWidth: () => void;
  onClose: () => void;
  flowSerialNo: number;
}

function OrderScheduling({
  visible,
  orderNo,
  batchNo,
  orderFuncType,
  nycbngOrgId,
  onClose,
  updateWidth,
  flowSerialNo,
}: PropsWithChildren<OrderSchedulingProps>) {
  const drawerRef = useRef<DrawerRefType>(null);
  const [goodsList, setGoodsList] = useState<ProductionGoodsListResult[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  // 表单改变
  const formChange = () => {
    if (!drawerRef.current?.getIsChange()) {
      drawerRef.current?.setIsChange(true);
    }
  };
  const getProductionGoods = () => {
    setLoading(true);
    getProductionGoodsList({ orderNo, batchNo }, nycbngOrgId).then((res) => {
      setGoodsList(
        res.list.map((item) => ({
          ...item,
          goodsNumber: 0,
        }))
      );
      setLoading(false);
    });
  };

  const onSubmit = () => {
    const saveProductionDetailList = goodsList
      .filter((item) => item.goodsNumber)
      .map((item) => ({
        productDetailNo: item.productDetailNo,
        number: item.goodsNumber,
      }));
    if (!saveProductionDetailList.length) {
      message.warning('请输入生产数量');
      return;
    }
    const fn = orderFuncType === 0 ? postProductionOrderSave : postOrderOperatePoint;
    const orderFlowSerialNo = orderFuncType === 0 ? null : flowSerialNo;
    fn(
      {
        flowSerialNo: orderFlowSerialNo,
        orderNo,
        batchNo: batchNo || null,
        saveProductionDetailList,
      },
      orderFuncType === 0 ? '' : nycbngOrgId
    )
      .then(() => {
        message.success('操作成功');
        onClose();
        updateWidth();
      })
      .catch((e) => {
        message.warning(e.message);
      });
  };

  useEffect(() => {
    if (visible) {
      getProductionGoods();
    }
  }, [visible]); // eslint-disable-line
  return (
    <Drawer
      ref={drawerRef}
      visible={visible}
      title="生产排期"
      onClose={() => {
        onClose();
      }}
      footer={
        <div className={styles.footer}>
          <div className={styles.footerText}>
            已选 {goodsList.filter((item) => item.goodsNumber).length}种 商品
          </div>
          <Button type="primary" onClick={onSubmit}>
            确定
          </Button>
        </div>
      }
    >
      <Spin tip="loading..." spinning={loading}>
        {goodsList.map((item) => (
          <div className={styles.card} key={item.productDetailNo}>
            <div className={styles.goodsItem}>
              <GoodsItem
                orderType="inventory"
                img={item.productMainPic}
                productName={item.productName}
                productUnit={item.productUnit}
                stock={item.stockNumber}
                productSpecificationList={item.productSpecificationList}
              />
            </div>
            <div className={styles.cardFooter}>
              <div className={styles.text}>生产数量</div>
              <Stepper
                min={0}
                max={999999999}
                onChange={(value) => {
                  setGoodsList(
                    goodsList.map((goodsItem) => {
                      if (item.productDetailNo === goodsItem.productDetailNo) {
                        return {
                          ...goodsItem,
                          goodsNumber: value,
                        };
                      }
                      return goodsItem;
                    })
                  );
                  // const it = item;
                  // it.goodsNumber = value;
                  formChange();
                }}
              />
            </div>
          </div>
        ))}
      </Spin>
    </Drawer>
  );
}

OrderScheduling.defaultProps = {
  batchNo: null,
};

export default OrderScheduling;
