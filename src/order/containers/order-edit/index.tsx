import { DateP<PERSON>, Drawer, Icon, Price, SimpleUploadInstance, Upload } from '@/components';
import { Button, Form, Input, InputNumber, message, Select, Switch, Tooltip, Modal } from 'antd';
import { Stepper } from 'antd-mobile';
import classNames from 'classnames';
import { Fragment, PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import {
  CustomerResult,
  formulaConvertUnitManyConvert,
  FormulaConvertUnitManyConvertResult,
  GoodsListResult,
  getOrderUnitConversion,
  StockGoodsListResult,
  // getDistributionCustomer,
  getSupplierOptions,
  supplierListMy,
} from '@/apis';
import { PayWayList, postAddOrder } from '@/apis/order';
import dayjs, { Dayjs } from 'dayjs';
import { Timeout } from 'ahooks/es/useRequest/src/types';
import BigNumber from 'big.js';
import { ListResult } from '@/apis/measure';
import { user } from '@/store';
import { DataAddOrder } from '@/apis/order/post-new-order';
import isNil from 'lodash/isNil';
import { DrawerRefType } from '@/components/drawer/drawer';
import { testPerm } from '@/utils/permission';
import SelectGoods, { SelectGoodsRefType } from '../select-goods/index';
import OrderPayment from '../order-payment/index';
import GoodsCard from '../../components/goods-card/index';
import ChildDrawer from '../../components/child-drawer/child-drawer';
import OrderProject from '../order-project';
import styles from './index.module.less';
import discount from '../../img/discount.png';

interface EditOrderProps {
  visible: boolean;
  onClose: () => void;
  getOrderList: () => void;
  type: number | string | null;
  distributionCompanyId?: number;
  distributionCompanyName?: string;
  distribution?: number | null;
  nycbngOrgId: string;
}

// eslint-disable-next-line no-unused-vars
function useDebounce(fn: (args: unknown) => void, delay: number) {
  const refTimer = useRef<Timeout>();
  return function f(...args: any) {
    if (refTimer.current) {
      clearTimeout(refTimer.current);
    }
    refTimer.current = setTimeout(() => {
      fn(args);
    }, delay);
  };
}

function EditOrder({
  visible,
  onClose,
  type,
  getOrderList,
  distributionCompanyId,
  distributionCompanyName,
  distribution,
  nycbngOrgId,
}: PropsWithChildren<EditOrderProps>) {
  const [iconItem] = useState([
    {
      name: '添加商品',
      iconName: 'plus-outline',
      isEdit: true,
      code: type === '1' ? 'R_001_009_016' : 'R_001_010_012',
    },
    {
      name: '按折扣定价',
      iconName: 'share',
      isEdit: true,
      code: type === '1' ? 'R_001_009_017' : 'R_001_010_013',
    },
    {
      name: '批量设置',
      iconName: 'control',
      isEdit: true,
      code: type === '1' ? 'R_001_009_018' : 'R_001_010_014',
    },
    {
      name: '附件备注',
      iconName: 'clip',
      isEdit: true,
      code: type === '1' ? 'R_001_009_019' : 'R_001_010_015',
    },
  ]);
  const { Option } = Select;
  const drawerRef = useRef<DrawerRefType>(null);
  const [childDrawerTitle, setChildDrawerTitle] = useState('');
  const [showChildDrawer, setShowChildDrawer] = useState(false);
  const [showSelectGoods, setShowSelectGoods] = useState(false);
  const [showOrderPayment, setShowOrderPayment] = useState(false);
  const [showOrderProject, setShowOrderProject] = useState(false);
  const [form] = Form.useForm();
  const [switchValue, setSwitchValue] = useState(false);
  const [supplier, setSupplier] = useState<CustomerResult[]>([]);
  const [customers, setCustomers] = useState<CustomerResult[]>([]);
  const [payment, setPayment] = useState<PayWayList>(Object);
  const [matEndowment, setMatEndowment] = useState<PayWayList>();
  const [goodsList, setGoodsList] = useState<GoodsListResult[]>([]);
  const [showAttachment, setShowAttachment] = useState(false);
  const [pricingId, setPriceId] = useState<number>(1);
  const [pricing, setPrice] = useState('');
  const [projectSwitch, setProjectSwitch] = useState(false);
  const [project, setProject] = useState<ListResult>();
  const [remark, setRemark] = useState('');
  const [endowmentId, setEndowmentId] = useState(0);
  const [unitList, setUnitList] = useState<FormulaConvertUnitManyConvertResult[]>([]);
  const [fileListNum, setFileListNum] = useState(0);
  const [btnLoading, setBtnLoading] = useState(false);
  const uploadRef = useRef(null as unknown as SimpleUploadInstance);
  const selectGoodsRef = useRef(null as unknown as SelectGoodsRefType);
  const data = useRef({
    orderType: false,
    supplierCompanyId: 0,
    messagesEnd: null,
  });
  const params = useRef({
    type: 0,
    tagId: '',
    pageNo: 1,
    pageSize: 10,
    customerName: '',
    totalPages: 0,
    customerType: 0,
  });
  const paramsCustomer = useRef({
    type: 0,
    tagId: '',
    pageNo: 1,
    pageSize: 10,
    customerName: '',
    totalPages: 0,
  });

  const totalAmount = useMemo(() => {
    const mount = new BigNumber(0);
    let total = new BigNumber(0).toNumber();
    goodsList.forEach((item) => {
      // @ts-ignore
      const x = new BigNumber(
        pricingId === 1 || pricingId === 3 ? item.totalPrice || 0 : item.preferential || 0
      )
        // @ts-ignore
        .times(item.number)
        .toNumber();
      total += mount.plus(x).toNumber();
    });
    return total || 0;
  }, [goodsList, pricingId]);

  // 表单改变
  const formChange = () => {
    if (!drawerRef.current?.getIsChange()) {
      drawerRef.current?.setIsChange(true);
    }
  };

  // 获取供应商列表
  const getSupplierList = (val?: string) => {
    supplierListMy({
      pageNo: params.current.pageNo,
      pageSize: params.current.pageSize,
      keyword: val,
      companyId: distributionCompanyId || user.companyId,
    }).then((res) => {
      if (val) {
        // @ts-ignore
        setSupplier([...res.list]);
        return;
      }
      params.current.pageNo += 1;
      params.current.totalPages = res.pagination.total;
      // @ts-ignore
      setSupplier([...supplier, ...res.list]);
    });
  };

  // 获取客户列表
  const getCustomerList = (val?: string) => {
    const api = getSupplierOptions({
      customerType: 1,
      pageNo: paramsCustomer.current.pageNo,
      pageSize: val ? 10 : paramsCustomer.current.pageSize,
      keyword: val,
      companyId: distributionCompanyId || user.companyId,
      onlySupplier: 1,
    });
    api.then((res) => {
      if (val) {
        setCustomers(res.list);
        return;
      }
      paramsCustomer.current.pageNo += 1;
      paramsCustomer.current.totalPages = res.pagination.total;
      setCustomers([...customers, ...res.list]);
    });
  };

  const clickIcon = (iconName: string) => {
    switch (iconName) {
      case 'plus-outline':
        setShowSelectGoods(true);
        break;
      case 'share':
        setChildDrawerTitle('商品的定价方式');
        setShowChildDrawer(true);
        break;
      case 'control':
        if (!goodsList.length) {
          message.warning('请先添加商品');
          return;
        }
        setChildDrawerTitle('批量设置');
        setShowChildDrawer(true);
        break;
      default:
        setShowAttachment(true);
        formChange();
        iconItem.forEach((item) => {
          const items = item;
          if (item.name === '附件备注') {
            items.isEdit = false;
          }
        });
        setTimeout(() => {
          const node = document.querySelector('#anchorId');
          node?.scrollIntoView({ behavior: 'smooth' });
        }, 300);
    }
    // return false;
  };

  const onsubmit = () => {
    if (totalAmount < 0) {
      message.error('商品总金额不能为负数');
      return;
    }
    if (totalAmount >= 10000000000) {
      message.error('单笔订单总金额过大');
      return;
    }
    if (!goodsList || !goodsList.length) {
      message.error('请添加商品');
      return;
    }
    if (goodsList.some((item) => !item.number)) {
      message.error('请输入商品数量');
      return;
    }
    if (goodsList.some((item) => !item.preferential && !item.totalPrice)) {
      message.error('请输入商品价格');
      return;
    }
    if (
      goodsList.some(
        (item) => item.totalPrice && item.totalPrice < (item.auxiliaryFloorPrice || item.floorPrice)
      )
    ) {
      message.error('商品单价不能小于底价');
      return;
    }
    form.validateFields().then((formValue) => {
      const paramsValue: DataAddOrder = {
        orderEnterType: distributionCompanyId ? 2 : 1,
        supplementEnterType: type === '0' ? 2 : 1,
        projectId: project?.id,
        isDistributorLoan: switchValue ? 1 : 0,
        loanPayWayNo: matEndowment?.payWayNo,
        loanType: formValue.endowmentWay,
        loanBill: formValue.matAmount,
        payWayNo: payment?.payWayNo,
        orderTime: String(dayjs(formValue.orderTime).valueOf()),
        crmCorfId: formValue.supplierId || formValue.customerId,
        remark: formValue.note,
        productList: goodsList.map((item) => ({
          shopSkuId: item.id,
          skuId: item.skuId,
          number: item.number,
          productPrice: item.preferential,
          productUnit: item.auxiliaryUnit || item.unit,
          pricingMethod: pricingId,
          payWayNo: payment?.payWayNo,
        })),
        attachmentUrls:
          uploadRef.current?.getFileList().map((item) => ({
            fileUrl: item.url,
            fileName: item.name,
            size: item.size,
            fileType: item.type === 'image/png' ? 2 : 1,
          })) || [],
      };
      paramsValue.distributionOrgId = distribution || null;
      if (distributionCompanyId) {
        paramsValue.sellerCompanyId = distributionCompanyId;
        paramsValue.buyerCompanyId = customers.filter(
          (item) => item.customerId === formValue.customerId
        )[0].customerCompanyId;
      } else {
        paramsValue.buyerOrSellerComId =
          type === '0'
            ? supplier.filter((item) => item.id === formValue.supplierId)[0].customerCompanyId
            : customers.filter((item) => item.customerId === formValue.customerId)[0]
                .customerCompanyId;
      }
      setBtnLoading(true);
      postAddOrder(paramsValue, nycbngOrgId)
        .then(() => {
          message.success('操作成功');
          onClose();
          getOrderList();
        })
        .finally(() => {
          setBtnLoading(false);
        });
    });
  };

  const getPayItem = (payItem: PayWayList[] | undefined) => {
    payItem?.forEach((item) => {
      if (data.current.orderType) {
        form.setFieldsValue({ endowment: item.name });
        setMatEndowment(item);
      } else {
        form.setFieldsValue({ payment: item.name });
        setPayment(item);
      }
    });
    formChange();
  };

  const getOrderGoods = (arr: StockGoodsListResult[]) => {
    const list = arr.map((item) => ({
      ...item,
      marketPrice: item.marketPrice ? item.marketPrice : 0,
      saleGroupStr: Number(item.saleGroupStr),
      // totalPrice: item.,
      preferential: item.preferential || null,
    }));
    setGoodsList(list as unknown as GoodsListResult[]);
    setShowSelectGoods(false);
    formChange();
  };

  const getPricing = (id: number, title: string) => {
    setPriceId(id);
    setPrice(title);
    setGoodsList(
      goodsList.map((item) => ({
        ...item,
        preferential: null,
        totalPrice: null,
      }))
    );
    formChange();
  };

  // 批量设置，每个商品的最终价格
  const initLastPrice = (item: GoodsListResult, value: number) => {
    let totalPrice = 0;
    if (pricingId === 1) {
      totalPrice = new BigNumber(value / 100).times(item.market || item.marketPrice).toNumber();
    } else if (pricingId === 2) {
      totalPrice = new BigNumber(value).toNumber();
    } else if (pricingId === 3) {
      if (value > item.marketPrice) {
        totalPrice = 0;
      } else {
        totalPrice = new BigNumber(item.market || item.marketPrice).minus(value).toNumber();
      }
    }
    return totalPrice;
  };

  // 批量设置，处理输入数值
  const initPreferential = (item: GoodsListResult, value: number) => {
    if (pricingId === 3 && value > item.marketPrice) {
      return item.marketPrice;
    }
    return value;
  };

  // 批量设置商品价格、数量
  const getBatchValue = (value?: number | null, number?: number | null) => {
    setGoodsList(
      goodsList.map((item) => ({
        ...item,
        preferential: isNil(value) ? item.preferential : initPreferential(item, value),
        number: isNil(number)
          ? item.number
          : Math.ceil(number / Number(item.productMinimumSalesUnit || item.saleGroupStr)) *
            Number(item.productMinimumSalesUnit || item.saleGroupStr),
        totalPrice: isNil(value) ? item.totalPrice : initLastPrice(item, value),
      }))
    );
    formChange();
  };

  const getProject = (value: ListResult) => {
    setProject(value);
    form.setFieldsValue({ project: value.projectName });
  };

  // 获取单位
  const getUnit = (templateId: number, mainUnitName: string) => {
    formulaConvertUnitManyConvert({
      templateId,
      mainUnitName,
    }).then((res) => {
      setUnitList(res.list);
    });
  };

  // 切换单位
  const onchangeUnit = (item: GoodsListResult, e: string) => {
    getOrderUnitConversion({
      skuId: item.skuId,
      productMarketPrice: item.marketPrice,
      productMinimumQuantity: item.minimum,
      productMinimumSalesUnit: String(item.saleGroupStr),
      productUnit: e,
      productUnitBefore: item.auxiliaryUnit || item.unit,
      number: item.number,
      stockNumber: item.stockNumber || item.stock,
      floorPrice: item.floorPrice,
    }).then((res) => {
      setGoodsList(
        goodsList.map((i) => {
          if (i.skuId === item.skuId) {
            if (item.preferential) {
              return {
                ...i,
                stockNumber: res.stockNumber,
                auxiliaryFloorPrice: Number(res.floorPrice),
                stock: res.stockNumber,
                auxiliaryUnit: e,
                number: Number(res.number),
                market: Number(res.productMarketPrice),
                productMinimumSalesUnit: res.productMinimumSalesUnit,
                productMinimumQuantity: res.productMinimumQuantity,
                totalPrice:
                  pricingId === 1
                    ? new BigNumber(item.preferential || 0)
                        .div(100)
                        .times(Number(res.productMarketPrice))
                        .toNumber()
                    : new BigNumber(res.productMarketPrice || item.marketPrice)
                        .minus(item.preferential || 0)
                        .toNumber(),
              };
            }
            return {
              ...i,
              stockNumber: res.stockNumber,
              auxiliaryFloorPrice: Number(res.floorPrice),
              stock: res.stockNumber,
              auxiliaryUnit: e,
              number: Number(res.number),
              market: Number(res.productMarketPrice),
              productMinimumSalesUnit: res.productMinimumSalesUnit,
              productMinimumQuantity: res.productMinimumQuantity,
              totalPrice: null,
            };
          }
          return i;
        }) as GoodsListResult[]
      );
    });
  };

  // 切换数量
  const onChangeNum = (item: GoodsListResult, value: number, event: string) => {
    if (
      (item.saleGroupStr &&
        // @ts-ignore
        BigNumber(value).div(item.saleGroupStr) % 1 !== 0 &&
        !item.productMinimumSalesUnit) ||
      (item.productMinimumSalesUnit &&
        // @ts-ignore
        BigNumber(value).div(item.productMinimumSalesUnit) % 1 !== 0)
    ) {
      if (event === 'blur') {
        setGoodsList(
          goodsList.map((it) => {
            if ((it.id || it.skuId) === (item.id || item.skuId)) {
              return {
                ...it,
                number:
                  Math.ceil(value / Number(item.productMinimumSalesUnit || item.saleGroupStr)) *
                  Number(item.productMinimumSalesUnit || item.saleGroupStr),
              };
            }
            return it;
          })
        );
        return message.error('请输入最小销售单元整数倍');
      }
    }
    setGoodsList(
      goodsList.map((each) => {
        if ((each.id || each.skuId) === (item.id || item.skuId)) {
          return {
            ...each,
            number: value,
            num: value,
          };
        }
        return each;
      })
    );
    return false;
  };

  const testPermission = (code: string): boolean => {
    if (
      !testPerm(code, () => {
        selectGoodsRef.current?.setNotCloseConfirm(true);
        drawerRef.current?.setIsChange(false);
        return true;
      })
    )
      return false;
    return true;
  };

  const paywayClick = (bool: boolean) => {
    if (!testPermission(type === '1' ? 'R_001_009_014' : 'R_001_010_010')) return;
    data.current.orderType = bool;
    setShowOrderPayment(true);
  };

  const disabledDateStart = (current: Dayjs) => current && current > dayjs().endOf('day');

  useEffect(() => {
    if (visible) {
      if (!testPerm('L_001_001_001_001')) return;
      params.current.pageNo = 1;
      paramsCustomer.current.pageNo = 1;
      if (distributionCompanyId) {
        getCustomerList();
        return;
      }
      if (type === '1') {
        getCustomerList();
      } else {
        getSupplierList();
      }
    } else {
      form.resetFields();
      setCustomers([]);
      setGoodsList([]);
      setSwitchValue(false);
      setProjectSwitch(false);
      setPrice('按折扣定价');
      setPriceId(1);
      setShowAttachment(false);
      setSupplier([]);
      iconItem.forEach((item) => {
        const items = item;
        if (item.name === '附件备注') {
          items.isEdit = true;
        }
      });
    }
  }, [visible]); // eslint-disable-line

  const PriceWay = (item: GoodsListResult) => {
    let pricingNote = null;

    if (pricingId === 1) {
      pricingNote = (
        <div className={styles.discount}>
          <div className={styles.unitPrice}>
            <Tooltip title={item.market || item.marketPrice}>
              ￥{item.market || item.marketPrice}
            </Tooltip>
          </div>
          <span className={styles.mar}>x</span>
          <InputNumber
            // @ts-ignore
            value={item.preferential}
            placeholder="折扣值"
            min={0}
            max={100}
            // type="number"
            controls={false}
            bordered={false}
            onChange={(value) => {
              const items = item;
              if (!value) {
                setGoodsList(
                  goodsList.map((it) => {
                    if ((it.id || it.skuId) === (item.id || item.skuId)) {
                      return {
                        ...it,
                        preferential: 0,
                      };
                    }
                    return it;
                  })
                );
                return;
              }
              if (item.market || item.marketPrice) {
                const x = new BigNumber(value).div(100).toNumber();
                const y = new BigNumber(x).times(item.market || item.marketPrice || 0).toNumber();
                items.totalPrice = y || 0;
              }
              setGoodsList(
                // @ts-ignore
                goodsList.map((it) => {
                  if ((it.id || it.skuId) === (item.id || item.skuId)) {
                    return {
                      ...it,
                      preferential: value,
                    };
                  }
                  return it;
                })
              );
              formChange();
            }}
          />
          <span className={styles.mar}>%</span>
          <span className={styles.mar}>=</span>
          <span className={styles.totalAmount}>
            <Tooltip title={item.totalPrice ? `￥${item.totalPrice}` : '最终价'} placement="top">
              {item.totalPrice ? `￥${item.totalPrice}` : '最终价'}
            </Tooltip>
          </span>
        </div>
      );
    } else if (pricingId === 2) {
      pricingNote = (
        <div className={styles.market}>
          <span>一口价：￥&nbsp;</span>
          <InputNumber
            // @ts-ignore
            value={item.preferential}
            min={0}
            max={999999999}
            placeholder="输入金额"
            // type="number"
            bordered={false}
            controls={false}
            onChange={(value) => {
              const mount = new BigNumber(0);
              let total = 0;
              goodsList.forEach((it) => {
                // @ts-ignore
                const x = new BigNumber(it.preferential)
                  // @ts-ignore
                  .times(it.number)
                  .toNumber();
                total += mount.plus(x).toNumber();
              });
              if (total >= 9999999999) {
                setGoodsList(
                  goodsList.map((each: GoodsListResult) => {
                    if ((each.id || each.skuId) === (item.id || item.skuId)) {
                      return {
                        ...each,
                        preferential: each.num,
                      };
                    }
                    return each;
                  })
                );
                return message.error('商品总价不能超过99亿');
              }
              setGoodsList(
                // @ts-ignore
                goodsList.map((each: GoodsListResult) => {
                  if ((each.id || each.skuId) === (item.id || item.skuId)) {
                    return {
                      ...each,
                      preferential: value || 0,
                      num: value || 0,
                    };
                  }
                  return each;
                })
              );
              formChange();
              return false;
            }}
          />
          <span className={styles.marketPrice}> 市场价：{item.marketPrice}</span>
        </div>
      );
    } else {
      pricingNote = (
        <div>
          <span>￥{item.market || item.marketPrice}</span>
          <span className={styles.mar}>-</span>
          <InputNumber
            max={item.marketPrice}
            // @ts-ignore
            value={item.preferential}
            placeholder="优惠金额"
            // type="number"
            bordered={false}
            controls={false}
            onChange={(value) => {
              const x = new BigNumber(item.market || item.marketPrice).minus(value || 0).toNumber();
              setGoodsList(
                // @ts-ignore
                goodsList.map((each: GoodsListResult) => {
                  if ((each.id || each.skuId) === (item.id || item.skuId)) {
                    return {
                      ...each,
                      totalPrice: x,
                      preferential: value,
                    };
                  }
                  return each;
                })
              );
              formChange();
            }}
            onInput={(val) => {
              // @ts-ignore
              if (Number(val) > item.marketPrice) {
                message.warning('商品单价不能为负数');
                setGoodsList(
                  goodsList.map((it) => ({
                    ...it,
                    preferential: item.skuId === it.skuId ? null : it.preferential,
                    totalPrice: item.skuId === it.skuId ? null : it.totalPrice,
                  }))
                );
                formChange();
              }
            }}
          />
          <span className={styles.mar}>=</span>
          <Tooltip title={item.totalPrice ? `￥${item.totalPrice}` : '最终价'}>
            <span className={styles.totalAmount}>
              {item.preferential || item.totalPrice ? `￥${item.totalPrice}` : '最终价'}
            </span>
          </Tooltip>
        </div>
      );
    }
    return pricingNote;
  };
  let supplierForm = null;
  let customerForm = null;
  let endowment = null;

  const isEditPrice = useMemo(() => goodsList.some((item) => item.preferential), [goodsList]);

  // 客户
  const customerHTML = (
    <div className={styles.noBorder}>
      <Form.Item name="customerId" label="客户" rules={[{ required: true, message: '请选择客户' }]}>
        <Select
          bordered={false}
          allowClear
          showSearch
          placeholder="选择客户"
          optionFilterProp="children"
          suffixIcon={<Icon name="down" className={styles.inputIcon} />}
          onPopupScroll={() => {
            if (paramsCustomer.current.pageNo > paramsCustomer.current.totalPages) return;
            getCustomerList();
          }}
          onDropdownVisibleChange={() => {
            form.validateFields(['supplierId']);
          }}
          onSearch={(val) => {
            paramsCustomer.current.pageNo = 1;
            getCustomerList(val);
          }}
          onChange={(e) => {
            if (e) {
              formChange();
            }
          }}
        >
          {customers.map((item) => (
            <Option value={item.customerId}>{item.customerName}</Option>
          ))}
        </Select>
      </Form.Item>
    </div>
  );

  // 供应商
  const supplierHTML = (
    <div className={styles.noBorder}>
      <Form.Item
        name="supplierId"
        label="供应商"
        rules={[{ required: true, message: '请选择供应商' }]}
      >
        <Select
          bordered={false}
          placeholder="选择供应商"
          showSearch
          optionFilterProp="children"
          onDropdownVisibleChange={() => {
            form.validateFields(['supplierId']);
          }}
          suffixIcon={<Icon name="down" className={styles.inputIcon} />}
          onPopupScroll={() => {
            if (params.current.pageNo > params.current.totalPages) return;
            getSupplierList();
          }}
          onSearch={(val) => {
            params.current.pageNo = 1;
            getSupplierList(val);
          }}
          onChange={(value) => {
            data.current.supplierCompanyId = supplier.filter(
              (item) => item.id === value
            )[0].customerCompanyId;
            formChange();
            getCustomerList();
          }}
        >
          {supplier.map((item) => (
            <Option value={item.id}>{item.customerName}</Option>
          ))}
        </Select>
      </Form.Item>
    </div>
  );

  if (type === '1') {
    customerForm = customerHTML;
  }
  if (type === '0') {
    supplierForm = supplierHTML;
  }

  if (distributionCompanyId) {
    customerForm = customerHTML;
    supplierForm = (
      <div className={styles.distributionSupplier}>
        <Form.Item label="供应商" required>
          <Select
            defaultValue={distributionCompanyId}
            bordered={false}
            suffixIcon={<Icon name="down" className={styles.inputIcon} />}
            disabled
            options={[{ label: distributionCompanyName, value: distributionCompanyId }]}
            onChange={(e) => {
              if (e) {
                formChange();
              }
            }}
          />
        </Form.Item>
      </div>
    );
    endowment = (
      <>
        <div className={styles.switch} style={switchValue ? {} : { paddingBottom: '10px' }}>
          <div className={styles.distributorAdvance}>
            <span>分销商垫资</span>
            <Tooltip title="生成分销商和商户订单，分销商必须完成垫资后 ，分销单才能正常流转">
              <Icon name="info" className={styles.iconTip} />
            </Tooltip>
          </div>
          <Switch
            checked={switchValue}
            onChange={(checked) => {
              if (checked) {
                setSwitchValue(true);
              } else {
                setSwitchValue(false);
              }
              if (checked) {
                formChange();
              } else {
                drawerRef.current?.setIsChange(false);
              }
            }}
          />
        </div>
        {switchValue ? (
          <div className={styles.mandatory}>
            <div
              className={styles.formItem}
              role="button"
              tabIndex={0}
              onClick={() => {
                paywayClick(true);
              }}
            >
              <Form.Item
                name="endowment"
                rules={[{ required: switchValue, message: '请选择付款方式' }]}
                label="付款方式"
              >
                <Input
                  // value={distributionPay?.name}
                  placeholder="请选择付款方式"
                  suffix={<Icon name="right" className={styles.inputIcon} />}
                  bordered
                  disabled
                />
              </Form.Item>
            </div>
            <div
              className={classNames(
                endowmentId === 0 || endowmentId === 2 ? styles.noBorder : styles.endowmentWay
              )}
            >
              <Form.Item
                name="endowmentWay"
                label="垫资方式"
                rules={[{ required: switchValue, message: '请选择垫资方式' }]}
              >
                <Select
                  placeholder="请选择垫资方式"
                  suffixIcon={<Icon name="down" className={styles.inputIcon} />}
                  onChange={(e) => {
                    setEndowmentId(e);
                    formChange();
                  }}
                >
                  <Option value={1}>按订单金额百分比</Option>
                  <Option value={2}>按商品底价垫资</Option>
                </Select>
              </Form.Item>
            </div>
            {endowmentId === 1 ? (
              <div className={styles.noBorder}>
                <Form.Item
                  name="matAmount"
                  rules={[{ required: switchValue, message: '请输入垫资比例' }]}
                  label={
                    <>
                      <span>垫资比例&nbsp;%</span>
                      <Tooltip title="垫资金额=订单金额*垫资比例">
                        <Icon name="info" className={styles.iconTip} />
                      </Tooltip>
                    </>
                  }
                >
                  <InputNumber
                    max={100}
                    placeholder="请输入垫资比例"
                    bordered={false}
                    controls={false}
                  />
                </Form.Item>
              </div>
            ) : null}
          </div>
        ) : null}
      </>
    );
  }

  return (
    <Drawer
      ref={drawerRef}
      title="新建订单"
      visible={visible}
      destroyOnClose
      onClose={() => {
        onClose();
      }}
      className={styles.EditDrawer}
      footer={
        <div className={styles.footer}>
          <div>
            合计：
            <span className={styles.money}>
              <Price value={totalAmount} symbol="" format />
            </span>
            <div className={styles.text}>共{goodsList.length || 0}种商品</div>
          </div>
          <Button type="primary" loading={btnLoading} onClick={useDebounce(onsubmit, 500)}>
            生成{distributionCompanyId ? '分销' : '补录'}订单
          </Button>
        </div>
      }
    >
      <Form layout="vertical" className={classNames(styles.form, 'adjust-drawer')} form={form}>
        <div className={styles.oneCard}>
          {supplierForm}
          {customerForm}
        </div>
        <div className={styles.card} style={distributionCompanyId ? {} : { paddingBottom: '1px' }}>
          <Form.Item
            name="orderTime"
            label="开单时间"
            rules={[{ required: true, message: '请选择开单时间' }]}
          >
            <DatePicker disabledDate={disabledDateStart} onChange={formChange} />
          </Form.Item>
          <div
            className={styles.formItem}
            role="button"
            tabIndex={0}
            onClick={() => {
              paywayClick(false);
            }}
          >
            <Form.Item
              name="payment"
              label="付款方式"
              rules={[{ required: true, message: '请选择付款方式' }]}
            >
              <Input
                placeholder="请选择付款方式"
                suffix={<Icon name="right" className={styles.inputIcon} />}
                bordered
              />
            </Form.Item>
          </div>
          <div className={projectSwitch ? styles.openSwitch : styles.switchProject}>
            <div
              className={projectSwitch ? styles.downProject : styles.projectContext}
              style={distributionCompanyId ? {} : { borderBottom: 'none' }}
            >
              <div>项目</div>
              <Switch
                checked={projectSwitch}
                onChange={(value) => {
                  if (!testPermission(type === '1' ? 'R_001_009_015' : 'R_001_010_011')) return;
                  setProjectSwitch(value);
                  if (!value) {
                    form.setFieldsValue({
                      project: '',
                    });
                    setProject(undefined);
                  }
                  if (value) {
                    formChange();
                  } else {
                    drawerRef.current?.setIsChange(false);
                  }
                }}
              />
            </div>
          </div>
          {projectSwitch ? (
            <div className={styles.project}>
              <div
                className={classNames(distributionCompanyId ? '' : styles.projectNoBorder)}
                role="button"
                tabIndex={0}
                onClick={() => {
                  form.validateFields(['supplierId']).then(() => {
                    setShowOrderProject(true);
                  });
                }}
              >
                <Form.Item
                  name="project"
                  label="项目选择"
                  rules={[{ required: projectSwitch, message: '请选择项目' }]}
                >
                  <Input
                    // value={project?.projectName}
                    placeholder="选择项目"
                    suffix={<Icon name="right" className={styles.inputIcon} />}
                    bordered
                    disabled
                  />
                </Form.Item>
              </div>
            </div>
          ) : null}
          {endowment}
        </div>
        <div className={classNames(styles.btnCard, styles.btnS)}>
          {iconItem.map((item) => (
            <Fragment key={item.iconName}>
              {item.isEdit ? (
                <div
                  className={classNames(
                    styles.btnItem,
                    item.name === '添加商品' ? styles.btnBlue : ''
                  )}
                  role="button"
                  tabIndex={0}
                  onClick={() => {
                    if (testPermission(item.code)) {
                      clickIcon(item.iconName);
                    }
                  }}
                >
                  {item.iconName !== 'share' ? (
                    <Icon name={item.iconName} className={styles.icon} />
                  ) : (
                    <img src={discount} className={styles.img} alt="" />
                  )}

                  <div
                    className={styles.btnText}
                    style={item.name === '添加商品' ? { color: '#008CFF' } : {}}
                  >
                    {item.name === '按折扣定价' ? pricing || item.name : item.name}
                  </div>
                </div>
              ) : null}
            </Fragment>
          ))}
        </div>
        {goodsList.map((item) => (
          <div className={styles.goodsCard} key={item.skuId}>
            <Icon
              name="close-circle2"
              className={styles.deleteIcon}
              onClick={() => {
                Modal.confirm({
                  title: '提示',
                  icon: '',
                  centered: true,
                  width: 290,
                  getContainer: document.querySelector('.ant-drawer-wrapper-body') as HTMLElement,
                  content: '确认删除此商品',
                  onOk: () => {
                    setGoodsList(goodsList.filter((goods) => goods.id !== item.id));
                    setTimeout(() => {
                      if (goodsList.length) {
                        formChange();
                      } else {
                        drawerRef.current?.setIsChange(false);
                      }
                    }, 0);
                  },
                });
              }}
            />
            <div className={styles.goodsInfo}>
              <GoodsCard
                // @ts-ignore
                img={item.imagesList[0]}
                productName={item.name}
                floorPrice={item.auxiliaryFloorPrice || item.floorPrice}
                productUnit={item.auxiliaryUnit || item.unit}
                saleGroup={item.productMinimumSalesUnit || item.saleGroupStr}
                productSpecificationList={item.standardList}
                stock={item.stockNumber || item.stock || '0'}
              />
            </div>
            <div className={styles.footerItem}>
              <span>单位</span>
              {item.unitTemplateId ? (
                <Select
                  placeholder="请选择"
                  defaultValue={item.auxiliaryUnit || item.unit}
                  suffixIcon={<Icon name="down" />}
                  options={unitList.map((each) => ({
                    label: each.unitName,
                    value: each.unitName,
                  }))}
                  onFocus={() => {
                    if (item.unitTemplateId && item.unit) {
                      getUnit(item.unitTemplateId || 0, item.unit || '');
                    }
                  }}
                  onChange={(e) => {
                    onchangeUnit(item, e);
                    formChange();
                  }}
                />
              ) : (
                <Select
                  placeholder="请选择"
                  defaultValue={item.auxiliaryUnit || item.unit}
                  disabled
                />
              )}
            </div>
            <div className={styles.footerItem}>
              <span>单价</span>
              {PriceWay(item)}
            </div>
            <div className={styles.footerItem}>
              <span>数量</span>
              <Stepper
                min={Number(item.productMinimumQuantity) || Number(item.minimum) || 0}
                value={item.number}
                step={Number(item.productMinimumSalesUnit) || Number(item.saleGroupStr) || 1}
                onChange={(value) => {
                  onChangeNum(item, value, 'change');
                  formChange();
                }}
                onBlur={(e) => {
                  onChangeNum(item, Number(e.target.value), 'blur');
                }}
              />
            </div>
          </div>
        ))}
        {showAttachment ? (
          <div className={styles.fileCard} id="anchorId">
            <div className={styles.header}>
              <div>
                <span className={styles.title}>附件</span>
                <span className={styles.subtitle}>({fileListNum}/5)</span>
              </div>
              {/* <Icon
                name="close-circle2"
                className={styles.deleteIcon}
                onClick={() => {
                  setShowAttachment(false);
                }}
              /> */}
            </div>
            <div className={styles.subtitle}>可上传的类型PDF\DOC\XSL\图片，单个文件限20M</div>
            <div className={styles.upload}>
              <Upload
                ref={uploadRef}
                listType="mixin"
                maxCount={5}
                multiple
                accept=".xsl, .xlsx, .png, .pdf, .docx, .jpg, .jpeg"
                beforeUpload={(e, uploadList) => {
                  if (uploadList.length > 5) {
                    message.error('最多上传5个附件');
                    return false;
                  }
                  formChange();
                  return true;
                }}
                onFileListChange={(e) => {
                  formChange();
                  setFileListNum(e.length);
                }}
              />
            </div>
            <div className={styles.note}>
              <div className={styles.noteTitle}>
                <span className={styles.title}>备注</span>
                <span className={styles.subtitle}>({remark.length}/200)</span>
              </div>
              <div className={styles.noBorder}>
                <Form.Item name="note">
                  <Input.TextArea
                    placeholder="请输入"
                    maxLength={200}
                    onChange={(e) => {
                      setRemark(e.target.value);
                      formChange();
                    }}
                  />
                </Form.Item>
              </div>
            </div>
          </div>
        ) : null}
      </Form>

      <ChildDrawer
        visible={showChildDrawer}
        title={childDrawerTitle}
        type={pricingId}
        isEditPrice={isEditPrice}
        height={childDrawerTitle === '批量设置' ? 292 : 425}
        getPricing={getPricing}
        getBatchValue={getBatchValue}
        onClose={() => {
          setShowChildDrawer(false);
        }}
      />

      <SelectGoods
        ref={selectGoodsRef}
        visible={showSelectGoods}
        // @ts-ignore
        selectGoodsList={goodsList}
        distributionCompanyId={distributionCompanyId}
        nycbngOrgId={nycbngOrgId || ''}
        onClose={() => {
          setShowSelectGoods(!showSelectGoods);
        }}
        // @ts-ignore
        confirm={getOrderGoods}
      />

      <OrderPayment
        visible={showOrderPayment}
        id={data.current.orderType ? matEndowment?.id : payment?.id}
        onClose={() => {
          setShowOrderPayment(false);
        }}
        getPayItem={getPayItem}
      />

      <OrderProject
        visible={showOrderProject}
        ids={project?.id}
        companyId={distributionCompanyId || user.companyId}
        getProject={getProject}
        onClose={() => {
          setShowOrderProject(false);
        }}
      />
    </Drawer>
  );
}

EditOrder.defaultProps = {
  distributionCompanyId: 0,
  distributionCompanyName: '',
  distribution: null,
};

export default EditOrder;
