@import 'styles/mixins/mixins';

.EditDrawer {
  overflow: visible;
  transition: none !important;
  transform: none !important;

  :global {
    .ant-drawer-content {
      overflow: hidden;
    }
  }
}

.form {
  :global {
    .ant-form-item-control-input-content {
      border-bottom: 1px solid #f3f3f3;
    }

    .ant-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }
    }

    .ant-form-item-control {
      padding: 0 12px;
    }

    .ant-input-number-input {
      padding: 0;
    }

    .ant-input-affix-wrapper {
      border: none;
    }

    .ant-input[disabled] {
      cursor: pointer;
    }

    .ant-input-affix-wrapper {
      padding: 4px 0 16px;
      background: #fff;
      cursor: pointer;

      &:focus {
        box-shadow: none;
      }
    }

    .ant-input-affix-wrapper:not(.ant-input-affix-wrapper-disabled):hover {
      border: none;
      box-shadow: none;
    }

    .ant-input-affix-wrapper > input.ant-input {
      cursor: pointer;
    }

    .ant-picker {
      width: 100%;
      padding: 4px 0 16px;
      border: none;
    }

    .ant-input[disabled] {
      color: black;
    }

    .ant-select:not(.ant-select-customize-input) .ant-select-selector,
    .ant-input {
      border: none;
    }

    .ant-input:focus,
    .ant-input-focused,
    .ant-select-selector,
    .ant-picker-focused,
    .ant-input-status-error:not(.ant-input-disabled, .ant-input-borderless).ant-input:focus {
      box-shadow: none;
    }

    .ant-select-focused:not(.ant-select-disabled).ant-select:not(.ant-select-customize-input)
      .ant-select-selector {
      box-shadow: none;
    }

    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      padding: 0;
    }

    .ant-select-single .ant-select-selector .ant-select-selection-search {
      left: -1px;
    }

    .ant-input-number {
      width: 100%;
    }

    .ant-form-item-explain-error {
      font-size: 12px;
    }

    .adm-stepper .adm-stepper-input,
    .adm-button:not(.adm-button-default).adm-button-fill-none {
      height: 36px;
    }

    .adm-stepper .adm-stepper-input {
      background: #f5f6fa;
    }

    .ant-select-arrow {
      right: 0;
    }

    .adm-button:not(.adm-button-default).adm-button-fill-none {
      background: #f5f6fa;
    }
  }

  .inputIcon {
    color: #999eb2;
    font-size: 16px;
  }
}

.distributionSupplier {
  :global {
    .ant-select {
      padding-bottom: 7px;
    }

    .ant-form-item {
      margin-bottom: 16px !important;
    }
  }
}

.card,
.btnCard,
.fileCard,
.oneCard {
  margin-bottom: 20px;
  padding: 20px 9px 7px;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;
}

.card,
.oneCard {
  :global {
    input,
    .ant-select {
      font-size: 16px;
    }
  }
}

.oneCard {
  padding-bottom: 5px;

  :global {
    .ant-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }
    }
  }
}

.btnCard {
  padding: 12px 24px;
}

.fileCard {
  padding: 20px;

  :global {
    .ant-form-item-control {
      padding: 0;
    }

    .ant-input {
      padding: 0;
    }
  }
}

.switch,
.switchDown {
  display: flex;
  // padding-left: 11px;
  padding: 0 11px;
  justify-content: space-between;
  align-items: center;

  &:last-child {
    padding-bottom: 0;
    padding-left: 11px;
  }

  .iconTip {
    color: #999eb2;
    font-size: 16px;
    margin-left: 6px;
    cursor: pointer;
  }
}

.switch {
  padding: 17px 11px;
}

.switchDown {
  border-bottom: 1px solid #f3f3f3;
}

.switchProject,
.openSwitch {
  padding: 0 11px;
}

.openSwitch {
  padding-bottom: 17px;
}

.distributorAdvance {
  display: flex;
  align-items: center;
}

.projectContext,
.downProject {
  display: flex;
  padding-bottom: 17px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f3f3;
}

.downProject {
  padding-bottom: 0;
  border-bottom: none;
}

.mandatory {
  position: relative;
}

.project {
  position: relative;
  // margin-bottom: 15px;
}

.btnS {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .btnItem,
  .btnBlue {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
  }

  .btnBlue {
    color: #008cff !important;
  }

  .icon {
    font-size: 21px;
    margin-bottom: 6px;
  }

  .img {
    margin-bottom: 10px;
  }

  .btnText {
    color: #888b98;
    font-size: 12px;
  }
}

.goodsCard {
  margin-bottom: 20px;
  padding: 0 20px;
  position: relative;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;

  .deleteIcon {
    font-size: 18px;
    position: absolute;
    top: 6px;
    right: 6px;
    opacity: 0.2;
    cursor: pointer;
  }

  :global {
    .ant-input-number {
      width: 79px;
      background: #f5f6fa;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      color: black;
    }

    .adm-stepper-middle {
      border: none;
    }

    .adm-stepper {
      width: 170px;
      border-radius: 10px;
    }

    .ant-select {
      width: 100px;
      text-align: right;
    }

    .ant-select-selector {
      background: none !important;
    }

    .ant-select-single.ant-select-show-arrow .ant-select-selection-item {
      padding-right: 17px;
    }

    .ant-input-number-input {
      height: 36px;
    }

    .ant-input-number-input {
      padding: 0 10px;
    }
  }

  .footerItem {
    display: flex;
    // height: 54px;
    padding: 9px 0;
    justify-content: space-between;
    align-items: center;
    font-family: PingFangSC-Regular;
    // font-size: 16px;

    &:last-child {
      padding-bottom: 20px;
    }
  }

  .marketPrice {
    color: #999eb2;
    font-size: 12px;
    margin-left: 4px;
    text-decoration: line-through;
  }

  .totalAmount {
    color: #ea1c26;
    max-width: 80px;
    cursor: pointer;
    .text-overflow();
  }

  .goodsInfo {
    margin-bottom: 4px;
    padding: 20px 0;
    border-bottom: 1px solid #f3f3f3;
  }
}

.market {
  :global {
    .ant-input-number-input {
      padding: 0 10px;
    }
  }
}

.discount {
  display: flex;
  align-items: center;

  :global {
    .ant-input-number {
      width: 79px;
    }

    .ant-form-item {
      margin-bottom: 0;
    }
  }

  .unitPrice {
    width: 70px;
    text-align: right;
    .text-overflow();

    cursor: pointer;
  }
}

.mar {
  margin: 0 4px;
}

.header {
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  margin-bottom: 3px;
}

.title {
  margin-right: 4px;
  // font-size: 16px;
}

.subtitle {
  color: #888b98;
  font-size: 12px;
}

.upload {
  margin-top: 15px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;

  :global {
    .ant-form-item-control-input-content {
      border-bottom: none;
    }
  }
}

.note {
  margin-top: 16px;

  :global {
    .ant-input {
      border: none;
      font-size: 16px;
    }
  }

  .noteTitle {
    margin-bottom: 15px;
  }
}

.formItem {
  margin-bottom: 15px;
}

.footer {
  display: flex;
  padding: 20px;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 45%) 12%,
    rgb(255 255 255 / 72%) 25%,
    rgb(255 255 255 / 90%) 36%
  );

  .money {
    color: #ea1c26;
    font-size: 20px;
  }

  .text {
    color: #888b98;
    font-size: 12px;
    margin-top: 4px;
  }
}

.noBorder,
.projectNoBorder,
.endowmentWay {
  :global {
    .ant-form-item-control-input-content {
      border: none;
    }

    .ant-input-affix-wrapper-disabled {
      padding-bottom: 0;
    }
  }
}

.endowmentWay {
  :global {
    .ant-form-item {
      margin-bottom: 16px !important;
    }
  }
}

.projectNoBorder {
  margin-bottom: 10px;
}
