@import 'styles/mixins/mixins';

.item {
  padding: 0 20px 0 10px;
}

.goods {
  display: flex;
  padding: 8px 0;
}

.imgBox {
  width: 80px;
  height: 80px;
  margin-right: 10px;
  position: relative;
  border-radius: 6px;
}

.img {
  display: flex;
  width: 80px;
  height: 80px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
}

.imgPackage {
  .img();

  margin-right: 10px;
}

.giftTag {
  color: #fff;
  font-size: 10px;
  display: inline-block;
  width: 32px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
  opacity: 0.8;
  border-radius: 0 12px;
  background: rgb(249 174 8 / 80%);
}

.goodsInfo {
  flex: 1;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.name {
  width: 150px;
  margin-bottom: 2px;
  .text-overflow();
}

.standardPackage {
  color: #888b98;
  font-size: 12px;
}

.price {
  font-size: 12px;
}

.checkbox {
  display: flex;
  align-items: center;
}

.selectNum {
  display: flex;
  margin: 8px 0;
  border: 1px solid rgb(177 179 190 / 50%);
  border-radius: 6px;

  & .label {
    width: 100px;
    line-height: 32px;
    text-align: center;
    border-right: 1px solid rgb(177 179 190 / 50%);
  }

  :global {
    .adm-stepper {
      flex: 1;
    }

    .adm-stepper-input {
      width: 100%;
      background-color: #fff;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      background-color: #fff;
      color: #000;
    }
  }
}

.changeGoods {
  font-size: 12px;
  text-align: right;
  cursor: pointer;
}

.changeGoodsText {
  display: inline-block;
  height: 18px;
  line-height: 18px;
}

.standard {
  color: #888b98;
  font-size: 12px;
  display: flex;
  height: 24px;
  line-height: 24px;
  margin-bottom: 12px;
  padding: 0 8px;
  cursor: pointer;
  border-radius: 6px;
  background-color: rgb(177 179 190 / 10%);
}

.standardList {
  flex: 1;
  .text-overflow();
}

.standardItem:last-child span {
  display: none;
}

.standardNum {
  color: #040919;
  display: flex;
  padding-left: 8px;
  position: relative;
  align-items: center;

  &::before {
    content: '';
    width: 1px;
    height: 12px;
    position: absolute;
    top: 6px;
    left: 0;
    background-color: #d8d8d8;
  }
}

.matchGoods {
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #f3f3f3;
}

.packageListTitle {
  color: #888b98;
  font-size: 12px;
  padding: 6px 0 2px;
}

.goodsServer {
  width: 166px;
  margin-top: 10px;
  cursor: pointer;
}
