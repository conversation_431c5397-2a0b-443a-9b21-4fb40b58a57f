import { useState } from 'react';
import { Checkbox } from 'antd';
import { Stepper } from 'antd-mobile';
import { CartGoodsService, PackageGoodsListResult } from '@/apis';
import { Icon } from '@/components';
import BigNumber from 'big.js';
import GoodsServer from '../../../components/goods-service';
import styles from './index.module.less';

interface PackageItemProps {
  info: PackageGoodsListResult;
  hasIds: number[];
  // eslint-disable-next-line no-unused-vars
  onChangeGoods: (groupId: number, packageId: number, productId: number) => void;
  onSelectSpecs: (
    // eslint-disable-next-line no-unused-vars
    groupId: number,
    // eslint-disable-next-line no-unused-vars
    shopSkuId: number,
    // eslint-disable-next-line no-unused-vars
    id: number,
    // eslint-disable-next-line no-unused-vars
    servers: CartGoodsService[]
  ) => void;
  // eslint-disable-next-line no-unused-vars
  onChangePackageIds: (val: boolean, id: number) => void;
  // eslint-disable-next-line no-unused-vars
  onChangePackageNum: (num: number, id: number) => void;
}

function PackageItem({
  info,
  hasIds,
  onChangeGoods,
  onSelectSpecs,
  onChangePackageIds,
  onChangePackageNum,
}: PackageItemProps) {
  const [selectPackageIds, setSelectPackageIds] = useState<number[]>([...hasIds]);

  const onSelectPackageIds = (val: boolean, id: number) => {
    if (val) {
      selectPackageIds.push(id);
      setSelectPackageIds([...selectPackageIds]);
    } else {
      setSelectPackageIds([...selectPackageIds.filter((item) => item !== id)]);
    }
    onChangePackageIds(val, id);
  };

  return (
    <Checkbox.Group value={hasIds}>
      <div className={styles.item}>
        <div className={styles.goods}>
          <div className={styles.imgPackage}>
            <img
              src={
                info.imageList.length
                  ? info.imageList[0]
                  : 'https://img.huahuabiz.com/default/image/default_holder.png'
              }
              alt=""
            />
          </div>
          <div className={styles.goodsInfo}>
            <div>
              <div className={styles.name}>{info.name}</div>
              <div className={styles.standardPackage}>{info.introduce}</div>
            </div>
            <div className={styles.price}>¥{info.price}</div>
          </div>
          <div className={styles.checkbox}>
            <Checkbox
              value={info.id}
              onChange={(e) => {
                onSelectPackageIds(e.target.checked, info.id);
              }}
            />
          </div>
        </div>
        {selectPackageIds.includes(info.id) && (
          <>
            <div className={styles.selectNum}>
              <div className={styles.label}>商品数量</div>
              <Stepper
                className={styles.inputNumber}
                defaultValue={info.number}
                min={0}
                max={99999999}
                step={1}
                digits={0}
                onChange={(e) => onChangePackageNum(e, info.id)}
              />
            </div>
            <div className={styles.matchGoods}>
              <div className={styles.packageListTitle}>套餐包内容</div>
              {info.skuList.map((item) => (
                <div key={item.shopSkuId}>
                  <div className={styles.goods}>
                    <div className={styles.imgBox}>
                      <img
                        className={styles.img}
                        src={
                          item.image || 'https://img.huahuabiz.com/default/image/default_holder.png'
                        }
                        alt=""
                      />
                      {item.isGift > 0 && <span className={styles.giftTag}>赠品</span>}
                    </div>
                    <div className={styles.goodsInfo}>
                      <div>
                        <div className="mb-2">{item.name}</div>
                        <div
                          role="button"
                          tabIndex={0}
                          className={styles.standard}
                          onClick={() =>
                            onSelectSpecs(
                              item.groupId,
                              item.shopSkuId,
                              info.id,
                              item.serverGoods || []
                            )
                          }
                        >
                          <span>已选:</span>
                          <span className={styles.standardList}>
                            {item.standardList.map((each) => (
                              <span className={styles.standardItem} key={each.name + each.value}>
                                {each.value}
                                <span>*</span>
                              </span>
                            ))}
                          </span>
                          <span className={styles.standardNum}>
                            共{item.skuCount}种
                            <Icon name="down" size={16} color="#999EB2" />
                          </span>
                        </div>
                      </div>

                      <div>
                        搭配数量：
                        {info.number
                          ? BigNumber(item.quantity || 0)
                              .times(info.number || 0)
                              .toNumber()
                          : item.quantity}
                        {item.unit}
                      </div>
                      {item.serverGoods &&
                        item.serverGoods.length > 0 &&
                        item.serverGoods.some(
                          (server) => server.serviceStandardProductVOS.length
                        ) && (
                          <div
                            role="button"
                            tabIndex={item.shopSkuId}
                            className={styles.goodsServer}
                            onClick={() =>
                              onSelectSpecs(
                                item.groupId,
                                item.shopSkuId,
                                info.id,
                                item.serverGoods || []
                              )
                            }
                          >
                            <GoodsServer services={item.serverGoods} />
                          </div>
                        )}
                    </div>
                  </div>
                  {item.spuCount > 0 && (
                    <div
                      role="button"
                      tabIndex={0}
                      className={styles.changeGoods}
                      onClick={() => onChangeGoods(item.groupId, info.id, item.productId)}
                    >
                      <span className={styles.changeGoodsText}>可更换商品{item.spuCount}种</span>
                      <Icon name="right" size={14} color="#999EB2" />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </>
        )}
      </div>
    </Checkbox.Group>
  );
}

export default PackageItem;
