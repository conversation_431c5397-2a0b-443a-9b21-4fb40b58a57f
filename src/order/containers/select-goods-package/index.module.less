@import 'styles/mixins/mixins';

.headerFilter {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.headerTitle {
  cursor: pointer;
}

.titleDropdown {
  width: 120px !important;
  min-width: 0 !important;
}

.selectGoods {
  position: absolute;
  z-index: 9998 !important;

  :global {
    .ant-drawer-mask {
      opacity: 0 !important;
      animation: initial !important;
    }

    .ant-drawer-header {
      background-color: #fff;
    }

    .ant-drawer-body {
      display: flex;
      padding: 0 !important;
      flex-direction: column;
    }

    .ant-modal-confirm .ant-modal-body {
      padding: 20px;
    }
  }
}

.search {
  padding: 8px 20px 12px;
  background-color: #fff;
  border-bottom: 1px solid #f3f3f3;
}

.searchInput {
  background: #f5f6fa;

  :global {
    .ant-input {
      background-color: #f5f6fa;
    }
  }
}

.box {
  flex: 1;
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.category {
  width: 82px;
  height: 100%;
  background: #f5f6fa;
  overflow: auto;

  &::-webkit-scrollbar {
    display: none !important;
  }
}

.item,
.itemActive {
  width: 82px;
  padding: 20px 10px;
  text-align: center;
  cursor: pointer;
  .text-overflow();

  &:hover {
    background-color: #fff;
  }
}

.itemActive {
  background-color: #fff;
}

.boxList {
  display: flex;
  height: 100%;
  position: relative;
  flex: 1;
  flex-direction: column;
}

.categoryList {
  padding: 12px 20px 12px 12px;
  background-color: #fff;
}

.categoryItem,
.categoryItemActive {
  display: inline-block;
  width: 60px;
  margin-right: 12px;
  padding: 5px 12px;
  border-radius: 10px;
  background: #f3f3f3;
  cursor: pointer;
  .text-overflow();
}

.categoryItemActive {
  color: #008cff;
  background: #d9eeff;
  border: 1px solid #008cff;
}

.categoryMore {
  width: 100%;
  padding: 4px 0 10px 12px;
  position: absolute;
  top: 52px;
  left: 0;
  z-index: 9999;
  border-radius: 0 0 18px 18px;
  background: #fff;
  box-shadow: 0 8px 6px 0 rgb(0 0 0 / 5%);
}

.categoryBox {
  height: 80px;
  overflow: auto;
}

.filter {
  width: 20px;
  height: 20px;
  margin-top: 5px;
  float: right;
  text-align: center;
  background: #f5f6fa;
  border-radius: 50%;
  cursor: pointer;
}

.list {
  flex: 1;
  width: 100%;
  overflow: auto;
  background-color: #fff;
}

.goodsItem {
  padding: 10px 20px 10px 10px;
}

.goods {
  display: flex;
  align-items: center;
  // margin-bottom: 16px;
}

.goodsSelect {
  display: flex;
  margin: 12px 0 16px;
  border: 1px solid rgb(177 179 190 / 50%);
  border-radius: 6px;

  & .label {
    width: 100px;
    line-height: 32px;
    text-align: center;
    border-right: 1px solid rgb(177 179 190 / 50%);
  }

  .select {
    flex: 1;
  }

  :global {
    .adm-stepper {
      flex: 1;
    }

    .adm-stepper-input {
      width: 100%;
      background-color: #fff;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      background-color: #fff;
      color: #000;
    }
  }
}

.footer {
  display: flex;
  padding: 14px 20px;
  justify-content: space-between;

  & .footerText {
    display: flex;
    align-items: center;
  }

  & .img {
    width: 32px;
    height: 32px;
    margin-right: 16px;
  }

  & .num {
    color: #008cff;
    font-size: 18px;
    font-weight: 500;
    width: 130px;
    margin-left: 16px;
    .text-overflow();
  }

  :global {
    .ant-btn {
      padding: 0;
    }

    .ant-btn-primary {
      height: 28px;
      line-height: 28px;
    }
  }
}

.endMessage {
  color: #888b98;
  font-size: 12px;
}

.selectServer {
  font-size: 12px;
  text-align: right;
  cursor: pointer;
}
