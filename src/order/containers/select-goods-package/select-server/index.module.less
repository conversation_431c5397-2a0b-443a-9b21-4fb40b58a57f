@import 'styles/mixins/mixins';

.card {
  padding: 0 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: -8px 8px 24px 0 rgb(0 0 0 / 16%);
  overflow: hidden;
}

.serverName {
  font-weight: 600;
  padding: 16px 0;
}

.serverItemName {
  display: flex;
  width: fit-content;
  height: 28px;
  line-height: 28px;
  margin-bottom: 12px;
  padding: 0 16px;
  align-items: center;
  border-radius: 10px;
  background: #f3f3f3;
  cursor: pointer;
}

.serverEachName {
  display: inline-block;
  max-width: 216px;
  .text-overflow();
}

.serverItemNameActive {
  background-color: #d9eeff;
  border: 1px solid #008cff;
  color: #008cff;
}
