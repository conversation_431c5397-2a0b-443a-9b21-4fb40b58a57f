import { useEffect, useMemo, useState } from 'react';
import { Drawer } from '@/components';
import { useRequest } from 'ahooks';
import { CartGoodsService, goodsServerDetail, GoodsServerDetailResult } from '@/apis';
import classNames from 'classnames';
import styles from './index.module.less';

interface SelectServerProps {
  visible: boolean;
  goodsServerId: number;
  serverList: CartGoodsService[];
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onConfirm: (serverGoods: CartGoodsService[]) => void;
}

function SelectServer({
  visible,
  goodsServerId,
  serverList,
  onClose,
  onConfirm,
  ...props
}: SelectServerProps) {
  const [list, setList] = useState<GoodsServerDetailResult[]>([]);

  const serviceIds = useMemo(() => {
    const arrIds: number[] = [];
    if (serverList && serverList.length) {
      serverList.forEach((item) => {
        item.serviceStandardProductVOS.forEach((each) => {
          arrIds.push(each.shopSkuId);
        });
      });
    }
    return arrIds;
  }, [serverList]);

  const { run } = useRequest(goodsServerDetail, {
    manual: true,
    defaultParams: [{ id: goodsServerId }],
    onSuccess: (res) => {
      res.list.forEach((item) => {
        item.standardDetailList.forEach((each) => {
          const eachs = each;
          eachs.checked = serviceIds?.includes(each.id);
        });
      });
      setList(JSON.parse(JSON.stringify(res.list)));
    },
  });

  /**
   * @description 选择服务商品
   */
  const selectService = (groupName: string, idVal: number) => {
    list.forEach((item) => {
      if (item.groupName === groupName) {
        item.standardDetailList.forEach((each) => {
          const eachs = each;
          if (eachs.id === idVal) {
            eachs.checked = !eachs.checked;
          } else {
            eachs.checked = false;
          }
        });
      }
    });
    setList(JSON.parse(JSON.stringify(list)));
  };

  const onOk = () => {
    const arr = list.map((item) => ({
      groupName: item.groupName,
      serviceStandardProductVOS: item.standardDetailList
        .filter((items) => items.checked)
        .map((each) => ({
          shopSkuId: each.id,
          skuId: each.skuId,
          serviceNumber: '',
          serviceProductPrice: String(each.marketPrice),
          serviceName: each.name,
        })),
    }));
    // if (!arr.some((item) => item.serviceStandardProductVOS.length)) {
    //   message.warning('请选择服务');
    //   return;
    // }
    onClose();
    onConfirm(arr);
  };

  useEffect(() => {
    if (visible) {
      run({ id: goodsServerId });
    }
  }, [visible, run, goodsServerId]);

  return (
    <Drawer
      visible={visible}
      title="选择服务"
      zIndex={99999}
      onClose={onClose}
      {...props}
      footer={<Drawer.Footer showCancel={false} okText="确定" onOk={onOk} />}
    >
      <div className={styles.card}>
        {list.map((item) => (
          <div key={item.groupName}>
            <div key={item.groupName}>
              <div className={styles.serverName}>{item.groupName}</div>
              {item.standardDetailList.map((each) => (
                <div
                  role="button"
                  tabIndex={0}
                  key={each.id}
                  className={classNames(styles.serverItemName, {
                    [styles.serverItemNameActive]: each.checked,
                  })}
                  onClick={() => selectService(item.groupName, each.id)}
                >
                  <span className={styles.serverEachName}>{each.name}</span>
                  <span className="ml-2">￥{each.marketPrice}</span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default SelectServer;
