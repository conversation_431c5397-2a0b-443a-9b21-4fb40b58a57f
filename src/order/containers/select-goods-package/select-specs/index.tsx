import { useEffect, useRef, useState, useMemo } from 'react';
import { Drawer } from '@/components';
import { useRequest } from 'ahooks';
import {
  CartGoodsService,
  getPackageGoodsDetail,
  GetPackageGoodsDetailParams,
  GetPackageGoodsDetailResult,
  packageCalculatePrice,
  PackageSkuListResult,
} from '@/apis';
import classNames from 'classnames';
import styles from './index.module.less';

interface SelectSpecsProps {
  visible: boolean;
  id: number;
  groupId: number;
  packageId: number;
  serverList: CartGoodsService[];
  skuList?: PackageSkuListResult[];
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onConfirm: (info: GetPackageGoodsDetailResult) => void;
}

function SelectSpecs({
  visible,
  id,
  groupId,
  packageId,
  serverList,
  skuList,
  onClose,
  onConfirm,
  ...props
}: SelectSpecsProps) {
  const params = useRef<GetPackageGoodsDetailParams>({
    groupId: 0,
    packageId: 0,
    shopSkuId: 0,
  });
  const [info, setInfo] = useState<GetPackageGoodsDetailResult>({
    allStandardVOList: [],
    choiceList: [],
    companyId: 0,
    currentPayWayNo: 0,
    currentSpec: '',
    groupId: 0,
    hasLink: false,
    id: 0,
    imageList: [],
    isGift: 0,
    marketPrice: 0,
    name: '',
    payWayNo: [],
    price: 0,
    productId: 0,
    quantity: '',
    serviceStandardList: [],
    shopId: 0,
    skuId: 0,
    standardList: [],
    unit: '',
    saleGroup: 0,
    barsCode: '',
  });

  const serviceIds = useMemo(() => {
    const arrIds: number[] = [];
    if (serverList && serverList.length) {
      serverList.forEach((item) => {
        item.serviceStandardProductVOS.forEach((each) => {
          arrIds.push(each.shopSkuId);
        });
      });
    }
    return arrIds;
  }, [serverList]);

  /**
   * @description 获取列表
   */
  const { run } = useRequest(getPackageGoodsDetail, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      const metaIds: number[] = [];
      res.allStandardVOList.forEach((item) => {
        const ids = item.standardCase.split('-');
        if (ids && ids.length !== 0) {
          ids.forEach((each) => {
            metaIds.push(Number(each));
          });
        }
      });
      res.serviceStandardList.forEach((item) => {
        item.standardDetailList.forEach((each) => {
          const eachs = each;
          eachs.checked = serviceIds?.includes(each.id);
        });
      });
      // 商品规格
      res.choiceList.forEach((item) => {
        item.value.forEach((val) => {
          const vals = val;
          vals.disabled = !metaIds.includes(Number(val.id));
        });
      });
      // 拿取到需要渲染的选中规格
      let specification: any = {};
      res.allStandardVOList.forEach((item) => {
        if (item.shopSkuId === res.id) {
          specification = item;
        }
      });
      // 查询默认规格
      specification.standardCase.split('-').forEach((item: any, index: any) => {
        if (res.choiceList[index]) {
          res.choiceList[index].value.forEach((item2) => {
            const item2s = item2;
            if (item2s.id === Number(item)) {
              item2s.checked = true;
            }
          });
        }
      });
      setInfo(JSON.parse(JSON.stringify(res)));
    },
  });

  /**
   * @description 选择/反选商品规格
   * @param i1
   * @param i2
   */
  const checkMeta = (i1: number, i2: number) => {
    const meta = info.choiceList[i1].value[i2];
    if (meta.disabled && !meta.checked) {
      return;
    }
    const proTemp = [...info.choiceList[i1].value];
    proTemp.forEach((item, i) => {
      if (i === i2) {
        if (item.checked) {
          info.choiceList[i1].value[i].checked = true;
        }
        info.choiceList[i1].value[i].checked = !item.checked;
      } else {
        info.choiceList[i1].value[i].checked = false;
      }
    });
    setInfo({ ...info });
    // 选中id
    const axis: number[] = [];
    info.choiceList.forEach((item1) => {
      item1.value.forEach((item2) => {
        if (item2.checked) {
          axis.push(Number(item2.id));
        }
      });
    });
    const str = axis.join('-');
    info.allStandardVOList.forEach((item1) => {
      if (item1.standardCase === str) {
        info.id = item1.shopSkuId;
        setInfo({ ...info });
        run({
          groupId,
          packageId,
          shopSkuId: item1.shopSkuId,
        });
      }
    });
  };

  /**
   * @description 选择服务商品
   */
  const selectService = (groupName: string, idVal: number) => {
    info.serviceStandardList.forEach((item) => {
      if (item.groupName === groupName) {
        item.standardDetailList.forEach((each) => {
          const eachs = each;
          if (eachs.id === idVal) {
            eachs.checked = !eachs.checked;
          } else {
            eachs.checked = false;
          }
        });
      }
    });
    setInfo(JSON.parse(JSON.stringify(info)));
  };

  /**
   * @description 已选
   */
  const hasSelectArr = useMemo(() => {
    const arr: string[] = [];
    info.choiceList.forEach((item) => {
      item.value.forEach((each) => {
        if (each.checked) {
          arr.push(each.title);
        }
      });
    });
    return arr;
  }, [info]);

  const onOk = () => {
    if (info.id !== id && skuList?.length) {
      const arr: PackageSkuListResult[] = JSON.parse(JSON.stringify(skuList));
      arr?.forEach((item) => {
        const items = item;
        if (items.groupId === groupId) {
          items.shopSkuId = info.id;
          items.groupId = info.groupId;
          items.currentPayWayNo = info.currentPayWayNo;
        }
      });
      packageCalculatePrice({
        packageId,
        packageSkuList: arr.map((item) => ({
          groupId: item.groupId,
          shopSkuId: item.shopSkuId,
          payWayNo: item.currentPayWayNo,
          serviceIds: [],
        })),
        calculateFreightFlag: false,
      }).then((res) => {
        info.newPrice = `${res.price}`;
        info.marketPrice = res.marketPrice;
      });
    }
    onConfirm({ ...info });
  };

  useEffect(() => {
    if (visible) {
      run({
        groupId,
        packageId,
        shopSkuId: id,
      });
    }
  }, [id, groupId, packageId, run, visible]);

  return (
    <Drawer
      visible={visible}
      title="选择规格"
      zIndex={99999}
      {...props}
      onClose={onClose}
      className={styles.selectSpecs}
      footer={<Drawer.Footer okText="确定" showCancel={false} onOk={onOk} />}
    >
      <div className={styles.card}>
        <div className={styles.info}>
          <img
            className={styles.img}
            src={
              info.imageList.length
                ? info.imageList[0]
                : 'https://img.huahuabiz.com/default/image/default_holder.png'
            }
            alt=""
          />
          <div className={styles.goodsInfo}>
            <div className={styles.name}>{info.name}</div>
            <div className={styles.hasSelect}>
              已选：
              <span>
                {hasSelectArr.map((item, index) => (
                  <span className={styles.hasSelectItem} key={`${item + index}`}>
                    {item} <span className={styles.hasSelectItemTag}>; </span>
                  </span>
                ))}
              </span>
            </div>
          </div>
        </div>
        <div className={styles.standardList}>
          {info.choiceList.map((item, k) => (
            <div key={item.name}>
              <div className={styles.standardTitle}>{item.name}</div>
              <div className={styles.standardBox}>
                {item.value.map((each, j) => (
                  <span
                    role="button"
                    tabIndex={j}
                    key={each.id}
                    className={classNames(styles.standardItem, {
                      [styles.standardItemChecked]: each.checked,
                    })}
                    title={each.title}
                    onClick={() => {
                      if (item.value.length === 1 && each.checked) {
                        return;
                      }
                      checkMeta(k, j);
                    }}
                  >
                    {each.title}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
        <div className={styles.cell}>
          <div className={styles.label}>单位</div>
          <div className={styles.value}>{info.unit}</div>
        </div>
        <div className={styles.cell}>
          <div className={styles.label}>搭配数量</div>
          <div className={styles.value}>{Number(info.quantity)}</div>
        </div>
      </div>
      {info.serviceStandardList.length > 0 && (
        <>
          <div className="mb-4">商品服务</div>
          <div className={styles.card}>
            {info.serviceStandardList.map((item) => (
              <div key={item.groupName}>
                <div className={styles.serverName}>{item.groupName}</div>
                <div className={styles.serverBox}>
                  {item.standardDetailList.map((each) => (
                    <div
                      role="button"
                      tabIndex={0}
                      key={each.id}
                      className={classNames(styles.serverItemName, {
                        [styles.serverItemNameActive]: each.checked,
                      })}
                      onClick={() => selectService(item.groupName, each.id)}
                    >
                      <span className={styles.serverEachName}>{each.name}</span>
                      <span className="ml-2">￥{each.marketPrice}</span>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </>
      )}
    </Drawer>
  );
}

SelectSpecs.defaultProps = {
  skuList: [],
};

export default SelectSpecs;
