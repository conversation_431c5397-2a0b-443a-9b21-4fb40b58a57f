@import 'styles/mixins/mixins';

.selectSpecs {
  z-index: 9999 !important;
}

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.info {
  display: flex;
  margin-bottom: 20px;
  padding: 20px 0;
  border-bottom: 1px solid #f3f3f3;
}

.img {
  width: 70px;
  height: 70px;
  margin-right: 12px;
  border-radius: 10px;
}

.goodsInfo {
  flex: 1;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.name {
  .text-overflow(2);
}

.hasSelect {
  color: #888b98;
  font-size: 12px;
}

.standardTitle {
  font-weight: 600;
  margin-bottom: 16px;
}

.standardBox {
  display: flex;
}

.standardItem {
  display: flex;
  width: 92px;
  height: 28px;
  margin-right: 12px;
  margin-bottom: 12px;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  background-color: #f3f3f3;
  cursor: pointer;
  .text-overflow();

  &:nth-child(3n) {
    margin-right: 0;
  }
}

.standardItemChecked {
  background-color: #d9eeff;
  border: 1px solid #008cff;
  color: #008cff;
}

.cell {
  display: flex;
  margin-bottom: 16px;
  justify-content: space-between;

  & .label {
    font-weight: 600;
  }

  & .value {
    color: #888b98;
  }
}

.serverName {
  font-weight: 600;
  padding: 16px 0;
}

.serverBox {
  display: flex;
  flex-wrap: wrap;
}

.serverItemName {
  display: flex;
  width: fit-content;
  height: 28px;
  line-height: 28px;
  margin-right: 12px;
  margin-bottom: 12px;
  padding: 0 16px;
  align-items: center;
  border-radius: 10px;
  background: #f3f3f3;
  cursor: pointer;
}

.serverEachName {
  display: inline-block;
  max-width: 216px;
  .text-overflow();
}

.serverItemNameActive {
  .standardItemChecked();
}

.hasSelectItem:last-child .hasSelectItemTag {
  display: none;
}
