import { useEffect, useRef, useState } from 'react';
import { Checkbox } from 'antd';
import { Drawer } from '@/components';
import {
  getGoodsPackageGroupGoodsList,
  GetGoodsPackageGroupGoodsListParams,
  GetGoodsPackageGroupGoodsItem,
  GetPackageGoodsDetailResult,
  CartGoodsService,
  packageCalculatePrice,
  PackageSkuListResult,
} from '@/apis';
import { useRequest } from 'ahooks';
import BigNumber from 'big.js';
import SelectSpecs from '../select-specs';
import GoodsPackageItem from '../../../components/goods-package-item';
import styles from './index.module.less';

interface ChangeGoodsProps {
  visible: boolean;
  number: number;
  groupId: number;
  packageId: number;
  productId: number;
  skuList?: PackageSkuListResult[];
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onConfirm: (goodsInfo: GetGoodsPackageGroupGoodsItem) => void;
}

function ChangeGoods({
  visible,
  number,
  groupId,
  packageId,
  productId,
  skuList,
  onClose,
  onConfirm,
  ...props
}: ChangeGoodsProps) {
  const params = useRef<GetGoodsPackageGroupGoodsListParams>({
    groupId: 0,
    packageId: 0,
    productId: 0,
  });
  const [list, setList] = useState<GetGoodsPackageGroupGoodsItem[]>([]);
  const [selectSpecs, setSelectSpecs] = useState(false);
  const [id, setId] = useState(0);
  const [goodsServerList, setGoodsServerList] = useState<CartGoodsService[]>([]);

  /**
   * @description 获取列表
   */
  const { run } = useRequest(getGoodsPackageGroupGoodsList, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      res.list.forEach((item) => {
        const items = item;
        items.number = number || 1;
      });
      setList(res.list);
    },
  });

  const onChangeGoods = (e: GetPackageGoodsDetailResult) => {
    const arr = e.serviceStandardList.filter((item) =>
      item.standardDetailList.some((each) => each.checked)
    );
    list.forEach((item, index) => {
      if (e.id === item.id) {
        list[index] = {
          ...item,
          // @ts-ignore
          serviceProductVOS: arr.map((item1) => ({
            groupName: item1.groupName,
            serviceStandardProductVOS: item1.standardDetailList
              .filter((each) => each.checked)
              .map((server) => ({
                shopSkuId: server.id,
                skuId: server.skuId,
                serviceProductPrice: server.marketPrice,
                serviceName: server.name,
                serviceNumber: BigNumber(item.quantity || 0)
                  .div(item.saleGroup || 1)
                  .times(item.number || 1)
                  .toNumber(),
              })),
          })),
        };
      }
    });
    setList([...list]);
  };

  const onSelectGoods = (val: number) => {
    list.forEach((item) => {
      const items = item;
      if (items.id === val) {
        items.checked = !items.checked;
      }
    });
    setList([...list]);
  };

  const onOk = () => {
    const arr = list.filter((item) => item.checked);
    if (arr.length) {
      const arrList: PackageSkuListResult[] = JSON.parse(JSON.stringify(skuList));
      arrList?.forEach((item) => {
        const items = item;
        if (items.groupId === groupId) {
          items.shopSkuId = arr[0].id;
          items.groupId = arr[0].groupId;
          items.currentPayWayNo = arr[0].currentPayWayNo;
        }
      });
      packageCalculatePrice({
        packageId,
        packageSkuList: arrList.map((item) => ({
          groupId: item.groupId,
          shopSkuId: item.shopSkuId,
          payWayNo: item.currentPayWayNo,
          serviceIds: [],
        })),
        calculateFreightFlag: false,
      }).then((res) => {
        onConfirm({
          ...arr[0],
          newPrice: `${res.price}`,
          marketPrice: res.marketPrice,
        });
      });
    }
    onClose();
  };

  useEffect(() => {
    if (visible) {
      run({
        groupId,
        packageId,
        productId,
      });
    }
  }, [visible, run, groupId, packageId, productId]);

  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      {...props}
      zIndex={9999}
      push={false}
      title="更换商品"
      footer={<Drawer.Footer okText="确定" showCancel={false} onOk={onOk} />}
    >
      {list.map((item) => (
        <div className={styles.card} key={item.id}>
          <div className={styles.checkbox}>
            <Checkbox checked={item.checked} onChange={() => onSelectGoods(item.id)} />
          </div>
          <GoodsPackageItem
            key={item.id}
            info={item}
            onSelectSpecs={(e, goodsServers) => {
              setId(e);
              setGoodsServerList(goodsServers);
              setSelectSpecs(true);
            }}
          />
        </div>
      ))}
      <SelectSpecs
        visible={selectSpecs}
        id={id}
        groupId={groupId}
        packageId={packageId}
        onClose={() => setSelectSpecs(false)}
        onConfirm={(e) => {
          setSelectSpecs(false);
          onChangeGoods(e);
        }}
        serverList={goodsServerList}
      />
    </Drawer>
  );
}

ChangeGoods.defaultProps = {
  skuList: [],
};

export default ChangeGoods;
