import {
  PropsWithChildren,
  useEffect,
  useRef,
  useState,
  useReducer,
  useMemo,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Search, Drawer, Icon, Empty } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Button, Spin, Checkbox, Select, message, Modal, Divider, Menu, Dropdown } from 'antd';
import { Stepper } from 'antd-mobile';

import {
  getCategoryCustomeList,
  GetCategoryCustomeItemResult,
  stockGoodsList,
  StockGoodsListResult,
  formulaConvert,
  FormulaConvertDetaiResult,
  getOrderUnitConversion,
  formulaConvertManyconvert,
  GetCategoryCustomeListParams,
  packageGoodsList,
  PackageGoodsListResult,
  CartGoodsService,
  GetGoodsPackageGroupGoodsItem,
  PackageSkuListResult,
} from '@/apis';
import { StockGoodsListParams } from '@/apis/psi/stock-goods-list';
import classNames from 'classnames';
import BigNumber from 'big.js';
import { useRequest } from 'ahooks';
import { testPerm } from '@/utils/permission';
import SelectServer from './select-server';
import SelectSpecs from './select-specs';
import ChangeGoods from './change-goods';
import SelectGoodsStandard from '../select-goods-filter/select-goods-filter';
import cart from '../../assets/img/cart.png';
import PackageItem from './package-item';
import GoodsItem from '../../components/goods-item';
import GoodsServer from '../../components/goods-service';
import styles from './index.module.less';
import getOrderSettingInfo from '../../../../apis/order/get-order-setting-info';

const pageSize = 10;

export interface SelectGoodsRefType {
  setNotCloseConfirm: SimpleFn<boolean>;
}

interface SelectGoodsPropps {
  visible: boolean;
  isInitData?: boolean;
  distributionCompanyId?: number;
  orderNo?: number;
  confirm: (
    // eslint-disable-next-line no-unused-vars
    arr: StockGoodsListResult[],
    // eslint-disable-next-line no-unused-vars
    packageList: PackageGoodsListResult[]
  ) => void | Promise<void>;
  onClose: () => void;
  selectGoodsList: StockGoodsListResult[];
  selectGoodsPackageList: PackageGoodsListResult[];
  nycbngOrgId?: string;
  orderType: number;
  orderTypeNum: number;
  isAdjust?: boolean;
  warehouseNo?: string;
  isNegativeStockEnable?: boolean;
  targetCompanyId?: string;
  payWayNo?: string;
}

interface InitialState {
  customizeCategorySet: Array<number | null>;
  keyword: string;
  pageNo: number;
  totalPage: number;
  list: StockGoodsListResult[];
  standardFilterParams?: { name: string; value: string[] }[];
  shopSkuNames?: string[];
  targetCompanyId?: string;
  payWayNo?: string;
}

const initialState = {
  customizeCategorySet: [],
  keyword: '',
  pageNo: 1,
  totalPage: 1,
  list: [],
  standardFilterParams: [],
  shopSkuNames: [],
  targetCompanyId: '',
  payWayNo: '',
};

type GoodsAction =
  | {
      type: 'setPage';
      payload: { pageNo?: number; totalPage?: number };
    }
  | {
      type: 'setList';
      payload: StockGoodsListResult[];
    }
  | {
      type: 'setAll';
      payload: InitialState;
    };

const reducer = (state: InitialState, action: GoodsAction): InitialState => {
  switch (action.type) {
    case 'setPage':
      return { ...state, ...action.payload };
    case 'setList':
      return { ...state, list: action.payload };
    case 'setAll':
      return action.payload;
    default:
      return state;
  }
};

const SelectGoods = forwardRef<SelectGoodsRefType, PropsWithChildren<SelectGoodsPropps>>(
  (
    {
      visible,
      isInitData,
      selectGoodsList,
      selectGoodsPackageList,
      distributionCompanyId,
      confirm,
      onClose,
      orderType,
      orderTypeNum,
      orderNo,
      nycbngOrgId,
      isAdjust,
      warehouseNo,
      isNegativeStockEnable,
      targetCompanyId,
      payWayNo,
      ...props
    },
    ref
  ) => {
    const [categoryCustomes, setCategoryCustomes] = useState(
      null as null | GetCategoryCustomeItemResult[]
    );
    const [categoryCustomesTwo, setCategoryCustomesTwo] = useState(
      null as null | GetCategoryCustomeItemResult[]
    );
    const content = useRef(null as unknown as HTMLDivElement);
    const [selectIds, setSelectIds] = useState(Array<number | string | boolean>);
    const [selectCategoryId, setSelectCategoryId] = useState(null as null | number);
    const [selectCategoryIdTwo, setSelectCategoryIdTwo] = useState(null as null | number);
    const parentIdRef = useRef(0);
    const [units, setUnits] = useState<FormulaConvertDetaiResult[]>([]);
    const [state, dispatch] = useReducer(reducer, initialState);
    const [showMore, setShowMore] = useState(false);
    const hasSelectGoodsList = useRef<StockGoodsListResult[]>([]);
    const notCloseConfirm = useRef(false);
    const [showSelectGoodsStandard, setShowSelectGoodsStandard] = useState(false);
    const [showChangeGoods, setShowChangeGoods] = useState(false);
    const [selectType, setSelectType] = useState(1);
    const packagesListParams = useRef({
      pageNo: 1,
      pageSize: 10,
      searchKey: '',
      customizeCatCodes: [],
    });
    const [packagesListParamsVal, setPackagesListParamsVal] = useState({
      count: 0,
      total: 0,
    });
    const [packagesList, setPackagesList] = useState<PackageGoodsListResult[]>([]);
    const [selectChangeGoodsInfo, setSelectChangeGoodsInfo] = useState<{
      groupId: number;
      packageId: number;
      productId: number;
      number: number;
    }>({
      groupId: 0,
      packageId: 0,
      productId: 0,
      number: 0,
    });
    const [hasSelectPackagesList, setHasSelectPackagesList] = useState<PackageGoodsListResult[]>(
      []
    );

    const [showSelectSpecs, setShowSelectSpecs] = useState(false);
    const selectSpeceParams = useRef({ groupId: 0, shopSkuId: 0, packageId: 0 });

    const [serverList, setServerList] = useState<CartGoodsService[]>([]);
    const [goodsServerId, setGoodsServerId] = useState(0);
    const [showSelectServer, setShowSelectServer] = useState(false);

    const [skuList, setSkuList] = useState<PackageSkuListResult[]>([]);
    const [isNegativeStock, setIsNegativeStock] = useState(false);

    const productSum = useMemo(() => {
      const hasIds = hasSelectGoodsList.current.map((each) => each.skuId);
      state.list.forEach((each) => {
        if (selectIds.includes(each.skuId)) {
          if (!hasIds.includes(each.skuId)) {
            hasSelectGoodsList.current.push(each);
          } else {
            for (let i = 0; i < hasSelectGoodsList.current.length; i += 1) {
              if (hasSelectGoodsList.current[i].skuId === each.skuId) {
                hasSelectGoodsList.current[i].number = each.number;
              }
            }
          }
        }
      });
      let count = BigNumber(0);
      hasSelectGoodsList.current
        .filter((item) => selectIds.includes(item.skuId))
        .forEach((item) => {
          count = count.plus(item.number ? item.number : 0);
        });
      hasSelectPackagesList.forEach((item) => {
        count = count.plus(item.number ? item.number : 0);
      });
      return count.toNumber();
    }, [state.list, hasSelectPackagesList, selectIds]);

    const getCategoryCustomes = () => {
      const params: GetCategoryCustomeListParams = {
        grade: 1,
        pageNo: 1,
        pageSize: 9999,
      };
      if (distributionCompanyId) params.companyId = distributionCompanyId;
      getCategoryCustomeList({ ...params }).then((res) => {
        if (res.list.length) {
          res.list.unshift({
            id: null,
            categoryName: '全部',
          });
        }
        setCategoryCustomes(res.list);
      });
    };

    const selectCategory = (id: number | null) => {
      if (!id) {
        setSelectCategoryId(null);
        setCategoryCustomesTwo([]);
        return;
      }
      parentIdRef.current = id;
      setShowMore(false);
      setSelectCategoryId(id);
      const data: GetCategoryCustomeListParams = {
        pageNo: 1,
        pageSize: 9999,
        parentId: id || null,
      };
      if (distributionCompanyId) data.companyId = distributionCompanyId;
      getCategoryCustomeList(data).then((res) => {
        setCategoryCustomesTwo(res.list);
      });
    };

    const getGoods = (argument: InitialState) => {
      const formData: StockGoodsListParams = {
        sort: 'desc',
        pageNo: argument.pageNo,
        pageSize,
        keyword: argument.keyword,
        customizeCategorySet: argument.customizeCategorySet,
        businessSourceType: 1,
        standardFilterParams: argument.standardFilterParams,
        shopSkuNames: argument.shopSkuNames,
        warehouseNo,
        targetCompanyId: Number(targetCompanyId) || '',
        payWayNo: Number(payWayNo) || '',
      };
      if (distributionCompanyId) {
        formData.businessSourceType = 2;
        formData.distributionCompanyId = distributionCompanyId;
      }
      if (isAdjust) {
        formData.orderNo = orderNo;
      }
      stockGoodsList(formData, nycbngOrgId).then((res) => {
        if (hasSelectGoodsList.current.length) {
          for (let i = 0; i < res.list.length; i += 1) {
            hasSelectGoodsList.current.forEach((item1) => {
              if (res.list[i].skuId === item1.skuId) {
                res.list[i] = item1;
              }
            });
          }
        }
        dispatch({
          type: 'setAll',
          payload: {
            customizeCategorySet: argument.customizeCategorySet,
            keyword: argument.keyword,
            pageNo: argument.pageNo + 1,
            list: [...argument.list, ...res.list],
            totalPage: Math.ceil(res.pagination.count / pageSize),
            standardFilterParams: argument.standardFilterParams,
            shopSkuNames: argument.shopSkuNames,
          },
        });
      });
    };

    const loadMore = () => {
      if (!(state.pageNo <= state.totalPage)) return;
      getGoods(state);
    };

    const { run: getOrderInfo } = useRequest(getOrderSettingInfo, {
      manual: true,
      onSuccess: (res) => {
        setIsNegativeStock(Boolean(res.isNegativeStockEnable));
      },
    });

    /**
     * @description 获取商品包列表
     */
    const { run: runPackagesList } = useRequest(packageGoodsList, {
      manual: true,
      defaultParams: [{ ...packagesListParams.current }],
      onSuccess: (result) => {
        if (hasSelectPackagesList.length) {
          for (let i = 0; i < result.list.length; i += 1) {
            hasSelectPackagesList.forEach((item1) => {
              if (result.list[i].id === item1.id) {
                // eslint-disable-next-line no-param-reassign
                result.list[i].number = item1.number;
                // eslint-disable-next-line no-param-reassign
                result.list[i].price = item1.price;
                // eslint-disable-next-line no-param-reassign
                result.list[i].skuList = item1.skuList;
              }
            });
          }
        }
        if (packagesListParams.current.pageNo === 1) {
          setPackagesList(result.list);
        } else {
          setPackagesList([...packagesList, ...result.list]);
        }
        setPackagesListParamsVal({
          count: result.pagination.count,
          total: result.pagination.total,
        });
        packagesListParams.current.pageNo += 1;
      },
    });

    const loadMorePackage = () => {
      if (!(packagesListParams.current.pageNo <= packagesListParamsVal.total)) return;
      runPackagesList({ ...packagesListParams.current });
    };

    const onChangeGoods = (e: any, skuId: number) => {
      const arr = JSON.parse(JSON.stringify(selectIds));
      if (e?.target.checked) {
        arr.push(skuId);
      } else {
        for (let i = 0; i < arr.length; i += 1) {
          if (skuId === arr[i]) {
            arr.splice(i, 1);
          }
        }
      }
      setSelectIds(arr);
    };

    // 获取单位
    const getUnit = (templateId: number, mainUnitName: string) => {
      formulaConvertManyconvert({
        templateId,
        mainUnitName,
      }).then((res) => {
        // @ts-ignore
        setUnits(res.list);
      });
    };

    // 切换单位
    const onChangeUnit = (item: StockGoodsListResult, e: string) => {
      getOrderUnitConversion({
        stockNumber: item.stockNumber || item.stock,
        floorPrice: item.floorPrice,
        productMarketPrice: item.marketPrice,
        productMinimumQuantity: item.minimum,
        productMinimumSalesUnit: item.saleGroupStr,
        skuId: item.skuId,
        number: item.number,
        productUnit: e, // 选中的单位
        productUnitBefore: item.auxiliaryUnit || item.unit, // 选则之前的单位
      }).then((res) => {
        dispatch({
          type: 'setList',
          payload: state.list.map((tmpItem) => {
            if (tmpItem.id !== item.id) {
              return tmpItem;
            }
            return {
              ...tmpItem,
              auxiliaryUnit: e,
              number: Number(res.number) || 0,
              stockNumber: res.stockNumber,
              productMinimumSalesUnit: res.productMinimumSalesUnit,
              productMinimumQuantity: res.productMinimumQuantity,
              auxiliaryFloorPrice: Number(res.floorPrice),
              productMarketPrice: Number(res.productMarketPrice),
            };
          }),
        });
        hasSelectGoodsList.current = hasSelectGoodsList.current.map((it) => {
          if (item.id === it.id) {
            return {
              ...it,
              auxiliaryUnit: e,
              number: Number(res.number) || 0,
              stockNumber: res.stockNumber,
              market: Number(res.productMarketPrice),
              productMinimumSalesUnit: res.productMinimumSalesUnit,
              productMinimumQuantity: res.productMinimumQuantity,
              auxiliaryFloorPrice: Number(res.floorPrice) || it.floorPrice,
              productMarketPrice: Number(res.productMarketPrice),
            };
          }
          return it;
        });
      });
    };

    // 切换数量
    const onChangeNum = (item: StockGoodsListResult, val: number, type: string) => {
      if (
        !isNegativeStock &&
        val > Number(item.stockNumber || item.stock) &&
        isNegativeStockEnable &&
        type === 'blur'
      ) {
        dispatch({
          type: 'setList',
          payload: state.list.map((tmpItem) => {
            if (tmpItem.id !== item.id) {
              return tmpItem;
            }
            return {
              ...tmpItem,
              number:
                Number(item.stockNumber || item.stock) > 0
                  ? Math.floor(
                      Number(item.stockNumber || item.stock) /
                        Number(item.productMinimumSalesUnit || item.saleGroupStr)
                    ) * Number(item.productMinimumSalesUnit || item.saleGroupStr)
                  : 0,
              num:
                Number(item.stockNumber || item.stock) > 0
                  ? Math.floor(
                      Number(item.stockNumber || item.stock) /
                        Number(item.productMinimumSalesUnit || item.saleGroupStr)
                    ) * Number(item.productMinimumSalesUnit || item.saleGroupStr)
                  : 0,
            };
          }),
        });
        message.warning('商品数量不能超过库存');
        return;
      }
      if (
        (item.saleGroupStr &&
          // @ts-ignore
          BigNumber(val).div(item.saleGroupStr) % 1 !== 0 &&
          !item.productMinimumSalesUnit) ||
        (item.productMinimumSalesUnit &&
          // @ts-ignore
          BigNumber(val).div(item.productMinimumSalesUnit) % 1 !== 0)
      ) {
        if (type === 'blur') {
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => {
              if (tmpItem.id !== item.id) {
                return tmpItem;
              }
              return {
                ...tmpItem,
                number:
                  Math.ceil(val / Number(item.productMinimumSalesUnit || item.saleGroupStr)) *
                    Number(item.productMinimumSalesUnit || item.saleGroupStr) || 0,
              };
            }) as StockGoodsListResult[],
          });
          message.error('请输入最小销售单元整数倍');
          return;
        }
      }
      dispatch({
        type: 'setList',
        payload: state.list.map((tmpItem) => {
          if (tmpItem.id !== item.id) {
            return tmpItem;
          }
          return {
            ...tmpItem,
            number:
              val > 99999999
                ? Math.ceil(99999999 / Number(item.productMinimumSalesUnit || item.saleGroupStr)) *
                  Number(item.productMinimumSalesUnit || item.saleGroupStr)
                : val,
            num:
              val > 99999999
                ? Math.ceil(99999999 / Number(item.productMinimumSalesUnit || item.saleGroupStr)) *
                  Number(item.productMinimumSalesUnit || item.saleGroupStr)
                : val,
          };
        }),
      });
    };

    const onCloseConfirm = () => {
      Modal.confirm({
        title: '提示',
        content: '您编辑的内容尚未保存，确定要离开吗?',
        cancelText: '离开',
        okText: '继续编辑',
        icon: null,
        width: 290,
        centered: true,
        getContainer: content.current as HTMLElement,
        onCancel() {
          onClose();
        },
      });
    };

    const onCloseDrawer = () => {
      if (notCloseConfirm.current) {
        onClose();
        return;
      }
      if (!selectGoodsList.length) {
        if (state.list.some((item) => item.number > 0)) {
          onCloseConfirm();
          return;
        }
        onClose();
      } else if (
        JSON.stringify(
          selectGoodsList.map((item) => ({
            skuId: item.skuId,
            number: item.number,
          }))
        ) !==
        JSON.stringify(
          state.list
            .filter((item) => selectIds.includes(item.skuId))
            .map((item1) => ({
              skuId: item1.skuId,
              number: item1.number,
            }))
        )
      ) {
        onCloseConfirm();
      } else {
        onClose();
      }
    };

    // 统计数量
    const computeNum = () => {
      const hasIds = hasSelectGoodsList.current.map((each) => each.skuId);
      state.list.forEach((each) => {
        if (selectIds.includes(each.skuId)) {
          if (!hasIds.includes(each.skuId)) {
            hasSelectGoodsList.current.push(each);
          } else {
            for (let i = 0; i < hasSelectGoodsList.current.length; i += 1) {
              if (hasSelectGoodsList.current[i].skuId === each.skuId) {
                hasSelectGoodsList.current[i].number = each.number;
                hasSelectGoodsList.current[i].num = each.num;
                hasSelectGoodsList.current[i].preferential = each.preferential;
              }
            }
          }
        }
      });
    };

    useImperativeHandle(
      ref,
      () => ({
        setNotCloseConfirm: (boolean) => {
          notCloseConfirm.current = boolean;
        },
      }),
      []
    );

    /**
     * @description 更换服务商品
     */
    const onChangePackageGoods = (e: GetGoodsPackageGroupGoodsItem) => {
      packagesList.forEach((item) => {
        const items = item;
        if (items.id === selectChangeGoodsInfo.packageId) {
          items.price = e.newPrice || items.price;
          item.skuList.forEach((each, index) => {
            if (each.groupId === e.groupId) {
              items.skuList[index] = {
                name: e.name,
                groupId: e.groupId,
                shopSkuId: e.id,
                skuId: e.skuId,
                quantity: Number(e.quantity),
                image: e.imageList[0],
                productId: e.productId,
                saleGroup: Number(e.saleGroup),
                spuCount: each.spuCount,
                skuCount: e.skuCount,
                unit: e.unit,
                standardList: e.standardList,
                isGift: items.skuList[index].isGift ? items.skuList[index].isGift : e.isGift,
                serverGoods: e.serviceProductVOS,
                currentPayWayNo: e.currentPayWayNo,
              };
            }
          });
        }
      });
      hasSelectPackagesList.forEach((item) => {
        const items = item;
        if (items.id === selectChangeGoodsInfo.packageId) {
          items.price = e.newPrice || items.price;
          item.skuList.forEach((each, index) => {
            if (each.groupId === e.groupId) {
              items.skuList[index] = {
                name: e.name,
                groupId: e.groupId,
                shopSkuId: e.id,
                skuId: e.skuId,
                quantity: Number(e.quantity),
                image: e.imageList[0],
                productId: e.productId,
                saleGroup: Number(e.saleGroup),
                spuCount: each.spuCount,
                skuCount: e.skuCount,
                unit: e.unit,
                standardList: e.standardList,
                isGift: items.skuList[index].isGift ? items.skuList[index].isGift : e.isGift,
                serverGoods: e.serviceProductVOS,
                currentPayWayNo: e.currentPayWayNo,
              };
            }
          });
        }
      });
      setPackagesList(JSON.parse(JSON.stringify(packagesList)));
      setHasSelectPackagesList(JSON.parse(JSON.stringify(hasSelectPackagesList)));
    };

    const getList = () => {
      packagesListParams.current = {
        ...packagesListParams.current,
        pageNo: 1,
      };
      runPackagesList({ ...packagesListParams.current });
    };

    useEffect(() => {
      if (isInitData || visible) {
        state.shopSkuNames = [];
        state.standardFilterParams = [];
      }
    }, [isInitData, visible]); // eslint-disable-line

    useEffect(() => {
      if (visible) {
        getCategoryCustomes();
        state.pageNo = 1;
        state.list = [];
        getGoods(state);
        getOrderInfo();
      }
    }, [visible]); // eslint-disable-line

    useEffect(() => {
      if (visible) {
        if (selectGoodsPackageList.length) {
          for (let i = 0; i < packagesList.length; i += 1) {
            selectGoodsPackageList.forEach((item1) => {
              if (packagesList[i].id === item1.id) {
                packagesList[i] = item1;
              }
            });
          }
          setHasSelectPackagesList(JSON.parse(JSON.stringify(hasSelectPackagesList)));
          setPackagesList(JSON.parse(JSON.stringify(packagesList)));
        } else {
          setHasSelectPackagesList([]);
          packagesListParams.current = {
            ...packagesListParams.current,
            pageNo: 1,
          };
          runPackagesList({ ...packagesListParams.current });
        }
        if (selectGoodsList.length) {
          hasSelectGoodsList.current = JSON.parse(JSON.stringify(selectGoodsList));
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => {
              for (let i = 0; i < selectGoodsList.length; i += 1) {
                if (tmpItem.skuId === selectGoodsList[i].skuId) {
                  return {
                    ...tmpItem,
                    number: selectGoodsList[i].number || 0,
                    num: selectGoodsList[i].num || 0,
                    unit: selectGoodsList[i].auxiliaryUnit || selectGoodsList[i].unit,
                    preferential:
                      selectGoodsList[i].preferential || selectGoodsList[i].preferential,
                    serverGoods: selectGoodsList[i].serverGoods || [],
                  };
                }
              }
              return {
                ...tmpItem,
                number: 0,
                num: 0,
              };
            }),
          });
          const ids = selectGoodsList.map((item) => item.skuId);
          // @ts-ignore
          setSelectIds(ids);
        } else if (state.list.length) {
          hasSelectGoodsList.current = [];
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => ({
              ...tmpItem,
              number: 0,
              num: 0,
              serverGoods: [],
            })),
          });
        }
      } else {
        state.keyword = '';
        state.pageNo = 1;
        state.list = [];
        getGoods(state);
        setSelectIds([]);
      }
    }, [visible]); // eslint-disable-line

    const titleMenuItems = [
      { label: '添加商品', key: 1 },
      { label: '添加套餐包', key: 2 },
    ];

    return (
      <Drawer
        visible={visible}
        {...props}
        getContainer={() => document.querySelector('#content') as HTMLElement}
        className={styles.selectGoods}
        width={375}
        title={
          <Dropdown
            overlayClassName={styles.titleDropdown}
            overlay={
              <Menu
                items={titleMenuItems}
                onClick={({ key }) => {
                  setSelectType(+key);
                  if (+key === 2) {
                    getList();
                  } else {
                    state.pageNo = 1;
                    state.list = [];
                    getGoods(state);
                  }
                }}
              />
            }
            placement="bottom"
          >
            <div className={styles.headerTitle}>
              <span className="mr-1">{selectType === 1 ? '添加商品' : '添加套餐包'}</span>
              <Icon name="down" size={18} />
            </div>
          </Dropdown>
        }
        onClose={onCloseDrawer}
        zIndex={9998}
        push={false}
        footer={
          <div className={styles.footer}>
            <div className={styles.footerText}>
              <img className={styles.img} src={cart} alt="" />
              <span>总数量 </span>
              <span className={styles.num} title={`${productSum}`}>
                {productSum}
              </span>
            </div>
            <Button
              type="primary"
              onClick={() => {
                computeNum();
                const arr = hasSelectGoodsList.current.filter((item) =>
                  selectIds.includes(item.skuId)
                );
                if (!arr.length && !hasSelectPackagesList.length)
                  return message.error('请选择商品');
                if (arr.some((item) => !item.number)) return message.error('请填写商品数量!');
                if (hasSelectPackagesList.some((item) => !item.number))
                  return message.error('请填写套餐包数量!');
                confirm(arr, hasSelectPackagesList);
                return false;
              }}
            >
              选好了
            </Button>
          </div>
        }
        extra={
          selectType === 1 ? (
            <div
              role="button"
              tabIndex={0}
              className={styles.headerFilter}
              onClick={() => {
                let code = '';
                if (orderTypeNum === 2) {
                  code = orderType ? 'R_001_021_009' : 'R_001_022_009';
                } else {
                  code = orderType ? 'R_001_009_020' : 'R_001_010_016';
                }
                if (!testPerm(code)) {
                  return;
                }
                setShowSelectGoodsStandard(true);
              }}
            >
              筛选
            </div>
          ) : null
        }
      >
        <div className={styles.search}>
          <Search
            className={styles.searchInput}
            placeholder={`搜索${selectType === 1 ? '商品' : '套餐包'}名称`}
            defaultValue={state.keyword}
            onSearch={(searchKey) => {
              state.keyword = searchKey;
              packagesListParams.current = {
                ...packagesListParams.current,
                pageNo: 1,
                searchKey,
              };
              if (selectType === 1) {
                state.pageNo = 1;
                state.list = [];
                getGoods(state);
              } else {
                runPackagesList(packagesListParams.current);
              }
            }}
          />
        </div>
        <div
          ref={content}
          className={styles.box}
          role="button"
          tabIndex={0}
          onClick={() => {
            if (showMore) {
              setShowMore(!showMore);
            }
          }}
        >
          <div className={styles.category}>
            {categoryCustomes
              ? categoryCustomes.map((item) => (
                  <div
                    role="button"
                    tabIndex={item.id || 0}
                    key={item.id}
                    title={item.categoryName}
                    className={classNames(
                      selectCategoryId === item.id ? styles.itemActive : styles.item
                    )}
                    onClick={() => {
                      computeNum();
                      packagesListParams.current = {
                        ...packagesListParams.current,
                        // @ts-ignore
                        customizeCatCodes: [item.code],
                        pageNo: 1,
                      };
                      if (selectType === 1) {
                        state.customizeCategorySet = [item.id || null];
                        // state.keyword = '';
                        state.pageNo = 1;
                        state.list = [];
                        getGoods(state);
                      } else {
                        runPackagesList(packagesListParams.current);
                      }
                      selectCategory(item.id);
                      setSelectCategoryIdTwo(null);
                    }}
                  >
                    {item.categoryName}
                  </div>
                ))
              : null}
          </div>
          <div className={styles.boxList}>
            {categoryCustomesTwo && categoryCustomesTwo.length > 0 ? (
              <div className={styles.categoryList}>
                {categoryCustomesTwo
                  ? categoryCustomesTwo.slice(0, 3).map((item) => (
                      // key={item.id}
                      <span
                        role="button"
                        key={item.id}
                        tabIndex={item.id || 0}
                        title={item.categoryName}
                        className={classNames(
                          selectCategoryIdTwo === item.id
                            ? styles.categoryItemActive
                            : styles.categoryItem,
                          'mb-4'
                        )}
                        onClick={() => {
                          computeNum();
                          if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                            setSelectCategoryIdTwo(null);
                            state.customizeCategorySet = [parentIdRef.current];
                          } else {
                            setSelectCategoryIdTwo(item.id);
                            state.customizeCategorySet = [item.id];
                          }
                          if (selectType === 1) {
                            state.pageNo = 1;
                            state.keyword = '';
                            state.list = [];
                            getGoods(state);
                          } else {
                            packagesListParams.current = {
                              ...packagesListParams.current,
                              // @ts-ignore
                              customizeCatCodes: [item.code],
                              pageNo: 1,
                            };
                            runPackagesList(packagesListParams.current);
                          }
                        }}
                      >
                        {item.categoryName}
                      </span>
                    ))
                  : null}
                {categoryCustomesTwo.length > 3 ? (
                  <span
                    role="button"
                    tabIndex={0}
                    className={styles.filter}
                    onClick={() => {
                      setShowMore(!showMore);
                    }}
                  >
                    <Icon name="down" color="#000" />
                  </span>
                ) : null}
              </div>
            ) : null}
            {showMore ? (
              <div className={styles.categoryMore}>
                <div className={styles.categoryBox}>
                  {categoryCustomesTwo
                    ? categoryCustomesTwo.slice(3, categoryCustomesTwo.length).map((item) => (
                        <span
                          role="button"
                          key={item.id}
                          tabIndex={item.id || 0}
                          className={classNames(
                            selectCategoryIdTwo === item.id
                              ? styles.categoryItemActive
                              : styles.categoryItem,
                            'mb-4'
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            computeNum();
                            if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                              setSelectCategoryIdTwo(null);
                              state.customizeCategorySet = [parentIdRef.current];
                            } else {
                              setSelectCategoryIdTwo(item.id);
                              state.customizeCategorySet = [item.id];
                            }
                            if (selectType === 1) {
                              state.pageNo = 1;
                              state.list = [];
                              getGoods(state);
                            } else {
                              packagesListParams.current = {
                                ...packagesListParams.current,
                                // @ts-ignore
                                customizeCatCodes: [item.code],
                                pageNo: 1,
                              };
                              runPackagesList(packagesListParams.current);
                            }
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))
                    : null}
                </div>
              </div>
            ) : null}
            <div
              className={classNames(
                state.list.length === 0 && packagesList.length === 0 ? styles.noData : styles.list
              )}
              id="list"
            >
              {state.list.length > 0 && selectType === 1 && (
                <InfiniteScroll
                  dataLength={state.list.length}
                  hasMore={state.pageNo <= state.totalPage}
                  loader={
                    <div className="text-center">
                      <Spin tip="加载中..." />
                    </div>
                  }
                  endMessage={
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>加载到底了</span>
                      </Divider>
                    </div>
                  }
                  next={loadMore}
                  scrollableTarget="list"
                >
                  <Checkbox.Group defaultValue={selectIds}>
                    {state.list.map((item) => (
                      <div className={styles.goodsItem} key={item.id}>
                        <div className={styles.goods}>
                          <GoodsItem
                            name={item.name}
                            img={item.images || (item.imagesList && item.imagesList[0])}
                            standard={item.standardList}
                            stock={item.stockNumber || item.stock}
                            saleGroup={item.productMinimumSalesUnit || item.saleGroupStr || '1'}
                            floorPrice={item.auxiliaryFloorPrice || item.floorPrice}
                            showStock
                          />
                          <Checkbox
                            value={item.skuId}
                            onChange={(e) => {
                              if (e.target.checked && item.unitTemplateId) {
                                formulaConvert(item.unitTemplateId || 0).then((res) => {
                                  // @ts-ignore
                                  setUnits(res.detailList);
                                });
                              }
                              onChangeGoods(e, item.skuId);
                            }}
                          />
                        </div>
                        {/* @ts-ignore */}
                        {selectIds.includes(item.skuId) ? (
                          <>
                            {/* {item.unitTemplateId ? ( */}
                            <div className={styles.goodsSelect}>
                              <div className={styles.label}>商品单位</div>
                              {item.unitTemplateId ? (
                                <Select
                                  placeholder="请选择"
                                  defaultValue={item.auxiliaryUnit || item.unit}
                                  bordered={false}
                                  className={styles.select}
                                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                                  options={units.map((each) => ({
                                    label: each.unitName,
                                    value: each.unitName,
                                  }))}
                                  onFocus={() => {
                                    if (item.unitTemplateId && item.unit) {
                                      getUnit(item.unitTemplateId, item.unit);
                                    }
                                  }}
                                  onChange={(e) => {
                                    onChangeUnit(item, e);
                                  }}
                                />
                              ) : (
                                <Select
                                  placeholder="请选择"
                                  defaultValue={item.auxiliaryUnit || item.unit}
                                  bordered={false}
                                  disabled
                                  className={styles.select}
                                />
                              )}
                            </div>
                            {/* ) : null} */}
                            <div className={styles.goodsSelect}>
                              <div className={styles.label}>商品数量</div>
                              <Stepper
                                min={
                                  Number(item.productMinimumQuantity) || Number(item.minimum) || 0
                                }
                                //   item.isNegativeStock || !isNegativeStockEnable
                                // ? 99999999
                                // : Number(item.stock)
                                max={99999999}
                                step={
                                  isAdjust
                                    ? 1
                                    : Number(item.productMinimumSalesUnit) ||
                                      Number(item.saleGroupStr) ||
                                      1
                                }
                                value={item.number}
                                defaultValue={
                                  Number(item.productMinimumQuantity) || Number(item.minimum)
                                }
                                onBlur={(e) => {
                                  onChangeNum(item, Number(e.target.value), 'blur');
                                }}
                                onChange={(e) => {
                                  onChangeNum(item, e, 'change');
                                }}
                                className={styles.inputNumber}
                              />
                            </div>
                            {item.relationServiceFlag && (
                              <div>
                                {item.serverGoods &&
                                item.serverGoods.length > 0 &&
                                item.serverGoods.some(
                                  (server) => server.serviceStandardProductVOS.length
                                ) ? (
                                  <GoodsServer
                                    number={BigNumber(item.number || 0)
                                      .div(item.productMinimumSalesUnit || item.saleGroup || 0)
                                      .toNumber()}
                                    services={item.serverGoods}
                                    onClick={() => {
                                      setServerList(item.serverGoods || []);
                                      setShowSelectServer(true);
                                    }}
                                  />
                                ) : (
                                  <div
                                    role="button"
                                    tabIndex={0}
                                    className={styles.selectServer}
                                    onClick={() => {
                                      setServerList([]);
                                      setGoodsServerId(item.id);
                                      setShowSelectServer(true);
                                    }}
                                  >
                                    <span>选择服务</span>
                                    <Icon name="right" size={16} color="#999EB2" />
                                  </div>
                                )}
                              </div>
                            )}
                          </>
                        ) : null}
                      </div>
                    ))}
                  </Checkbox.Group>
                </InfiniteScroll>
              )}
              {packagesList.length > 0 && selectType === 2 && (
                <InfiniteScroll
                  dataLength={packagesList.length}
                  hasMore={packagesList.length < packagesListParamsVal.count}
                  loader={
                    <div className="text-center">
                      <Spin tip="加载中..." />
                    </div>
                  }
                  endMessage={
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>加载到底了</span>
                      </Divider>
                    </div>
                  }
                  next={loadMorePackage}
                  scrollableTarget="list"
                >
                  {packagesList.map((item) => (
                    <PackageItem
                      key={item.id}
                      info={item}
                      hasIds={hasSelectPackagesList.map((packages) => packages.id)}
                      onChangeGoods={(groupId, packageId, productId) => {
                        setSelectChangeGoodsInfo({
                          groupId,
                          packageId,
                          productId,
                          number: item.number,
                        });
                        setSkuList(item.skuList);
                        setShowChangeGoods(true);
                      }}
                      onSelectSpecs={(groupId, shopSkuId, packageId: number, servers) => {
                        selectSpeceParams.current = {
                          groupId,
                          shopSkuId,
                          packageId,
                        };
                        setServerList([...servers]);
                        setSkuList(item.skuList);
                        setShowSelectSpecs(true);
                      }}
                      onChangePackageIds={(e, id) => {
                        if (e) {
                          setHasSelectPackagesList([
                            ...hasSelectPackagesList,
                            ...packagesList.filter((val) => id === val.id),
                          ]);
                        } else {
                          setHasSelectPackagesList(
                            hasSelectPackagesList.filter((val) => val.id !== id)
                          );
                        }
                      }}
                      onChangePackageNum={(e, id) => {
                        packagesList.forEach((goods) => {
                          const items = goods;
                          if (items.id === id) {
                            items.number = e;
                            items.skuList.forEach((item1) => {
                              if (item1.serverGoods && item1.serverGoods.length) {
                                item1.serverGoods.forEach((item2) => {
                                  item2.serviceStandardProductVOS.forEach((server) => {
                                    const servers = server;
                                    servers.serviceNumber = String(
                                      BigNumber(Number(item1.quantity || 0))
                                        .div(Number(item1.saleGroup) || 1)
                                        .times(item.number || 0)
                                        .toNumber()
                                    );
                                  });
                                });
                              }
                            });
                          }
                        });
                        hasSelectPackagesList.forEach((goods) => {
                          const items = goods;
                          if (items.id === id) {
                            items.number = e;
                            items.skuList.forEach((item1) => {
                              if (item1.serverGoods && item1.serverGoods.length) {
                                item1.serverGoods.forEach((item2) => {
                                  item2.serviceStandardProductVOS.forEach((server) => {
                                    const servers = server;
                                    servers.serviceNumber = String(
                                      BigNumber(Number(item1.quantity || 0))
                                        .div(Number(item1.saleGroup) || 1)
                                        .times(item.number || 0)
                                        .toNumber()
                                    );
                                  });
                                });
                              }
                            });
                          }
                        });
                        setPackagesList([...JSON.parse(JSON.stringify(packagesList))]);
                        setHasSelectPackagesList([
                          ...JSON.parse(JSON.stringify(hasSelectPackagesList)),
                        ]);
                      }}
                    />
                  ))}
                </InfiniteScroll>
              )}
              {(state.list.length === 0 && selectType === 1) ||
              (packagesList.length === 0 && selectType === 2) ? (
                <Empty> </Empty>
              ) : null}
            </div>
          </div>
        </div>
        <SelectGoodsStandard
          visible={showSelectGoodsStandard}
          businessSourceType={1}
          standardArr={state.standardFilterParams || []}
          names={state.shopSkuNames || []}
          onClose={() => setShowSelectGoodsStandard(false)}
          onConfirm={(e, names) => {
            state.pageNo = 1;
            state.list = [];
            state.standardFilterParams = e;
            state.shopSkuNames = names;
            getGoods(state);
          }}
        />
        <ChangeGoods
          visible={showChangeGoods}
          onClose={() => setShowChangeGoods(false)}
          groupId={selectChangeGoodsInfo.groupId}
          packageId={selectChangeGoodsInfo.packageId}
          productId={selectChangeGoodsInfo.productId}
          number={selectChangeGoodsInfo.number}
          skuList={skuList}
          onConfirm={onChangePackageGoods}
        />
        <SelectSpecs
          visible={showSelectSpecs}
          serverList={serverList}
          groupId={selectSpeceParams.current.groupId}
          id={selectSpeceParams.current.shopSkuId}
          packageId={selectSpeceParams.current.packageId}
          skuList={skuList}
          onClose={() => setShowSelectSpecs(false)}
          onConfirm={(e) => {
            packagesList.forEach((item) => {
              const item1 = item;
              item1.number = Number(item.number);
              if (item1.id === selectSpeceParams.current.packageId) {
                item1.price = e.newPrice || item1.price;
                item1.skuList.forEach((each) => {
                  if (each.shopSkuId === selectSpeceParams.current.shopSkuId) {
                    const eachs = each;
                    eachs.name = e.name;
                    eachs.groupId = e.groupId;
                    eachs.shopSkuId = e.id;
                    eachs.skuId = e.skuId;
                    eachs.image = e.imageList.length ? e.imageList[0] : '';
                    eachs.quantity = Number(e.quantity);
                    eachs.productId = e.productId;
                    eachs.unit = e.unit;
                    eachs.standardList = [...e.standardList];
                    eachs.isGift = e.isGift;
                    eachs.saleGroup = e.saleGroup;
                  }
                  if (each.shopSkuId === e.id) {
                    // @ts-ignore
                    // eslint-disable-next-line no-param-reassign
                    each.serverGoods =
                      e.serviceStandardList && e.serviceStandardList.length
                        ? e.serviceStandardList.map((group) => ({
                            groupName: group.groupName,
                            serviceStandardProductVOS: group.standardDetailList
                              .filter((items) => items.checked)
                              .map((server) => ({
                                shopSkuId: server.id,
                                skuId: server.skuId,
                                serviceNumber: BigNumber(Number(each.quantity || 0))
                                  .div(Number(each.saleGroup) || 1)
                                  .times(item.number || 0)
                                  .toNumber(),
                                serviceProductPrice: server.marketPrice,
                                serviceName: server.name,
                              })),
                          }))
                        : [];
                  }
                });
              }
            });
            hasSelectPackagesList.forEach((item) => {
              const item1 = item;
              item1.number = Number(item.number);
              if (item1.id === selectSpeceParams.current.packageId) {
                item1.price = e.newPrice || item1.price;
                item.skuList.forEach((each) => {
                  if (each.shopSkuId === selectSpeceParams.current.shopSkuId) {
                    const eachs = each;
                    eachs.name = e.name;
                    eachs.groupId = e.groupId;
                    eachs.shopSkuId = e.id;
                    eachs.skuId = e.skuId;
                    eachs.image = e.imageList.length ? e.imageList[0] : '';
                    eachs.quantity = Number(e.quantity);
                    eachs.productId = e.productId;
                    eachs.unit = e.unit;
                    eachs.standardList = [...e.standardList];
                    eachs.isGift = e.isGift;
                    eachs.saleGroup = e.saleGroup;
                  }
                  if (each.shopSkuId === e.id) {
                    // @ts-ignore
                    // eslint-disable-next-line no-param-reassign
                    each.serverGoods =
                      e.serviceStandardList && e.serviceStandardList.length
                        ? e.serviceStandardList.map((group) => ({
                            groupName: group.groupName,
                            serviceStandardProductVOS: group.standardDetailList
                              .filter((items) => items.checked)
                              .map((server) => ({
                                shopSkuId: server.id,
                                skuId: server.skuId,
                                serviceNumber: BigNumber(each.quantity)
                                  .div(Number(each.saleGroup) || 1)
                                  .times(item1.number || 0)
                                  .toNumber(),
                                serviceProductPrice: server.marketPrice,
                                serviceName: server.name,
                              })),
                          }))
                        : [];
                  }
                });
              }
            });
            setPackagesList(JSON.parse(JSON.stringify(packagesList)));
            setHasSelectPackagesList(JSON.parse(JSON.stringify(hasSelectPackagesList)));
            setShowSelectSpecs(false);
          }}
        />
        <SelectServer
          visible={showSelectServer}
          goodsServerId={goodsServerId}
          onClose={() => setShowSelectServer(false)}
          onConfirm={(arr) => {
            dispatch({
              type: 'setList',
              // @ts-ignore
              payload: state.list.map((tmpItem) => {
                if (tmpItem.id !== goodsServerId) {
                  return tmpItem;
                }
                return {
                  ...tmpItem,
                  serverGoods: arr.filter((item) => item.serviceStandardProductVOS.length),
                };
              }),
            });
            // @ts-ignore
            hasSelectGoodsList.current = hasSelectGoodsList.current.map((it) => {
              if (goodsServerId === it.id) {
                return {
                  ...it,
                  serverGoods: arr.filter((item) => item.serviceStandardProductVOS.length),
                };
              }
              return it;
            });
            setShowSelectServer(false);
          }}
          serverList={serverList}
        />
      </Drawer>
    );
  }
);

SelectGoods.defaultProps = {
  isInitData: false,
  warehouseNo: '',
  distributionCompanyId: undefined,
  orderNo: 0,
  nycbngOrgId: '',
  isAdjust: false,
  isNegativeStockEnable: false,
  targetCompanyId: '',
  payWayNo: '',
};

export default SelectGoods;
