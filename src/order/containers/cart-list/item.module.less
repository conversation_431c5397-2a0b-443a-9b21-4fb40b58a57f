@import './common.less';

.item {
  padding-top: 13px;
  padding-bottom: 13px;
  background-color: @white;
  position: relative;

  &:last-child {
    border-radius: 0 0 @border-radius-base @border-radius-base;
  }

  & + & {
    &::before {
      content: '';
      display: block;
      border-top: 1px solid #f5f6f7;
      position: absolute;
      top: 0;
      right: 25px;
      left: 25px;
    }
  }
}

.goodsInfoBox {
  display: flex;
}

.image {
  display: flex;
  width: 100px;
  height: 100px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: @border-radius-xs;
}

.info {
  display: inline-block;
  width: 224px;
  height: 100px;
  padding-left: 20px;
  vertical-align: top;
}

.title {
  .text-overflow(2);

  line-height: 20px;
  margin-bottom: 8px;
}

.link {
  &,
  &:hover {
    color: @text-color;
  }
}

.label {
  color: @primary-color;
  font-size: @font-size-sm;
  line-height: 1;
  margin-right: 4px;
  padding: 3px 4px;
  border-radius: 2px;
  background-color: @color8;
}

.goodsPrice {
  line-height: 1;
}

.originalPrice {
  // 没了
  color: @text-color-secondary;
  font-size: @font-size-sm;
  line-height: 1;
  margin-bottom: 4px;
  text-decoration-line: line-through;
}

.tag {
  color: @white;
  font-size: @font-size-sm;
  display: inline-block;
  line-height: 1;
  margin-top: 8px;
  padding: 4px 10px;
  border-radius: 10px;
  background-color: @red;
}

.input {
  width: 95px;
  border-color: #f5f6f7;
  background-color: #f5f6f7;
}

.unit {
  font-size: @font-size-sm;
  padding-left: 4px;
}

.question {
  color: @red;
  font-size: @font-size-xl;
  margin-left: 7px;
  vertical-align: middle;
}

.totalPrice {
  color: @red;
}

.close {
  font-size: @font-size-lg;
  line-height: 1;
  border: 0 solid transparent;
  background-color: transparent;
  cursor: pointer;
}

.children {
  width: 406px;
  padding-bottom: 12px;
  padding-left: 60px;
}

.tooltip {
  font-size: @font-size-sm;
  line-height: 17px;
  margin-bottom: 0;
}

.warn {
  color: @red;
  font-size: @font-size-sm;
  line-height: 17px;
}
