import { Mouse<PERSON><PERSON><PERSON>and<PERSON>, useContext, useMemo } from 'react';
import classNames from 'classnames';
import { useMemoizedFn } from 'ahooks';
import RightOutlined from '@/components/icon/right';
import type { CartGoods } from '@/apis';
import goodsPackage from '../goods-package-drawer';
import CartContext from '../../context/cart';
import styles from './package.module.less';

export interface PackageProps {
  item: CartGoods;
  shopId: number;
  disabled?: boolean;
}

function Package({ item, shopId, disabled }: PackageProps) {
  const { getCart } = useContext(CartContext);
  const goodsList = useMemo(
    () => item.packageCartProductVOS.slice(0, 5),
    [item.packageCartProductVOS]
  );
  const onOpenDetail: MouseEventHandler<HTMLDivElement> = useMemoizedFn((e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      goodsPackage({
        shopId,
        goods: item,
        onReload: () => {
          getCart();
        },
      });
    }
  });

  const classes = classNames(styles.package, {
    [styles.full]: goodsList.length >= 5,
    [styles.disabled]: disabled,
  });
  return (
    <div className={classes}>
      <div className={styles.group}>
        {goodsList.map((goods) => (
          <div key={goods.cartNo} title={goods.productName} className={styles.image}>
            <div className={styles.imgBox}>
              <img src={goods.productMainPic} alt={goods.productName} />
            </div>
          </div>
        ))}
      </div>
      <div role="button" tabIndex={0} className={styles.more} onClick={onOpenDetail}>
        <span>
          {item.packageProductTotal}个
          <br />
          商品
        </span>
        <RightOutlined className={styles.moreIcon} />
      </div>
    </div>
  );
}

Package.defaultProps = {
  disabled: false,
};

export default Package;
