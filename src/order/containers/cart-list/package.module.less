@import 'styles/mixins/mixins';

.package {
  display: inline-block;
  max-width: 346px;
  padding: 8px;
  border-radius: @border-radius-xs;
  background-color: #f5f6fa;
  position: relative;
}

.full {
  padding-right: 56px;

  .more {
    width: 77px;
    height: auto;
    padding: 8px;
    border-radius: 0 6px 6px 0;
    background: linear-gradient(270deg, #f5f6f7 0%, #f5f6f7 87%, rgb(245 246 247 / 0%) 100%);
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
  }
}

.disabled {
  .more {
    cursor: not-allowed;
  }
}

.group {
  display: inline-block;
  margin-right: -4px;
  margin-left: -4px;
}

.image {
  display: inline-block;
  padding-right: 4px;
  padding-left: 4px;
  vertical-align: top;

  > img {
    width: 50px;
    height: 50px;
    border-radius: @border-radius-xs;
  }
}

.more {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  display: inline-flex;
  width: 67px;
  height: 50px;
  padding-left: 8px;
  justify-content: flex-end;
  align-items: center;
  text-align: center;
  vertical-align: top;
  cursor: pointer;
}

.moreIcon {
  font-size: @font-size-lg;
  margin-left: 4px;
}

.imgBox {
  display: flex;
  width: 50px;
  height: 50px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: @border-radius-xs;
}
