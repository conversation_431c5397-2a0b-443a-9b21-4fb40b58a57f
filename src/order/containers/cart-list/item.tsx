import { HTMLAttributes, memo } from 'react';
import { Checkbox, CheckboxProps, InputNumber, InputNumberProps, message, Tooltip } from 'antd';
import InfoOutlined from '@/components/icon/info';
import CloseOutlined from '@/components/icon/close';
import QuestionFilled from '@/components/icon/question-fill';
import classNames from 'classnames';
import type { CartGoods, EditCartGoodsNumProps } from '@/apis';
import BigNumber from 'big.js';
import { formatPrice } from '@/utils/utils';
import { useMemoizedFn } from 'ahooks';
import isNaN from 'lodash/isNaN';
import isNil from 'lodash/isNil';
import { CartGoodsService, editCartGoodsNum } from '@/apis';
import { Link } from 'react-router-dom';
import ToggleGoodsOrService, { ToggleGoodsOrServiceProps } from '../toggle-goods-or-service';
import MetaInner from '../toggle-goods-or-service/meta-inner';
import ServiceGroup from '../../components/goods-service';
import GoodsPackage from './package';
import styles from './item.module.less';

export interface ItemProps
  extends Omit<HTMLAttributes<HTMLDivElement>, 'onChange'>,
    Pick<ToggleGoodsOrServiceProps, 'updateSku'> {
  checked: boolean;
  item: CartGoods;
  shopId?: number;
  update?: MultipleParamsFn<[cartNo: number, data: Partial<CartGoods>]>;
  onChange: CheckboxProps['onChange'];
  onDelete?: MultipleParamsFn<[item: CartGoods]>;
}

/**
 * 检查数字精度
 * @param digital {number|string} 被检查的数字
 * @param accuracy {number|string} 数字的精度
 */
function checkDigitalAccuracy(digital: number | string, accuracy: number | string) {
  let digitalPow = 0;
  let accuracyPow = 0;
  try {
    digitalPow = digital.toString().split('.')[1].length;
  } catch (e) {
    console.warn(e);
  }
  try {
    accuracyPow = accuracy.toString().split('.')[1].length;
  } catch (e) {
    console.warn(e);
  }
  const pow = 10 ** Math.max(0, Math.max(digitalPow, accuracyPow));
  const floatValue = new BigNumber(digital).times(pow).mod(new BigNumber(accuracy).times(pow));
  return floatValue.eq(0);
}

function Item({
  checked,
  item,
  shopId,
  update,
  updateSku,
  className,
  onChange,
  onDelete,
  ...props
}: ItemProps) {
  const { productMarketPrice, productPrice } = item;
  // 是否显示原价
  const showOriginalPrice = !!productMarketPrice && productPrice !== productMarketPrice;
  // 是否是商品包
  const isPackage = item.productType === 2;
  const price = isPackage || showOriginalPrice ? productPrice : productMarketPrice;
  const step = isPackage ? 1 : item.productMinimumSalesUnit || 1;
  const min = isPackage ? 1 : item.productMinimumQuantity || 1;

  const onStep = useMemoizedFn((num: string | number) => {
    if (!isNil(num) && +num >= +step && +num !== +item.number) {
      const data: EditCartGoodsNumProps = { cartNo: item.cartNo, productNumber: +num };
      if (isPackage && item.packageId) {
        data.packageId = item.packageId;
      }
      editCartGoodsNum(data);
      if (update) {
        let currentPrice = new BigNumber(price).times(num);
        if (!isPackage && item.serviceProductVOS) {
          let servicePrice = new BigNumber(0);
          item.serviceProductVOS.forEach((group) => {
            group.serviceStandardProductVOS.forEach((service) => {
              servicePrice = servicePrice.plus(service.serviceProductPrice);
            });
          });
          currentPrice = currentPrice.plus(
            servicePrice.times(Math.max(1, Math.floor(+num / +step)))
          );
        }
        update(item.cartNo, {
          number: num as string,
          productPriceAmount: currentPrice.toFixed(item.priceNum),
        });
      }
    }
  });
  const onBlur: InputNumberProps['onBlur'] = useMemoizedFn((e) => {
    const num = e.target.value;
    if (!isNaN(+num)) {
      if (!checkDigitalAccuracy(num, step)) {
        message.error('请输入最小销售单元整数倍');
        return;
      }
      onStep(Math.max(+min, +num));
    }
  });

  const productStatus = +item.productStatus;
  const disabled = productStatus !== 50;

  let url: string;
  let title;
  let metaAndService;
  let extra;
  if (isPackage) {
    url = `/shop/goods/package/detail?packageId=${item.packageId}&shopId=${shopId}`;
    title = (
      <>
        <span className={styles.label}>套餐包</span>
        <Link to={url} className={styles.link}>
          {item.packageName}
        </Link>
      </>
    );
    extra = <GoodsPackage disabled={disabled} item={item} shopId={shopId || 0} />;
    metaAndService = <MetaInner meta={item.packageProductSpecificationList} />;
  } else {
    url = `/shop/goods/detail?shopSkuId=${item.shopSkuId}&shopId=${shopId}&payWayNo=${item.payWayNo}`;
    title = (
      <Link to={url} className={styles.link}>
        {item.productName}
      </Link>
    );
    const checkService = item.serviceProductVOS && item.serviceProductVOS.length !== 0;
    metaAndService = (
      <ToggleGoodsOrService
        item={item}
        suffix={
          (checkService || item.isExistServiceSku) && `；${checkService ? '修改' : '选择'}服务`
        }
        updateSku={updateSku}
      />
    );
    const serviceList: CartGoodsService[] | null = item.serviceProductVOS || null;
    if (serviceList && serviceList.length !== 0) {
      let number: number = +item.number;
      if (isNaN(number)) {
        number = 1;
      }
      number = Math.max(1, Math.ceil(number / +step));
      extra = <ServiceGroup services={serviceList} number={number} />;
    }
  }

  if (disabled) {
    let warn;
    if (productStatus === 0) {
      warn = <span>当前区域不配送</span>;
    } else if (productStatus === 90) {
      warn = <span>商品已失效</span>;
    } else if (productStatus === 10) {
      warn = (
        <>
          <span>付款方式已变化</span>
          <ToggleGoodsOrService item={item} updateSku={updateSku} className="ml-5">
            请重新选购
          </ToggleGoodsOrService>
        </>
      );
    }
    if (warn) {
      extra = (
        <>
          {extra}
          <div className={classNames(styles.warn, { 'mt-3': !!extra })}>
            <InfoOutlined className="mr-1" />
            {warn}
          </div>
        </>
      );
    }
  }

  const classes = classNames(className, styles.item);
  return (
    <div {...props} className={classes}>
      <div className={styles.tr}>
        <div className={styles.td1}>
          <Checkbox checked={checked} disabled={disabled} value={item.cartNo} onChange={onChange} />
        </div>
        <div className={styles.td2}>
          <div className={styles.goodsInfoBox}>
            <Link to={url}>
              <div className={styles.image}>
                <img src={item.productMainPic || item.packageProductMainPic} alt="" />
              </div>
            </Link>
            <div className={styles.info}>
              <h4 className={styles.title}>{title}</h4>
              {metaAndService}
            </div>
          </div>
        </div>
        <div className={styles.td3}>
          {showOriginalPrice && (
            <p className={styles.originalPrice}>￥{formatPrice(productMarketPrice)}</p>
          )}
          <p className={styles.goodsPrice}>￥{formatPrice(price, item.priceNum)}</p>
          {item.haveTranFee ? <div className={styles.tag}>含运费</div> : null}
        </div>
        <div className={styles.td4}>
          <InputNumber
            value={item.number}
            min={min}
            step={step}
            precision={item.productUnitNum}
            className={styles.input}
            onStep={onStep}
            onBlur={onBlur}
          />
          <span className={styles.unit}>{item.productUnit}</span>
          {!isPackage && (
            <Tooltip
              title={
                <p className={styles.tooltip}>
                  最小销售单元({step} {item.productUnit})
                  <br />
                  最小起订量({min} {item.productUnit})
                </p>
              }
            >
              <QuestionFilled className={styles.question} />
            </Tooltip>
          )}
        </div>
        <div className={styles.td5}>
          <span className={styles.totalPrice}>￥{formatPrice(item.productPriceAmount)}</span>
        </div>
        <div className={styles.td6}>
          <button
            type="button"
            className={styles.close}
            onClick={(e) => {
              e.stopPropagation();
              if (onDelete) {
                onDelete(item);
              }
            }}
          >
            <CloseOutlined />
          </button>
        </div>
      </div>
      {extra && <div className={styles.children}>{extra}</div>}
    </div>
  );
}

Item.displayName = 'CartItem';
Item.defaultProps = {
  shopId: 0,
  update: undefined,
  onDelete: undefined,
};

export default memo(Item, (prev, next) => prev.checked === next.checked && prev.item === next.item);
