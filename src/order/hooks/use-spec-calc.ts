import { useState } from 'react';
import type { GoodsDetailResult } from '@/apis';
import { PathFinder, getPrime, descartes, cartesian } from '../utils/sku-calc';

export default function useSpecCalc() {
  const WAY_KEY = 'S_';
  const [info, setInfo] = useState({} as GoodsDetailResult);
  const [checkedList, setCheckedList] = useState<number[]>([]);
  // const [disabledIds, setDisabledIds] = useState<number[]>([]);
  const [specData, setSpecData] = useState<{ validIds: number[]; inValidIds: number[] }>({
    validIds: [],
    inValidIds: [],
  });
  const [pathFinder, setPathFinder] = useState<PathFinder>();
  // const valueInLabelRef = useRef<{
  //   data: { [key: string]: number };
  //   keys: string[];
  //   values: number[];
  // }>({ data: {}, keys: [], values: [] });
  const [valueInLabelObj, setValueInLabelObj] = useState<{
    data: { [key: string]: number };
    keys: string[];
    values: number[];
  }>({ data: {}, keys: [], values: [] });

  const setInfoData = (val: GoodsDetailResult) => {
    setInfo({ ...val });
  };

  const setCheckedListData = (values: number[]) => {
    setCheckedList([...values]);
  };

  const getValidData = () => specData;

  // 规格id生成笛卡尔积
  const createSkuCartesianArr = () => {
    const ids = info.choiceList.map((item) =>
      item.value
        .map((item2) => item2.id || (item2.childrenList || []).map((child) => child.id))
        .flat()
        .map((item3) => item3)
    );
    const titles = info.choiceList.map((item) =>
      item.value
        .map((item2) => item2.title || item2.childrenList?.map((child) => child.title))
        .flat()
    );
    const unitIds = info.unitList.map((item) => item.id);
    const unitNames = info.unitList.map((item) => item.title);
    const idsByUnit: number[][] = [...ids, unitIds];
    return {
      ids,
      titles,
      idsByUnit,
      titlesByUnit: cartesian([titles, unitNames]) as string[][],
    };
  };

  // 处理生成有效id值和无效id值
  const processValidData = (unDisabled: number[]) => {
    const validIds: number[] = [];
    const inValidIds: number[] = [];
    const { values: valueInLabelValues, keys: valueInLabelKeys } = valueInLabelObj;
    valueInLabelValues.forEach((item, index) => {
      const val = valueInLabelKeys[index].split('_')[1];
      if (unDisabled.includes(item)) {
        validIds.push(Number(val));
      } else {
        inValidIds.push(Number(val));
      }
    });
    return {
      validIds,
      inValidIds,
    };
  };

  // 处理sku规格禁用逻辑
  const processSkuSpecData = () => {
    const type = createSkuCartesianArr().ids;
    const specList = info.allSkuList.map((item) => item.standardCase);
    // 抹平规格内容
    const types = type.flat();
    // 通过抹平规格，获取规格对应质数
    const prime = getPrime(types.length);
    // 质数对应规格数 枚举值处理
    const valueInLabel: { [key: string]: number } = {};
    types.forEach((item, index) => {
      valueInLabel[WAY_KEY + item] = prime[index];
    });
    // 根据规格坐标，排序质数坐标
    const way = type.map((i) => i.map((ii) => valueInLabel[WAY_KEY + ii]));
    const allSku = descartes(type);
    // 使用笛卡尔积计算下sku
    const sku: { stock: number; skuCase: string[]; skuPrime: number[] }[] = allSku.map(
      (item: string[]) => ({
        // 随机库存内容
        stock: specList.includes(item.join('-')) ? 1 : 0,
        // // 规格名
        // skuName: item,
        skuCase: item,
        // 规格对应质数
        skuPrime: item.map((ii) => valueInLabel[WAY_KEY + ii]),
      })
    );
    // 筛选可选的 SKU
    const canUseSku = sku.filter((item) => item.stock);
    // 初始化规格展示内容
    const newPathFinder = new PathFinder(
      way,
      canUseSku.map((item) => item.skuPrime)
    );
    // 设置已选中的值
    const nowSpecList = info?.currentSpec?.split('-') || [];
    nowSpecList.forEach((item, index) => {
      if (index !== nowSpecList.length - 1) {
        const primeVal = valueInLabel[WAY_KEY + item];
        newPathFinder?.add(primeVal);
      }
    });

    // 获取不可选规格内容（质数组）
    const unDisabled = newPathFinder
      .getWay()
      .flat()
      .filter((item) => item);
    const valueInLabelKeys = Object.keys(valueInLabel);
    const valueInLabelValues = Object.values(valueInLabel);
    // const disabled = valueInLabelValues;
    const validIds: number[] = [];
    const inValidIds: number[] = [];
    valueInLabelValues.forEach((item, index) => {
      const val = valueInLabelKeys[index].split('_')[1];
      if (unDisabled.includes(item)) {
        validIds.push(Number(val));
      } else {
        inValidIds.push(Number(val));
      }
    });
    // console.log('valueInLabel', valueInLabel);
    // console.log('unDisabled', unDisabled);
    // console.log('valueInLabel', valueInLabel);
    // console.log('validIds', validIds);
    // console.log('inValidIds', inValidIds);
    // console.log('pathFinder', newPathFinder);

    setPathFinder(newPathFinder);
    // setDisabledIds([...inValidIds]);
    setSpecData({ ...{ inValidIds, validIds } });
    // console.log('disabled', disabled);
    setValueInLabelObj({
      ...{
        data: valueInLabel,
        keys: valueInLabelKeys,
        values: valueInLabelValues,
      },
    });
  };

  /**
   * 点击选择规格
   * @param {String} val
   * @param {Number} prime
   */
  const onClickSelType = (val: number, prime: number, primeIndex: number) => {
    // 获取已经选中的规格,质数，规格枚举值,以及原本规格名称
    const { ids } = createSkuCartesianArr();
    const { data: valueInLabel } = valueInLabelObj;
    // const { selected, valueInLabel, type: stateType } = this.state;
    // 检查此次选择是否在已选内容中
    const index = checkedList.indexOf(val);
    // 获取已经有的矩阵值
    const light = pathFinder?.light;
    // 如果未选中则提供选中，如果选中移除
    if (index > -1) {
      pathFinder?.remove(prime);
      // checkedList.splice(index, 1);
    } else if (light && light[primeIndex].includes(2)) {
      // 如果同规格中，有选中，则先移除选中，
      // 获取需要移除的同行规格
      const removeType = WAY_KEY + ids[primeIndex][light[primeIndex].indexOf(2)];
      // 获取需要提出的同行规格质数
      const removePrime = valueInLabel[removeType];
      // 移除
      pathFinder?.remove(removePrime);
      // checkedList.splice(checkedList.indexOf(removeType), 1);
      // 移除同行后，添加当前选择规格
      pathFinder?.add(prime);
    } else {
      pathFinder?.add(prime);
    }

    // 更新不可选规格
    const unDisabled = pathFinder?.getWay().flat() || [];
    // console.log('selected', selected);
    const { inValidIds, validIds } = processValidData(unDisabled);
    // console.log('unDisabled', unDisabled);
    // console.log('inValidIds', inValidIds);

    // setDisabledIds([...inValidIds]);
    setSpecData({ ...{ inValidIds, validIds } });
  };

  return {
    processSkuSpecData,
    setInfoData,
    setCheckedListData,
    onClickSelType,
    getValidData,
  };
}
