import { useMemo } from 'react';
import type { GoodsDetailResult, GetPackageGoodsDetailResult } from '@/apis';

export interface GoodsValue {
  meta: number[] | string[];
  unit: string | number;
  selectUnit: string;
  payWayNo: number;
}

function useGoods(
  goods?: GoodsDetailResult | GetPackageGoodsDetailResult | null
): [values: GoodsValue | null, disableList: number[][] | string[][], checkedGroupIds: number[]] {
  const id = goods ? (goods as GoodsDetailResult).shopSkuId || goods.id : null;
  return useMemo(() => {
    if (goods) {
      const allSkuList =
        (goods as GoodsDetailResult).allSkuList ||
        (goods as GetPackageGoodsDetailResult).allStandardVOList;
      const sku = (allSkuList || []).find((item) => item.shopSkuId === id);
      const meta = sku ? sku.standardCase.split('-').map((current) => +current) : [];

      // const isSpecGroup =
      //   goods.choiceList
      //     .map((item) => item.value.map((valueItem) => valueItem.groupId))
      //     .flat()
      //     .filter((item) => item).length > 0;
      const checkedGroupIds: number[] = [];
      const disabled: number[][] = [];
      const enableList = (goods as GoodsDetailResult).enableList || allSkuList;
      if (enableList && enableList.length > 0) {
        const enabledCaseList = enableList.map((item) => item.standardCase) || [];
        goods.choiceList.forEach((choice, index) => {
          disabled[index] = [];
          const tmpMeta = [...meta];
          choice.value.forEach((item) => {
            if (item.id) {
              if (meta[index] !== item.id) {
                tmpMeta[index] = item.id as number;
                if (!enabledCaseList.includes(tmpMeta.join('-'))) {
                  disabled[index].push(item.id as number);
                }
              }
            } else {
              item.childrenList?.forEach((child) => {
                if (meta[index] !== child.id) {
                  tmpMeta[index] = child.id as number;
                  if (!enabledCaseList.includes(tmpMeta.join('-'))) {
                    // disabled[index].push(child.id as number);
                  }
                } else {
                  checkedGroupIds.push(child.groupId);
                }
              });
            }
          });
        });
      }

      const { unitList } = goods as GoodsDetailResult;
      let unit: string | number = '';
      if (unitList && unitList.length > 0) {
        const unitItem = unitList.find((item) => item.title === goods.unit);
        if (unitItem) {
          unit = unitItem.id;
        }
      }

      return [
        {
          meta,
          unit,
          selectUnit: goods.unit,
          payWayNo: goods.currentPayWayNo,
        },
        disabled,
        checkedGroupIds,
      ];
    }
    return [null, [], []];
  }, [id, goods?.unit, goods?.currentPayWayNo]); // eslint-disable-line
}

export default useGoods;
