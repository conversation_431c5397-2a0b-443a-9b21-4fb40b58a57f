import { StandardItem } from '@/apis';
import { PropsWithChildren } from 'react';
import { Price } from '@/components';
import styles from './index.module.less';

interface GoodsItemProp {
  orderType?: 'production' | 'sales' | 'inventory' | 'procurement';
  img?: string;
  productPrice?: string;
  number?: string;
  productUnit?: string;
  productName?: string;
  productSpecificationList?: [{ name: string; value: string }] | StandardItem[];
  stock?: string;
  saleGroup?: string | number;
  floorPrice?: number;
  isOrder?: boolean;
  price?: boolean;
  isGift?: number | null;
}

function GoodsCard({
  orderType,
  productPrice,
  number,
  productUnit,
  productName,
  productSpecificationList,
  img,
  saleGroup,
  stock,
  floorPrice,
  isOrder,
  price,
  isGift,
}: PropsWithChildren<GoodsItemProp>) {
  let detailType = null;
  if (orderType === 'production') {
    detailType = (
      <>
        {/* <div className={styles.stockNum}>库存：{stock || '0'}</div> */}
        <div className={styles.numberOf}>
          生产数量：{number}
          {productUnit}
        </div>
      </>
    );
  } else if (orderType === 'sales') {
    detailType = isOrder ? (
      <div className={styles.isOrder}>
        <Price value={productPrice as string} format color="#040919" decimalNum={null} />
        <div className={styles.text}>x{number || 0}</div>
      </div>
    ) : (
      <div className={styles.numberOf}>
        {price ? (
          <div className={styles.text}>
            数量：{number || 0}
            {productUnit}
          </div>
        ) : (
          <div style={{ paddingRight: '8px' }}>
            <Price value={productPrice as string} format color="#040919" decimalNum={null} />
            &nbsp;X&nbsp;{number || 0}
            {productUnit}
          </div>
        )}
      </div>
    );
  } else if (orderType === 'inventory') {
    detailType = (
      <>
        <div>
          库存：{stock}
          {productUnit}
        </div>
        {/* <div className={styles.numberOf}>生产数量：10桶</div> */}
      </>
    );
  } else {
    detailType = (
      <>
        <div className={styles.label}>
          最小销售单元：{saleGroup}
          {productUnit}
        </div>
        <div className={styles.increase}>
          <div>
            <span className={styles.label}>底价：</span>
            {floorPrice}
          </div>
          <div>
            <span className={styles.label}>库存：</span>
            {stock}
          </div>
        </div>
      </>
    );
  }
  return (
    <div className={styles.card}>
      <div className={styles.imgIcon}>
        <img src={img} alt="" className={styles.img} />
        {isGift === 1 && <span className={styles.gift}>赠品</span>}
        {/* <div className={styles.goodsIcon}>新增</div> */}
      </div>
      <div className={styles.info}>
        <div>
          <div className={styles.goodsName}>{productName}</div>
          <div className={styles.specifications}>
            {productSpecificationList?.map((item) => (
              <span
                className={styles.goodsUnit}
                title={`${item.name}:${item.value}`}
                key={item.name}
              >
                {item.name}：{item.value}
              </span>
            ))}
          </div>
        </div>
        {detailType}
      </div>
    </div>
  );
}

GoodsCard.defaultProps = {
  orderType: '',
  productPrice: '',
  number: '',
  productUnit: '',
  productName: '',
  productSpecificationList: [],
  img: '',
  stock: '',
  floorPrice: 0,
  saleGroup: '',
  isOrder: false,
  price: false,
  isGift: null,
};

export default GoodsCard;
