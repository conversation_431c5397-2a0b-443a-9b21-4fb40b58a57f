@import 'styles/mixins/mixins';

.card {
  display: flex;
  // padding: 20px 0 16px;
}

.imgIcon {
  min-width: 108px;
  min-height: 108px;
  line-height: 108px;
  margin-right: 12px;
  position: relative;

  .img {
    height: 108px;
    border-radius: 10px;
  }

  .goodsIcon {
    color: #fff;
    font-size: 12px;
    width: 44px;
    height: 21px;
    line-height: 21px;
    position: absolute;
    top: 0;
    border-radius: 10px;
    text-align: center;
    background: rgb(0 0 0 / 60%);
  }
}

.gift {
  color: #fff;
  font-size: 10px;
  display: inline-block;
  width: 32px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  bottom: 0;
  left: 0;
  text-align: center;
  opacity: 0.8;
  border-radius: 0 12px;
  background: rgb(249 174 8 / 80%);
}

.info {
  display: flex;
  width: 0;
  flex: 1;
  justify-content: space-between;
  flex-direction: column;

  .goodsName {
    margin-bottom: 8px;
    .text-overflow(2);

    word-wrap: break-word;
  }

  .specifications {
    .text-overflow();
  }

  .goodsUnit {
    color: #888b98;
    font-size: 12px;
    // display: inline-block;
    // width: 170px;
    margin-right: 5px;
    // .text-overflow();
  }
}

.stockNum {
  color: #888b98;
  font-size: 12px;
}

.increase {
  display: flex;
  justify-content: space-between;
}

.label {
  color: #888b98;
  font-size: 12px;
}

.isOrder {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .text {
    color: #888b98;
  }
}
