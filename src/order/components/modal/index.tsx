import { Modal } from 'antd';
import { PropsWithChildren } from 'react';
import styles from './index.module.less';

interface popupProps {
  visible: boolean;
  content?: string;
  okText?: string;
  cancelText?: string;
  onDetermine: () => void;
  onclose: () => void;
}

function PopupModal({
  visible,
  okText,
  content,
  cancelText,
  onDetermine,
  onclose,
}: PropsWithChildren<popupProps>) {
  return (
    <Modal
      title="提示"
      visible={visible}
      width="311px"
      className={styles.modal}
      centered
      onOk={onDetermine}
      okText={okText}
      cancelText={cancelText}
      onCancel={onclose}
      getContainer={false}
      closable={false}
      maskClosable={false}
    >
      <div>{!content ? '您编辑的内容尚未保存，确定要离开吗？' : content}</div>
    </Modal>
  );
}

PopupModal.defaultProps = {
  content: '',
  okText: '继续编辑',
  cancelText: '离开',
};

export default PopupModal;
