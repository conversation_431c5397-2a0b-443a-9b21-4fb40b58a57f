import { CanOptButtonList } from '@/apis/order/get-order-sales';
import { Button } from 'antd';
import styles from './index.module.less';

interface OrderButtonProps {
  actionName?: string;
  actionCode?: number;
  hiddenBtn?: CanOptButtonList[];
  size?: boolean;
  btnType?: boolean;
  onOrderProcessing?: () => void;
  onPartial?: () => void;
}

function OrderButton(props: OrderButtonProps) {
  const { actionName, actionCode, onOrderProcessing, onPartial, hiddenBtn, size, btnType } = props;
  const onOrderBtn = () => {
    // eslint-disable-next-line default-case
    switch (actionCode) {
      case 2010:
        // @ts-ignore
        onOrderProcessing();
        break;
    }
  };
  const onHiddenBtn = (hidActionCode: number) => {
    // eslint-disable-next-line default-case
    switch (hidActionCode) {
      case 1311:
        // @ts-ignore
        onPartial();
        break;
    }
  };
  if (hiddenBtn && hiddenBtn.length !== 0) {
    return (
      <>
        {hiddenBtn.map((item) => (
          <div
            className={styles.btn}
            role="button"
            tabIndex={0}
            onClick={(e) => {
              e.stopPropagation();
              onHiddenBtn(item.actionCode);
            }}
            key={item.batchNo}
          >
            {item.actionName}
          </div>
        ))}
      </>
    );
  }
  return (
    <Button
      type={btnType ? 'default' : 'primary'}
      size={size ? 'middle' : 'small'}
      key={actionCode}
      className={styles.btnS}
      onClick={(e) => {
        e.stopPropagation();
        onOrderBtn();
      }}
    >
      {actionName}
    </Button>
  );
}

OrderButton.defaultProps = {
  actionName: '',
  actionCode: null,
  onOrderProcessing: () => {},
  onPartial: null,
  hiddenBtn: [],
  size: true,
  btnType: false,
};

export default OrderButton;
