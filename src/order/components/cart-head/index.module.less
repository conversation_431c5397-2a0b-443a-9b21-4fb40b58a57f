@import 'styles/mixins/mixins';

.head {
  display: flex;
  margin-bottom: 10px;
  border-radius: @border-radius-base;
  background-color: @white;
}

.link {
  color: @text-color-secondary;
  display: block;
  min-width: 160px;
  line-height: 36px;
  padding: 12px 20px;
  text-align: center;
  position: relative;

  &:global {
    &.active {
      color: @primary-color;

      &::after {
        content: '';
        display: block;
        width: 24px;
        height: 3px;
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 2px;
        background-color: @primary-color;
      }
    }
  }
}
