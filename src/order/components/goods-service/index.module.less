@import 'styles/mixins/mixins';

.service {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  display: flex;
}

.open {
  .group {
    max-height: none;
  }

  .btnIcon {
    transform: rotate(180deg);
  }
}

.body,
.name {
  .flex-column();

  overflow: hidden;
}

.group {
  max-height: 58px;
  padding-left: 4px;
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    display: block;
    height: 8px;
    border-left: 1px solid rgba(#b1b3be, 0.5);
    position: absolute;
    top: 3px;
    left: 0;
  }
}

.item {
  display: flex;
  line-height: 14px;
  margin-bottom: 8px;

  &:last-child {
    margin-bottom: 0;
  }
}

.name {
  .text-overflow();

  padding-right: 8px;
}

.price {
  color: @text-color;
}

.btn {
  font-size: @font-size-sm;
  display: inline-block;
  line-height: 14px;
  padding: 0;
  border: 0 solid transparent;
  background-color: transparent;
}

.btnIcon {
  font-size: @font-size-lg;
  transition-duration: 0.3s;
}

.text {
  white-space: nowrap;
}
