import { H<PERSON><PERSON><PERSON>ribut<PERSON>, Mouse<PERSON><PERSON><PERSON><PERSON><PERSON>, useMemo, useState } from 'react';
import classnames from 'classnames';
import { CartGoodsService } from '@/apis';
import DownOutlined from '@/components/icon/down';
import { useMemoizedFn } from 'ahooks';
import styles from './index.module.less';

export interface GoodsServiceProps extends HTMLAttributes<HTMLDivElement> {
  services: CartGoodsService[];
  number?: string | number;
}

function GoodsService({ services, number, className, ...props }: GoodsServiceProps) {
  const [open, setOpen] = useState(false);
  const list = useMemo(() => {
    const items: CartGoodsService['serviceStandardProductVOS'] = [];
    services.forEach((service) => {
      service.serviceStandardProductVOS.forEach((item) => {
        items.push(item);
      });
    });
    return items;
  }, [services]);
  const onClick: MouseEventHandler<HTMLButtonElement> = useMemoizedFn((e) => {
    e.preventDefault();
    e.stopPropagation();
    setOpen((prevState) => !prevState);
  });

  const classes = classnames(className, styles.service, { [styles.open]: open });
  return (
    <div {...props} className={classes}>
      <div className={`pr-1 ${styles.text}`}>服务</div>
      <div className={styles.body}>
        <div className={styles.group}>
          {list.map((item) => (
            <div key={item.shopSkuId} className={styles.item}>
              <div className={styles.name}>{item.serviceName}</div>
              <div className={styles.text}>
                <span className={styles.price}>¥{item.serviceProductPrice}</span>
                {Number(number || item.serviceNumber) > 0 && (
                  <span>&nbsp;x&nbsp;{number || item.serviceNumber}</span>
                )}
              </div>
            </div>
          ))}
        </div>
        {list.length > 3 && (
          <div className="pt-3 text-right">
            <button type="button" className={styles.btn} onClick={onClick}>
              <span>{open ? '收起' : '展开'}</span>
              <DownOutlined className={styles.btnIcon} />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

GoodsService.defaultProps = {
  number: 0,
};

export default GoodsService;
