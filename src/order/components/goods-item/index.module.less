@import 'styles/mixins/mixins';

.goodsInfo {
  display: flex;
}

.goodsImgBox {
  position: relative;
}

.goodsImg {
  display: flex;
  width: 80px;
  height: 80px;
  margin-right: 8px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: 12px;
}

.goodsText {
  display: flex;
  margin-right: 16px;
  justify-content: space-between;
  flex-direction: column;

  & .name {
    width: 143px;
    .text-overflow();
  }

  & .label {
    color: #888b98;
    font-size: 12px;
    margin-bottom: 3px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.labelText {
  margin-top: 2px;
}

.standard {
  width: 143px;
  .text-overflow();
}

.standardName:last-child .tip {
  display: none;
}
