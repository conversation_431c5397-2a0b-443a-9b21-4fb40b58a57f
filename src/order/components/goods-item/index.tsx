import { PropsWithChildren, useMemo } from 'react';
import { StandardItem } from '@/apis';
import isString from 'lodash/isString';
import styles from './index.module.less';

interface Info {
  img?: string;
  name?: string;
  standard?: StandardItem[] | string;
  skuCode?: string;
  unit?: string;
  stock?: string;
  showStock?: boolean;
  saleGroup?: string;
  floorPrice?: number;
}

function GoodsItem({
  img,
  name,
  standard,
  skuCode,
  unit,
  showStock,
  stock,
  saleGroup,
  floorPrice,
}: PropsWithChildren<Info>) {
  const standardVal = useMemo(() => {
    let str = '';
    if (isString(standard)) {
      str = standard;
    } else {
      standard?.forEach((item, index) => {
        str += `${item.name}: ${item.value}${index === standard.length - 1 ? '' : ','}`;
      });
    }
    return str;
  }, [standard]);

  return (
    <div className={styles.goodsInfo}>
      <div className={styles.goodsImg}>
        <img src={img || 'https://img.huahuabiz.com/default/image/default_holder.png'} alt="" />
      </div>

      <div className={styles.goodsText}>
        <div className={styles.name} title={name}>
          {name}
        </div>
        <div className={styles.label}>
          <div className={styles.standard} title={standardVal}>
            {isString(standard) ? (
              standard
            ) : (
              <div>
                {standard?.length
                  ? standard.map((item) => (
                      <span key={`${item.value}${item.name}`} className={styles.standardName}>
                        {item.name}: {item.value}
                        <span className={styles.tip}>，</span>
                      </span>
                    ))
                  : null}
              </div>
            )}
          </div>
          <div className={styles.labelText}>{skuCode}</div>
          {unit ? (
            <div>
              <span>单位: {unit}</span>
            </div>
          ) : null}
          <div className={styles.label}>最小销售单元：{saleGroup}</div>
        </div>
        <div>
          {showStock ? (
            <div>
              <div className={styles.label}>
                底价：{floorPrice} 库存： {stock}
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}

GoodsItem.defaultProps = {
  img: '',
  name: '',
  standard: [],
  skuCode: '',
  unit: '',
  stock: '',
  saleGroup: '',
  showStock: false,
  floorPrice: 0,
};

export default GoodsItem;
