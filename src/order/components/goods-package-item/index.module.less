@import 'styles/mixins/mixins';

.goodsItem {
  display: flex;
}

.checkbox {
  height: 80px;
  line-height: 80px;
  margin-right: 8px;
}

.img {
  width: 80px;
  height: 80px;
  margin-right: 12px;
  border-radius: 10px;
}

.info {
  flex: 1;
  height: 100%;
}

.name {
  font-weight: 600;
  width: 187px;
  margin-bottom: 8px;
  .text-overflow();
}

.standard {
  color: #888b98;
  font-size: 12px;
  display: flex;
  height: 24px;
  line-height: 24px;
  margin-bottom: 12px;
  padding: 0 8px;
  cursor: pointer;
  border-radius: 6px;
  background-color: rgb(177 179 190 / 10%);
}

.standardList {
  flex: 1;
  .text-overflow();
}

.standardItem:last-child span {
  display: none;
}

.standardNum {
  color: #040919;
  display: flex;
  padding-left: 8px;
  position: relative;
  align-items: center;

  &::before {
    content: '';
    width: 1px;
    height: 12px;
    position: absolute;
    top: 6px;
    left: 0;
    background-color: #d8d8d8;
  }
}

.num {
  font-size: 12px;
}

.goodsItemServerBox {
  display: flex;
  padding: 12px 0 0;
  justify-content: flex-end;
}

.goodsItemServer {
  width: 180px;
}
