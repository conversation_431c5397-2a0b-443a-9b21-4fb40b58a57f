import { Icon } from '@/components';
import { CartGoodsService, GetGoodsPackageGroupGoodsItem } from '@/apis';
import GoodsService from '../goods-service';
import styles from './index.module.less';

interface GoodsPackageItemProps {
  info: GetGoodsPackageGroupGoodsItem;
  onSelectSpecs: MultipleParamsFn<[id: number, goodsServers: CartGoodsService[]]>;
}

function PackageItem({ info, onSelectSpecs }: GoodsPackageItemProps) {
  return (
    <div className={styles.item}>
      <div className={styles.goodsItem}>
        <img
          className={styles.img}
          src={
            info.imageList.length
              ? info.imageList[0]
              : 'https://img.huahuabiz.com/default/image/default_holder.png'
          }
          alt=""
        />
        <div className={styles.info}>
          <div className={styles.name}>{info.name}</div>
          <div
            role="button"
            tabIndex={0}
            className={styles.standard}
            onClick={() => onSelectSpecs(info.id, info.serviceProductVOS)}
          >
            <span>已选:</span>
            <span className={styles.standardList}>
              {info.standardList.map((item) => (
                <span className={styles.standardItem} key={item.name + item.value}>
                  {item.value}
                  <span>*</span>
                </span>
              ))}
            </span>
            <span className={styles.standardNum}>
              共{info.skuCount}种
              <Icon name="down" size={16} color="#999EB2" />
            </span>
          </div>
          <div className={styles.num}>
            搭配数量：{info.quantity}
            {info.unit}
          </div>
        </div>
      </div>
      <div className={styles.goodsItemServerBox}>
        {info.serviceProductVOS && info.serviceProductVOS.length > 0 && (
          <GoodsService className={styles.goodsItemServer} services={info.serviceProductVOS} />
        )}
      </div>
    </div>
  );
}

export default PackageItem;
