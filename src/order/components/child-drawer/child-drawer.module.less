.childDrawer {
  position: absolute;

  :global {
    .ant-drawer-content,
    .ant-drawer-header {
      background-color: #f5f6fa;
    }

    .ant-drawer-content-wrapper {
      transition: none;
    }

    .ant-drawer {
      position: absolute;
    }
  }

  .shutDown {
    color: #888b98;
    font-size: 16px;
    font-weight: normal;
    font-family: PingFangSC-Regular;
  }
}

.card {
  display: flex;
  margin-bottom: 20px;
  padding: 12px 20px;
  justify-content: space-between;
  align-items: center;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;

  :global {
    .ant-input-number-group {
      width: 120px;
      height: 36px;
      line-height: 36px;
      border-radius: 10px;
      background: #f5f6fa;
    }

    .ant-input-number-input:placeholder-shown {
      height: 36px;
    }

    .ant-input-number-input {
      height: 36px;
    }

    .ant-input-group-wrapper {
      width: 120px;
      border-radius: 4px;
      background: #f5f6fa;
    }

    .ant-input-number-group-addon {
      border: none;
      border-radius: 10px;
      background: #f5f6fa;
    }

    .ant-input-number-group .ant-input-number {
      border-radius: 10px;
    }

    .ant-input-group-addon {
      background: #f5f6fa;
    }

    .ant-input-group-addon {
      border: none;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      background: #f5f6fa;
      color: black;
    }

    .adm-stepper .adm-stepper-input,
    .adm-button:not(.adm-button-default).adm-button-fill-none {
      height: 36px;
    }

    .adm-stepper .adm-stepper-input {
      background: #f5f6fa;
    }

    // .adm-stepper-minus {
    //   margin-right: 9px;
    // }

    // .adm-stepper-plus {
    //   margin-left: 9px;
    // }

    .adm-stepper-middle {
      border: none;
    }

    .adm-stepper {
      width: 170px;
      border-radius: 10px;
    }

    // .adm-input {
    //   width: 70px;
    // }
  }

  .icon {
    color: black;
  }
}

.title {
  font-size: 16px;
}

.pricingCard,
.priceIsEdit {
  margin-bottom: 20px;
  padding: 12px;
  border-radius: 18px;
  border: 1px solid #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;
  cursor: pointer;

  .describe {
    color: rgb(4 9 25 / 60%);
    font-size: 12px;
    margin-top: 8px;
  }
}

.priceIsEdit {
  position: relative;
  border: 1px solid #008cff;

  &::before {
    content: '';
    width: 42px;
    height: 39px;
    position: absolute;
    top: -1px;
    right: -1px;
    background-image: url('../../img/selected-mark.png');
  }
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .selectedIcon {
    color: #008cff;
    font-size: 16px;
  }
}

.footer {
  padding: 20px;

  :global {
    .ant-btn {
      width: 100%;
    }
  }
}
