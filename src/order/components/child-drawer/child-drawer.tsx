import { Icon } from '@/components';
import { But<PERSON>, Drawer, InputNumber, Modal } from 'antd';
import { Stepper } from 'antd-mobile';
import { PropsWithChildren, useEffect, useState } from 'react';
import styles from './child-drawer.module.less';

interface ChildDrawerProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  type: number;
  height?: number;
  // eslint-disable-next-line no-unused-vars
  getPricing: (id: number, title: string) => void;
  // eslint-disable-next-line no-unused-vars
  getBatchValue: (value?: number | null, goodsNumber?: number | null) => void;
  isEditPrice: boolean;
}

function ChildDrawer({
  visible,
  onClose,
  title,
  type,
  height,
  getPricing,
  getBatchValue,
  isEditPrice,
}: PropsWithChildren<ChildDrawerProps>) {
  const [pricing, setPrice] = useState([
    {
      title: '按折扣定价',
      describe: '市场价x折扣(百分比)=最终价格',
      isEdit: true,
      id: 1,
    },
    {
      title: '按一口价定价',
      describe: '直接定一个固定价格',
      isEdit: false,
      id: 2,
    },
    {
      title: '按减金额定价',
      describe: '市场价-优惠金额=最终价格',
      isEdit: false,
      id: 3,
    },
  ]);
  const [priceValue, setPriceValue] = useState<number | null>(null);
  const [goodsNumber, setGoodsNumber] = useState<number | null>(null);

  const selectedPricing = (val: string) => {
    const priceIsEdit = pricing.map((item) => ({
      ...item,
      isEdit: item.title === val,
    }));
    setPrice(priceIsEdit);
  };

  useEffect(() => {
    if (visible) {
      setPrice(
        pricing.map((item) => ({
          ...item,
          isEdit: item.id === type,
        }))
      );
      setPriceValue(null);
      setGoodsNumber(null);
    }
  }, [visible]); //eslint-disable-line

  let batchSet = null;
  if (type === 1) {
    batchSet = (
      <div className={styles.card}>
        <div className={styles.title}>折扣</div>
        <InputNumber
          // @ts-ignore
          value={priceValue}
          min={0}
          max={100}
          type="number"
          keyboard={false}
          controls={false}
          bordered={false}
          placeholder="请输入"
          addonAfter={<span className={styles.icon}>%</span>}
          // @ts-ignore
          onChange={setPriceValue}
        />
      </div>
    );
  } else if (type === 2) {
    batchSet = (
      <div className={styles.card}>
        <div className={styles.title}>一口价</div>
        <InputNumber
          // @ts-ignore
          value={priceValue}
          min={0}
          max={999999999}
          type="number"
          controls={false}
          bordered={false}
          placeholder="请输入"
          addonBefore={<span className={styles.icon}>￥</span>}
          onChange={(value) => {
            // @ts-ignore
            setPriceValue(value);
          }}
        />
      </div>
    );
  } else if (type === 3) {
    batchSet = (
      <div className={styles.card}>
        <div className={styles.title}>优惠金额</div>
        <InputNumber
          // @ts-ignore
          value={priceValue}
          min={0}
          max={999999999}
          type="number"
          controls={false}
          bordered={false}
          placeholder="请输入"
          addonBefore={<span className={styles.icon}>￥</span>}
          // @ts-ignore
          onChange={setPriceValue}
        />
      </div>
    );
  }

  return (
    <Drawer
      visible={visible}
      title={title}
      placement="bottom"
      width={375}
      height={height}
      getContainer={false}
      mask={visible}
      closable={title === '批量设置'}
      className={styles.childDrawer}
      onClose={onClose}
      closeIcon={<span className={styles.shutDown}>取消</span>}
      extra={
        title === '批量设置' ? null : (
          <Icon name="close" style={{ fontSize: '20px', color: '#999EB2' }} onClick={onClose} />
        )
      }
      footer={
        <div className={styles.footer}>
          <Button
            type={
              (title === '批量设置' && priceValue) || goodsNumber || title === '商品的定价方式'
                ? 'primary'
                : 'default'
            }
            disabled={
              !((title === '批量设置' && priceValue) || goodsNumber || title === '商品的定价方式')
            }
            onClick={() => {
              if (title === '批量设置') {
                getBatchValue(priceValue, goodsNumber);
                onClose();
                return;
              }
              pricing.forEach((item) => {
                if (item.isEdit) {
                  if (type === item.id) {
                    onClose();
                  } else if (type !== item.id && isEditPrice) {
                    onClose();
                    Modal.confirm({
                      title: '提示',
                      content: '切换定价方式，将清空已设置的单价，确定切换吗？',
                      icon: null,
                      width: 290,
                      zIndex: 9999,
                      centered: true,
                      className: styles.showModal,
                      getContainer: document.querySelector('.adjust-drawer') as HTMLElement,
                      onOk() {
                        onClose();
                        getPricing(item.id, item.title);
                        // getBatchValue(0, goodsNumber);
                      },
                    });
                  } else {
                    getPricing(item.id, item.title);
                    onClose();
                  }
                }
              });
            }}
          >
            确定
          </Button>
        </div>
      }
    >
      {title === '批量设置' ? (
        <>
          {batchSet}
          <div className={styles.card}>
            <div className={styles.title}>数量</div>
            <Stepper
              value={goodsNumber || 0}
              allowEmpty
              min={0}
              max={999999999}
              onChange={(value) => {
                setGoodsNumber(value);
              }}
            />
          </div>
        </>
      ) : (
        <>
          {pricing.map((item) => (
            <div
              className={item.isEdit ? styles.priceIsEdit : styles.pricingCard}
              role="button"
              tabIndex={0}
              onClick={() => {
                selectedPricing(item.title);
              }}
              key={item.id}
            >
              <div className={styles.header}>
                <div className={styles.title}>{item.title}</div>
                {/* {item.isEdit ? <Icon name="tick" className={styles.selectedIcon} /> : null} */}
              </div>
              <div className={styles.describe}>{item.describe}</div>
            </div>
          ))}
        </>
      )}
    </Drawer>
  );
}

ChildDrawer.defaultProps = {
  height: 292,
};

export default ChildDrawer;
