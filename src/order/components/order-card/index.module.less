@import 'styles/mixins/mixins';

.popover {
  margin: 5px;
  padding-bottom: 0;
  cursor: pointer;

  :global {
    .ant-popover-arrow {
      display: none;
    }
  }
}

.card {
  margin-bottom: 20px;
  padding: 20px 20px 24px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.orderHeader {
  margin-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;

  .orderTitle,
  .productionTitle {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .orderTitle {
    margin-bottom: 8px;
  }

  .productionTitle {
    padding-bottom: 16px;
  }

  .companyName {
    max-width: 80%;
    .text-overflow();
  }

  .productionCompany {
    color: #132948;
    font-size: 16px;
    font-weight: 500;
    width: 80%;
    .text-overflow();
  }

  .name {
    font-size: 16px;
    display: flex;
    width: 65%;
  }

  .icon {
    font-size: 10px;
    min-width: 60px;
    height: 20px;
    line-height: 20px;
    margin-left: 8px;
    padding: 0 2px;
    border-radius: 2px;
    text-align: center;
  }

  .state {
    color: #ea1c26;
    // width: 125px;
    font-size: 12px;
    text-align: right;
    // .text-overflow();
  }

  .orderSerial {
    color: #888b98;
    font-size: 12px;
    display: flex;
    margin-bottom: 16px;
    justify-content: space-between;
  }
}

.goodsCard {
  margin-bottom: 16px;
}

.text {
  color: #888b98;
  font-size: 12px;
  margin-top: 14px;
  cursor: pointer;
}

.amount {
  display: flex;
  margin-top: 40px;
  justify-content: flex-end;
  align-items: baseline;

  .money {
    color: black;
    font-size: 24px;
  }
}

.hiddenBtn {
  text-align: center;

  .item {
    padding: 8px;
    cursor: pointer;

    &:hover {
      border-radius: 10px;
      background: rgb(217 238 255 / 30%);
    }
  }
}

.footer {
  margin-top: 24px;

  :global {
    // .ant-btn > span {
    //   width: 56px;
    //   white-space: nowrap;
    //   text-overflow: ellipsis;
    //   overflow: hidden;
    // }

    .ant-btn {
      min-width: auto !important;
    }
  }

  .btn {
    display: flex;
    width: 55%;
    justify-content: flex-end;
    align-items: center;
  }

  .primaryBtn {
    line-height: 20px;
    margin-left: 15px;
    float: right;
  }

  .font {
    color: #999eb2;
    font-size: 24px;
    cursor: pointer;
  }

  .cardBottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    // margin-top: 24px;
  }

  .cardSingle,
  .productionCardSingle {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    // margin-top: 24px;
  }

  .productionCardSingle {
    margin-top: 40px;
  }
}

.productionBtn {
  display: flex;
  margin-top: 24px;
  justify-content: flex-end;
  align-items: center;

  .btn {
    margin-left: 16px;
  }
}

.copy {
  margin-left: 4px;
  cursor: pointer;
}

@media screen and (max-width: 1300px) {
  .companyName {
    max-width: 40% !important;
  }
}
