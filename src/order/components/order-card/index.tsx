import { But<PERSON>, Popover, message } from 'antd';
import { <PERSON>E<PERSON>, PropsWithChildren, useEffect, useState } from 'react';
import copy from 'copy-to-clipboard';
import { Icon, Price } from '@/components';
import {
  CanOptButtonList,
  ProductVOList,
  OrdCalcTimeDiffVOResult,
} from '@/apis/order/get-order-sales';
import { ProductionDetailList } from '@/apis/order/get-order-production';
import { BuyProductVOList } from '@/apis/order';
import { createUuid } from '@/utils/utils';
import GoodsCard from '../goods-card';
import GoodsService from '../goods-service';
import styles from './index.module.less';

interface orderCardProps {
  orderType?: 'sales' | 'production' | 'procurement';
  // eslint-disable-next-line no-unused-vars
  onButton: (code: number) => void;
  companyName?: string;
  orderStatusName?: string;
  buyerCompanyName?: string;
  orderNumber?: string;
  isGroup?: boolean;
  productVOList: ProductVOList[] | BuyProductVOList[] | ProductionDetailList[];
  orderPriceAmount?: string;
  canOptButtonList: CanOptButtonList[];
  orderFuncTypeName?: string;
  orderNumberShow?: boolean;
  onClick?: () => void;
  ordCalcTimeDiffVO?: OrdCalcTimeDiffVOResult;
}

function OrderCard({
  orderType,
  onButton,
  companyName,
  orderStatusName,
  buyerCompanyName,
  orderNumber,
  orderNumberShow,
  productVOList,
  orderPriceAmount,
  canOptButtonList,
  orderFuncTypeName,
  isGroup,
  onClick,
  ordCalcTimeDiffVO,
}: PropsWithChildren<orderCardProps>) {
  const [hiddenBtn, setHiddenBtn] = useState<CanOptButtonList[]>([]);
  const [showBtn, setShowBtn] = useState<CanOptButtonList[]>([]);
  const [productShowList, setProductShowList] = useState(false);

  const orderNameColor = () => {
    switch (orderFuncTypeName) {
      case '线上商城':
        return {
          background: '#D9EEFF',
          color: '#008CFF',
        };
      case '商城补录':
        return {
          background: '#D0EFE2',
          color: '#05D380',
        };
      case '补录退货':
        return {
          background: '#D0EFE2',
          color: '#05D380',
        };
      case '分销订单':
        return {
          background: '#FEF3DA',
          color: '#F9AE08',
        };
      default:
        return {
          background: '#FCDDDF',
          color: '#EA1C26',
        };
    }
  };

  const onBtn = (e: MouseEvent<HTMLDivElement>, code: number) => {
    e.stopPropagation();
    onButton(code);
  };

  const onCopy = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    copy(orderNumber || '');
    message.success('复制成功');
  };

  useEffect(() => {
    const hideBtns: CanOptButtonList[] = [];
    const showBtns: CanOptButtonList[] = [];
    const codes = [1301, 1501, 1601, 1304, 1303, 1900, 1505, 1008, 1709, 1800, 1311, 1101, 1102];
    canOptButtonList.forEach((item) => {
      if (codes.includes(item.actionCode)) {
        hideBtns.push(item);
      } else {
        showBtns.push(item);
      }
    });
    if (showBtns.length > 1) {
      hideBtns.concat(showBtns.splice(2));
    }
    setHiddenBtn(hideBtns);
    setShowBtn(showBtns);
  }, [canOptButtonList]);

  return (
    <div
      className={styles.card}
      role="button"
      tabIndex={0}
      onClick={() => {
        if (onClick) onClick();
      }}
    >
      <div className={styles.orderHeader}>
        {orderType === 'production' ? (
          <div className={styles.productionTitle}>
            <div className={styles.productionCompany}>{companyName}</div>
            <div className={styles.state}>{orderStatusName}</div>
          </div>
        ) : (
          <div className={styles.orderTitle}>
            <div className={styles.name}>
              <div className={styles.companyName}>{buyerCompanyName}</div>
              <div
                className={styles.icon}
                style={{ background: orderNameColor().background, color: orderNameColor().color }}
              >
                {orderFuncTypeName}
              </div>
            </div>
            <div className={styles.state}>{orderStatusName}</div>
          </div>
        )}
        {!orderNumberShow ? (
          <div className={styles.orderSerial}>
            <span>
              订单编号：{orderNumber}
              <Icon name="copy" className={styles.copy} onClick={onCopy} />
            </span>
            <span>
              {ordCalcTimeDiffVO ? <span>还剩{ordCalcTimeDiffVO.formateStr}</span> : null}
            </span>
          </div>
        ) : null}
      </div>
      {productVOList.slice(0, !productShowList ? 3 : 99).map((item) => (
        <div className={styles.goodsCard}>
          <GoodsCard
            img={item.productMainPic}
            orderType={orderType}
            productPrice={item.productPrice}
            number={item.number}
            productUnit={item.productUnit}
            productName={item.productName}
            productSpecificationList={item.productSpecificationList}
            key={createUuid()}
            isGift={item.isGift}
          />
          {item.orderServiceProductParams && item.orderServiceProductParams.length > 0 && (
            <GoodsService services={item.orderServiceProductParams} className="mt-3" />
          )}
        </div>
      ))}
      {productVOList.length > 3 ? (
        <div
          className={styles.text}
          role="button"
          tabIndex={0}
          onClick={(e) => {
            if (productShowList) {
              setProductShowList(false);
            } else if (!orderNumberShow) {
              onBtn(e, 0);
            } else {
              setProductShowList(true);
            }
          }}
        >
          {!orderNumberShow ? (
            <span>
              全部{productVOList.length}种商品 <Icon name="right" />
            </span>
          ) : (
            <div>
              {!productShowList ? (
                <span>
                  显示剩余{productVOList.length - 3}种商品 <Icon name="right" />
                </span>
              ) : (
                <span>
                  收起 <Icon name="right" />
                </span>
              )}
            </div>
          )}
        </div>
      ) : null}
      {orderType === 'production' ? (
        <div className={styles.footer}>
          <div className={styles.productionCardSingle}>
            <div className={canOptButtonList.length >= 2 ? styles.btn : ''}>
              {showBtn.map((item, index) => (
                <Button
                  type={
                    (showBtn.length > 1 && index) || showBtn.length === 1 ? 'primary' : 'default'
                  }
                  className={styles.primaryBtn}
                  onClick={(e) => {
                    // @ts-ignore
                    onBtn(e, item.actionCode);
                  }}
                  key={item.batchNo}
                >
                  {item.actionName}
                </Button>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className={styles.footer}>
          {showBtn.length ? (
            <div
              className={styles.amount}
              style={canOptButtonList.length ? { marginBottom: '24px' } : {}}
            >
              <span>共{productVOList.length}种货品 总金额：￥</span>
              <Price
                value={orderPriceAmount as string}
                symbol=""
                format
                color="#040919"
                className={styles.money}
                separate
              />
            </div>
          ) : null}
          <div className={hiddenBtn?.length > 0 ? styles.cardBottom : styles.cardSingle}>
            {hiddenBtn?.length > 0 && isGroup ? (
              <Popover
                overlayClassName={styles.popover}
                content={
                  <div className={styles.hiddenBtn}>
                    {hiddenBtn.map((item) => (
                      <div
                        className={styles.item}
                        role="button"
                        tabIndex={0}
                        onClick={(e) => {
                          onBtn(e, item.actionCode);
                        }}
                        key={createUuid()}
                      >
                        {item.actionName}
                      </div>
                    ))}
                  </div>
                }
              >
                <Icon
                  name="zu13366"
                  className={styles.font}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              </Popover>
            ) : null}
            {showBtn.length && isGroup ? (
              <div className={canOptButtonList.length >= 2 ? styles.btn : ''}>
                {showBtn.map((item, index) => (
                  <Button
                    type={
                      (showBtn.length > 1 && index) || showBtn.length === 1 ? 'primary' : 'default'
                    }
                    className={styles.primaryBtn}
                    onClick={(e) => {
                      // @ts-ignore
                      onBtn(e, item.actionCode);
                    }}
                    key={createUuid()}
                  >
                    {item.actionName}
                  </Button>
                ))}
              </div>
            ) : (
              <div className={styles.amount} style={{ margin: '0' }}>
                <span>共{productVOList.length}种货品 总金额：￥</span>
                <Price
                  value={orderPriceAmount as string}
                  symbol=""
                  format
                  color="#040919"
                  className={styles.money}
                  separate
                />
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

OrderCard.defaultProps = {
  ordCalcTimeDiffVO: null,
  orderType: '',
  orderNumberShow: false,
  isGroup: true,
  companyName: '',
  orderStatusName: '',
  buyerCompanyName: '',
  orderNumber: '',
  orderPriceAmount: '',
  orderFuncTypeName: '',
  onClick: undefined,
};

export default OrderCard;
