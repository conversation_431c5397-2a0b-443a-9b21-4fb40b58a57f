import { Context, Empty } from '@/components';
import { useSearchParams } from 'react-router-dom';
import { Col, Divider, Row, Spin } from 'antd';
import { useEffect, useRef, useState, useMemo } from 'react';
import { getOrderMallList, getDistributionCompanyInfo, MallOrderParams } from '@/apis/order';
import { RecordsResult } from '@/apis/order/get-order-sales';
import { createUuid } from '@/utils/utils';
import InfiniteScroll from 'react-infinite-scroll-component';
import { sessions } from '@/store';
import OrderCard from '../../components/order-card';
import styles from '../list/index.module.less';

function SalesOrders() {
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<RecordsResult[]>([]);
  const [isFilterActive, setIsFilterActive] = useState(false);
  const [load, setLoad] = useState(false);
  const cardNum = useState([1, 2]);
  const [filterList] = useState([
    { label: '全部', value: 0 },
    { label: '待处理', value: 1 },
    { label: '进行中', value: 2 },
    { label: '已完成', value: 80 },
    { label: '已关闭', value: 90 },
  ]);

  const params = useRef({
    batchNo: 0,
    key: '',
  });
  const distributionId = Number(searchParams.get('distribution'));
  const [pageCount, setPageCount] = useState(1);
  const pageList = {
    pageSize: 6,
    pageNo: 1,
    gmallId: Number(searchParams.get('mallId')),
    key: searchParams.get('key') || '',
    orderStatus: Number(searchParams.get('orderStatus')) || 0,
    tenantId: searchParams.get('tenantId') || '',
  };
  const [page, setPage] = useState<MallOrderParams>(pageList);

  const getSalesList = (listItem: MallOrderParams) => {
    setLoading(true);
    const formData: MallOrderParams = listItem;
    getOrderMallList(formData)
      .then((res) => {
        setPageCount(Math.ceil(res.total / res.size));
        setPage({ ...formData, pageNo: formData.pageNo + 1 });
        setList(res.records);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getOrderList = () => {
    setLoad(true);
    getOrderMallList(page).then((res) => {
      setLoad(false);
      setPageCount(Math.ceil(res.total / res.size));
      setPage({ ...page, pageNo: page.pageNo + 1 });
      setList(list.concat(res.records));
    });
  };

  const pointList = (id: number) => {
    const listItem = { ...pageList, orderStatus: id, key: params.current.key };
    setPage(listItem);
    getSalesList(listItem);
  };

  const loadMore = () => {
    getOrderList();
  };

  const searchList = (key: string) => {
    const listItem = {
      ...page,
      pageSize: 6,
      pageNo: 1,
      key,
    };
    getSalesList(listItem);
  };

  const initPageData = (orderItem: MallOrderParams) => {
    setLoading(true);
    getOrderMallList(orderItem)
      .then((item) => {
        setPage({ ...orderItem, pageNo: 2 });
        setPageCount(Math.ceil(item.total / item.size));
        setList(item.records);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const description = useMemo(() => {
    if (page.key || page.orderStatus) {
      return '换个筛选条件再试试';
    }
    return null;
  }, [page]);

  useEffect(() => {
    sessions.select('orderCode');
    const orderItem: MallOrderParams = {
      ...page,
      orderStatus: Number(searchParams.get('orderStatus')),
    };
    if (distributionId) {
      getDistributionCompanyInfo({ id: distributionId })
        .then(() => {})
        .finally(() => {
          initPageData(orderItem);
          setPage({ ...page, pageNo: page.pageNo + 1 });
        });
      return;
    }
    initPageData(orderItem);
    setPage({ ...page, pageNo: page.pageNo + 1 });
  }, []); // eslint-disable-line

  return (
    <Context
      loading={loading}
      container
      theme={list.length >= 1 ? null : 'default'}
      id="content"
      head={
        <Context.Head
          searchTooltipTitle="搜索"
          quickFilter={
            <Context.QuickFilter
              label="显示"
              value={filterList.filter((f) => f.value === page.orderStatus)[0].label}
              options={filterList}
              onChange={(e) => {
                setIsFilterActive(false);
                pointList(e);
              }}
              style={{ minWidth: '80px' }}
            />
          }
          isFilterActive={isFilterActive}
          onSearch={(e) => {
            params.current.key = e;
            searchList(e);
          }}
          //   onFilter={() => {
          //     if (!orderState.length) return;
          //     setShowFilter(true);
          //   }}
          title={[
            {
              title: '网站管理',
              to: `/website/manage?webSiteId=${searchParams.get(
                'webSiteId'
              )}&tenantId=${searchParams.get('tenantId')}`,
            },
            '订单',
          ]}
        />
      }
    >
      {list.length ? (
        <div className={styles.soldList} id="soldList">
          <InfiniteScroll
            dataLength={list.length}
            hasMore={page.pageNo <= pageCount}
            loader={
              load ? (
                <div className="text-center">
                  <Spin tip="加载中..." />
                </div>
              ) : undefined
            }
            endMessage={
              <div className={styles.divider}>
                <Divider plain>
                  <span className={styles.endMessage}>没有更多了</span>
                </Divider>
              </div>
            }
            next={loadMore}
            scrollableTarget="soldList"
          >
            <Row gutter={20}>
              {cardNum.map((num, numIndex) => (
                <Col md={12} xs={24} key={createUuid()}>
                  {list
                    ?.filter((f, index) => {
                      if (numIndex === 1 ? index % 2 : !(index % 2)) {
                        return { ...f };
                      }
                      return null;
                    })
                    .map((item) => (
                      <OrderCard
                        orderType="sales"
                        buyerCompanyName={
                          searchParams.get('region') === '1'
                            ? item.buyerCompanyName
                            : item.sellerCompanyName
                        }
                        orderNumber={item.orderNumber}
                        orderStatusName={item.orderStatusName}
                        productVOList={item.productVOList}
                        orderPriceAmount={item.orderPriceAmount}
                        canOptButtonList={item.canOptButtonList}
                        orderFuncTypeName={item.orderFuncTypeName}
                        onButton={() => {}}
                        onClick={() => {}}
                        isGroup={false}
                        key={item.orderNo}
                      />
                    ))}
                </Col>
              ))}
            </Row>
          </InfiniteScroll>
        </div>
      ) : (
        <div>
          {!loading ? (
            <Empty
              type="order"
              message="暂无数据"
              description={description}
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
              }}
              imageStyle={{
                width: '80%',
                height: '100%',
              }}
            />
          ) : null}
        </div>
      )}
    </Context>
  );
}

export default SalesOrders;
