import { MouseEvent, useMemo, useReducer } from 'react';
import { CheckboxProps, message, Spin } from 'antd';
import { Context, dialog, Empty } from '@/components';
import { useMemoizedFn, useMount, useUnmount } from 'ahooks';
import { deleteGoodsForCart, getCurrentUserCart, GetCurrentUserCartParams } from '@/apis';
import BigNumber from 'big.js';
import union from 'lodash/union';
import difference from 'lodash/difference';
import { formatPrice } from '@/utils/utils';
import isNumber from 'lodash/isNumber';
import { layout, sessions } from '@/store';
import CartContext, { type CartContextValue, reducer } from '../../context/cart';
import CartHead from '../../components/cart-head';
import CartFooter from '../../containers/cart-footer';
import Group from '../../containers/cart-list';
import styles from './index.module.less';

function Cart() {
  const [{ loading, groups, checks }, dispatch] = useReducer<typeof reducer>(reducer, {
    loading: true,
    groups: [],
    checks: [],
  });
  const [count, totalAmount] = useMemo(() => {
    let tmpCount = 0;
    let tmpTotalAmount = new BigNumber(0);
    groups.forEach((group) => {
      const list = group.cartProductVOList;
      list.forEach((item) => {
        if (checks.includes(item.cartNo)) {
          tmpTotalAmount = tmpTotalAmount.plus(item.productPriceAmount);
        }
        if (item.productStatus === 50) {
          tmpCount += 1;
        }
      });
    });
    return [tmpCount, formatPrice(tmpTotalAmount.toFixed(2))];
  }, [checks, groups]);
  const toggleChecks = useMemoizedFn((cartNoList: number[], isAdd = true) => {
    dispatch({
      type: 'setCheck',
      payload: isAdd ? union(checks, cartNoList) : difference(checks, cartNoList),
    });
  });
  const removeGoodsList = useMemoizedFn((e: MouseEvent | number) => {
    const isSingle = isNumber(e);
    const cartNoList = isSingle ? [e] : checks;
    if (cartNoList.length === 0) {
      message.warn('请先选择商品');
      return;
    }
    const instance = dialog({
      content: `是否删除${isSingle ? '该' : '选中'}商品？`,
      title: '提示',
      width: 320,
      centered: true,
      onConfirm: (close) => {
        instance.update({ confirmLoading: true });
        // 获取商品包的全部购物车编码
        groups.forEach((group) => {
          group.cartProductVOList.forEach((item) => {
            if (item.productType === 2 && cartNoList.includes(item.cartNo)) {
              item.packageCartProductVOS?.forEach((goods) => {
                if (!cartNoList.includes(goods.cartNo)) {
                  cartNoList.push(goods.cartNo);
                }
              });
            }
          });
        });
        deleteGoodsForCart(cartNoList)
          .then(() => {
            message.success('删除成功');
            dispatch({ type: 'delete', payload: cartNoList });
            close();
          })
          .catch(() => {
            instance.update({ confirmLoading: true });
          });
      },
    });
  });
  const onChangeCheck: CheckboxProps['onChange'] = useMemoizedFn((e) => {
    const { target } = e;
    dispatch({
      type: 'setCheck',
      payload: target.checked ? [...checks, target.value] : difference(checks, [target.value]),
    });
  });
  const getCart = useMemoizedFn((values?: number[]) => {
    const params: GetCurrentUserCartParams = {};
    if (values) {
      [params.provinceId, params.cityId, params.districtId] = values;
    }
    dispatch({ type: 'loading' });
    getCurrentUserCart(params)
      .then((res) => {
        dispatch({ type: 'set', payload: res?.cartItemVOList || [] });
      })
      .catch(() => {
        dispatch({ type: 'set', payload: [] });
      });
  });
  const value: CartContextValue = useMemo(
    () => ({ checks, dispatch, getCart, toggleChecks, removeGoodsList, onChangeCheck }),
    [checks, getCart, onChangeCheck, removeGoodsList, toggleChecks]
  );

  useMount(() => {
    getCart();
    layout.setNav('cart');
    sessions.select('cart');
  });

  useUnmount(() => {
    layout.setNav('home');
  });

  let content;
  if (!loading) {
    if (!groups || groups.length === 0) {
      content = (
        <div className={styles.empty}>
          <Empty type="cart" />
        </div>
      );
    } else {
      content = groups.map((group, index) => (
        <Group key={group.shopId} group={group} index={index} />
      ));
    }
  }

  return (
    <Context theme={null} head={<CartHead />} className={styles.view}>
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <CartContext.Provider value={value}>
          <div className={styles.wrap}>
            <div className={styles.cart}>
              <div className={styles.cartHead}>
                <div className={styles.td1}>&nbsp;</div>
                <div className={styles.td2}>商品（{count}）</div>
                <div className={styles.td3}>单价</div>
                <div className={styles.td4}>数量</div>
                <div className={styles.td5}>金额</div>
                <div className={styles.td6}>操作</div>
              </div>
              <div className={styles.body}>{content}</div>
            </div>
            <CartFooter
              count={count}
              totalAmount={totalAmount}
              groups={groups}
              onRemoveChecks={removeGoodsList}
            />
          </div>
        </CartContext.Provider>
      </Spin>
    </Context>
  );
}

Cart.displayName = 'CartPage';

export default Cart;
