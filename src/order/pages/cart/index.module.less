@import '../../containers/cart-list/common.less';

.view {
  border-radius: 0;
}

.spin {
  &:global {
    &,
    & > .ant-spin-container {
      height: 100%;
    }
  }
}

.wrap {
  display: flex;
  height: 100%;
  flex-flow: column;
  overflow: hidden;
}

.body {
  .flex-row();
}

.cart {
  composes: body;
  display: flex;
  flex-flow: column;
  width: 100%;
  height: 100%;
  overflow: auto hidden;

  > .body {
    min-width: @row-min-width;
    overflow: hidden auto;
  }
}

.cartHead {
  color: @text-color-secondary;
  line-height: 16px;
  padding-bottom: 10px;
  composes: tr;

  .td1 {
    border-radius: @border-radius-base 0 0 @border-radius-base;
  }

  .td6 {
    border-radius: 0 @border-radius-base @border-radius-base 0;
  }

  > div {
    background-color: @white;
  }
}

.footer {
  border-radius: 0 0 10px 10px;
  background-color: @white;

  :global {
    .ant-btn-ghost {
      border: 1px solid @text-color-secondary;
    }
  }
}

.location {
  color: @text-color-secondary;
  font-size: @font-size-lg;
}

.footerText {
  font-size: @font-size-lg;
  line-height: 22px;
}

.price {
  color: @red;
  display: inline-block;
}

.footerTooltip {
  color: @text-color-secondary;
  font-size: @font-size-sm;
  display: inline-block;
  margin-left: 20px;
}

.empty {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  background-color: @white;
}
