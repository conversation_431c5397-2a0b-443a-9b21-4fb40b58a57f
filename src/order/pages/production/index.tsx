import { useCallback, useReducer, useRef, useState } from 'react';
import { useMount } from 'ahooks';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Spin, Divider, Row, Col, Modal, message } from 'antd';
import { getProductionOrderList, postOrderFinishProduce, printProductionOrder } from '@/apis';
import { Context, Empty } from '@/components';
import { usePermission } from '@/hooks';
import { ProductionOrderItem } from '@/apis/order/get-order-production';
import { useNavigate, useSearchParams } from 'react-router-dom';
import checkPerm from '../../utils/permission';
import OrderCard from '../../components/order-card';
import FilterDrawer from './filter-drawer';
import styles from './index.module.less';

interface StateType {
  list: ProductionOrderItem[];
  leftList: ProductionOrderItem[];
  rightList: ProductionOrderItem[];
}

type ActionType = {
  type: 'setState';
  payload: Partial<StateType>;
};

const initialState: StateType = {
  list: [],
  leftList: [],
  rightList: [],
};

const reducer = (state: StateType, action: ActionType): StateType => {
  switch (action.type) {
    case 'setState':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

const cardHeight = 108;
const productHeight = 124;
const buttonHeight = 78;
const titles = [{ title: '订单管理', to: '/indent' }, '生产订单'];
const options = [
  { label: '全部', value: 0 },
  { label: '待生产', value: 1 },
  { label: '已完成', value: 2 },
];

function ProductionOrder() {
  const { confirm } = Modal;
  const navigate = useNavigate();
  const [visibleFilterDrawer, setVisibleFilterDrawer] = useState(false);
  const [loading, setLoading] = useState(false);
  const [spinLoading, setSpinLoading] = useState(false);
  const [isFilterActive, setIsFilterActive] = useState(false);
  const [state, dispatch] = useReducer(reducer, initialState);
  const scrollEl = useRef(null as unknown as HTMLDivElement);
  const [searchParams] = useSearchParams();

  const apiParams = useRef({
    key: searchParams.get('key') || '',
    orderStatus: Number(searchParams.get('orderStatus')) || 0,
    startDate: Number(searchParams.get('startTime')) || 0,
    endDate: Number(searchParams.get('endTime')) || 0,
    pageNo: 1,
    pageSize: 6,
    totalPages: 1,
  });

  const getList = useCallback((nowList: ProductionOrderItem[] = []) => {
    const data = apiParams.current;
    const formData: Record<string, string | number> = {
      pageNo: data.pageNo,
      pageSize: data.pageSize,
    };
    if (data.key) formData.key = data.key;
    if (data.orderStatus) formData.orderStatus = data.orderStatus;
    if (data.startDate) formData.startDate = data.startDate;
    if (data.endDate) formData.endDate = data.endDate;
    setLoading(true);
    setSpinLoading(true);
    getProductionOrderList(formData)
      .then((res) => {
        const list = [...nowList, ...res.list];
        const leftList: ProductionOrderItem[] = [];
        const rightList: ProductionOrderItem[] = [];
        let leftListHeight = 0;
        let rightListHeight = 0;
        list.forEach((item: ProductionOrderItem) => {
          if (leftListHeight <= rightListHeight) {
            leftList.push(item);
            leftListHeight +=
              cardHeight +
              productHeight * item.productionDetailList.length +
              buttonHeight * +!!item.canOptButtonList.length;
          } else {
            rightList.push(item);
            rightListHeight +=
              cardHeight +
              productHeight * item.productionDetailList.length +
              buttonHeight * +!!item.canOptButtonList.length;
          }
        });
        dispatch({
          type: 'setState',
          payload: { list, leftList, rightList },
        });
        apiParams.current.totalPages = Math.ceil(res.pagination.count / apiParams.current.pageSize);
      })
      .finally(() => {
        setLoading(false);
        setSpinLoading(false);
        if (scrollEl.current) scrollEl.current.scrollTop = 0;
      });
  }, []);

  const showModal = (name: string, productOrderId: number) => {
    confirm({
      title: '提示',
      content: `是否${name}`,
      icon: null,
      centered: true,
      onOk() {
        postOrderFinishProduce({ productOrderId }).then(() => {
          message.success('生产完成');
          getList();
        });
      },
      onCancel() {},
    });
  };

  const onOrderButton = (code: number, productOrderId: number) => {
    if (!checkPerm(-1, 0, 0, code)) return;
    if (code === 2021) {
      showModal('生产完成', productOrderId);
    } else if (code === 2022) {
      printProductionOrder({ productOrderId }).then((res) => {
        const printUrl = (window.webkitURL || window.URL).createObjectURL(res);
        window.open(printUrl, '_blank');
      });
    }
  };

  const toDetail = (productOrderId: number) => {
    navigate(`/order/production/detail?productOrderId=${productOrderId}`);
  };

  const searchPerm = usePermission('R_001_018');

  const filterPerm = usePermission('R_001_019', () => {
    setVisibleFilterDrawer(true);
  });

  useMount(() => {
    getList();
  });

  return (
    <Context
      permission={{ code: 'R_001_007', newLogic: true }}
      theme={state.leftList.length >= 1 ? null : 'default'}
      head={
        <Context.Head
          searchTooltipTitle="搜索"
          quickFilter={
            <Context.QuickFilter
              defaultValue={0}
              label="显示"
              options={options}
              onChange={(e) => {
                apiParams.current = {
                  ...apiParams.current,
                  orderStatus: e,
                  pageNo: 1,
                };
                getList();
                setIsFilterActive(false);
              }}
              style={{ minWidth: '80px' }}
            />
          }
          isFilterActive={isFilterActive}
          onSearchClick={searchPerm}
          onSearch={(e) => {
            apiParams.current = {
              ...apiParams.current,
              key: e,
              pageNo: 1,
            };
            getList();
          }}
          onFilter={filterPerm}
          placeholder="搜索订单"
          title={titles}
        />
      }
    >
      {state.list.length ? (
        <div id="scrollableDiv" ref={scrollEl} className={styles.scrollableDiv}>
          <InfiniteScroll
            dataLength={state.list.length}
            next={() => {
              if (apiParams.current.pageNo >= apiParams.current.totalPages || loading) return;
              apiParams.current = {
                ...apiParams.current,
                pageNo: apiParams.current.pageNo + 1,
              };
              getList(state.list);
            }}
            hasMore={apiParams.current.pageNo < apiParams.current.totalPages}
            loader={
              !spinLoading ? (
                <div className="text-center">
                  <Spin tip="加载中..." />
                </div>
              ) : undefined
            }
            endMessage={
              <div className={styles.divider}>
                <Divider plain>
                  <span className={styles.endMessage}>没有更多了</span>
                </Divider>
              </div>
            }
            scrollableTarget="scrollableDiv"
          >
            <Spin tip="Loading..." spinning={spinLoading}>
              <Row gutter={[20, 0]}>
                <Col span={12}>
                  {state.leftList.map((item) => (
                    <OrderCard
                      key={item.productOrderId}
                      orderType="production"
                      orderNumberShow
                      companyName={item.companyName}
                      productVOList={item.productionDetailList}
                      canOptButtonList={item.canOptButtonList}
                      orderStatusName={item.orderStatusName}
                      onButton={(code) => {
                        onOrderButton(code, item.productOrderId);
                      }}
                      onClick={() => {
                        toDetail(item.productOrderId);
                      }}
                    />
                  ))}
                </Col>
                <Col span={12}>
                  {state.rightList.map((item) => (
                    <OrderCard
                      key={item.productOrderId}
                      orderType="production"
                      orderNumberShow
                      companyName={item.companyName}
                      productVOList={item.productionDetailList}
                      canOptButtonList={item.canOptButtonList}
                      orderStatusName={item.orderStatusName}
                      onButton={(code) => {
                        onOrderButton(code, item.productOrderId);
                      }}
                      onClick={() => {
                        toDetail(item.productOrderId);
                      }}
                    />
                  ))}
                </Col>
              </Row>
            </Spin>
          </InfiniteScroll>
        </div>
      ) : (
        <div className={styles.emptyContent}>
          {!spinLoading && (
            <Empty
              message="暂无数据"
              description={isFilterActive ? '换个筛选条件在试试' : '没有相关的订单数据'}
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
              }}
              imageStyle={{
                width: '80%',
                height: '100%',
              }}
            />
          )}
        </div>
      )}

      <FilterDrawer
        visible={visibleFilterDrawer}
        startDate={apiParams.current.startDate}
        endDate={apiParams.current.endDate}
        onOk={(startDate, endDate) => {
          apiParams.current = {
            ...apiParams.current,
            startDate,
            endDate,
            pageNo: 1,
          };
          if (startDate || endDate) {
            setIsFilterActive(true);
          } else {
            setIsFilterActive(false);
          }
          getList();
          setVisibleFilterDrawer(false);
        }}
        onClose={() => {
          setVisibleFilterDrawer(false);
        }}
      />
    </Context>
  );
}

export default ProductionOrder;
