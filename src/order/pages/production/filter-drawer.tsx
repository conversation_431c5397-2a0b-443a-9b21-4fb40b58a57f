import { useEffect } from 'react';
import { Form } from 'antd';
import type { DrawerProps } from 'antd';
import { Drawer, DatePicker } from '@/components';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import styles from './filter-drawer.module.less';

interface PropsType extends DrawerProps {
  startDate?: number;
  endDate?: number;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  onOk: (startDate: number, endDate: number) => void;
}

function FilterDrawer({ startDate, endDate, onOk, onClose, visible, ...porps }: PropsType) {
  const [form] = Form.useForm();

  const disabledDateStart = (current: Dayjs) =>
    (current && current > dayjs().endOf('day')) ||
    (current &&
      current >
        dayjs(form.getFieldsValue().endDate ? form.getFieldsValue().endDate : '3333-08').endOf(
          'day'
        ));

  const disabledDateEnd = (current: Dayjs) =>
    (current && current > dayjs().endOf('day')) ||
    (current &&
      current <
        dayjs(
          form.getFieldsValue().startDate ? form.getFieldsValue().startDate : '1999-08'
        ).subtract(0, 'days'));

  const onsubmit = () => {
    const values = form.getFieldsValue(true);
    const start = values.startDate ? values.startDate.startOf('day').valueOf() : 0;
    const end = values.endDate ? values.endDate.startOf('day').valueOf() + 86399999 : 0;
    onOk(start, end);
  };

  useEffect(() => {
    if (visible) {
      const start = startDate ? dayjs(startDate) : undefined;
      const end = endDate ? dayjs(endDate) : undefined;
      form.setFieldsValue({
        startDate: start,
        endDate: end,
      });
    }
  }, [endDate, form, startDate, visible]);

  return (
    <Drawer
      title="筛选"
      visible={visible}
      onClose={onsubmit}
      footer={
        <Drawer.Footer
          cancelText="重置"
          onOk={onsubmit}
          onCancel={() => {
            form.setFieldsValue({
              startDate: undefined,
              endDate: undefined,
            });
          }}
        />
      }
      {...porps}
    >
      <div className={styles.card}>
        <div>订单时间</div>
        <div className={styles.time}>
          <Form form={form}>
            <Form.Item name="startDate">
              <DatePicker
                bordered={false}
                placeholder="开始时间"
                disabledDate={disabledDateStart}
              />
            </Form.Item>
            <span className={styles.icon} />
            <Form.Item name="endDate">
              <DatePicker bordered={false} placeholder="结束时间" disabledDate={disabledDateEnd} />
            </Form.Item>
          </Form>
        </div>
      </div>
    </Drawer>
  );
}

FilterDrawer.displayName = 'FilterDrawer';

FilterDrawer.defaultProps = {
  startDate: undefined,
  endDate: undefined,
};

export default FilterDrawer;
