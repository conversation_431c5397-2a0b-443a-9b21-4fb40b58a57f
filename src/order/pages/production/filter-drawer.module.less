.card {
  border-radius: 18px;
  padding: 16px 20px 20px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;
}

.time {
  margin-top: 16px;

  :global {
    .ant-form {
      display: flex;
    }

    .ant-form-item {
      display: inline-block;
      width: 140px !important;
      margin-bottom: 7px;
    }

    .ant-picker-suffix {
      display: none;
    }

    .ant-picker.ant-picker-borderless {
      width: 140px;
      height: 28px;
      border-radius: 10px;
      background: #f3f3f3 !important;
    }
  }

  .icon {
    display: inline-block;
    width: 20px;
    border: 1px solid #d8d8d8;
    vertical-align: middle;
    margin: 19px 4px;
  }
}

.footer {
  display: flex;
  width: 100%;
  margin-bottom: 24px;
  justify-content: space-around;
  align-items: center;

  .btn {
    width: 160px;
    height: 38px;
  }
}
