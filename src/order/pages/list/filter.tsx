import { pointItemResult, SalesOrderParams } from '@/apis/order';
import { DatePicker, Drawer } from '@/components';
import { Button, Form, Tooltip } from 'antd';
import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { Fragment, PropsWithChildren, useEffect, useRef, useState } from 'react';
import { createUuid } from '@/utils/utils';
import { DrawerRefType } from '@/components/drawer/drawer';
import styles from './filter.module.less';

interface FilterProps {
  visible: boolean;
  onClose: () => void;
  // eslint-disable-next-line react/no-unused-prop-types
  orderItem: pointItemResult[];
  page: SalesOrderParams;
  // eslint-disable-next-line no-unused-vars
  searchFilter: (data: SalesOrderParams) => void;
}

function Filter({
  visible,
  onClose,
  orderItem,
  page,
  searchFilter,
}: PropsWithChildren<FilterProps>) {
  const [form] = Form.useForm();
  const drawerRef = useRef<DrawerRefType>(null);
  const [orderState, setOrderState] = useState<pointItemResult[]>([]);

  const selectedState = (data: { name: string }) => {
    const arrStete = orderState.map((item) => {
      if (item.name === data.name) {
        return { ...item, iEdit: !item.iEdit };
      }
      return {
        ...item,
      };
    });
    setOrderState(arrStete);
  };

  const addFilter = () => {
    const filter = form.getFieldsValue();
    const startDate = filter.startDate ? dayjs(filter.startDate).startOf('day').valueOf() : null;
    const endDate = filter.endDate ? dayjs(filter.endDate).startOf('day').valueOf() : null;
    const flow = orderState.filter((f) => f.iEdit);
    const filterList = {
      startDate,
      endDate: endDate ? endDate + 86399999 : null,
      flowSerialNo: flow[0] ? Number(flow[0].serialNo) : null,
      actionPointNos: flow.length ? flow.map((m) => m.actionPointNo) : [],
    };
    // @ts-ignore
    searchFilter({ ...page, ...filterList, pageNo: 1 });
    onClose();
  };

  // (current && current > dayjs().endOf('day')) ||
  const disabledDateStart = (current: Dayjs) =>
    (current && current > dayjs().endOf('day')) ||
    current >
      dayjs(form.getFieldsValue().endDate ? form.getFieldsValue().endDate : '2033-08').endOf('day');

  // (current && current > dayjs().endOf('day')) ||
  const disabledDateEnd = (current: Dayjs) =>
    (current && current > dayjs().endOf('day')) ||
    current <
      dayjs(form.getFieldsValue().startDate ? form.getFieldsValue().startDate : '1999-08').subtract(
        0,
        'days'
      );

  useEffect(() => {
    if (visible) {
      setOrderState(
        orderItem.map((m) => {
          if (page.actionPointNos?.includes(m.actionPointNo)) {
            return {
              ...m,
              iEdit: true,
            };
          }
          return { ...m };
        })
      );
      form.setFieldsValue({
        startDate: page.startDate ? dayjs(page.startDate) : null,
        endDate: page.endDate ? dayjs(page.endDate) : null,
      });
    }
  }, [visible]); // eslint-disable-line
  return (
    <Drawer
      title="筛选"
      ref={drawerRef}
      visible={visible}
      onClose={() => {
        // onClose();
        addFilter();
      }}
      footer={
        <div className={styles.footer}>
          <Button
            className={styles.btn}
            onClick={() => {
              form.resetFields();
              setOrderState(
                orderState.map((m) => ({
                  ...m,
                  iEdit: false,
                }))
              );
            }}
          >
            重置
          </Button>
          <Button type="primary" className={styles.btn} onClick={addFilter}>
            确定
          </Button>
        </div>
      }
    >
      {orderState.length > 0 && (
        <div className={styles.card}>
          <div>订单状态</div>
          <div className={styles.content}>
            {orderState.map((item) => (
              <Fragment key={createUuid()}>
                <Tooltip placement="top" title={item.name}>
                  <div
                    className={classNames(styles.state, item.iEdit ? styles.iEdit : '')}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      selectedState(item);
                    }}
                  >
                    {item.name}
                  </div>
                </Tooltip>
              </Fragment>
            ))}
          </div>
        </div>
      )}
      <div className={styles.timeCard}>
        <div>订单时间</div>
        <div className={styles.time}>
          <Form form={form}>
            <Form.Item name="startDate">
              <DatePicker
                bordered={false}
                placeholder="开始时间"
                disabledDate={disabledDateStart}
              />
            </Form.Item>
            <span className={styles.icon} />
            <Form.Item name="endDate">
              <DatePicker bordered={false} placeholder="结束时间" disabledDate={disabledDateEnd} />
            </Form.Item>
          </Form>
        </div>
      </div>
    </Drawer>
  );
}

export default Filter;
