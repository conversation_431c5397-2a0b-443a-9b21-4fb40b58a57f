.card {
  border-radius: 18px;
  padding: 20px 20px 8px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;
}

.content {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;

  .state {
    width: 92px;
    height: 28px;
    line-height: 28px;
    margin: 0 9px 12px 0;
    padding: 0 10px;
    overflow: hidden;
    border-radius: 10px;
    border: 1px solid #fff;
    background: #f3f3f3;
    text-align: center;
    font-family: PingFangSC-Regular;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: pointer;
  }

  .iEdit {
    border: 1px solid #008cff;
    background: #d9eeff;
    color: #008cff;
  }
}

.timeCard {
  margin-top: 24px;
  border-radius: 18px;
  padding: 20px 20px 11px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;
}

.time {
  margin-top: 16px;

  :global {
    .ant-form {
      display: flex;
    }

    .ant-form-item {
      display: inline-block;
      width: 140px !important;
      margin-bottom: 7px;
    }

    .ant-picker-suffix {
      display: none;
    }

    .ant-picker.ant-picker-borderless {
      width: 140px;
      height: 28px;
      border-radius: 10px;
      background: #f3f3f3 !important;
    }
  }

  .icon {
    display: inline-block;
    width: 20px;
    border: 1px solid #d8d8d8;
    vertical-align: middle;
    margin: 16px 4px 21px;
  }
}

.footer {
  display: flex;
  width: 100%;
  padding: 20px;
  justify-content: space-around;
  align-items: center;

  .btn {
    width: 160px;
    height: 38px;
  }
}
