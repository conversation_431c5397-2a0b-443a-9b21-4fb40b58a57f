import { Context, Empty } from '@/components';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { But<PERSON>, Col, Divider, Modal, Row, Spin } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import {
  getOrderPointList,
  getPurchaseList,
  getSalesOrderList,
  pointItemResult,
  getDistributionCompanyInfo,
  getOrderRelateList,
  deliveryNotice,
  checkAcceptnotice,
  orderBuyList,
  orderBackList,
  getNycbngPerm,
} from '@/apis';
import { RecordsResult, SalesOrderParams } from '@/apis/order/get-order-sales';
import { testPerm } from '@/utils/permission';
import { createUuid } from '@/utils/utils';
import InfiniteScroll from 'react-infinite-scroll-component';
import { sessions } from '@/store';
import { usePermission } from '@/hooks';
import { useMemoizedFn } from 'ahooks';
import { stockOrderCreate } from '@@/order/containers/order-create/stock-order-create';
import OrderCard from '../../components/order-card';
import checkPerm from '../../utils/permission';
import Filter from './filter';
import OrderWith, { widthRefType } from '../../containers/order-dealwith';
import OrderCreate from '../../containers/order-create';
import OrderScheduling from '../../containers/order-scheduling/index';
import GoodsPartial from '../../containers/order-pratial/index';
import OrderAdjust from '../../containers/order-adjust/index';
import ModifyPrice from '../../containers/order-price/index';
import EditOrder from '../../containers/order-edit/index';
import ProductionOrder from './production-order';
import styles from './index.module.less';

function SalesOrders() {
  const [searchParams] = useSearchParams({
    orderStatus: '0',
    region: '0',
    open: '0',
    distribution: '',
    supplierId: '0',
    isCmOrder: '',
    sellerCompanyIds: [],
  });
  const withRef = useRef<widthRefType>(null);
  const [showFilter, setShowFilter] = useState(false);
  const [showOrderWith, setShowOrderWith] = useState(false);
  const [showScheduling, setShowScheduling] = useState(false);
  const [showOrderScheduling, setShowOrderScheduling] = useState(false);
  const [showGoodsPartial, setShowGoosPartial] = useState(false);
  const [showOrderAdjust, setShowOrderAdjust] = useState(false);
  const [loading, setLoading] = useState(false);
  const [adjustId, setAdjustId] = useState<number>(0);
  const [customerCompanyId, setCustomerCompanyId] = useState<number>(0);
  const [adjustName, setAdjustName] = useState<string>('');
  const [showModifyPrice, setShowModifyPrice] = useState(false);
  const [showEditOrder, setShowEditOrder] = useState(false);
  const [showOrderCreate, setShowOrderCreate] = useState(false);
  const [list, setList] = useState<RecordsResult[]>([]);
  const [orderNo, setOrderNo] = useState<number>(0);
  const [orderNumber, setOrderNumber] = useState<string>('');
  const [orderState, setOrderState] = useState<pointItemResult[]>([]);
  const [isFilterActive, setIsFilterActive] = useState(false);
  const [load, setLoad] = useState(false);
  const [distributionCompanyId, setDistributionCompanyId] = useState(0);
  const [distributionCompanyName, setDistributionCompanyName] = useState('');
  const cardNum = useState([1, 2]);
  const [filterList] = useState([
    { label: '全部', value: 0, code: 'R_001_012' },
    { label: '待处理', value: 1, code: 'R_001_011' },
    { label: '进行中', value: 2, code: 'R_001_013' },
    { label: '已完成', value: 80, code: 'R_001_014' },
    { label: '已关闭', value: 90, code: 'R_001_015' },
  ]);
  const soldList = useRef<HTMLDivElement>(null);
  const orderInfo = useRef({
    orderFuncType: 0,
  });
  const orderFlowSerialNo = useRef(0);
  const isOpen =
    (Number(searchParams.get('open')) && Number(searchParams.get('distribution'))) ||
    Number(searchParams.get('isDistributionOrder'));
  const navigate = useNavigate();
  const params = useRef({
    batchNo: 0,
    key: '',
  });
  const orderType = searchParams.get('region');
  const distributionId = Number(searchParams.get('distribution'));
  const [pageCount, setPageCount] = useState(1);
  const pageList = {
    pageSize: 6,
    pageNo: 1,
    key: searchParams.get('key') || '',
    orderStatus: Number(searchParams.get('orderStatus')),
    startDate: null,
    endDate: null,
    isPsiOrder: 0,
    actionPointNos: [],
    flowSerialNo: null,
    isDistributionOrder: isOpen ? 1 : null,
    isCmOrder: searchParams.get('isCmOrder') || '',
    // @ts-ignore
    sellerCompanyIds: JSON.parse(searchParams.get('sellerCompanyIds')),
  };
  const scrollRef = useRef<InfiniteScroll>(null);
  const pageRef = useRef<SalesOrderParams>(pageList);
  const stockType = searchParams.get('stockType');
  // const orderTypeNum = useRef(0);

  let fn: any = null;
  if (Number(stockType) === 1) {
    fn = orderBackList;
  } else if (Number(stockType) === 2) {
    fn = orderBuyList;
  } else {
    fn = searchParams.get('region') === '1' ? getSalesOrderList : getPurchaseList;
  }

  // const [isAddOrderSelect, setIsAddOrderSelect] = useState(false);

  const onGoodsPartial = (value: number) => {
    setOrderNo(value);
    setShowGoosPartial(true);
  };

  const onOrderAdjust = () => {
    setAdjustId(orderNo);
    setShowOrderAdjust(true);
  };

  const onOrderDeal = () => {
    setShowOrderWith(true);
  };

  const onChangePrice = () => {
    setShowModifyPrice(true);
  };

  const getOrderDetailList = (item: SalesOrderParams) =>
    fn(
      item.buyerOrSellerCompanyId
        ? item
        : {
            ...item,
            buyerOrSellerCompanyId:
              Number(searchParams.get('supplierId')) || item.buyerOrSellerCompanyId,
          },
      isOpen ? `${distributionId}` : ''
    ).then((res: any) => {
      pageRef.current = { ...item, pageNo: item.pageNo + 1 };
      setPageCount(Math.ceil(res.total / res.size));
      if (item.pageNo === 1) {
        setList([...res.records]);
      } else {
        setList((prevState) => [...prevState, ...res.records]);
      }
    });

  const getSalesList = (listItem: SalesOrderParams) => {
    setLoading(true);
    const formData: SalesOrderParams = listItem;
    if (distributionCompanyId) {
      formData.buyerOrSellerCompanyId = distributionCompanyId;
    }
    if (Number(searchParams.get('supplierId'))) {
      formData.buyerOrSellerCompanyId = Number(searchParams.get('supplierId')) || null;
    }
    getOrderDetailList(formData).finally(() => {
      setLoading(false);
    });
  };

  const getOrderList = useMemoizedFn(() => {
    setLoad(true);
    getOrderDetailList(pageRef.current).finally(() => {
      setLoad(false);
    });
  });

  const searchList = (key: string) => {
    const listItem = {
      ...pageRef.current,
      pageSize: 6,
      pageNo: 1,
      key,
    };
    getSalesList(listItem);
  };

  const pointList = (id: number) => {
    const listItem = { ...pageList, orderStatus: id, key: params.current.key };
    getOrderPointList({ orderStatus: id }).then((res) => {
      setOrderState(res.list);
      getSalesList(listItem);
    });
  };

  const updateWidth = () => {
    withRef.current?.getOrderAction();
  };

  const toDetail = (val: number) => {
    navigate(
      `/indent/detail?orderNo=${val}&current=${
        Number(orderType) === 0 ? 'buy' : 'sell'
      }&distribution=${distributionId}&nycbngOrgId=${isOpen ? distributionId : 0}&stockType=${
        stockType || ''
      }`
    );
  };

  const loadMore = () => {
    getOrderList();
  };

  const onOrderButton = (
    code: number,
    no: number,
    name: string,
    num: string,
    item: RecordsResult
  ) => {
    if (!checkPerm(item.orderFuncType, Number(orderType) === 0 ? 1 : 2, item.orderType, code))
      return null;
    setOrderNo(no);
    setOrderNumber(num);
    orderInfo.current = item;
    if (code === 2020) {
      return setShowOrderScheduling(true);
    }
    if (code === 2010) {
      setAdjustName(name);
      setCustomerCompanyId(item.buyerCompanyId);
      onOrderDeal();
      return null;
    }
    if (code === 1910) {
      setAdjustId(no);
      getOrderRelateList({ orderNo: no }, isOpen ? `${distributionId}` : '')
        .then(() => {
          setShowScheduling(true);
        })
        .catch(() => {
          setShowScheduling(false);
        });
      return null;
    }
    if (code === 4001) {
      Modal.confirm({
        title: '提示',
        icon: '',
        centered: true,
        okText: '确认',
        content: `是否发货通知`,
        onOk: () => {
          deliveryNotice({
            orderNo: no,
          }).then(() => {
            getSalesList({ ...pageList, pageNo: 1 });
          });
        },
      });
      return false;
    }
    if (code === 4002) {
      Modal.confirm({
        title: '提示',
        icon: '',
        centered: true,
        okText: '确认',
        content: `是否验收通知`,
        onOk: () => {
          checkAcceptnotice({
            orderNo: no,
          }).then(() => {
            getSalesList({ ...pageList, pageNo: 1 });
          });
        },
      });
      return false;
    }
    navigate(
      `/indent/detail?orderNo=${no}&current=${
        searchParams.get('region') === '1' ? 'sell' : 'buy'
      }&distribution=${distributionId}&nycbngOrgId=${
        isOpen ? distributionId : 0
      }&stockType=${stockType}`
    );
    return null;
  };

  const initPageData = (orderItem: SalesOrderParams) => {
    setLoading(true);
    getOrderDetailList(orderItem)
      .then(() => {
        getOrderPointList({ orderStatus: Number(searchParams.get('orderStatus')) }).then((res) => {
          setOrderState(res.list);
        });
        return new Promise<void>((resolve) => {
          setTimeout(() => {
            const el = scrollRef.current?.getScrollableTarget();
            if (el && el.offsetHeight >= el.scrollHeight) {
              getOrderDetailList(pageRef.current).then(resolve);
            } else {
              resolve();
            }
          }, 100);
        });
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getPerm = () => {
    getNycbngPerm().then((res) => {
      const prem = res.list
        .filter((f) => f.id === distributionId)[0]
        .permList.includes('R_001_009_001');
      if (searchParams.get('open') === '1') {
        setTimeout(() => {
          setShowEditOrder(prem);
        }, 300);
      }
    });
  };

  const searchPerm = usePermission('R_001_018');

  const filterPerm = usePermission('R_001_019', () => {
    if (!orderState.length) return;
    setShowFilter(true);
  });

  const newBuy = usePermission('R_001_010_001', () => {
    if (distributionId) {
      setShowEditOrder(true);
    } else {
      setShowOrderCreate(true);
    }
  });
  const newSell = usePermission('R_001_009_001', () => {
    if (distributionId) {
      setShowEditOrder(true);
    } else {
      setShowOrderCreate(true);
    }
  });

  const newEnterBuy = usePermission('R_001_022_001', () => {
    setShowOrderCreate(true);
  });
  const newEnterSell = usePermission('R_001_021_001', () => {
    setShowOrderCreate(true);
  });

  useEffect(() => {
    sessions.select('orderCode');
    const page = pageRef.current;
    const orderItem: SalesOrderParams = {
      ...page,
      orderStatus: Number(searchParams.get('orderStatus')),
      startDate: Number(searchParams.get('startTime')) || null,
      endDate: Number(searchParams.get('endTime')) || null,
    };
    if (distributionId) {
      getDistributionCompanyInfo({ id: distributionId })
        .then((result) => {
          setDistributionCompanyId(result.companyId);
          setDistributionCompanyName(result.companyName);
          orderItem.buyerOrSellerCompanyId = isOpen ? result.companyId : null;
          orderItem.isOnlyDistributed = isOpen ? null : 1;
          getPerm();
        })
        .finally(() => {
          initPageData(orderItem);
          pageRef.current = { ...page, pageNo: page.pageNo + 1 };
        });
      return;
    }
    if (Number(searchParams.get('isOnlyDistributed'))) {
      orderItem.isOnlyDistributed = 1;
      if (Number(searchParams.get('distributorComId'))) {
        orderItem.distributorComId = Number(searchParams.get('distributorComId'));
      }
    }
    initPageData(orderItem);
    pageRef.current = { ...page, pageNo: page.pageNo + 1 };
  }, []); // eslint-disable-line

  useEffect(() => {
    if (!distributionId && Number(searchParams.get('open'))) {
      setShowOrderCreate(true);
    }
  }, [distributionId, searchParams]);

  useEffect(() => {
    if (Number(searchParams.get('replenishOrderNo')) && localStorage.getItem('ORDERCREATE')) {
      stockOrderCreate({
        onClose(): void {},
        onConfirm(): void {
          localStorage.removeItem('ORDERCREATE');
          getSalesList({ ...pageList, pageNo: 1, orderStatus: 0 });
        },
        orderType: 0,
        orderTypeNum: 1,
        replenishOrderNo: Number(searchParams.get('replenishOrderNo')),
      });
    }
  }, [searchParams]); // eslint-disable-line

  const page = pageRef.current;

  // const menuItems = [
  //   { label: '订单', key: 1, type: 'sale' },
  //   { label: '退货单', key: 2, type: 'sale' },
  //   { label: '订单', key: 1, type: 'purchase' },
  //   { label: '进货单', key: 2, type: 'purchase' },
  // ];

  const orderTitle = useMemo(() => {
    let str = '';
    if (stockType) {
      str = stockType === '1' ? '销售退货单' : '采购退货单';
    } else {
      str = Number(orderType) ? '销售订单' : '采购订单';
    }
    return str;
  }, [orderType, stockType]);

  const orderCreateText = useMemo(() => {
    switch (Number(stockType)) {
      case 1:
        return '退货单';
      case 2:
        return '退货单';
      default:
        return '订单';
    }
  }, [stockType]);

  return (
    <Context
      loading={loading}
      container
      theme={list.length >= 1 ? null : 'default'}
      id="content"
      head={
        <Context.Head
          searchTooltipTitle="搜索"
          quickFilter={
            !Number(stockType) ? (
              <Context.QuickFilter
                label="显示"
                value={filterList.filter((f) => f.value === pageRef.current.orderStatus)[0].label}
                options={filterList}
                onChange={(e) => {
                  const { code } = filterList.filter((f) => f.value === e)[0];
                  if (!testPerm(code)) return;
                  setIsFilterActive(false);
                  pointList(e);
                }}
                style={{ minWidth: '80px' }}
              />
            ) : null
          }
          isFilterActive={isFilterActive}
          onSearchClick={searchPerm}
          onSearch={(e) => {
            params.current.key = e;
            searchList(e);
          }}
          onFilter={!Number(stockType) ? filterPerm : undefined}
          placeholder="搜索订单"
          title={[
            {
              title: '订单管理',
              to: '/indent',
            },
            orderTitle,
          ]}
          extra={
            distributionId ? (
              <Context.HeadTool>
                <Button
                  type="primary"
                  size="small"
                  onClick={Number(orderType) === 0 ? newBuy : newSell}
                >
                  新建订单
                </Button>
              </Context.HeadTool>
            ) : (
              // <Context.HeadTool>
              //   <Dropdown
              //     overlayClassName={styles.titleDropdown}
              //     trigger={['click']}
              //     visible={isAddOrderSelect}
              //     overlay={
              //       <Menu
              //         items={menuItems.filter(
              //           (item) => item.type === (Number(orderType) ? 'sale' : 'purchase')
              //         )}
              //         onClick={({ key }) => {
              //           orderTypeNum.current = +key;
              //           setShowOrderCreate(true);
              //           setIsAddOrderSelect(false);
              //         }}
              //       />
              //     }
              //     placement="bottom"
              //     onVisibleChange={() => {
              //       setIsAddOrderSelect(!isAddOrderSelect);
              //     }}
              //   >
              //     <Button type="primary" size="small">
              //       新建
              //     </Button>
              //   </Dropdown>
              // </Context.HeadTool>
              <Context.HeadTool>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => {
                    if (Number(stockType) === 1) {
                      newEnterSell();
                    } else if (Number(stockType) === 2) {
                      newEnterBuy();
                    } else {
                      // @ts-ignore
                      // eslint-disable-next-line no-unused-expressions
                      Number(orderType) === 0 ? newBuy() : newSell();
                    }
                  }}
                >
                  新建{orderCreateText}
                </Button>
              </Context.HeadTool>
            )
          }
        />
      }
    >
      {list.length ? (
        <div className={styles.soldList} id="soldList" ref={soldList}>
          <InfiniteScroll
            ref={scrollRef}
            dataLength={list.length}
            hasMore={pageRef.current.pageNo <= pageCount}
            loader={
              load ? (
                <div className="text-center">
                  <Spin tip="加载中..." />
                </div>
              ) : undefined
            }
            endMessage={
              <div className={styles.divider}>
                <Divider plain>
                  <span className={styles.endMessage}>没有更多了</span>
                </Divider>
              </div>
            }
            next={loadMore}
            scrollableTarget="soldList"
          >
            <Row gutter={20}>
              {cardNum.map((num, numIndex) => (
                <Col md={12} xs={24} key={createUuid()}>
                  {list
                    ?.filter((f, index) => {
                      if (numIndex === 1 ? index % 2 : !(index % 2)) {
                        return { ...f };
                      }
                      return null;
                    })
                    .map((item) => (
                      <OrderCard
                        orderType="sales"
                        buyerCompanyName={
                          (searchParams.get('region') === '1' && !Number(stockType)) ||
                          (searchParams.get('region') === '0' && Number(stockType) === 2)
                            ? item.buyerCompanyName
                            : item.sellerCompanyName
                        }
                        orderNumber={item.orderNumber}
                        orderStatusName={item.orderStatusName}
                        productVOList={item.productVOList}
                        orderPriceAmount={item.orderPriceAmount}
                        canOptButtonList={item.canOptButtonList}
                        orderFuncTypeName={item.orderFuncTypeName}
                        onButton={(code) => {
                          onOrderButton(
                            code,
                            item.orderNo,
                            item.orderFuncTypeName,
                            item.orderNumber,
                            item
                          );
                        }}
                        onClick={() => {
                          toDetail(item.orderNo);
                        }}
                        key={item.orderNo}
                        ordCalcTimeDiffVO={item.ordCalcTimeDiffVO}
                      />
                    ))}
                </Col>
              ))}
            </Row>
          </InfiniteScroll>
        </div>
      ) : (
        !loading && (
          <Empty
            type="order"
            message="暂无数据"
            description={
              page.key ||
              page.orderStatus ||
              page.startDate ||
              page.endDate ||
              page.actionPointNos?.length
                ? '换个筛选条件再试试'
                : null
            }
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
            }}
            imageStyle={{
              width: '80%',
              height: '100%',
            }}
          />
        )
      )}
      <Filter
        visible={showFilter}
        page={pageRef.current}
        searchFilter={(data: SalesOrderParams) => {
          if (
            data.pageNo === 1 &&
            data?.actionPointNos?.length === 0 &&
            !data.startDate &&
            !data.endDate
          ) {
            setIsFilterActive(false);
          } else {
            setIsFilterActive(true);
          }
          getSalesList(data);
        }}
        onClose={() => {
          setShowFilter(false);
        }}
        orderItem={orderState}
      />

      <OrderWith
        ref={withRef}
        visible={showOrderWith}
        orderNo={orderNo}
        orderNumber={orderNumber}
        type={searchParams.get('region')}
        nycbngOrgId={isOpen ? `${distributionId}` : ''}
        distributionCompanyId={distributionCompanyId}
        customerCompanyId={customerCompanyId}
        onClose={() => {
          setShowOrderWith(false);
        }}
        onOrderAdjust={() => {
          onOrderAdjust();
        }}
        onGoodsPartial={() => {
          onGoodsPartial(orderNo);
        }}
        getOrderList={() => {
          getSalesList({
            ...pageRef.current,
            pageSize: 6,
            pageNo: 1,
          });
        }}
        onChangePrice={() => {
          onChangePrice();
        }}
        onScheduling={(value, batchNo, flowSerialNo) => {
          setOrderNo(value);
          orderFlowSerialNo.current = Number(flowSerialNo);
          params.current.batchNo = batchNo || 0;
          setShowOrderScheduling(true);
        }}
      />
      <ProductionOrder
        visible={showScheduling}
        adjustId={adjustId}
        nycbngOrgId={isOpen ? `${distributionId}` : ''}
        onClose={() => {
          setShowScheduling(false);
        }}
      />

      <OrderScheduling
        orderNo={orderNo}
        nycbngOrgId={isOpen ? `${distributionId}` : ''}
        batchNo={params.current.batchNo}
        orderFuncType={orderInfo.current.orderFuncType}
        visible={showOrderScheduling}
        flowSerialNo={orderFlowSerialNo.current}
        updateWidth={updateWidth}
        onClose={() => {
          setShowOrderScheduling(false);
        }}
      />

      <GoodsPartial
        visible={showGoodsPartial}
        orderNo={orderNo}
        nycbngOrgId={isOpen ? `${distributionId}` : ''}
        updateWidth={updateWidth}
        onClose={() => {
          setShowGoosPartial(false);
        }}
      />

      <OrderAdjust
        visible={showOrderAdjust}
        adjustId={adjustId}
        distributionCompanyId={distributionCompanyId}
        nycbngOrgId={isOpen ? `${distributionId}` : ''}
        adjustName={adjustName}
        orderNo={orderNo}
        onClose={() => {
          setShowOrderAdjust(false);
        }}
        getSalesList={() => {
          if (distributionCompanyId) {
            searchList('');
          } else {
            getOrderList();
          }
          updateWidth();
        }}
      />

      <ModifyPrice
        visible={showModifyPrice}
        orderNo={orderNo}
        nycbngOrgId={isOpen ? `${distributionId}` : ''}
        onClose={() => {
          getSalesList({ ...pageList });
          setShowModifyPrice(false);
        }}
        getOrderList={() => {
          getSalesList({ ...pageList, pageNo: 1 });
        }}
        updateWidth={updateWidth}
      />

      <EditOrder
        visible={showEditOrder}
        getOrderList={() => {
          getSalesList({ ...pageList, pageNo: 1, orderStatus: 0 });
        }}
        type={searchParams.get('region')}
        distributionCompanyId={distributionCompanyId}
        distribution={Number(searchParams.get('distribution'))}
        distributionCompanyName={distributionCompanyName}
        nycbngOrgId={isOpen ? `${distributionId}` : ''}
        onClose={() => {
          setShowEditOrder(false);
        }}
      />
      <OrderCreate
        visible={showOrderCreate}
        orderType={Number(orderType)}
        orderTypeNum={Number(stockType) ? 2 : 1}
        onClose={() => setShowOrderCreate(false)}
        onConfirm={() => {
          getSalesList({ ...pageList, pageNo: 1, orderStatus: 0 });
        }}
      />
    </Context>
  );
}

export default SalesOrders;
