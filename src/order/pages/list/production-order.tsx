import { getOrderRelateList, postOrderFinishProduce, printProductionOrder } from '@/apis';
import { ProductionOrderItem } from '@/apis/order/get-order-relate-list';
import { Drawer } from '@/components';
import { PropsWithChildren, useEffect, useState } from 'react';
import { Modal } from 'antd';
import { useNavigate } from 'react-router-dom';
import checkPerm from '../../utils/permission';
import OrderCard from '../../components/order-card/index';
import styles from './production-order.module.less';

interface productionOrderProps {
  visible: boolean;
  onClose: () => void;
  adjustId: number;
  nycbngOrgId: string;
}

function ProductionOrder({
  visible,
  onClose,
  adjustId,
  nycbngOrgId,
}: PropsWithChildren<productionOrderProps>) {
  const { confirm } = Modal;
  const navigate = useNavigate();
  const [relate, setRelate] = useState<ProductionOrderItem[]>([]);

  const getRelate = () => {
    getOrderRelateList({ orderNo: adjustId }, nycbngOrgId)
      .then((res) => {
        setRelate(res.list);
      })
      .catch(() => {
        onClose();
      });
  };

  const showModal = (name: string, productOrderId: number) => {
    confirm({
      title: '提示',
      content: `是否${name}`,
      icon: null,
      width: 290,
      centered: true,
      className: styles.showModal,
      getContainer: '#production-order .ant-drawer-wrapper-body',
      onOk() {
        postOrderFinishProduce({ productOrderId }).then(() => {
          getRelate();
        });
      },
      onCancel() {},
    });
  };

  const onButton = (code: number, productOrderId: number) => {
    if (!checkPerm(-1, 0, 0, code)) return;
    if (code === 1910) {
      getRelate();
      return;
    }
    if (code === 2021) {
      showModal('生产完成', productOrderId);
      return;
    }
    if (code === 2022) {
      printProductionOrder({ productOrderId }).then((res) => {
        const printUrl = (window.webkitURL || window.URL).createObjectURL(res);
        window.open(printUrl, '_blank');
      });
    }
  };

  const toDetail = (productOrderId: number) => {
    navigate(`/order/production/detail?productOrderId=${productOrderId}`);
  };

  useEffect(() => {
    if (visible) {
      getRelate();
    }
  }, [visible]); // eslint-disable-line
  return (
    <Drawer
      title="生产订单"
      id="production-order"
      visible={visible}
      onClose={() => {
        onClose();
      }}
    >
      {relate.map((item) => (
        <OrderCard
          orderType="production"
          companyName={item.companyName}
          orderStatusName={item.orderStatusName}
          orderNumberShow
          // @ts-ignore
          productVOList={item.productionDetailList}
          canOptButtonList={item.canOptButtonList}
          onButton={(code) => {
            onButton(code, item.productOrderId);
          }}
          onClick={() => {
            toDetail(item.productOrderId);
          }}
        />
      ))}
    </Drawer>
  );
}

export default ProductionOrder;
