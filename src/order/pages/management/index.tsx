import { Context, Icon } from '@/components';
import { Select, Switch, message, InputNumber } from 'antd';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import {
  auditResultFoo,
  getOrderAuditList,
  getOrderAuditUpdate,
  getOrderSettingInfo,
  postOrderAuditUpdate,
  settingInfoResult,
} from '@/apis/order';
import { getSearchMatchQuery, matchListResult } from '@/apis/search';
import { checkPerm, testPerm } from '@/utils/permission';
import { Outlet, useNavigate } from 'react-router-dom';
import Approval from './approval';
import selectCustomer from './select-customer';
import { SelectListCustomerProps } from './select-customer/select-customer';
import styles from './index.module.less';

interface orderItemType {
  name: string;
  text: string;
  keyName: string;
  checked: boolean;
  id: number;
  code?: string;
}

const orderList: orderItemType[] = [
  {
    name: '自动接单：',
    text: '开启后，本公司将自动接受买家下的单',
    keyName: 'isAutoOrder',
    checked: false,
    id: 8,
    code: 'R_001_008_002_001',
  },
  {
    name: '自动下单：',
    text: '开启后，本公司将自动向您的商品共享上游自动下单',
    keyName: 'isAutoToUpOrder',
    checked: false,
    id: 18,
    code: 'R_001_020',
  },
  {
    name: '自动锁价：',
    text: '开启后，销售订单生效会自动锁定价格',
    keyName: 'autoLockPrice',
    checked: false,
    id: 24,
    // code: 'R_001_020',
  },
  {
    name: '仓库发货验收确认：',
    text: '开启后，本公司在发货和验收对应的出入库单时需确认',
    keyName: 'isWarehouseEnable',
    checked: false,
    id: 13,
  },
  {
    name: '生成生产排期单：',
    text: '开启后，卖家接单商品会生成生产排期订单',
    keyName: 'isBuyerDeliveryTime',
    checked: false,
    id: 15,
  },
  {
    name: '定向采购：',
    text: '开启后，使用分单功能时，接单公司需向定向采购方采购',
    keyName: 'isOrienPurc',
    checked: false,
    id: 6,
  },
  {
    name: '买家期望送达时间：',
    text: '开启后，买家下单时必须填写期望送达时间',
    keyName: 'isExpectedDeliveryTime',
    checked: false,
    id: 9,
  },
  {
    name: '填写施工信息：',
    text: '开启后，本公司在购买商品时需填写施工单位',
    keyName: 'isFillMessage',
    checked: false,
    id: 7,
  },
  // {
  //   name: '填写项目信息：',
  //   text: '开启后，本公司在购买商品时需填写项目信息',
  //   keyName: 'isProjectInformation',
  //   checked: false,
  //   id: 14,
  // },
  {
    name: '部分分单：',
    text: '开启后，本公司在分单时商品数量不需要全部分完',
    keyName: 'isCanPartSplit',
    checked: false,
    id: 19,
  },
  {
    name: '负库存：',
    text: '开启后，允许商品销售数量超过库存数量',
    keyName: 'isNegativeStockEnable',
    checked: false,
    id: 20,
  },
  {
    name: '自动验收天数：',
    text: '开启自动验收，系统根据用户设置的自动验收天数进行自动验收',
    keyName: 'autoAccept',
    checked: false,
    id: 21,
  },
  {
    name: '自动取消天数：',
    text: '开启自动取消，系统根据用户设置的自动取消天数进行自动取消',
    keyName: 'autoOffOrder',
    checked: false,
    id: 23,
  },
  // {
  //   name: '补录结算：',
  //   text: '开启补录结算，补录订单产生的款项可在财务管理统一付款/收款',
  //   keyName: 'isSuppleSettle',
  //   checked: false,
  //   id: 25,
  // },
  {
    name: '补录收款确认：',
    text: '开启后，补录订单线下收款时需要财务进行确认',
    keyName: 'supplementaryReceipt',
    checked: false,
    id: 22,
  },
  {
    name: '补录付款确认：',
    text: '开启后，补录订单线下付款时需要财务进行确认',
    keyName: 'supplementaryPay',
    checked: false,
    id: 28,
  },
  {
    name: '配送方式：',
    text: '开启后，展示付款方式选项，关闭后则不展示',
    keyName: 'isDeliveryMode',
    checked: false,
    id: 26,
  },

  {
    name: '订单标签：',
    text: '开启后，订单标签变为创建新订单的必选项',
    keyName: 'requiredOrderLabel',
    checked: false,
    id: 29,
  },
];

function Management() {
  const { Option } = Select;
  const navigator = useNavigate();
  const [regulateId, setRegulate] = useState(1);
  const [flowList, setFlowList] = useState<auditResultFoo[]>([]);
  const [orderListId, setOrderListId] = useState(orderList);
  const [infoList, setInfoList] = useState<settingInfoResult>();
  const [approvalVis, setApprovalVis] = useState(false);
  const [approvalId, setApprovalId] = useState<number>(0);
  const [approvalName, setApprovalName] = useState<string>('');
  const [selectId, setSelectId] = useState<number | null>(null);
  const [updateId, setUpdateId] = useState<number>(0);
  const [matchList, setMatchList] = useState<matchListResult[]>([]);
  const [regulateList] = useState([
    {
      name: '节点审批管理',
      id: 1,
      code: 'R_001_008_001',
    },
    {
      name: '订单管理',
      id: 2,
      code: 'R_001_008_002',
    },
    {
      name: '订单标签',
      id: 3,
      // code: 'R_001_008_002',
    },
  ]);
  const [checkDays, setCheckDays] = useState(0);
  const [checkCancelDays, setCheckCancelDays] = useState(1);
  const [customerList, setCustomerList] = useState<SelectListCustomerProps[]>([]);
  const [supplierList, setSupplierList] = useState<SelectListCustomerProps[]>([]);

  // 节点审批管理按钮
  const flowChange = (checked: boolean, data: auditResultFoo) => {
    if (!data.procFrmName) {
      setApprovalId(data.wfFormType);
      setUpdateId(data.id);
      setApprovalName(data.procFrmName);
      setApprovalVis(true);
      return;
    }
    const updateWfAuditCfgStateLsParamList = [
      {
        id: data.id,
        state: checked ? 1 : 0,
      },
    ];
    getOrderAuditUpdate({ updateWfAuditCfgStateLsParamList }).then(() => {
      getOrderAuditList({}).then((res) => {
        setFlowList(res.list);
      });
    });
  };

  // 订单管理按钮开关
  const orderChange = (checked: boolean, { id, code }: orderItemType) => {
    if (code && !testPerm(code)) return;
    if (id === 20 && !checked && orderListId.filter((item) => item.id === 18)[0].checked) {
      message.warning('开启自动下单不能关闭负库存交易');
      return;
    }
    if (id === 18 && checked && !orderListId.filter((item) => item.id === 20)[0].checked) {
      message.warning('开启自动下单不能关闭负库存交易');
      return;
    }
    if (id !== 6) {
      postOrderAuditUpdate({
        item: id,
        option: checked ? 1 : 0,
        days: id === 21 ? checkDays : checkCancelDays,
      })
        .then(() => {
          setOrderListId(
            orderListId.map((m) => ({
              ...m,
              checked: m.id === id ? checked : m.checked,
            }))
          );
        })
        .catch(() => {
          setOrderListId(
            orderListId.map((m) => ({
              ...m,
              checked: m.id === id ? !checked : m.checked,
            }))
          );
        });
    } else if (id === 6 && !checked) {
      postOrderAuditUpdate({ item: id, option: 0 }).then(() => {
        setSelectId(null);
        setOrderListId(
          orderListId.map((m) => ({
            ...m,
            checked: m.id === id ? checked : m.checked,
          }))
        );
      });
    } else {
      setOrderListId(
        orderListId.map((m) => ({
          ...m,
          checked: m.id === id ? checked : m.checked,
        }))
      );
    }
  };

  const onChangeDays = (num: number) => {
    const nums = Number(num.toFixed(0));
    setCheckDays(nums);
    postOrderAuditUpdate({
      item: 21,
      option: 1,
      days: nums,
    }).then(() => {});
  };

  const onChangeCancelDays = (num: number) => {
    let days = num;
    if (num < 1) {
      days = 1;
      message.warning('自动取消天数不能小于1');
      setCheckCancelDays(1);
    }
    if (num > 999) {
      days = 999;
      message.warning('自动取消天数不能大于999');
      setCheckCancelDays(999);
    }
    const nums = Number(days.toFixed(0));
    setCheckCancelDays(nums);
    postOrderAuditUpdate({
      item: 23,
      option: 1,
      days: nums,
    }).then(() => {});
  };

  // 订单管理定向采购方公司变更
  const onChange = (value: number) => {
    postOrderAuditUpdate({ item: 6, assignCompanyId: value, option: 1 }).then(() => {
      setSelectId(value);
    });
  };

  // 订单管理定向采购搜索
  const onSearch = (value: string) => {
    getSearchMatchQuery({ search: value }).then((res) => {
      setMatchList(res.list);
    });
  };

  // 选择客户供应商
  const onSelectCustomer = (val: string) => {
    selectCustomer({
      customerList,
      supplierList,
      type: val,
      onConfirm: (arr) => {
        if (val === 'customer') {
          setCustomerList([...arr]);
        } else {
          setSupplierList([...arr]);
        }
      },
    });
  };

  const onCustomerDelete = (id: number) => {
    const arr = customerList.filter((item) => item.customerId !== id);
    postOrderAuditUpdate({
      customerList: arr,
      supplierList,
      item: 25,
      option: 1,
    }).then(() => {
      setCustomerList([...arr]);
      message.success('删除成功');
    });
  };

  const onSupplierDelete = (id: number) => {
    const arr = supplierList.filter((item) => item.customerId !== id);
    postOrderAuditUpdate({
      supplierList: arr,
      customerList,
      item: 25,
      option: 1,
    }).then(() => {
      setSupplierList([...arr]);
      message.success('删除成功');
    });
  };

  useEffect(() => {
    if (!approvalVis && checkPerm('R_001_008_001')) {
      getOrderAuditList({}).then((res) => {
        setFlowList(res.list);
      });
    }
  }, [approvalVis]);

  useEffect(() => {
    if (!checkPerm('R_001_008_001')) {
      setRegulate(2);
    }

    // 获取数据
    if (checkPerm('R_001_008_002')) {
      getOrderSettingInfo().then((res) => {
        const data = orderListId.map((m) => {
          const keys = Object.keys(res).indexOf(m.keyName);
          const values = Object.values(res);
          return {
            ...m,
            checked: !!values[keys],
          };
        });
        setCheckDays(res.days);
        setCheckCancelDays(res.autoOffDays || 1);
        setCustomerList(res.customerVoList || []);
        setSupplierList(res.supplierVoList || []);
        onSearch(res.assignCompanyName);
        setSelectId(res.assignCompanyName ? res.assignCompanyId : null);
        setInfoList(res);
        setOrderListId(data);
      });
    }
  }, []); // eslint-disable-line

  const hideHead = window.name === 'SystemSettingIframe';

  return (
    <Context
      permission={{ code: ['R_001_008_001', 'R_001_008_002'], newLogic: true }}
      theme={null}
      head={
        <Context.Head
          placeholder="后台管理"
          isBack={!hideHead}
          title={[
            hideHead
              ? '订单管理'
              : {
                  title: '订单管理',
                  to: '/indent',
                },
            '后台管理',
          ]}
        />
      }
    >
      <div className={styles.card} style={{ minWidth: '700px' }}>
        <div className={styles.cardLeft}>
          {regulateList.map((m) => (
            <div
              role="button"
              tabIndex={m.id}
              className={classNames(
                styles.regulate,
                m.id === regulateId ? styles.regulateForce : ''
              )}
              onClick={() => {
                if (!m.code) {
                  setRegulate(m.id);
                  navigator('/indent/management/order-type');
                  return;
                }
                if (testPerm(m.code)) {
                  navigator('/indent/management');
                  setRegulate(m.id);
                }
              }}
              key={m.id}
            >
              {m.name}
            </div>
          ))}
        </div>
        {regulateId === 1 && (
          <div className={styles.cardRight}>
            {flowList.map((m) => (
              <div className={styles.flow} key={m.id}>
                <div className={styles.flowName}>{m.actionName}：</div>
                <div className={styles.flowBody}>
                  <Switch checked={!!m.state} onChange={(value) => flowChange(value, m)} />
                  {!!m.state && (
                    <div
                      role="button"
                      tabIndex={0}
                      className={styles.open}
                      onClick={() => {
                        setApprovalId(m.wfFormType);
                        setUpdateId(m.id);
                        setApprovalName(m.procFrmName);
                        setApprovalVis(true);
                      }}
                    >
                      <span>流程审批：</span>
                      <span className={styles.switch}>
                        <span>{m.procFrmName}</span>
                        <Icon
                          name="right"
                          size={18}
                          style={{ cursor: 'pointer', marginLeft: '48px' }}
                        />
                      </span>
                    </div>
                  )}
                  <div className={styles.flowText}>{m.explain}</div>
                </div>
              </div>
            ))}
          </div>
        )}
        {regulateId === 2 && (
          <div className={styles.cardRight}>
            {orderListId.map((m) => (
              <>
                <div className={styles.flow} key={m.id}>
                  <div className={styles.flowName} style={{ minWidth: '175px' }}>
                    {m.name}
                  </div>
                  <div className={styles.flowBody}>
                    <Switch checked={m.checked} onChange={(value) => orderChange(value, m)} />
                    {m.checked && m.keyName === 'isOrienPurc' && (
                      <div>
                        定向采购方:
                        <Select
                          showSearch
                          defaultValue={selectId}
                          placeholder={
                            infoList?.assignCompanyName
                              ? infoList?.assignCompanyName
                              : '请输入定向采购公司名称'
                          }
                          optionFilterProp="children"
                          onChange={onChange}
                          onSearch={onSearch}
                          style={{ width: '200px', marginLeft: '10px' }}
                        >
                          {matchList.length &&
                            matchList.map((match) => (
                              <Option value={match.id} className={styles.optionTick}>
                                {match.companyName}
                              </Option>
                            ))}
                        </Select>
                      </div>
                    )}
                    {m.id === 21 && m.checked && (
                      <div className={styles.days}>
                        <div>自动验收天数：</div>
                        <InputNumber
                          value={Number(checkDays.toFixed(0) || undefined)}
                          placeholder="请输入"
                          controls={false}
                          min={0}
                          max={*********}
                          onBlur={(e) => onChangeDays(Number(e.target.value))}
                        />
                      </div>
                    )}
                    {m.id === 23 && m.checked && (
                      <div className={styles.days}>
                        <div>自动取消天数：</div>
                        <InputNumber
                          value={Number(checkCancelDays.toFixed(0) || undefined)}
                          placeholder="请输入"
                          controls={false}
                          min={0}
                          max={999}
                          onBlur={(e) => onChangeCancelDays(Number(e.target.value))}
                        />
                      </div>
                    )}
                    <div className={styles.flowText}>{m.text}</div>
                  </div>
                </div>
                {m.id === 25 && m.checked && (
                  <div className={styles.customer}>
                    <div className={styles.customerCell}>
                      <div className={styles.customerLabel} />
                      <div
                        role="button"
                        tabIndex={0}
                        className={styles.customerValue}
                        onClick={() => onSelectCustomer('supplier')}
                      >
                        {supplierList.length > 0 ? (
                          <span className={styles.customerList}>
                            {supplierList.map((item) => (
                              <span className={styles.customerItem}>
                                <span>{item.customerName}</span>
                                <Icon
                                  className={styles.customerItemClose}
                                  name="close"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onSupplierDelete(item.customerId);
                                  }}
                                />
                              </span>
                            ))}
                          </span>
                        ) : (
                          <span className={styles.placeholder}>选择供应商</span>
                        )}
                        <Icon className={styles.customerValueIcon} name="right" />
                      </div>
                    </div>
                    <div className={styles.customerCell}>
                      <div className={styles.customerLabel} />
                      <div
                        role="button"
                        tabIndex={0}
                        className={styles.customerValue}
                        onClick={() => onSelectCustomer('customer')}
                      >
                        {customerList.length > 0 ? (
                          <span className={styles.customerList}>
                            {customerList.map((item) => (
                              <span className={styles.customerItem}>
                                <span>{item.customerName}</span>
                                <Icon
                                  className={styles.customerItemClose}
                                  name="close"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onCustomerDelete(item.customerId);
                                  }}
                                />
                              </span>
                            ))}
                          </span>
                        ) : (
                          <span className={styles.placeholder}>选择客户</span>
                        )}
                        <Icon className={styles.customerValueIcon} name="right" />
                      </div>
                    </div>
                  </div>
                )}
              </>
            ))}
          </div>
        )}
        {regulateId === 3 && <Outlet />}
      </div>
      <Approval
        visible={approvalVis}
        onClose={() => {
          setApprovalVis(false);
        }}
        updateId={updateId}
        auditId={approvalId}
        auditName={approvalName}
      />
    </Context>
  );
}

export default Management;
