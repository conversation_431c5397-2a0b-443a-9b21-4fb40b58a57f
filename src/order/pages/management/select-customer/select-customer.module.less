@import 'styles/mixins/mixins';

.drawer {
  :global {
    .ant-drawer-body {
      display: flex;
      flex-direction: column;
    }
  }
}

.search {
  padding: 5px 0 20px;
}

.list {
  flex: 1;
  overflow: auto;
}

.item {
  display: flex;
  height: 52px;
  margin-bottom: 20px;
  padding: 0 20px;
  align-items: center;
  border-radius: 18px;
  background: #fff;
}

.itemName {
  margin-left: 5px;
  flex: 1;
  .text-overflow(1);
}

.noData {
  padding: 100px 0;
}
