import { Drawer, Search, Empty } from '@/components';
import { DrawerProps, Spin, Checkbox, Divider } from 'antd';
import { useRequest } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import { getCustomerPageAllCustomerList, supplierListMy, postOrderAuditUpdate } from '@/apis';
import { user } from '@/store';
import InfiniteScroll from 'react-infinite-scroll-component';
import debounce from 'lodash/debounce';
import isFunction from 'lodash/isFunction';
import styles from './select-customer.module.less';

export interface SelectListCustomerProps {
  customerCompanyId: number;
  customerId: number;
  customerName: string;
}

export interface SelectCustomerProps extends DrawerProps {
  type: string;
  customerList: SelectListCustomerProps[];
  supplierList: SelectListCustomerProps[];
  onConfirm: MultipleParamsFn<[arr: SelectListCustomerProps[]]>;
}

function SelectCustomer({
  type,
  customerList,
  supplierList,
  onConfirm,
  ...props
}: SelectCustomerProps) {
  const params = useRef({
    customerType: type === 'customer' ? 0 : '',
    keywords: '',
    keyword: '',
    pageNo: 1,
    pageSize: 20,
    companyId: type === 'customer' ? '' : user.companyId,
  });
  const totalPages = useRef(0);
  const [list, setList] = useState<any[]>([]);
  const [selectList, setSelectList] = useState<SelectListCustomerProps[]>(
    type === 'customer' ? customerList : supplierList
  );

  const { run, loading } = useRequest(
    // @ts-ignore
    type === 'customer' ? getCustomerPageAllCustomerList : supplierListMy,
    {
      manual: true,
      defaultParams: [{ ...params.current }],
      onSuccess: (res) => {
        totalPages.current = res.pagination.total;
        if (params.current.pageNo === 1) {
          setList(res.list);
        } else {
          setList([...list, ...res.list]);
        }
        params.current.pageNo += 1;
      },
    }
  );

  const onLoadGoods = () => {
    if (params.current.pageNo > totalPages.current) return;
    // @ts-ignore
    run({ ...params.current });
  };

  const onSearch = debounce((val: string) => {
    params.current = {
      ...params.current,
      pageNo: 1,
      keywords: val,
      keyword: val,
    };
    // @ts-ignore
    run({ ...params.current });
  }, 300);

  const onSelectCustomer = (val: boolean, id: number, name: string, companyId: number) => {
    if (val) {
      selectList.push({
        customerCompanyId: companyId,
        customerId: id,
        customerName: name,
      });
      setSelectList([...selectList]);
    } else {
      setSelectList([...selectList.filter((item) => item.customerId !== id)]);
    }
  };

  const onOk = () => {
    const paramsArr = selectList.map((item) => ({
      customerCompanyId: item.customerCompanyId,
      customerId: item.customerId,
    }));
    postOrderAuditUpdate({
      [type === 'customer' ? 'customerList' : 'supplierList']: paramsArr,
      [type === 'customer' ? 'supplierList' : 'customerList']:
        type === 'customer' ? supplierList : customerList,
      item: 25,
      option: 1,
    }).then(() => {
      if (isFunction(props.onClose)) {
        props.onClose();
      }
      onConfirm(selectList);
    });
  };

  useEffect(() => {
    setSelectList(type === 'customer' ? customerList : supplierList);
    // @ts-ignore
    run({ ...params.current });
  }, [customerList, run, supplierList, type]);

  const footer = <Drawer.Footer showCancel={false} onOk={onOk} />;

  return (
    <Drawer
      {...props}
      title={type === 'customer' ? '选择客户' : '选择供应商'}
      footer={footer}
      className={styles.drawer}
    >
      <div className={styles.search}>
        <Search placeholder="搜索" onSearch={onSearch} />
      </div>
      <div className={styles.list} id="list">
        <Spin spinning={loading}>
          <InfiniteScroll
            dataLength={list.length}
            hasMore={params.current.pageNo < totalPages.current}
            loader={
              <div className="text-center">
                <Spin tip="加载中..." />
              </div>
            }
            next={onLoadGoods}
            scrollableTarget="list"
            endMessage={
              <div className={styles.divider}>
                <Divider plain>
                  <span className={styles.endMessage}>加载到底了</span>
                </Divider>
              </div>
            }
          >
            {list.length > 0 &&
              list.map((item) => (
                <div className={styles.item} key={item.id}>
                  {selectList.map((each) => each.customerId).includes(item.customerId)}
                  <Checkbox
                    checked={selectList
                      .map((each) => each.customerId)
                      .includes(item.customerId || item.id)}
                    onChange={(e) =>
                      onSelectCustomer(
                        e.target.checked,
                        item.customerId || item.id,
                        item.customerName,
                        item.customerCompanyId
                      )
                    }
                  />
                  <span className={styles.itemName}>{item.customerName}</span>
                </div>
              ))}
            {list.length === 0 && (
              <div className={styles.noData}>
                <Empty />
              </div>
            )}
          </InfiniteScroll>
        </Spin>
      </div>
    </Drawer>
  );
}

export default SelectCustomer;
