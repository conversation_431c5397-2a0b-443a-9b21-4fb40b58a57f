import { Drawer } from '@/components';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';
import { PropsWithChildren, useEffect, useMemo, useState } from 'react';
import Empty from '@/components/empty/index';
import { Button, message } from 'antd';
import { appListResult, getWorkflowQueryApprovalList } from '@/apis/workflow';
import { getOrderAuditSetting, getOrderAuditUpdate } from '@/apis';
import { checkPermission } from '@/utils/permission';
import styles from './approval.module.less';

interface productionOrderProps {
  visible: boolean;
  onClose: () => void;
  updateId: number;
  auditId: number;
  auditName: string;
}

function Approval(props: PropsWithChildren<productionOrderProps>) {
  const { visible, onClose, auditId, auditName, updateId } = props;
  const [list, setList] = useState<appListResult[]>([]);
  const [approvalId, setApprovalId] = useState(0);
  const navigate = useNavigate();

  // 关侧拉时，判断有没有选择审批单控制按钮开关
  const updateAudit = () => {
    const updateWfAuditCfgStateLsParamList = [
      {
        id: auditId,
        state: approvalId ? 1 : 0,
      },
    ];
    getOrderAuditUpdate({ updateWfAuditCfgStateLsParamList });
  };

  const jumpTo = () => {
    if (checkPermission('D_001_001_004')) {
      navigate('/admin/workflow/design');
    } else {
      message.error('暂无权限，请联系公司管理员开通！');
    }
  };

  const getOrderList = () => {
    if (visible) {
      let updWfAuditCfgSettingLsParamsList = list
        .filter((f) => f.id === approvalId)
        .map((m) => ({
          id: updateId,
          procFrmName: m.name,
          procDefId: m.processDefinitionId,
          procDefKey: m.processDefinitionKey,
        }));
      if (!updWfAuditCfgSettingLsParamsList.length) {
        updWfAuditCfgSettingLsParamsList = [
          {
            id: updateId,
            procFrmName: '',
            procDefId: '',
            procDefKey: '',
          },
        ];
      }
      getOrderAuditSetting({ updWfAuditCfgSettingLsParamsList })
        .then(() => {
          onClose();
        })
        .finally(() => {
          setApprovalId(0);
        });
    }
  };

  const getWorkList = () =>
    getWorkflowQueryApprovalList({ formType: auditId }).then((res) => {
      setList(res.list);
      const audit = res.list.filter((s) => s.name === auditName);
      setApprovalId(audit[0].id);
    });

  const procFrmName = useMemo(() => {
    switch (auditId) {
      case 22:
        return '确认接单';
      case 23:
        return '备货';
      case 24:
        return '发货';
      case 25:
        return '到货验收';
      case 30:
        return '安装完成';
      case 31:
        return '安装验收';
      case 32:
        return '结算验收';
      case 33:
        return '质保期结束';
      case 34:
        return '付预付款';
      case 35:
        return '付发货款';
      case 36:
        return '付验收款';
      case 37:
        return '付安装款';
      case 38:
        return '付质保款';
      case 39:
        return '付结算款';
      default:
        return '';
    }
  }, [auditId]);

  useEffect(() => {
    if (visible && auditId) getWorkList();
  }, [auditId, visible]); // eslint-disable-line

  return (
    <Drawer
      title="选择审批流程"
      visible={visible}
      onClose={() => {
        updateAudit();
        onClose();
        setApprovalId(0);
      }}
      className={styles.approval}
      extra={
        list.length ? (
          <div className={styles.headText} role="button" tabIndex={0} onClick={jumpTo}>
            新建
          </div>
        ) : undefined
      }
      footer={
        !!list.length && (
          <Drawer.Footer
            okText="确定"
            onOk={getOrderList}
            showCancel={false}
            disabled={approvalId === 0}
          />
        )
      }
    >
      <div className={styles.text}>只展示包含{procFrmName}申请套件的审批流程</div>
      {list.length > 0 ? (
        <div className={classNames(styles.approval)}>
          {list.map((m) => (
            <div
              role="button"
              tabIndex={m.id}
              className={classNames(styles.card, approvalId === m.id ? styles.cardChoose : '')}
              onClick={() => {
                setApprovalId(m.id);
              }}
              key={m.appId}
            >
              <div className={styles.cardLeft}>
                <img src={m.icon} alt="" />
                <span>{m.name}</span>
              </div>
              <div className={styles.cardRight}>
                {/* <span className={styles.harp}>预览</span> */}
                {/* <span>{approvalId === m.id ? '已选' : '选择'}</span> */}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <Empty
          type="content"
          message="暂无审批流程"
          description="如需审批，请点击“创建审批”"
          style={{ position: 'fixed', top: '50%', left: '50%', transform: 'translate(-50%, -50%)' }}
        >
          <Button type="primary" size="small" onClick={jumpTo}>
            创建审批
          </Button>
        </Empty>
      )}
    </Drawer>
  );
}
export default Approval;
