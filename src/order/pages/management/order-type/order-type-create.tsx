import { useEffect, useState } from 'react';
import { modalPopup } from '@/utils/popup';
import { Modal } from '@/components';
import { ModalProps, Input, message } from 'antd';
import { orderTypeCreateApi } from '@/apis';
import isFunction from 'lodash/isFunction';
import debounce from 'lodash/debounce';

interface OrderTypeCreateProps extends ModalProps {
  id?: number;
  name?: string;
  onSuccess: () => void;
}

function OrderTypeCreate({ id, name, onSuccess, ...props }: OrderTypeCreateProps) {
  const [orderTypeName, setOrderTypeName] = useState(name || '');

  const onOk = debounce(() => {
    if (!orderTypeName.length) {
      message.warning('请输入订单类型名称');
      return;
    }
    orderTypeCreateApi({ id: id || null, orderTypeName }).then(() => {
      onSuccess();
      message.success(id ? '编辑成功' : '新增成功');
      if (isFunction(props.onCancel)) {
        props.onCancel();
      }
    });
  }, 300);

  useEffect(() => {
    setOrderTypeName(name || '');
  }, [name]);

  return (
    <Modal title="新建订单类型" {...props} centered width={372} onOk={onOk}>
      <Input
        placeholder="请输入订单类型名称"
        value={orderTypeName}
        onChange={(e) => setOrderTypeName(e.target.value)}
      />
    </Modal>
  );
}

OrderTypeCreate.defaultProps = {
  id: 0,
  name: '',
};

function orderTypeCreate(config: OrderTypeCreateProps) {
  modalPopup(OrderTypeCreate, {
    ...config,
  });
}

export default orderTypeCreate;
