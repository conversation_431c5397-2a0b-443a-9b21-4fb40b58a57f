import { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { Dropdown, Menu, Spin, message } from 'antd';
import { Icon, Empty, Modal } from '@/components';
import { orderTypeList, OrderTypeListData, orderTypeDelete } from '@/apis';
import InfiniteScroll from 'react-infinite-scroll-component';
import orderTypeCreate from './order-type-create';
import styles from './order-type.module.less';

const handleItems = [
  {
    key: '1',
    label: '编辑',
  },
  {
    key: '2',
    label: '删除',
  },
];

function OrderType() {
  const params = useRef({
    pageNo: 1,
    pageSize: 15,
    keyword: '',
    isPaging: true,
  });
  const totalPages = useRef(0);
  const [list, setList] = useState<OrderTypeListData[]>([]);

  const { run, loading } = useRequest(orderTypeList, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      totalPages.current = res.pagination.total;
      if (params.current.pageNo === 1) {
        setList([...res.list]);
      } else {
        setList([...list, ...res.list]);
      }
      params.current.pageNo += 1;
    },
  });

  const onLoadMore = () => {
    if (!(params.current.pageNo <= totalPages.current)) return;
    run({ ...params.current });
  };

  const onCreate = (id?: number, name?: string) => {
    orderTypeCreate({
      id,
      name,
      onSuccess: () => {
        params.current = {
          ...params.current,
          pageNo: 1,
        };
        run({ ...params.current });
      },
    });
  };

  const onHandle = (key: string, id: number, name: string) => {
    if (key === '1') {
      onCreate(id, name);
    }
    if (key === '2') {
      Modal.confirm({
        title: '提示',
        content: '确定删除该订单标签吗？',
        okText: '确定',
        cancelText: '取消',
        centered: true,
        onOk: () => {
          orderTypeDelete({ id }).then(() => {
            message.success('删除成功');
            params.current = {
              ...params.current,
              pageNo: 1,
            };
            run({ ...params.current });
          });
        },
      });
    }
  };

  useEffect(() => {
    run({ ...params.current });
  }, [run]);

  return (
    <div className={styles.box}>
      <div className={styles.title}>
        <span className={styles.titleText}>订单标签</span>
        <span className={styles.addBtn}>
          <Icon name="plus" />
          <span role="button" tabIndex={0} className="ml-1" onClick={() => onCreate()}>
            新建订单标签
          </span>
        </span>
      </div>
      <div className={styles.list} id="list-scroll">
        <Spin spinning={loading} wrapperClassName={styles.spin}>
          <InfiniteScroll
            scrollableTarget="list-scroll"
            dataLength={list.length}
            hasMore={params.current.pageNo < totalPages.current}
            loader={
              <div className={styles.loader}>
                <Spin tip="加载中..." />
              </div>
            }
            endMessage={null}
            next={onLoadMore}
          >
            {list.length > 0 &&
              list.map((item) => (
                <div className={styles.item} key={item.id}>
                  <span className={styles.orderTypeName} title={item.orderTypeName}>
                    {item.orderTypeName}
                  </span>
                  <Dropdown
                    overlay={
                      <Menu
                        onClick={({ key }) => onHandle(key, item.id, item.orderTypeName)}
                        className={styles.menu}
                        items={handleItems}
                      />
                    }
                    placement="bottom"
                  >
                    <Icon className={styles.handle} name="zu13366" />
                  </Dropdown>
                </div>
              ))}
          </InfiniteScroll>
          {list.length === 0 && (
            <div className={styles.noData}>
              <Empty />
            </div>
          )}
        </Spin>
      </div>
    </div>
  );
}

export default OrderType;
