@import 'styles/mixins/mixins';

.box {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 0 16px;
  flex-direction: column;
  background: #f5f6fa;
}

.title {
  display: flex;
  padding: 20px 0;
  justify-content: space-between;
}

.titleText {
  font-size: 16px;
  font-weight: 600;
}

.addBtn {
  color: #008cff;
  font-size: 14px;
  cursor: pointer;
}

.list {
  flex: 1;
  overflow: auto;
}

.spin {
  width: 100%;
  height: 100%;

  :global {
    & .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}

.noData {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.item {
  display: flex;
  height: 52px;
  margin-bottom: 20px;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
  border-radius: 10px;
  background: #fff;
}

.orderTypeName {
  max-width: 500px;
  .text-overflow();
}

.handle {
  color: #999eb2;
  font-size: 22px;
  cursor: pointer;
}

.menu {
  :global {
    .ant-dropdown-menu-item {
      text-align: center;
    }
  }
}

.modal {
  :global {
    .ant-modal-confirm .ant-modal-body {
      padding: 0 20px;
    }

    .ant-modal-confirm-body > .anticon {
      display: none;
    }
  }
}
