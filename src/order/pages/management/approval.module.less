.approval {
  .headText {
    color: #008cff;
    font-size: 16px;
    cursor: pointer;
  }

  .btnChecked {
    color: #fcfcfd;
    background: #c6ccd8;
    padding: 24px 20px;
    text-align: center;
    cursor: not-allowed;
  }

  .text {
    color: #888b98;
    font-size: 14px;
    font-weight: normal;
    line-height: 17px;
    margin-top: 12px;
    margin-bottom: 8px;
    letter-spacing: 0;
  }

  .card {
    display: flex;
    height: 64px;
    margin: 20px 0;
    padding: 16px;
    justify-content: space-between;
    align-items: center;
    border-radius: 18px;
    background: #fff;
    box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
    border: 1px solid #fff;
    cursor: pointer;

    .cardLeft {
      display: flex;
      align-items: center;

      img {
        width: 32px;
        height: 32px;
        margin-right: 12px;
      }
    }

    .cardRight {
      color: #008cff;
      font-size: 14px;
      display: flex;
      align-items: center;

      .harp {
        margin-right: 6px;
        padding-right: 6px;
        position: relative;
      }

      .harp::after {
        content: '';
        width: 1px;
        height: 12px;
        position: absolute;
        top: 3px;
        right: -1px;
        background: #008cff;
      }

      span {
        cursor: pointer;
      }
    }
  }

  .cardChoose {
    position: relative;
    border: 1px solid #008cff;
    transition: background 0.5s cubic-(0.645, 0.045, 0.355, 1);
  }

  .cardChoose::before {
    content: '';
    width: 42px;
    height: 39px;
    position: absolute;
    top: -1px;
    right: -1px;
    background-image: url(/src/order/img/selected-mark.png);
  }
}
