.card {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-select:not(.ant-select-customize-input) .ant-select-selector {
      background-color: #f5f6fa;
    }
  }

  .cardLeft {
    width: 20%;
    min-width: 160px;
    height: 100%;
    padding: 20px 0;
    overflow: auto;
    border-right: 1px solid #f3f3f3;

    .regulate {
      color: #040919;
      font-size: 16px;
      font-weight: 500;
      height: 48px;
      line-height: 48px;
      padding-left: 30px;
      cursor: pointer;
    }

    .regulateForce {
      position: relative;
      background: #f5f6fa;
      transition: background 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .regulateForce::after {
      content: '';
      width: 4px;
      height: 31px;
      position: absolute;
      top: 9px;
      left: 0;
      border-radius: 0 2px 2px 0;
      background: #008cff;
    }
  }

  .cardRight {
    width: 80%;
    height: 99%;
    padding: 30px 0;
    overflow: auto;

    .flow {
      display: flex;
      margin-bottom: 30px;

      .flowName {
        color: #040919;
        font-size: 16px;
        min-width: 125px;
        margin-right: 10px;
        padding-left: 32px;
        text-align: right;
      }

      .flowText {
        color: #888b98;
        font-size: 14px;
        margin-top: 10px;
      }

      .open {
        display: flex;
        align-items: center;
        margin-top: 12px;
      }
    }

    .switch {
      display: flex;
      align-items: center;
      margin-left: 24px;
      cursor: pointer;
    }
  }
}

.days {
  display: flex;
  align-items: center;

  :global {
    .ant-input-number-input {
      text-align: center;
    }
  }
}

.customerCell {
  display: flex;
  margin-bottom: 20px;
  padding-right: 20px;
}

.customerLabel {
  font-size: 16px;
  display: inline-block;
  width: 175px;
  line-height: 32px;
  margin-right: 10px;
  text-align: right;
}

.customerValue {
  display: flex;
  height: 32px;
  line-height: 32px;
  padding: 0 12px;
  justify-content: space-between;
  flex: 1;
  border: 1px solid #b1b3be;
  border-radius: 6px;
  cursor: pointer;
}

.customerList {
  overflow: hidden;
}

.customerItem {
  display: inline-block;
  height: 24px;
  line-height: 23px;
  margin-right: 4px;
  padding: 0 8px;
  background: #f5f6fa;
  border-radius: 4px;
}

.customerItemClose {
  font-size: 18px;
  margin-left: 8px;
}

.placeholder {
  color: #b1b3be;
}
