import { useEffect, useState } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { MenuProps, Menu } from 'antd';
import { Icon } from '@/components';
import { testPerm } from '@/utils/permission';
import classNames from 'classnames';
import { getOrderSaleOldNum } from '@/apis/order';
import styles from './index.module.less';

interface countItemParams {
  num: number;
  text: string;
  value: number;
}
function OrderInfo() {
  const navigate = useNavigate();
  const items: MenuProps['items'] = [
    {
      label: '销售订单',
      key: 1,
    },
    {
      label: '采购订单',
      key: 0,
    },
  ];
  const [countItem, setCountItem] = useState<countItemParams[]>([]);
  const [current, setCurrent] = useState('1');
  const [pendingNum, setPendingNum] = useState<number>(0);

  const onClick: MenuProps['onClick'] = (e) => {
    setCurrent(e.key);
  };

  const jumpTo = (status: number) => {
    if (status === 1 && !testPerm('R_001_011')) return;
    if (status === 0 && !testPerm('R_001_012')) return;
    if (status === 2 && !testPerm('R_001_013')) return;
    if (status === 80 && !testPerm('R_001_014')) return;
    if (status === 90 && !testPerm('R_001_015')) return;
    navigate(`/indent/list?orderStatus=${status}&region=${current}&open=0`);
  };

  useEffect(() => {
    getOrderSaleOldNum({ ordType: Number(current) ? 0 : 1 }).then((res) => {
      setPendingNum(res.pendingNum);
      setCountItem([
        {
          num: res.total,
          text: `总订单`,
          value: 0,
        },
        {
          num: res.processingNum,
          text: `进行中`,
          value: 2,
        },
        {
          num: res.completedNum,
          text: `已完成`,
          value: 80,
        },
        {
          num: res.closeNum,
          text: `已关闭`,
          value: 90,
        },
      ]);
    });
  }, [current]);

  return (
    <div className={classNames(styles.card, styles.cardBlue)}>
      <div className={styles.head}>
        <Menu
          onClick={onClick}
          selectedKeys={[current]}
          mode="horizontal"
          items={items}
          style={{ color: '#d1e0fe' }}
        />
        <Link to={`/indent/create?orderProperty=${Number(current) === 1 ? 2 : 1}&orderType=1`}>
          <div className={styles.btn}>
            <Icon name="plus" className="mr-1" />
            新建
          </div>
        </Link>
      </div>
      <div className={styles.countAll}>
        <div className={styles.number}>{pendingNum}</div>
        <div
          className={styles.text}
          role="button"
          tabIndex={0}
          onClick={() => {
            jumpTo(1);
          }}
        >
          待处理
          <Icon name="right" size={18} style={{ cursor: 'pointer' }} />
        </div>
      </div>
      <div className={styles.countItem}>
        {countItem.map((m) => (
          <div
            className={styles.count}
            role="button"
            tabIndex={0}
            key={m.value}
            onClick={() => {
              jumpTo(m.value);
            }}
          >
            <div className={styles.number}>{m.num}</div>
            <div className={styles.text}>
              {m.text} <Icon name="right" size={18} style={{ cursor: 'pointer', opacity: '0.7' }} />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default OrderInfo;
