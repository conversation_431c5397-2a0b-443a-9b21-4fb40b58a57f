import { DatePicker, Echarts, Empty, Icon } from '@/components';
import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useState } from 'react';
import { getOrderLineGraph } from '@/apis/order';
import { checkPerm, checkPermission } from '@/utils/permission';
import { useEcharts, usePermission } from '@/hooks';
import styles from './index.module.less';

const list = [
  {
    skuName: '销售额',
    totalNum: [900, 500, 600, 500, 2100, 2400, 1600, 2000, 1700, 1750, 2300, 2300],
    startColor: 'rgba(45,213,145,0.20)',
    endColor: 'rgba(51,214,148,0.00)',
    color: '#06c88f',
  },
  {
    skuName: '客单价',
    totalNum: [500, 1400, 900, 1500, 1200, 1700, 1600, 1100, 1700, 1750, 2000, 2000],
    startColor: 'rgba(14,118,254,0.20)',
    endColor: 'rgba(15,118,255,0.00)',
    color: '#457af7',
  },
];

function IndentManagement() {
  const [chartList, setChartList] = useState(list);
  const [type, setType] = useState<string>(dayjs(new Date()).format('YYYY'));
  const query = useEcharts();

  const onChange = (_date: Dayjs | null, dateString: string) => {
    if (!checkPermission('R_001_016')) return;
    setType(dateString);
  };

  const customFormat = (value: Dayjs) => `${value.format('YYYY年')}`;

  // 价格千分位
  const toPrice = (num: number | string) => {
    if (typeof num === 'number' && num % 1 === 0) {
      return `${num}`.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,');
    }
    return `${num}`.replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
  };

  const datePerm = usePermission('R_001_016');

  const chartPerm = usePermission('R_001_006_002');

  useEffect(() => {
    getOrderLineGraph({ ordType: 0, yearFormat: type }).then((res) => {
      const graph = chartList;
      graph[0].totalNum = res.saleLine.map((m) => m.value);
      graph[1].totalNum = res.customerUnitPriceLine.map((m) => m.value);
      setChartList(graph);
      query.current?.setOption((echarts) => ({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none',
          },
          formatter(
            params: {
              name: string;
              value: number;
            }[]
          ) {
            let html = `<div>${params[0].name}月</div>`;
            const data = chartList.map(
              (m, index) =>
                `<div style="display:flex;align-items:center">
                  <span style="width: 6px; height: 6px ;border-radius: 50%;background: ${
                    m.color
                  };display: inline-block"></span>
                  <span style="margin-left:4px">${toPrice(params[index].value)}</span>
                </div>`
            );
            html += data.join('');
            return html;
          },
        },
        grid: {
          left: '1%',
          right: '1%',
          bottom: '3%',
          containLabel: true,
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
          axisLabel: {
            color: '#888B98',
          },
        },
        yAxis: {
          type: 'value',
          min: chartList.every((e) => e.totalNum.length) ? null : 0,
          max: chartList.every((e) => e.totalNum.length) ? null : 2500,
          splitLine: {
            lineStyle: {
              type: 'dotted',
              color: 'rgba(177, 179, 190, 0.3)',
            },
          },
        },
        series: chartList.map((item) => {
          if (item.totalNum.length) {
            return {
              type: 'line',
              lineStyle: {
                color: item.color,
              },
              itemStyle: {
                normal: {
                  color: item.color,
                  borderColor: '#fff',
                  borderType: 'solid',
                  borderWidth: 2,
                },
                lineStyle: {
                  width: 20,
                  type: 'dotted',
                },
              },
              areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: item.startColor,
                  },
                  {
                    offset: 1,
                    color: item.endColor,
                  },
                ]),
              },
              data: item.totalNum,
              showSymbol: false,
              symbol: 'circle',
              symbolSize: 15,
            };
          }
          return {};
        }),
      }));
    });
  }, [type]); // eslint-disable-line

  return (
    <div className={classNames(styles.card, styles.management)}>
      <div className={styles.head}>
        <div className={styles.text}>销售统计</div>
        <DatePicker
          picker="year"
          defaultValue={dayjs(new Date())}
          onChange={onChange}
          allowClear={false}
          format={customFormat}
          suffixIcon={<Icon name="down" style={{ color: '#999EB2' }} />}
          style={{ width: '96px' }}
          onClick={datePerm}
        />
      </div>
      {checkPerm('R_001_006_002') ? (
        <div className={styles.rel}>
          <Echarts ref={query} className={styles.static} />
          <div className={styles.staticFoo}>
            {chartList.map((m) => (
              <div className={styles.shopText} key={m.skuName}>
                <div className={styles.circle} style={{ background: m.color }} />
                <div className={styles.circleText}>{m.skuName}</div>
              </div>
            ))}
          </div>
        </div>
      ) : (
        <div role="button" tabIndex={0} style={{ height: '280px' }} onClick={chartPerm}>
          <div className={styles.reportStates}>
            {chartList.map((m) => (
              <div className={styles.shopText} key={m.skuName}>
                <div className={styles.circle} style={{ background: m.color }} />
                <div className={styles.circleText}>{m.skuName}</div>
              </div>
            ))}
          </div>
          <Empty
            type="content"
            message=""
            description="暂无数据"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
            }}
            imageStyle={{
              width: '80%',
              height: '100%',
            }}
          />
        </div>
      )}
    </div>
  );
}

export default IndentManagement;
