import { Empty, DatePicker, Icon, Echarts } from '@/components';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { getOrderTopGoods, pirListResult } from '@/apis/order';
import { checkPerm, checkPermission } from '@/utils/permission';
import { useEcharts, usePermission } from '@/hooks';
import styles from './index.module.less';

const colors = ['#A45DF5', '#FF82AC', '#FFBB38', '#16DBCC', '#008CFF'];

function SalesFive() {
  const query = useEcharts();
  const [chartList, setChartList] = useState<pirListResult[]>([]);
  const [dateType, setDateType] = useState<string>(dayjs(new Date()).format('YYYY-MM'));
  const [queryType, setQueryType] = useState<number>(0);

  const onChange = (_date: Dayjs | null) => {
    if (!checkPermission('R_001_017')) return;
    setDateType(dayjs(_date).format('YYYY-MM'));
  };

  const customFormat = (value: Dayjs) => `${value.format('YYYY年M月')}`;

  // 价格千分位
  const toPrice = (num: number | string) => {
    if (typeof num === 'number' && num % 1 === 0) {
      return `${num}`.replace(/\d{1,3}(?=(\d{3})+$)/g, '$&,');
    }
    return `${num}`.replace(/(\d)(?=(\d{3})+\.)/g, '$1,');
  };

  const datePerm = usePermission('R_001_017');

  const chartPerm = usePermission('R_001_006_003');

  useEffect(() => {
    getOrderTopGoods({ ordType: 0, yearDateFormat: dateType, queryType }).then((res) => {
      setChartList(res.pieList);
      let max = 0;
      res.pieList.forEach((item) => {
        if (item.value >= max) {
          max = item.value;
        }
      });
      const number = [0.9, 0.8, 0.7, 0.6, 0.5];
      const option = {
        tooltip: {
          position: 'right',
          formatter(params: { data: { name: string; itemValue: number } }) {
            // @ts-ignore
            return `
              <div>
                  <div style="color:#040919;text-align: left">数量 ${toPrice(
                    params.data.itemValue
                  )}</div>
                  <div style="color:#888b98;text-align: left">${params.data.name}</div>
              </div>
            `;
          },
        },
        series: [
          {
            name: 'Area Mode',
            type: 'pie',
            radius: [10, 56],
            roseType: 'area',
            label: {
              show: false,
            },
            data: res.pieList.map((item, index) => {
              if (item.value) {
                return {
                  name: item.goodsTypeName,
                  value: number[index],
                  itemValue: item.value,
                  itemStyle: {
                    color: colors[index],
                  },
                };
              }
              return {};
            }),
          },
        ],
      };
      query.current?.setOption(option);
    });
  }, [dateType, queryType]); // eslint-disable-line

  return (
    <div className={classNames(styles.card, styles.salesFive)}>
      <div className={styles.top}>
        <div className={styles.sort}>
          <span
            className={queryType === 0 ? styles.head : styles.text}
            role="button"
            tabIndex={0}
            onClick={() => {
              setChartList([]);
              setQueryType(0);
            }}
          >
            销售前五商品
          </span>
          <span
            className={queryType === 1 ? styles.head : styles.text}
            role="button"
            tabIndex={0}
            onClick={() => {
              setChartList([]);
              setQueryType(1);
            }}
          >
            销售前五分类
          </span>
        </div>
        <DatePicker
          picker="month"
          defaultValue={dayjs(new Date())}
          format={customFormat}
          onChange={onChange}
          allowClear={false}
          suffixIcon={<Icon name="down" />}
          style={{ width: '117px' }}
          onClick={datePerm}
        />
      </div>
      <div
        className={styles.bottom}
        role="button"
        tabIndex={0}
        onClick={() => {
          if (!checkPerm('R_001_006_003')) {
            chartPerm();
          }
        }}
      >
        {chartList.length && checkPerm('R_001_006_003') ? (
          <>
            <div className={styles.bottomLeft}>
              <Echarts ref={query} className={styles.salesEch} />
              <div className={styles.shop}>{queryType === 0 ? '销售前五商品' : '销售前五分类'}</div>
            </div>
            <div className={styles.bottomRight}>
              {chartList.map((m, mIndex) => (
                <div className={styles.shopText} key={m.goodsTypeName}>
                  <div className={styles.circle} style={{ background: colors[mIndex] }} />
                  <div className={styles.circleText}>{m.goodsTypeName}</div>
                </div>
              ))}
            </div>
          </>
        ) : (
          <Empty
            type="content"
            message=""
            description="暂无数据"
            style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
            }}
            imageStyle={{
              width: '80%',
              height: '100%',
            }}
          />
        )}
      </div>
    </div>
  );
}

export default SalesFive;
