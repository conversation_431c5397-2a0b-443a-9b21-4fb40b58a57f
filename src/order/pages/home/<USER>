import { useEffect } from 'react';
import { Col, Row } from 'antd';
import { Context } from '@/components';
import { sessions } from '@/store';
import Management from './management';
import OrderInfo from './order-info';
import Schedule from './schedule';
import SalesFive from './sales-five';
import IconNav from './icon-nav';

function Order() {
  useEffect(() => {
    sessions.select('orderCode');
  }, []);

  return (
    <Context
      container
      permission={{ code: 'R_001', newLogic: true }}
      theme={null}
      head={<Context.Head title="订单管理" />}
    >
      <Row gutter={[20, 20]}>
        <Col md={14} lg={12} xs={24}>
          {/* 销售采购订单 */}
          <OrderInfo />
        </Col>
        <Col md={10} lg={12} xs={24}>
          {/* 生产完成进度 */}
          <Schedule />
        </Col>
        <Col span={24}>
          {/* 销售统计 */}
          <Management />
        </Col>
        <Col md={14} xs={24}>
          {/* 销售前五商品 */}
          <SalesFive />
        </Col>
        <Col md={10} xs={24}>
          {/* icon跳路由 */}
          <IconNav />
        </Col>
      </Row>
    </Context>
  );
}

export default Order;
