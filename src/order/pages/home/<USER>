import { getOrderProgress } from '@/apis/order';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import styles from './index.module.less';

function Schedule() {
  const [sum, setSum] = useState([
    {
      number: 175,
      value: '计划数量',
    },
    {
      number: 125,
      value: '完成数量',
    },
  ]);

  useEffect(() => {
    getOrderProgress().then((res) => {
      setSum([
        {
          number: res.plansNum,
          value: '计划数量',
        },
        {
          number: res.completedNum,
          value: '完成数量',
        },
      ]);
    });
  }, []);

  return (
    <div className={classNames(styles.card, styles.schedule)}>
      <div className={styles.head}>生产完成进度</div>
      <div className={styles.progress}>
        <div
          className={styles.strip}
          style={{
            width: `${
              // eslint-disable-next-line no-nested-ternary
              Math.floor((sum[1].number / sum[0].number) * 100) >= 100
                ? 100
                : Math.floor((sum[1].number / sum[0].number) * 100)
                ? Math.floor((sum[1].number / sum[0].number) * 100)
                : 0
            }%`,
          }}
        >
          <div className={styles.centum}>{`${
            Math.floor((sum[1].number / sum[0].number) * 100)
              ? Math.floor((sum[1].number / sum[0].number) * 100)
              : 0
          }%`}</div>
        </div>
      </div>
      <div className={styles.plan}>
        {sum.map((m, index) => (
          <div className={styles.bottom} key={`${index + 1}`}>
            <div className={styles.number}>{m.number}</div>
            <div className={styles.value}>{m.value}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Schedule;
