/* stylelint-disable */
@import 'styles/mixins/mixins';

.navigation {
  color: #040919;
  font-size: 24px;
  font-weight: 500;
  width: 96px;
  height: 24px;
  line-height: 24px;
  margin: 12px 0;
  letter-spacing: 0;
}

.card {
  padding: 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-menu {
      background: rgb(0 0 0 / 0%);
    }

    .ant-menu-title-content {
      font-size: 16px;
    }

    .ant-menu-horizontal {
      border-bottom: 0;
      line-height: 10px;

      &:not(.ant-menu-dark) {
        > .ant-menu-item-selected,
        > .ant-menu-item:hover {
          color: #fff;
        }

        > .ant-menu-item-selected::after,
        > .ant-menu-item-active::after {
          border-bottom: 2px solid rgb(0 0 0 / 0%);
          left: 37px;
          transition: background 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        }

        > .ant-menu-item-selected::after {
          width: 20px;
          height: 4px;
          background: #fff;
          border-radius: 85px;
        }
      }
    }

    .ant-btn-sm {
      width: 56px;
      height: 24px;
      border-radius: 6px;
      box-sizing: border-box;
      border: 1px solid #fff;
    }

    .ant-btn.ant-btn-background-ghost {
      border: 1px solid #fff;
    }
  }

  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .btn {
    color: #fff;
    font-size: 12px;
    display: flex;
    width: 56px;
    height: 24px;
    justify-content: center;
    box-sizing: border-box;
    align-items: center;
    text-align: center;
    border-radius: 6px;
    border: 1px solid #fff;
    cursor: pointer;
  }

  .countAll {
    margin: 24px 0;
    padding-left: 16px;

    .number {
      color: #fff;
      font-size: 32px;
      font-weight: 500;
      margin-bottom: 8px;
    }

    .text {
      color: #fff;
      font-size: 14px;
      display: flex;
      align-items: center;
      cursor: pointer;
      opacity: 0.7;
    }
  }

  .count {
    display: inline-block;
    margin-right: 16px;
    cursor: pointer;

    &:first-child {
      padding-right: 16px;
      position: relative;

      &::after {
        content: '';
        width: 1px;
        height: 20px;
        position: absolute;
        top: 20px;
        right: 0;
        background-color: rgb(255 255 255 / 20%);
      }
    }

    .number {
      color: #fff;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 8px;
      padding-right: 18px;
      text-align: center;
    }

    .text {
      color: #fff;
      font-size: 14px;
      display: flex;
      align-items: center;
      opacity: 0.7;
    }
  }

  .countItem {
    // display: flex;
    // align-items: center;
    white-space: nowrap;
    padding-left: 16px;
  }
}

.head {
  color: #040919;
  font-size: 18px;
  font-weight: 500;
  line-height: 26px;
  letter-spacing: 0;
}

.schedule {
  height: 248px;

  .progress {
    height: 10px;
    border-radius: 86px;
    background: #f5f6fa;
    margin-top: 70px;
    margin-bottom: 40px;
  }

  .strip {
    width: 70%;
    height: 100%;
    position: relative;
    border-radius: 86px;
    background: linear-gradient(270deg, #05d380 0%, #05d380 0%, #05bed3 99%, #05bed3 100%);
  }

  .centum {
    color: #fff;
    width: 44px;
    height: 25px;
    line-height: 25px;
    position: absolute;
    top: -34px;
    right: -28px;
    text-align: center;
    border-radius: 4px;
    opacity: 0.8;
    background: #040919;
    box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  }

  .centum::after {
    content: '';
    width: 6px;
    height: 6px;
    position: absolute;
    top: 21px;
    left: 20px;
    transform: rotate(134deg);
    opacity: 0.8;
    background: #040919;
    box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  }

  .plan {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }

  .bottom {
    display: flex;
    flex-direction: column;
    align-items: center;

    .number {
      color: #040919;
      font-size: 18px;
      margin-bottom: 8px;
    }

    .value {
      color: #888b98;
      font-size: 14px;
    }
  }
}

.salesFive {
  height: 362px;

  .top {
    display: flex;
    margin-bottom: 8px;
    justify-content: space-between;
    align-items: center;
  }

  .sort {
    display: flex;
    align-items: center;
  }

  .head {
    margin-right: 20px;
    cursor: pointer;
  }

  .text {
    color: #888b98;
    font-size: 14px;
    font-weight: 500;
    margin-right: 20px;
    cursor: pointer;
  }

  .bottom {
    display: flex;
    height: 270px;
    position: relative;
    align-items: center;
  }

  .bottomLeft {
    width: 130px;
    margin-right: 45px;
  }

  .bottomRight {
    width: 100%;
    overflow: hidden;
  }

  .salesEch {
    width: 130px;
    height: 130px;
    line-height: 130px;
    text-align: center;
  }

  .shop {
    color: #888b98;
    font-size: 12px;
    margin-top: 14px;
    text-align: center;
  }

  .shopText {
    display: flex;
    align-items: center;
    margin-top: 16px;
  }

  .circle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .circleText {
    color: #040919;
    font-size: 14px;
    width: 90%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.iconNav {
  height: 362px;

  .img {
    display: flex;
    height: 84px;
    margin: 5px 0;
    justify-content: space-between;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    & img {
      width: 64px;
      height: 64px;
    }
  }
}

.management {
  height: 353px;

  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .text {
    color: #040919;
    font-size: 18px;
    font-weight: 500;
    margin-left: 8px;
  }

  .static {
    width: 100%;
    height: 280px;
  }

  .rel {
    position: relative;
  }

  .staticFoo {
    display: flex;
    margin-left: 8px;
    position: absolute;
    top: 14px;
    left: 0;
    align-items: center;
  }

  .shopText {
    display: flex;
    align-items: center;
    margin-right: 54px;
  }

  .circle {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 4px;
  }

  .circleText {
    color: #888b98;
    // font-size: 12px;
  }
}

.cardBlue {
  height: 248px;
  padding: 16px 20px 20px 5px;
  background: linear-gradient(58deg, #1b38fa 2%, #1b38fa 2%, #1b87fa 97%, #1b87fa 97%);
}

.reportStates {
  display: flex;
  align-items: center;
  padding: 8px 0 0 8px;
}
