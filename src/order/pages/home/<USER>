import { Col, Row } from 'antd';
import { useNavigate, useSearchParams } from 'react-router-dom';
import classNames from 'classnames';
import { checkCharge, testPerm } from '@/utils/permission';
import salesPurchaseCreate from '@@/order/containers/sales-purchase-create';
import { useEffect } from 'react';
import styles from './index.module.less';
import sales from '../../img/sale.png';
import purch from '../../img/purch.png';
import production from '../../img/production.png';
import baskStage from '../../img/baskStage.png';
import bill from '../../img/bill.png';
// import examine from '../../img/examine.png';

const iconItem = [
  {
    name: '销售订单',
    img: sales,
    url: '/indent/list?orderStatus=0&region=1&open=0',
    code: ['R_001_001', 'R_001_009', 'B_001_002_001'],
  },
  {
    name: '采购订单',
    img: purch,
    url: '/indent/list?orderStatus=0&region=0&open=0',
    code: ['R_001_002', 'R_001_010', 'B_001_003_001'],
  },
  {
    name: '生产订单',
    img: production,
    url: '/indent/production',
    code: 'R_001_007',
  },
  {
    name: '分单管理',
    img: bill,
    url: '/order/split/order/list',
    code: 'R_001_005',
  },
  // {
  //   name: '订单核查',
  //   img: examine,
  //   url: '/order/verification/list',
  // },
  {
    name: '采购退货单',
    img: 'https://img.huahuabiz.com/user_files/2023215/1676428338288854.png',
    url: '/indent/list?stockType=2&region=0',
    code: 'R_001_022',
  },
  {
    name: '销售退货单',
    img: 'https://img.huahuabiz.com/user_files/2023215/1676428338284820.png',
    url: '/indent/list?stockType=1&region=1',
    code: 'R_001_021',
  },
  {
    name: '以销定采',
    img: 'https://img.huahuabiz.com/user_files/202359/1683601478763998.png',
    url: '1',
    code: 'R_001_023',
  },
  {
    name: '查询报表',
    img: 'https://img.huahuabiz.com/user_files/20231221/1703137921457450.png',
    url: '/indent/report/buy?orderProperty=1',
    code: 'R_001_026',
  },
  {
    name: '后台管理',
    img: baskStage,
    url: '/indent/management',
    code: 'R_001_008',
  },
];

function IconNav() {
  const [params] = useSearchParams({ formCode: '' });
  const isSellOnBuy = params.get('isSellOnBuy') || '';
  const navigate = useNavigate();

  const iconJump = (url: string, code: string | string[]) => {
    if (code === 'R_001_026') {
      if (!checkCharge('R_001_026_002') && checkCharge('R_001_026_001')) {
        navigate(`/indent/report/sell?orderProperty=2`);
        return;
      }
      if (!checkCharge('R_001_026_002') && !checkCharge('R_001_026_001')) {
        testPerm('R_001_026_001');
        return;
      }
    }
    if (url === '1') {
      if (!testPerm(code)) {
        return;
      }
      salesPurchaseCreate({
        onOk: () => navigate('/indent/submit'),
      });
      return;
    }
    if (testPerm(code)) {
      navigate(url);
    }
  };

  useEffect(() => {
    if (isSellOnBuy) {
      salesPurchaseCreate({
        onOk: () => navigate('/indent/submit'),
      });
    }
  }, [isSellOnBuy, navigate]);

  return (
    <div className={classNames(styles.card, styles.iconNav)}>
      <Row gutter={[20, 22]}>
        {iconItem.map((m) => (
          <Col span={8} key={m.name}>
            <div
              role="button"
              tabIndex={0}
              className={styles.img}
              onClick={() => {
                iconJump(m.url, m.code);
              }}
            >
              <img src={m.img} alt="" />
              <div>{m.name}</div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  );
}

export default IconNav;
