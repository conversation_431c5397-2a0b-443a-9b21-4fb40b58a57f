import { RouteObject } from 'react-router-dom';

const routes: RouteObject[] = [
  // {
  //   path: 'stock',
  //   element: lazy(() => import('./pages/home')),
  // },
  // {
  //   path: 'stock/query',
  //   element: lazy(() => import('./pages/query')),
  // },
  // {
  //   path: 'stock/warn',
  //   element: lazy(() => import('./pages/warn')),
  // },
  // {
  //   path: 'stock/warehouse',
  //   element: lazy(() => import('./pages/warehouse')),
  // },
  // {
  //   path: 'stock/enter',
  //   element: lazy(() => import('./pages/enter')),
  // },
  // {
  //   path: 'stock/out',
  //   element: lazy(() => import('./pages/out')),
  // },
  // {
  //   path: 'stock/taking',
  //   element: lazy(() => import('./pages/taking')),
  // },
  // {
  //   path: 'stock/allot',
  //   element: lazy(() => import('./pages/allot')),
  // },
  // {
  //   path: 'stock/receive',
  //   element: lazy(() => import('./pages/receive')),
  // },
  // {
  //   path: 'stock/batch',
  //   element: lazy(() => import('./pages/batch')),
  // },
  // {
  //   path: 'stock/replenish',
  //   element: lazy(() => import('./pages/replenish')),
  // },
  // {
  //   path: 'stock/backstage',
  //   element: lazy(() => import('./pages/backstage')),
  //   children: [
  //     {
  //       path: 'node-approval',
  //       element: lazy(() => import('./pages/backstage/node-approval')),
  //     },
  //     {
  //       path: 'manage',
  //       element: lazy(() => import('./pages/backstage/manage')),
  //     },
  //   ],
  // },
  // {
  //   path: 'stock/report',
  //   element: lazy(() => import('./pages/report')),
  // },
  // {
  //   path: 'stock/transfer-report',
  //   element: lazy(() => import('./pages/transfer-report')),
  // },
  // {
  //   path: 'stock/comprehensive-statement',
  //   element: lazy(() => import('./pages/comprehensive-statement')),
  // },
];

export default routes;
