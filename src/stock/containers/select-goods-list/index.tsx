import { Drawer, Search } from '@/components';
import { drawerPopup } from '@/utils/popup';
import { DrawerProps } from 'antd';
import styles from './index.module.less';

interface SelectGoodsListProps extends DrawerProps {
  onClose?: () => void;
}

function SelectGoodsList({ onClose, ...props }: SelectGoodsListProps) {
  return (
    <Drawer onClose={onClose} {...props} title="添加补货商品">
      <div className={styles.search}>
        <Search placeholder="搜索商品名称" />
      </div>
    </Drawer>
  );
}

SelectGoodsList.defaultProps = {
  onClose: () => {},
};

export const selectGoodsList = (props: SelectGoodsListProps) => {
  drawerPopup(SelectGoodsList, props);
};

export default selectGoodsList;
