import { useEffect, useRef, useState } from 'react';
import isFunction from 'lodash/isFunction';
import { modalPopup } from '@/utils/popup';
import { Checkbox, Divider, Modal, ModalProps, Spin } from 'antd';
import { stockWarehouseCompanyList, StockWarehouseCompanyListResult } from '@/apis';
import { useRequest } from 'ahooks';
import { Icon, Search } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import classNames from 'classnames';
import styles from './index.module.less';

export interface SelectCompanyProps extends ModalProps {
  hasSelect: StockWarehouseCompanyListResult[];
  onCancel?: () => void;
  // eslint-disable-next-line no-unused-vars
  onSuccess: (arr: StockWarehouseCompanyListResult[]) => void;
}

function SelectCompany({ hasSelect, onCancel, onSuccess, ...props }: SelectCompanyProps) {
  const [list, setList] = useState<StockWarehouseCompanyListResult[]>([]);
  const params = useRef({
    pageNo: 1,
    pageSize: 10,
    key: '',
  });
  const totalCount = useRef(0);
  const [hasSelectList, setHasSelectList] = useState<StockWarehouseCompanyListResult[]>([]);

  const { run, loading } = useRequest(stockWarehouseCompanyList, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      totalCount.current = res.pagination.total;
      const arrIds = hasSelectList.map((each) => each.shareCompanyId);
      res.list.forEach((item) => {
        const items = item;
        items.checked = arrIds.includes(item.shareCompanyId);
      });
      if (params.current.pageNo === 1) {
        setList(res.list);
      } else {
        setList([...list, ...res.list]);
      }

      params.current.pageNo += 1;
    },
  });

  const onLoadMore = () => {
    if (!(params.current.pageNo <= totalCount.current)) return;
    run({ ...params.current });
  };

  const onSearch = (val: string) => {
    params.current.pageNo = 1;
    params.current.key = val;
    run({ ...params.current });
  };

  const onChangeCompany = (val: boolean, id: number) => {
    let arr: StockWarehouseCompanyListResult[] = JSON.parse(JSON.stringify(hasSelectList));
    list.forEach((item) => {
      const items = item;
      if (item.shareCompanyId === id) {
        items.checked = !item.checked;
        if (val) {
          arr.push(item);
        } else {
          arr = hasSelectList.filter((each) => each.shareCompanyId !== id);
        }
      }
    });
    setList([...list]);
    setHasSelectList([...arr]);
  };

  const onDeleteCompany = (id: number) => {
    list.forEach((item) => {
      const items = item;
      if (item.shareCompanyId === id) {
        items.checked = false;
      }
    });
    setList([...list]);
    setHasSelectList([...hasSelectList.filter((each) => each.shareCompanyId !== id)]);
  };

  useEffect(() => {
    setHasSelectList([...hasSelect]);
    params.current.pageNo = 1;
    run({ ...params.current });
  }, [hasSelect, run]);

  return (
    <Modal
      {...props}
      title="选择企业"
      width={730}
      centered
      closable={false}
      className={styles.box}
      onCancel={onCancel}
      onOk={() => {
        onCancel?.();
        onSuccess([...hasSelectList]);
      }}
    >
      <div className={styles.left}>
        <div className={styles.search}>
          <Search placeholder="搜索成员" onSearch={(e) => onSearch(e)} />
        </div>
        <div id="list" className={styles.list}>
          <Spin spinning={loading && params.current.pageNo === 1} wrapperClassName={styles.spin}>
            {list.length > 0 ? (
              <InfiniteScroll
                dataLength={list.length}
                hasMore={params.current.pageNo < totalCount.current}
                loader={
                  <div className="text-center">
                    <Spin tip="加载中..." />
                  </div>
                }
                next={onLoadMore}
                scrollableTarget="list"
                endMessage={
                  list.length > 0 &&
                  params.current.key.length === 0 && (
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>加载到底了</span>
                      </Divider>
                    </div>
                  )
                }
              >
                {list.map((item) => (
                  <label className={styles.item} key={item.shareCompanyId}>
                    <Checkbox
                      checked={item.checked}
                      onChange={(e) => onChangeCompany(e.target.checked, item.shareCompanyId)}
                    />
                    <img
                      className={styles.itemImg}
                      src={
                        item.logoUrl ||
                        'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
                      }
                      alt=""
                    />
                    <span className={styles.companyName} title={item.shareCompanyName}>
                      {item.shareCompanyName}
                    </span>
                  </label>
                ))}
              </InfiniteScroll>
            ) : (
              <div className={styles.noData}>没有找到结果</div>
            )}
          </Spin>
        </div>
      </div>
      <div className={styles.right}>
        <div className={styles.rightTitle}>已选：{hasSelectList.length}个企业</div>
        <div className={classNames(styles.list, styles.listHas)}>
          {hasSelectList.map((item) => (
            <div className={styles.hasSelectItem} key={item.shareCompanyId}>
              <span>{item.shareCompanyName}</span>
              <Icon
                name="close"
                className={styles.hasSelectItemIcon}
                onClick={() => onDeleteCompany(item.shareCompanyId)}
              />
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
}

SelectCompany.defaultProps = {
  onCancel: () => {},
};

function selectCompany(config: SelectCompanyProps) {
  return modalPopup(SelectCompany, {
    ...config,
    onSuccess: (arr) => {
      if (isFunction(config.onSuccess)) {
        config.onSuccess(arr);
      }
    },
  });
}

export default selectCompany;
