@import 'styles/mixins/mixins';

.box {
  :global {
    .ant-modal-body {
      display: flex;
      height: 470px;
      padding: 0;
    }
  }
}

.left,
.right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.left {
  border-right: 1px solid #f3f3f3;
}

.search {
  padding: 16px 20px;
}

.list {
  flex: 1;
  overflow: auto;

  :global {
    .ant-spin-nested-loading {
      height: 100%;
    }
  }
}

.listHas {
  padding: 0 20px;
}

.item {
  display: flex;
  align-items: center;
  height: 56px;
  padding: 12px 20px;
}

.itemImg {
  width: 32px;
  height: 32px;
  margin: 0 8px;
  border-radius: 50%;
}

.rightTitle {
  padding: 20px;
}

.hasSelectItem {
  font-size: 12px;
  display: inline-block;
  margin-right: 8px;
  margin-bottom: 8px;
  padding: 2px 8px;
  border-radius: 4px;
  background: #f5f6fa;
}

.companyName {
  display: inline-block;
  width: 250px;
  .text-overflow();
}

.hasSelectItemIcon {
  color: #999eb2;
  font-size: 16px;
  margin-left: 4px;
  cursor: pointer;
}

.divider {
  padding: 0 20px;
}

.spin {
  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.noData {
  color: #b1b3be;
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
