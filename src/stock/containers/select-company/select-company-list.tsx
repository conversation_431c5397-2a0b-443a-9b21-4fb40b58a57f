import { Drawer } from '@/components';
import { drawerPopup } from '@/utils/popup';
import { DrawerProps } from 'antd';
import { ShareCompanyVOSResult } from '@/apis';
import styles from './select-company-list.module.less';

interface SelectProcessProps extends DrawerProps {
  list: ShareCompanyVOSResult[];
  onClose?: () => void;
}

function SelectCompanyList({ list, onClose, ...props }: SelectProcessProps) {
  return (
    <Drawer title="共享企业" onClose={onClose} {...props}>
      <div className={styles.card}>
        {list.map((item) => (
          <div key={item.shareCompanyId} className={styles.item}>
            <img
              className={styles.img}
              src={
                item.logoUrl ||
                'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
              }
              alt=""
            />
            <span className={styles.companyName} title={item.shareCompanyName}>
              {item.shareCompanyName}
            </span>
          </div>
        ))}
      </div>
    </Drawer>
  );
}

SelectCompanyList.defaultProps = {
  onClose: () => {},
};

export const selectCompanyList = (props: SelectProcessProps) => {
  drawerPopup(SelectCompanyList, props);
};

export default selectCompanyList;
