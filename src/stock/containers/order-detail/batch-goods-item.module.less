@import 'styles/mixins/mixins';

.goodsItem {
  position: relative;
  border-bottom: 1px solid #f3f3f3;

  &:last-child {
    border-bottom: 0;
  }
}

.delete {
  color: rgb(0 0 0 / 30%);
  position: absolute;
  top: 8px;
  right: -12px;
  cursor: pointer;
}

.goodsItemInfo {
  display: flex;
  padding: 20px 0;
}

.goodsItemInfoStock {
  padding: 20px 0 12px;
}

.goodsInfo {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.goodsImg {
  width: 64px;
  height: 64px;
  margin-right: 16px;
  border-radius: 8px;
}

.goodsName {
  color: #040919;
  width: 200px;
  margin-bottom: 8px;
  .text-overflow(2);
}

.meta {
  color: #888b98;
  font-size: 12px;
  width: 200px;
  .text-overflow();
}

.metaItem:last-child .metaTag {
  display: none;
}

.saleGroup {
  color: #888b98;
  font-size: 12px;
}

.batchTitle {
  display: flex;
  margin-bottom: 16px;
  padding: 10px 0;
  border-radius: 8px;
  background-color: #f4f4f9;

  .batch {
    width: 160px;
    // padding-left: 20px;
    text-align: center;
    position: relative;

    &::after {
      content: '';
      width: 1px;
      height: 14px;
      position: absolute;
      top: 3px;
      right: 0;
      background-color: #d8d8d8;
    }
  }

  & .unit {
    position: relative;
    flex: 1;
    text-align: center;

    &::after {
      content: '';
      width: 1px;
      height: 14px;
      position: absolute;
      top: 3px;
      right: 0;
      background-color: #d8d8d8;
    }
  }

  .number {
    flex: 1;
    text-align: center;
  }
}

.batchItem {
  display: flex;
  margin-bottom: 16px;

  .batch {
    width: 160px;
    // padding-left: 20px;
    text-align: center;
  }

  & .unit {
    flex: 1;
    text-align: center;
  }

  .number {
    flex: 1;
    text-align: center;
  }
}

.stock {
  display: flex;
  margin-bottom: 16px;
  padding-left: 96px;
  justify-content: space-between;
}

.status {
  font-size: 12px;
  display: flex;
  min-width: 78px;
  line-height: 20px;
  padding: 0 8px;
  justify-content: center;
  box-sizing: border-box;
  align-items: center;
  border-radius: 20px;
}

.stockProfit {
  background: #d9eeff;
  color: #008cff;
}

.stockLoss {
  background: #fcdddf;
  color: #ea1c26;
  border: 0.5px solid #ea1c26;
}

.stockUnbiased {
  color: #040919;
  padding: 1px 8px;
  background: #f5f6fa;
}

.more {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 16px;
  text-align: center;
  cursor: pointer;
}

.stockProfitF {
  color: #ea1c26;
}

.stockLossF {
  color: #008cff;
}

.stockBlack {
  color: #000;
}
