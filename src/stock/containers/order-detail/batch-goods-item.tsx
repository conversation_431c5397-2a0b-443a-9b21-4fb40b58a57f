import { Icon } from '@/components';
import { StockGoodsInfo } from '@/apis';
import { StandardItem } from '@/apis/mcs/goods-list';
import classnames from 'classnames';
import { checkPermission } from '@/utils/permission';
import styles from './batch-goods-item.module.less';

interface GoodsItemProps {
  info: StockGoodsInfo;
  isBatch: boolean;
  orderType: number;
  // eslint-disable-next-line no-unused-vars
  onMore: (id: number) => void;
}

function BatchGoodsItem({ info, isBatch, orderType, onMore }: GoodsItemProps) {
  const metaTitle = (list: StandardItem[]) => {
    let str = '';
    list?.forEach((item, index) => {
      str += `${item.name}: ${item.value}${index === list.length - 1 ? '' : ','}`;
    });
    return str;
  };

  return (
    <div className={styles.goodsItem}>
      <div
        className={classnames(styles.goodsItemInfo, {
          [styles.goodsItemInfoStock]: orderType === 11,
        })}
      >
        <img
          className={styles.goodsImg}
          src={info.images || 'https://img.huahuabiz.com/default/image/default_holder.png'}
          alt=""
        />
        <div className={styles.goodsInfo}>
          <div>
            <div className={styles.goodsName} title={info.skuName}>
              {info.skuName}
            </div>
            <div className={styles.meta} title={metaTitle(JSON.parse(info.standardJson))}>
              规格：
              {JSON.parse(info.standardJson).map((each: any, index: any) => (
                <span className={styles.metaItem} key={`${index + 1}`}>
                  {each.name}: {each.value}
                  <span className={styles.metaTag}>;</span>
                </span>
              ))}
            </div>
            <div className={styles.meta}>条形码：{info?.barsCode || '--'}</div>
          </div>
          {/* {orderType !== 11 && orderType !== 14 && ( */}
          {/*  <div className={styles.saleGroup}>库存: {info.stock}</div> */}
          {/* )} */}
        </div>
      </div>
      {/* {orderType === 11 && (
        <div className={styles.stock}>
          <span>库存 {info.stock}</span>
          <span
            className={classnames(styles.status, {
              [styles.stockProfit]: info.checkStatusStr === '盘盈',
              [styles.stockLoss]: info.checkStatusStr === '盘亏',
              [styles.stockUnbiased]: info.checkStatusStr === '持平',
            })}
          >
            {info.checkStatusStr} {info.checkStock || '--'}
          </span>
        </div>
      )} */}
      {info.skuRelationParamList && info.skuRelationParamList.length > 0 && (
        <>
          <div className={styles.batchList}>
            <div className={styles.batchTitle}>
              <span className={styles.unit}>单位</span>
              {isBatch && <span className={styles.batch}>批次号</span>}
              {orderType === 11 && (
                <span className={classnames(styles.number, { [styles.unit]: orderType === 11 })}>
                  库存数量
                </span>
              )}
              <span className={classnames(styles.number, { [styles.unit]: orderType === 11 })}>
                {orderType === 11 && '盘点'}数量
              </span>
              {orderType === 11 && <span className={styles.number}>盈亏数量</span>}
            </div>
            {info.skuRelationParamList
              .filter((item, index) => (info.isMore ? item : index < 2))
              .map((item, index) => (
                <div className={styles.batchItem} key={`${index + 1}`}>
                  <span className={styles.unit}>{item.auxiliaryUnit || item.unitName}</span>
                  {isBatch && <span className={styles.batch}>{item.skuBatchNo}</span>}
                  {orderType === 11 && (
                    <span className={styles.unit}>
                      {checkPermission('AV_001_004_007')
                        ? item.mainQuantity || item.stockStr
                        : '***'}
                    </span>
                  )}
                  <span className={styles.unit}>{item.quantity || item.realStock}</span>
                  {orderType === 11 &&
                    (checkPermission('AV_001_004_007') ? (
                      <span
                        className={classnames(styles.unit, {
                          [styles.stockProfitF]: item.checkStatus,
                          [styles.stockLossF]: !item.checkStatus,
                          [styles.stockBlack]:
                            Number(item.checkStockStr) && Number(item.checkStockStr) === 0,
                        })}
                      >
                        {/* {Number(item.checkStock) < 0 && '-'} */}
                        {Number(item.checkStockStr) && Number(item.checkStockStr) > 0 && '+'}
                        {item.mainQuantity || item.checkStockStr}
                      </span>
                    ) : (
                      <span>***</span>
                    ))}
                </div>
              ))}
          </div>
          {info.skuRelationParamList.length > 2 &&
            (!info.isMore ? (
              <div
                role="button"
                tabIndex={0}
                className={styles.more}
                onClick={() => onMore(info.id || Number(info.shopSkuId))}
              >
                <span>
                  还有{info.skuRelationParamList.length - 2}个
                  {`${[9, 11].includes(orderType) ? '信息' : '批次'} `}
                </span>
                <Icon name="down" />
              </div>
            ) : (
              <div
                role="button"
                tabIndex={0}
                className={styles.more}
                onClick={() => onMore(info.id || Number(info.shopSkuId))}
              >
                <span>收起</span>
                <Icon name="down" />
              </div>
            ))}
        </>
      )}
    </div>
  );
}
export default BatchGoodsItem;
