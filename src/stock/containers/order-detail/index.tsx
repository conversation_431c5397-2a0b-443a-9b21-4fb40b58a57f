import React, { PropsWithChildren, useEffect, useState, useMemo } from 'react';
import { Drawer, Icon, FileList } from '@/components';
import { message, Spin } from 'antd';
import classNames from 'classnames';
import {
  StockOrderDetailResult,
  StockEnterConfirmParams,
  stockTakingDetailExport,
  orderDetailResult,
  getPlatReceiveOrderDetail,
  postPlatOrderDelete,
  postPlatInOrderAudit,
  postPlatOutOrderAudit,
  postPlatReceiveBatch,
} from '@/apis';
import dayjs from 'dayjs';
import { idParams } from '@/apis/psi/get-plat-receive-order-detail';
import getStateTag from '@@/stock/utils/get-state-tag';
import styles from './index.module.less';
import phonePng from '../../assets/imgs/phone.png';
import addressPng from '../../assets/imgs/address.png';
import relation from '../../assets/imgs/relation.png';
import StateBtn from '../../components/state-btn';
import StateDropdown from '../../components/state-dropdown';
import Print from '../print';
import BatchGoodsItem from './batch-goods-item';
import BatchGoodsList from './batch-goods-list';

interface PlatformProps {
  isNeedInitWorkflowForm: boolean;
  processDefinitionId: string;
  processDefinitionKey: string;
  processDefinitionName: string;
  businessOrderNo: string;
  businessType: number;
}

interface DetailPropps {
  id: number;
  visible: boolean;
  orderType: number;
  onClose: () => void;
  openUpload?: () => void;
  // eslint-disable-next-line no-unused-vars
  orderDeteil?: (id: number) => Promise<StockOrderDetailResult>;
  // eslint-disable-next-line no-unused-vars
  receiveDetail?: (id: idParams) => Promise<orderDetailResult>;
  confirm: () => void;
  // eslint-disable-next-line no-unused-vars
  orderDetele: (id: number) => Promise<null>;
  // eslint-disable-next-line no-unused-vars
  orderConfirm?: (arg0: StockEnterConfirmParams) => Promise<PlatformProps>;
  editEntryList?: () => void;
  // eslint-disable-next-line no-unused-vars
  orderSubmit?: (data: { id: number }) => Promise<PlatformProps>;
  orderPrintPdf?: () => void;
}

function OrderDetail({
  id,
  orderType,
  orderDeteil,
  confirm,
  orderDetele,
  orderSubmit,
  orderConfirm,
  onClose,
  editEntryList,
  orderPrintPdf,
  visible,
  openUpload,
  ...props
}: PropsWithChildren<DetailPropps>) {
  const [orderInfo, setOrderInfo] = useState<StockOrderDetailResult>({
    bindHeadName: '',
    bindWarehouseName: '',
    address: '',
    checkStatusStr: '',
    companyName: '',
    createMemberName: '',
    customerCompanyName: '',
    customerPhone: 0,
    destinationWarehouseId: 0,
    destinationWarehouseName: '',
    destinationWarehouseNo: '',
    operator: '',
    operatorId: 0,
    orderDate: '',
    orderNumber: '',
    orderStatus: 0,
    orderTradeNo: 0,
    orignWarehouseId: 0,
    orignWarehouseName: '',
    orignWarehouseNo: '',
    phone: 0,
    productionTime: 0,
    quantity: 0,
    quantityKind: 0,
    remark: '',
    warehouseName: '',
    customerId: 0,
    customerNo: 0,
    customerName: '',
    batchNo: 0,
    customerCompanyId: 0,
    orderNo: 0,
    businessName: '',
    businessType: 0,
    platformInboundSkuVOS: [],
    platformOutboundSkuVOS: [],
    platformAllotSkuVOS: [],
    inventorySkuVOS: [],
    attachmentList: [],
    isMore: false,
    warehouseId: 0,
    canJumpOrder: false,
    warehouseNo: '',
    isCanEdit: true,
    id: 0,
    orderFuncName: '',
  });
  const [receiveInfo, setReceiveInfo] = useState<orderDetailResult>({
    id: 34,
    operator: '',
    attachmentList: [],
    receiveSkuVOList: [],
    warehouseNo: '',
    warehouseName: '',
    warehouseId: 2,
    remark: '',
    recipientId: 0,
    recipient: '',
    operatorId: 0,
    orderDate: '',
    orderNo: '',
    orderStatus: 0,
    orderStatusStr: '',
    purpose: '',
  });
  const [isMoreInfo, setIsMoreInfo] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [orderName, setOrderName] = useState('');
  const [showPrint, setShowPrint] = useState(false);
  const [showBatchGoodsList, setShowBatchGoodsList] = useState(false);

  const title = useMemo(() => {
    switch (orderType) {
      case 9:
        return '入库';
      case 10:
        return '出库';
      case 13:
        return '调拨';
      case 11:
        return '盘点';
      case 14:
        return '领用';
      default:
        return '';
    }
  }, [orderType]);

  // const isDisableOrder = useMemo(() => {
  //   if (
  //     orderName !== '其他入库单' &&
  //     orderName !== '其他出库单' &&
  //     orderName !== '退货入库单' &&
  //     id
  //   ) {
  //     return true;
  //   }
  //   return false;
  // }, [id, orderName]);

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const onMore = (idVal: number) => {
    (
      orderInfo.platformInboundSkuVOS ||
      orderInfo.platformOutboundSkuVOS ||
      orderInfo.platformAllotSkuVOS ||
      orderInfo.inventorySkuVOS
    ).forEach((item) => {
      const items = item;
      if (Number(item.id || item.shopSkuId) === idVal) {
        items.isMore = !items.isMore;
      }
    });
    setOrderInfo(JSON.parse(JSON.stringify(orderInfo)));
  };

  const onMoreReceive = (idVal: number) => {
    receiveInfo?.receiveSkuVOList.forEach((item) => {
      const items = item;
      if (item.shopSkuId === idVal) {
        items.isMore = !items.isMore;
      }
    });
    setReceiveInfo(JSON.parse(JSON.stringify(receiveInfo)));
  };

  const exportDetail = () => {
    stockTakingDetailExport(id).then((res) => {
      const URL = window.webkitURL || window.URL;
      const url = URL.createObjectURL(res);
      urlDownload(url, `盘点单详情-${orderInfo.orderNo}.xls`);
    });
  };

  const fileTypeValue = (fileType: number, fileName: string) => {
    if (fileType === 1) {
      if (fileName.split('.').pop() === 'txt') {
        return 'text';
      }
      return fileName.split('.').pop();
    }
    return 'image';
  };

  const itemList = useMemo(() => {
    if (orderInfo?.platformInboundSkuVOS && orderInfo?.platformInboundSkuVOS.length) {
      return orderInfo?.platformInboundSkuVOS;
    }
    if (orderInfo?.platformOutboundSkuVOS && orderInfo?.platformOutboundSkuVOS.length) {
      return orderInfo?.platformOutboundSkuVOS;
    }
    if (orderInfo?.platformAllotSkuVOS && orderInfo?.platformAllotSkuVOS.length) {
      return orderInfo?.platformAllotSkuVOS;
    }
    return orderInfo?.inventorySkuVOS || [];
  }, [orderInfo]); // eslint-disable-line
  const stateDropdownChange = (name: string) => {
    if (name === '编辑') {
      if (editEntryList) {
        editEntryList();
      }
      onClose();
    }
    if (name === '删除') {
      if (orderType !== 14) {
        orderDetele(id).then(() => {
          message.success('删除成功');
          confirm();
        });
      } else {
        postPlatOrderDelete(id).then(() => {
          message.success(`删除成功`);
          confirm();
        });
      }
    }
    if (name === '导出详情') {
      exportDetail();
    }
    if (name === '打印') {
      setShowPrint(true);
      // if (orderPrintPdf) {
      //   orderPrintPdf();
      // }
    }
  };

  const stateBtnChange = (name: string) => {
    // if (isDisableOrder && name === '审批' && openUpload) {
    //   openUpload();
    // }
    if (name === '删除') {
      if (orderType !== 14) {
        orderDetele(id).then(() => {
          message.success('删除成功');
          confirm();
        });
      } else {
        postPlatOrderDelete(id).then(() => {
          message.success(`删除成功`);
          confirm();
        });
      }
    }
    if (name === '审批' || name === '取消审批') {
      if (orderType !== 14 && (orderType === 13 || orderType === 11)) {
        if (orderConfirm) {
          orderConfirm({
            // eslint-disable-next-line no-nested-ternary
            orderStatus: orderInfo?.orderStatus === 1 ? 0 : 1,
            ids: [id],
          }).then((res) => {
            const {
              isNeedInitWorkflowForm,
              processDefinitionKey,
              processDefinitionName,
              businessOrderNo,
              businessType,
            } = res;
            if (isNeedInitWorkflowForm) {
              window.open(
                `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                  {
                    businessOrderNo,
                    businessType,
                  }
                )}`
              );
            } else {
              message.success(
                `${
                  // eslint-disable-next-line no-nested-ternary
                  orderInfo?.orderStatus ? (orderInfo?.orderStatus === 1 ? '取消' : '审批') : '审批'
                }成功`
              );
            }
            confirm();
          });
        }
      } else if (orderType === 14) {
        postPlatReceiveBatch({
          ids: [id],
          orderStatus: receiveInfo?.orderStatus === 1 ? 0 : 1,
        }).then((res) => {
          const {
            isNeedInitWorkflowForm,
            processDefinitionKey,
            processDefinitionName,
            businessOrderNo,
            businessType,
          } = res;
          if (isNeedInitWorkflowForm) {
            window.open(
              `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                {
                  businessOrderNo,
                  businessType,
                }
              )}`
            );
          } else {
            message.success(`${receiveInfo?.orderStatus === 1 ? '取消' : '审核'}成功`);
          }
          confirm();
        });
      } else {
        const request = title === '入库' ? postPlatInOrderAudit : postPlatOutOrderAudit;
        request({
          // eslint-disable-next-line no-nested-ternary
          orderStatus: orderInfo?.orderStatus ? (orderInfo?.orderStatus === 1 ? 0 : 1) : 1,
          id,
        }).then((res) => {
          const {
            isNeedSecStep,
            isNeedApprovoal,
            description,
            isNeedInitWorkflowForm,
            processDefinitionKey,
            processDefinitionName,
            businessOrderNo,
            businessType,
          } = res;
          if (isNeedInitWorkflowForm) {
            confirm();
            window.open(
              `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                {
                  businessOrderNo,
                  businessType,
                }
              )}`
            );
          } else {
            if (description.length > 0) {
              message.success(description);
            } else {
              message.success(`${orderInfo?.orderStatus === 1 ? '取消' : '操作'}成功`);
            }
            if (
              openUpload &&
              isNeedSecStep &&
              isNeedApprovoal === 0 &&
              orderName !== '退货入库单'
            ) {
              openUpload();
              return;
            }
            confirm();
          }
        });
      }
    }
    if (name === '提交' && orderSubmit) {
      orderSubmit({ id }).then(() => {
        message.success(`提交成功`);
        confirm();
      });
    }
  };

  const stockType = () => {
    let num = 0;
    if (
      (orderInfo.businessType === 9 || orderInfo.businessType === 10) &&
      orderInfo.orderFuncName === '补录退货'
    ) {
      num = orderType === 9 ? 1 : 2;
    } else {
      num = 0;
    }
    return num;
  };

  const currentType = () => {
    let str = '';
    if (orderType === 9) {
      if (orderInfo.businessType === 9 || orderInfo.businessType === 8) {
        str = 'sell';
      } else {
        str = 'buy';
      }
    } else if (orderType === 10) {
      if (orderInfo.businessType === 10) {
        str = 'buy';
      } else {
        str = 'sell';
      }
    }
    return str;
  };

  useEffect(() => {
    if (visible) {
      setShowBatchGoodsList(false);
      setIsMoreInfo(false);
      setLoading(true);
      if (orderType === 14) {
        getPlatReceiveOrderDetail({ id })
          .then((res) => {
            setReceiveInfo(res);
            // 存入打印数据
            const orderPrintData = {
              orderType,
              ...res,
            };
            sessionStorage.setItem('orderPrintData', JSON.stringify(orderPrintData));
            setOrderName(title);
            setOrderInfo({
              ...orderInfo,
              attachmentList: res.attachmentList,
              operator: res.operator,
            });
          })
          .catch(() => {
            onClose();
          })
          .finally(() => {
            setLoading(false);
          });
        return;
      }
      if (orderDeteil) {
        orderDeteil(id)
          .then((res) => {
            setOrderInfo(res);
            // 存入打印数据
            const orderPrintData = {
              orderType,
              ...res,
            };
            sessionStorage.setItem('orderPrintData', JSON.stringify(orderPrintData));
            if (orderType === 10 || orderType === 9) {
              setOrderName(res.businessName);
            } else {
              setOrderName(title);
            }
          })
          .catch(() => {
            onClose();
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }
  }, [id, orderDeteil, visible, orderType]); // eslint-disable-line

  return (
    <Drawer
      title={`${title}单详情`}
      visible={visible}
      {...props}
      className={styles.detail}
      push={false}
      onClose={() => {
        onClose();
      }}
      footer={
        <StateBtn
          status={(orderType !== 14 ? orderInfo.orderStatus : receiveInfo.orderStatus) || 0}
          orderType={orderType}
          orderName={orderName}
          isCanEdit={orderInfo?.isCanEdit || false}
          btnChange={(name: string) => {
            stateBtnChange(name);
          }}
        />
      }
      extra={
        <StateDropdown
          status={(orderType !== 14 ? orderInfo.orderStatus : receiveInfo.orderStatus) || 0}
          orderName={orderName}
          orderType={orderType}
          isCanEdit={orderInfo?.isCanEdit || false}
          ifCanDelete={orderInfo.ifCanDelete}
          btnChange={(name: string) => {
            stateDropdownChange(name);
          }}
        />
      }
    >
      <Spin spinning={loading}>
        {orderType === 9 || orderType === 10 || orderType === 13 ? (
          <div className={styles.orderInfo}>
            {orderType === 9 || orderType === 10 ? (
              <div className={styles.header}>
                <div className={styles.headerTitle}>
                  <img
                    className={classNames(styles.orderStatus, 'mr-3')}
                    src={getStateTag(orderInfo.orderStatus as number)}
                    alt=""
                  />
                  <span className={styles.title}>{orderInfo?.businessName}</span>
                </div>
                {orderInfo?.orderFuncName ? (
                  <div className={styles.tip}>{orderInfo.orderFuncName}</div>
                ) : null}
              </div>
            ) : null}
            {orderType === 13 ? (
              <div className={styles.headerAllot}>
                <div className={styles.item}>
                  <div className={styles.itemRight}>
                    <span className={styles.tipType}>调出</span>
                    <span className={styles.address}>{orderInfo.orignWarehouseName}</span>
                  </div>
                  <img
                    className={classNames(styles.orderStatus, 'mr-3')}
                    src={getStateTag(orderInfo.orderStatus as number)}
                    alt=""
                  />
                </div>
                <div className={styles.itemRight}>
                  <span className={styles.tipType}>调入</span>
                  <span className={styles.address}>{orderInfo.destinationWarehouseName}</span>
                </div>
              </div>
            ) : null}
            <div className={styles.num}>
              {orderType === 13 ? '单据编号' : '订单编号'}
              <span className={styles.serial}>{orderInfo?.orderNo}</span>
            </div>
            <div className={styles.time} style={{ marginBottom: orderType === 9 ? '8px' : '20px' }}>
              订单创建时间
              <span className={styles.serial}>
                {dayjs(orderInfo?.orderDate).format('YYYY-MM-DD HH:mm:ss')}
              </span>
            </div>
            {orderType !== 10 && orderInfo?.productionTime > 0 && (
              <div className={styles.time} style={{ marginBottom: '20px' }}>
                订单生产时间
                <span className={styles.serial}>
                  {dayjs(orderInfo?.productionTime).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
            )}
            {orderInfo?.canJumpOrder ? (
              <div
                role="button"
                tabIndex={0}
                className={styles.relation}
                onClick={() => {
                  window.open(
                    `/indent/detail?orderNo=${
                      orderInfo?.orderTradeNo
                    }&current=${currentType()}&stockType=${stockType()}`
                  );
                }}
              >
                <div>
                  <img className={styles.relationImg} src={relation} alt="" />
                  <span>关联订单</span>
                </div>
                <Icon name="right" />
              </div>
            ) : null}
          </div>
        ) : (
          <div className={styles.takingInfo}>
            <div className={styles.num}>
              {orderType === 14 ? '领用单信息' : '单据编号'}
              {orderType !== 14 && (
                <span className={styles.serial}>
                  {(orderType === 14 ? receiveInfo : orderInfo)?.orderNo}
                </span>
              )}
              <img
                className={styles.orderStatus}
                src={getStateTag((orderInfo.orderStatus || receiveInfo.orderStatus) as number)}
                alt=""
              />
            </div>
            {orderType !== 14 ? (
              <>
                <div className={styles.num}>
                  盘点时间
                  <span className={styles.serial}>
                    {dayjs(orderInfo?.orderDate).format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                </div>
                <div className={styles.num} style={{ marginBottom: '20px' }}>
                  盘点仓库
                  <span className={styles.serial}>{orderInfo?.warehouseName}</span>
                </div>
              </>
            ) : (
              <>
                <div className={styles.num}>
                  单据编号
                  <span className={styles.serial}>{receiveInfo.orderNo}</span>
                </div>
                <div className={styles.num} style={{ marginBottom: '20px' }}>
                  领用时间
                  <span className={styles.serial}>
                    {dayjs(receiveInfo?.orderDate).format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                </div>
              </>
            )}

            {/* {title === '盘点' ? (
              <div role="button" tabIndex={0} className={styles.relation}>
                <div>
                  <img className={styles.relationImg} src={profitLoss} alt="" />
                  <span>盘盈</span>
                </div>
              </div>
            ) : null} */}
          </div>
        )}
        <div className={styles.card}>
          {(orderType === 9 || orderType === 10) &&
          (orderInfo?.customerCompanyName || orderInfo?.customerPhone || orderInfo?.address) ? (
            <div className={styles.cardTitle}>
              {orderInfo?.customerCompanyName && (
                <div className={styles.title}>
                  <div className={styles.titleName}>{orderType === 9 ? '供应商' : '采购方'}</div>
                  <div className={styles.divider} />
                  <span
                    className={styles.titleText}
                    title={`${orderInfo.customerCompanyName}${
                      orderInfo.bindWarehouseName && orderInfo.bindWarehouseName.length
                        ? `(${orderInfo.bindWarehouseName}/${orderInfo.bindHeadName})`
                        : ''
                    }`}
                  >
                    {`${orderInfo.customerCompanyName}${
                      orderInfo.bindWarehouseName && orderInfo.bindWarehouseName.length
                        ? `(${orderInfo.bindWarehouseName}/${orderInfo.bindHeadName})`
                        : ''
                    }`}
                  </span>
                </div>
              )}
              {orderInfo?.customerPhone ? (
                <div className={classNames(styles.label, 'mb-1')}>
                  <img src={phonePng} alt="" className="mr-3" />
                  {orderInfo?.customerPhone}
                </div>
              ) : null}
              {orderInfo?.address ? (
                <div className={classNames(styles.label, 'mb-3')}>
                  <img src={addressPng} alt="" className="mr-3" />
                  {orderInfo?.address}
                </div>
              ) : null}
            </div>
          ) : (
            <div className={styles.goodsTitle}>商品信息</div>
          )}

          {itemList
            ?.filter((item, index: number) => (orderInfo.isMore ? index || !index : index < 2))
            .map((item) => (
              <BatchGoodsItem
                info={item}
                orderType={orderType}
                isBatch={![9, 11].includes(orderType)}
                onMore={onMore}
              />
            ))}
          {receiveInfo?.receiveSkuVOList
            ?.filter((item, index: number) => (orderInfo.isMore ? index || !index : index < 2))
            .map((item) => (
              <BatchGoodsItem
                // @ts-ignore
                info={item}
                isBatch={![9, 11].includes(orderType)}
                orderType={orderType}
                onMore={onMoreReceive}
              />
            ))}
          <div className={styles.total}>
            <span>
              共
              <span className={styles.totalNum}>
                {itemList.length || receiveInfo?.receiveSkuVOList.length}
              </span>
              种商品
            </span>
            {(itemList.length > 2 || receiveInfo?.receiveSkuVOList.length > 2) && (
              <span
                role="button"
                tabIndex={0}
                className={styles.lookMore}
                onClick={() => setShowBatchGoodsList(true)}
              >
                查看全部
                <Icon name="right" />
              </span>
            )}
          </div>
        </div>
        <div className={styles.cardInfo}>
          <div className={styles.title}>
            {orderType === 9 || orderType === 10 ? '订单信息' : '单据信息'}
          </div>
          {orderType !== 14 && (
            <div className="mb-3">
              <span className={styles.label}>
                业务人员
                <span className={classNames('ml-5', styles.fontColor)}>{orderInfo?.operator}</span>
              </span>
            </div>
          )}
          {orderType === 14 && (
            <div className="mb-3">
              <span className={styles.label}>
                领用人员
                <span className={classNames('ml-5', styles.fontColor)}>
                  {receiveInfo?.recipient}
                </span>
              </span>
            </div>
          )}
          {receiveInfo.warehouseName && orderType === 14 && (
            <>
              <div className="mb-5">
                <span className={styles.remark}>操作人员</span>
                <span className={styles.remarkValue}>{receiveInfo?.operator}</span>
              </div>
              {isMoreInfo && (
                <>
                  <div className="mb-5">
                    <span className={styles.remark}>出货仓库</span>
                    <span className={styles.remarkValue}>{receiveInfo?.warehouseName}</span>
                  </div>
                  <div className="mb-5">
                    <span className={styles.remark}>备注</span>
                    <span className={styles.remarkValue}>{receiveInfo?.remark}</span>
                  </div>
                </>
              )}
            </>
          )}
          {orderInfo.createMemberName && (
            <div className="mb-3">
              <span className={styles.label}>
                操作人员
                <span className={classNames('ml-5', styles.fontColor)}>
                  {orderInfo.createMemberName}
                </span>
              </span>
            </div>
          )}
          {orderInfo?.warehouseName && (orderType === 9 || orderType === 10) ? (
            <div className="mb-3">
              <span className={styles.label}>
                {`${orderType === 9 ? '入' : '出'}`}货仓库{' '}
                <span className={classNames('ml-5', styles.fontColor)}>
                  {orderInfo?.warehouseName}
                </span>
              </span>
            </div>
          ) : null}
          {/* && orderInfo?.remark */}
          {orderType === 13 || orderType === 11 ? (
            <div className="mb-5">
              <span className={styles.remark}>备注</span>
              <span className={styles.remarkValue}>{orderInfo?.remark}</span>
            </div>
          ) : null}
          {isMoreInfo && (orderType === 9 || orderType === 10) ? (
            <>
              {orderInfo?.remark ? (
                <div className="mb-5">
                  <span className={styles.remark}>备注</span>
                  <span className={styles.remarkValue}>{orderInfo?.remark}</span>
                </div>
              ) : null}
              <div className={classNames(styles.title, styles.maTop)}>
                {`${orderType === 9 ? '采购方' : '供应方'}`}信息
              </div>
              {orderInfo?.companyName && (
                <div className={styles.name}>
                  <span className={styles.label}>{`${orderType === 9 ? '采购' : '供应'}`}单位</span>
                  <span className={styles.value}>{orderInfo?.companyName}</span>
                </div>
              )}
              {orderInfo.address && (
                <div>
                  <span className={styles.remark}>联系地址</span>
                  <span className={styles.address}>{orderInfo?.address}</span>
                </div>
              )}
            </>
          ) : null}
          {orderType === 9 || orderType === 10 || orderType === 14 ? (
            <div
              role="button"
              tabIndex={0}
              className={styles.btn}
              onClick={() => {
                setIsMoreInfo(!isMoreInfo);
              }}
            >
              <div className={styles.moreBtn}>
                <span>{!isMoreInfo ? '展示完整信息' : '收起'}</span>
                <Icon name="down" />
              </div>
            </div>
          ) : null}
        </div>
        {orderInfo && orderInfo.attachmentList && orderInfo?.attachmentList?.length > 0 ? (
          <div className={styles.cardInfo}>
            <div className={styles.title}>附件</div>
            {orderInfo?.attachmentList?.filter((item) => item.fileType === 1).length > 0 ? (
              <FileList
                listType="list"
                download
                fileList={orderInfo?.attachmentList
                  ?.filter((item) => item.fileType === 1)
                  .map((item, index) => ({
                    uid: `${index}`,
                    name: item.fileName,
                    url: item.filePath,
                    status: 'done',
                    size: item.size,
                    type: fileTypeValue(item.fileType, item.fileName),
                  }))}
              />
            ) : null}
            {orderInfo?.attachmentList?.filter((item) => item.fileType === 0).length > 0 ? (
              <FileList
                listType="card"
                isDelete={false}
                fileList={orderInfo.attachmentList
                  .filter((item) => item.fileType === 0)
                  .map((item, index) => ({
                    uid: `${index}`,
                    name: item.fileName,
                    url: item.filePath,
                    status: 'done',
                    size: item.size,
                  }))}
              />
            ) : null}
          </div>
        ) : null}
      </Spin>
      <Print
        visible={showPrint}
        templateType={orderType}
        id={orderInfo.id || id}
        onCancel={() => setShowPrint(false)}
      />
      <BatchGoodsList
        visible={showBatchGoodsList}
        orderType={orderType}
        onClose={() => setShowBatchGoodsList(false)}
        // @ts-ignore
        goodsList={itemList?.length ? itemList : receiveInfo?.receiveSkuVOList}
        onMore={orderType !== 14 ? onMore : onMoreReceive}
        isBatch={![9, 11].includes(orderType)}
      />
    </Drawer>
  );
}

OrderDetail.defaultProps = {
  orderConfirm: () => {},
  editEntryList: () => {},
  orderPrintPdf: () => {},
  orderDeteil: () => {},
  receiveDetail: () => {},
  openUpload: () => {},
  orderSubmit: () => {},
};

export default OrderDetail;
