@import 'styles/mixins/mixins';

.orderInfo,
.takingInfo {
  margin-bottom: 20px;
  border-radius: 18px;
  background: linear-gradient(180deg, #0840ff 0%, #0840ff 0%, #4fb8ff 100%, #4fb8ff 100%);
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  overflow: hidden;

  & .header {
    color: #fff;
    display: flex;
    padding: 20px;
    justify-content: space-between;
  }

  & .headerAllot {
    color: #fff;
    padding: 20px;

    & .item {
      font-size: 16px;
      display: flex;
      margin-bottom: 8px;
      justify-content: space-between;
      align-items: center;
    }

    & .itemRight {
      font-size: 16px;
      display: flex;
      align-items: center;
    }

    & .address {
      width: 180px;
      .text-overflow();
    }

    & .tipType {
      font-size: 12px;
      display: inline-block;
      width: 38px;
      height: 20px;
      line-height: 20px;
      margin-right: 8px;
      text-align: center;
      border-radius: 20px;
      background: rgb(255 255 255 / 30%);
    }
  }

  .headerTitle {
    display: flex;
    align-items: center;
  }

  & .tip {
    font-weight: 500;
    display: flex;
    align-items: center;
  }

  & .title {
    font-size: 20px;
    font-weight: 600;
  }

  & .state {
    color: #006eff;
    font-size: 12px;
    width: 64px;
    height: 25px;
    line-height: 25px;
    text-align: center;
    background: linear-gradient(
      270deg,
      rgb(255 255 255 / 88%) 15%,
      rgb(255 255 255 / 90%) 0%,
      rgb(255 255 255 / 90%) 0%,
      #fff 90%,
      #fff 88%,
      #fff 100%
    );
    border-radius: 15px 0 0 15px;
  }

  .stateNo {
    .state();

    color: #f9ae08;
  }

  & .num,
  .time {
    color: #fff;
    margin-bottom: 14px !important;
    padding: 0 20px;
  }

  & .num {
    margin-bottom: 8px;
  }

  .serial {
    margin-left: 8px;
  }

  & .relation {
    color: #fff;
    display: flex;
    margin-top: 12px;
    padding: 10px 20px;
    justify-content: space-between;
    cursor: pointer;
    background: #1d86fd;
    border-radius: 0 0 18px 18px;

    & .relationImg {
      width: 20px;
      height: 20px;
      margin-right: 8px;
    }
  }
}

.takingInfo {
  padding-top: 20px;
  //padding-bottom: 40px;
  position: relative;
}

//.takingStatus {
//  position: absolute;
//  left: 0;
//  bottom: 0;
//  width: 100%;
//  height: 40px;
//  padding: 10px 20px;
//  color: #fff;
//  background: linear-gradient(97deg, #326aff 0%, #326aff 0%, #7d67ff 99%, #7d67ff 99%);
//}

.orderStatus {
  height: 18px;
  float: right;
}

.takingResult {
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;
}

.takingStatusLoss,
.takingStatusSurplus {
  display: flex;
  align-items: center;
}

.editIcon {
  color: #000;
  font-size: 24px;
  cursor: pointer;
}

.takingStatusLoss {
  color: #ea1c26;
}

.takingStatusSurplus {
  color: #006eff;
}

.batchItem {
  padding: 7px 8px;
  text-align: center;
  border-radius: 10px;
  cursor: pointer;

  &:hover {
    background: rgb(217 238 255 / 30%);
  }
}

.card,
.cardInfo {
  margin-bottom: 20px;
  padding: 0 20px 20px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  & .goodsTitle {
    color: #040919;
    font-size: 16px;
    padding-top: 20px;
  }

  & .cardTitle {
    margin-top: 20px;
  }

  & .title {
    font-size: 16px;
    font-weight: 500;
    display: flex;
    margin-bottom: 12px;
    align-items: center;

    & .titleText {
      display: inline-block;
      width: 230px;
      // margin-left: 10px;
      .text-overflow();
    }
  }

  .titleName {
    width: 50px;
  }

  .divider {
    width: 2px;
    height: 14px;
    margin: 0 6px;
    background-color: #b1b3be;
  }

  .maTop {
    margin-top: 20px;
  }

  & .name {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  & .label {
    color: #888b98;
    display: flex;
    align-items: center;
  }

  & .fontColor {
    color: black;
  }

  & .value {
    display: inline-block;
    width: 200px;
    margin-left: 16px;
    .text-overflow();
  }
}

.cardInfo {
  padding: 20px;
}

.goodsItem {
  display: flex;
  padding: 20px 0;

  & .img {
    width: 108px;
    height: 108px;
    border-radius: 10px;
    margin-right: 12px;
  }

  & .text {
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    & .name {
      width: 180px;
      margin-bottom: 8px;
      .text-overflow(2);
    }

    & .standard {
      color: #888b98;
      width: 180px;
      .text-overflow();
    }
  }
}

.more {
  color: #888b98;
  font-size: 12px;
  display: block;
  cursor: pointer;
  border: none !important;
  outline: none !important;
}

.remark {
  color: #888b98;
  display: inline-block;
  width: 57px;
  margin-right: 20px;
  text-align: right;
  vertical-align: top;
}

.remarkValue {
  display: inline-block;
  width: 220px;
}

//.address {
//  display: inline-block;
//  width: 220px;
//}

.btn {
  margin-top: 16px;
  text-align: center;
  border: none !important;
  outline: none !important;
}

.moreBtn {
  display: inline-block;
  margin: 0 auto;
  padding: 4px 8px;
  border-radius: 10px;
  border: 1px solid #f3f3f3;
  cursor: pointer;
}

.total {
  margin-top: 20px;
  text-align: right;
}

.totalNum {
  font-size: 18px;
  font-weight: 600;
}

.lookMore {
  margin-left: 4px;
  cursor: pointer;
}

.footer {
  display: flex;
  padding: 24px 20px;
}

.editor {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}
