@import 'styles/mixins/mixins';

.drawer {
  position: absolute !important;
  transition: none !important;

  :global {
    .ant-drawer-content-wrapper {
      height: 80% !important;
      transform: none !important;
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-wrapper-body {
      padding-top: 0;
      background: #f5f6fa;
    }

    .ant-drawer-mask {
      height: 100%;
      background-color: rgb(0 0 0 / 40%) !important;
    }
  }
}

.close {
  font-size: 18px;
  cursor: pointer;
}

.list {
  padding: 0 20px;
  border-radius: 18px;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 6%);
  background-color: #fff;
  overflow: hidden;
}
