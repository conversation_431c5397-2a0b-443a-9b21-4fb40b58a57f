import { useState, useEffect } from 'react';
import { Drawer, Icon } from '@/components';
import { StockGoodsInfo } from '@/apis';
import BatchGoodsItem from './batch-goods-item';
import styles from './batch-goods-list.module.less';

interface BatchGoodsListProps {
  visible: boolean;
  isBatch: boolean;
  orderType: number;
  goodsList: StockGoodsInfo[];
  onClose: () => void;
}

function BatchGoodsList({ visible, isBatch, orderType, goodsList, onClose }: BatchGoodsListProps) {
  const [list, setList] = useState<StockGoodsInfo[]>(goodsList);

  useEffect(() => {
    if (visible) {
      const arr: StockGoodsInfo[] = JSON.parse(JSON.stringify(goodsList));
      arr.forEach((item) => {
        const items = item;
        items.isMore = false;
      });
      setList(arr);
    }
  }, [visible]); // eslint-disable-line

  const onMore = (idVal: number) => {
    list.forEach((item) => {
      const items = item;
      if (Number(items.id || items.shopSkuId) === idVal) {
        items.isMore = !items.isMore;
      }
    });
    setList([...list]);
  };

  return (
    <Drawer
      visible={visible}
      placement="bottom"
      width={375}
      getContainer={false}
      className={styles.drawer}
      title="全部商品"
      closable={false}
      onClose={onClose}
      mask
      extra={<Icon name="close" className={styles.close} onClick={onClose} />}
    >
      <div className={styles.list}>
        {list.map((item) => (
          <BatchGoodsItem
            key={item.id}
            orderType={orderType}
            info={item}
            isBatch={isBatch}
            onMore={onMore}
          />
        ))}
      </div>
    </Drawer>
  );
}

export default BatchGoodsList;
