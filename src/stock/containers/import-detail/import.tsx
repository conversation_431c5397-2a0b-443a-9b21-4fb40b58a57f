import { PropsWithChildren, useEffect, useRef, useState, useMemo } from 'react';
import { Drawer, Icon } from '@/components';
import { Button, Upload, Progress, message, Modal, Select } from 'antd';
import type { UploadProps } from 'antd';
import {
  stockWarehouseList,
  StockWarehouseResult,
  postPlatInBoundData,
  postWareAllotTemplate,
  postWareExportInBound,
  postPlatOutBoundData,
  postWareExportOutBound,
  postWareExportAllot,
} from '@/apis';
import { useRequest } from 'ahooks';
import classNames from 'classnames';
import styles from './import.module.less';
import filePng from '../../assets/imgs/file.png';

// 调拔单
const IMPORT_ITEM = [
  {
    title: '导入',
    name: '入库',
    importName: '入库单导入模板.xlsx',
    importApi: postPlatInBoundData,
    downloadApi: postWareExportInBound,
  },
  {
    title: '导入',
    name: '出库',
    importName: '出库单导入模板.xlsx',
    importApi: postPlatOutBoundData,
    downloadApi: postWareExportOutBound,
  },

  {
    title: '导入',
    name: '调拔单',
    importName: '调拔单导入模板.xlsx',
    importApi: postWareExportAllot,
    downloadApi: postWareAllotTemplate,
  },
];

interface ImportPropps {
  visible: boolean;
  // 注意事项
  noteList?: string[];
  importValue: number;
  // eslint-disable-next-line no-unused-vars
  onClose: () => void;
}

interface fileInfoResult {
  size: number;
  name: string;
  originFileObj: any;
}

function ImportDetail({
  visible,
  noteList,
  importValue,
  onClose,
  ...props
}: PropsWithChildren<ImportPropps>) {
  const paramsWarehouse = useRef({ pageNo: 1, totalPages: 0, pageSize: 10, warehouseStatus: 0 });
  const [warehouses, setWarehouses] = useState<StockWarehouseResult[]>([]);
  const [warehouseNo, setWarehouseNo] = useState('');
  const [fileInfo, setFileInfo] = useState<fileInfoResult>({
    size: 0,
    name: '',
    originFileObj: null,
  });
  const [percent, setPercent] = useState(0);

  const { run: runWarehouse, loading: loadingWarehouse } = useRequest(stockWarehouseList, {
    defaultParams: [paramsWarehouse.current],
    manual: true,
    onSuccess: (result) => {
      paramsWarehouse.current.pageNo += 1;
      paramsWarehouse.current.totalPages = result.pagination.total;
      setWarehouses([...warehouses, ...result.list]);
    },
  });

  const uploadProps: UploadProps = {
    multiple: true,
    maxCount: 1,
    showUploadList: false,
    beforeUpload() {},
    onChange(e) {
      // @ts-ignore
      setFileInfo(e.file);
      if (e.event) {
        const { event } = e;
        // @ts-ignore
        setPercent(Math.floor((event.loaded / event.total) * 100));
      }
    },
  };

  const fileSize = useMemo(() => {
    if (fileInfo.size / 1024 / 1024 > 1) {
      return `${(fileInfo.size / 1024 / 1024).toFixed(2)}M`;
    }
    return `${(fileInfo.size / 1024).toFixed(2)}KB`;
  }, [fileInfo.size]);

  const importItem = useMemo(() => IMPORT_ITEM[importValue], [importValue]);

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const downTemplate = () => {
    importItem.downloadApi(warehouseNo).then((res) => {
      const URL = window.webkitURL || window.URL;
      const url = URL.createObjectURL(res);
      urlDownload(url, importItem.importName);
    });
    return false;
  };

  const importData = () => {
    const formData = new FormData();
    formData.append('file', fileInfo.originFileObj);
    // @ts-ignore
    importItem.importApi(formData).then((res) => {
      if (res.list && res.list.length > 0) {
        Modal.confirm({
          title: '导入错误',
          icon: '',
          okText: '确认',
          centered: true,
          className: styles.importData,
          content: (
            <div className={styles.errorList}>
              {res.list.map((item: any) => (
                <div className={styles.errorItem}>{item.errorMessage}</div>
              ))}
            </div>
          ),
          closable: true,
          onOk: () => {
            onClose();
          },
        });
      } else {
        message.success('数据处理中，导入结果会发送到消息列表，届时请关注');
        setFileInfo({
          size: 0,
          name: '',
          originFileObj: null,
        });
        onClose();
      }
    });
  };

  const onCloseDrawer = () => {
    setFileInfo({
      size: 0,
      name: '',
      originFileObj: null,
    });
    onClose();
  };

  const footer = (
    <div className={styles.footer}>
      <Button disabled={!fileInfo.originFileObj} type="primary" block onClick={importData}>
        确认
      </Button>
    </div>
  );

  useEffect(() => {
    if (visible) {
      setWarehouses([]);
      runWarehouse(paramsWarehouse.current);
    }
  }, [runWarehouse, visible]);

  return (
    <Drawer
      title={importItem.title}
      className={styles.filter}
      visible={visible}
      onClose={onCloseDrawer}
      {...props}
      footer={footer}
    >
      <div className={styles.card}>
        {!!importValue && (
          <>
            <div className={styles.title}>请选择需要{importItem.name}的仓库</div>
            <Select
              bordered={false}
              allowClear
              showSearch
              suffixIcon={<Icon name="down" size={16} />}
              placeholder="请选择仓库"
              onChange={(e) => {
                setWarehouseNo(e);
              }}
              onPopupScroll={() => {
                if (
                  loadingWarehouse ||
                  paramsWarehouse.current.pageNo > paramsWarehouse.current.totalPages
                )
                  return;
                runWarehouse(paramsWarehouse.current);
              }}
              options={warehouses.map((item) => ({
                label: item.warehouseName,
                value: item.warehouseNo,
              }))}
            />
          </>
        )}
        <div className={styles.title}>注意事项</div>
        {noteList?.map((character) => (
          <div className={styles.label}>{character}</div>
        ))}
        <div className={styles.down}>
          <div className={styles.tip}>
            请按照数据模版的格式准备导入。点击下载《{importItem.name}导入模板》
          </div>
          <div className={styles.btn}>
            <Button
              type="primary"
              size="small"
              disabled={!warehouseNo && !!importValue}
              onClick={() => {
                downTemplate();
              }}
            >
              下载模板
            </Button>
          </div>
        </div>
      </div>
      <div className={styles.card}>
        <div className={classNames(styles.title, styles.startRed)}>上传文件</div>
        <Upload {...uploadProps}>
          <div className={styles.upload}>
            {fileInfo.name ? (
              <Icon
                name="close-outline1"
                className={styles.icon}
                size={16}
                color="#999EB2"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  setFileInfo({
                    size: 0,
                    name: '',
                    originFileObj: '',
                  });
                }}
              />
            ) : null}
            <img className={styles.img} src={filePng} alt="" />
            {!fileInfo.name ? (
              <div className={styles.text}>选择或拖拽文件到此处</div>
            ) : (
              <div>{fileInfo.name}</div>
            )}
            {percent && percent !== 100 ? (
              <div className={styles.progress}>
                <Progress percent={percent} strokeColor="#6484FE" trailColor="#F5F6FA" />
                <span>{percent}%</span>
              </div>
            ) : null}
            {fileInfo.size ? <div className={styles.size}>{fileSize}</div> : null}
          </div>
        </Upload>
      </div>
    </Drawer>
  );
}

ImportDetail.defaultProps = {
  noteList: [
    '1、模版中表头名称不能更改，表头行不能删除',
    '2、其中标*为必填项，必须填写',
    '3、导入文件请勿超过20MB',
  ],
};

export default ImportDetail;
