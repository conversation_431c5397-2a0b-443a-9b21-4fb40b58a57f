import {
  PropsWith<PERSON><PERSON><PERSON>n,
  useEffect,
  useRef,
  useState,
  useCallback,
  useReducer,
  forwardRef,
  useImperativeHandle,
  useMemo,
} from 'react';
import { Search, Drawer, Icon, Empty } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Button, Spin, Checkbox, Select, message } from 'antd';
import { Stepper } from 'antd-mobile';
import BigNumber from 'big.js';
import {
  getCategoryCustomeList,
  GetCategoryCustomeItemResult,
  stockGoodsList,
  StockGoodsListResult,
  formulaConvertManyconvert,
  FormulaConvertManyconvertResult,
  goodsUnitConvert,
  formulaConvert,
} from '@/apis';
import classNames from 'classnames';
import PopupModal from '../../components/modal/index';
import cart from '../../assets/imgs/cart.png';
import GoodsItem from '../../components/goods-item';
import styles from './index.module.less';

const pageSize = 10;

interface SelectGoodsPropps {
  visible: boolean;
  orderType: number;
  // eslint-disable-next-line no-unused-vars
  confirm: (arr: StockGoodsListResult[]) => void | Promise<void>;
  onClose: () => void;
  selectGoodsList: StockGoodsListResult[];
  warehouseNo: string;
}

interface SelectGoodsRefs {
  initData: () => void;
}

interface InitialState {
  customizeCategorySet: Array<number | null>;
  keyword: string;
  pageNo: number;
  totalPage: number;
  list: StockGoodsListResult[];
  warehouseNo: string;
  isQuerySelfSku: number;
  isInboundType: number;
}

const initialState = {
  customizeCategorySet: [],
  keyword: '',
  pageNo: 1,
  totalPage: 1,
  list: [],
  isQuerySelfSku: 0,
  isInboundType: 0,
  warehouseNo: '',
};

type GoodsAction =
  | {
      type: 'setPage';
      payload: { pageNo?: number; totalPage?: number };
    }
  | {
      type: 'setList';
      payload: StockGoodsListResult[];
    }
  | {
      type: 'setAll';
      payload: InitialState;
    };

const reducer = (state: InitialState, action: GoodsAction): InitialState => {
  switch (action.type) {
    case 'setPage':
      return { ...state, ...action.payload };
    case 'setList':
      return { ...state, list: action.payload };
    case 'setAll':
      return action.payload;
    default:
      return state;
  }
};

const SelectGoods = forwardRef<SelectGoodsRefs, PropsWithChildren<SelectGoodsPropps>>(
  ({ visible, selectGoodsList, confirm, onClose, orderType, warehouseNo, ...props }, ref) => {
    const [categoryCustomes, setCategoryCustomes] = useState<GetCategoryCustomeItemResult[]>([]);
    const [categoryCustomesTwo, setCategoryCustomesTwo] = useState(
      null as null | GetCategoryCustomeItemResult[]
    );
    const content = useRef(null as unknown as HTMLDivElement);
    const [selectIds, setSelectIds] = useState<Array<number | string | boolean>>([]);
    const [selectCategoryId, setSelectCategoryId] = useState<number | null>(null);
    const [selectCategoryIdTwo, setSelectCategoryIdTwo] = useState<number | null>(null);
    const [units, setUnits] = useState<FormulaConvertManyconvertResult[]>([]);
    const [state, dispatch] = useReducer(reducer, initialState);
    const [showMore, setShowMore] = useState(false);
    const [Modal, setModal] = useState(false);
    const hasSelectGoodsList = useRef<StockGoodsListResult[]>([]);
    const parentIdRef = useRef(0);

    const paramsCustom = useRef({ grade: 1, pageNo: 1, pageSize: 20, totalPages: 0 });

    const productSum = useMemo(() => {
      const hasIds = hasSelectGoodsList.current.map((each) => each.skuId);
      state.list.forEach((each) => {
        if (selectIds.includes(each.skuId)) {
          if (!hasIds.includes(each.skuId)) {
            hasSelectGoodsList.current.push(each);
          } else {
            for (let i = 0; i < hasSelectGoodsList.current.length; i += 1) {
              if (hasSelectGoodsList.current[i].skuId === each.skuId) {
                hasSelectGoodsList.current[i].number = each.number;
              }
            }
          }
        }
      });
      let count = new BigNumber(0);
      hasSelectGoodsList.current
        .filter((item) => selectIds.includes(item.skuId))
        .forEach((item) => {
          count = count.plus(item.number ? item.number : 0);
        });
      return new BigNumber(count).toFixed(6);
    }, [state.list, selectIds]);

    const getCategoryCustomes = () => {
      getCategoryCustomeList({ ...paramsCustom.current }).then((res) => {
        if (paramsCustom.current.pageNo === 1) {
          res.list.unshift({
            id: null,
            categoryName: '全部',
          });
          setCategoryCustomes(res.list);
        } else {
          setCategoryCustomes([...categoryCustomes, ...res.list]);
        }
        paramsCustom.current.totalPages = Math.ceil(
          res.pagination.count / paramsCustom.current.pageSize
        );
      });
    };

    const loadMoreCategory = () => {
      paramsCustom.current.pageNo += 1;
      if (!(paramsCustom.current.pageNo <= paramsCustom.current.totalPages)) return;
      getCategoryCustomes();
    };

    const selectCategory = (id: number | null) => {
      if (!id) {
        setSelectCategoryId(null);
        setCategoryCustomesTwo([]);
        return;
      }
      parentIdRef.current = id;
      setShowMore(false);
      setSelectCategoryId(id);
      getCategoryCustomeList({ pageNo: 1, pageSize: 100, parentId: id || null }).then((res) => {
        setCategoryCustomesTwo(res.list);
        setSelectCategoryIdTwo(null);
      });
    };

    const getGoods = useCallback(
      (argument: InitialState) => {
        stockGoodsList({
          sort: 'desc',
          pageNo: argument.pageNo,
          pageSize,
          isQuerySelfSku: orderType === 9 ? 0 : 1,
          isInventoryType: orderType === 11 ? 1 : 0,
          isWarehouseFilter: orderType === 9 ? 0 : 1,
          warehouseNo,
          keyword: argument.keyword,
          customizeCategorySet: argument.customizeCategorySet,
          standardFilterParams: [],
          shopSkuNames: [],
        }).then((res: { list: StockGoodsListResult[]; pagination: { count: number } }) => {
          if (hasSelectGoodsList.current.length) {
            for (let i = 0; i < res.list.length; i += 1) {
              hasSelectGoodsList.current.forEach((item1) => {
                if (res.list[i].skuId === item1.skuId) {
                  res.list[i] = item1;
                }
              });
            }
          }
          dispatch({
            type: 'setAll',
            payload: {
              customizeCategorySet: argument.customizeCategorySet,
              keyword: argument.keyword,
              isQuerySelfSku: argument.isQuerySelfSku,
              isInboundType: argument.isInboundType,
              warehouseNo: argument.warehouseNo,
              pageNo: argument.pageNo + 1,
              list: [...argument.list, ...res.list],
              totalPage: Math.ceil(res.pagination.count / pageSize),
            },
          });
        });
      },
      [orderType, warehouseNo]
    );

    const loadMore = () => {
      if (!(state.pageNo <= state.totalPage)) return;
      getGoods(state);
    };

    const initData = () => {
      dispatch({
        type: 'setList',
        payload: [],
      });
    };

    // 关闭抽屉
    const onCloseDrawer = () => {
      let flag = false;
      state.list.forEach((tmpItem) => {
        for (let i = 0; i < selectGoodsList.length; i += 1) {
          if (tmpItem.skuId === selectGoodsList[i].skuId) {
            if (
              tmpItem.unit !== (selectGoodsList[i].auxiliaryUnit || selectGoodsList[i].unit) ||
              tmpItem.number !== selectGoodsList[i].number
            ) {
              flag = true;
            }
          }
        }
      });
      if (flag || (selectGoodsList.length === 0 && selectIds.length > 0)) {
        setModal(true);
      } else {
        dispatch({
          type: 'setList',
          payload: [],
        });
        onClose();
        initData();
      }
    };

    const onChangeGoods = (e: any, skuId: number, val: StockGoodsListResult) => {
      const arr = JSON.parse(JSON.stringify(selectIds));
      if (e?.target.checked) {
        arr.push(skuId);
        hasSelectGoodsList.current.push(val);
      } else {
        for (let i = 0; i < arr.length; i += 1) {
          if (skuId === arr[i]) {
            arr.splice(i, 1);
          }
        }
        hasSelectGoodsList.current = hasSelectGoodsList.current.filter(
          (item) => item.skuId !== skuId
        );
      }
      setSelectIds(arr);
    };

    // 获取单位
    const getUnit = (templateId: number, mainUnitName: string) => {
      formulaConvertManyconvert({
        templateId,
        mainUnitName,
      }).then((res) => {
        // @ts-ignore
        setUnits(res.list);
      });
    };

    // 切换单位
    const onChangeUnit = (item: StockGoodsListResult, e: string) => {
      goodsUnitConvert({
        stockNumber: item.stockNumber || item.stock,
        productMinimumQuantity: item.minimum,
        productMinimumSalesUnit: item.saleGroup,
        skuId: item.skuId,
        number: item.number,
        productUnit: e, // 选中的单位
        productUnitBefore: item.auxiliaryUnit || item.unit, // 选则之前的单位
      }).then((res) => {
        dispatch({
          type: 'setList',
          payload: state.list.map((tmpItem) => {
            if (tmpItem.id !== item.id) {
              return tmpItem;
            }
            return {
              ...tmpItem,
              auxiliaryUnit: e,
              number: Number(res.number) || 0,
              stockNumber: res.stockNumber,
              productMinimumSalesUnit: res.productMinimumSalesUnit,
              productMinimumQuantity: res.productMinimumQuantity,
            };
          }),
        });
        hasSelectGoodsList.current = hasSelectGoodsList.current.map((it) => {
          if (item.id === it.id) {
            return {
              ...it,
              auxiliaryUnit: e,
              number: Number(res.number) || 0,
              stockNumber: res.stockNumber,
              market: Number(res.productMarketPrice),
              productMinimumSalesUnit: res.productMinimumSalesUnit,
              productMinimumQuantity: res.productMinimumQuantity,
              auxiliaryFloorPrice: Number(res.floorPrice) || it.floorPrice,
            };
          }
          return it;
        });
      });
    };

    // 切换数量
    const onChangeNum = (item: StockGoodsListResult, val: number, type: string) => {
      if (
        (item.saleGroup &&
          // @ts-ignore
          BigNumber(val).div(item.saleGroup) % 1 !== 0 &&
          !item.productMinimumSalesUnit) ||
        (item.productMinimumSalesUnit &&
          // @ts-ignore
          BigNumber(val).div(item.productMinimumSalesUnit) % 1 !== 0)
      ) {
        if (type === 'blur') {
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => {
              if (tmpItem.id !== item.id) {
                return tmpItem;
              }
              return {
                ...tmpItem,
                number: tmpItem.num,
              };
            }),
          });
          return message.error('请输入最小销售单元整数倍');
        }
        // return false;
      }
      dispatch({
        type: 'setList',
        payload: state.list.map((tmpItem) => {
          if (tmpItem.id !== item.id) {
            return tmpItem;
          }
          return {
            ...tmpItem,
            number: val,
            num: val,
          };
        }),
      });
      return false;
    };

    // 统计数量
    const computeNum = () => {
      const hasIds = hasSelectGoodsList.current.map((each) => each.skuId);
      state.list.forEach((each) => {
        if (selectIds.includes(each.skuId)) {
          if (!hasIds.includes(each.skuId)) {
            hasSelectGoodsList.current.push(each);
          } else {
            for (let i = 0; i < hasSelectGoodsList.current.length; i += 1) {
              if (hasSelectGoodsList.current[i].skuId === each.skuId) {
                hasSelectGoodsList.current[i].number = each.number;
                hasSelectGoodsList.current[i].num = each.num;
                hasSelectGoodsList.current[i].preferential = each.preferential;
              }
            }
          }
        }
      });
    };

    useImperativeHandle(ref, () => ({
      initData,
    }));

    useEffect(() => {
      if (visible) {
        setCategoryCustomesTwo([]);
        setSelectCategoryId(null);
        getCategoryCustomes();
        getGoods(initialState);
      }
    }, [getGoods, visible]); // eslint-disable-line

    useEffect(() => {
      if (visible) {
        if (selectGoodsList.length) {
          hasSelectGoodsList.current = JSON.parse(JSON.stringify(selectGoodsList));
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => {
              for (let i = 0; i < selectGoodsList.length; i += 1) {
                if (tmpItem.skuId === selectGoodsList[i].skuId) {
                  return {
                    ...tmpItem,
                    number: selectGoodsList[i].number || 0,
                    num: selectGoodsList[i].num || 0,
                    unit: selectGoodsList[i].auxiliaryUnit || selectGoodsList[i].unit,
                    preferential:
                      selectGoodsList[i].preferential || selectGoodsList[i].preferential,
                  };
                }
              }
              return {
                ...tmpItem,
                number: 0,
                num: 0,
              };
            }),
          });
          const ids = selectGoodsList.map((item) => item.skuId);
          // @ts-ignore
          setSelectIds(ids);
        } else if (state.list.length) {
          hasSelectGoodsList.current = [];
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => ({
              ...tmpItem,
              number: 0,
              num: 0,
            })),
          });
        }
      } else {
        state.keyword = '';
        state.pageNo = 1;
        state.list = [];
        // getGoods(state);
        hasSelectGoodsList.current = [];
        setSelectIds([]);
      }
    }, [visible]); // eslint-disable-line

    return (
      <Drawer
        visible={visible}
        {...props}
        getContainer={() => document.querySelector('#content') as HTMLElement}
        className={styles.selectGoods}
        title="添加商品"
        onClose={onCloseDrawer}
        footer={
          <div className={styles.footer}>
            <div className={styles.footerText}>
              <img className={styles.img} src={cart} alt="" />
              <span>总数量 </span>
              <span className={styles.num}>{productSum}</span>
            </div>
            <Button
              type="primary"
              onClick={() => {
                computeNum();
                const arr = hasSelectGoodsList.current.filter((item) =>
                  selectIds.includes(item.skuId)
                );
                if (!arr.length) return message.error('请选择商品');
                if (arr.some((item) => !item.number)) return message.error('请填写商品数量!');
                confirm(arr);
                return false;
              }}
            >
              选好了
            </Button>
          </div>
        }
      >
        <div className={styles.search}>
          <Search
            className={styles.searchInput}
            placeholder="搜索商品名称"
            onSearch={(searchKey) => {
              state.keyword = searchKey;
              state.pageNo = 1;
              state.list = [];
              getGoods(state);
            }}
          />
        </div>
        <div ref={content} className={styles.box}>
          <div className={styles.category} id="category">
            <InfiniteScroll
              dataLength={categoryCustomes.length}
              className={styles.categoryScrollbar}
              hasMore={paramsCustom.current.pageNo < paramsCustom.current.totalPages}
              loader={
                <div className="text-center">
                  <Spin tip="加载中..." />
                </div>
              }
              next={loadMoreCategory}
              scrollableTarget="category"
            >
              {categoryCustomes
                ? categoryCustomes.map((item) => (
                    <div
                      role="button"
                      tabIndex={item.id || 0}
                      key={item.id}
                      className={classNames(styles.item, {
                        [styles.itemActive]: selectCategoryId === item.id,
                      })}
                      onClick={() => {
                        computeNum();
                        state.customizeCategorySet = [item.id || null];
                        // state.keyword = '';
                        state.pageNo = 1;
                        state.list = [];
                        getGoods(state);
                        selectCategory(item.id);
                        setSelectCategoryIdTwo(null);
                      }}
                    >
                      {item.categoryName}
                    </div>
                  ))
                : null}
            </InfiniteScroll>
          </div>
          <div className={styles.boxList}>
            {categoryCustomesTwo && categoryCustomesTwo.length > 0 ? (
              <div className={styles.categoryList}>
                {categoryCustomesTwo
                  ? categoryCustomesTwo.slice(0, 2).map((item) => (
                      <span
                        role="button"
                        tabIndex={item.id || 0}
                        key={item.id}
                        className={classNames(
                          selectCategoryIdTwo === item.id
                            ? styles.categoryItemActive
                            : styles.categoryItem,
                          'mb-4'
                        )}
                        onClick={() => {
                          computeNum();
                          state.pageNo = 1;
                          state.keyword = '';
                          state.list = [];
                          if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                            setSelectCategoryIdTwo(null);
                            state.customizeCategorySet = [parentIdRef.current];
                          } else {
                            setSelectCategoryIdTwo(item.id);
                            state.customizeCategorySet = [item.id];
                          }
                          getGoods(state);
                        }}
                      >
                        {item.categoryName}
                      </span>
                    ))
                  : null}
                {categoryCustomesTwo.length > 2 ? (
                  <span
                    role="button"
                    tabIndex={0}
                    className={styles.filter}
                    onClick={() => {
                      setShowMore(!showMore);
                    }}
                  >
                    <Icon name="down" color="#000" />
                  </span>
                ) : null}
              </div>
            ) : null}
            {showMore ? (
              <div className={styles.categoryMore}>
                <div className={styles.categoryBox}>
                  {categoryCustomesTwo
                    ? categoryCustomesTwo.slice(2, categoryCustomesTwo.length).map((item) => (
                        <span
                          role="button"
                          tabIndex={item.id || 0}
                          key={item.id}
                          className={classNames(
                            selectCategoryIdTwo === item.id
                              ? styles.categoryItemActive
                              : styles.categoryItem,
                            'mb-4'
                          )}
                          onClick={() => {
                            computeNum();
                            state.pageNo = 1;
                            state.keyword = '';
                            state.list = [];
                            if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                              setSelectCategoryIdTwo(null);
                              state.customizeCategorySet = [parentIdRef.current];
                            } else {
                              setSelectCategoryIdTwo(item.id);
                              state.customizeCategorySet = [item.id];
                            }
                            getGoods(state);
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))
                    : null}
                </div>
              </div>
            ) : null}
            <div
              className={classNames(state.list.length === 0 ? styles.noData : styles.list)}
              id="list"
            >
              {state.list.length > 0 ? (
                <InfiniteScroll
                  dataLength={state.list.length}
                  hasMore={state.pageNo <= state.totalPage}
                  loader={
                    <div className="text-center">
                      <Spin tip="加载中..." />
                    </div>
                  }
                  next={loadMore}
                  scrollableTarget="list"
                >
                  <Checkbox.Group defaultValue={selectIds}>
                    {state.list.map((item) => (
                      <div className={styles.goodsItem} key={item.id}>
                        <div className={styles.goods}>
                          <GoodsItem
                            name={item.name}
                            img={item.images || (item.imagesList && item.imagesList[0])}
                            standard={item.standardList}
                            stock={Number(item.stockNumber || item.stock)}
                            showStock
                            saleGroup={Number(item.productMinimumSalesUnit || item.saleGroup || 1)}
                            skuBelongType={Number(item.skuBelongType)}
                            onTheWaySkuNumber={item.onTheWaySkuNumber}
                          />
                          <Checkbox
                            value={item.skuId}
                            onChange={(e) => {
                              if (e.target.checked && item.unitTemplateId) {
                                formulaConvert(item.unitTemplateId || 0).then((res) => {
                                  // @ts-ignore
                                  setUnits(res.detailList);
                                });
                              }
                              onChangeGoods(e, item.skuId, item);
                            }}
                          />
                        </div>
                        {selectIds.includes(item.skuId) ? (
                          <>
                            <div className={styles.goodsSelect}>
                              <div className={styles.label}>商品单位</div>
                              {item.unitTemplateId ? (
                                <Select
                                  placeholder="请选择"
                                  defaultValue={item.auxiliaryUnit || item.unit}
                                  value={item.auxiliaryUnit || item.unit}
                                  bordered={false}
                                  className={styles.select}
                                  options={units.map((each) => ({
                                    label: each.unitName,
                                    value: each.unitName,
                                  }))}
                                  onFocus={() => {
                                    if (item.unitTemplateId && item.unit) {
                                      getUnit(item.unitTemplateId, item.unit);
                                    }
                                  }}
                                  onChange={(e) => {
                                    onChangeUnit(item, e);
                                  }}
                                />
                              ) : (
                                <Select
                                  bordered={false}
                                  className={styles.select}
                                  value={item.unit}
                                  disabled
                                />
                              )}
                            </div>
                            <div className={styles.goodsSelect}>
                              <div className={styles.label}>商品数量</div>
                              <Stepper
                                max={99999999}
                                min={
                                  Number(item.productMinimumQuantity) || Number(item.minimum) || 0
                                }
                                step={
                                  Number(item.productMinimumSalesUnit) ||
                                  Number(item.saleGroup) ||
                                  1
                                }
                                value={item.number}
                                defaultValue={Number(item.productMinimumQuantity || item.minimum)}
                                onBlur={(e) => {
                                  onChangeNum(item, Number(e.target.value), 'blur');
                                }}
                                onChange={(e) => {
                                  onChangeNum(item, e, 'change');
                                }}
                                className={styles.inputNumber}
                              />
                            </div>
                          </>
                        ) : null}
                      </div>
                    ))}
                  </Checkbox.Group>
                </InfiniteScroll>
              ) : (
                <Empty />
              )}
            </div>
          </div>
        </div>
        <PopupModal
          visible={Modal}
          onDetermine={() => {
            setModal(false);
          }}
          onclose={() => {
            dispatch({
              type: 'setList',
              payload: state.list.map((tmpItem) => {
                for (let i = 0; i < selectGoodsList.length; i += 1) {
                  if (tmpItem.id === selectGoodsList[i].id) {
                    return {
                      ...tmpItem,
                      number: selectGoodsList[i].number,
                    };
                  }
                }
                return {
                  ...tmpItem,
                };
              }),
            });
            setModal(false);
            onClose();
          }}
        />
      </Drawer>
    );
  }
);

export default SelectGoods;
