import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { DatePicker, Drawer, Icon } from '@/components';
import { Form, FormInstance } from 'antd';
import classNames from 'classnames';
import { postPlatWareHouse, ReportAllotParams, WareListRusult } from '@/apis';
import styles from './index.module.less';
import Warehouse from '../filter-warehouse';

interface FilterPropps {
  visible: boolean;
  onClose: () => void;
  data: ReportAllotParams;
  // eslint-disable-next-line no-unused-vars
  confirm: (arg: ReportAllotParams) => void;
}

const orderList = [
  {
    businessType: 2,
    name: '草稿',
  },
  {
    businessType: 0,
    name: '待审批',
  },
  {
    businessType: 1,
    name: '已审批',
  },
  {
    businessType: 3,
    name: 'OA审批中',
  },
];

function TransferReportFilter({
  visible,
  confirm,
  data,
  onClose,
  ...props
}: PropsWithChildren<FilterPropps>) {
  const formRef = useRef(null as unknown as FormInstance);
  const [warehouses, setWarehouse] = useState<WareListRusult[]>([]);
  const [intWarehouse, setInWarehouse] = useState<number[]>([0]);
  const [isInOrOut, setIsInOrOut] = useState('in');
  const [outWarehouse, setOutWarehouse] = useState<number[]>([0]);
  const [count, setCount] = useState(0);
  const [showWarehouse, setShowWarehouse] = useState(false);
  const [filterParams, setFilterParams] = useState<ReportAllotParams>({
    key: '',
    startDate: '',
    endDate: '',
    orderStatusList: [],
    orignWarehouseIds: [],
    destinationWarehouseIds: [],
  });

  const disabledDateStart = (current: Dayjs) => {
    const endDateFoo = formRef.current.getFieldsValue().endDate;
    return (
      current &&
      (current > dayjs(endDateFoo).endOf('day') ||
        current < dayjs(dayjs(endDateFoo).subtract(1, 'year')).add(1, 'days'))
    );
  };

  const disabledDateEnd = (current: Dayjs) => {
    const startDateFoo = formRef.current.getFieldsValue().startDate;
    return (
      current &&
      (current > dayjs(startDateFoo).add(1, 'year') ||
        current < dayjs(startDateFoo).subtract(0, 'day'))
    );
  };

  const selectWarehouses = (id: number, isIn: boolean) => {
    const list = isIn ? [...intWarehouse] : [...outWarehouse];
    warehouses.forEach((item) => {
      if (item.id === id) {
        if (list.indexOf(id) > -1) {
          list.splice(list.indexOf(id), 1);
        } else {
          list.push(item.id);
        }
      }
    });

    setFilterParams({
      ...filterParams,
      [isIn ? 'destinationWarehouseIds' : 'orignWarehouseIds']: id
        ? list.filter((item) => item)
        : [],
    });
    (isIn ? setInWarehouse : setOutWarehouse)(id ? list.filter((item) => item) : [0]);
  };

  const getWareList = () => {
    postPlatWareHouse({
      pageNo: 1,
      pageSize: 4,
    }).then((res) => {
      setWarehouse([{ id: 0, warehouseName: '全部仓库', warehouseStatus: 0 }, ...res.list]);
      setCount(res.pagination.count);
      if (data.startDate) {
        formRef.current.setFieldsValue({
          startDate: dayjs(Number(data.startDate)),
        });
      }
      if (data.endDate) {
        formRef.current.setFieldsValue({
          endDate: dayjs(Number(data.endDate)),
        });
      }
    });
  };

  useEffect(() => {
    if (visible) {
      const dataFoo = {
        key: data?.key || '',
        startDate: data?.startDate || String(dayjs().subtract(1, 'month').valueOf()),
        endDate: data?.endDate || String(dayjs().valueOf()),
        orderStatusList: data?.orderStatusList || [],
        orignWarehouseIds: data?.orignWarehouseIds || [],
        destinationWarehouseIds: data?.destinationWarehouseIds || [],
      };
      setFilterParams(dataFoo);
      getWareList();
      setTimeout(() => {
        formRef.current.setFieldsValue({
          startDate: dayjs(Number(data.startDate)),
          endDate: dayjs(Number(data.endDate)),
        });
      });
    }
  }, [visible, data]); // eslint-disable-line

  return (
    <Drawer
      title="筛选"
      visible={visible}
      {...props}
      className={styles.filter}
      onClose={() => confirm(filterParams)}
    >
      <div className={styles.card}>
        <div className={styles.title}>调出仓库</div>
        <div className={styles.list}>
          {warehouses.map((item, index) => (
            <span
              className={outWarehouse.includes(item.id as number) ? styles.itemActive : styles.item}
              role="button"
              key={item.id}
              tabIndex={index}
              onClick={() => {
                selectWarehouses(item.id as number, false);
              }}
            >
              <span className={styles.name}>{item.warehouseName}</span>
            </span>
          ))}
          {count > 4 ? (
            <span
              role="button"
              tabIndex={0}
              className={styles.itemActive}
              onClick={() => {
                setIsInOrOut('out');
                setShowWarehouse(true);
              }}
            >
              更多仓库
              <Icon name="right" />
            </span>
          ) : null}
        </div>
      </div>
      <div className={styles.card}>
        <div className={styles.title}>调入仓库</div>
        <div className={styles.list}>
          {warehouses.map((item, index) => (
            <span
              className={intWarehouse.includes(item.id as number) ? styles.itemActive : styles.item}
              role="button"
              key={item.id}
              tabIndex={index}
              onClick={() => {
                selectWarehouses(item.id as number, true);
              }}
            >
              <span className={styles.name}>{item.warehouseName}</span>
            </span>
          ))}
          {count > 4 ? (
            <span
              role="button"
              tabIndex={0}
              className={styles.itemActive}
              onClick={() => {
                setIsInOrOut('in');
                setShowWarehouse(true);
              }}
            >
              更多仓库
              <Icon name="right" />
            </span>
          ) : null}
        </div>
      </div>

      <div className={styles.card}>
        <div className={styles.title}>单据状态</div>
        <div className={styles.list}>
          {orderList.map((item) => (
            <span
              role="button"
              tabIndex={0}
              key={item.businessType}
              onClick={() => {
                let arr = filterParams.orderStatusList as number[];
                if (arr.includes(item.businessType)) {
                  arr = arr.filter((f) => f !== item.businessType);
                } else {
                  arr.push(item.businessType);
                }
                setFilterParams({
                  ...filterParams,
                  orderStatusList: arr,
                });
              }}
              className={
                filterParams.orderStatusList.includes(item.businessType)
                  ? styles.itemActive
                  : styles.item
              }
            >
              {item.name}
            </span>
          ))}
        </div>
      </div>

      <div className={styles.card}>
        <div className={styles.title}>单据时间</div>
        <div className={styles.time}>
          <Form ref={formRef}>
            <Form.Item name="startDate">
              <DatePicker
                onChange={(e) => {
                  setFilterParams({
                    ...filterParams,
                    startDate: String(dayjs(e).startOf('day').valueOf() || ''),
                  });
                }}
                allowClear={false}
                disabledDate={disabledDateStart}
                placeholder="请选择开始时间"
                bordered={false}
              />
            </Form.Item>

            <span className={styles.line} />
            <Form.Item name="endDate">
              <DatePicker
                onChange={(e) => {
                  setFilterParams({
                    ...filterParams,
                    endDate: String(dayjs(e).startOf('day').valueOf() + 86399999 || ''),
                  });
                }}
                disabledDate={disabledDateEnd}
                placeholder="请选择结束时间"
                allowClear={false}
                bordered={false}
              />
            </Form.Item>
          </Form>
        </div>
      </div>

      <div className={styles.footer}>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.footerBtn, styles.footerBtnEdit)}
          onClick={() => {
            const dataFoo = {
              key: data?.key || '',
              startDate: String(dayjs().subtract(1, 'month').valueOf()),
              endDate: String(dayjs().valueOf()),
              orderStatusList: [],
              orignWarehouseIds: [],
              destinationWarehouseIds: [],
            };
            setFilterParams(dataFoo);
            setTimeout(() => {
              formRef.current.setFieldsValue({
                startDate: dayjs(dayjs().subtract(1, 'month').valueOf()),
                endDate: dayjs(dayjs().valueOf()),
              });
            });
            setInWarehouse([0]);
            setOutWarehouse([0]);
          }}
        >
          重置
        </div>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.footerBtn, styles.footerBtnAdd)}
          onClick={() => {
            confirm(filterParams);
          }}
        >
          确定
        </div>
      </div>

      <Warehouse
        confirm={(e) => {
          const arr =
            isInOrOut === 'in'
              ? filterParams.destinationWarehouseIds
              : filterParams.orignWarehouseIds;
          setShowWarehouse(false);
          // @ts-ignore
          (isInOrOut === 'in' ? setInWarehouse : setOutWarehouse)(e);
          setFilterParams({
            ...filterParams,
            [isInOrOut === 'in' ? 'destinationWarehouseIds' : 'orignWarehouseIds']: [
              ...new Set((arr || []).concat(e)),
            ],
          });
        }}
        visible={showWarehouse}
        selectWarehouse={isInOrOut === 'in' ? intWarehouse : outWarehouse}
        onCloseWarehouse={() => {
          setShowWarehouse(false);
        }}
      />
    </Drawer>
  );
}

export default TransferReportFilter;
