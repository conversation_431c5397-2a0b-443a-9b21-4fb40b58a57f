.card {
  margin-bottom: 20px;
  padding: 16px 20px 0;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-picker.ant-picker-borderless {
      width: 100%;
    }
  }
}

.cardBorder {
  border-bottom: 1px solid #f3f3f3;
}

.field {
  padding-bottom: 10pxs;
}

.text {
  color: #b1b3be;
  font-size: 12px;
}

.upload {
  margin-top: 12px;
  padding-bottom: 20px;
}

.footer {
  display: flex;
  padding: 24px 20px;
  justify-content: space-between;

  :global {
    button.ant-btn.ant-btn-primary,
    button.ant-btn.ant-btn-default {
      width: 100%;
    }
  }
}
