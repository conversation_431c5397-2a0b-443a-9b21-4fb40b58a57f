import {
  postPlatInOrderUpload,
  postPlatOutOrderUpload,
  stockEnterDetail,
  stockOutDetail,
} from '@/apis';
import { uploadParams } from '@/apis/psi/port-plat-in-order-upload';
import { DatePicker, Drawer, Icon, SimpleUploadInstance, Upload } from '@/components';
import { usePermission } from '@/hooks';
import { checkCharge, checkPermission } from '@/utils/permission';
import { Button, Form, FormInstance, Input } from 'antd';
import dayjs from 'dayjs';
import { PropsWithChildren, useRef, useState } from 'react';
import styles from './index.module.less';

interface FilterPropps {
  visible: boolean;
  id: number;
  orderType: number;
  onClose: () => void;
}

function OrderUpload({
  visible,
  onClose,
  id,
  orderType,
  ...props
}: PropsWithChildren<FilterPropps>) {
  const formRef = useRef(null as unknown as FormInstance);
  const uploadRef = useRef(null as unknown as SimpleUploadInstance);
  const [state, setState] = useState(false);
  const [loading, setLoading] = useState(false);
  const uploadCode = orderType === 9 ? 'R_001_002_012_001' : 'R_001_001_012_001';
  const uploadDisabled = !checkCharge(uploadCode) || !checkPermission(uploadCode);

  const confirm = () => {
    const detailsRequest = orderType === 9 ? stockEnterDetail : stockOutDetail;
    const request = orderType === 9 ? postPlatInOrderUpload : postPlatOutOrderUpload;

    detailsRequest(id).then((res) => {
      setLoading(true);
      const { expressNo, expressName, expressPhone, logisticsCreateTime } =
        formRef.current.getFieldsValue();
      const attachmentList = uploadRef.current.getFileList();
      const data: uploadParams = {
        id: res.id,
        orderNo: res.orderTradeNo,
        batchNo: res.batchNo,
        warehouseId: res.warehouseId,
        attachmentsType: 1,
        checklistCreateTime: res.orderDate,
        remark: res.remark,
        logisticsInfo: {
          expressNo,
          expressName,
          expressPhone,
          logisticsCreateTime: String(new Date(logisticsCreateTime).getTime()),
        },
        attachmentsOssAddrList: attachmentList.map((item) => ({
          fileName: item.name,
          size: Number(item.size) || 0,
          filePath: item.url,
          fileType: item.type?.indexOf('image') !== -1 ? 0 : 1,
        })),
      };
      request(data)
        .then(() => {
          onClose();
        })
        .finally(() => {
          setLoading(false);
        });
    });
  };

  const haveState = () => {
    const { expressNo, expressName, expressPhone, logisticsCreateTime } =
      formRef.current.getFieldsValue();
    if (expressNo && expressName && expressPhone && logisticsCreateTime) {
      setState(true);
      return;
    }
    setState(false);
  };

  const onConfirm = usePermission(
    orderType === 9 ? 'R_001_002_012_003' : 'R_001_001_012_003',
    () => {
      confirm();
    }
  );

  const onUpload = usePermission(uploadCode);

  return (
    <Drawer
      title={orderType === 9 ? '上传验收附件' : '上传发货附件'}
      visible={visible}
      {...props}
      footer={
        <div className={styles.footer}>
          {state ? (
            <Button type="primary" loading={loading} onClick={onConfirm}>
              {orderType === 9 ? '确认验收' : '确认发货'}
            </Button>
          ) : (
            <Button>{orderType === 9 ? '确认验收' : '确认发货'}</Button>
          )}
        </div>
      }
      onClose={() => {
        onClose();
      }}
    >
      <Form ref={formRef} layout="vertical" className={styles.box}>
        <div className={styles.card}>
          <Form.Item
            label="物流单号"
            rules={[{ required: true, message: '物流单号不能为空' }]}
            name="expressNo"
            className={styles.cardBorder}
          >
            <Input
              placeholder="请填写物流单号"
              bordered={false}
              onChange={() => {
                haveState();
              }}
              className={styles.input}
            />
          </Form.Item>
          <Form.Item
            label="发货联系人"
            rules={[{ required: true, message: '发货联系人不能为空' }]}
            name="expressName"
            className={styles.cardBorder}
          >
            <Input
              placeholder="请填写发货联系人"
              bordered={false}
              onChange={() => {
                haveState();
              }}
              className={styles.input}
            />
          </Form.Item>
          <Form.Item
            label="联系电话"
            rules={[{ required: true, message: '联系电话不能为空' }]}
            name="expressPhone"
            className={styles.cardBorder}
          >
            <Input
              placeholder="请填写联系电话"
              bordered={false}
              maxLength={11}
              onChange={(e) => {
                haveState();
                if (e.target.value) {
                  const value = e.target.value.match(/[0-9]/g)?.join('');
                  formRef.current.setFieldsValue({
                    expressPhone: value || '',
                  });
                }
              }}
              className={styles.input}
            />
          </Form.Item>
          <Form.Item
            label={`实际${orderType === 9 ? '验收' : '发货'}时间`}
            rules={[
              { required: true, message: `实际${orderType === 9 ? '验收' : '发货'}时间不能为空` },
            ]}
            name="logisticsCreateTime"
            className={styles.field}
          >
            <DatePicker
              placeholder={`请选择实际${orderType === 9 ? '验收' : '发货'}时间`}
              suffixIcon={<Icon name="down" size={16} />}
              bordered={false}
              disabledDate={(current) =>
                current > dayjs(new Date()).endOf('day') ||
                current < dayjs(new Date()).subtract(1, 'day')
              }
              onChange={() => {
                haveState();
              }}
            />
          </Form.Item>
        </div>
        <div className={styles.card}>
          <div>添加附件</div>
          <div className={styles.text}>(上传附件大小5MB以内，最多上传三张)</div>
          <div className={styles.upload} role="button" tabIndex={0} onClick={onUpload}>
            <Upload
              ref={uploadRef}
              listType="mixin"
              maxCount={3}
              multiple
              disabled={uploadDisabled}
              accept=".xsl, .xlsx, .png, .pdf, .docx, .jpg, .jpeg"
            />
          </div>
        </div>
      </Form>
    </Drawer>
  );
}

export default OrderUpload;
