import { PropsWithChildren, useEffect, useState, useRef } from 'react';
import { Button, Form, Select, Input, FormInstance, Modal, message } from 'antd';
import { useRequest } from 'ahooks';
import { Drawer, Icon, Upload, DatePicker, SimpleUploadInstance } from '@/components';
import {
  GoodsListResult,
  permissionMemberList,
  PermissionMemberListResult,
  stockWarehouseList,
  StockWarehouseResult,
  StockOrderDetailResult,
  OrderAddParams,
  customerTagGroupList,
} from '@/apis';
import dayjs from 'dayjs';
import classNames from 'classnames';
import { user } from '@/store';
import SelectGoodsList from '@@/stock/components/select-goods-list';
import debounce from 'lodash/debounce';
import goodsNoStock from '@@/order-rewrite/components/goods-no-stock';
import styles from './index.module.less';
import SelectBatchGoods from '../select-batch-goods';
import orderPermission from '../../utils/order-permission';
import warehouseListAllApi from '../../../../apis/psi/warehouse-list-all-api';

interface PlatformProps {
  isNeedInitWorkflowForm: boolean;
  processDefinitionId: string;
  processDefinitionKey: string;
  processDefinitionName: string;
  businessOrderNo: string;
  businessType: number;
  isNeedSecStep: boolean;
  isNeedApprovoal: number;
  description: string;
}

interface OrderAddPropps {
  visible: boolean;
  orderType: number;
  onClose: () => void;
  submit: () => void;
  id?: number;
  // eslint-disable-next-line no-unused-vars
  orderDeteil?: (id: number) => Promise<StockOrderDetailResult>;
  // eslint-disable-next-line no-shadow,no-unused-vars
  OrderCreact: (arg0: OrderAddParams) => Promise<PlatformProps>;
}

function AllotAdd({
  visible,
  submit,
  orderType,
  id,
  orderDeteil,
  OrderCreact,
  ...props
}: PropsWithChildren<OrderAddPropps>) {
  const [form] = Form.useForm();
  const params = useRef({
    type: 0,
    tagId: '',
    pageNo: 1,
    totalPages: 0,
    pageSize: 10,
    customerName: '',
  });
  const paramsWarehouse = useRef({ pageNo: 1, totalPages: 0, pageSize: 10, warehouseStatus: 0 });
  const paramsWarehouseEnter = useRef({
    pageNo: 1,
    totalPages: 0,
    pageSize: 10,
    warehouseStatus: 0,
  });
  const uploadRef = useRef(null as unknown as SimpleUploadInstance);
  const selectGoodsRef = useRef();
  const [showSelectGoods, setShowSelectGoods] = useState(false);
  const [selectGoodsList, setSelectGoodsList] = useState<GoodsListResult[]>([]);
  const [colleatues, setColleatues] = useState<PermissionMemberListResult[]>([]);
  const [warehouses, setWarehouses] = useState<StockWarehouseResult[]>([]);
  const [warehouseEnter, setWarehouseEnter] = useState<StockWarehouseResult[]>([]);
  const [warehouseNo, setWarehouseNo] = useState('');
  // const [firstWarehouseId, setFirstWarehouseId] = useState<number | null>();
  const [fileList, setFileList] = useState([]);
  const formRef = useRef(null as unknown as FormInstance);
  const [info, setInfo] = useState('');
  const [goodsInfo, setGoodsInfo] = useState('');
  const [fileListInfo, setFileListInfo] = useState('');
  const [focusValue, setFocusValue] = useState('');
  const [isDisabled, setIsDisabled] = useState(!!(id && id > 0));
  const [orderTime, setOrderTime] = useState<string>('');
  const orderNo = useRef('');

  const { run: runWarehouse, loading: loadingWarehouse } = useRequest(stockWarehouseList, {
    defaultParams: [paramsWarehouse.current],
    manual: true,
    onSuccess: (result) => {
      paramsWarehouse.current.pageNo += 1;
      paramsWarehouse.current.totalPages = result.pagination.total;
      if (id) {
        const data = [
          ...warehouses,
          ...result.list.filter((item) => item.id !== JSON.parse(info).orignWarehouseId),
        ];
        setWarehouses(data);
      } else {
        setWarehouses([...warehouses, ...result.list]);
      }
    },
  });

  const { run: runWarehouseEnter, loading: loadingWarehouseEnter } = useRequest(
    warehouseListAllApi,
    {
      defaultParams: [paramsWarehouseEnter.current],
      manual: true,
      onSuccess: (result) => {
        paramsWarehouseEnter.current.pageNo += 1;
        paramsWarehouseEnter.current.totalPages = result.pagination.total;
        if (id) {
          const data = [
            ...warehouseEnter,
            ...result.list.filter((item) => item.id !== JSON.parse(info).destinationWarehouseId),
          ];
          setWarehouseEnter(data);
        } else {
          setWarehouseEnter([...warehouseEnter, ...result.list]);
        }
      },
    }
  );

  // eslint-disable-next-line consistent-return
  const onFinish = debounce((orderStatus: 0 | 1 | 2) => {
    const formInfo = formRef.current.getFieldsValue();
    if (!orderPermission(orderType, orderStatus === 1 ? '审批' : '取消') && orderStatus !== 2)
      return message.error('暂无权限，请联系公司管理员开通');
    if (formInfo.orignWarehouseId === formInfo.destinationWarehouseId)
      return message.error('调入仓库与调出仓库不能相同');
    if (selectGoodsList.length === 0) return message.error('请选择商品!');
    if (selectGoodsList.some((item) => item.batchList.some((each) => !each.number))) {
      return message.warning('请填写商品数量');
    }
    form.validateFields().then(() => {
      const attachmentList = uploadRef.current.getFileList();
      setIsDisabled(true);
      OrderCreact({
        orderStatus,
        id,
        ...formInfo,
        orderDate: dayjs(formRef.current.getFieldsValue(['orderDate']).orderDate).valueOf(),
        allotOrderSkuParams: selectGoodsList.map((item) => ({
          skuId: item.skuId,
          // auxiliaryUnit: item.auxiliaryUnit || item.unit,
          allotStock: item.number,
          shopSkuId: item.shopSkuId || item.id,
          skuRelationParamList: item.batchList.map((each) => ({
            skuBatchNo: each.batch,
            unitName: each.auxiliaryUnit || each.unit,
            quantity: each.number,
          })),
        })),
        attachmentList: attachmentList.map((item) => ({
          fileName: item.name,
          size: item.size,
          filePath: item.url,
          fileType: item.type?.indexOf('image') !== -1 ? 0 : 1,
        })),
      })
        .then((res) => {
          const {
            isNeedInitWorkflowForm,
            processDefinitionKey,
            processDefinitionName,
            businessOrderNo,
            businessType,
          } = res;
          if (isNeedInitWorkflowForm) {
            window.open(
              `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                {
                  businessOrderNo,
                  businessType,
                }
              )}`
            );
          } else {
            message.success(`${id ? '编辑' : '添加'}成功!`);
          }
          setWarehouseNo('');
          // @ts-ignore
          selectGoodsRef.current?.initData();
          submit();
        })
        .catch((e) => {
          if (e.code === 5005012) {
            goodsNoStock({ list: JSON.parse(e.message) });
          } else {
            message.error(e.message);
          }
        })
        .finally(() => {
          setIsDisabled(false);
        });
      return false;
    });
  }, 500);

  const leaveTip = () => {
    Modal.confirm({
      title: '提示',
      icon: '',
      cancelText: '离开',
      okText: '继续编辑',
      centered: true,
      content: '您编辑的内容尚未保存，确定要离开吗？',
      onOk: () => {},
      onCancel: () => {
        setWarehouseNo('');
        paramsWarehouse.current.pageNo = 1;
        paramsWarehouseEnter.current.pageNo = 1;
        setWarehouses([]);
        setWarehouseEnter([]);
        props.onClose();
      },
    });
  };

  const onDelete = (idVal: number) => {
    Modal.confirm({
      title: '提示',
      icon: '',
      centered: true,
      width: 290,
      content: '确定删除该商品？',
      getContainer: document.querySelector('.order-create') as HTMLElement,
      onOk: () => {
        setSelectGoodsList(
          selectGoodsList.filter((goods) => (goods.id || goods.shopSkuId) !== idVal)
        );
        message.success('删除成功!');
      },
    });
  };

  const onMore = (idVal: number) => {
    selectGoodsList.forEach((item) => {
      const items = item;
      if (item.id === idVal) {
        items.isMore = !items.isMore;
      }
    });
    setSelectGoodsList(JSON.parse(JSON.stringify(selectGoodsList)));
  };

  useEffect(() => {
    if (visible) {
      permissionMemberList({ companyId: user.companyId, permCode: 'AV_001_003' }).then((res) => {
        setColleatues(res.list);
      });
      if (id && visible) {
        orderDeteil?.(id).then((res) => {
          orderNo.current = String(res.orderNo);
          if (warehouses.length === 0) {
            warehouses.push({
              warehouseName: res.orignWarehouseName || '',
              // @ts-ignore
              id: res.orignWarehouseId,
              warehouseNo: res?.orignWarehouseNo || '',
            });
          }
          if (warehouseEnter.length === 0) {
            warehouseEnter.push({
              warehouseName: res.destinationWarehouseName || '',
              warehouseNo: res.destinationWarehouseNo || '',
              id: res.destinationWarehouseId || 0,
            });
          }
          // @ts-ignore
          setWarehouseNo(res.orignWarehouseNo);
          formRef.current.setFieldsValue({
            orignWarehouseId: res.orignWarehouseId,
            destinationWarehouseId: res.destinationWarehouseId,
            orderDate: dayjs(res.orderDate),
            operatorId: res.operatorId,
            remark: res.remark,
          });
          setOrderTime(String(res.orderDate));
          setSelectGoodsList(
            // @ts-ignore
            (
              res.platformInboundSkuVOS ||
              res.platformOutboundSkuVOS ||
              res.platformAllotSkuVOS
            ).map((item) => ({
              ...item,
              name: item.skuName,
              standardList: JSON.parse(item.standardJson),
              number: item.quantity || item.allotStock,
              productMinimumSalesUnit: item.auxiliarySaleGroup,
              batchList: item.skuRelationParamList.map((each) => ({
                batch: each.skuBatchNo,
                unit: each.auxiliaryUnit || each.unitName,
                number: each.quantity,
              })),
            }))
          );
          setFileList(
            // @ts-ignore
            res.attachmentList.map((item, index) => ({
              uid: index,
              name: item.fileName,
              size: item.size,
              url: item.filePath,
              type: item.fileType === 0 ? 'image/' : '',
            }))
          );
          setInfo(JSON.stringify(formRef.current.getFieldsValue()));
          setGoodsInfo(
            JSON.stringify(
              (
                res.platformInboundSkuVOS ||
                res.platformOutboundSkuVOS ||
                res.platformAllotSkuVOS
              ).map((item) => ({
                ...item,
                name: item.skuName,
                standardList: JSON.parse(item.standardJson),
                number: item.quantity || item.allotStock,
                productMinimumSalesUnit: item.auxiliarySaleGroup,
              }))
            )
          );
          setFileListInfo(
            JSON.stringify(
              res.attachmentList.map((item, index) => ({
                uid: index,
                name: item.fileName,
                size: item.size,
                url: item.filePath,
                type: item.fileType === 0 ? 'image/' : '',
              }))
            )
          );
        });
      }
      if (!id) {
        formRef?.current?.resetFields();
        setFileList([]);
        setSelectGoodsList([]);
        form.setFieldsValue({
          orderDate: dayjs(),
        });
      }
    }
    customerTagGroupList({
      isDefault: 1,
    }).then((res) => {
      const arr = res.list[0]?.tags;
      params.current.tagId = `${
        // @ts-ignore
        arr.filter(
          (item: { name: string }) => item.name === (orderType === 9 ? '供应商' : '客户')
        )[0].id
      }`;
    });
  }, [id, orderDeteil, orderType, runWarehouse, visible]); // eslint-disable-line

  return (
    <Drawer
      className={styles.orderAdd}
      {...props}
      visible={visible}
      width={375}
      title={`${id ? '编辑' : '新建'}调拨单`}
      push={false}
      onClose={() => {
        if (!visible) {
          return;
        }
        if (!id) {
          if (
            Object.values(formRef.current.getFieldsValue()).some((item) => item !== undefined) ||
            selectGoodsList.length > 0
          ) {
            leaveTip();
          } else {
            setWarehouseNo('');
            paramsWarehouse.current.pageNo = 1;
            paramsWarehouseEnter.current.pageNo = 1;
            setWarehouses([]);
            setWarehouseEnter([]);
            props.onClose();
          }
        } else if (
          goodsInfo !== JSON.stringify(selectGoodsList) ||
          info !== JSON.stringify(formRef.current.getFieldsValue()) ||
          fileListInfo !== JSON.stringify(fileList)
        ) {
          leaveTip();
        } else {
          setWarehouseNo('');
          paramsWarehouse.current.pageNo = 1;
          paramsWarehouseEnter.current.pageNo = 1;
          setWarehouses([]);
          setWarehouseEnter([]);
          props.onClose();
        }
      }}
      extra={
        <div
          role="button"
          tabIndex={0}
          className={
            isDisabled || selectGoodsList.length === 0 ? styles.importData : styles.importDataC
          }
          onClick={() => {
            if (isDisabled || selectGoodsList.length === 0) return;
            onFinish(2);
          }}
        >
          保存
        </div>
      }
      footer={
        <div className={styles.footer}>
          <Button
            className={classNames(
              isDisabled || selectGoodsList.length === 0 ? styles.btnDisadled : styles.btn
            )}
            disabled={isDisabled || selectGoodsList.length === 0}
            onClick={() => {
              if (isDisabled || selectGoodsList.length === 0) return;
              if (!orderPermission(13, '提交')) {
                message.error('暂无权限，请联系公司管理员开通');
              } else {
                onFinish(0);
              }
            }}
          >
            提交
          </Button>
          <Button
            className={styles.btn}
            type="primary"
            disabled={isDisabled || selectGoodsList.length === 0}
            onClick={() => {
              if (!orderPermission(13, '审批')) {
                message.error('暂无权限，请联系公司管理员开通');
              } else {
                onFinish(1);
              }
            }}
          >
            审批
          </Button>
        </div>
      }
    >
      <div className="order-create">
        <Form
          ref={formRef}
          layout="vertical"
          className={styles.form}
          // validateTrigger={['onChange']}
          onFieldsChange={() => {
            const disabled =
              Object.values(
                formRef.current.getFieldsValue([
                  'orignWarehouseId',
                  'destinationWarehouseId',
                  'operatorId',
                ])
              ).every((item) => item !== undefined) &&
              (form.getFieldsValue(['orderDate']) || orderTime.length);
            setIsDisabled(!disabled);
          }}
        >
          <div className={styles.card}>
            <Form.Item
              name="operatorId"
              label="业务员"
              rules={[{ required: true, message: '请选择业务员' }]}
            >
              <Select
                allowClear
                showSearch
                optionFilterProp="label"
                suffixIcon={<Icon name="down" size={16} />}
                placeholder="请选择业务员"
                bordered={false}
                options={colleatues.map((item) => ({
                  label: item.name,
                  value: item.id,
                }))}
                getPopupContainer={(triggerNode) => triggerNode.parentElement}
              />
            </Form.Item>
            <Form.Item
              name="orderDate"
              label="单据时间"
              rules={[{ required: true, message: `请选择单据时间` }]}
            >
              <DatePicker
                placeholder="请选择单据时间"
                suffixIcon={<Icon name="down" size={16} />}
                bordered={false}
                defaultValue={dayjs()}
              />
            </Form.Item>
            <Form.Item
              name="orignWarehouseId"
              label="调出仓库"
              rules={[{ required: true, message: `请选择调出仓库` }]}
            >
              <Select
                allowClear
                showSearch
                optionFilterProp="label"
                suffixIcon={<Icon name="down" size={16} />}
                placeholder="请选择调出仓库"
                bordered={false}
                onPopupScroll={() => {
                  if (
                    loadingWarehouse ||
                    paramsWarehouse.current.pageNo > paramsWarehouse.current.totalPages
                  )
                    return;
                  runWarehouse(paramsWarehouse.current);
                }}
                options={warehouses.map((item) => ({
                  label: item.warehouseName,
                  value: item.id,
                }))}
                onFocus={() => {
                  // if (id) {
                  //   setFirstWarehouseId(formRef.current.getFieldsValue().orignWarehouseId || null);
                  // }
                  setFocusValue(warehouseNo);
                  if (paramsWarehouse.current.pageNo > 1) return;
                  runWarehouse(paramsWarehouse.current);
                }}
                onChange={(e) => {
                  if (focusValue && selectGoodsList.length) {
                    Modal.confirm({
                      title: '提示',
                      content: '更改调出仓库将清空已添加商品，确定更改吗？',
                      centered: true,
                      keyboard: false,
                      maskClosable: false,
                      onOk: () => {
                        setWarehouseNo(warehouses.filter((item) => item.id === e)[0].warehouseNo);
                        setSelectGoodsList([]);
                      },
                      onCancel: () => {
                        setWarehouseNo(focusValue);
                        formRef.current.setFieldsValue({
                          orignWarehouseId: warehouses.filter(
                            (item) => item.warehouseNo === focusValue
                          )[0].id,
                        });
                      },
                    });
                  } else {
                    setWarehouseNo(warehouses.filter((item) => item.id === e)[0].warehouseNo);
                    setSelectGoodsList([]);
                  }
                }}
                onClear={() => {
                  if (focusValue) {
                    Modal.confirm({
                      title: '提示',
                      content: '更改调出仓库将清空已添加商品，确定更改吗？',
                      centered: true,
                      keyboard: false,
                      maskClosable: false,
                      onOk: () => {
                        setWarehouseNo('');
                        setSelectGoodsList([]);
                      },
                      onCancel: () => {
                        setWarehouseNo(focusValue);
                        formRef.current.setFieldsValue({
                          orignWarehouseId: warehouses.filter(
                            (item) => item.warehouseNo === focusValue
                          )[0].id,
                        });
                      },
                    });
                  } else {
                    setWarehouseNo('');
                    setSelectGoodsList([]);
                  }
                }}
              />
            </Form.Item>
            <div className={styles.antLabel}>
              <Form.Item
                name="destinationWarehouseId"
                label="调入仓库"
                rules={[{ required: true, message: `请选择调入仓库` }]}
              >
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  suffixIcon={<Icon name="down" size={16} />}
                  placeholder="请选择调入仓库"
                  bordered={false}
                  onPopupScroll={() => {
                    if (
                      loadingWarehouseEnter ||
                      paramsWarehouseEnter.current.pageNo > paramsWarehouseEnter.current.totalPages
                    )
                      return;
                    runWarehouseEnter(paramsWarehouseEnter.current);
                  }}
                  options={warehouseEnter.map((item) => ({
                    label: item.warehouseName,
                    value: item.id,
                  }))}
                  onFocus={() => {
                    if (paramsWarehouseEnter.current.pageNo > 1) return;
                    runWarehouseEnter(paramsWarehouseEnter.current);
                  }}
                />
              </Form.Item>
            </div>
          </div>
          <div className={styles.card}>
            <span
              role="button"
              tabIndex={0}
              className={classNames(!warehouseNo ? styles.addGoodsDisable : styles.addGoods)}
              onClick={() => {
                if (!warehouseNo) {
                  message.error('请先选择仓库');
                } else {
                  setShowSelectGoods(true);
                }
              }}
            >
              <Icon name="plus" className={styles.icon} />
            </span>
            添加商品
            {!warehouseNo && <span className={styles.fontTip}>（请先选择仓库）</span>}
          </div>
          {selectGoodsList.length > 0 && (
            <SelectGoodsList
              warehouseNo={warehouseNo}
              isBatch
              orderType={orderType}
              selectGoodsList={selectGoodsList}
              onDelete={onDelete}
              onMore={onMore}
              onAddGoods={(arr) => {
                setShowSelectGoods(true);
                setSelectGoodsList(arr);
              }}
              onConfirm={(e) => {
                setSelectGoodsList(e);
              }}
              isEdit={Boolean(id)}
              orderNo={orderNo.current}
            />
          )}
          <div className={styles.card}>
            <div className={classNames(styles.antForm, styles.fromTextarea)}>
              <Form.Item label="备注" name="remark">
                <Input.TextArea
                  className={styles.textarea}
                  placeholder="请填写备注"
                  maxLength={255}
                  autoSize={{ minRows: 3, maxRows: 5 }}
                />
              </Form.Item>
            </div>
            <div className={styles.footerTitle}>添加附件</div>
            <div className={styles.prompt}>(附件最多5个 图片最多6张)</div>
            <div className={styles.antLabel}>
              <Form.Item name="attachment">
                <Upload
                  fileList={fileList}
                  listType="mixin"
                  ref={uploadRef}
                  multiple
                  maxCount={11}
                  beforeUpload={(e, fileUploadList) => {
                    let imageCount = 0;
                    let fileCount = 0;
                    fileUploadList.forEach((file) => {
                      if (file.type && file.type.indexOf('image/') === 0) {
                        imageCount += 1;
                      } else {
                        fileCount += 1;
                      }
                    });
                    if (imageCount > 6 || fileCount > 5) {
                      message.error('附件最多5个 图片最多6张');
                    }
                    return imageCount <= 6 && fileCount <= 5;
                  }}
                />
              </Form.Item>
            </div>
          </div>
        </Form>
      </div>
      <SelectBatchGoods
        visible={showSelectGoods}
        orderType={orderType}
        warehouseNo={warehouseNo}
        // @ts-ignore
        ref={selectGoodsRef}
        onClose={() => {
          setShowSelectGoods(!showSelectGoods);
        }}
        // @ts-ignore
        selectGoodsList={selectGoodsList}
        // @ts-ignore
        confirm={(e: GoodsListResult[]) => {
          setShowSelectGoods(false);
          setSelectGoodsList(e);
        }}
        orderNo={orderNo.current || ''}
        isEdit={Boolean(id)}
      />
    </Drawer>
  );
}

AllotAdd.defaultProps = {
  id: 0,
  orderDeteil: null,
};

export default AllotAdd;
