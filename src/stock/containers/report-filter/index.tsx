import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { DatePicker, Drawer } from '@/components';
import { Form, FormInstance } from 'antd';
import classNames from 'classnames';
import styles from './index.module.less';

interface FilterParams {
  startDate?: number;
  endDate?: number;
}

interface FilterPropps {
  visible: boolean;
  onClose: () => void;
  startDate?: number;
  endDate?: number;
  // eslint-disable-next-line no-unused-vars
  confirm: (arg: FilterParams) => void;
}

function ReportFilter({
  visible,
  confirm,
  startDate,
  endDate,
  onClose,
  ...props
}: PropsWithChildren<FilterPropps>) {
  const formRef = useRef(null as unknown as FormInstance);
  const [filterParams, setFilterParams] = useState<FilterParams>({
    startDate: 0,
    endDate: 0,
  });

  const disabledDateStart = (current: Dayjs) => {
    const endDateFoo = formRef.current.getFieldsValue().endDate;
    return (
      current &&
      (current > dayjs(endDateFoo).endOf('day') ||
        current < dayjs(dayjs(endDateFoo).subtract(1, 'year')).add(1, 'days'))
    );
  };

  const disabledDateEnd = (current: Dayjs) => {
    const startDateFoo = formRef.current.getFieldsValue().startDate;
    return (
      current &&
      (current > dayjs(startDateFoo).add(1, 'year') ||
        current < dayjs(startDateFoo).subtract(0, 'day'))
    );
  };

  useEffect(() => {
    if (visible) {
      const data = {
        startDate: startDate || dayjs().subtract(1, 'year').valueOf(),
        endDate: endDate || dayjs().valueOf(),
      };
      setFilterParams(data);
      setTimeout(() => {
        formRef.current.setFieldsValue({
          startDate: dayjs(data.startDate),
          endDate: dayjs(data.endDate),
        });
      });
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer title="筛选" visible={visible} {...props} className={styles.filter} onClose={onClose}>
      <div className={styles.card}>
        <div className={styles.title}>操作时间</div>
        <div className={styles.time}>
          <Form ref={formRef}>
            <Form.Item name="startDate">
              <DatePicker
                onChange={(e) => {
                  setFilterParams({
                    ...filterParams,
                    startDate: dayjs(e).startOf('day').valueOf() || 0,
                  });
                }}
                allowClear={false}
                disabledDate={disabledDateStart}
                placeholder="请选择开始时间"
                bordered={false}
              />
            </Form.Item>

            <span className={styles.line} />
            <Form.Item name="endDate">
              <DatePicker
                onChange={(e) => {
                  setFilterParams({
                    ...filterParams,
                    endDate: dayjs(e).startOf('day').valueOf() + 86399999 || 0,
                  });
                }}
                disabledDate={disabledDateEnd}
                placeholder="请选择结束时间"
                allowClear={false}
                bordered={false}
              />
            </Form.Item>
          </Form>
        </div>
      </div>

      <div className={styles.footer}>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.footerBtn, styles.footerBtnAdd)}
          onClick={() => {
            confirm(filterParams);
          }}
        >
          确定
        </div>
      </div>
    </Drawer>
  );
}

ReportFilter.defaultProps = {
  startDate: dayjs().subtract(1, 'year').valueOf(),
  endDate: dayjs().valueOf(),
};

export default ReportFilter;
