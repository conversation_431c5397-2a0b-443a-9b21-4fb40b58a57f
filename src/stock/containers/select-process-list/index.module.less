@import 'styles/mixins/mixins';

.add {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.tip {
  color: #888b98;
  margin-bottom: 16px;
}

.card {
  display: flex;
  margin-bottom: 20px;
  padding: 16px;
  justify-content: space-between;
  align-items: center;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
  cursor: pointer;
}

.cardActive {
  background: rgb(217 238 255 / 30%);
}

.cardName {
  display: flex;
  align-items: center;
}

.img {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  border-radius: 50%;
}

.name {
  display: inline-block;
  width: 180px;
  .text-overflow();
}

.selectBtn {
  color: #008cff;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.line {
  margin: 0 8px 3px;
}

.noData {
  display: flex;
  height: 100%;
  margin-top: -100px;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
