import { useEffect, useState } from 'react';
import { Drawer, Empty } from '@/components';
import classNames from 'classnames';
import { appListResult, getWorkflowQueryApprovalList } from '@/apis';
import { drawerPopup } from '@/utils/popup';
import { Button, DrawerProps } from 'antd';
import styles from './index.module.less';
import stockCfgUpdata from '../../../../apis/psi/stock-cfg-updata';

interface SelectProcessProps extends DrawerProps {
  formType: number;
  selectId: string;
  stockTypeId: number;
  onClose?: () => void;
  onConfirm: () => void;
  onCreate: () => void;
  // eslint-disable-next-line no-unused-vars
  onPreview: (pId: string, name: string) => void;
}

function SelectProcessList({
  formType,
  stockTypeId,
  selectId,
  visible,
  onClose,
  onConfirm,
  onCreate,
  onPreview,
  ...props
}: SelectProcessProps) {
  const [list, setList] = useState<appListResult[]>([]);
  const [id, setId] = useState('');

  const onOk = () => {
    const info = list.filter((item) => item.processDefinitionId === id)[0];
    stockCfgUpdata({
      id: stockTypeId,
      procFrmName: info.name,
      procDefId: info.processDefinitionId,
      procDefKey: info.processDefinitionKey,
    }).then(() => {
      onClose?.();
      onConfirm();
    });
  };

  useEffect(() => {
    if (visible) {
      getWorkflowQueryApprovalList({ formType }).then((res) => {
        setId(selectId || '');
        setList(res.list);
      });
    }
  }, [formType, selectId, visible]);

  return (
    <Drawer
      title="选择审批流程"
      visible={visible}
      onClose={onClose}
      {...props}
      extra={
        <div role="button" tabIndex={0} className={styles.add} onClick={onCreate}>
          新建
        </div>
      }
      footer={
        list.length ? (
          <Drawer.Footer okText="确定" showCancel={false} disabled={!id} onOk={onOk} />
        ) : null
      }
    >
      {list.length > 0 && <div className={styles.tip}>只展示包含申请套件的审批流程</div>}
      {list.length > 0 ? (
        list.map((item) => (
          <div
            className={classNames(styles.card, {
              [styles.cardActive]: id === item.processDefinitionId,
            })}
            role="button"
            tabIndex={item.id}
            key={item.id}
            onClick={() => {
              setId(item.processDefinitionId);
            }}
          >
            <div className={styles.cardName}>
              <img className={styles.img} src={item.icon} alt="" />
              <div className={styles.name}>{item.name}</div>
            </div>
            <div className={styles.selectBtn}>
              <span
                role="button"
                tabIndex={item.id}
                onClick={() => onPreview(item.processDefinitionId, item.name)}
              >
                预览
              </span>
              <span className={styles.line}>|</span>
              <span>{id === item.processDefinitionId ? '已选' : '选择'}</span>
            </div>
          </div>
        ))
      ) : (
        <div className={styles.noData}>
          <Empty
            message="暂无审批流程"
            description={
              <div>
                <div>当前您暂未有相关的审批流程</div>
                <div>如需审批，请点击“创建审批”</div>
              </div>
            }
          />
          <Button type="primary" size="small" className="mt-4" onClick={onCreate}>
            创建审批
          </Button>
        </div>
      )}
    </Drawer>
  );
}

SelectProcessList.defaultProps = {
  onClose: () => {},
};

export const selectProcessList = (props: SelectProcessProps) => {
  drawerPopup(SelectProcessList, props);
};

export default selectProcessList;
