import { useEffect, useMemo, useRef, useState } from 'react';
import { Modal } from 'antd';
import { getToken } from '@/utils/auth';
import styles from './index.module.less';

export interface PrintProps {
  visible: boolean;
  id?: number;
  orderNo?: number;
  templateType: number;
  onCancel?: () => void;
}

function Print({ visible, id, templateType, orderNo, onCancel }: PrintProps) {
  const iframeEl = useRef(null as unknown as HTMLIFrameElement);
  const [modalShow, setModalShow] = useState(false);

  const src = useMemo(() => {
    let urlParams = `templateType=${templateType}`;
    if (id) urlParams += `&id=${id}`;
    if (orderNo) urlParams += `&orderNo=${orderNo}`;
    if (import.meta.env.BIZ_ADMIN_APP_DOMAIN.includes(window.location.protocol)) {
      return `${import.meta.env.BIZ_ADMIN_APP_DOMAIN}print?${urlParams}`;
    }
    return `${window.location.protocol || 'https:'}${
      import.meta.env.BIZ_ADMIN_APP_DOMAIN
    }print?${urlParams}`;
  }, [id, orderNo, templateType]);

  const onClose = () => {
    if (onCancel) onCancel();
    setModalShow(false);
  };

  useEffect(() => {
    setModalShow(visible);
  }, [visible]);

  return (
    <Modal
      title="模板设置"
      visible={modalShow}
      width={1280}
      onCancel={onClose}
      className={styles.modal}
    >
      <iframe
        title="打印"
        src={src}
        className={styles.iframe}
        name="iframe"
        ref={iframeEl}
        onLoad={() => {
          iframeEl.current?.contentWindow?.postMessage(getToken(), `${src}`);
        }}
      />
    </Modal>
  );
}

Print.defaultProps = {
  onCancel: () => null,
  id: 0,
  orderNo: 0,
};

export default Print;
