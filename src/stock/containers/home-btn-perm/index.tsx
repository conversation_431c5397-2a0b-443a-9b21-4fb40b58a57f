import { message, Modal } from 'antd';
import { PropsWithChildren, useMemo } from 'react';
import orderPermission from '../../utils/order-permission';
import styles from './index.module.less';

interface Props {
  status: number;
  orderType: number;
  orderTypeId?: number;
  isCanEdit?: boolean;
  ifCanDelete?: boolean;
  // eslint-disable-next-line no-unused-vars
  btnChange: (name: string) => void;
}

function HomeBtnPerm({
  status,
  orderType,
  orderTypeId,
  isCanEdit,
  btnChange,
  ifCanDelete,
}: PropsWithChildren<Props>) {
  // 是否有编辑按钮
  const editShow = useMemo(() => {
    if (orderTypeId === 11 || orderTypeId === 12) {
      return false;
    }
    return true;
  }, [orderTypeId]);

  // 是否有提交按钮
  const submitShow = useMemo(() => {
    if (status === 0) {
      return false;
    }
    return true;
  }, [status]);

  // 是否有(审批 取消)按钮
  //   const approvedShow = useMemo(() => {
  //     if (
  //       orderTypeId === 3 ||
  //       orderTypeId === 4 ||
  //       orderTypeId === 9 ||
  //       orderTypeId === 10 ||
  //       orderTypeId === 11 ||
  //       orderTypeId === 12
  //     ) {
  //       return false;
  //     }
  //     return true;
  //   }, [orderTypeId]);

  // 是否有删除按钮
  const deleteShow = useMemo(() => {
    if (
      orderTypeId === 3 ||
      orderTypeId === 4 ||
      orderTypeId === 9 ||
      orderTypeId === 10 ||
      orderTypeId === 11 ||
      orderTypeId === 12
    ) {
      return false;
    }
    return true;
  }, [orderTypeId]);

  const checkPerm = (name: string) => {
    if (!orderPermission(orderType, name)) {
      message.error('暂无权限，请联系公司管理员开通');
    } else if (name === '编辑') {
      btnChange(name);
    } else {
      Modal.confirm({
        title: '提示',
        icon: '',
        okText: '确认',
        cancelText: '取消',
        centered: true,
        content: `是否${name}`,
        onOk: () => {
          btnChange(name);
        },
      });
    }
  };

  return (
    <>
      {status !== 1 && submitShow ? (
        <div
          role="button"
          tabIndex={0}
          className={styles.batchItem}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            checkPerm('提交');
          }}
        >
          提交
        </div>
      ) : null}
      {status !== 1 && editShow && isCanEdit ? (
        <div
          role="button"
          tabIndex={0}
          className={styles.batchItem}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            checkPerm('编辑');
          }}
        >
          编辑
        </div>
      ) : null}
      {[9, 10, 13].includes(orderType) && status !== 1 && (
        <div
          role="button"
          tabIndex={0}
          className={styles.batchItem}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            checkPerm('审批');
          }}
        >
          审批
        </div>
      )}
      {![9, 10, 13].includes(orderType) && (
        <div
          role="button"
          tabIndex={0}
          className={styles.batchItem}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            checkPerm(status === 1 ? '取消审批' : '审批');
          }}
        >
          {status === 1 ? '取消审批' : '审批'}
        </div>
      )}
      {status !== 1 && deleteShow && !ifCanDelete && (
        <div
          role="button"
          tabIndex={0}
          className={styles.batchItem}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            checkPerm('删除');
          }}
        >
          删除
        </div>
      )}
      {ifCanDelete && (
        <div
          role="button"
          tabIndex={0}
          className={styles.batchItem}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
            checkPerm('删除');
          }}
        >
          删除
        </div>
      )}
    </>
  );
}

HomeBtnPerm.defaultProps = {
  orderTypeId: 0,
  isCanEdit: true,
  ifCanDelete: false,
};

export default HomeBtnPerm;
