import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { DatePicker, Drawer, Icon } from '@/components';
import { Checkbox, Form, FormInstance } from 'antd';
import classNames from 'classnames';
import { postPlatWareHouse, WareListRusult, PlatReportAllotParams } from '@/apis';
import styles from './index.module.less';
import Warehouse from '../filter-warehouse';

interface FilterPropps {
  visible: boolean;
  onClose: () => void;
  data: PlatReportAllotParams;
  // eslint-disable-next-line no-unused-vars
  confirm: (arg: PlatReportAllotParams) => void;
}

const orderList: { businessType: number; name: string }[] = [
  // {
  //   businessType: 1,
  //   name: '日',
  // },
  // {
  //   businessType: 2,
  //   name: '月',
  // },
  // {
  //   businessType: 3,
  //   name: '年',
  // },
];
const orderListFoo = [
  {
    businessType: 4,
    name: '仓库',
  },
  {
    businessType: 5,
    name: '商品',
  },
];

function StatementReportFilter({
  visible,
  confirm,
  data,
  onClose,
  ...props
}: PropsWithChildren<FilterPropps>) {
  const formRef = useRef(null as unknown as FormInstance);
  const [warehouses, setWarehouse] = useState<WareListRusult[]>([]);
  const [collectMode, setCollectMode] = useState<number[]>([]);
  const [intWarehouse, setInWarehouse] = useState<number[]>([0]);
  const [count, setCount] = useState(0);
  const [checked, setChecked] = useState(false);
  const [showWarehouse, setShowWarehouse] = useState(false);
  const [filterParams, setFilterParams] = useState<PlatReportAllotParams>({
    startDate: '',
    endDate: '',
    collectType: 1,
    collectMode: 2,
    warehouseIdList: [],
    isShowAmount: 0,
  });

  const disabledDateStart = (current: Dayjs) => {
    const endDateFoo = formRef.current.getFieldsValue().endDate;
    return (
      current &&
      (current > dayjs(endDateFoo).endOf('day') ||
        current < dayjs(dayjs(endDateFoo).subtract(1, 'year')))
    );
  };

  const disabledDateEnd = (current: Dayjs) => {
    const startDateFoo = formRef.current.getFieldsValue().startDate;
    return (
      current &&
      (current > dayjs(startDateFoo).add(1, 'year') ||
        current < dayjs(startDateFoo).subtract(0, 'day'))
    );
  };

  const selectWarehouses = (id: number) => {
    const list = [...intWarehouse];
    warehouses.forEach((item) => {
      if (item.id === id) {
        if (list.indexOf(id) > -1) {
          list.splice(list.indexOf(id), 1);
        } else {
          list.push(item.id);
        }
      }
    });

    setFilterParams({
      ...filterParams,
      warehouseIdList: id ? list.filter((item) => item) : [],
    });
    setInWarehouse(id ? list.filter((item) => item) : [0]);
  };

  const getWareList = () => {
    postPlatWareHouse({
      pageNo: 1,
      pageSize: 4,
    }).then((res) => {
      setWarehouse([{ id: 0, warehouseName: '全部仓库', warehouseStatus: 0 }, ...res.list]);
      setCount(res.pagination.count);
      if (data.startDate) {
        formRef.current.setFieldsValue({
          startDate: dayjs(Number(data.startDate)),
        });
      }
      if (data.endDate) {
        formRef.current.setFieldsValue({
          endDate: dayjs(Number(data.endDate)),
        });
      }
    });
  };

  useEffect(() => {
    if (visible) {
      const dataFoo = {
        ...data,
        startDate: data?.startDate || String(dayjs().subtract(1, 'month').valueOf()),
        endDate: data?.endDate || String(dayjs().valueOf()),
        collectType: data?.collectType || 1,
        collectMode: data?.collectMode || 2,
        warehouseIdList: data?.warehouseIdList || [],
        isShowAmount: data?.isShowAmount || 0,
      };
      setChecked(!!data?.isShowAmount);
      // eslint-disable-next-line no-nested-ternary
      const numbers = data?.collectMode === 3 ? [4, 5] : data?.collectMode === 2 ? [5] : [4];
      setCollectMode(data?.collectMode ? numbers : []);
      setFilterParams(dataFoo);
      getWareList();
      setTimeout(() => {
        formRef.current.setFieldsValue({
          startDate: dayjs(Number(data.startDate)),
          endDate: dayjs(Number(data.endDate)),
        });
      });
    }
  }, [visible, data]); // eslint-disable-line

  return (
    <Drawer
      title="筛选"
      visible={visible}
      {...props}
      className={styles.filter}
      onClose={() => {
        // eslint-disable-next-line no-nested-ternary
        const numbers = collectMode.length === 2 ? 3 : collectMode.includes(5) ? 2 : 1;
        const res = {
          ...filterParams,
          collectMode: collectMode.length ? numbers : null,
        };
        confirm(res);
      }}
    >
      <div className={styles.card}>
        <div className={styles.title}>仓库名称</div>
        <div className={styles.list}>
          {warehouses.map((item, index) => (
            <span
              className={intWarehouse.includes(item.id as number) ? styles.itemActive : styles.item}
              role="button"
              key={item.id}
              tabIndex={index}
              onClick={() => {
                selectWarehouses(item.id as number);
              }}
            >
              <span className={styles.name}>{item.warehouseName}</span>
            </span>
          ))}
          {count > 4 ? (
            <span
              role="button"
              tabIndex={0}
              className={styles.itemActive}
              onClick={() => {
                setShowWarehouse(true);
              }}
            >
              更多仓库
              <Icon name="right" />
            </span>
          ) : null}
        </div>
      </div>

      <div className={styles.card}>
        <div className={styles.title}>查询时间段</div>
        <div className={styles.time}>
          <Form ref={formRef}>
            <Form.Item name="startDate">
              <DatePicker
                onChange={(e) => {
                  setFilterParams({
                    ...filterParams,
                    startDate: String(dayjs(e).startOf('day').valueOf()) || '',
                  });
                }}
                allowClear={false}
                disabledDate={disabledDateStart}
                placeholder="请选择开始时间"
                bordered={false}
              />
            </Form.Item>

            <span className={styles.line} />
            <Form.Item name="endDate">
              <DatePicker
                onChange={(e) => {
                  setFilterParams({
                    ...filterParams,
                    endDate: String(dayjs(e).startOf('day').valueOf() + 86399999) || '',
                  });
                }}
                disabledDate={disabledDateEnd}
                placeholder="请选择结束时间"
                allowClear={false}
                bordered={false}
              />
            </Form.Item>
          </Form>
        </div>
      </div>

      <div className={styles.card}>
        <div className={styles.title}>汇总方式</div>
        <div className={styles.list}>
          {orderList.map((item) => (
            <span
              role="button"
              tabIndex={0}
              key={item.businessType}
              onClick={() => {
                setFilterParams({
                  ...filterParams,
                  collectType: item.businessType,
                });
              }}
              className={
                filterParams.collectType === item.businessType ? styles.itemActive : styles.item
              }
            >
              {item.name}
            </span>
          ))}
          {orderListFoo.map((item) => (
            <span
              role="button"
              tabIndex={0}
              key={item.businessType}
              onClick={() => {
                let arr = collectMode as number[];
                if (arr.length === 1 && arr.includes(item.businessType)) {
                  arr = [item.businessType];
                } else if (arr.length === 1 && !arr.includes(item.businessType)) {
                  arr = [...arr, item.businessType];
                } else if (arr.length === 2) {
                  arr = [item.businessType === 4 ? 5 : 4];
                }
                setCollectMode(arr);
              }}
              className={collectMode.includes(item.businessType) ? styles.itemActive : styles.item}
            >
              {item.name}
            </span>
          ))}
        </div>
      </div>

      <div>
        <Checkbox
          checked={checked}
          onChange={(e) => {
            setChecked(e?.target?.checked);
            setFilterParams({
              ...filterParams,
              isShowAmount: e?.target?.checked ? 1 : 0,
            });
          }}
        />
        <span className={styles.revealPrice}>显示金额</span>
      </div>

      <div className={styles.footer}>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.footerBtn, styles.footerBtnEdit)}
          onClick={() => {
            const dataFoo = {
              startDate: String(dayjs().subtract(1, 'month').valueOf()),
              endDate: String(dayjs().valueOf()),
              collectType: 1,
              collectMode: 2,
              warehouseIdList: [],
              isShowAmount: 0,
            };
            setChecked(false);
            setCollectMode([5]);
            setFilterParams(dataFoo);
            setInWarehouse([0]);
            setTimeout(() => {
              formRef.current.setFieldsValue({
                startDate: dayjs(dayjs().subtract(1, 'month').valueOf()),
                endDate: dayjs(dayjs().valueOf()),
              });
            });
          }}
        >
          重置
        </div>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.footerBtn, styles.footerBtnAdd)}
          onClick={() => {
            // eslint-disable-next-line no-nested-ternary
            const numbers = collectMode.length === 2 ? 3 : collectMode.includes(5) ? 2 : 1;
            const res = {
              ...filterParams,
              collectMode: collectMode.length ? numbers : null,
            };
            confirm(res);
          }}
        >
          确定
        </div>
      </div>

      <Warehouse
        confirm={(e) => {
          setShowWarehouse(false);
          // @ts-ignore
          setInWarehouse(e);
          setFilterParams({
            ...filterParams,
            warehouseIdList: [...new Set((filterParams.warehouseIdList || []).concat(e))],
          });
        }}
        visible={showWarehouse}
        selectWarehouse={intWarehouse}
        onCloseWarehouse={() => {
          setShowWarehouse(false);
        }}
      />
    </Drawer>
  );
}

export default StatementReportFilter;
