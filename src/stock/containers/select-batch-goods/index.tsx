import {
  PropsWithChildren,
  useEffect,
  useRef,
  useState,
  useCallback,
  useReducer,
  forwardRef,
  useImperativeHandle,
} from 'react';
import { Search, Drawer, Icon, Empty } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Button, Spin, Checkbox, message, Tooltip } from 'antd';
import {
  getCategoryCustomeList,
  GetCategoryCustomeItemResult,
  stockGoodsList,
  StockGoodsListResult,
} from '@/apis';
import classNames from 'classnames';
import { selectGoodsFilter } from '@/src/order/containers/select-goods-filter';
import debounce from 'lodash/debounce';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import PopupModal from '../../components/modal/index';
// import cart from '../../assets/imgs/cart.png';
import GoodsItem from '../../components/goods-item';
import SetupGoodsInfo from '../../components/select-goods-list/setup-goods-info';
import styles from './index.module.less';

const pageSize = 10;

interface SelectGoodsPropps {
  visible: boolean;
  orderType: number;
  // eslint-disable-next-line no-unused-vars
  confirm: (arr: StockGoodsListResult[]) => void | Promise<void>;
  onClose: () => void;
  selectGoodsList: StockGoodsListResult[];
  warehouseNo: string;
  orderNo?: string;
  isEdit: boolean;
}

interface SelectGoodsRefs {
  initData: () => void;
}

interface ArrProps {
  name: string;
  value: string[];
}

interface InitialState {
  customizeCategorySet: Array<number | null>;
  shopSkuNames: string[];
  standardFilterParams: ArrProps[];
  keyword: string;
  pageNo: number;
  totalPage: number;
  list: StockGoodsListResult[];
  warehouseNo: string;
  isQuerySelfSku: number;
  isInboundType: number;
}

const initialState = {
  customizeCategorySet: [],
  shopSkuNames: [],
  standardFilterParams: [],
  keyword: '',
  pageNo: 1,
  totalPage: 1,
  list: [],
  isQuerySelfSku: 0,
  isInboundType: 0,
  warehouseNo: '',
};

type GoodsAction =
  | {
      type: 'setPage';
      payload: { pageNo?: number; totalPage?: number };
    }
  | {
      type: 'setList';
      payload: StockGoodsListResult[];
    }
  | {
      type: 'setAll';
      payload: InitialState;
    };

const reducer = (state: InitialState, action: GoodsAction): InitialState => {
  switch (action.type) {
    case 'setPage':
      return { ...state, ...action.payload };
    case 'setList':
      return { ...state, list: action.payload };
    case 'setAll':
      return action.payload;
    default:
      return state;
  }
};

const SelectBatchGoods = forwardRef<SelectGoodsRefs, PropsWithChildren<SelectGoodsPropps>>(
  (
    {
      visible,
      selectGoodsList,
      confirm,
      onClose,
      orderType,
      warehouseNo,
      orderNo,
      isEdit,
      ...props
    },
    ref
  ) => {
    const [categoryCustomes, setCategoryCustomes] = useState<GetCategoryCustomeItemResult[]>([]);
    const [categoryCustomesTwo, setCategoryCustomesTwo] = useState(
      null as null | GetCategoryCustomeItemResult[]
    );
    const content = useRef(null as unknown as HTMLDivElement);
    const [selectIds, setSelectIds] = useState<Array<number | string | boolean>>([]);
    const [selectCategoryId, setSelectCategoryId] = useState<number | null>(null);
    const [selectCategoryIdTwo, setSelectCategoryIdTwo] = useState<number | null>(null);
    // const [units, setUnits] = useState<FormulaConvertManyconvertResult[]>([]);
    const [state, dispatch] = useReducer(reducer, initialState);
    const [showMore, setShowMore] = useState(false);
    const [Modal, setModal] = useState(false);
    const [allChecked, setAllChecked] = useState(false);
    const hasSelectGoodsList = useRef<StockGoodsListResult[]>([]);
    const parentIdRef = useRef(0);

    const paramsCustom = useRef({ grade: 1, pageNo: 1, pageSize: 20, totalPages: 0 });

    const [showSelectGoodsList, setShowSelectGoodsList] = useState(false);

    const getCategoryCustomes = () => {
      getCategoryCustomeList({ ...paramsCustom.current }).then((res) => {
        if (paramsCustom.current.pageNo === 1) {
          res.list.unshift({
            id: null,
            categoryName: '全部',
          });
          setCategoryCustomes(res.list);
        } else {
          setCategoryCustomes([...categoryCustomes, ...res.list]);
        }
        paramsCustom.current.totalPages = Math.ceil(
          res.pagination.count / paramsCustom.current.pageSize
        );
      });
    };

    const loadMoreCategory = () => {
      paramsCustom.current.pageNo += 1;
      if (!(paramsCustom.current.pageNo <= paramsCustom.current.totalPages)) return;
      getCategoryCustomes();
    };

    const selectCategory = (id: number | null) => {
      if (!id) {
        setSelectCategoryId(null);
        setCategoryCustomesTwo([]);
        return;
      }
      parentIdRef.current = id;
      setShowMore(false);
      setSelectCategoryId(id);
      getCategoryCustomeList({ pageNo: 1, pageSize: 100, parentId: id || null }).then((res) => {
        setCategoryCustomesTwo(res.list);
        setSelectCategoryIdTwo(null);
      });
    };

    const getGoods = useCallback(
      (argument: InitialState) =>
        stockGoodsList({
          sort: 'desc',
          pageNo: argument.pageNo,
          pageSize,
          isQuerySelfSku: orderType === 9 ? 0 : 1,
          isInventoryType: orderType === 11 ? 1 : 0,
          isWarehouseFilter: orderType === 9 ? 0 : 1,
          warehouseNo,
          keyword: argument.keyword,
          customizeCategorySet: argument.customizeCategorySet,
          businessSourceType: 0,
          standardFilterParams: argument.standardFilterParams,
          shopSkuNames: argument.shopSkuNames,
        }).then((res: { list: StockGoodsListResult[]; pagination: { count: number } }) => {
          if (hasSelectGoodsList.current.length) {
            for (let i = 0; i < res.list.length; i += 1) {
              hasSelectGoodsList.current.forEach((item1) => {
                if (res.list[i].skuId === item1.skuId) {
                  res.list[i] = item1;
                }
              });
            }
          }
          dispatch({
            type: 'setAll',
            payload: {
              customizeCategorySet: argument.customizeCategorySet,
              standardFilterParams: argument.standardFilterParams,
              shopSkuNames: argument.shopSkuNames,
              keyword: argument.keyword,
              isQuerySelfSku: argument.isQuerySelfSku,
              isInboundType: argument.isInboundType,
              warehouseNo: argument.warehouseNo,
              pageNo: argument.pageNo + 1,
              list: [...argument.list, ...res.list],
              totalPage: Math.ceil(res.pagination.count / pageSize),
            },
          });
        }),
      [orderType, warehouseNo]
    );

    const loadMore = () => {
      if (!(state.pageNo <= state.totalPage)) return;
      setAllChecked(false);
      getGoods(state);
    };

    const initData = () => {
      dispatch({
        type: 'setList',
        payload: [],
      });
    };

    // 关闭抽屉
    const onCloseDrawer = () => {
      let flag = false;
      state.list.forEach((tmpItem) => {
        for (let i = 0; i < selectGoodsList.length; i += 1) {
          if (tmpItem.skuId === selectGoodsList[i].skuId) {
            if (
              tmpItem.unit !== (selectGoodsList[i].auxiliaryUnit || selectGoodsList[i].unit) ||
              tmpItem.number !== selectGoodsList[i].number
            ) {
              flag = true;
            }
          }
        }
      });
      if (flag || (selectGoodsList.length === 0 && selectIds.length > 0)) {
        setModal(true);
      } else {
        dispatch({
          type: 'setList',
          payload: [],
        });
        onClose();
        initData();
      }
    };

    const onChangeGoods = (e: any, skuId: number, val: StockGoodsListResult) => {
      const arr = JSON.parse(JSON.stringify(selectIds));
      if (e?.target.checked) {
        arr.push(skuId);
        hasSelectGoodsList.current.push(val);
      } else {
        for (let i = 0; i < arr.length; i += 1) {
          if (skuId === arr[i]) {
            arr.splice(i, 1);
          }
        }
        hasSelectGoodsList.current = hasSelectGoodsList.current.filter(
          (item) => item.skuId !== skuId
        );
        if (allChecked) setAllChecked(false);
      }
      setSelectIds(arr);
    };

    // 统计数量
    const computeNum = () => {
      const hasIds = hasSelectGoodsList.current.map((each) => each.skuId);
      state.list.forEach((each) => {
        if (selectIds.includes(each.skuId)) {
          if (!hasIds.includes(each.skuId)) {
            hasSelectGoodsList.current.push(each);
          } else {
            for (let i = 0; i < hasSelectGoodsList.current.length; i += 1) {
              if (hasSelectGoodsList.current[i].skuId === each.skuId) {
                hasSelectGoodsList.current[i].number = each.number;
                hasSelectGoodsList.current[i].num = each.num;
                hasSelectGoodsList.current[i].preferential = each.preferential;
              }
            }
          }
        }
      });
    };

    const allCheckedChange = (e: CheckboxChangeEvent) => {
      const checked = e?.target?.checked;
      setAllChecked(checked);
      let listGoods = hasSelectGoodsList.current;
      const stateLists = state.list;
      if (checked) {
        stateLists.forEach((f) => {
          const ids = listGoods.map((m) => m.skuId);
          if (!ids.includes(f.skuId)) {
            listGoods.push(f);
          }
        });
      } else {
        listGoods = listGoods.filter((f) => !stateLists.map((m) => m.skuId).includes(f.skuId));
      }
      hasSelectGoodsList.current = listGoods;
      setSelectIds(listGoods.map((m) => m.skuId));
    };

    useImperativeHandle(ref, () => ({
      initData,
    }));

    const onSearch = debounce((searchKey: string) => {
      state.keyword = searchKey;
      state.pageNo = 1;
      state.list = [];
      getGoods(state);
    }, 500);

    useEffect(() => {
      if (visible) {
        setCategoryCustomesTwo([]);
        setSelectCategoryId(null);
        getCategoryCustomes();
        // setShowStockTip(true);
        getGoods(state);
      }
    }, [getGoods, visible]); // eslint-disable-line

    useEffect(() => {
      if (visible) {
        state.standardFilterParams = [];
        state.shopSkuNames = [];
        if (selectGoodsList.length) {
          hasSelectGoodsList.current = JSON.parse(JSON.stringify(selectGoodsList));
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => {
              for (let i = 0; i < selectGoodsList.length; i += 1) {
                if (tmpItem.skuId === selectGoodsList[i].skuId) {
                  return {
                    ...tmpItem,
                    number: selectGoodsList[i].number || 0,
                    num: selectGoodsList[i].num || 0,
                    unit: selectGoodsList[i].auxiliaryUnit || selectGoodsList[i].unit,
                    preferential:
                      selectGoodsList[i].preferential || selectGoodsList[i].preferential,
                  };
                }
              }
              return {
                ...tmpItem,
                number: 0,
                num: 0,
              };
            }),
          });
          const ids = selectGoodsList.map((item) => item.skuId);
          // @ts-ignore
          setSelectIds(ids);
        } else if (state.list.length) {
          hasSelectGoodsList.current = [];
          dispatch({
            type: 'setList',
            payload: state.list.map((tmpItem) => ({
              ...tmpItem,
              number: 0,
              num: 0,
            })),
          });
        }
      } else {
        state.keyword = '';
        state.pageNo = 1;
        state.list = [];
        // getGoods(state);
        state.standardFilterParams = [];
        state.shopSkuNames = [];
        hasSelectGoodsList.current = [];
        setAllChecked(false);
        setSelectIds([]);
      }
    }, [visible]); // eslint-disable-line

    const footer = (
      <div>
        {/* {showStockTip && orderType !== 11 && (
          <div className={styles.stockTip}>
            <img
              className={styles.stockTipImg}
              src="https://img.huahuabiz.com/user_files/2023616/1686898724715212.png"
              alt=""
            />
            <span>在途库存=未生成仓库单的采购订单-未生成仓库单的销售订单。</span>
            <Icon
              name="close"
              className={styles.stockTipDelete}
              onClick={() => setShowStockTip(false)}
            />
          </div>
        )} */}
        <div className={styles.footer}>
          <div className={styles.footerText}>
            {/* <img className={styles.img} src={cart} alt="" /> */}
            <Checkbox checked={allChecked} onChange={allCheckedChange} />
            <span className={styles.img}>全选当前页</span>
            <span>已选 </span>
            <span className={styles.num}>
              {/* {productSum} */}
              {hasSelectGoodsList.current.length}
            </span>
          </div>
          <Button
            type="primary"
            onClick={() => {
              computeNum();
              const arr = hasSelectGoodsList.current.filter((item) =>
                selectIds.includes(item.skuId)
              );
              if (!arr.length) {
                message.warning('请选择商品');
                return;
              }
              setShowSelectGoodsList(true);
              // confirm(arr);
            }}
          >
            选好了
          </Button>
        </div>
      </div>
    );

    const extra = (
      <div
        role="button"
        tabIndex={0}
        className={styles.goodsFilter}
        onClick={() => {
          selectGoodsFilter({
            businessSourceType: 0,
            onClose: () => {},
            onConfirm: (arr, names) => {
              state.pageNo = 1;
              state.list = [];
              state.standardFilterParams = arr;
              state.shopSkuNames = names;
              getGoods(state);
            },
            standardArr: state.standardFilterParams,
            names: state.shopSkuNames,
            visible: true,
          });
        }}
      >
        筛选
      </div>
    );

    return (
      <Drawer
        visible={visible}
        {...props}
        getContainer={() => document.querySelector('#content') as HTMLElement}
        className={styles.selectGoods}
        title="添加商品"
        onClose={onCloseDrawer}
        footer={footer}
        extra={extra}
      >
        <div className={styles.search}>
          <Search
            className={styles.searchInput}
            placeholder="搜索商品名称"
            onSearch={(searchKey) => onSearch(searchKey)}
          />
        </div>
        <div ref={content} className={styles.box}>
          <div className={styles.category} id="category">
            <InfiniteScroll
              dataLength={categoryCustomes.length}
              className={styles.categoryScrollbar}
              hasMore={paramsCustom.current.pageNo < paramsCustom.current.totalPages}
              loader={
                <div className="text-center">
                  <Spin tip="加载中..." />
                </div>
              }
              next={loadMoreCategory}
              scrollableTarget="category"
            >
              {categoryCustomes
                ? categoryCustomes.map((item) => (
                    <div
                      role="button"
                      tabIndex={item.id || 0}
                      key={item.id}
                      className={classNames(styles.item, {
                        [styles.goods]: selectCategoryId === item.id,
                      })}
                      onClick={() => {
                        computeNum();
                        state.customizeCategorySet = item.id ? [item.id] : [];
                        // state.keyword = '';
                        state.pageNo = 1;
                        state.list = [];
                        getGoods(state);
                        selectCategory(item.id);
                        setSelectCategoryIdTwo(null);
                        setAllChecked(false);
                      }}
                    >
                      <Tooltip title={item.categoryName}>{item.categoryName}</Tooltip>
                    </div>
                  ))
                : null}
            </InfiniteScroll>
          </div>
          <div className={styles.boxList}>
            {categoryCustomesTwo && categoryCustomesTwo.length > 0 ? (
              <div className={styles.categoryList}>
                {categoryCustomesTwo
                  ? categoryCustomesTwo.slice(0, 2).map((item) => (
                      <span
                        role="button"
                        tabIndex={item.id || 0}
                        key={item.id}
                        className={classNames(
                          selectCategoryIdTwo === item.id
                            ? styles.categoryItemActive
                            : styles.categoryItem,
                          'mb-4'
                        )}
                        onClick={() => {
                          computeNum();
                          state.pageNo = 1;
                          state.keyword = '';
                          state.list = [];
                          if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                            setSelectCategoryIdTwo(null);
                            state.customizeCategorySet = [parentIdRef.current];
                          } else {
                            setSelectCategoryIdTwo(item.id);
                            state.customizeCategorySet = item.id ? [item.id] : [];
                          }
                          getGoods(state);
                        }}
                      >
                        {item.categoryName}
                      </span>
                    ))
                  : null}
                {categoryCustomesTwo.length > 2 ? (
                  <span
                    role="button"
                    tabIndex={0}
                    className={styles.filter}
                    onClick={() => {
                      setShowMore(!showMore);
                    }}
                  >
                    <Icon name="down" color="#000" />
                  </span>
                ) : null}
              </div>
            ) : null}
            {showMore ? (
              <div className={styles.categoryMore}>
                <div className={styles.categoryBox}>
                  {categoryCustomesTwo
                    ? categoryCustomesTwo.slice(2, categoryCustomesTwo.length).map((item) => (
                        <span
                          role="button"
                          tabIndex={item.id || 0}
                          key={item.id}
                          className={classNames(
                            selectCategoryIdTwo === item.id
                              ? styles.categoryItemActive
                              : styles.categoryItem,
                            'mb-4'
                          )}
                          onClick={() => {
                            computeNum();
                            state.pageNo = 1;
                            state.keyword = '';
                            state.list = [];
                            if (selectCategoryIdTwo && selectCategoryIdTwo === item.id) {
                              setSelectCategoryIdTwo(null);
                              state.customizeCategorySet = [parentIdRef.current];
                            } else {
                              setSelectCategoryIdTwo(item.id);
                              state.customizeCategorySet = item.id ? [item.id] : [];
                            }
                            getGoods(state);
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))
                    : null}
                </div>
              </div>
            ) : null}
            <div
              className={classNames(state.list.length === 0 ? styles.noData : styles.list)}
              id="list"
            >
              {state.list.length > 0 ? (
                <InfiniteScroll
                  dataLength={state.list.length}
                  hasMore={state.pageNo <= state.totalPage}
                  loader={
                    <div className="text-center">
                      <Spin tip="加载中..." />
                    </div>
                  }
                  next={loadMore}
                  scrollableTarget="list"
                >
                  <Checkbox.Group value={selectIds}>
                    {state.list.map((item) => (
                      <div className={styles.goodsItem} key={item.id}>
                        <div className={styles.goods}>
                          <GoodsItem
                            name={item.name}
                            img={item.images || (item.imagesList && item.imagesList[0])}
                            standard={item.standardList}
                            stock={item.stockNumber || item.stock}
                            onTheWaySkuNumber={item.onTheWaySkuNumber}
                            skuNumber={orderType !== 11}
                            barsCode={item.barsCode}
                            saleGroup={Number(item.productMinimumSalesUnit || item.saleGroup || 1)}
                            skuBelongType={Number(item.skuBelongType)}
                          />
                          <Checkbox
                            value={item.skuId}
                            onChange={(e) => {
                              onChangeGoods(e, item.skuId, item);
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </Checkbox.Group>
                </InfiniteScroll>
              ) : (
                <Empty />
              )}
            </div>
          </div>
        </div>
        <PopupModal
          visible={Modal}
          onDetermine={() => {
            setModal(false);
          }}
          onclose={() => {
            dispatch({
              type: 'setList',
              payload: state.list.map((tmpItem) => {
                for (let i = 0; i < selectGoodsList.length; i += 1) {
                  if (tmpItem.id === selectGoodsList[i].id) {
                    return {
                      ...tmpItem,
                      number: selectGoodsList[i].number,
                    };
                  }
                }
                return {
                  ...tmpItem,
                };
              }),
            });
            setModal(false);
            onClose();
          }}
        />
        <SetupGoodsInfo
          warehouseNo={warehouseNo}
          visible={showSelectGoodsList}
          isBatch={orderType !== 9 && orderType !== 11}
          orderType={orderType}
          // @ts-ignore
          selectGoodsList={hasSelectGoodsList.current}
          showStock
          onCancel={() => setShowSelectGoodsList(false)}
          onAddGoods={(arr) => {
            const ids = arr.map((item) => item.skuId);
            setSelectIds([...ids]);
            // @ts-ignore
            hasSelectGoodsList.current = arr;
            setShowSelectGoodsList(false);
          }}
          onConfirm={(arr) => {
            const ids = arr.map((item) => item.skuId);
            setSelectIds([...ids]);
            setShowSelectGoodsList(false);
            // @ts-ignore
            hasSelectGoodsList.current = arr;
            const list = hasSelectGoodsList.current.filter((item) =>
              selectIds.includes(item.skuId)
            );
            confirm(list);
            onClose();
          }}
          orderNo={orderNo || ''}
          isEdit={isEdit}
        />
      </Drawer>
    );
  }
);

SelectBatchGoods.defaultProps = {
  orderNo: '',
};

export default SelectBatchGoods;
