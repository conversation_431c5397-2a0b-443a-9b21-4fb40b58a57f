import { PropsWithChildren, useEffect, useState, useCallback, useReducer } from 'react';
import { Checkbox, Drawer, Spin } from 'antd';
import { Search, Icon } from '@/components';
import { postPlatWareHouse, WareListRusult } from '@/apis';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './index.module.less';

const pageSize = 15;

interface FilterWarehousePropps {
  visible: boolean;
  onCloseWarehouse: () => void;
  selectWarehouse: number[];
  // eslint-disable-next-line no-unused-vars
  confirm: (arr: number[]) => void;
  // eslint-disable-next-line react/no-unused-prop-types
  selectApi?: any;
}

interface InitialState {
  pageNo: number;
  totalPage: number;
  list: WareListRusult[];
  key: string;
}

const initialState = {
  pageNo: 1,
  totalPage: 2,
  list: [],
  key: '',
};

type WarehouseAction =
  | {
      type: 'setPage';
      payload: { pageNo?: number; totalPage?: number };
    }
  | {
      type: 'setList';
      payload: WareListRusult[];
    }
  | {
      type: 'setAll';
      payload: InitialState;
    };

const reducer = (state: InitialState, action: WarehouseAction): InitialState => {
  switch (action.type) {
    case 'setPage':
      return { ...state, ...action.payload };
    case 'setList':
      return { ...state, list: action.payload };
    case 'setAll':
      return action.payload;
    default:
      return state;
  }
};

function Warehouse({
  visible,
  selectWarehouse,
  onCloseWarehouse,
  confirm,
  selectApi,
}: PropsWithChildren<FilterWarehousePropps>) {
  const [categoryDrawer, setCategoryDrawer] = useState(visible);
  const [checkedIds, setCheckedIds] = useState<number[]>([]);
  const [checkedList, setCheckedList] = useState<WareListRusult[]>([]);
  const [state, dispatch] = useReducer(reducer, initialState);
  const [searchKey, setSearchKey] = useState('');

  const onClose = () => {
    setCategoryDrawer(false);
    onCloseWarehouse();
  };

  const getWarehouse = useCallback(
    (argument: InitialState) => {
      // eslint-disable-next-line no-undef
      selectApi?.({
        pageNo: argument.pageNo,
        pageSize,
        key: argument.key,
      }).then((res: any) => {
        setCheckedList(res.list.filter((item: any) => selectWarehouse.includes(item.id as number)));
        dispatch({
          type: 'setAll',
          payload: {
            key: argument.key,
            pageNo: argument.pageNo + 1,
            list: [...argument.list, ...res.list],
            totalPage: Math.ceil(res.pagination.count / pageSize),
          },
        });
      });
    },
    [selectApi, selectWarehouse]
  );

  const onChange = (value: any) => {
    setCheckedIds(value);
    setCheckedList(state.list.filter((item) => value.includes(item.id as number)));
  };

  const del = (id: number | string | boolean) => {
    setCheckedIds(checkedIds.filter((item) => item !== id));
    setCheckedList(checkedList ? checkedList.filter((item) => item.id !== id) : []);
  };

  const loadMore = () => {
    if (!(state.pageNo <= state.totalPage)) return;
    getWarehouse(state);
  };

  useEffect(() => {
    setCategoryDrawer(visible);
    if (visible) {
      setCheckedIds(selectWarehouse);
      setSearchKey('');
      getWarehouse(initialState);
    }
  }, [getWarehouse, selectWarehouse, visible]); // eslint-disable-line

  return (
    <Drawer
      onClose={onClose}
      visible={categoryDrawer}
      placement="bottom"
      width={375}
      className={styles.drawer}
      getContainer={false}
      mask={categoryDrawer}
      title="选择仓库"
      extra={
        <div
          role="button"
          tabIndex={0}
          onClick={() => {
            confirm(checkedIds);
          }}
        >
          确定
        </div>
      }
    >
      <Search
        placeholder="请输入仓库名称"
        className={styles.search}
        value={searchKey}
        onSearch={(e) => {
          setSearchKey(e);
          state.key = e;
          state.pageNo = 1;
          state.list = [];
          getWarehouse(state);
        }}
      />
      <div className={styles.select}>
        {checkedList
          ? checkedList.map((item) => (
              <span key={item.id} className={styles.item}>
                <span className={styles.name}>{item.warehouseName}</span>
                <Icon
                  onClick={() => {
                    del(item.id as number);
                  }}
                  className={styles.del}
                  name="close-circle"
                />
              </span>
            ))
          : null}
      </div>

      <div className={styles.list} id="list">
        <InfiniteScroll
          dataLength={state.list.length}
          hasMore={state.pageNo <= state.totalPage}
          loader={
            <div className="text-center">
              <Spin tip="加载中..." />
            </div>
          }
          next={loadMore}
          scrollableTarget="list"
        >
          <Checkbox.Group onChange={onChange} value={checkedIds}>
            {state.list
              ? state.list.map((item) => (
                  <div className={styles.label} key={item.id}>
                    <Checkbox value={item.id}>{item.warehouseName}</Checkbox>
                  </div>
                ))
              : null}
          </Checkbox.Group>
        </InfiniteScroll>
      </div>
    </Drawer>
  );
}

Warehouse.defaultProps = {
  selectApi: postPlatWareHouse,
};

export default Warehouse;
