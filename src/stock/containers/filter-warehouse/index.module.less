@import 'styles/mixins/mixins';

.drawer {
  position: absolute !important;
  transition: none !important;

  :global {
    .ant-drawer-content-wrapper {
      height: 70% !important;
      transform: none !important;
    }

    .ant-drawer-content,
    .ant-drawer-header {
      background-color: #fff;
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-wrapper-body {
      padding-top: 0;
    }

    .ant-drawer-body {
      display: flex;
      flex-direction: column;
    }
  }
}

.search {
  margin-bottom: 16px;
  background: #fff !important;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);

  :global {
    .ant-input {
      background: #fff !important;
    }
  }
}

.select {
  display: flex;
  flex-wrap: wrap;

  .item {
    display: flex;
    width: 77px;
    height: 36px;
    margin-right: 7px;
    margin-bottom: 12px;
    padding: 5px;
    justify-content: center;
    position: relative;
    align-items: center;
    border: 1px solid rgb(218 219 224 / 100%);
    border-radius: 10px;
    cursor: pointer;

    & .name {
      .text-overflow();
    }

    &:nth-child(4n) {
      margin-right: 0;
    }

    & .text {
      display: inline-block;
      width: 67px;
      text-align: center;
      .text-overflow();
    }

    & .del {
      color: #e3e2e2;
      font-size: 16px;
      position: absolute;
      top: -10px;
      right: -10px;
      cursor: pointer;
    }
  }
}

.list {
  flex: 1;
  overflow: auto;

  :global {
    .ant-checkbox-group {
      width: 100%;
    }
  }
}

.label {
  display: flex;
  width: 100%;
  padding: 12px 0;
  justify-content: space-between;

  .next,
  .nextActive {
    color: rgb(0 140 255 / 100%);
    font-size: 12px;
    cursor: pointer;
  }

  .nextActive {
    color: rgb(177 179 190 / 100%);
    cursor: default;
  }

  :global {
    .el-input__inner {
      border: 1px solid #cad1db !important;
    }

    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      border-radius: 0 !important;

      &::after {
        top: 0 !important;
        left: 4px !important;
      }
    }
  }
}
