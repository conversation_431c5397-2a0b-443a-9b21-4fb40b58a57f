import { PropsWithChildren, useEffect, useState, useRef, useMemo } from 'react';
import { Button, Form, Select, Input, FormInstance, Modal, message, Tooltip } from 'antd';
import { useMemoizedFn, useRequest } from 'ahooks';
import { Drawer, Icon, Upload, DatePicker, SimpleUploadInstance } from '@/components';
import {
  GoodsListResult,
  getCustomerPageAllCustomerList,
  supplierListMy,
  CustomerResult,
  permissionMemberList,
  PermissionMemberListResult,
  stockWarehouseList,
  StockWarehouseResult,
  StockOrderDetailResult,
  OrderAddParams,
  postPlatBaseStockCheck,
  ContactListProps,
} from '@/apis';
import { user } from '@/store';
// import { Stepper } from 'antd-mobile';
import dayjs from 'dayjs';
import { isPhone } from '@/utils/utils';
import classNames from 'classnames';
import debounce from 'lodash/debounce';
import goodsNoStock from '@@/order-rewrite/components/goods-no-stock';
import styles from './index.module.less';
import SelectBatchGoods from '../select-batch-goods';
import SelectGoodsList from '../../components/select-goods-list';
import orderPermission from '../../utils/order-permission';

interface PlatformProps {
  isNeedInitWorkflowForm: boolean;
  processDefinitionId: string;
  processDefinitionKey: string;
  processDefinitionName: string;
  businessOrderNo: string;
  businessType: number;
  isNeedSecStep: boolean;
  isNeedApprovoal: number;
  description: string;
}

interface WareAddPropps {
  pageNo: number;
  totalPages: number;
  pageSize: number;
  warehouseStatus: number;
  functionType?: number;
  customerId?: number;
  customerCompanyId?: number;
}

interface OrderAddPropps {
  visible: boolean;
  orderType: number;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  submit: () => void;
  orderName?: string;
  id?: number;
  // eslint-disable-next-line no-unused-vars
  orderDeteil?: (id: number) => Promise<StockOrderDetailResult>;
  OrderCreact: (
    // eslint-disable-next-line no-unused-vars
    arg0: OrderAddParams
  ) => Promise<PlatformProps>;
  openUpload?: () => void;
}

function OrderAdd({
  visible,
  submit,
  orderType,
  orderName,
  id,
  orderDeteil,
  OrderCreact,
  openUpload,
  ...props
}: PropsWithChildren<OrderAddPropps>) {
  const [form] = Form.useForm();
  const params = useRef({
    pageNo: 1,
    totalPages: 0,
    pageSize: 10,
    customerType: orderType === 9 ? null : 0,
    companyId: orderType === 9 ? user.companyId || null : null,
    keyword: orderType === 9 ? '' : null,
    keywords: orderType === 9 ? null : '',
    filterContact: orderType === 9 ? null : false,
    filterPublic: orderType === 9 ? null : true,
  });
  const paramsWarehouse = useRef<WareAddPropps>({
    pageNo: 1,
    totalPages: 0,
    pageSize: 10,
    warehouseStatus: 0,
  });
  const uploadRef = useRef(null as unknown as SimpleUploadInstance);
  const selectGoodsRef = useRef();
  const [label, setLabel] = useState('');
  const [showSelectGoods, setShowSelectGoods] = useState(false);
  const [selectGoodsList, setSelectGoodsList] = useState<GoodsListResult[]>([]);
  const [colleatues, setColleatues] = useState<PermissionMemberListResult[]>([]);
  const [customers, setCustomers] = useState<CustomerResult[]>([]);
  const [customerInfo, setCustomerInfo] = useState<CustomerResult>({
    customerId: 0,
    customerNo: 0,
    customerName: '',
    customerCompanyId: 0,
  });
  const [phoneList, setPhoneList] = useState<ContactListProps[]>([]);
  const [warehouses, setWarehouses] = useState<StockWarehouseResult[]>([]);
  const [warehouseNo, setWarehouseNo] = useState('');
  const [fileList, setFileList] = useState([]);
  const formRef = useRef(null as unknown as FormInstance);
  const [info, setInfo] = useState('');
  const [firstWarehouseId, setFirstWarehouseId] = useState<number | null>();
  const [goodsInfo, setGoodsInfo] = useState('');
  const [fileListInfo, setFileListInfo] = useState('');
  const [isDisabled, setIsDisabled] = useState(!!(id && id > 0));
  // const [units, setUnits] = useState<FormulaConvertManyconvertResult[]>([]);
  const orderNo = useRef('');

  // @ts-ignore
  const { run, loading } = useRequest(
    // @ts-ignore
    orderType === 9 ? supplierListMy : getCustomerPageAllCustomerList,
    {
      manual: true,
      defaultParams: [
        {
          pageNo: params.current.pageNo,
          pageSize: params.current.pageSize,
          customerType: orderType === 9 ? null : 0,
          companyId: orderType === 9 ? user.companyId || null : null,
          keyword: params.current.keyword,
          keywords: params.current.keyword,
          filterContact: orderType === 9 ? null : false,
          filterPublic: orderType === 9 ? null : true,
        },
      ],
      onSuccess: (result) => {
        if (params.current.pageNo === 1) {
          // @ts-ignore
          setCustomers(result.list);
        } else {
          // @ts-ignore
          setCustomers([...customers, ...result.list]);
        }
        params.current.pageNo += 1;
        params.current.totalPages = result.pagination.total;
      },
    }
  );

  const { run: runWarehouse, loading: loadingWarehouse } = useRequest(stockWarehouseList, {
    defaultParams: [paramsWarehouse.current],
    manual: true,
    onSuccess: (result) => {
      paramsWarehouse.current.pageNo += 1;
      paramsWarehouse.current.totalPages = result.pagination.total;

      setWarehouses([...warehouses, ...result.list]);
      // if (id) {
      //   setWarehouses([
      //     ...warehouses,
      //     ...result.list.filter((item) => item.id !== JSON.parse(info).warehouseId),
      //   ]);
      // } else {
      //   setWarehouses([...warehouses, ...result.list]);
      // }
    },
  });

  const onDelete = (idVal: number) => {
    Modal.confirm({
      title: '提示',
      icon: '',
      centered: true,
      width: 290,
      content: '确定删除该商品？',
      getContainer: document.querySelector('.order-create') as HTMLElement,
      onOk: () => {
        setSelectGoodsList(
          selectGoodsList.filter((goods) => (goods.id || goods.shopSkuId) !== idVal)
        );
        message.success('删除成功!');
      },
    });
  };

  const onMore = (idVal: number) => {
    selectGoodsList.forEach((item) => {
      const items = item;
      if (item.id === idVal) {
        items.isMore = !items.isMore;
      }
    });
    setSelectGoodsList(JSON.parse(JSON.stringify(selectGoodsList)));
  };

  const isDisableOperatorId = useMemo(() => {
    if (
      orderName !== '其他入库单' &&
      orderName !== '其他出库单' &&
      orderName !== '退货入库单' &&
      orderName !== '采购入库单' &&
      orderName !== '销售出库单' &&
      id &&
      id > 0
    ) {
      return true;
    }
    return false;
  }, [visible]); // eslint-disable-line

  const isDisableOrder = useMemo(() => {
    if (
      orderName !== '其他入库单' &&
      orderName !== '其他出库单' &&
      orderName !== '退货入库单' &&
      id &&
      id > 0
    ) {
      return true;
    }
    return false;
  }, [visible]); // eslint-disable-line

  const onFinish = debounce((orderStatus: 0 | 1 | 2) => {
    const attachmentList = uploadRef.current.getFileList();
    form.validateFields().then(() => {
      if (!orderPermission(orderType, orderStatus === 1 ? '审批' : '取消') && orderStatus !== 2)
        return message.error('暂无权限，请联系公司管理员开通');
      if (selectGoodsList.length === 0) return message.error('请选择商品!');
      if (selectGoodsList.some((item) => item.batchList.some((each) => !each.number))) {
        return message.warning('请填写商品数量');
      }
      if (
        selectGoodsList.some((item) => item.batchList.some((each) => !each.batch.length)) &&
        orderType === 10
      ) {
        return message.warning('请选择商品批次');
      }
      const data = {
        orderStatus,
        id,
        ...formRef.current.getFieldsValue(),
        customerNo: customerInfo?.customerNo || null,
        customerName: customerInfo?.customerName,
        customerCompanyId: customerInfo?.customerCompanyId,
        orderDate: dayjs(formRef.current.getFieldsValue(['orderDate']).orderDate).valueOf(),
        productionTime: formRef.current.getFieldsValue(['productionTime']).productionTime
          ? dayjs(formRef.current.getFieldsValue(['productionTime']).productionTime).valueOf()
          : '',
        platformInOrOutBoundSkuParams: selectGoodsList.map((item) => ({
          shopSkuId: item.shopSkuId || item.id,
          skuId: item.skuId,
          // auxiliaryUnit: item.auxiliaryUnit || item.unit,
          quantity: item.number,
          skuRelationParamList: item.batchList.map((each) => ({
            skuBatchNo: each.batch,
            unitName: each.auxiliaryUnit || each.unit,
            quantity: each.number,
          })),
        })),
        attachmentList: attachmentList.map((item) => ({
          fileName: item.name,
          size: item.size,
          filePath: item.url,
          fileType: item.type?.indexOf('image') !== -1 ? 0 : 1,
        })),
      };
      setIsDisabled(true);
      OrderCreact(data)
        .then((res) => {
          const {
            isNeedSecStep,
            isNeedApprovoal,
            description,
            isNeedInitWorkflowForm,
            processDefinitionKey,
            processDefinitionName,
            businessOrderNo,
            businessType,
          } = res;
          if (isNeedInitWorkflowForm) {
            window.open(
              `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                {
                  businessOrderNo,
                  businessType,
                }
              )}`
            );
          }
          setWarehouseNo('');
          if (!isNeedInitWorkflowForm) {
            if (isNeedApprovoal === 1) {
              message.success(description);
            } else {
              message.success(`${id ? '编辑' : '添加'}成功!`);
            }
          }
          if (isNeedSecStep && isNeedApprovoal === 0 && openUpload && orderName !== '退货入库单') {
            openUpload();
            return;
          }
          // @ts-ignore
          selectGoodsRef.current?.initData();
          // eslint-disable-next-line no-use-before-define
          submit();
        })
        .catch((e) => {
          if (e.code === 5005012) {
            goodsNoStock({ list: JSON.parse(e.message) });
          } else {
            message.error(e.message);
          }
        })
        .finally(() => {
          setIsDisabled(false);
        });
      return false;
    });
  }, 500);

  const leaveTip = () => {
    Modal.confirm({
      title: '提示',
      icon: '',
      cancelText: '离开',
      okText: '继续编辑',
      centered: true,
      content: '您编辑的内容尚未保存，确定要离开吗？',
      onOk: () => {},
      onCancel: () => {
        setWarehouseNo('');
        paramsWarehouse.current.pageNo = 1;
        setWarehouses([]);
        props.onClose();
      },
    });
  };

  const onChangeWarehouse = () => {
    if (orderType !== 9) {
      selectGoodsList.forEach((item) => {
        item.batchList.forEach((each) => {
          const eachs = each;
          eachs.batch = '';
        });
      });
      setSelectGoodsList([...selectGoodsList]);
    }
  };

  const onSearch = debounce((e: string) => {
    params.current = {
      ...params.current,
      pageNo: 1,
      keyword: orderType === 9 ? e : null,
      keywords: orderType === 9 ? null : e,
    };
    // @ts-ignore
    run({ ...params.current });
  }, 500);

  const isShowSave = useMemo(() => {
    if (orderName !== '其他入库单' && orderName !== '其他出库单' && id && id > 0) {
      return false;
    }
    return true;
  }, [visible]); // eslint-disable-line

  useEffect(() => {
    if (visible) {
      params.current = {
        keyword: orderType === 9 ? '' : null,
        keywords: orderType === 9 ? null : '',
        pageNo: 1,
        totalPages: 0,
        pageSize: 10,
        customerType: orderType === 9 ? null : 0,
        companyId: orderType === 9 ? user.companyId || null : null,
        filterContact: orderType === 9 ? null : false,
        filterPublic: orderType === 9 ? null : true,
      };
      permissionMemberList({
        companyId: user.companyId,
        permCode: orderType === 9 ? 'AV_001_002' : 'AV_001_001',
      }).then((res) => {
        setColleatues(res.list);
      });
      if (id && visible) {
        orderDeteil?.(id).then((res) => {
          orderNo.current = String(res.orderNo);
          if (customers.length === 0 && res.customerId && res.customerName) {
            customers.push({
              customerId: res.customerId,
              customerNo: res.customerNo,
              customerName: res.customerName,
              customerPhone: res.customerPhone,
              customerCompanyId: res.customerCompanyId,
            });
          }
          if (customers.every((e) => e.customerId !== res.customerId) && res.customerId) {
            customers.push({
              customerId: res.customerId,
              customerNo: res.customerNo,
              customerName: res.customerName,
              customerPhone: res.customerPhone,
              customerCompanyId: res.customerCompanyId,
            });
          }
          if (warehouses.length === 0) {
            warehouses.push({
              warehouseName: res.warehouseName || '',
              id: res.warehouseId,
              warehouseNo: res.warehouseNo,
            });
          }
          setWarehouseNo(res.warehouseNo);
          formRef.current.setFieldsValue({
            customerId: res.customerId && res.customerName ? res.customerId : null,
            customerPhone: res.customerPhone || null,
            warehouseId: res.warehouseId,
            orderDate: dayjs(res.orderDate),
            productionTime: res.productionTime ? dayjs(res.productionTime) : null,
            operatorId: res.operatorId,
            remark: res.remark,
          });
          setSelectGoodsList(
            // @ts-ignore
            (res.platformInboundSkuVOS || res.platformOutboundSkuVOS).map((item) => ({
              ...item,
              name: item.skuName,
              firstNum: item.quantity,
              standardList: JSON.parse(item.standardJson),
              number: item.quantity,
              productMinimumSalesUnit: item.auxiliarySaleGroup,
              batchList: item.skuRelationParamList.map((each) => ({
                batch: each.skuBatchNo,
                unit: each.auxiliaryUnit || each.unitName,
                number: each.quantity,
              })),
            }))
          );
          setFileList(
            // @ts-ignore
            res.attachmentList.map((item, index) => ({
              uid: index,
              name: item.fileName,
              size: item.size,
              url: item.filePath,
              type: item.fileType === 0 ? 'image/' : '',
            }))
          );
          setCustomerInfo({
            customerId: res.customerId,
            customerNo: res.customerNo,
            customerName: res.customerName,
            customerPhone: res.customerPhone,
            customerCompanyId: res.customerCompanyId,
          });
          setInfo(JSON.stringify(formRef.current.getFieldsValue()));
          setGoodsInfo(
            JSON.stringify(
              (res.platformInboundSkuVOS || res.platformOutboundSkuVOS).map((item) => ({
                ...item,
                name: item.skuName,
                standardList: JSON.parse(item.standardJson),
                number: item.quantity,
                productMinimumSalesUnit: item.auxiliarySaleGroup,
              }))
            )
          );
          setFileListInfo(
            JSON.stringify(
              res.attachmentList.map((item, index) => ({
                uid: index,
                name: item.fileName,
                size: item.size,
                url: item.filePath,
                type: item.fileType === 0 ? 'image/' : '',
              }))
            )
          );
        });
      }
      if (!id) {
        formRef?.current?.resetFields();
        setCustomerInfo({
          customerId: 0,
          customerNo: 0,
          customerName: '',
          customerCompanyId: 0,
        });
        setFileList([]);
        setSelectGoodsList([]);
        form.setFieldsValue({
          orderDate: dayjs(),
        });
        // @ts-ignore
        run(params.current);
      }
    }
  }, [id, orderDeteil, orderType, run, runWarehouse, visible]); // eslint-disable-line

  useEffect(() => {
    switch (orderType) {
      case 9:
        return setLabel('入库');
      default:
        return setLabel('出库');
    }
  }, [orderType]);

  return (
    <Drawer
      {...props}
      visible={visible}
      width={375}
      title={`${id ? '编辑' : '新建'}${orderType === 9 ? '入' : '出'}库单`}
      push={false}
      onClose={() => {
        if (!visible) {
          return;
        }
        if (!id) {
          if (
            Object.values(formRef.current.getFieldsValue()).some((item) => item !== undefined) ||
            selectGoodsList.length > 0
          ) {
            leaveTip();
          } else {
            setWarehouseNo('');
            paramsWarehouse.current.pageNo = 1;
            setWarehouses([]);
            props.onClose();
          }
        } else if (
          goodsInfo !== JSON.stringify(selectGoodsList) ||
          info !== JSON.stringify(formRef.current.getFieldsValue()) ||
          fileListInfo !== JSON.stringify(fileList)
        ) {
          leaveTip();
        } else {
          setWarehouseNo('');
          paramsWarehouse.current.pageNo = 1;
          setWarehouses([]);
          props.onClose();
        }
      }}
      extra={
        isShowSave ? (
          <div
            role="button"
            tabIndex={0}
            className={
              isDisabled || selectGoodsList.length === 0 ? styles.importData : styles.importDataC
            }
            onClick={() => {
              if (isDisabled || selectGoodsList.length === 0) return;
              onFinish(2);
            }}
          >
            保存
          </div>
        ) : undefined
      }
      footer={
        <div className={styles.footer}>
          <Button
            className={classNames(
              isDisabled || selectGoodsList.length === 0 ? styles.btnDisadled : styles.btn
            )}
            disabled={isDisabled || selectGoodsList.length === 0}
            onClick={() => {
              if (isDisabled || selectGoodsList.length === 0) return;
              if (!orderPermission(orderType, '提交')) {
                message.error('暂无权限，请联系公司管理员开通');
              } else {
                onFinish(0);
              }
            }}
          >
            提交
          </Button>
          <Button
            className={styles.btn}
            type="primary"
            disabled={isDisabled || selectGoodsList.length === 0}
            onClick={() => {
              if (!orderPermission(orderType, '审批')) {
                message.error('暂无权限，请联系公司管理员开通');
              } else {
                onFinish(1);
              }
            }}
          >
            审批
          </Button>
        </div>
      }
    >
      <div className="order-create">
        <Form
          ref={formRef}
          layout="vertical"
          className={styles.form}
          // validateTrigger={['onChange']}
          onFieldsChange={() => {
            const disabled =
              Object.values(formRef.current.getFieldsValue(['warehouseId', 'operatorId'])).every(
                (item) => item !== undefined
              ) && form.getFieldsValue(['orderDate']);

            setIsDisabled(!disabled);
          }}
        >
          <div className={styles.card}>
            <Form.Item
              className={styles.noRequired}
              name="customerId"
              label={orderType === 9 ? '供应商' : '客户'}
            >
              <Select
                allowClear
                showSearch
                disabled={isDisableOrder}
                filterOption={false}
                optionFilterProp="label"
                suffixIcon={<Icon name="down" size={16} />}
                placeholder={
                  !customerInfo.customerId && customerInfo.customerName.length
                    ? customerInfo.customerName
                    : `请选择${orderType === 9 ? '供应商' : '客户'}`
                }
                bordered={false}
                loading
                onPopupScroll={() => {
                  if (loading || params.current.pageNo > params.current.totalPages) return;
                  // @ts-ignore
                  run(params.current);
                }}
                // options={customers.map((item) => ({
                //   label: item.customerName,
                //   value: item.customerId || item.id,
                // }))}
                onSearch={onSearch}
                onChange={(e) => {
                  const customInfo: any = customers?.filter(
                    (item) => item.customerId === Number(e) || item.id === Number(e)
                  );
                  if (e) {
                    let phone: number | string = '';
                    if (
                      customInfo[0] &&
                      customInfo[0].length &&
                      customInfo[0].customerCategory === 2
                    ) {
                      phone = customInfo[0].phone;
                    } else {
                      phone =
                        customInfo[0].contactList && customInfo[0].contactList.length
                          ? Number(customInfo[0].contactList[0].phone)
                          : Number(customInfo[0].phone) || '';
                    }
                    setPhoneList([...customInfo[0].contactList]);
                    setCustomerInfo({
                      ...customInfo[0],
                      customerPhone: phone || null,
                    });
                    formRef.current.setFieldsValue({
                      customerPhone: phone || null,
                    });
                    if (orderType !== 9) {
                      formRef.current.setFieldsValue({
                        warehouseId: null,
                      });
                    }
                  }
                  params.current = {
                    ...params.current,
                    keyword: '',
                    keywords: '',
                    pageNo: 1,
                  };
                  // @ts-ignore
                  run(params.current);
                }}
                onFocus={() => {
                  if (params.current.pageNo > 1) return;
                  // @ts-ignore
                  run(params.current);
                }}
                onClear={() => {
                  setCustomerInfo({
                    customerId: 0,
                    customerNo: 0,
                    customerName: '',
                    customerCompanyId: 0,
                  });
                  formRef.current.setFieldsValue({
                    customerPhone: null,
                  });
                }}
              >
                {customers.map((item) => (
                  <Select.Option key={item.id || item.customerId}>
                    <Tooltip
                      placement="top"
                      title={`${item.customerName}${
                        item.warehouseName ? `(${item.warehouseName}/${item.userName})` : ''
                      }`}
                    >
                      <span>
                        {item.customerName}
                        {item.warehouseName ? `(${item.warehouseName}/${item.userName})` : ''}
                      </span>
                    </Tooltip>
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <div className={styles.antForm}>
              <Form.Item
                label="联系电话"
                name="customerPhone"
                validateTrigger="onBlur"
                getValueFromEvent={(e: any) => e.replace(/[^\d]/g, '')}
                rules={[
                  {
                    validator: (_, value) =>
                      !value || isPhone(`${value}`)
                        ? Promise.resolve()
                        : Promise.reject(new Error('手机号码错误')),
                  },
                ]}
              >
                <Select
                  disabled={isDisableOrder}
                  bordered={false}
                  allowClear
                  showSearch
                  placeholder="请选择电话号码"
                  suffixIcon={<Icon name="down" size={16} />}
                  onChange={(e) => {
                    setCustomerInfo({
                      ...customerInfo,
                      customerPhone: e,
                    });
                    formRef.current.setFieldsValue({
                      customerPhone: e,
                    });
                  }}
                  optionLabelProp="label"
                  onFocus={() => {
                    if (!customerInfo.customerNo) {
                      setPhoneList([]);
                    }
                  }}
                >
                  {phoneList.map((item) => (
                    <Select.Option value={item.phone} label={item.phone} key={item.id}>
                      <div className={styles.formSelect}>
                        <div>
                          <div
                            className={classNames(styles.selectPhone, {
                              [styles.selectActive]:
                                Number(customerInfo.customerPhone) === Number(item.phone),
                            })}
                          >
                            {item.phone}
                          </div>
                          <div className={styles.selectName}>{item.contactName}</div>
                        </div>
                        {Number(customerInfo.customerPhone) === Number(item.phone) && (
                          <img
                            src="https://img.huahuabiz.com/user_files/2023417/1681724945896901.png"
                            alt=""
                          />
                        )}
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </div>
          <div className={styles.card}>
            <Form.Item
              name="orderDate"
              label={`${label}时间`}
              rules={[{ required: true, message: `请选择${label}时间` }]}
            >
              <DatePicker
                placeholder={`请选择${label}时间`}
                suffixIcon={<Icon name="down" size={16} />}
                disabled={isDisableOrder}
                bordered={false}
                defaultValue={dayjs()}
              />
            </Form.Item>
            {orderType === 9 && (
              <Form.Item className={styles.noRequired} name="productionTime" label="生产时间">
                <DatePicker
                  placeholder="请选择生产时间"
                  suffixIcon={<Icon name="down" size={16} />}
                  disabled={isDisableOrder}
                  bordered={false}
                  // defaultValue={dayjs()}
                />
              </Form.Item>
            )}
            <Form.Item
              name="warehouseId"
              label={`${label}仓库`}
              rules={[{ required: true, message: `请选择${label}仓库` }]}
            >
              <Select
                allowClear
                showSearch
                disabled={orderType === 10 && orderName === '销售出库单' && !!id}
                optionFilterProp="label"
                suffixIcon={<Icon name="down" size={16} />}
                placeholder={`请选择${label}仓库`}
                bordered={false}
                onPopupScroll={() => {
                  if (
                    loadingWarehouse ||
                    paramsWarehouse.current.pageNo > paramsWarehouse.current.totalPages
                  )
                    return;
                  runWarehouse(paramsWarehouse.current);
                }}
                options={warehouses.map((item) => ({
                  label: item.warehouseName,
                  value: item.id,
                }))}
                onFocus={useMemoizedFn(() => {
                  if (id && (orderName === '采购入库单' || orderName === '销售出库单')) {
                    setFirstWarehouseId(formRef.current.getFieldsValue().warehouseId || null);
                  }
                  paramsWarehouse.current.pageNo = 1;
                  const data = {
                    ...paramsWarehouse.current,
                    pageNo: 1,
                  };

                  if (['入库', '出库'].includes(label) && customerInfo.customerCompanyId) {
                    setWarehouses([]);
                    data.functionType = label === '入库' ? 2 : 3;
                    data.customerCompanyId = customerInfo.customerCompanyId;
                  }
                  if (['出库'].includes(label) && customerInfo.id) {
                    data.customerId = customerInfo.id;
                  }
                  setWarehouses([]);
                  runWarehouse(data);
                })}
                onChange={(e) => {
                  const warehouseId = warehouses.filter((item) => item.id === e)[0].warehouseNo;
                  setWarehouseNo(warehouseId);
                  if (id && orderName === '销售出库单') {
                    postPlatBaseStockCheck({
                      warehouseNo: warehouseId,
                      skuIds: selectGoodsList.map((m) => m.skuId),
                    }).then((res) => {
                      if (!res.isExistsFlag) {
                        message.error('仓库中不存在此商品！');
                        formRef.current.setFieldsValue({
                          warehouseId: firstWarehouseId,
                        });
                      } else {
                        onChangeWarehouse();
                      }
                    });
                  } else if (id && orderType === 10 && orderName !== '销售出库单') {
                    setSelectGoodsList([]);
                  } else if (orderType !== 9) {
                    setSelectGoodsList([]);
                  }
                }}
                onClear={() => {
                  if (orderType !== 9) {
                    setWarehouseNo('');
                    setSelectGoodsList([]);
                  }
                }}
              />
            </Form.Item>
            <div className={styles.antLabel}>
              <Form.Item
                name="operatorId"
                label="业务员"
                rules={[{ required: true, message: '请选择业务员' }]}
              >
                <Select
                  allowClear
                  showSearch
                  optionFilterProp="label"
                  suffixIcon={<Icon name="down" size={16} />}
                  placeholder="请选择业务员"
                  disabled={isDisableOperatorId}
                  bordered={false}
                  options={colleatues.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                  getPopupContainer={(triggerNode) => triggerNode.parentElement}
                />
              </Form.Item>
            </div>
          </div>
          {!isDisableOrder && (
            <div className={styles.card}>
              <span
                role="button"
                tabIndex={0}
                className={classNames(
                  !warehouseNo && orderType !== 9 ? styles.addGoodsDisable : styles.addGoods
                )}
                onClick={() => {
                  if (isDisableOrder) return;
                  if (!warehouseNo && orderType !== 9) {
                    message.error('请先选择仓库');
                  } else {
                    setShowSelectGoods(true);
                  }
                }}
              >
                <Icon name="plus" className={styles.icon} />
              </span>
              <span className={styles.font}>
                添加商品
                {orderType !== 9 && !warehouseNo && (
                  <span className={styles.fontTip}>（请先选择仓库）</span>
                )}
              </span>
            </div>
          )}
          {selectGoodsList.length > 0 && (
            <SelectGoodsList
              warehouseNo={warehouseNo}
              isBatch={orderType !== 9}
              orderType={orderType}
              selectGoodsList={selectGoodsList}
              isDisableOrder={isDisableOrder}
              onDelete={onDelete}
              onMore={onMore}
              onAddGoods={(arr) => {
                setShowSelectGoods(true);
                setSelectGoodsList(arr);
              }}
              onConfirm={(e) => {
                setSelectGoodsList(e);
              }}
              isEdit={Boolean(id)}
              orderNo={orderNo.current}
              businessName={orderName}
            />
          )}
          <div className={styles.card}>
            <div className={classNames(styles.antForm, styles.fromTextarea)}>
              <Form.Item label="备注" name="remark">
                <Input.TextArea
                  className={styles.textarea}
                  placeholder="请填写备注"
                  maxLength={255}
                  autoSize={{ minRows: 3, maxRows: 5 }}
                />
              </Form.Item>
            </div>
            <div className={styles.footerTitle}>添加附件</div>
            <div className={styles.prompt}>(附件最多5个 图片最多6张)</div>
            <div className={styles.antLabel}>
              <Form.Item name="attachment">
                <Upload
                  fileList={fileList}
                  listType="mixin"
                  multiple
                  ref={uploadRef}
                  maxCount={11}
                  beforeUpload={(e, fileUploadList) => {
                    let imageCount = 0;
                    let fileCount = 0;
                    fileUploadList.forEach((file) => {
                      if (file.type && file.type.indexOf('image/') === 0) {
                        imageCount += 1;
                      } else {
                        fileCount += 1;
                      }
                    });
                    if (imageCount > 6 || fileCount > 5) {
                      message.error('附件最多5个 图片最多6张');
                    }
                    return imageCount <= 6 && fileCount <= 5;
                  }}
                />
              </Form.Item>
            </div>
          </div>
        </Form>
        <SelectBatchGoods
          visible={showSelectGoods}
          orderType={orderType}
          warehouseNo={warehouseNo}
          // @ts-ignore
          ref={selectGoodsRef}
          onClose={() => {
            setShowSelectGoods(!showSelectGoods);
          }}
          // @ts-ignore
          selectGoodsList={selectGoodsList}
          // @ts-ignore
          confirm={(e: GoodsListResult[]) => {
            setShowSelectGoods(false);
            setSelectGoodsList(e);
          }}
          orderNo={orderNo.current || ''}
          isEdit={Boolean(id)}
        />
      </div>
    </Drawer>
  );
}

OrderAdd.defaultProps = {
  id: 0,
  orderDeteil: null,
  orderName: '',
  openUpload: () => {},
};

export default OrderAdd;
