@import '../../styles/form.module.less';

.card,
.cardGods {
  margin-bottom: 20px;
  padding: 16px 10px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .font {
    font-size: 16px;
  }

  .fontTip {
    color: #ea1c26;
    font-size: 12px;
  }

  :global {
    .ant-form-item-control-input {
      padding: 0 10px;
    }

    .ant-select-selection-search {
      left: 0 !important;
    }
  }
}

.icon {
  font-size: 20px;
}

.cardGods {
  padding: 16px 20px;
}

.antForm {
  :global {
    .ant-form-item-control-input-content {
      border-bottom: none;
    }

    .ant-form-item-label {
      padding-left: 10px;
    }
  }
}

.inputStyle {
  padding: 0 0 12px 11px;
}

.noRequired {
  :global {
    .ant-form-item-label {
      padding-left: 10px;
    }
  }
}

.importDataC {
  color: #008cff;
  cursor: pointer;
}

.importData {
  color: #888b98;
  cursor: not-allowed;
}

.antLabel {
  :global {
    .ant-form-item-control-input-content {
      border-bottom: none;
    }
  }
}

.fromTextarea {
  :global {
    .ant-form-item-control-input-content {
      padding-left: 0;
    }
  }
}

.prompt {
  color: #b1b3be;
  font-size: 12px;
  margin: 2px 0 12px;
  padding-left: 10px;
}

.footerTitle {
  margin-top: 20px;
  padding-left: 10px;
}

.addGoods,
.addGoodsDisable {
  color: #008cff;
  display: inline-block;
  width: 36px;
  height: 36px;
  line-height: 36px;
  margin: 0 12px 0 4px;
  text-align: center;
  border-radius: 8px;
  background: #d9eeff;
  cursor: pointer;

  & .delete {
    color: #008cff;
    font-size: 22px;
    cursor: pointer;
  }
}

.addGoodsDisable {
  background: rgb(198 204 216 / 20%);
  color: #b1b3be;
  cursor: no-drop;
}

.gooodsItemBox {
  border-bottom: 1px solid #f3f3f3;
  margin-bottom: 20px;

  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }
}

.gooodsItem {
  display: flex;
  align-items: center;
  padding-bottom: 20px;
  border-bottom: 1px solid #f3f3f3;
  position: relative;

  & .delete {
    margin-right: 12px;
    cursor: pointer;
    position: absolute;
    top: 0;
    right: 0;
  }
}

.num {
  color: #040919;
  display: flex;
  padding-top: 10px;
  justify-content: space-between;
  align-items: center;

  :global {
    .adm-stepper {
      background: #f5f6fa;
      border-radius: 10px;
    }

    .adm-stepper-minus,
    .adm-stepper-plus {
      color: #000;
    }
  }

  &:last-child {
    margin-bottom: 20px;
  }
}

.addFile {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  //width: 80px;
  //height: 80px;
  border-radius: 10px;
  background: #f5f6fa;
  cursor: pointer;
}

.footer {
  display: flex;
  text-align: center;
  padding: 24px 16px;

  & .btn,
  .btnDisadled {
    flex: 1;
    margin-right: 15px;

    &:last-child {
      margin-right: 0;
    }
  }

  .btnDisadled {
    color: #888b98;
    margin-right: 15px;
    flex: 1;
    cursor: no-drop;
    background: #f3f3f3;

    &:hover {
      background: #f3f3f3;
      color: #888b98;
    }
  }

  .btnColor {
    background: #c6ccd8;
  }
}

.selectGoods {
  width: 200px;
  text-align: right;
}
