import { checkPermission } from '@/utils/permission';

const orderPermission = (orderType: number, btnName: string) => {
  // 判断是什么单
  const type = (orderId: number) => {
    switch (orderId) {
      case 9:
        return 2;
      case 10:
        return 1;
      case 13:
        return 3;
      case 11:
        return 4;
      case 14:
        return 8;
      default:
        return 0;
    }
  };

  // 分别对应的code
  const btn = (name: string) => {
    switch (name) {
      case '提交':
        return 3;
      case '审批':
        return 4;
      case '批量审批':
        return 4;
      case '取消':
        return 5;
      case '批量取消':
        return 5;
      case '取消审批':
        return 5;
      case '编辑':
        return 1;
      case '删除':
        return 5;
      case '查看':
      case '打印':
        return 2;
      case '导出详情':
        return 6;
      default:
        return 0;
    }
  };

  return checkPermission(`AV_001_00${type(orderType)}_00${btn(btnName)}`);
};

export default orderPermission;
