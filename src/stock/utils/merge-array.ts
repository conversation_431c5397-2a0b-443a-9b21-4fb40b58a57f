import { StockGoodsListResult } from '@/apis';

const mergeArray = (arr: StockGoodsListResult[]) => {
  const resObj: any = {};
  arr.forEach((item: any) => {
    if (resObj[item.supplierCompanyId]) {
      resObj[item.supplierCompanyId].push(item);
    } else {
      resObj[item.supplierCompanyId] = [item];
    }
  });
  const newArr = [];
  // eslint-disable-next-line guard-for-in,no-restricted-syntax
  for (const key in resObj) {
    newArr.push({
      supplierName: resObj[key].length ? resObj[key][0].supplierName : '',
      goodsList: resObj[key],
    });
  }
  return newArr;
};

export default mergeArray;
