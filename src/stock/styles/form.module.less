.form {
  :global {
    .ant-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
        border-bottom: none;
      }
    }

    .ant-form-item-control-input-content {
      border-bottom: 1px solid #f3f3f3;
    }

    .ant-form-item-label {
      color: @text-color;
    }

    .ant-select-selector,
    .ant-input,
    .ant-picker {
      margin-bottom: 4px;
      padding: 0 !important;
    }

    .ant-select-clear {
      right: 1px;
    }

    .ant-picker {
      width: 100%;
    }

    .ant-select-arrow {
      right: 0;
    }

    .ant-input-textarea-show-count.ant-input-textarea-in-form-item::after {
      margin-top: -20px;
    }

    .ant-select-status-error.ant-select:not(.ant-select-disabled, .ant-select-customize-input)
      .ant-select-selector {
      border: none !important;
    }

    .ant-form-item-explain-error {
      padding-left: 12px;
    }
  }

  .borderNo {
    border-bottom: none;
  }

  .textarea {
    height: 84px !important;
    padding: 12px !important;
    border-radius: 12px !important;
    border: 1px solid #f3f3f3 !important;
  }
}
