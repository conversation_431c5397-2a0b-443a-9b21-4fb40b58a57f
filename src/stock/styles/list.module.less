@import 'styles/mixins/mixins';

:global {
  .list {
    font-weight: 500;
  }
}

.item {
  margin-top: 20px;
  padding: 16px 20px;
  border-radius: 10px;

  &:hover {
    background: rgb(217 238 255 / 30%);
  }

  &:active {
    background: rgb(217 238 255 / 50%);
  }
}

.iconColor {
  color: #c6ccd8;
}

.checkboxGroup {
  width: 100%;
}

.checkbox {
  margin-right: 8px;
}

.info {
  display: flex;
  align-items: center;
}

.text,
.handle {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  .text-overflow();
}

.textLeft {
  & div {
    width: 60px;
    text-align: left;
  }
}

.labelText {
  margin-top: 4px;
}

.blueText {
  color: #006eff;
  font-size: 14px;
}

.handle {
  align-items: end;

  .icon {
    color: #000;
    font-size: 20px;
    margin-right: 5px;
    cursor: pointer;
  }

  .upload {
    width: 30px;
    height: 30px;
    margin-bottom: 5px;
  }
}

.name {
  display: inline-block;
  width: 180px;
  .text-overflow();
}

.greyText {
  color: #888b98;
  margin-top: 4px;
}

.fontSize {
  font-size: 25px;
}

// 底部
.batch {
  display: flex;
  align-items: center;
}

.selectNum {
  margin: 0 32px 0 16px;
}

.batchBtn {
  color: #008cff;
  cursor: pointer;
}

.batchBtnInline {
  display: inline-block;
  margin-left: 24px;
}

.batchItem {
  padding: 7px 8px;
  text-align: center;
  border-radius: 10px;
  cursor: pointer;

  &:hover {
    background: rgb(217 238 255 / 30%);
  }
}

.headItem {
  color: #008cff;
  text-align: center;
  cursor: pointer;
}

.warehouseName {
  display: flex;
  align-items: center;
}

.tableItem {
  margin-left: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.blue {
  color: #006eff;
}

.surpass,
.lack {
  display: flex;
  justify-content: center;
  align-items: center;
}

.lack {
  color: #ea1c26;
}

.grayText {
  color: #888b98;
}

.surpass {
  color: #006eff;
}

.numBox {
  display: flex;
  align-items: center;
  justify-content: center;
}

.num {
  display: flex;
  flex-direction: column;
  margin-left: 4px;
}

.numImg {
  width: 9px;
  height: 6px;
  margin-bottom: 2px;
  cursor: pointer;

  &:last-child {
    margin-bottom: 0;
  }
}

.button {
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 1;
  padding: 5px 5px 4px;
  border: 1px solid transparent;
  border-radius: 10px;
  background-color: transparent;
  cursor: pointer;
  vertical-align: top;

  &:hover {
    color: @primary-text-colors[secondary];
    border-color: @white;
    background-color: @white;
  }
}

.stateImg {
  width: 28px;
  height: 18px;
  margin-right: 4px;

  &:last-child {
    margin-right: 0;
  }
}

.categoryItem {
  margin-bottom: 4px;
  text-align: center;
  .text-overflow(1);
}

.categoryItemPopover:last-child .punctuate {
  display: none;
}

.lookMore {
  color: #888b98;
  font-size: 12px;
  cursor: pointer;
}

.replenishQuantity {
  color: #006eff !important;
}

.thresholdStock {
  color: #ea1c26;
  display: flex;
  justify-content: center;
  align-items: center;
}

.stockStatus {
  margin: 0 4px;
}

.standard {
  color: #888b98;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.grey {
  color: #000;
  display: -webkit-box;
  margin-left: 4px;
  padding-right: 24px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.cords {
  min-width: 32px;
}

.defaultImg {
  width: 28px;
  height: 18px;
  margin-left: 4px;
}
