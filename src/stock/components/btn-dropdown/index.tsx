import { ReceiveListResult } from '@/apis';
import { Icon } from '@/components';
import { Dropdown, Menu, Space } from 'antd';
import React, { PropsWithChildren, useMemo } from 'react';
import classNames from 'classnames';
import styles from './index.module.less';

interface MessageProps {
  item: ReceiveListResult;
  // eslint-disable-next-line no-unused-vars
  confirm: (name: string) => void;
}

function BtnDropdown({ item, confirm }: PropsWithChildren<MessageProps>) {
  const menu = useMemo(() => {
    // if (item.orderStatusStr === '已审核') {
    //   return [
    //     {
    //       key: '1',
    //       label: (
    //         <div
    //           role="button"
    //           tabIndex={0}
    //           onClick={() => {
    //             confirm('取消审批');
    //           }}
    //         >
    //           取消审批
    //         </div>
    //       ),
    //     },
    //   ];
    // }
    if (item.orderStatusStr === '待审核') {
      return [
        {
          key: '1',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                confirm('编辑');
              }}
            >
              编辑
            </div>
          ),
        },
        {
          key: '2',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                confirm('审批');
              }}
            >
              审批
            </div>
          ),
        },
        {
          key: '3',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                confirm('删除');
              }}
            >
              删除
            </div>
          ),
        },
      ];
    }
    if (item.orderStatusStr === '草稿') {
      return [
        {
          key: '1',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                confirm('提交');
              }}
            >
              提交
            </div>
          ),
        },
        {
          key: '2',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                confirm('编辑');
              }}
            >
              编辑
            </div>
          ),
        },
        {
          key: '3',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                confirm('审批');
              }}
            >
              审批
            </div>
          ),
        },
        {
          key: '4',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                confirm('删除');
              }}
            >
              删除
            </div>
          ),
        },
      ];
    }
    return undefined;
  }, [item]); // eslint-disable-line

  return (
    <div className={styles.text} role="button" tabIndex={0} onClick={(e) => e.stopPropagation()}>
      {menu ? (
        <Dropdown className={styles.btn} overlay={<Menu items={menu} />} placement="bottom">
          <Space onClick={(e) => e.stopPropagation()} className={styles.handle}>
            <Icon className={styles.icon} name="zu13366" />
          </Space>
        </Dropdown>
      ) : (
        <Icon className={classNames(styles.icon, styles.iconColor)} name="zu13366" />
      )}
    </div>
  );
}

export default BtnDropdown;
