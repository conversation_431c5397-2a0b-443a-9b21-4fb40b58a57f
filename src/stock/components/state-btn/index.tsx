import { Button, message, Modal } from 'antd';
import { PropsWithChildren, useMemo } from 'react';
import orderPermission from '../../utils/order-permission';
import styles from './index.module.less';

interface Props {
  status: number;
  orderType: number;
  orderName?: string;
  isCanEdit?: boolean;
  // eslint-disable-next-line no-unused-vars
  btnChange: (name: string) => void;
}

function StateBtn({
  status,
  btnChange,
  orderName,
  orderType,
  isCanEdit,
}: PropsWithChildren<Props>) {
  const check = (value: string) => {
    if (!orderPermission(orderType, value)) {
      message.error('暂无权限，请联系公司管理员开通');
    } else {
      Modal.confirm({
        title: '提示',
        icon: '',
        centered: true,
        okText: '确认',
        cancelText: '取消',
        content: `是否${value}`,
        onOk: () => {
          btnChange(value);
        },
      });
    }
  };

  const btnS = useMemo(() => {
    let item;
    if (status === 0) {
      item = (
        <>
          {([9, 10].includes(orderType) && isCanEdit) || ![9, 10].includes(orderType) ? (
            <Button
              block
              className="mr-4"
              onClick={() => {
                check('删除');
              }}
            >
              删除
            </Button>
          ) : null}
          <Button
            block
            className="ml-4"
            type="primary"
            onClick={() => {
              check('审批');
            }}
          >
            审批
          </Button>
        </>
      );
    } else if (status === 1 && ![9, 10].includes(orderType)) {
      item = (
        <Button
          block
          type="primary"
          onClick={() => {
            check('取消审批');
          }}
        >
          取消审批
        </Button>
      );
    } else if (status === 2) {
      item = (
        <>
          <Button
            block
            className="mr-4"
            onClick={() => {
              check('提交');
            }}
          >
            提交
          </Button>
          <Button
            block
            type="primary"
            className="ml-4"
            onClick={() => {
              check('审批');
            }}
          >
            审批
          </Button>
        </>
      );
    }
    if (!orderName && status === 0) {
      item = (
        <Button
          block
          type="primary"
          onClick={() => {
            check('审批');
          }}
        >
          审批
        </Button>
      );
    }
    if (orderName === '调拨入库单' && status === 0) {
      item = (
        <Button
          block
          type="primary"
          onClick={() => {
            check('审批');
          }}
        >
          审批
        </Button>
      );
    }
    if (
      orderName === '销售出库单' ||
      orderName === '采购入库单' ||
      orderName === '调拨出库单' ||
      orderName === '退货入库单' ||
      orderName === '调拨入库单'
    ) {
      if (status !== 1) {
        item = (
          <Button
            block
            type="primary"
            onClick={() => {
              check('审批');
            }}
          >
            审批
          </Button>
        );
      }
    }

    return item;
  }, [status, orderName, orderType, isCanEdit]); // eslint-disable-line
  return <div className={styles.footer}>{btnS}</div>;
}
StateBtn.defaultProps = {
  orderName: '',
  isCanEdit: false,
};
export default StateBtn;
