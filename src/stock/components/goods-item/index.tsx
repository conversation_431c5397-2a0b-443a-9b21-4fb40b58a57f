import { PropsWithChildren, useMemo } from 'react';
import { StandardItem } from '@/apis';
import classNames from 'classnames';
import { Tooltip } from 'antd';
import styles from './index.module.less';

interface Info {
  img?: string;
  name?: string;
  standard?: StandardItem[];
  unit?: string;
  barsCode?: string;
  stock?: number | string;
  onTheWaySkuNumber: string;
  showStock?: boolean;
  skuBelongType: number;
  skuNumber?: boolean;
}

function GoodsItem({
  img,
  name,
  standard,
  unit,
  showStock,
  stock,
  onTheWaySkuNumber,
  skuBelongType,
  barsCode,
  skuNumber,
}: PropsWithChildren<Info>) {
  const standardVal = useMemo(() => {
    let str = '';
    standard?.forEach((item) => {
      str += `${item.name}: ${item.value},`;
    });
    return str;
  }, [standard]);
  return (
    <div className={styles.goodsInfo}>
      <img
        className={styles.goodsImg}
        src={img || 'https://img.huahuabiz.com/default/image/default_holder.png'}
        alt=""
      />
      <div className={styles.goodsText}>
        <div>
          <div className={styles.name} title={name}>
            {skuBelongType === 1 && (
              <img
                className={styles.nameTag}
                src="https://img.huahuabiz.com/user_files/2023625/1687686014486265.png"
                alt=""
              />
            )}
            <span>{name}</span>
          </div>
          <div className={styles.label}>
            <Tooltip placement="top" title={standardVal}>
              <div className={styles.standard}>
                {standard
                  ? standard.map((item) => (
                      <span key={`${item.value}${item.name}`} className={styles.standardName}>
                        {item.name}: {item.value}
                        <span className={styles.tip}>，</span>
                      </span>
                    ))
                  : null}
              </div>
            </Tooltip>
          </div>
        </div>
        <div>
          {unit ? (
            <div>
              <span>单位: {unit}</span>
            </div>
          ) : null}
          <div>
            <span className={styles.label}>条形码: {barsCode || '--'}</span>
          </div>
          {showStock ? (
            <div>
              <div className={classNames(styles.label, { 'mb-1': skuNumber })}>库存 {stock}</div>
              {skuNumber && <div className={styles.label}>在途库存 {onTheWaySkuNumber}</div>}
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
}

GoodsItem.defaultProps = {
  img: '',
  name: '',
  standard: [],
  unit: '',
  barsCode: '',
  stock: 0,
  showStock: false,
  saleGroup: 1,
  skuNumber: true,
};

export default GoodsItem;
