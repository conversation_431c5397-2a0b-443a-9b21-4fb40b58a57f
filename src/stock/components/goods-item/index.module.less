@import 'styles/mixins/mixins';

.goodsInfo {
  display: flex;
}

.goodsImg {
  width: 64px;
  height: 64px;
  margin-right: 8px;
  border-radius: 12px;
}

.goodsText {
  display: flex;
  margin-right: 16px;
  justify-content: space-between;
  flex-direction: column;

  & .name {
    width: 143px;
    margin-bottom: 4px;
    .text-overflow(2);
  }

  & .label {
    color: #888b98;
    font-size: 12px;
    display: inline-block;
    width: 143px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.nameTag {
  width: 28px;
  height: 18px;
  margin-right: 4px;
  vertical-align: middle;
}

.labelText {
  margin-top: 2px;
}

.standard {
  width: 143px;
  .text-overflow();
}

.standardName:last-child .tip {
  display: none;
}
