import { Modal } from 'antd';
import { PropsWithChildren } from 'react';
import styles from './index.module.less';

interface popupProps {
  visible: boolean;
  content?: string;
  onDetermine: () => void;
  onclose: () => void;
}

function PopupModal({ visible, ...props }: PropsWithChildren<popupProps>) {
  return (
    <Modal
      title="提示"
      visible={visible}
      width="311px"
      className={styles.modal}
      centered
      onOk={props.onDetermine}
      okText="继续编辑"
      cancelText="离开"
      onCancel={props.onclose}
      getContainer={false}
    >
      <div>{!props.content ? '您编辑的内容尚未保存，确定要离开吗？' : props.content}</div>
    </Modal>
  );
}

PopupModal.defaultProps = {
  content: '',
};

export default PopupModal;
