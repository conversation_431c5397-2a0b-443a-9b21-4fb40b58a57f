import { useState, useImperativeHandle, forwardRef, useRef } from 'react';
import { useReactToPrint } from 'react-to-print';
import { message } from 'antd';
import dayjs from 'dayjs';
import styles from './index.module.less';

export interface PrintPageEv {
  handlePrint: () => void;
}
const PrintPage = forwardRef<PrintPageEv>((props, ref) => {
  const [printData, setPrintData] = useState({
    businessName: '',
    customerCompanyId: 0,
    customerCompanyName: '',
    customerContact: '',
    customerId: 0,
    customerName: '',
    customerPhone: '',
    orderNo: '',
    operator: '',
    orderStatusStr: '',
    warehouseName: '',
    createMemberName: '',
    recipient: '',
    orderDate: '',
    list: [],
    remark: '',
    orderType: 0,
  });
  const componentRef = useRef(null);
  const printOptionEv = useReactToPrint({
    content: () => componentRef.current,
    pageStyle: `@page {size: auto,margin: 3mm;}`,
    copyStyles: false,
  });
  const handlePrint = () => {
    const storageData = sessionStorage.getItem('orderPrintData') || '{}';
    if (storageData) {
      const storageDataObj = JSON.parse(storageData);
      let listKey = '';
      switch (storageDataObj.orderType) {
        case 9:
          listKey = 'platformInboundSkuVOS';
          break;
        case 10:
          listKey = 'platformOutboundSkuVOS';
          break;
        case 11:
          listKey = 'inventorySkuVOS';
          break;
        case 13:
          listKey = 'platformAllotSkuVOS';
          break;
        case 14:
          listKey = 'receiveSkuVOList';
          break;
        default:
          break;
      }
      if (listKey) {
        setPrintData({
          ...storageDataObj,
          list: storageDataObj[listKey].map((item: any) => ({
            ...item,
            standardJson: JSON.parse(item.standardJson),
          })),
        });
        setTimeout(() => {
          printOptionEv();
        }, 1000);
      } else {
        message.success('数据类型错误，请联系管理员！');
      }
    }
  };
  const typeOrder = (type: string) => {
    const typeData = [
      ['领用单', '退货入库单', '采购入库单', '调拨入库单', '其他入库单', '生产入库单'],
      ['销售出库单', '调拨出库单', '其他出库单'],
    ];
    if (typeData[0].includes(type)) {
      return 1;
    }
    return 0;
  };
  useImperativeHandle(ref, () => ({
    handlePrint,
  }));
  return (
    <div>
      {/* <ReactToPrint
        trigger={() => <button>打印</button>}
        content={() => componentRef.current}
        pageStyle={`@page {padding-top:10px}`} //设置打印样式
        copyStyles={false}
        //这里是第一处设置：打印未显示元素的关键，默认情况copyStyles是为true的，
        //打印未显示的元素时，我们需要把它设置为false，这样打印出来的页面才不会是空白页。
      /> */}
      <div ref={componentRef} className={styles.printContent}>
        {/* 这里是你要打印的内容{printData} */}
        <h3
          style={{
            fontSize: '30px',
            textAlign: 'center',
            fontWeight: 'bold',
            marginBottom: '14px',
            letterSpacing: '16px',
          }}
        >
          {printData.orderType === 14 ? '领用出库单' : printData.businessName}
        </h3>
        {printData.customerCompanyId ? (
          <div style={{ display: 'flex', marginBottom: '4px' }}>
            <span style={{ width: '40%' }}>
              {typeOrder(printData.businessName) ? '供应商' : '客户'}名称：
              {printData.customerName}
            </span>
            <span style={{ width: '20%' }}>联系人：{printData.customerContact}</span>
            <span style={{ width: '40%' }}>联系电话：{printData.customerPhone}</span>
          </div>
        ) : null}
        <div style={{ display: 'flex', marginBottom: '4px' }}>
          <span style={{ width: '40%' }}>单据编号：{printData.orderNo}</span>
          <span style={{ width: '20%' }}>
            操作人：{printData.orderType === 14 ? printData.operator : printData.createMemberName}
          </span>
          <span style={{ width: '40%' }}>单据状态：{printData.orderStatusStr}</span>
        </div>
        <table
          style={{
            width: '100%',
            textAlign: 'center',
            border: 'solid 1px #bdbdbd',
            borderCollapse: 'collapse',
          }}
        >
          <thead
            style={{
              height: '40px',
              background: `${typeOrder(printData.businessName) ? '#5b9bd5' : '#ed7d31'}`,
              color: 'white',
              printColorAdjust: 'exact',
              WebkitPrintColorAdjust: 'exact',
            }}
          >
            <tr>
              <th style={{ borderRight: 'solid 1px #e7e7e7', padding: '8px' }}>序号</th>
              <th style={{ width: '30%', borderRight: 'solid 1px #e7e7e7' }}>产品名称</th>
              <th style={{ width: '15%', borderRight: 'solid 1px #e7e7e7' }}>规格型号</th>
              <th style={{ borderRight: 'solid 1px #e7e7e7' }}>单位</th>
              <th style={{ borderRight: 'solid 1px #e7e7e7' }}>数量</th>
              <th style={{ width: '35%' }}>备注</th>
            </tr>
          </thead>
          <tbody style={{ fontSize: '14px' }}>
            {printData.list.map((item: any, i: number) => (
              <tr key={item.shopSkuId}>
                <td style={{ border: 'solid 1px #bdbdbd', height: '20px' }}>{i + 1}</td>
                <td style={{ border: 'solid 1px #bdbdbd' }}>{item.skuName}</td>
                <td style={{ border: 'solid 1px #bdbdbd', fontSize: '12px' }}>
                  {item.standardJson.map((stand: any) => (
                    <div>
                      {stand.name}:{stand.value}
                    </div>
                  ))}
                </td>
                <td style={{ border: 'solid 1px #bdbdbd' }}>{item.auxiliaryUnit || item.unit}</td>
                <td style={{ border: 'solid 1px #bdbdbd' }}>{item.quantity}</td>
                <td style={{ border: 'solid 1px #bdbdbd' }}>
                  <div
                    style={{
                      width: '200px',
                      overflow: 'hidden',
                      whiteSpace: 'nowrap',
                      textOverflow: 'ellipsis',
                    }}
                  >
                    {printData.remark}
                  </div>
                </td>
              </tr>
            ))}
            <tr>
              <td style={{ border: 'solid 1px #bdbdbd', height: '20px' }} />
              <td style={{ border: 'solid 1px #bdbdbd' }} />
              <td style={{ border: 'solid 1px #bdbdbd' }} />
              <td style={{ border: 'solid 1px #bdbdbd' }} />
              <td style={{ border: 'solid 1px #bdbdbd' }} />
              <td style={{ border: 'solid 1px #bdbdbd' }} />
            </tr>
          </tbody>
        </table>
        <div style={{ display: 'flex', marginTop: '4px' }}>
          <span style={{ width: '40%' }}>
            {typeOrder(printData.businessName) ? '入货' : '出货'}仓库：{printData.warehouseName}
          </span>
          <span style={{ width: '20%' }}>
            业务员：{printData.orderType === 14 ? printData.recipient : printData.operator}
          </span>
          <span style={{ width: '40%' }}>
            {typeOrder(printData.businessName) ? '入库' : '出库'}日期：
            {dayjs(printData.orderDate || new Date()).format('YYYY-MM-DD')}
          </span>
        </div>
      </div>
    </div>
  );
});
export default PrintPage;
