import { useMemo } from 'react';
import { StandardItem } from '@/apis';
import { isArray, isString } from 'lodash';
import styles from './index.module.less';

interface GoodsItemStandardProps {
  standard: StandardItem[] | string;
}

function GoodsItemStandard({ standard }: GoodsItemStandardProps) {
  const standardTitle = useMemo(() => {
    let str = '';
    if (isString(standard)) {
      str = standard;
    } else {
      standard?.forEach((item, index) => {
        str += `${item.name}: ${item.value}${index === standard.length - 1 ? '' : ','}`;
      });
    }
    return str;
  }, [standard]);

  return (
    <div className={styles.standard} title={standardTitle}>
      {isArray(standard)
        ? standard.map((item, index) => (
            <span className={styles.standardItem} key={`${index + 1}`}>
              {item.name}: {item.value}
              <span className={styles.standardTag}>，</span>
            </span>
          ))
        : standard}
    </div>
  );
}

export default GoodsItemStandard;
