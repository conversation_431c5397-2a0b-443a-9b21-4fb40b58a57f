import { ReceiveListResult } from '@/apis';
import { PropsWithChildren } from 'react';
import getStateTag from '@@/stock/utils/get-state-tag';
import styles from './index.module.less';

interface MessageProps {
  item: ReceiveListResult;
}

function OrderMessage({ item }: PropsWithChildren<MessageProps>) {
  return (
    <div className={styles.receive}>
      <img className={styles.orderInfoImg} src={getStateTag(item.orderStatus)} alt="" />
      <span className={styles.name} title={item.orderNo}>
        {item.orderNo}
      </span>
    </div>
  );
}

export default OrderMessage;
