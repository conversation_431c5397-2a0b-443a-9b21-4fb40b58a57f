@import 'styles/mixins/mixins';

.box {
  margin-bottom: 20px;
  padding: 0 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  overflow: hidden;
}

.lookDetail {
  display: flex;
  padding: 16px 0;
  justify-content: space-between;
}

.lookDetailBtn {
  color: #008cff;
  cursor: pointer;
}

.goodsItemBox {
  border-bottom: 1px solid #f3f3f3;

  &:last-child {
    border-bottom: 0;
  }
}
