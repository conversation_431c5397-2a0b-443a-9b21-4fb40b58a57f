import { useEffect, useState } from 'react';
import { GoodsListResult } from '@/apis';
import { Icon } from '@/components';
import GoodsItem from './goods-item';
import SetupGoodsInfo from './setup-goods-info';
import styles from './index.module.less';

interface SelectGoodsListProps {
  warehouseNo: string;
  isBatch: boolean;
  orderType: number;
  selectGoodsList: GoodsListResult[];
  // eslint-disable-next-line no-unused-vars
  onDelete: (id: number) => void;
  // eslint-disable-next-line no-unused-vars
  onMore: (id: number) => void;
  // eslint-disable-next-line no-unused-vars
  onAddGoods: (arr: GoodsListResult[]) => void;
  // eslint-disable-next-line no-unused-vars
  onConfirm: (list: GoodsListResult[]) => void;
  isDisableOrder?: boolean;
  isEdit: boolean;
  orderNo?: string;
  businessName?: string;
}

function SelectGoodsList({
  warehouseNo,
  isBatch,
  orderType,
  selectGoodsList,
  onDelete,
  onMore,
  onAddGoods,
  onConfirm,
  isDisableOrder,
  isEdit,
  orderNo,
  businessName,
}: SelectGoodsListProps) {
  const [list, setList] = useState<GoodsListResult[]>([]);
  const [showSelectGoodsList, setShowSelectGoodsList] = useState(false);

  useEffect(() => {
    setList(JSON.parse(JSON.stringify(selectGoodsList)));
  }, [selectGoodsList]);

  return (
    <div className={styles.box}>
      {list
        .filter((item, index) => index < 2)
        .map((item) => (
          <div
            role="button"
            tabIndex={0}
            key={item.id}
            onClick={() => setShowSelectGoodsList(true)}
            className={styles.goodsItemBox}
          >
            <GoodsItem
              orderType={orderType}
              info={item}
              onDelete={onDelete}
              isBatch={isBatch}
              onMore={onMore}
              isDisableOrder={isDisableOrder || false}
            />
          </div>
        ))}
      <div className={styles.lookDetail}>
        <span>{`已设置${list.length}种商品的${isBatch ? '批次和' : ''}数量`}</span>
        <span
          className={styles.lookDetailBtn}
          role="button"
          tabIndex={0}
          onClick={() => setShowSelectGoodsList(true)}
        >
          查看详情
          <Icon name="right" />
        </span>
      </div>
      <SetupGoodsInfo
        warehouseNo={warehouseNo}
        visible={showSelectGoodsList}
        isBatch={isBatch}
        orderType={orderType}
        selectGoodsList={selectGoodsList}
        isDisableOrder={isDisableOrder}
        onCancel={() => setShowSelectGoodsList(false)}
        onAddGoods={(arr) => {
          onAddGoods(arr);
          setShowSelectGoodsList(false);
        }}
        onConfirm={onConfirm}
        isEdit={isEdit}
        orderNo={orderNo || ''}
        businessName={businessName || ''}
      />
    </div>
  );
}

SelectGoodsList.defaultProps = {
  isDisableOrder: false,
  currentUnit: '',
  orderNo: '',
  businessName: '',
};

export default SelectGoodsList;
