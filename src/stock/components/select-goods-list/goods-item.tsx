import { Icon } from '@/components';
import { GoodsListResult } from '@/apis';
import { StandardItem } from '@/apis/mcs/goods-list';
import styles from './goods-item.module.less';

interface GoodsItemProps {
  info: GoodsListResult;
  isBatch: boolean;
  orderType: number;
  // eslint-disable-next-line no-unused-vars
  onDelete: (id: number) => void;
  // eslint-disable-next-line no-unused-vars
  onMore: (id: number) => void;
  isDisableOrder: boolean;
}

function GoodsItem({ info, isBatch, orderType, onDelete, onMore, isDisableOrder }: GoodsItemProps) {
  const metaTitle = (list: StandardItem[]) => {
    let str = '';
    list?.forEach((item, index) => {
      str += `${item.name}: ${item.value}${index === list.length - 1 ? '' : ','}`;
    });
    return str;
  };

  return (
    <div className={styles.goodsItem}>
      <div className={styles.goodsItemInfo}>
        <img
          className={styles.goodsImg}
          src={
            info.images ||
            (info.imagesList && info.imagesList.length && info.imagesList[0]) ||
            'https://img.huahuabiz.com/default/image/default_holder.png'
          }
          alt=""
        />
        <div className={styles.goodsInfo}>
          <div className={styles.goodCard}>
            <div className={styles.goodsName} title={info.name}>
              {info.name}
            </div>
            <div>
              <div className={styles.meta} title={metaTitle(info.standardList)}>
                规格：
                {info.standardList.map((each, index) => (
                  <span className={styles.metaItem} key={`${index + 1}`}>
                    {each.name}: {each.value}
                    <span className={styles.metaTag}>;</span>
                  </span>
                ))}
              </div>
              <div className={styles.meta}>条形码：{info?.barsCode || '--'}</div>
            </div>
          </div>
          {/* <div className={styles.saleGroup}>库存: {info.stock}</div> */}
        </div>
      </div>
      {!isDisableOrder && (
        <Icon
          name="close-circle2"
          size={16}
          className={styles.delete}
          onClick={(e) => {
            e.stopPropagation();
            onDelete(info.id || info.shopSkuId);
          }}
        />
      )}
      {info.batchList && info.batchList.length > 0 && (
        <>
          <div className={styles.batchList}>
            <div className={styles.batchTitle}>
              <span className={styles.unit}>单位</span>
              {isBatch && <span className={styles.batch}>批次号</span>}
              <span className={styles.number}>数量1</span>
            </div>
            {info.batchList
              .filter((item, index) => (info.isMore ? item : index < 2))
              .map((item, index) => (
                <div className={styles.batchItem} key={`${index + 1}`}>
                  <span className={styles.unit}>
                    {item.auxiliaryUnit || item.unit || info.skuRelationParamList[index].unitName}
                  </span>
                  {isBatch && <span className={styles.batch}>{item.batch}</span>}
                  <span className={styles.number}>{item.number}</span>
                </div>
              ))}
          </div>
          {info.batchList.length > 2 &&
            (!info.isMore ? (
              <div
                role="button"
                tabIndex={0}
                className={styles.more}
                onClick={(e) => {
                  e.stopPropagation();
                  onMore(info.id);
                }}
              >
                <span>
                  还有{info.batchList.length - 2}个
                  {`${[9, 11].includes(orderType) ? '信息' : '批次'} `}
                </span>
                <Icon name="down" />
              </div>
            ) : (
              <div
                role="button"
                tabIndex={0}
                className={styles.more}
                onClick={(e) => {
                  e.stopPropagation();
                  onMore(info.id);
                }}
              >
                <span>收起</span>
                <Icon name="down" />
              </div>
            ))}
        </>
      )}
    </div>
  );
}
export default GoodsItem;
