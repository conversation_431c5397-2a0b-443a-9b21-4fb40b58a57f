@import 'styles/mixins/mixins';

:global {
  .ant-modal-confirm .ant-modal-body {
    padding: 20px;
  }
}

.modal {
  :global {
    .ant-modal-header {
      border-bottom: 0;
    }

    .ant-modal-body {
      padding: 0 20px;
    }

    .ant-modal-footer {
      display: flex;
      padding: 8px 20px 20px;
      justify-content: space-between;
      border-top: 0;
    }
  }
}

.requiry {
  color: #ea1c26;
}

.title {
  display: flex;
  padding: 10px 0;
  background: #f5f6fa;
  border-radius: 10px;
}

.goodsName,
.goodsInfo {
  display: flex;
  align-items: center;
  // width: 216px;
  flex: 1;
  padding-left: 20px;
}

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.batchInfo {
  width: 143px;
  margin: 0 8px;
  .center();

  :global {
    .ant-select {
      width: 150px;
    }

    .ant-select-selection-item {
      overflow: hidden;
      text-overflow: inherit;
      white-space: inherit;
    }

    .ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
      background-color: #f3f3f3;
      color: #040919;
    }

    .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
      background: #f4faff;
    }
  }
}

.batchList {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  flex: 1;
}

.batchItem {
  display: flex;
  width: 100%;
  margin-bottom: 16px;
  position: relative;

  &:last-child {
    margin-bottom: 0;
  }
}

.error {
  color: #ea1c26;
  font-size: 12px;
  position: absolute;
  top: 42px;
  left: 264px;
}

.unitInfo {
  width: 130px !important;
  margin: 0 8px;
  .center();

  :global {
    .ant-select {
      width: 104px;
    }

    .ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector {
      background-color: #fff;
      color: #040919;
    }
  }
}

.unitInfoBatch {
  // flex: 1;
  width: auto;
}

.numInfo {
  // flex: 1;
  width: 102px;
  margin: 0 8px;
  .center();

  :global {
    .ant-input {
      width: 102px;
    }
  }

  input::-webkit-outer-spin-button,
  input::-webkit-inner-spin-button {
    appearance: none !important;
  }

  input[type='number'] {
    appearance: textfield;
  }
}

.batchItemNo {
  :global {
    .ant-select {
      width: 130px;
    }

    .ant-input {
      width: 102px;
    }
  }
}

.inventory {
  width: 128px;
  text-align: center;
}

.label {
  color: #888b98;
  font-size: 14px;
  margin: 2px 0;
  text-align: center;
}

.batchItemInfo {
  color: #888b98;
  font-size: 14px;
  font-weight: 400;
  // margin-left: 4px;
  margin-top: 3px;
}

.stockSku {
  width: 200px;
  padding-right: 20px;
  position: relative;
  // padding: 8px;
  border-radius: 10px;
}

.stockBatch {
  color: #040919;
}

.stockSkuFoo {
  background: #f4faff;
}

.stockImg {
  position: absolute;
  top: 18px;
  right: 0;
}

.expireJson {
  margin-left: 12px;
}

.handle {
  width: 84px;
  .center();
}

.list {
  height: 450px;
  padding-bottom: 44px;
  overflow: auto;
}

.noData {
  padding: 48px;
}

.item {
  display: flex;
  padding: 12px 0;
  border-bottom: 1px solid #f3f3f3;

  &:last-child {
    border-bottom: 0;
  }
}

.meta {
  color: #888b98;
  font-size: 12px;
  width: 100px;
  .text-overflow();
}

.metaItem:last-child .metaTag {
  display: none;
}

.goodsInfo {
  display: flex;

  & .img {
    width: 64px;
    height: 64px;
    border-radius: 6px;
    margin-right: 12px;
  }

  & .name {
    width: 130px;
    margin-bottom: 8px;
    .text-overflow(2);
  }

  & .meta {
    color: #888b98;
    font-size: 12px;
    width: 130px;
    margin-bottom: 8px;
  }

  & .saleGroup {
    color: #888b98;
    font-size: 12px;
    width: 130px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.btnDel {
  color: #ea1c26;
  font-size: 12px;
  cursor: pointer;
}

.btnDelFoo {
  color: #888b98;
  cursor: not-allowed;
}

.btnAdd {
  color: #008cff;
  font-size: 12px;
  margin-left: 9px;
  cursor: pointer;
}

.addGoods {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.stockTip {
  color: #f9ae08;
  font-size: 12px;
  display: flex;
  width: 100%;
  height: 44px;
  padding: 0 20px;
  justify-content: space-between;
  position: absolute;
  right: 0;
  bottom: 56px;
  align-items: center;
  background-color: #fef3da;

  span {
    font-size: 14px;
    vertical-align: bottom;
  }
}

.stockTipImg {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.stockTipDelete {
  color: #999eb2;
  font-size: 22px;
  margin-left: 12px;
  cursor: pointer;
}

.goodsUnit {
  line-height: 32px;
}
