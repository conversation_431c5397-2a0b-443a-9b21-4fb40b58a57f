@import 'styles/mixins/mixins';

.goodsItem {
  position: relative;
}

.delete {
  color: rgb(0 0 0 / 30%);
  position: absolute;
  top: 8px;
  right: -12px;
  cursor: pointer;
}

.goodsItemInfo {
  display: flex;
  padding: 20px 0;
}

.goodsInfo {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.goodsImg {
  width: 80px;
  height: 80px;
  margin-right: 16px;
  border-radius: 8px;
}

.goodsName {
  width: 200px;
  margin-bottom: 8px;
  .text-overflow(2);
}

.meta {
  color: #888b98;
  font-size: 12px;
  width: 200px;
  margin-bottom: 4px;
  .text-overflow();
}

.meta:last-child {
  margin-bottom: 0;
}

.metaItem:last-child .metaTag {
  display: none;
}

.saleGroup {
  color: #888b98;
  font-size: 12px;
}

.batchTitle {
  display: flex;
  margin-bottom: 16px;
  padding: 10px 0;
  border-radius: 8px;
  background-color: #f5f6fa;

  .batch {
    width: 160px;
    // padding-left: 20px;
    text-align: center;
    position: relative;

    &::after {
      content: '';
      width: 1px;
      height: 14px;
      position: absolute;
      top: 3px;
      right: 0;
      background-color: #d8d8d8;
    }
  }

  & .unit {
    position: relative;
    flex: 1;
    text-align: center;

    &::after {
      content: '';
      width: 1px;
      height: 14px;
      position: absolute;
      top: 3px;
      right: 0;
      background-color: #d8d8d8;
    }
  }

  .number {
    flex: 1;
    text-align: center;
  }
}

.batchItem {
  display: flex;
  margin-bottom: 16px;

  .batch {
    width: 160px;
    // padding-left: 20px;
    text-align: center;
  }

  & .unit {
    flex: 1;
    text-align: center;
  }

  .number {
    flex: 1;
    text-align: center;
  }
}

.more {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 16px;
  text-align: center;
  cursor: pointer;
}

.goodCard {
  display: flex;
  height: 100%;
  justify-content: space-between;
  flex-direction: column;
}
