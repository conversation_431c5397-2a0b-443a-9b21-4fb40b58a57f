import { useRef, useState, useEffect, useMemo } from 'react';
import { useRequest } from 'ahooks';
import { Modal, Select, Input, message, Button } from 'antd';
import { Empty, Icon } from '@/components';
import {
  GoodsListResult,
  getFormulaConvertAllUnit,
  FormulaConvertManyconvertResult,
  StandardItem,
  stockBatchQuery,
  StockBatchQueryResult,
  BatchListResult,
  getUnitListBySkuId,
  postBaseBatchUnitStock,
  // goodsUnitConvert,
} from '@/apis';
import { batchUnitResult } from '@/apis/psi/post-base-batch-unit-stock';
import { user } from '@/store';
import classNames from 'classnames';
// import BigNumber from 'big.js';
import { checkPermission } from '@/utils/permission';
import styles from './setup-goods-info.module.less';

interface SetupGoodsInfoProps {
  visible: boolean;
  warehouseNo: string;
  orderType: number;
  isBatch: boolean;
  onCancel: () => void;
  // eslint-disable-next-line no-unused-vars
  onAddGoods: (arr: GoodsListResult[]) => void;
  selectGoodsList: GoodsListResult[];
  // eslint-disable-next-line no-unused-vars
  onConfirm: (list: GoodsListResult[]) => void;
  isDisableOrder?: boolean;
  isEdit: boolean;
  orderNo: string;
  skuNumber?: boolean;
  businessName?: string;
}

function SetupGoodsInfo({
  visible,
  warehouseNo,
  orderType,
  isBatch,
  onCancel,
  onAddGoods,
  onConfirm,
  selectGoodsList,
  isDisableOrder,
  isEdit,
  orderNo,
  skuNumber,
  businessName = '',
}: SetupGoodsInfoProps) {
  const [list, setList] = useState<GoodsListResult[]>([]);
  const [unitAll, setUnitAll] = useState<batchUnitResult[]>([]);
  const initList = useRef<GoodsListResult[]>([]);
  const batchParams = useRef({
    companyId: user.companyId,
    warehouseNo,
    skuId: 0,
  });
  const [batchList, setBatchList] = useState<StockBatchQueryResult[]>([]);
  const batchListEl = useRef(null as unknown as HTMLDivElement);
  const unitParams = useRef({
    templateId: 0,
    mainUnitName: '',
  });
  const [unitList, setUnitList] = useState<FormulaConvertManyconvertResult[]>([]);
  const currentUnit = useRef('');
  const [isCheck, setIsCheck] = useState(false);
  const [showStockTip, setShowStockTip] = useState(true);

  const metaTitle = (arr: StandardItem[]) => {
    let str = '';
    arr?.forEach((item, index) => {
      str += `${item.name}: ${item.value}${index === list.length - 1 ? '' : ','}`;
    });
    return str;
  };

  const { run: runBatch } = useRequest(stockBatchQuery, {
    manual: true,
    defaultParams: [{ ...batchParams.current }],
    onSuccess: (res) => {
      setBatchList(res.list);
    },
  });

  const { run: runUnit } = useRequest(getFormulaConvertAllUnit, {
    manual: true,
    defaultParams: [{ ...unitParams.current }],
    onSuccess: (res) => {
      setUnitList(res.list);
    },
  });

  const isRepeat = (arr: string[]) => {
    let flag = false;
    for (let i = 0; i < arr.length; i += 1) {
      if (arr.indexOf(arr[i]) !== i) {
        flag = true;
      }
    }
    return flag;
  };

  /**
   * @description 判断是否出现重复单位
   */
  const isRepeatUnit = (id: number, index: number, val: string) => {
    const arr = list.filter((item) => item.id === id);
    arr.forEach((item) => {
      const items = item;
      item.batchList.forEach((each, idx) => {
        const eachs = each;
        if (index === idx) {
          eachs.unitStr = val || '';
        }
      });
      items.batchListStr = item.batchList
        .filter(
          (batch) =>
            batch.batch &&
            (batch.unitStr?.length || batch.auxiliaryUnit?.length || batch.unit?.length)
        )
        .map((each) => each.batch + (each.unitStr || each.auxiliaryUnit || each.unit));
    });
    if (arr.some((item) => isRepeat(item.batchListStr || []) && item.batchListStr?.length)) {
      message.warning(
        [9, 11].includes(orderType) ? '不允许添加相同单位选项' : `不允许同批次和同单位选项`
      );
    }
    return arr.some((item) => isRepeat(item.batchListStr || []) && item.batchListStr?.length);
  };

  /**
   * @description 判断是否出现重复批次
   */
  const isRepeatBatch = (id: number, index: number, val: string) => {
    const arr = list.filter((item) => item.id === id);
    list.forEach((item) => {
      const items = item;
      item.batchList.forEach((each, idx) => {
        const eachs = each;
        if (index === idx) {
          eachs.batchStr = val || '';
        }
      });
      items.batchListStr = item.batchList
        .filter(
          (batch) =>
            (batch.batchStr || batch.batch) && (batch.auxiliaryUnit?.length || batch.unit?.length)
        )
        .map((each) => (each.batchStr || each.batch) + (each.auxiliaryUnit || each.unit));
    });
    if (arr.some((item) => isRepeat(item.batchListStr || []) && item.batchListStr?.length)) {
      message.warning(
        [9, 11].includes(orderType) ? '不允许添加相同单位选项' : `不允许同批次和同单位选项`
      );
    }
    return arr.some((item) => isRepeat(item.batchListStr || []) && item.batchListStr?.length);
  };

  const onChangeBatch = (
    id: number,
    index: number,
    val: string,
    batchInfo: BatchListResult
    // chooseItem: StockBatchQueryResult[]
  ) => {
    if (batchInfo.auxiliaryUnit || batchInfo.unit) {
      if (isRepeatBatch(id, index, val)) {
        return;
      }
    }
    // const itemFoo = chooseItem.find((f) => f.skuBatchNo === val);
    list.forEach((item) => {
      if (item.id === id) {
        item.batchList.forEach((each, idx) => {
          const eachs: any = each;
          if (index === idx) {
            eachs.batch = val;
            // eachs.stock = itemFoo?.stock || 0;
            // eachs.onTheWaySkuNumber = itemFoo?.onTheWaySkuNumber || 0;
          }
        });
      }
    });

    setList(JSON.parse(JSON.stringify(list)));
  };

  const onChangeUnit = (
    id: number,
    index: number,
    val: string,
    saleGroup: number,
    batchInfo: BatchListResult,
    chooseItem: FormulaConvertManyconvertResult[]
  ) => {
    if (batchInfo.batch && isBatch && isRepeatUnit(id, index, val)) {
      return;
    }
    // const saleGroupNum = 1;
    // let newScale = 1;
    // let scaleNum = 1;
    // unitList.forEach((item) => {
    //   if (item.unitName === val) {
    //     newScale = item.scale;
    //     saleGroupNum = BigNumber(saleGroup).div(item.scale).toNumber();
    //   }
    // });
    // if (batchInfo.unit) {
    //   unitList.forEach((item) => {
    //     if (item.unitName === batchInfo.unit && batchInfo.unit) {
    //       scaleNum = BigNumber(item.scale).div(newScale).toNumber();
    //     }
    //   });
    // }
    const itemFoo = chooseItem.find((f) => f.unitName === val);
    list.forEach((item) => {
      if (item.id === id) {
        item.batchList.forEach((each, idx) => {
          const eachs: any = each;
          if (index === idx) {
            eachs.unit = val;
            eachs.auxiliaryUnit = val;
            // eachs.saleGroup = saleGroupNum;
            if (orderType === 11) {
              eachs.stock = itemFoo?.scale || '';
            }
            if ([10, 13, 14].includes(orderType)) {
              if (eachs.number) {
                // eachs.number = BigNumber(Number(each.number)).times(Number(scaleNum)).toNumber();
                eachs.number = '';
              }
              if (eachs.batch) {
                eachs.batch = '';
              }
            }
          }
        });
      }
    });
    setList(JSON.parse(JSON.stringify(list)));
  };

  // const onFocusNum = (
  //   id: number,
  //   index: number,
  //   goodsItem: BatchListResult,
  //   itemVal: GoodsListResult
  // ) => {
  //   if (isEdit && !goodsItem.saleGroup) {
  //     if (goodsItem.unit === itemVal.unit) {
  //       list.forEach((item) => {
  //         if (item.id === id) {
  //           item.batchList.forEach((each, idx) => {
  //             const eachs: any = each;
  //             if (index === idx) {
  //               eachs.saleGroup = itemVal.saleGroup;
  //             }
  //           });
  //         }
  //       });
  //       setList(JSON.parse(JSON.stringify(list)));
  //     } else {
  //       goodsUnitConvert({
  //         stockNumber: itemVal.stockNumber || itemVal.stock,
  //         productMinimumQuantity: itemVal.minimum,
  //         productMinimumSalesUnit: `${itemVal.saleGroup}`,
  //         skuId: itemVal.skuId,
  //         productUnit: goodsItem.unit, // 选中的单位
  //         productUnitBefore: itemVal.unit, // 选则之前的单位
  //       }).then((res) => {
  //         list.forEach((item) => {
  //           if (item.id === id) {
  //             item.batchList.forEach((each, idx) => {
  //               const eachs: any = each;
  //               if (index === idx) {
  //                 eachs.auxiliaryUnit = res.productUnit;
  //                 eachs.saleGroup = res.productMinimumSalesUnit;
  //               }
  //             });
  //           }
  //         });
  //         setList(JSON.parse(JSON.stringify(list)));
  //       });
  //     }
  //   }
  // };

  const onChangeData = (id: number, index: number, val: string) => {
    list.forEach((item) => {
      if (item.id === id) {
        item.batchList.forEach((each, idx) => {
          const eachs: any = each;
          if (index === idx) {
            eachs.number = Number(val) <= 0 && val !== '' ? 0 : val;
          }
        });
      }
    });
    setList(JSON.parse(JSON.stringify(list)));
  };

  const onBlurData = (
    id: number,
    index: number,
    val: string
    // saleGroup: string,
    // batchInfo: BatchListResult
  ) => {
    // if (
    //   (batchInfo.auxiliaryUnit || batchInfo.unit) &&
    //   Number(saleGroup) &&
    //   BigNumber(val || 0)
    //     .div(saleGroup || 0)
    //     .toNumber() %
    //     1 !==
    //     0
    // ) {
    //   list.forEach((item) => {
    //     if (item.id === id) {
    //       item.batchList.forEach((each, idx) => {
    //         const eachs: any = each;
    //         if (index === idx) {
    //           eachs.number = BigNumber(
    //             Math.ceil(Number(val) / Number(eachs.saleGroup || saleGroup || 1))
    //           )
    //             .times(Number(eachs.saleGroup || saleGroup || 1))
    //             .toNumber();
    //         }
    //       });
    //     }
    //   });
    //   setList(JSON.parse(JSON.stringify(list)));
    //   message.error('请输入最小销售单元整数倍');
    //   return;
    // }
    list.forEach((item) => {
      if (item.id === id) {
        item.batchList.forEach((each, idx) => {
          const eachs: any = each;
          if (index === idx) {
            eachs.number = val;
          }
        });
      }
    });
    setList(JSON.parse(JSON.stringify(list)));
  };

  const onAdd = (id: number) => {
    list.forEach((item) => {
      if (item.id === id) {
        if (isDisableOrder && orderType === 10) {
          item.batchList.push({
            batch: '',
            unit: currentUnit.current || item.batchList[0].auxiliaryUnit || item.batchList[0].unit,
            auxiliaryUnit:
              currentUnit.current || item.batchList[0].auxiliaryUnit || item.batchList[0].unit,
            number: '',
            stock: '',
            stockNumber: '',
            saleGroup: '',
            minimum: item.minimum,
            onTheWaySkuNumber: '',
            availableStock: 0,
            onTheWayNumber: 0,
          });
        } else {
          item.batchList.push({
            batch: '',
            unit: !item.unitTemplateId || orderType === 11 ? item.unit : '',
            auxiliaryUnit: !item.unitTemplateId || orderType === 11 ? item.unit : '',
            number: '',
            stock: '',
            stockNumber: '',
            saleGroup: item.saleGroupStr,
            minimum: item.minimum,
            onTheWaySkuNumber: '',
            availableStock: 0,
            onTheWayNumber: 0,
          });
        }
      }
    });
    setList([...list]);
  };

  const onDelete = (id: number, index: number, index1: number, lengthNum: number) => {
    if (lengthNum > 1) {
      list.forEach((item) => {
        if ((item.id || item.shopSkuId) === id) {
          item.batchList.splice(index, 1);
        }
      });
      setList(JSON.parse(JSON.stringify(list)));
      message.success('删除成功!');
    } else {
      if (isEdit && isDisableOrder && orderType === 10) {
        message.warning('商品批次不能为空!');
        return;
      }
      Modal.confirm({
        title: '提示',
        icon: '',
        centered: true,
        width: 290,
        content: `确定删除该商品？`,
        onOk: () => {
          list.splice(index1, 1);
          setList([...list]);
          message.success('删除成功!');
        },
      });
    }
  };

  const onOk = () => {
    const errorIndex = list.findIndex((item) =>
      item.batchList.some(
        (each) => (!each.batch && isBatch) || !(each.auxiliaryUnit || each.unit) || !each.number
      )
    );
    let top = 0;
    batchListEl.current.querySelectorAll('.item').forEach((item, index) => {
      if (index < errorIndex) {
        top += item.clientHeight;
      }
    });
    batchListEl.current.scrollTop = top;
    setIsCheck(true);
    const flag = list.some((item) =>
      item.batchList.some(
        (each) =>
          (!each.batch && isBatch) ||
          !(each.auxiliaryUnit || each.unit) ||
          (!each.number && orderType !== 11) ||
          (each.number === '' && orderType === 11)
      )
    );
    if (flag) {
      return;
    }
    if (
      list.some((item) => item.batchList.some((each) => Number(each.number) <= 0)) &&
      orderType !== 11
    ) {
      message.warning('商品数量不能小于0');
      return;
    }
    if (list.some((item) => item.batchList.some((each) => each.number === ''))) {
      message.warning('商品数量不能为空');
      return;
    }
    list.forEach((item) => {
      const items = item;
      items.batchListStr = item.batchList.map(
        (each) => each.batch + (each.auxiliaryUnit || each.unit)
      );
    });

    if (list.some((item) => isRepeat(item.batchListStr || []))) {
      message.warning(
        [9, 11].includes(orderType) ? '不允许添加相同单位选项' : `不允许同批次和同单位选项`
      );
      return;
    }
    if (!flag) {
      onConfirm(list);
      onCancel();
    }
  };

  const showStockTipFoo = useMemo(
    () => orderType !== 11 && orderType !== 9 && showStockTip,
    [orderType, showStockTip]
  );

  // const title = useMemo(() => {
  //   switch (orderType) {
  //     case 10:
  //       return '出库';
  //     case 13:
  //       return '调拨';
  //     case 14:
  //       return '领用';
  //     default:
  //       return '';
  //   }
  // }, [orderType]);

  const modelWidth = useMemo(() => {
    switch (orderType) {
      case 9:
        return 641;
      case 11:
        return 770;
      case 10:
      case 13:
      case 14:
        return 918;
      default:
        return 700;
    }
  }, [orderType]);

  const onClose = () => {
    if (JSON.stringify(list) === JSON.stringify(initList.current)) {
      onCancel();
    } else {
      Modal.confirm({
        title: '提示',
        content: '页面内容未保存，是否离开当前页面？',
        cancelText: '离开',
        okText: '继续编辑',
        icon: null,
        width: 290,
        centered: true,
        onOk: () => {},
        onCancel: () => {
          onCancel();
        },
      });
    }
  };

  const billType = useMemo(() => {
    switch (orderType) {
      case 10:
        return 1;
      case 14:
        return 2;
      case 13:
        return 3;
      default:
        return '';
    }
  }, [orderType]);

  const inputDis = (obj: BatchListResult) => {
    if (orderType === 11 || orderType === 9) {
      return !(obj.auxiliaryUnit || obj.unit);
    }
    return !obj.batch;
  };

  const getUnitItem = (id: number, unit: string) => {
    if (unit && id) {
      const unitFoo = unitAll.find((f) => f.skuId === id && f.unitName === unit);
      const unitEdit = list.find((f) => f.skuId === id);
      let data = {
        ...unitFoo,
      };
      if (unitEdit?.skuRelationParamList && unitEdit?.skuRelationParamList?.length) {
        const unitEditFoo = unitEdit.skuRelationParamList.find((f) => f.unitName === unit);
        data = {
          ...unitFoo,
          ...unitEditFoo,
        };
      }
      return data;
    }

    return {
      availableStockStr: 0,
      onTheWayNumber: 0,
    };
  };

  useEffect(() => {
    if (visible) {
      if (orderType === 10 && isDisableOrder) {
        currentUnit.current = selectGoodsList[0].auxiliaryUnit;
      }
      selectGoodsList.forEach((item) => {
        const items = item;
        items.id = items.id || item.shopSkuId;
        if (!item.batchList || !item.batchList.length) {
          items.batchList = [
            {
              batch: '',
              unit:
                !item.unitTemplateId || orderType === 11 || isDisableOrder
                  ? currentUnit.current || item.unit
                  : currentUnit.current || '',
              auxiliaryUnit:
                !item.unitTemplateId || orderType === 11 || isDisableOrder
                  ? currentUnit.current || item.unit
                  : currentUnit.current || '',
              stock: '',
              stockNumber: '',
              number: '',
              saleGroup: item.saleGroupStr,
              minimum: item.minimum,
              onTheWaySkuNumber: '',
              onTheWayNumber: 0,
              availableStock: 0,
            },
          ];
        } else {
          // console.log(items, 'items');
          // items.batchList = items.batchList.map((m, fIndex) => {
          //   const foo = item.skuRelationParamList[fIndex];
          //   console.log(foo, 'foo');
          //   return {
          //     ...m,
          //     availableStock: foo.availableStock || 0,
          //     onTheWayNumber: foo.onTheWayNumber || 0,
          //   };
          // });
        }
      });
      if (orderType !== 9) {
        postBaseBatchUnitStock({
          warehouseNo,
          isInventoryType: orderType === 11 ? 1 : 0,
          skuIdList: selectGoodsList.map((m) => m.skuId),
        }).then((res) => {
          setUnitAll(res.list);
        });
      }
      if (orderType !== 9 && orderType !== 11) {
        setShowStockTip(true);
      }
      setList(JSON.parse(JSON.stringify(selectGoodsList)));
      initList.current = JSON.parse(JSON.stringify(selectGoodsList));
    } else {
      setIsCheck(false);
    }
  }, [orderType, selectGoodsList, visible, isDisableOrder, warehouseNo]);

  const footer = (
    <>
      {showStockTipFoo && (
        <div className={styles.stockTip}>
          <div>
            <img
              className={styles.stockTipImg}
              src="https://img.huahuabiz.com/user_files/2023616/1686898724715212.png"
              alt=""
            />
            <span>在途库存=未生成仓库单的采购订单-未生成仓库单的销售订单。</span>
          </div>
          <Icon
            name="close"
            className={styles.stockTipDelete}
            onClick={() => setShowStockTip(false)}
          />
        </div>
      )}
      <div>
        {!isDisableOrder && (
          <div
            role="button"
            className={styles.addGoods}
            tabIndex={0}
            onClick={() => onAddGoods(list)}
          >
            <Icon name="plus" size={20} className="mr-1" />
            <span>添加商品</span>
          </div>
        )}
      </div>
      <div>
        <Button type="default" onClick={onClose}>
          取消
        </Button>
        <Button type="primary" onClick={onOk}>
          确定
        </Button>
      </div>
    </>
  );

  return (
    <Modal
      visible={visible}
      onCancel={onClose}
      className={styles.modal}
      centered
      width={modelWidth}
      title={
        <>
          <span className={styles.requiry}>*</span>
          设置商品信息
        </>
      }
      footer={footer}
    >
      <div className={styles.title}>
        <div className={styles.goodsName}>商品属性</div>
        <div
          className={classNames(styles.unitInfo, {
            [styles.unitInfoBatch]: orderType === 9,
          })}
        >
          单位
        </div>
        {orderType !== 9 && <div className={styles.inventory}>库存</div>}
        {/* {isBatch && <div className={styles.batchInfo}>{title}批次</div>} */}
        {isBatch && <div className={styles.batchInfo}>库存批次</div>}
        <div className={styles.numInfo}>{`${orderType === 11 ? '盘点' : ''}`}数量</div>
        <div className={styles.handle}>操作</div>
      </div>
      <div className={styles.list} ref={batchListEl}>
        {list.length > 0 ? (
          list.map((item, index1) => (
            <div className={classNames(styles.item, 'item')} key={item.id}>
              <div className={styles.goodsInfo}>
                <img
                  className={styles.img}
                  src={
                    item.images ||
                    (item.imagesList && item.imagesList.length && item.imagesList[0]) ||
                    'https://img.huahuabiz.com/default/image/default_holder.png'
                  }
                  alt=""
                />
                <div className={styles.goods}>
                  <div className={styles.name} title={item.name}>
                    {item.name}
                  </div>
                  <div className={styles.meta} title={metaTitle(item.standardList)}>
                    {item.standardList &&
                      item.standardList.length > 0 &&
                      item.standardList.map((each) => (
                        <span className={styles.metaItem} key={each.name}>
                          {each.name}: {each.value}
                          <span className={styles.metaTag}>;</span>
                        </span>
                      ))}
                  </div>

                  <div className={styles.saleGroup}>条形码: {item?.barsCode || '--'}</div>
                </div>
              </div>
              <div className={styles.batchList}>
                {item.batchList &&
                  item.batchList.length > 0 &&
                  item.batchList.map((goods, index) => (
                    <div
                      // eslint-disable-next-line react/no-array-index-key
                      key={`${goods.batch}+${goods.unit}+${index}`}
                      className={classNames(styles.batchItem, { [styles.batchItemNo]: !isBatch })}
                    >
                      <div
                        className={classNames(styles.unitInfo, {
                          [styles.unitInfoBatch]: orderType === 9,
                        })}
                      >
                        <Select
                          value={goods.auxiliaryUnit || goods.unit || null}
                          options={unitList}
                          disabled={isDisableOrder}
                          fieldNames={{ label: 'unitName', value: 'unitName' }}
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                          status={!(goods.auxiliaryUnit || goods.unit) && isCheck ? 'error' : ''}
                          onFocus={() => {
                            if (orderType !== 9) {
                              setUnitList([]);
                              getUnitListBySkuId({ skuIdList: [item.skuId] }).then((res) => {
                                const listFoo =
                                  res.list[0]?.unitNameList.map((unit) => ({
                                    unitName: unit,
                                    scale: 0,
                                  })) || [];
                                if (listFoo.length) {
                                  setUnitList(
                                    listFoo as unknown as FormulaConvertManyconvertResult[]
                                  );
                                } else {
                                  setUnitList([{ unitName: goods.unit, scale: item.saleGroup }]);
                                }
                              });
                              return;
                            }
                            if (item.unitTemplateId) {
                              setUnitList([]);
                              runUnit({
                                templateId: item.unitTemplateId,
                              });
                            } else {
                              setUnitList([{ unitName: goods.unit, scale: item.saleGroup }]);
                            }
                          }}
                          onChange={(e) =>
                            onChangeUnit(item.id, index, e, item.saleGroup, goods, unitList)
                          }
                          placeholder="请选择"
                        />
                      </div>

                      {orderType !== 9 && (
                        <div className={styles.inventory}>
                          {goods.unit || orderType === 11 ? (
                            // eslint-disable-next-line react/jsx-no-useless-fragment
                            <>
                              {orderType === 11 ? (
                                <div className={styles.goodsUnit}>
                                  {checkPermission('AV_001_004_007')
                                    ? getUnitItem(
                                        item?.skuId,
                                        goods.auxiliaryUnit || goods.unit || ''
                                      )?.availableStockStr || 0
                                    : '***'}
                                </div>
                              ) : (
                                <>
                                  <div className={classNames(styles.label, { 'mb-1': skuNumber })}>
                                    可用库存{' '}
                                    {getUnitItem(
                                      item?.skuId,
                                      goods.auxiliaryUnit || goods.unit || ''
                                    )?.availableStockStr || 0}
                                  </div>
                                  {skuNumber && (
                                    <div className={styles.label}>
                                      在途库存{' '}
                                      {getUnitItem(
                                        item?.skuId,
                                        goods.auxiliaryUnit || goods.unit || ''
                                      )?.onTheWayNumber || 0}
                                    </div>
                                  )}
                                </>
                              )}
                            </>
                          ) : (
                            <div className={styles.goodsUnit}>--</div>
                          )}
                        </div>
                      )}

                      {isBatch && (
                        <div className={styles.batchInfo}>
                          <Select
                            value={goods.batch || null}
                            disabled={!(goods.auxiliaryUnit || goods.unit)}
                            getPopupContainer={(triggerNode) => triggerNode.parentNode}
                            // fieldNames={{ label: 'skuBatchNo', value: 'skuBatchNo' }}
                            placeholder="请选择批次"
                            optionLabelProp="skuBatchNo"
                            status={!goods.batch && isCheck ? 'error' : ''}
                            onFocus={() => {
                              const params: any = {
                                warehouseNo,
                                skuId: item.skuId,
                                operateType: isEdit ? 2 : 1,
                                unitName: goods.unit,
                              };
                              if (isEdit && billType) {
                                params.orderNo = orderNo;
                                params.billType = billType;
                              }
                              runBatch({ ...params });
                            }}
                            // onChange={(e) => onChangeBatch(item.id, index, e, goods, batchList)}
                            onChange={(e) => onChangeBatch(item.id, index, e, goods)}
                            dropdownMatchSelectWidth={false}
                            style={{ width: '143px' }}
                          >
                            {batchList.map((batchItem) => (
                              <Select.Option
                                value={batchItem.skuBatchNo}
                                label={batchItem.skuBatchNo}
                                key={item.id}
                              >
                                <div className={styles.stockSku}>
                                  <div>批次：{batchItem.skuBatchNo}</div>
                                  <span className={styles.batchItemInfo}>
                                    库存：{batchItem.stock}
                                    {batchItem.expireJson ? (
                                      <span className={styles.expireJson}>
                                        ; 保质期：{batchItem.expireJson}
                                      </span>
                                    ) : (
                                      ''
                                    )}
                                  </span>
                                  {batchItem.skuBatchNo === goods.batch && (
                                    <img
                                      className={styles.stockImg}
                                      src="https://img.huahuabiz.com/user_files/2023417/1681724945896901.png"
                                      alt=""
                                    />
                                  )}
                                </div>
                              </Select.Option>
                            ))}
                          </Select>
                        </div>
                      )}

                      <div className={styles.numInfo}>
                        <Input
                          status={
                            (!goods.number && isCheck && orderType !== 11) ||
                            (orderType === 11 && goods.number === '' && isCheck)
                              ? 'error'
                              : ''
                          }
                          disabled={inputDis(goods)}
                          value={goods.number === '' ? '' : Number(goods.number)}
                          min={Number(goods.minimum) || 0}
                          type="number"
                          placeholder="请输入"
                          // @ts-ignore
                          onWheel={(e) => e.target.blur()}
                          // onFocus={() => onFocusNum(item.id, index, goods, item)}
                          onChange={(e) => onChangeData(item.id, index, e.target.value)}
                          onBlur={(e) => onBlurData(item.id, index, e.target.value)}
                        />
                      </div>
                      <div className={styles.handle}>
                        {!(orderType === 9 && isDisableOrder) && (
                          <>
                            <span
                              role="button"
                              tabIndex={0}
                              className={classNames(styles.btnDel, {
                                [styles.btnDelFoo]:
                                  item.batchList.length === 1 &&
                                  businessName === '销售出库单' &&
                                  orderType === 10,
                              })}
                              onClick={() => {
                                if (
                                  item.batchList.length === 1 &&
                                  businessName === '销售出库单' &&
                                  orderType === 10
                                )
                                  return;
                                setTimeout(() => {
                                  onDelete(
                                    item.id || item.shopSkuId,
                                    index,
                                    index1,
                                    item.batchList.length
                                  );
                                });
                              }}
                            >
                              删除
                            </span>
                            {/* {orderType !== 11 && ( */}
                            <span
                              role="button"
                              tabIndex={0}
                              className={styles.btnAdd}
                              onClick={() => {
                                setTimeout(() => {
                                  onAdd(item.id);
                                });
                              }}
                            >
                              添加
                            </span>
                            {/* )} */}
                          </>
                        )}
                      </div>
                      {/* {isCheck &&
                        ((!goods.batch && isBatch) || !(goods.auxiliaryUnit || goods.unit)) && (
                          <div className={styles.error}>请输入信息</div>
                        )} */}
                    </div>
                  ))}
              </div>
            </div>
          ))
        ) : (
          <div className={styles.noData}>
            <Empty description="请先添加商品" />
          </div>
        )}
      </div>
    </Modal>
  );
}

SetupGoodsInfo.defaultProps = {
  businessName: '',
  isDisableOrder: false,
  skuNumber: false,
};

export default SetupGoodsInfo;
