import { PropsWithChildren } from 'react';
import { Icon } from '@/components';
import { Link } from 'react-router-dom';
import styles from './index.module.less';

interface CardProps {
  title: string;
  toUrl?: string;
}

function Card({ title, toUrl, children }: PropsWithChildren<CardProps>) {
  return (
    <div className={styles.card}>
      {toUrl ? (
        <Link to={toUrl}>
          <div className={styles.title}>
            <span className={styles.label}>{title}</span>
            <Icon name="right" className={styles.icon} />
          </div>
        </Link>
      ) : (
        <div className={styles.title}>
          <span className={styles.label}>{title}</span>
          <Icon name="right" className={styles.icon} />
        </div>
      )}
      {children}
    </div>
  );
}

Card.defaultProps = {
  toUrl: null,
};

export default Card;
