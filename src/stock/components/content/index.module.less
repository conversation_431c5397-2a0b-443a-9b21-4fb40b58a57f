.content {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 20px 20px 0;
}

.box {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: auto;
}

.title {
  padding: 10px 20px;
  border-radius: @border-radius-sm;
  background: #f5f6fa;
}

.list {
  flex: 1;
  overflow: auto;
}

.footer {
  display: flex;
  height: 48px;
  padding: 10px 0;
  justify-content: space-between;
  flex-direction: row-reverse;
}
