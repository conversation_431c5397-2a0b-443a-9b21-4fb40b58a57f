import { PropsWithChildren, ReactNode } from 'react';
import styles from './index.module.less';

interface ContentProps {
  title?: ReactNode | null;
  footer?: ReactNode | null;
}

function Content({ title, footer, children }: PropsWithChildren<ContentProps>) {
  return (
    <div className={styles.content}>
      <div className={styles.box}>
        <div className={styles.title}>{title}</div>
        <div className={styles.list}>{children}</div>
      </div>
      <div className={styles.footer}>{footer}</div>
    </div>
  );
}

Content.defaultProps = {
  title: null,
  footer: null,
};

export default Content;
