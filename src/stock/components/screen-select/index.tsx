import { Popconfirm, Checkbox, PopconfirmProps, Empty } from 'antd';
import { PropsWithChildren, useMemo, useRef, useState, useEffect } from 'react';
import { CheckboxValueType } from 'antd/lib/checkbox/Group';
import classNames from 'classnames';
import styles from './index.module.less';

export interface selectListProps {
  belongCompanyId: number;
  cityId: number;
  cityName: string;
  companyId: number;
  id: number;
  linkId: number;
  provinceId: number;
  provinceName: string;
  region: string;
  regionId: number;
  warehouseAddress: string;
  warehouseBelongType: number;
  warehouseBelongTypeStr: string;
  warehouseName: string;
  warehouseNo: string;
  warehouseShareStatus: number;
  warehouseStatus: number;
  warehouseStock: string;
  warehouseStockSort: number;
}

interface wareSelectListProps {
  warehouseNo: string;
  warehouseName: string;
}

interface Props {
  iconUrl?: string;
  selectId?: (number | string)[];
  selectList: wareSelectListProps[];
  // eslint-disable-next-line no-unused-vars
  getSelectId?: (item: (number | string)[]) => void;
}

function ScreenSelect({ selectId, getSelectId, iconUrl, selectList }: PropsWithChildren<Props>) {
  const popRef = useRef(null as unknown as PopconfirmProps);
  const [visible, setVisible] = useState(false);
  const [checkboxId, setCheckboxId] = useState<(number | string)[]>(selectId || []);

  const confirm = () => {
    if (getSelectId) getSelectId(checkboxId);
    setVisible(false);
  };

  const onCancel = () => {
    setCheckboxId([]);
    setVisible(true);
  };

  const onChange = (checkedValues: CheckboxValueType[]) => {
    setCheckboxId(checkedValues as number[]);
  };

  const imgClick = () => {
    setVisible((val) => !val);
  };

  const imgUrl = useMemo(() => {
    if (iconUrl) return iconUrl;
    if (selectId?.length) {
      return 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023727/1690445873810416.png';
    }
    return 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023727/1690427608333810.png';
  }, [selectId, iconUrl]);

  useEffect(() => {
    if (checkboxId.length && !selectId?.length) setCheckboxId([]);
  }, [selectId]); // eslint-disable-line

  useEffect(() => {
    const box = document.querySelector('.ant-drawer-content');
    box?.addEventListener('click', () => {
      // const id = e.target?.parentNode?.id !== 'screenFirm';
      // console.log(e, id, 'id');
      if (visible) {
        setVisible(false);
      }
    });

    return () => {
      box?.removeEventListener('click', () => {});
    };
  }, [visible]);

  // eslint-disable-next-line react/no-unstable-nested-components
  function CustomContent() {
    return (
      <div className={styles.screenBody}>
        {selectList.length ? (
          <Checkbox.Group value={checkboxId} style={{ width: '100%' }} onChange={onChange}>
            {selectList.map((checkItem: wareSelectListProps) => (
              <div
                key={checkItem.warehouseNo}
                className={classNames(styles.screenItem, {
                  [styles.screenItemFoo]: checkboxId.includes(checkItem.warehouseNo),
                })}
              >
                <Checkbox value={checkItem.warehouseNo}>{checkItem.warehouseName}</Checkbox>
              </div>
            ))}
          </Checkbox.Group>
        ) : (
          <Empty />
        )}
      </div>
    );
  }

  return (
    <Popconfirm
      icon={null}
      placement="bottom"
      ref={popRef}
      visible={visible}
      title={CustomContent()}
      onConfirm={confirm}
      onCancel={onCancel}
      destroyTooltipOnHide
      okText="确定"
      cancelText="重置"
      overlayClassName={styles.popConFirm}
    >
      <div role="button" tabIndex={0} onClick={() => imgClick()} id="screenFirm">
        <img className={styles.screenImg} src={imgUrl} alt="" />
      </div>
    </Popconfirm>
  );
}

ScreenSelect.defaultProps = {
  selectId: [],
  getSelectId: () => {},
  iconUrl: '',
};

export default ScreenSelect;
