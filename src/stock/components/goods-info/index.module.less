@import 'styles/mixins/mixins';

.goodsInfo {
  display: flex;
}

.goodsImg {
  width: 64px;
  height: 64px;
  margin-right: 8px !important;
  border-radius: 12px;
  object-fit: contain;
}

.goodsText {
  display: flex;
  width: calc(100% - 64px);
  justify-content: center;
  flex: 1;
  flex-direction: column;

  & .label {
    color: #888b98;
    font-size: 12px;
  }

  & .goodsStandard {
    .text-overflow(1);
  }

  & .labelText {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  & .goodStand {
    display: flex;
    align-items: center;

    & .text {
      width: 100%;
      .text-overflow(2);

      word-wrap: break-word;
    }
  }
}

.goodsName {
  .text-overflow(2);

  & img {
    display: inline-block;
  }
}
