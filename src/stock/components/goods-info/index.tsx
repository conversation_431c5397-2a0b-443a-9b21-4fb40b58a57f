import { PropsWithChildren, ReactNode } from 'react';
import { Tooltip } from 'antd';
import styles from './index.module.less';

import lackCome from '../../assets/imgs/lack-come.png';
import surpassCome from '../../assets/imgs/surpass-come.png';

interface Info {
  img?: string;
  name?: string;
  standard?: ReactNode;
  standardId: number;
  barsCode?: string;
  unit?: string;
  imgWidth?: number;
  imgHeight?: number;
  isReport?: boolean;
  isShowBarsCode?: boolean;
  skuCode?: string;
}

function GoodsInfo({
  img,
  name,
  standard,
  barsCode,
  unit,
  imgWidth = 64,
  imgHeight = 64,
  skuCode,
  standardId,
  isReport,
  isShowBarsCode,
}: PropsWithChildren<Info>) {
  return (
    <div className={styles.goodsInfo}>
      <img
        className={styles.goodsImg}
        style={{
          width: `${imgWidth}px`,
          height: `${imgHeight}px`,
          margin: isReport ? '6px 0' : undefined,
        }}
        src={img || 'https://img.huahuabiz.com/default/image/default_holder.png'}
        alt=""
      />
      <div className={styles.goodsText} style={{ marginLeft: isReport ? '5px' : undefined }}>
        <div className={styles.goodStand}>
          {standardId && standardId > 1 ? (
            <div className={styles.goodsName}>
              <img className="mr-1" src={standardId === 2 ? surpassCome : lackCome} alt="" />
              {name}
            </div>
          ) : (
            <Tooltip title={name} placement="bottom">
              <span className={styles.text}>
                {name} {skuCode}
              </span>
            </Tooltip>
          )}
        </div>

        <div className={styles.label}>
          {standard && (
            <div
              className={styles.goodsStandard}
              style={{ marginTop: isReport ? '5px' : undefined }}
            >
              规格：{standard}
            </div>
          )}
          {isShowBarsCode && <div className={styles.labelText}>条形码：{barsCode || '--'}</div>}
          {unit && <div className={styles.labelText}>单位：{unit}</div>}
        </div>
      </div>
    </div>
  );
}

GoodsInfo.defaultProps = {
  img: '',
  name: '',
  standard: '',
  number: '',
  barsCode: '',
  unit: '',
  imgWidth: 64,
  imgHeight: 64,
  isReport: false,
  isShowBarsCode: true,
  skuCode: '',
};

export default GoodsInfo;
