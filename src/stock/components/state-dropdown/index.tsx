import { Icon } from '@/components';
import { Dropdown, Menu, message, Modal, Space } from 'antd';
import { PropsWithChildren, useMemo } from 'react';
import orderPermission from '../../utils/order-permission';
import styles from './index.module.less';

interface Props {
  status: number;
  orderType: number;
  orderName?: string;
  isCanEdit?: boolean;
  ifCanDelete?: boolean;
  // eslint-disable-next-line no-unused-vars
  btnChange: (name: string) => void;
}

function StateDropdown({
  status,
  btnChange,
  orderType,
  isCanEdit,
  orderName,
  ifCanDelete,
}: PropsWithChildren<Props>) {
  const check = (value: string) => {
    if (!orderPermission(orderType, value)) {
      message.error('暂无权限，请联系公司管理员开通');
    } else if (value === '编辑' || value === '打印') {
      btnChange(value);
    } else {
      Modal.confirm({
        title: '提示',
        icon: '',
        centered: true,
        okText: '确认',
        cancelText: '取消',
        content: `是否${value}`,
        onOk: () => {
          btnChange(value);
        },
      });
    }
  };
  const drop = useMemo(() => {
    let dropList: any[] = [];
    if (status === 0) {
      dropList = [];
    }
    if (status === 2) {
      dropList = [
        {
          key: '1',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                check('编辑');
              }}
            >
              编辑
            </div>
          ),
        },
        {
          key: '2',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                check('删除');
              }}
            >
              删除
            </div>
          ),
        },
      ];
      if (orderName !== '调拨' && orderName !== '盘点') {
        const dropListPlus = [
          {
            key: '3',
            label: (
              <div
                role="button"
                tabIndex={0}
                onClick={() => {
                  check('打印');
                }}
              >
                打印
              </div>
            ),
          },
        ];
        dropList = dropList.concat(dropListPlus);
      }
    }
    if (orderType === 11 && (status === 1 || status === 0)) {
      dropList = [
        {
          key: '4',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                check('导出详情');
              }}
            >
              导出详情
            </div>
          ),
        },
      ];
    }
    if ((orderName === '调拨入库单' || orderName === '调拨出库单') && status === 0) {
      dropList = [];
    }
    if (orderName === '调拨出库单' && status === 2) {
      dropList = [];
    }
    if (orderName === '盘点' && status === 0) {
      dropList = [
        {
          key: '1',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                check('编辑');
              }}
            >
              编辑
            </div>
          ),
        },
        {
          key: '4',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                check('导出详情');
              }}
            >
              导出详情
            </div>
          ),
        },
        {
          key: '3',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                check('打印');
              }}
            >
              打印
            </div>
          ),
        },
      ];
    }
    if (
      !status &&
      !['调拨出库单', '调拨入库单', '退货入库单', '退货出库单'].includes(orderName || '') &&
      isCanEdit
    ) {
      if ([11, 13].includes(orderType)) {
        dropList = [
          {
            key: '1',
            label: (
              <div
                role="button"
                tabIndex={0}
                onClick={() => {
                  check('编辑');
                }}
              >
                编辑
              </div>
            ),
          },
        ];
        if ([13].includes(orderType)) {
          dropList.push({
            key: '3',
            label: (
              <div
                role="button"
                tabIndex={0}
                onClick={() => {
                  check('打印');
                }}
              >
                打印
              </div>
            ),
          });
        }
      } else {
        if (isCanEdit) {
          dropList.push({
            key: '1',
            label: (
              <div
                role="button"
                tabIndex={0}
                onClick={() => {
                  check('编辑');
                }}
              >
                编辑
              </div>
            ),
          });
        }
        dropList.push({
          key: '3',
          label: (
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                check('打印');
              }}
            >
              打印
            </div>
          ),
        });
      }
    }
    // if (!checkPermission('AV_001_010_001')) {
    //   dropList = dropList.filter((item) => item.key !== '3');
    // }
    if (ifCanDelete && dropList.every((s) => s.key !== '2')) {
      dropList.push({
        key: '2',
        label: (
          <div
            role="button"
            tabIndex={0}
            onClick={() => {
              check('删除');
            }}
          >
            删除
          </div>
        ),
      });
    }
    return dropList;
  }, [status, orderType, orderName, isCanEdit]); // eslint-disable-line
  return (
    <div className={styles.text} role="button" tabIndex={0} onClick={(e) => e.stopPropagation()}>
      {drop.length > 1 ? (
        <Dropdown className={styles.btn} overlay={<Menu items={drop} />} placement="bottom">
          <Space onClick={(e) => e.stopPropagation()} className={styles.handle}>
            <Icon className={styles.icon} name="zu13366" />
          </Space>
        </Dropdown>
      ) : (
        <div>
          {!drop.length ? (
            ![11, 13].includes(orderType) && (
              <span
                role="button"
                tabIndex={0}
                className={styles.btn}
                onClick={() => {
                  check('打印');
                }}
              >
                打印
              </span>
            )
          ) : (
            <span
              role="button"
              tabIndex={0}
              className={styles.btn}
              onClick={() => {
                check(drop[0].key === '4' ? '导出详情' : '编辑');
              }}
            >
              {drop[0].key === '4' ? '导出详情' : '编辑'}
            </span>
          )}
        </div>
      )}
    </div>
  );
}

StateDropdown.defaultProps = {
  orderName: '',
  isCanEdit: true,
  ifCanDelete: false,
};

export default StateDropdown;
