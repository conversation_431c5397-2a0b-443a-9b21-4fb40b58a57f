import { useEffect } from 'react';
import { Col, Row } from 'antd';
import { Context } from '@/components';
import { sessions } from '@/store';
import Query from './query';
import Hot from './hot';
import CommonUse from './common-use';
import Warn from './warn';
import Order from './order';

function Home() {
  useEffect(() => {
    sessions.select('stock');
  }, []);

  return (
    // <Context permission="AV_001" container theme={null} head={<Context.Head title="仓库管理" />}>
    <Context container theme={null} head={<Context.Head title="仓库管理" />}>
      <Row gutter={20}>
        <Col md={12} xs={24}>
          <Order />
          <Warn />
          <CommonUse />
        </Col>
        <Col md={12} xs={24}>
          <Query />
          <Hot />
        </Col>
      </Row>
    </Context>
  );
}

export default Home;
