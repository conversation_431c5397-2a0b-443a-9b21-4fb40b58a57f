import { useNavigate } from 'react-router-dom';
import { testPerm } from '@/utils/permission';
import styles from './common-use.module.less';

const useList = [
  {
    name: '仓库列表',
    image: 'https://img.huahuabiz.com/user_files/2023227/1677491800675194.png',
    url: '/stock/warehouse',
    code: 'AV_001_007',
  },
  {
    name: '库存查询',
    image: 'https://img.huahuabiz.com/user_files/2023227/1677491800673429.png',
    url: '/stock/query',
    code: 'AV_001_006',
  },
  {
    name: '查询报表',
    image: 'https://img.huahuabiz.com/user_files/2023227/1677491800672326.png',
    url: '/stock/batch',
    code: 'AV_001_013',
  },
  {
    name: '库存预警',
    image: 'https://img.huahuabiz.com/user_files/2023227/1677491800674792.png',
    url: '/stock/warn',
    code: 'AV_001_005',
  },
  {
    name: '补货单',
    image: 'https://img.huahuabiz.com/user_files/202344/1680571747395607.png',
    url: '/stock/replenish',
    code: 'AV_001_010',
  },
  // {
  //   name: '批次成本报表',
  //   image: 'https://img.huahuabiz.com/user_files/2023828/1693210197970385.png',
  //   url: '/stock/report',
  //   code: 'AV_001_013',
  // },
  {
    name: '后台管理',
    image: 'https://img.huahuabiz.com/user_files/202344/1680571675771464.png',
    url: '/stock/backstage/node-approval',
    code: 'AV_001_011',
  },
  {
    name: '货位管理',
    image: 'https://img.huahuabiz.com/user_files/202459/1715243546345610.png',
    url: '/stock/slotting-setting',
    code: 'AV_001_014',
  },
];

function CommonUse() {
  const navigate = useNavigate();

  const onGoList = (code: string, url: string) => {
    if (code.length) {
      if (!testPerm(code)) return;
    }
    navigate(url);
  };

  return (
    <div className={styles.card}>
      <div className={styles.title}>常用</div>
      <div className={styles.list}>
        {useList.map((item, index) => (
          <div
            role="button"
            tabIndex={index}
            className={styles.item}
            key={item.name}
            onClick={() => onGoList(item.code || '', item.url)}
          >
            <img className={styles.itemImg} src={item.image} alt="" />
            <div>{item.name}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default CommonUse;
