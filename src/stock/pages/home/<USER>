import { Link, useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { stockHomeWarnNum } from '@/apis';
import { checkPermission } from '@/utils/permission';
import classNames from 'classnames';
import { message } from 'antd';
import styles from './index.module.less';
import noData from '../../assets/imgs/no-data.png';
import exceed from '../../assets/imgs/exceed.png';
import short from '../../assets/imgs/short.png';

function Warn() {
  const navigate = useNavigate();

  const [warnNum, setWarnNum] = useState({
    totalNum: 0,
    shortageNum: 0,
    exceedNum: 0,
  });

  const goList = () => {
    if (checkPermission('AV_001_005') && checkPermission('AV_001_005_002')) {
      navigate('/stock/warn');
    } else {
      message.error('暂无权限，请联系公司管理员开通');
    }
  };

  useEffect(() => {
    if (checkPermission('AV_001_005')) {
      stockHomeWarnNum().then((res) => {
        setWarnNum(res);
      });
    }
  }, []);
  return (
    <div className={styles.cardBox}>
      <div role="button" tabIndex={0} onClick={goList}>
        <div className={styles.title}>库存预警</div>
      </div>
      <div className={styles.warn}>
        {warnNum.totalNum ? (
          <>
            <Link to="/stock/warn">
              <div className={styles.num}>{warnNum.totalNum}</div>
              <div className={styles.tip}>预警商品/种</div>
            </Link>
            <div>
              <Link to="/stock/warn?stockStatus=3">
                <div className={classNames(styles.warnItem, 'mb-5')}>
                  <img className={styles.warnImg} src={short} alt="" />
                  <span className={styles.short}>短缺</span>
                  <span className={styles.numItem}>
                    <span className={styles.num}>{warnNum.shortageNum}</span> 种
                  </span>
                </div>
              </Link>
              <Link to="/stock/warn?stockStatus=2">
                <div className={styles.warnItem}>
                  <img className={styles.warnImg} src={exceed} alt="" />
                  <span className={styles.exceed}>超出</span>
                  <span className={styles.numItem}>
                    <span className={styles.num}>{warnNum.exceedNum}</span> 种
                  </span>
                </div>
              </Link>
            </div>
          </>
        ) : (
          <div role="button" tabIndex={0} onClick={goList} className={styles.noData}>
            <img className={styles.noDataImg} src={noData} alt="" />
            <div className={styles.noDataText}>暂无预警信息</div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Warn;
