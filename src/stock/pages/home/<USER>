import { useState } from 'react';
import styles from '@@/stock/pages/home/<USER>';
import classNames from 'classnames';
import noData from '@@/stock/assets/imgs/no-data.png';
import { SkuResult, stockHomeSkuNum, stockSkuInit } from '@/apis';
import type {
  ComposeOption,
  DatasetComponentOption,
  GridComponentOption,
  TitleComponentOption,
  TooltipComponentOption,
} from 'echarts';
import { message, Spin } from 'antd';
import { useNavigate } from 'react-router-dom';
import { checkPermission } from '@/utils/permission';
import { Echarts } from '@/components';
import { useEcharts } from '@/hooks';
import { useMount } from 'ahooks';

type ECOption = ComposeOption<
  TitleComponentOption | TooltipComponentOption | GridComponentOption | DatasetComponentOption
>;

const colors = ['#008CFF', '#16DBCC', '#FFBB38', '#FF82AC', '#A45DF5'];

function Query() {
  const navigate = useNavigate();

  const [skuList, setSkuList] = useState<SkuResult[]>([]);
  const query = useEcharts();
  const [loading, setLoading] = useState(false);

  const getData = () => {
    setLoading(true);
    stockHomeSkuNum()
      .then((res) => {
        setSkuList(res.list);
        let max = 0;
        res.list.forEach((item) => {
          if (item.totalNum >= max) {
            max = item.totalNum;
          }
        });
        const number = [0.9, 0.8, 0.7, 0.6, 0.5];
        const option: ECOption = {
          tooltip: {
            position: 'right',
            formatter(params) {
              // @ts-ignore
              return `<span style="color:#040919">数量 ${params.data.itemValue}</span><br /> <span style="color:#888b98">${params.data.name}</span>`;
            },
          },
          series: [
            {
              name: 'Area Mode',
              type: 'pie',
              radius: [29, 76],
              roseType: 'area',
              label: {
                show: false,
              },
              data: res.list.map((item, index) => ({
                name: item.skuName,
                value: number[index],
                itemValue: item.totalNum,
                itemStyle: {
                  color: colors[index],
                },
              })),
            },
          ],
        };
        query.current?.setOption(option);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const goList = () => {
    if (checkPermission('AV_001_006') && checkPermission('AV_001_006_001')) {
      navigate('/stock/query');
    } else {
      message.error('暂无权限，请联系公司管理员开通');
    }
  };

  useMount(() => {
    if (checkPermission('AV_001_009_001')) {
      stockSkuInit().then(() => {
        getData();
      });
    }
  });

  return (
    <div role="button" tabIndex={0} className={styles.cardBox} onClick={goList}>
      <Spin spinning={loading}>
        <div className={classNames(styles.title, 'pb-1')}>库存查询</div>
        <div className={styles.queryList}>
          {skuList && skuList.length > 0 && checkPermission('AV_001_009_001') ? (
            <>
              <div className={styles.queryBox}>
                <Echarts ref={query} className={styles.query} />
                <div className={styles.queryText}>库存前五商品</div>
              </div>
              <div className={styles.queryListBox}>
                {skuList.map((item, index) => (
                  <div className={styles.queryItem} key={item.skuId}>
                    <span className={styles.name} title={item.skuName}>
                      <span className={styles.tip} style={{ background: colors[index] }} />
                      {item.skuName.trim()}
                    </span>
                    <span className={styles.queryItemValue}>{item.totalNum}</span>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className={styles.noData}>
              <img className={styles.noDataImg} src={noData} alt="" />
              <div className={styles.noDataText}>
                <span className={styles.text}>暂无库存信息</span>
              </div>
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
}

export default Query;
