import { useEffect, useState } from 'react';
import { Select } from 'antd';
import { Icon } from '@/components';
import { stockHomeHotNum, HotNumResult } from '@/apis';
import { checkPermission } from '@/utils/permission';
import styles from './index.module.less';
import noData from '../../assets/imgs/no-data.png';

const timeOption = [
  {
    label: '本月',
    value: 2,
  },
  {
    label: '本季',
    value: 5,
  },
  {
    label: '本年',
    value: 3,
  },
];

const colors = ['#008CFF', '#16DBCC', '#FFBB38', '#FF82AC', '#A45DF5'];

function Hot() {
  const [type, setType] = useState<2 | 5 | 3>(2);
  const [hotList, setHotList] = useState<HotNumResult[]>([]);
  const [num, setNum] = useState(0);

  useEffect(() => {
    if (checkPermission('AV_001_009_002')) {
      stockHomeHotNum(type).then((res) => {
        // eslint-disable-next-line no-return-assign,no-param-reassign
        setNum(res.list.reduce((total, item) => (total += item.ratio), 0));
        setHotList(res.list);
      });
    }
  }, [type]);

  return (
    <div className={styles.cardBox}>
      <div className={styles.titleHot}>
        <div>动销前五分类</div>
        {checkPermission('AV_001_009_002') && (
          <Select
            defaultValue={2}
            className={styles.select}
            suffixIcon={<Icon name="down" size={16} />}
            onChange={(e) => {
              // @ts-ignore
              setType(e);
            }}
          >
            {timeOption.map((item) => (
              <Select.Option value={item.value} key={item.value}>
                {item.label}
              </Select.Option>
            ))}
          </Select>
        )}
      </div>
      <div className={styles.hotList}>
        {hotList.length > 0 && checkPermission('AV_001_009_002') ? (
          <>
            <div className={styles.hot}>
              {hotList.map((item, index) => (
                <span
                  key={item.categoryId}
                  className={styles.hotRatio}
                  style={{ background: colors[index], width: `${(item.ratio / num) * 100}%` }}
                />
              ))}
            </div>
            {hotList.map((item, index) => (
              <div key={item.categoryId}>
                <div className={styles.hotItem}>
                  <span className={styles.skuName}>
                    <span className={styles.tip} style={{ background: colors[index] }} />
                    {item.categoryName}
                  </span>
                  <span className={styles.ratio}>{item.ratioStr}%</span>
                </div>
              </div>
            ))}
          </>
        ) : (
          <div className={styles.noData}>
            <img className={styles.noDataImg} src={noData} alt="" />
            <div className={styles.noDataText}>暂无分类信息</div>
          </div>
        )}
      </div>
    </div>
  );
}

export default Hot;
