import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { message, Progress } from 'antd';
import classNames from 'classnames';
import { OrderNumResult, stockHomeOrderNum } from '@/apis';
import { checkPermission, testPerm } from '@/utils/permission';
import { Icon } from '@/components';
import styles from './index.module.less';
import has from '../../assets/imgs/has.png';
import no from '../../assets/imgs/no.png';
import write from '../../assets/imgs/write.png';
import orderPermission from '../../utils/order-permission';

const list = [
  {
    id: 9,
    title: '入库单',
    url: '/stock/enter',
    create: '/stock/create?stockType=enter',
    permCode: 'AV_001_002',
    permCodeCreate: 'AV_001_002_001',
  },
  {
    id: 10,
    title: '出库单',
    url: '/stock/out',
    create: '/stock/create?stockType=out',
    permCode: 'AV_001_001',
    permCodeCreate: 'AV_001_001_001',
  },
  {
    id: 11,
    title: '盘点单',
    url: '/stock/taking',
    create: '/stock/create?stockType=taking',
    permCode: 'AV_001_004',
    permCodeCreate: 'AV_001_004_001',
  },
  {
    id: 13,
    title: '调拨单',
    url: '/stock/allot',
    create: '/stock/create?stockType=allot',
    permCode: 'AV_001_003',
    permCodeCreate: 'AV_001_003_001',
  },
  {
    id: 14,
    title: '领用出库单',
    url: '/stock/receive',
    create: '/stock/create?stockType=receive',
    permCode: 'AV_001_008',
    permCodeCreate: 'AV_001_008_001',
  },
];

function Order() {
  const navigate = useNavigate();

  const [active, setActive] = useState(() => {
    if (checkPermission('AV_001_002')) {
      return 9;
    }
    if (checkPermission('AV_001_001')) {
      return 10;
    }
    if (checkPermission('AV_001_003')) {
      return 13;
    }
    return 11;
  });

  const [orderNum, setOrderNum] = useState({
    orderType: 0,
    totalNum: 0,
    draftNum: 0,
    confirmedNum: 0,
    noConfirmedNum: 0,
  });

  const activeInfo = useMemo(() => list.filter((item) => item.id === active)[0], [active]);

  const pushRouter = (url: string) => {
    if (
      orderPermission(activeInfo.id, '编辑') ||
      orderPermission(activeInfo.id, '提交') ||
      orderPermission(activeInfo.id, '删除') ||
      orderPermission(activeInfo.id, '查看') ||
      orderPermission(activeInfo.id, '审批') ||
      orderPermission(activeInfo.id, '取消')
    ) {
      navigate(url);
    } else {
      message.error('暂无权限，请联系公司管理员开通');
    }
  };

  useEffect(() => {
    if (checkPermission(['AV_001_001', 'AV_001_002', 'AV_001_003', 'AV_001_004', 'AV_001_008'])) {
      stockHomeOrderNum().then((res) => {
        const orderInfo = (res.list &&
          res.list.filter((item: OrderNumResult) => item.orderType === active)[0]) || {
          orderType: 0,
          totalNum: 0,
          confirmedNum: 0,
          noConfirmedNum: 0,
          draftNum: 0,
        };
        setOrderNum(orderInfo);
      });
    }
  }, [active]);

  return (
    <div>
      <div className={styles.card}>
        <div className={styles.tabs}>
          {list.map(
            (item) => (
              // (item.id === 9 && checkPermission('AV_001_002')) ||
              // (item.id === 10 && checkPermission('AV_001_001')) ||
              // (item.id === 11 && checkPermission('AV_001_004')) ||
              // (item.id === 13 && checkPermission('AV_001_003')) ||
              // (item.id === 14 && checkPermission('AV_001_008')) ? (
              <div
                role="button"
                tabIndex={item.id}
                key={item.id}
                className={active === item.id ? styles.active : styles.item}
                onClick={() => {
                  setActive(item.id);
                }}
              >
                {item.title}
              </div>
            )
            // ) : null
          )}
        </div>
        <div className={styles.orderBox}>
          {/* {orderNum.totalNum ? ( */}
          <div
            role="button"
            tabIndex={0}
            className={styles.boxProgress}
            onClick={() => {
              pushRouter(activeInfo.url);
            }}
          >
            <Progress
              width={126}
              type="circle"
              strokeColor={{
                '0%': '#006EFF',
                '50%': '#57E3FF',
                '100%': '#45E763',
              }}
              trailColor="#F5F6FA"
              percent={(orderNum.confirmedNum / orderNum.totalNum) * 100}
              strokeWidth={10}
              format={() => orderNum.totalNum}
              className={styles.progress}
            />
            <div className={styles.progressText}>
              总{`${activeInfo.title === '领用出库单' ? '领用单' : activeInfo.title}`}/条
            </div>
          </div>
          <div className={styles.boxText}>
            <div>
              <div
                tabIndex={0}
                role="button"
                className={styles.boxProgress}
                onClick={() => {
                  pushRouter(`${activeInfo.url}?orderStatus=1`);
                }}
              >
                <div className={styles.label}>
                  <img className={styles.img} src={has} alt="" />
                  <span>已审批/条</span>
                </div>
                <div className={classNames(styles.value, 'mb-2')}>
                  <span className={styles.valueFont}>{orderNum.confirmedNum}</span>
                </div>
              </div>
              <div
                tabIndex={0}
                role="button"
                className={styles.boxProgress}
                onClick={() => {
                  pushRouter(`${activeInfo.url}?orderStatus=0`);
                }}
              >
                <div className={styles.label}>
                  <img className={styles.img} src={no} alt="" />
                  <span>待审批/条</span>
                </div>
                <div className={classNames(styles.value, 'mb-2')}>
                  <span className={styles.valueFont}>{orderNum.noConfirmedNum}</span>
                </div>
              </div>
              <div
                tabIndex={0}
                role="button"
                className={styles.boxProgress}
                onClick={() => {
                  pushRouter(`${activeInfo.url}?orderStatus=2`);
                }}
              >
                <div className={styles.label}>
                  <img className={styles.img} src={write} alt="" />
                  <span>草稿/条</span>
                </div>
                <div className={styles.value}>
                  <span className={styles.valueFont}>{orderNum.draftNum}</span>
                </div>
              </div>
            </div>
          </div>
          {/* ) : (
            <div className={styles.noData}>
              <img className={styles.img} src={noData} alt="" />
              <div className={styles.noDataText}>暂无单据信息</div>
            </div>
          )} */}
        </div>
        {/* {isPerm && ( */}
        <div
          className={styles.btn}
          role="button"
          tabIndex={0}
          onClick={() => {
            if (!testPerm(activeInfo.permCodeCreate)) {
              return;
            }
            pushRouter(`${activeInfo.create}`);
          }}
        >
          <Icon name="plus" className="mr-1" size={16} />
          {`新建${activeInfo.title}`}
        </div>
        {/* )} */}
      </div>
    </div>
  );
}
export default Order;
