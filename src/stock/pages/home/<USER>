import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, message } from 'antd';
import { Icon } from '@/components';
import { stockHomeWarehouseNum, WarehouseResult } from '@/apis';
import { checkPermission } from '@/utils/permission';
import styles from './index.module.less';
import noData from '../../assets/imgs/no-data.png';
import warehouse1 from '../../assets/imgs/warehouse-1.png';
import warehouse2 from '../../assets/imgs/worehouse-2.png';

function Warehouse() {
  const navigate = useNavigate();

  const [warehouse, setWarehouse] = useState<WarehouseResult[]>([]);
  const [total, setTotal] = useState(0);

  const goList = (val: string) => {
    if (checkPermission('AV_001_007')) {
      navigate(val);
    }
  };

  useEffect(() => {
    if (checkPermission('AV_001_007')) {
      stockHomeWarehouseNum().then((res) => {
        setTotal(res.pagination.count || res.list.length);
        const arr = res.list.slice(0, 2);
        setWarehouse(arr);
      });
    }
  }, []);

  return (
    <div
      role="button"
      tabIndex={0}
      onClick={() => {
        if (checkPermission('AV_001_007_001')) {
          goList('/stock/warehouse');
        } else {
          message.error('暂无权限，请联系公司管理员开通');
        }
      }}
      className={styles.cardBox}
    >
      <div className={styles.titleWarehouse}>
        <span>
          <span>仓库列表</span>
          <span className={styles.warehouseCount}>共{total}个仓库</span>
        </span>
        <Icon name="right" />
      </div>
      <div className={styles.warehouseList}>
        {warehouse && warehouse.length > 0 ? (
          warehouse.map((item, index) => (
            <div className={styles.warehouseItem} key={item.warehouseNo}>
              <div className={styles.each}>
                <img className={styles.imgBox} src={index === 0 ? warehouse1 : warehouse2} alt="" />
                <span className={styles.name}>{item.warehouseName}</span>
              </div>
              <div className={styles.each}>
                <span className={styles.font}>{item.skuTotalNum}</span>
                <span className={styles.unit}> 件</span>
              </div>
            </div>
          ))
        ) : (
          <div className={styles.noData}>
            <img className={styles.noDataImg} src={noData} alt="" />
            {/* {checkPermission('AV_001_007') && checkPermission('AV_001_007_002') && ( */}
            <Button
              onClick={() => {
                goList('/stock/warehouse?type=add');
              }}
              size="small"
              type="primary"
            >
              新建仓库
            </Button>
            {/* )} */}
          </div>
        )}
      </div>
    </div>
  );
}

export default Warehouse;
