@import 'styles/mixins/mixins';

.card,
.cardBox {
  display: block;
  margin-bottom: 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  outline: none;

  :global {
    .ant-spin-nested-loading {
      min-height: 0 !important;

      .ant-spin {
        min-height: 0 !important;
      }
    }
  }
}

.valueFont {
  font-size: 18px;
}

.tabs {
  font-size: 16px;
  display: flex;
  padding-top: 10px;
  overflow: hidden;
  border-radius: 18px 18px 0 0;

  & .item,
  .active {
    color: #040919;
    font-size: 16px;
    height: 48px;
    line-height: 48px;
    flex: 1;
    text-align: center;
    cursor: pointer;
    outline: none;
    white-space: nowrap;
  }

  .item:hover {
    color: #00c6ff;
  }

  .active {
    font-size: 18px;
    font-weight: 600;
  }
}

.orderBox,
.orderBoxNodata {
  display: flex;
  height: 180px;
  padding: 20px 30px;

  :global {
    .ant-progress-text {
      color: black !important;
    }
  }
}

.orderBoxNodata {
  height: 280px;
}

.boxProgress {
  position: relative;
  cursor: pointer;
}

.progress {
  :global {
    .ant-progress-text {
      font-size: 18px;
      top: 42% !important;

      &:hover {
        color: #00c6ff !important;
      }
    }
  }
}

.progressText {
  color: #888b98;
  font-size: 12px;
  position: absolute;
  top: 80px;
  left: 34px;
}

.btn {
  display: flex;
  width: 100%;
  height: 50px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  border-top: 1px solid #f3f3f3;
}

.boxText {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: end;
  flex-direction: column;

  & .label {
    color: #888b98;
    display: flex;
    margin-bottom: 3px;
    align-items: center;
    cursor: pointer;

    & .img {
      margin-top: -1px;
      margin-right: 4px;
    }
  }

  & .value {
    color: #040919;
    font-size: 24px;
    font-weight: 500;
    text-align: right;
    cursor: pointer;

    &:hover {
      color: #00c6ff;
    }
  }
}

.cardBox {
  padding: 0 20px;
  overflow: hidden;
}

.title,
.titleHot,
.titleWarehouse {
  color: #040919;
  font-size: 18px;
  font-weight: 500;
  padding: 20px 0;
}

.title {
  padding: 20px 0 14px;
  cursor: pointer;
}

.titleWarehouse {
  display: flex;
  justify-content: space-between;
}

.warn {
  display: flex;
  height: 87px;
  padding: 10px 0 20px;
  justify-content: space-between;
  align-items: center;

  & .num {
    color: #040919;
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 5px;

    &:hover {
      color: #00c6ff;
    }
  }

  & .tip {
    color: #888b98;
  }

  & .short {
    color: #ea1c26;
    margin-right: 12px;
  }

  & .exceed {
    color: #008cff;
    margin-right: 12px;
  }

  & .numItem {
    color: #040919;

    &:hover {
      color: #00c6ff;
    }

    .num {
      font-size: 16px;
    }
  }
}

.warehouseList {
  height: 107px;
}

.warehouseItem {
  color: #040919;
  display: flex;
  height: 44px;
  padding-right: 8px;
  justify-content: space-between;

  &:hover {
    border-radius: 8px;
    background-color: #d9eeff;
  }

  & .each {
    display: flex;
    align-items: center;

    &:hover {
      color: #00c6ff;
    }
  }

  .imgBox {
    width: 32px;
    height: 32px;
    margin-right: 8px;
  }

  .name {
    font-size: 15px;
    display: inline-block;
    width: 130px;
    .text-overflow();
  }

  .font {
    font-size: 16px;
  }

  .unit {
    color: #040919;
    font-size: 15px;
    margin-left: 12px;
  }
}

.warehouseCount {
  color: #888b98;
  font-size: 14px;
  margin-left: 8px;
}

.queryList {
  display: flex;
  align-items: center;
  height: 270px;
}

.queryListBox {
  flex: 1;
}

.query {
  width: 170px;
  height: 170px;
  margin: 0 auto;
  margin-right: 40px;
}

.queryText {
  color: #888b98;
  font-size: 12px;
  display: inline-block;
  width: 170px;
  text-align: center;
}

.titleHot {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select {
  width: 86px;
}

.queryItem {
  color: #040919;
  display: flex;
  width: 100%;
  margin-bottom: 10px;
  justify-content: space-between;

  &:hover {
    color: #00c6ff;
  }

  & .tip {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-top: 6px;
    margin-right: 8px;
    border-radius: 50%;
    vertical-align: top;
  }

  & .name {
    display: inline-block;
    flex: 1;
    width: 40px;
    .text-overflow();

    text-align: left;
  }

  .queryItemValue {
    padding-left: 10px;
  }
}

@media (min-width: 1150px) {
  .queryItemValue {
    display: none;
  }
}

@media (min-width: 1370px) {
  .queryItemValue {
    display: block;
  }
}

.hotList {
  height: 232px;
}

.hot {
  display: flex;
  width: 100%;
  height: 22px;
  margin-bottom: 18px;
  border-radius: 4px;
  background-color: #b1b3be;
  overflow: hidden;

  & .hotRatio {
    display: inline-block;
    height: 100%;
  }
}

.hotItem {
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;

  & .tip {
    display: inline-block;
    width: 8px;
    height: 8px;
    margin-right: 8px;
    border-radius: 50%;
  }

  & .skuName {
    color: #888b98;
    display: flex;
    align-items: center;
  }

  & .ratio {
    font-weight: 500;
  }
}

.noData {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  & .noDataImg {
    display: inline-block;
    width: 137px;
    height: 67px;
    margin-bottom: 10px;
    margin-left: 50px;
  }
}

.noDataText {
  color: #040919;
  display: flex;
  margin-bottom: 12px;
  align-items: center;

  & .text {
    margin-right: 10px;
  }
}

.noDataTip {
  color: #b1b3be;
  font-size: 12px;
  width: 260px;
  text-align: center;
}

.warnitem {
  display: flex;
  align-items: center;
}

.warnImg {
  width: 13px;
  height: 16px;
  margin-right: 4px;
}
