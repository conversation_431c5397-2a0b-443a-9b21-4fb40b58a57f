@import 'styles/mixins/mixins';

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.title {
  color: #040919;
  font-size: 18px;
  font-weight: 500;
  padding: 20px 0;
}

.list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.item {
  color: #040919;
  width: 25%;
  margin-bottom: 16px;
  text-align: center;
  cursor: pointer;
}

.itemImg {
  width: 50px;
  height: 50px;
  margin-bottom: 16px;
}
