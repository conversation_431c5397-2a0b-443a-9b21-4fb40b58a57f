.labelItem {
  width: auto;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.headButton {
  cursor: pointer;
  border-radius: 8px;

  &:hover {
    background: #fff;
    color: #008cff;
  }
}

.reportPage {
  height: 100%;
  padding: 20px;
  box-sizing: border-box;
}

.table {
  :global {
    .ant-table-cell {
      padding: 9px 5px;
      border-bottom: none;
    }

    td.ant-table-cell {
      padding-top: 10px;
      padding-bottom: 10px;
      border-bottom: none !important;
      cursor: pointer;
    }

    .ant-table-cell:first-child {
      padding-left: 20px;
    }

    .ant-table-cell:last-child {
      padding-right: 36px;
    }

    th.ant-table-cell {
      color: #040919;
      font-weight: 600;
      height: 38px;
    }

    th.ant-table-cell::before {
      display: none;
    }

    .ant-input-number {
      font-size: 12px;
    }

    .ant-input-number input {
      padding: 0 5px;
      text-align: center;
    }

    .ant-input-number + span {
      margin-left: 4px;
    }

    .ant-table-filter-column {
      justify-content: center;
    }

    .ant-table-filter-column .ant-table-column-title {
      flex: none;
      max-width: calc(100% - 24px);
    }

    .ant-table-filter-trigger {
      margin-left: 0;
    }

    .ant-table-selection-col {
      width: 41px;
      min-width: 41px;
      max-width: 41px;
    }

    td.ant-table-cell-ellipsis > div {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      white-space: normal;
      word-break: normal;
    }

    th.ant-table-cell:last-child {
      border-bottom-right-radius: 10px;
    }

    th.ant-table-cell:first-child {
      border-bottom-left-radius: 10px;
    }

    td.ant-table-cell:first-child {
      border-bottom-left-radius: 10px;
      border-top-left-radius: 10px;
    }

    td.ant-table-cell:last-child {
      border-bottom-right-radius: 10px;
      border-top-right-radius: 10px;
    }

    .ant-table-row:hover td.ant-table-cell {
      background: rgb(217 238 255);
    }

    .ant-table-row {
      height: 72px;
    }

    .ant-table-placeholder .ant-table-cell {
      padding: 0;
    }
  }
}

.pagination {
  padding: 10px 0;
  padding-top: 0;
  position: absolute;
  right: 20px;
  bottom: 2px;
}

.emptyBox {
  display: flex;
  margin-top: 40px;
  overflow: hidden;
  justify-content: center;
  position: sticky;
  right: 0;
  left: 0;
  align-items: center;
}

.button {
  display: inline-block;
  width: 32px;
  height: 32px;
  line-height: 1;
  padding: 5px 5px 4px;
  border: 1px solid transparent;
  border-radius: 10px;
  background-color: transparent;
  cursor: pointer;
  vertical-align: top;

  &:hover {
    color: @primary-text-colors[secondary];
    border-color: @white;
    background-color: @white;
  }
}
