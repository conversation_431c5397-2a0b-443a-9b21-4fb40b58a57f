import {
  BatchCostPageType,
  postPlatQueryReportExportAllot,
  postQueryReportAllotPage,
  ReportAllotParams,
} from '@/apis';
import { Context, Icon, Empty, List } from '@/components';
import { checkPermission } from '@/utils/permission';
import { useMount, useUnmount } from 'ahooks';
import { Dropdown, Menu } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import dayjs from 'dayjs';
import { debounce } from 'lodash';
import { useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import TransferReportFilter from '../../containers/transfer-report-filter';
import styles from './index.module.less';

function TransferReport() {
  const navigate = useNavigate();
  const [reportVisible, setReportVisible] = useState(false);
  const wrapEl = useRef(null as unknown as HTMLDivElement);
  const [contextHeight, setContextHeight] = useState(0);
  const [ids, setIds] = useState<number[]>([]);
  const [emptyWidth, setEmptyWidth] = useState(0);
  const [filterParams, setFilterParams] = useState<ReportAllotParams>({
    key: '',
    startDate: String(dayjs().subtract(1, 'month').valueOf()),
    endDate: String(dayjs().valueOf()),
    orignWarehouseIds: [],
    destinationWarehouseIds: [],
    orderStatusList: [],
  });
  const filterOptions = useRef([
    { label: '批次库存查询', value: `/stock/batch` },
    { label: '批次成本报表', value: `/stock/report` },
    { label: '综合查询报表', value: `/stock/comprehensive-statement` },
    { label: '调拨单明细报表', value: `/stock/transfer-report` },
  ]);
  const selectBatch = useRef({
    skuId: 0,
    skuBatchNo: '',
    unitName: '',
  });

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const onExport = () => {
    const data = {
      ...filterParams,
      isAll: ids.length ? 0 : 1,
      ids,
    };
    postPlatQueryReportExportAllot(data).then((res) => {
      if (res.type !== 'application/json') {
        const URL = window.webkitURL || window.URL;
        const url = URL.createObjectURL(res);
        urlDownload(url, `调拨单报表.xls`);
      }
    });
  };

  const quickFilterNode = (
    <Context.QuickFilter
      label="显示"
      dropdownMatchSelectWidth={130}
      options={filterOptions.current}
      defaultValue="/stock/transfer-report"
      // value={currentUlr.current}
      onChange={(value) => {
        if (value) navigate(value);
      }}
    />
  );

  const head = (
    <Context.Head
      onSearch={(e) => {
        setFilterParams((val) => ({ ...val, key: e.trim() }));
      }}
      onFilter={() => {
        // if (testPerm('AV_001_013_003')) {
        setReportVisible(true);
        // }
      }}
      isFilterActive
      placeholder="商品信息"
      title={[
        {
          title: '仓库管理',
          to: '/stock',
        },
        '查询报表',
      ]}
      quickFilter={quickFilterNode}
      extra={
        checkPermission('AV_001_013_002_002') ? (
          <Context.HeadTool>
            <Dropdown
              overlay={
                <Menu
                  items={[
                    {
                      key: '2',
                      label: (
                        <div role="button" tabIndex={0} onClick={onExport}>
                          导出全部
                        </div>
                      ),
                    },
                  ]}
                />
              }
              placement="top"
            >
              <button type="button" className={styles.button}>
                <Icon name="shangchuan" size={20} />
              </button>
            </Dropdown>
          </Context.HeadTool>
        ) : null
      }
    />
  );

  const columns: ColumnsType<BatchCostPageType> = [
    {
      title: '单据编号',
      width: '200px',
      align: 'center',
      dataIndex: 'orderNo',
    },
    {
      title: '调出仓库',
      width: '135px',
      align: 'center',
      render: (item) => <div className={styles.labelItem}>{item.orignWarehouseName}</div>,
    },
    {
      title: '调入仓库',
      width: '135px',
      align: 'center',
      render: (item) => <div className={styles.labelItem}>{item.destinationWarehouseName}</div>,
    },

    {
      title: '单据状态',
      width: '130px',
      align: 'center',
      dataIndex: 'orderStatusStr',
    },
    {
      title: '商品编码',
      width: '165px',
      align: 'center',
      dataIndex: 'skuCode',
    },
    {
      title: '商品名称',
      width: '165px',
      align: 'center',
      dataIndex: 'skuName',
    },
    {
      title: '规格',
      width: '130px',
      align: 'center',
      dataIndex: 'standardJson',
    },
    {
      title: '条形码',
      width: '130px',
      align: 'center',
      render: (item) => <div className={styles.labelItem}>{item.barsCode || '--'}</div>,
    },
    {
      title: '单位',
      width: '100px',
      align: 'center',
      dataIndex: 'unitName',
    },
    {
      title: '数量',
      width: '100px',
      align: 'center',
      dataIndex: 'quantity',
    },
    {
      title: '单据时间',
      width: '130px',
      align: 'center',
      render: (item) => (
        <div className={styles.labelItem}>
          {item.orderDate ? dayjs(item.orderDate).format('YYYY-MM-DD') : '--'}
        </div>
      ),
    },
    {
      title: '业务员',
      width: '130px',
      align: 'center',
      dataIndex: 'operator',
    },
    {
      title: '创建时间',
      width: '230px',
      align: 'center',
      render: (item) => (
        <div className={styles.labelItem}>
          {item.createTime ? dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss') : '--'}
        </div>
      ),
    },
  ];

  const resize = debounce(() => {
    if (wrapEl.current) {
      setContextHeight(wrapEl.current.offsetHeight);
      setEmptyWidth(wrapEl.current.offsetWidth);
    }
  }, 100);

  useMount(() => {
    resize();
    window.addEventListener('resize', resize, false);
  });

  useUnmount(() => {
    window.removeEventListener('resize', resize, false);
  });

  return (
    <Context
      head={head}
      permission="AV_001_013_002"
      style={{ minWidth: '900px' }}
      wrapStyle={{ overflowX: 'auto' }}
    >
      <List
        columns={columns}
        className={styles.table}
        request={postQueryReportAllotPage}
        params={filterParams}
        rowKey={(data, index) => `ph_${index}`}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          selectedRowKeys: ids,
          onChange(e) {
            // @ts-ignore
            setIds(e);
          },
        }}
        onRow={(e) => ({
          onClick: () => {
            selectBatch.current = {
              skuId: e.skuId,
              skuBatchNo: e.skuBatchNo,
              unitName: e.unitName,
            };
          },
        })}
        locale={{
          emptyText: (
            <div
              className={styles.emptyBox}
              style={{
                height: `${(((contextHeight || 600) > 200 && contextHeight - 2) || 600) - 130}px`,
                width: `${emptyWidth - 47}px`,
              }}
            >
              <Empty />
            </div>
          ),
        }}
      />
      <TransferReportFilter
        visible={reportVisible}
        data={filterParams}
        confirm={(e) => {
          setFilterParams((val) => ({ ...val, ...e }));
          setReportVisible(false);
        }}
        onClose={() => setReportVisible(false)}
      />
    </Context>
  );
}

export default TransferReport;
