import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Context, List, ListInstance } from '@/components';
import { Button, message, Modal, Popover } from 'antd';
import {
  getPlatReceiveOrderPage,
  postPlatReceiveBatch,
  postPlatReceiveOrderDelete,
  ReceiveListParams,
  ReceiveListResult,
  getPlatReceiveOrderDetail,
  postPlatReceiveOrderSave,
  postPlatReceiveOrderUpdate,
  postPlatOrderDelete,
  postPlatReceiveOrderSubmit,
} from '@/apis';
import { useNavigate, useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';
import goodsNoStock from '@@/order-rewrite/components/goods-no-stock';
import OrderMessage from '../../components/order-message/index';
import BtnDropdown from '../../components/btn-dropdown/index';
import ReceiveAdd from '../../containers/receive-add';
import Filter from './filter';
import styles from './index.module.less';
import OrderDetail from '../../containers/order-detail';
import orderPermission from '../../utils/order-permission';
import PrintPage, { PrintPageEv } from '../../components/react-to-print';

const filterList = [
  { label: '全部', value: '' },
  { label: '已审批', value: '1' },
  { label: '待审批', value: '0' },
  { label: '草稿', value: '2' },
  { label: 'OA审批中', value: '3' },
];

function Receive() {
  const navigator = useNavigate();
  const [orderStatusParams] = useSearchParams({ orderStatus: '' });
  const showSearch = orderStatusParams.get('showSearch') || '';
  const orderStatusStr = orderStatusParams.get('orderStatus');
  const [orderStatus, setOrderStatus] = useState(orderStatusStr || '');
  const [showOrderFilter, setShowOrderFilter] = useState<boolean>(false);
  const refreshRef = useRef(null as unknown as ListInstance);
  const [ids, setIds] = useState<number[]>([]);
  const [receiveId, setReceiveId] = useState(0);
  const [showOrderAdd, setShowOrderAdd] = useState(false);
  const [showOrderDetail, setShowOrderDetail] = useState(false);

  const [skuParams, setSkuParams] = useState<ReceiveListParams>({
    warehouseIds: [],
    orderStatus: orderStatusStr,
    startDate: 0,
    endDate: 0,
    key: '',
    pageSize: 10,
    pageNo: 1,
  });
  const printPageRef = useRef(null as null | PrintPageEv);
  //   删除出库单
  const deleteOrder = (id: number) => {
    postPlatReceiveOrderDelete({ id }).then(() => {
      message.success('删除成功');
      refreshRef.current.refresh();
    });
  };

  //   提交出库单
  const submitOrder = (id: number) => {
    postPlatReceiveOrderSubmit({ id }).then(() => {
      message.success('提交成功');
      refreshRef.current.refresh();
    });
  };

  //   批量/单个 取消或者审核出库单
  const batchOrder = (id: number[], status: number, isBatch: number) => {
    if (status === 3) {
      message.warning('OA审批中');
      return;
    }
    postPlatReceiveBatch({ ids: id, orderStatus: status })
      .then((res) => {
        const {
          isNeedInitWorkflowForm,
          processDefinitionKey,
          processDefinitionName,
          businessOrderNo,
          businessType,
        } = res;
        if (isNeedInitWorkflowForm) {
          window.open(
            `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
              {
                businessOrderNo,
                businessType,
              }
            )}`
          );
        } else {
          message.success(`${isBatch ? '批量' : ''}${status ? '审核' : '取消'}成功`);
        }
        refreshRef.current.refresh();
      })
      .catch((e) => {
        if (e.code === 5005012) {
          goodsNoStock({ list: JSON.parse(e.message) });
        } else {
          message.error(e.message);
        }
      });
  };

  //   组件传过来的按钮名称以及list数据
  const btnConfirm = (name: string, item?: ReceiveListResult) => {
    if (ids.length === 0 && (name === '批量审批' || name === '批量取消')) {
      message.warning('请先选择订单');
      return;
    }
    if (name === '编辑' && item) {
      if (!orderPermission(14, '编辑')) {
        message.error('暂无权限，请联系公司管理员开通');
      } else {
        // setReceiveId(item.id);
        // setShowOrderAdd(true);
        navigator(`/stock/create?stockType=receive&id=${item.id}`);
      }
      return;
    }
    if (!orderPermission(14, name)) {
      message.error('暂无权限，请联系公司管理员开通!');
      return;
    }
    Modal.confirm({
      title: '提示',
      icon: '',
      okText: '确认',
      cancelText: '取消',
      centered: true,
      content: `是否${name}`,
      onOk: () => {
        if (name === '删除' && item) {
          deleteOrder(item.id);
        }
        if (name === '提交' && item) {
          submitOrder(item.id);
        }
        if (name === '取消审批' && item) {
          batchOrder([item.id], 0, 0);
        }
        if (name === '审批' && item) {
          batchOrder([item.id], 1, 0);
        }
        if (name === '批量审批') {
          batchOrder(ids, 1, 1);
        }
        if (name === '批量取消') {
          batchOrder(ids, 0, 1);
        }
      },
    });
  };

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <>
            <div
              role="button"
              tabIndex={0}
              className={styles.batchItem}
              onClick={() => {
                btnConfirm('批量取消');
              }}
            >
              批量取消
            </div>
            <div
              role="button"
              tabIndex={0}
              className={styles.batchItem}
              onClick={() => {
                btnConfirm('批量审批');
              }}
            >
              批量审批
            </div>
          </>
        }
      >
        <span className={styles.batchBtn}>批量操作</span>
      </Popover>
    ),
    [ids] // eslint-disable-line
  );

  const isFilter = useMemo(() => {
    if (skuParams.endDate || skuParams.startDate || skuParams?.warehouseIds?.length) {
      return true;
    }
    return false;
  }, [skuParams]);

  useEffect(() => {
    if (orderStatusParams.get('type') === 'add') {
      setTimeout(() => {
        setShowOrderAdd(true);
      });
    }
  }, [orderStatusParams]);

  return (
    <Context
      //   permission="AV_001_006"
      head={
        <Context.Head
          showSearch={!!showSearch}
          onSearch={(key) => {
            setSkuParams({ ...skuParams, key });
          }}
          onFilter={() => {
            setShowOrderFilter(true);
          }}
          isFilterActive={isFilter}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              value={orderStatus}
              // @ts-ignore
              options={filterList}
              dropdownMatchSelectWidth={100}
              onChange={(e) => {
                setSkuParams({ ...skuParams, orderStatus: e });
                setOrderStatus(e);
              }}
            />
          }
          placeholder="领用人员| 公司 | 订单编号"
          title={[
            {
              title: '库存管理',
              to: '/stock',
            },
            '领用出库单',
          ]}
          extra={
            checkPermission('AV_001_008_001') && (
              <Context.HeadTool>
                <Button
                  size="small"
                  type="primary"
                  onClick={() => {
                    // setReceiveId(0);
                    // setShowOrderAdd(true);
                    navigator('/stock/create?stockType=receive');
                  }}
                >
                  新建领用单
                </Button>
              </Context.HeadTool>
            )
          }
        />
      }
    >
      <List
        // @ts-ignore
        ref={refreshRef}
        onRow={(e) => ({
          onClick: () => {
            // eslint-disable-next-line no-console
            // setReceiveId(e.id);
            // setShowOrderDetail(true);
            navigator(`/stock/detail?stockType=receive&id=${e.id}`);
          },
        })}
        footer={footer}
        request={getPlatReceiveOrderPage}
        params={skuParams}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys: ids,
          onChange(e) {
            // @ts-ignore
            setIds(e);
          },
          getCheckboxProps: (record) => ({
            disabled: record.orderStatus === 3 || record.orderStatus === 1,
          }),
        }}
        rowKey="id"
        columns={[
          {
            title: '订单信息',
            width: '35%',
            render: (item) => (
              <div>
                <OrderMessage item={item} />
              </div>
            ),
          },
          {
            title: '出库仓库',
            width: '35%',
            align: 'center',
            dataIndex: 'warehouseName',
          },
          {
            title: '操作信息',
            width: '20%',
            align: 'center',
            render: (item) => (
              <div>
                <div>{item?.operator}</div>
                {item.orderDate ? (
                  <div className={styles.grey}>
                    {dayjs(item.orderDate).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                ) : null}
              </div>
            ),
          },
          {
            title: '操作',
            width: '10%',
            align: 'center',
            render: (item) => (
              <BtnDropdown
                item={item}
                confirm={(name: string) => {
                  btnConfirm(name, item);
                }}
              />
            ),
          },
        ]}
      />
      <Filter
        visible={showOrderFilter}
        onClose={() => {
          setShowOrderFilter(false);
        }}
        startDate={skuParams.startDate}
        endDate={skuParams.endDate}
        confirm={(e) => {
          // eslint-disable-next-line no-console
          setSkuParams({ ...skuParams, ...e });
          setShowOrderFilter(false);
        }}
      />
      <ReceiveAdd
        // @ts-ignore
        orderCreate={receiveId ? postPlatReceiveOrderUpdate : postPlatReceiveOrderSave}
        // @ts-ignore
        orderDetail={getPlatReceiveOrderDetail}
        visible={showOrderAdd}
        orderType={14}
        id={receiveId}
        onClose={() => {
          // setReceiveId(0);
          setShowOrderAdd(false);
        }}
        submit={() => {
          // refreshRef.current.refresh();
          // setReceiveId(0);
          setShowOrderAdd(false);
          setSkuParams({ ...skuParams, pageNo: 1 });
        }}
      />
      <OrderDetail
        orderDetele={postPlatOrderDelete}
        orderSubmit={postPlatReceiveOrderSubmit}
        confirm={() => {
          // setReceiveId(0);
          setShowOrderDetail(false);
          refreshRef.current.refresh();
        }}
        id={receiveId}
        visible={showOrderDetail}
        onClose={() => {
          // setReceiveId(0);
          setShowOrderDetail(false);
        }}
        editEntryList={() => {
          setReceiveId(receiveId);
          setShowOrderDetail(false);
          setShowOrderAdd(true);
        }}
        receiveDetail={getPlatReceiveOrderDetail}
        orderType={14}
        orderPrintPdf={() => {
          printPageRef.current?.handlePrint();
        }}
      />
      <PrintPage ref={printPageRef} />
    </Context>
  );
}

export default Receive;
