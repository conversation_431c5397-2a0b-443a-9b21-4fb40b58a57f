import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { Icon, Drawer, DatePicker } from '@/components';
import classNames from 'classnames';
import dayjs, { Dayjs } from 'dayjs';
import { postPlatWareHouse, WareListRusult } from '@/apis';
import { Form, FormInstance, Spin } from 'antd';
import Warehouse from '../../containers/filter-warehouse';
import styles from './filter.module.less';

interface FilterParams {
  startDate?: number;
  endDate?: number;
  warehouseIds?: number[];
}

interface FilterPropps {
  visible: boolean;
  startDate?: number;
  endDate?: number;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  confirm: (arg: FilterParams) => void;
}

function OrderFilter({
  visible,
  confirm,
  startDate,
  endDate,
  onClose,
  ...props
}: PropsWithChildren<FilterPropps>) {
  const [warehouses, setWarehouse] = useState<WareListRusult[]>([]);
  const [selectWarehouse, setSelectWarehouse] = useState<number[]>([0]);
  const [showWarehouse, setShowWarehouse] = useState(false);
  const [loading, setLoading] = useState(false);
  const [count, setCount] = useState(0);
  const formRef = useRef(null as unknown as FormInstance);
  const [filterParams, setFilterParams] = useState<FilterParams>({
    startDate: 0,
    endDate: 0,
    warehouseIds: [],
  });

  const selectWarehouses = (id: number) => {
    const list = [...selectWarehouse];
    warehouses.forEach((item) => {
      if (item.id === id) {
        if (list.indexOf(id) > -1) {
          list.splice(list.indexOf(id), 1);
        } else {
          list.push(item.id);
        }
      }
    });

    setFilterParams({
      ...filterParams,
      warehouseIds: id ? list.filter((item) => item) : [],
    });
    setSelectWarehouse(id ? list.filter((item) => item) : [0]);
  };

  // (current && current > dayjs().endOf('day')) ||
  const disabledDateStart = (current: Dayjs) =>
    current &&
    current >
      dayjs(
        formRef.current.getFieldsValue().endDate
          ? formRef.current.getFieldsValue().endDate
          : '2033-08'
      ).endOf('day');

  // (current && current > dayjs().endOf('day')) ||
  const disabledDateEnd = (current: Dayjs) =>
    current &&
    current <
      dayjs(
        formRef.current.getFieldsValue().startDate
          ? formRef.current.getFieldsValue().startDate
          : '1999-08'
      ).subtract(0, 'days');

  useEffect(() => {
    if (visible) {
      setLoading(true);
      postPlatWareHouse({
        pageNo: 1,
        pageSize: 4,
      })
        .then((res) => {
          setWarehouse([{ id: 0, warehouseName: '全部仓库', warehouseStatus: 0 }, ...res.list]);
          setCount(res.pagination.count);
          if (startDate) {
            formRef.current.setFieldsValue({
              startDate: dayjs(startDate),
            });
          }
          if (endDate) {
            formRef.current.setFieldsValue({
              endDate: dayjs(endDate),
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [visible]); //eslint-disable-line
  return (
    <Drawer
      title="筛选"
      visible={visible}
      {...props}
      className={styles.filter}
      onClose={() => {
        confirm(filterParams);
      }}
    >
      <Spin spinning={loading}>
        <div className={styles.card}>
          <div className={styles.title}>仓库名称</div>
          <div className={styles.list}>
            {warehouses.map((item, index) => (
              <span
                className={
                  selectWarehouse.includes(item.id as number) ? styles.itemActive : styles.item
                }
                role="button"
                key={item.id}
                tabIndex={index}
                onClick={() => {
                  selectWarehouses(item.id as number);
                }}
              >
                <span className={styles.name}>{item.warehouseName}</span>
              </span>
            ))}
            {count >= 4 ? (
              <span
                role="button"
                tabIndex={0}
                className={styles.itemActive}
                onClick={() => {
                  setShowWarehouse(true);
                }}
              >
                更多仓库
                <Icon name="right" />
              </span>
            ) : null}
          </div>
        </div>
        <div className={styles.card}>
          <div className={styles.title}>操作时间</div>
          <div className={styles.time}>
            <Form ref={formRef}>
              <Form.Item name="startDate">
                <DatePicker
                  onChange={(e) => {
                    setFilterParams({
                      ...filterParams,
                      startDate: dayjs(e).startOf('day').valueOf() || 0,
                    });
                  }}
                  placeholder="请选择开始时间"
                  disabledDate={disabledDateStart}
                  bordered={false}
                />
              </Form.Item>

              <span className={styles.line} />
              <Form.Item name="endDate">
                <DatePicker
                  onChange={(e) => {
                    setFilterParams({
                      ...filterParams,
                      endDate: dayjs(e).startOf('day').valueOf() + 86399999 || 0,
                    });
                  }}
                  placeholder="请选择结束时间"
                  disabledDate={disabledDateEnd}
                  bordered={false}
                />
              </Form.Item>
            </Form>
          </div>
        </div>

        <div className={styles.footer}>
          <div
            role="button"
            tabIndex={0}
            className={styles.footerBtn}
            onClick={() => {
              setSelectWarehouse([0]);
              formRef.current.resetFields();
              setFilterParams({
                startDate: 0,
                endDate: 0,
                warehouseIds: [],
              });
            }}
          >
            重置
          </div>
          <div
            role="button"
            tabIndex={0}
            className={classNames(styles.footerBtn, styles.footerBtnAdd)}
            onClick={() => {
              confirm(filterParams);
              return false;
            }}
          >
            确定
          </div>
        </div>
      </Spin>
      <Warehouse
        confirm={(e: number[]) => {
          setShowWarehouse(false);
          // @ts-ignore
          setSelectWarehouse(e);
          setFilterParams({
            ...filterParams,
            warehouseIds: [...new Set((filterParams.warehouseIds || []).concat(e))],
          });
        }}
        visible={showWarehouse}
        selectWarehouse={selectWarehouse}
        onCloseWarehouse={() => {
          setShowWarehouse(false);
        }}
      />
    </Drawer>
  );
}

OrderFilter.defaultProps = {
  startDate: 0,
  endDate: 0,
};

export default OrderFilter;
