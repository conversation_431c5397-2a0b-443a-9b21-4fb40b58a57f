import { useEffect, useState } from 'react';
import { Switch } from 'antd';
import { Icon } from '@/components';
import { useRequest } from 'ahooks';
import { testPerm } from '@/utils/permission';
import { stockCfgList, StockCfgListResult, stockCfgState } from '@/apis';
import { selectProcessList } from '../../../containers/select-process-list';
import styles from '../index.module.less';

function NodeApproval() {
  const [list, setList] = useState<StockCfgListResult[]>([]);

  const { run } = useRequest(stockCfgList, {
    manual: true,
    onSuccess: (res) => {
      setList(res.list);
    },
  });

  const tipText = (type: number) => {
    switch (type) {
      case 1:
        return '出库';
      case 2:
        return '入库';
      case 6:
        return '盘点';
      case 5:
        return '调拨';
      case 7:
        return '领用申请';
      case 13:
        return '补货计划审核';
      default:
        return '';
    }
  };

  const tipCode = (type: number) => {
    switch (type) {
      case 1:
        return 'AV_001_011_002_001';
      case 2:
        return 'AV_001_011_002_002';
      case 6:
        return 'AV_001_011_002_003';
      case 5:
        return 'AV_001_011_002_004';
      case 7:
        return 'AV_001_011_002_005';
      case 13:
        return 'AV_001_011_002_006';
      default:
        return '';
    }
  };

  const onChangeNode = (
    val: boolean,
    name: string,
    formType: number,
    stockTypeId: number,
    auditBusinessType: number
  ) => {
    const code = tipCode(auditBusinessType);
    if (code.length) {
      if (!testPerm(code)) return;
    }
    if (!name.length) {
      selectProcessList({
        formType,
        selectId: '',
        stockTypeId,
        onConfirm: () => {
          run();
        },
        onCreate: () => {
          window.open('/admin/workflow/design');
        },
        onPreview: (pId, nameVal) => {
          window.open(`/admin/process/applyProcess?pId=${pId}&name=${nameVal}&preview=true`);
        },
      });
    } else {
      stockCfgState({
        id: stockTypeId,
        state: val ? 1 : 0,
      }).then(() => {
        run();
      });
    }
  };

  const onSelectProcess = (
    formType: number,
    stockTypeId: number,
    procDefId: string,
    auditBusinessType: number
  ) => {
    const code = tipCode(auditBusinessType);
    if (code.length) {
      if (!testPerm(code)) return;
    }
    selectProcessList({
      formType,
      selectId: procDefId,
      stockTypeId,
      onConfirm: () => {
        run();
      },
      onCreate: () => {
        window.open('/admin/workflow/design');
      },
      onPreview: (pId, nameVal) => {
        window.open(`/admin/process/applyProcess?pId=${pId}&name=${nameVal}&preview=true`);
      },
    });
  };

  useEffect(() => {
    run();
  }, [run]);

  return (
    <div className={styles.box}>
      {list.map((item) => (
        <div key={item.id} className={styles.nodeItem}>
          <div className={styles.title}>
            <div className={styles.label}>{item.actionName}：</div>
            <Switch
              checked={Boolean(item.state)}
              onChange={(e) =>
                onChangeNode(e, item.procFrmName, item.wfFormType, item.id, item.auditBusinessType)
              }
            />
          </div>
          {item.state > 0 && item.procFrmName.length && (
            <div className={styles.process}>
              <span>审批流程：</span>
              <span
                role="button"
                tabIndex={0}
                className={styles.processBtn}
                onClick={() =>
                  onSelectProcess(item.wfFormType, item.id, item.procDefId, item.auditBusinessType)
                }
              >
                <span>{item.procFrmName}</span>
                <Icon name="right" />
              </span>
            </div>
          )}
          <div className={styles.disc}>
            开启后，仓库进行{tipText(item.auditBusinessType)}，需要OA审批通过
          </div>
        </div>
      ))}
    </div>
  );
}

export default NodeApproval;
