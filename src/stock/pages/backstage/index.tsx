import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Context } from '@/components';
import classNames from 'classnames';
import { checkPermission, testPerm } from '@/utils/permission';
import { useEffect } from 'react';
import styles from './index.module.less';

const items = [
  {
    label: '节点审批管理',
    url: '/stock/backstage/node-approval',
    perm: 'AV_001_011_002',
  },
  {
    label: '仓库管理',
    url: '/stock/backstage/manage',
    perm: 'AV_001_011_001',
  },
];

const isSettingIframe = window.name === 'SystemSettingIframe';

function Backstage() {
  const navigate = useNavigate();
  const location = useLocation();

  const goNavigate = (url: string, perm: string) => {
    if (!testPerm(perm)) {
      return;
    }
    navigate(url);
  };

  useEffect(() => {
    if (!checkPermission('AV_001_011_002')) {
      navigate('/stock/backstage/manage');
    }
  }, [navigate]);

  return (
    <Context
      head={
        <Context.Head
          isBack={!isSettingIframe}
          title={[
            isSettingIframe
              ? '仓库管理'
              : {
                  to: '/stock',
                  title: '仓库管理',
                },
            '管理后台',
          ]}
        />
      }
    >
      <div className={styles.content}>
        <div className={styles.menu}>
          {items.map((item, index) => (
            <div
              role="button"
              tabIndex={index}
              key={item.url}
              className={classNames(styles.menuItem, {
                [styles.menuItemActive]: location.pathname.includes(item.url),
              })}
              onClick={() => goNavigate(item.url, item.perm)}
            >
              {item.label}
            </div>
          ))}
        </div>
        <div className={styles.children}>
          <Outlet />
        </div>
      </div>
    </Context>
  );
}

export default Backstage;
