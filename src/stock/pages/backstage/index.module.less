.content {
  display: flex;
  width: 100%;
  height: 100%;
}

.menu {
  width: 143px;
  height: 100%;
  padding: 20px 0;
  border-right: 1px solid #f3f3f3;
}

.menuItem {
  color: #040919;
  font-size: 16px;
  display: inline-block;
  width: 100%;
  padding: 14px 0 14px 30px;

  &:hover {
    color: #040919;
  }
}

.menuItemActive {
  font-weight: 600;
  position: relative;
  background: #f5f6fa;

  &::before {
    content: '';
    width: 4px;
    height: 31px;
    position: absolute;
    top: 9px;
    left: 0;
    border-radius: 0 2px 2px 0;
    background: #008cff;
  }
}

.children {
  flex: 1;
}

.box {
  padding: 20px 0;
}

.nodeItem {
  margin-bottom: 20px;
}

.title {
  display: flex;
  align-items: center;
  height: 42px;
}

.label {
  font-size: 16px;
  width: 150px;
  text-align: right;
}

.process {
  display: flex;
  height: 32px;
  margin-bottom: 8px;
  padding-left: 150px;
  align-items: center;
}

.processBtn {
  display: flex;
  align-items: center;
  padding-left: 12px;
  cursor: pointer;
}

.disc {
  color: #888b98;
  font-size: 12px;
  padding-left: 150px;
}
