import { useEffect, useState } from 'react';
import { message, Switch } from 'antd';
import { stockReplenishState, stockReplenishSwitch } from '@/apis';
import { useRequest } from 'ahooks';
import { testPerm } from '@/utils/permission';
import styles from '../index.module.less';

function Manage() {
  const [replenishState, setReplenishState] = useState(false);

  const { run } = useRequest(stockReplenishState, {
    manual: true,
    onSuccess: (res) => {
      setReplenishState(Boolean(res.replenishState));
    },
  });

  const onChangeState = () => {
    if (!testPerm('AV_001_011_001_001')) return;
    stockReplenishSwitch({
      replenishState: replenishState ? 0 : 1,
    }).then(() => {
      message.success(`${replenishState ? '关闭' : '开启'}成功`);
    });
    setReplenishState(!replenishState);
  };

  useEffect(() => {
    run();
  }, [run]);

  return (
    <div className={styles.box}>
      <div className={styles.title}>
        <div className={styles.label}>库存短缺补货：</div>
        <Switch checked={replenishState} onClick={onChangeState} />
      </div>
      <div className={styles.disc}>开启后，仓库将根据库存预警，自动生成补货计划</div>
    </div>
  );
}

export default Manage;
