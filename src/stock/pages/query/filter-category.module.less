@import 'styles/mixins/mixins';

.drawer {
  position: absolute !important;
  transition: none !important;

  .level {
    cursor: pointer;
    color: #008cff;
  }

  :global {
    .ant-drawer-content-wrapper {
      height: 70% !important;
      transform: none !important;
    }

    .ant-drawer-content,
    .ant-drawer-header {
      background-color: #fff;
    }

    .ant-drawer-header {
      padding: 16px;
    }

    .ant-drawer-wrapper-body {
      padding-top: 0;
    }
  }
}

.search {
  margin-bottom: 16px;
  background: #fff !important;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);

  :global {
    .ant-input {
      background: #fff !important;
    }
  }
}

.checkboxLabel {
  :global {
    .ant-checkbox + span {
      display: inline-block;
      max-width: 290px;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }
  }
}

.select {
  display: flex;
  flex-wrap: wrap;

  .item {
    display: flex;
    width: 77px;
    height: 36px;
    margin-right: 7px;
    margin-bottom: 12px;
    padding: 5px;
    justify-content: center;
    position: relative;
    align-items: center;
    border: 1px solid rgb(218 219 224 / 100%);
    border-radius: 10px;

    &:nth-child(4n) {
      margin-right: 0;
    }

    & .text {
      display: inline-block;
      width: 67px;
      text-align: center;
      .text-overflow();
    }

    & .del {
      color: #e3e2e2;
      font-size: 20px;
      position: absolute;
      top: -10px;
      right: -10px;
      cursor: pointer;
    }
  }
}

.tier {
  .text {
    font-size: 12px;
    margin-right: 10px;
    cursor: pointer;
  }

  .itemActive {
    color: #888b98;
    cursor: inherit;
  }
}

.list {
  flex-grow: 1;
  overflow: auto;

  :global {
    .ant-checkbox-group {
      width: 100%;
    }
  }

  .level {
    color: #008cff;
    cursor: pointer;
  }

  .gray {
    color: #c6ccd8;
    cursor: not-allowed;
  }
}

.label {
  display: flex;
  width: 100%;
  padding: 12px 0;
  justify-content: space-between;

  .next,
  .nextActive {
    color: rgb(0 140 255 / 100%);
    font-size: 12px;
    cursor: pointer;
  }

  .nextActive {
    color: rgb(177 179 190 / 100%);
    cursor: default;
  }

  :global {
    .el-input__inner {
      border: 1px solid #cad1db !important;
    }

    .el-checkbox__inner {
      width: 16px;
      height: 16px;
      border-radius: 0 !important;

      &::after {
        top: 0 !important;
        left: 4px !important;
      }
    }
  }
}
