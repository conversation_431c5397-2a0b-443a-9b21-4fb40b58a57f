import { MouseEvent, useState, useCallback, useMemo } from 'react';
import { Context, Icon, ListUnit } from '@/components';
import { Dropdown, Menu, message, Popover, Tooltip } from 'antd';
import { user } from '@/store';
import {
  getSkuListPage,
  postPlatExportAll,
  postPlatQueryExportStock,
  postPlatStockBatch,
  skuListParams,
} from '@/apis';
import { shopColumnsProps, columnsProps } from '@/components/list-unit';
import { checkPermission } from '@/utils/permission';
import classNames from 'classnames';
import { useDebounceFn } from 'ahooks';
import styles from '../../styles/list.module.less';
import Filter from './filter';
import Detail from './detail';
import GoodsInfo from '../../components/goods-info';

function Query() {
  const [showFilter, setShowFilter] = useState(false);
  const [showDetail, setShowDetail] = useState(false);
  const param: skuListParams = {
    keyword: '',
    categoryIdList: [],
    minAvailableStock: '',
    maxAvailableStock: '',
    minActualStock: '',
    maxActualStock: '',
    warehouseNos: [],
  };
  const [skuParams, setSkuParams] = useState(param);
  const [skuId, setSkuId] = useState(0);
  const [skuUnit, setSkuUnit] = useState('');
  const [skuIds, setSkuIds] = useState<number[]>([]);

  // const standard = (item: { standardJson: string }) => {
  //   const stand = JSON.parse(item.standardJson);
  //   return stand.map((item1: { name: string; value: string }) => (
  //     <div>
  //       {item1.name}: {item1.value},
  //     </div>
  //   ));
  // };

  const standardFoo = (item: { standardJson: string }) => {
    const stand = JSON.parse(item.standardJson);
    let str = '';
    stand.forEach((item1: { name: string; value: string }) => {
      str += `${item1.name}: ${item1.value},`;
    });
    return str;
  };

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const categoryList = (list: any) => {
    let str;
    if (typeof list === 'string') {
      return list || '--';
    }
    if (list.length === 0) {
      str = '--';
    }
    if (list.length > 0 && list.length <= 2) {
      str = list.map((item: any) => (
        <div key={item.customizeCategoryId}>{item.customizeCategoryName}</div>
      ));
    }
    if (list.length > 2) {
      str = (
        <>
          {list
            .filter((item: any, index: any) => index < 2)
            .map((item: any) => (
              <div
                key={item.customizeCategoryId}
                className={styles.categoryItem}
                title={item.customizeCategoryName}
              >
                {item.customizeCategoryName}
              </div>
            ))}
          <Tooltip
            placement="bottom"
            trigger="hover"
            title={list
              .filter((item: any, index: any) => index >= 2)
              .map((item: any) => (
                <span key={item.customizeCategoryId} className={styles.categoryItemPopover}>
                  {item.customizeCategoryName}
                  <span className={styles.punctuate}>，</span>
                </span>
              ))}
          >
            <div
              role="button"
              tabIndex={0}
              className={styles.lookMore}
              onClick={(e) => e.stopPropagation()}
            >
              查看更多
              <Icon name="down" size={18} />
            </div>
          </Tooltip>
        </>
      );
    }
    return str;
  };
  const isFilter = useMemo(() => {
    if (skuParams?.categoryIdList?.length || skuParams?.warehouseNos?.length) {
      return true;
    }
    return false;
  }, [skuParams]);

  const columns: columnsProps[] = useMemo(() => {
    const colum = [
      {
        title: '单位',
        align: 'center',
        dataIndex: 'unitName',
      },
      {
        title: '实际库存',
        align: 'center',
        render: (item: any) => <div>{item.actualStock}</div>,
      },
      {
        title: '可用库存',
        align: 'center',
        render: (item: any) => <div>{item.availableStock}</div>,
      },
    ];
    if (skuParams?.categoryIdList?.length || skuParams?.warehouseNos?.length) {
      // 如果进行了筛选增加仓库和货位两个字段
      colum.unshift({
        title: '货位',
        align: 'center',
        dataIndex: 'positionName',
      });

      colum.unshift({
        title: '仓库',
        align: 'center',
        dataIndex: 'warehouseName',
      });
    }
    return colum;
  }, [skuParams]);

  const shopColumns: shopColumnsProps[] = [
    {
      width: '35%',
      align: 'left',
      padding: '0 24px 0 0',
      render: (item) => (
        <GoodsInfo
          img={item.images}
          name={item.skuName}
          unit=""
          number={item.skuCode}
          standardId={1}
          imgWidth={52}
          imgHeight={52}
          isShowBarsCode={false}
          skuCode={item.skuCode}
        />
      ),
    },
    {
      align: 'left',
      width: '25%',
      render: (item) => (
        <div className={styles.standard}>
          <span className={styles.cords}>规格: </span>
          <span className={styles.grey} title={standardFoo(item)}>
            {standardFoo(item)}
          </span>
        </div>
      ),
    },
    {
      align: 'left',
      width: '20%',
      render: (item) => (
        <div className={styles.standard}>
          <span className={styles.cords}>条码: </span>
          <span className={styles.grey}>{item.barsCode || '--'}</span>
        </div>
      ),
    },
    {
      align: 'left',
      width: '20%',
      render: (item) => (
        <div className={styles.standard}>
          <span className={styles.cords}>分类:</span>
          <span className={styles.grey}>
            {categoryList(
              item?.customizeCategoryList && item?.customizeCategoryList?.length
                ? item.customizeCategoryList
                : item.categoryName
            )}
          </span>
        </div>
      ),
    },
  ];

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <div
            role="button"
            tabIndex={0}
            className={styles.batchItem}
            onClick={() => {
              if (skuIds.length) {
                if (checkPermission('AV_001_006_002')) {
                  postPlatStockBatch({ skuIds, warehouseNos: skuParams.warehouseNos }).then(
                    (res) => {
                      if (res.type !== 'application/json') {
                        const URL = window.webkitURL || window.URL;
                        const url = URL.createObjectURL(res);
                        urlDownload(url, `批量-库存商品信息.xls`);
                      } else {
                        message.error(`系统开小差，请稍后再试`);
                      }
                    }
                  );
                } else {
                  message.error('暂无权限，请联系公司管理员开通');
                }
              } else {
                message.warning('请先勾选批量导出的商品！');
              }
            }}
          >
            批量导出
          </div>
        }
      >
        <span className={classNames(styles.batchBtn, styles.batchBtnInline)}>批量操作</span>
      </Popover>
    ),
    [skuIds, skuParams.warehouseNos]
  );

  const eportStock = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    postPlatQueryExportStock().then((res) => {
      const URL = window.webkitURL || window.URL;
      const url = URL.createObjectURL(res);
      urlDownload(url, `${user.companyName}-库存报表.xls`);
    });
  };

  const listExport = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    postPlatExportAll().then((res) => {
      const URL = window.webkitURL || window.URL;
      const url = URL.createObjectURL(res);
      urlDownload(url, `${user.companyName}-库存商品信息.xls`);
    });
  };

  const onSearch = useDebounceFn(
    (e: string) => {
      setSkuParams({ ...skuParams, keyword: e });
    },
    { wait: 100 }
  ).run;

  // @ts-ignore
  return (
    <Context
      permission="AV_001_006"
      head={
        <Context.Head
          onSearch={onSearch}
          onFilter={() => {
            setShowFilter(true);
          }}
          isFilterActive={isFilter}
          placeholder="名称 | 条形码 | 编码"
          title={[
            {
              title: '仓库管理',
              to: '/stock',
            },
            '库存查询',
          ]}
          extra={
            checkPermission('AV_001_006_002') ? (
              <Context.HeadTool>
                <Dropdown
                  overlay={
                    <Menu
                      items={[
                        {
                          key: '2',
                          label: (
                            <div role="button" tabIndex={0} onClick={(e) => listExport(e)}>
                              导出全部
                            </div>
                          ),
                        },
                        {
                          key: '1',
                          label: (
                            <div role="button" tabIndex={0} onClick={(e) => eportStock(e)}>
                              导出报表
                            </div>
                          ),
                        },
                      ]}
                    />
                  }
                  placement="top"
                >
                  <button type="button" className={styles.button}>
                    <Icon name="shangchuan" size={20} />
                  </button>
                </Dropdown>
              </Context.HeadTool>
            ) : null
          }
        />
      }
      style={{ backgroundColor: 'rgba(0,0,0,0)' }}
    >
      <ListUnit
        onRow={(e) => {
          setSkuId(e.skuId);
          setSkuUnit(e.unit);
          setShowDetail(true);
        }}
        footer={footer}
        request={getSkuListPage}
        params={skuParams}
        rowSelection={{
          columnWidth: '40px',
          onChange: (e, k) => {
            // @ts-ignore
            const Ids = k.map((item: any) => item.skuId);
            setSkuIds(Ids);
          },
        }}
        rowKey="skuId"
        columnsKey="stockUnitList"
        columns={columns}
        shopColumns={shopColumns}
      />
      <Filter
        visible={showFilter}
        setSkuParams={(list) => {
          setSkuParams({
            ...list,
            categoryIdList: list.categoryIdList?.filter((item) => item),
            warehouseNos: list.warehouseNos?.filter((item) => +item),
          });
          setShowFilter(false);
        }}
        onClose={() => {
          setShowFilter(false);
        }}
      />
      <Detail
        visible={showDetail}
        skuId={skuId}
        startTime={skuParams.startTime as number}
        endTime={skuParams.endTime as number}
        skuUnit={skuUnit}
        onShow={() => {
          setShowDetail(false);
        }}
      />
    </Context>
  );
}

export default Query;
