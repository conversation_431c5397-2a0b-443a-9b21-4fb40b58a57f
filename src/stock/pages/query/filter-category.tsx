import { useCallback, useEffect, useState } from 'react';
import { Checkbox, Drawer, Spin } from 'antd';
import type { DrawerProps } from 'antd';
import { Search, Icon } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import {
  getCustomCategoryList,
  GetCustomCategoryListResult,
  GetCustomCategoryListParams,
} from '@/apis';
import classNames from 'classnames';
import { createUuid } from '@/utils/utils';
import { useTranslation } from 'react-i18next';
import styles from './filter-category.module.less';

interface PropsType extends DrawerProps {
  cateGoryId: Array<number | string>;
  defaultChecked?: { id: number | string; categoryName: string }[];
  checkedMax?: number;
  onCloseCategory: () => void;
  setCateGoryId: MultipleParamsFn<
    [ids: Array<number | string>, list: GetCustomCategoryListResult[] | null]
  >;
  categoryList?: GetCustomCategoryListResult[];
}

function FilterCategory({
  visible,
  cateGoryId,
  defaultChecked,
  checkedMax,
  onCloseCategory,
  setCateGoryId,
  categoryList,
  ...props
}: PropsType) {
  const [apiParams, setApiParams] = useState<GetCustomCategoryListParams>({
    pageNo: 1,
    pageSize: 10,
    categoryName: '',
    grade: 1,
    parentId: null,
  });
  const { t } = useTranslation();
  const [tier, setTier] = useState([
    {
      categoryName: t('paymentManage_selectFirstLevelCategory'),
      id: 0,
    },
  ]);

  const [searchKey, setSearchKey] = useState(''); // 用于清空搜索框
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [categories, setCategories] = useState<GetCustomCategoryListResult[]>([]);
  const [checkedIds, setCheckedIds] = useState<Array<number | string>>([]);
  const [checkedList, setCheckedList] = useState<GetCustomCategoryListResult[]>([]);

  // 获取数据
  const getList = (params: GetCustomCategoryListParams, reset = false) => {
    setLoading(true);
    setApiParams(params);
    return getCustomCategoryList(params)
      .then((res) => {
        if (reset) {
          setCategories([...res.list]);
        } else {
          setCategories([...categories, ...res.list]);
        }
        setHasMore(
          (params.pageNo as number) < Math.ceil(res.pagination.count / (params.pageSize as number))
        );
        return res.list;
      })
      .finally(() => {
        setLoading(false);
      });
  };
  // @ts-ignore
  // eslint-disable-next-line no-unused-vars
  const tierIndex = useCallback(
    (tierId: number) => {
      switch (tierId) {
        case 1:
          return t('custom_cateOne');
        case 2:
          return t('custom_catetTwo');
        case 3:
          return t('custom_cateThree');
        case 4:
          return t('custom_cateFour');
        default:
          return '';
      }
    },
    [t]
  );

  // 下一级
  const levelClick = ({
    id,
    categoryName,
    grade,
    isExistChildren,
  }: GetCustomCategoryListResult) => {
    if (checkedIds.includes(id) || !isExistChildren) {
      return;
    }
    setSearchKey(createUuid());
    const len = tier.length;
    const newTier = tier.slice(0, len - 1);
    newTier.push(
      {
        categoryName,
        id,
      },
      {
        categoryName: t(`custom_cate${len + 1}`),
        id: 0,
      }
    );
    setTier(newTier);
    getList(
      {
        pageNo: 1,
        pageSize: 10,
        categoryName: '',
        parentId: id,
        grade: 1 + (grade as number),
      },
      true
    );
  };

  // 层级导航
  const tierClick = (id: number, index: number) => {
    if (!id) return;
    setSearchKey(createUuid());
    const newTier = tier.slice(0, index);
    newTier.push({
      categoryName: t(`custom_cate${index + 1}`),
      id: 0,
    });
    setTier(newTier);
    getList(
      {
        pageNo: 1,
        pageSize: 10,
        categoryName: '',
        grade: index + 1,
        parentId: index ? id : null,
      },
      true
    );
  };

  // 分页加载
  const loadMore = () => {
    if (loading || !hasMore) return;
    getList({
      ...apiParams,
      pageNo: 1 + (apiParams.pageNo as number),
    });
  };

  // 搜索
  const searchName = (value: string) => {
    getList(
      {
        ...apiParams,
        pageNo: 1,
        categoryName: value,
      },
      true
    );
  };

  // 选择
  const onCheckboxChange = useCallback(
    (item: GetCustomCategoryListResult) => {
      const i = checkedIds.indexOf(item.id);
      if (i === -1) {
        checkedIds.push(item.id);
        checkedList.push(item);
      } else {
        checkedIds.splice(i, 1);
        checkedList.splice(i, 1);
      }
      setCheckedIds([...checkedIds]);
      setCheckedList([...checkedList]);
    },
    [checkedIds, checkedList]
  );

  // 初始化选中
  const initChecked = () => {
    if (defaultChecked) {
      setCheckedList(defaultChecked as GetCustomCategoryListResult[]);
      setCheckedIds(defaultChecked.map((item) => item.id));
    } else if (cateGoryId.length) {
      setCheckedIds(cateGoryId);
    }
    setCheckedList([...(categoryList || [])]);
  };

  useEffect(() => {
    if (visible) {
      getList({
        pageNo: 1,
        pageSize: 10,
        categoryName: '',
        grade: 1,
        parentId: null,
      }).then(() => {
        initChecked();
      });
      setSearchKey(createUuid());
    } else {
      setApiParams({
        pageNo: 1,
        pageSize: 10,
        categoryName: '',
        grade: 1,
        parentId: null,
      });
      setTier([
        {
          categoryName: t('paymentManage_selectFirstLevelCategory'),
          id: 0,
        },
      ]);
      setSearchKey('');
      setHasMore(true);
      setCategories([]);
      setCheckedIds([]);
      setCheckedList([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <Drawer
      title={t('paymentManage_selectCategory')}
      visible={visible}
      placement="bottom"
      width={375}
      getContainer={false}
      className={styles.drawer}
      onClose={onCloseCategory}
      extra={
        <div
          role="button"
          tabIndex={0}
          className={styles.level}
          onClick={() => {
            setCateGoryId(checkedIds, checkedList);
            setCategories([]);
          }}
        >
          {t('public_confirm')}
        </div>
      }
      {...props}
    >
      <Search
        key={searchKey}
        placeholder={t('paymentManage_searchPlaceholder_admin')}
        className={styles.search}
        onSearch={(value) => {
          searchName(value);
        }}
      />
      <div className={styles.select}>
        {checkedList
          .filter((_, i) => i < Number(checkedMax) || 4)
          .map((item) => (
            <span key={item.id} className={styles.item}>
              <span className={styles.text}>{item.categoryName}</span>
              <Icon
                onClick={() => {
                  onCheckboxChange(item);
                }}
                className={styles.del}
                name="close-circle"
              />
            </span>
          ))}
      </div>
      <div className={styles.tier}>
        {tier.map((item, index) => (
          <span
            role="button"
            tabIndex={index}
            key={item.id}
            className={classNames(styles.text, item.id ? '' : styles.itemActive)}
            onClick={() => tierClick(item.id, index)}
          >
            {item.categoryName}
          </span>
        ))}
      </div>
      <div className={styles.list} id="categories">
        <InfiniteScroll
          dataLength={categories.length}
          hasMore={hasMore}
          loader={
            <div className="text-center">
              <Spin tip={t('public_loading')} />
            </div>
          }
          next={loadMore}
          scrollableTarget="categories"
          height={checkedIds.length ? 400 : 468}
        >
          <div>
            {categories.map((item) => (
              <div className={styles.label} key={item.id}>
                <Checkbox
                  checked={checkedIds.includes(item.id)}
                  disabled={!!checkedMax && checkedIds.length >= checkedMax}
                  onChange={() => {
                    onCheckboxChange(item);
                  }}
                  className={styles.checkboxLabel}
                >
                  <span title={item.categoryName}>{item.categoryName}</span>
                </Checkbox>
                <span
                  role="button"
                  tabIndex={item.id}
                  className={classNames(
                    styles.level,
                    checkedIds.includes(item.id) || !item.isExistChildren ? styles.gray : ''
                  )}
                  onClick={() => {
                    levelClick(item);
                  }}
                >
                  {t('channel_nextlever')}
                </span>
              </div>
            ))}
            {checkedIds.length || !hasMore ? '' : <div style={{ height: 68 }} />}
          </div>
        </InfiniteScroll>
      </div>
    </Drawer>
  );
}

FilterCategory.defaultProps = {
  defaultChecked: undefined,
  checkedMax: undefined,
  categoryList: [],
};

export default FilterCategory;
