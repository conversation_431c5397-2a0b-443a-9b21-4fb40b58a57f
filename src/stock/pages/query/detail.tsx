import { PropsWithChildren, useEffect, useState } from 'react';
import { Drawer, Empty, Icon } from '@/components';
import { Spin, Tooltip } from 'antd';
import classNames from 'classnames';
import {
  getPlatSkuId,
  idResult,
  stockListResult,
  getPlatStockList,
  getRecordStockPage,
  recordListResult,
  postPlatStockDetail,
  getBaseStockUnitList,
} from '@/apis';
import InfiniteScroll from 'react-infinite-scroll-component';
import { checkPermission } from '@/utils/permission';
import { useUnmount } from 'ahooks';
// eslint-disable-next-line import/named
import ScreenSelect from '../../components/screen-select';
import getStateTag from '../../utils/get-state-tag';
import styles from './detail.module.less';

interface DetailPropps {
  visible: boolean;
  skuId: number;
  skuUnit: string;
  startTime: number;
  endTime: number;
  onShow: () => void;
}

interface wareSelectListProps {
  warehouseNo: string;
  warehouseName: string;
}

function Detail({
  visible,
  skuUnit,
  onShow,
  skuId,
  startTime,
  endTime,
}: PropsWithChildren<DetailPropps>) {
  const [show, setShow] = useState(visible);
  const [skuList, setSkuList] = useState<idResult>();
  const [stockList, setStockList] = useState<stockListResult[]>([]);
  const [inventory, setInventory] = useState<recordListResult[]>([]);
  const [wareSelectId, setWareSelectId] = useState<(number | string)[]>([]);
  const [unitSelectId, setUnitSelectId] = useState<(number | string)[]>([]);
  const [unitSelectList, setUnitSelectList] = useState<wareSelectListProps[]>([]);
  const [wareSelectList, setWareSelectList] = useState<wareSelectListProps[]>([]);
  const [stockListParams, setStockListParams] = useState({
    skuId: 0,
    pageSize: 4,
    pageNo: 1,
    startTime: 0,
    endTime: 0,
  });
  const [inventoryListParams, setInventoryListParams] = useState({
    skuId: 0,
    pageSize: 10,
    pageNo: 1,
  });
  const [stockListTotal, setStockListTotal] = useState(true);
  const [inventoryTotal, setInventoryTotal] = useState(true);
  const [contentHeight, setContentHeight] = useState(0);
  const [recordHeight, setRecordHeight] = useState(0);
  const [dimensionType, setDimensionType] = useState(1);
  const [curShowRecordItemDetailList, setCurShowRecordItemDetailList] = useState(0); // 当前展开的库存记录货位
  const onClose = () => {
    setShow(false);
    onShow();
  };

  const standard = (item?: string) => {
    if (item) {
      const stand: string[] = [];
      JSON.parse(item).forEach((res: { name: string; value: string }) => {
        stand.push(`${res.name}:${res.value};`);
      });
      return stand.join(' ');
    }
    return '';
  };

  const loadMore = () => {
    setStockListParams({ ...stockListParams, skuId, pageNo: (stockListParams.pageNo += 1) });
  };

  const loadInventory = () => {
    setInventoryListParams({
      ...inventoryListParams,
      skuId,
      pageNo: (inventoryListParams.pageNo += 1),
    });
  };

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const exportSingle = () => {
    postPlatStockDetail({ skuIds: [skuId] }).then((res) => {
      const URL = window.webkitURL || window.URL;
      const url = URL.createObjectURL(res);
      urlDownload(url, `${skuList?.skuName}-库存详情.xls`);
    });
  };

  const restore = () => {
    setStockListParams((val) => ({ ...val, pageNo: 1 }));
    setStockList([]);
  };

  const dimensionClick = (type: number) => {
    if (type === dimensionType) return;
    restore();
    setWareSelectId([]);
    setUnitSelectId([]);
    setDimensionType(type);
  };

  const orderName = (type: number) => {
    switch (type) {
      case 1:
        return '其他出库单';
      case 2:
        return '其他入库单';
      case 3:
        return '销售出库单';
      case 4:
        return '采购入库单';
      case 5:
        return '调拨单';
      case 6:
        return '盘点单';
      case 7:
        return '领用出库单';
      case 8:
        return '生产入库单';
      case 9:
        return '退货入库单';
      case 10:
        return '退货出库单';
      case 11:
        return '调拨入库单';
      case 12:
        return '调拨出库单';
      case 14:
        return '铺货入库单';
      case 15:
        return '铺货出库单';
      default:
        return '';
    }
  };

  const getCompany = (choose: boolean) => {
    if (choose) {
      return (
        <div className={styles.each}>
          仓库
          <div className={styles.screenSelect}>
            {visible && (
              <ScreenSelect
                selectId={wareSelectId}
                selectList={wareSelectList}
                getSelectId={(ids) => {
                  if (ids === wareSelectId) return;
                  restore();
                  setWareSelectId(ids);
                }}
              />
            )}
          </div>
        </div>
      );
    }
    return (
      <div className={styles.each}>
        单位
        <div className={styles.screenSelectUnit}>
          {visible && (
            <ScreenSelect
              selectId={unitSelectId}
              selectList={unitSelectList}
              getSelectId={(ids) => {
                if (ids === unitSelectId) return;
                restore();
                setUnitSelectId(ids);
              }}
            />
          )}
        </div>
      </div>
    );
  };

  const getUnitEach = (item: stockListResult, choose: boolean) => {
    if (choose) {
      return (
        <div className={styles.each}>
          <Tooltip placement="topLeft" title={item.warehouseName}>
            {item.warehouseName}
          </Tooltip>
        </div>
      );
    }
    return <div className={styles.each}>{item.unitName}</div>;
  };

  const handleShowDetailBtn = (id: number) => {
    if (id === curShowRecordItemDetailList) {
      setCurShowRecordItemDetailList(0);
      return;
    }
    setCurShowRecordItemDetailList(id);
  };

  useUnmount(() => {
    setWareSelectId([]);
    setUnitSelectId([]);
  });

  useEffect(() => {
    setShow(visible);
    if (!visible) {
      setStockListParams({ skuId, pageSize: 4, pageNo: 1, startTime: 0, endTime: 0 });
      setInventoryListParams({ skuId, pageSize: 10, pageNo: 1 });
      setStockList([]);
      setInventory([]);
    }
  }, [visible]); // eslint-disable-line

  useEffect(() => {
    if (visible) {
      if (!skuId) return;
      getBaseStockUnitList({ skuId }).then((res) => {
        const unitList = res.unitNameList.map((m) => ({
          warehouseNo: m,
          warehouseName: m,
        }));
        const wareList = res.warehouseList;
        setWareSelectList(wareList);
        setUnitSelectList(unitList);
      });
      getPlatSkuId({ skuId }).then((res) => {
        setSkuList(res);
      });
    } else {
      setWareSelectId([]);
      setUnitSelectId([]);
    }
  }, [skuId, skuUnit, visible]);

  useEffect(() => {
    if (visible) {
      getPlatStockList({
        ...stockListParams,
        skuId,
        dimensionType,
        warehouseNoList: wareSelectId,
        unitNameList: unitSelectId,
        startTime,
        endTime,
      }).then((res) => {
        setContentHeight(res.total > 4 ? 200 : 0);
        setStockListTotal(Math.ceil(res.total / res.size) >= stockListParams.pageNo);
        setStockList(stockList.concat(res.list));
      });
    }
  }, [skuId, stockListParams, dimensionType, wareSelectId, unitSelectId, visible]); // eslint-disable-line

  useEffect(() => {
    if (visible) {
      getRecordStockPage({ ...inventoryListParams, skuId }).then((res) => {
        const record = document.querySelector('.ant-drawer-body') as HTMLElement;
        setRecordHeight(record?.offsetHeight);
        setInventoryTotal(Math.ceil(res.total / res.size) > inventoryListParams.pageNo);
        setInventory(inventory.concat(res.list));
      });
    }
  }, [skuId, inventoryListParams, visible]); // eslint-disable-line

  return (
    <Drawer
      title="库存详情"
      widthSize="600px"
      onClose={onClose}
      visible={show}
      className={styles.detail}
      extra={
        checkPermission('AV_001_006_002') ? (
          <div
            role="button"
            tabIndex={0}
            className={styles.export}
            onClick={() => {
              exportSingle();
            }}
          >
            导出详情
          </div>
        ) : null
      }
    >
      <div id="inventory">
        <InfiniteScroll
          dataLength={inventory.length}
          hasMore={inventoryTotal}
          loader={
            <div className="text-center">
              <Spin tip="加载中..." />
            </div>
          }
          next={loadInventory}
          scrollableTarget="inventory"
          height={recordHeight - 10}
        >
          <div className={styles.whiteCard}>
            <div className={styles.blurCard}>
              <div className={styles.goodsInfo}>
                <img
                  className={styles.goodsImg}
                  src={
                    skuList?.images || 'https://img.huahuabiz.com/default/image/default_holder.png'
                  }
                  alt=""
                />
                <div className={styles.goodsText}>
                  <div className={styles.goodsName}>{skuList?.skuName}</div>
                  <div>
                    <Tooltip placement="top" title={`规格：${standard(skuList?.standardJson)}`}>
                      <div className={styles.goodsStandard}>
                        规格：{standard(skuList?.standardJson)}
                      </div>
                    </Tooltip>
                    <div className={styles.goodsStandard}>条形码：{skuList?.barsCode || '--'}</div>
                  </div>
                </div>
              </div>

              {/* <div className={styles.goodsStock}>
                <div className={styles.stock}>
                  <span>实时库存：</span>
                  <span className={styles.goodsNum}>{skuList?.actualStock}</span>
                </div>
                <div className={styles.stock}>
                  <span>可用库存：</span>
                  <span className={styles.goodsNum}>{skuList?.availableStock}</span>
                </div>
              </div> */}
            </div>
            <div className={styles.dimension}>
              <div
                className={classNames({ [styles.dimensionFoo]: dimensionType === 1 })}
                role="button"
                tabIndex={0}
                onClick={() => dimensionClick(1)}
              >
                仓库维度
              </div>
              <div
                className={classNames({ [styles.dimensionFoo]: dimensionType === 2 })}
                role="button"
                tabIndex={0}
                onClick={() => dimensionClick(2)}
              >
                单位维度
              </div>
            </div>
            <div className={classNames(styles.cardList)}>
              <div className={styles.title}>
                {getCompany(dimensionType === 1)}
                {getCompany(dimensionType === 2)}
                <div className={styles.each}>货位</div>
                <div className={styles.each}>实时库存</div>
                <div className={styles.each}>可用库存</div>
              </div>
              <div className={styles.list} id="stockList">
                {stockList.length > 0 ? (
                  <InfiniteScroll
                    dataLength={stockList.length}
                    hasMore={stockListTotal}
                    loader={
                      <div className="text-center">
                        <Spin tip="加载中..." />
                      </div>
                    }
                    next={loadMore}
                    scrollableTarget="stockList"
                    height={contentHeight}
                  >
                    {stockList?.map((item) => (
                      <div className={styles.item} key={item.skuId}>
                        {getUnitEach(item, dimensionType === 1)}
                        {getUnitEach(item, dimensionType === 2)}
                        <div className={styles.each}>{item.positionName}</div>
                        <div className={styles.each}>{item.stock}</div>
                        <div className={styles.each}>
                          <Tooltip title={item.availableStock}>{item.availableStock}</Tooltip>
                        </div>
                      </div>
                    ))}
                  </InfiniteScroll>
                ) : null}
              </div>
            </div>
          </div>
          <div
            className={classNames(
              styles.card,
              styles.cardList,
              inventory && inventory?.length >= 4 ? styles.cardLife : ''
            )}
          >
            <div className={styles.record}>库存记录</div>
            {inventory && inventory.length > 0 ? (
              inventory?.map((item, index) => (
                <div key={item.warehouseNo}>
                  {inventory[index - 1]?.date !== inventory[index].date ? (
                    <div className={styles.time}>{item.date}</div>
                  ) : (
                    ''
                  )}
                  <div className={styles.recordItem}>
                    <div className={styles.cell}>
                      <div className={styles.label}>
                        <span className={styles.labelTitle}>{orderName(item.type)}</span>
                        <img
                          className={styles.orderInfoImg}
                          src={getStateTag(item.orderStatus as number)}
                          alt=""
                        />
                        {/* <span className={styles.affirm}>已确认</span> */}
                      </div>
                      <div className={styles.text}>订单 {item.orderNo}</div>
                    </div>
                    <div className={styles.cell}>
                      <div className={styles.text}>{item.warehouseName}</div>
                      <div className={styles.recordItemDetail}>
                        <div className={styles.num}>
                          {item.quantity > 0 ? '+' : ''}
                          {item.quantity} <span className={styles.unit}>{item.unitName}</span>
                        </div>
                        <div
                          role="button"
                          tabIndex={0}
                          className={styles.recordItemDetailBtn}
                          onClick={() => handleShowDetailBtn(item.id)}
                        >
                          查看详情{' '}
                          {curShowRecordItemDetailList === item.id ? (
                            <Icon name="up" />
                          ) : (
                            <Icon name="down" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  {curShowRecordItemDetailList === item.id &&
                    item.positionList.map((positionItem) => (
                      <div className={styles.recordItemDetailList} key={positionItem.positionId}>
                        <span>{positionItem.positionName}</span>
                        <span>
                          <span style={{ color: '#000' }}>{positionItem.quantity}</span>{' '}
                          {positionItem.unitName}
                        </span>
                      </div>
                    ))}
                </div>
              ))
            ) : (
              <div className={styles.nodata}>
                <Empty title="暂无库存记录" />
              </div>
            )}
          </div>
        </InfiniteScroll>
      </div>
    </Drawer>
  );
}

export default Detail;
