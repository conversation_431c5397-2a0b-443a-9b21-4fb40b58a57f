import { PropsWithChildren, useEffect, useState, useCallback, useReducer } from 'react';
import { Checkbox, Drawer, Spin } from 'antd';
import { Search, Icon } from '@/components';
import { postPlatWareHouse, WareListRusult } from '@/apis';
import InfiniteScroll from 'react-infinite-scroll-component';
import styles from './filter-warehouse.module.less';

const pageSize = 15;

interface FilterWarehousePropps {
  visible: boolean;
  onCloseWarehouse: () => void;
  selectWarehouse: string[];
  warehouseList?: WareListRusult[];
  // eslint-disable-next-line no-unused-vars
  confirm: (arr: Array<number | string | boolean>, list: WareListRusult[]) => void;
}

interface InitialState {
  pageNo: number;
  totalPage: number;
  list: WareListRusult[];
  key: string;
}

const initialState = {
  pageNo: 1,
  totalPage: 2,
  list: [],
  key: '',
};

type WarehouseAction =
  | {
      type: 'setPage';
      payload: { pageNo?: number; totalPage?: number };
    }
  | {
      type: 'setList';
      payload: WareListRusult[];
    }
  | {
      type: 'setAll';
      payload: InitialState;
    };

const reducer = (state: InitialState, action: WarehouseAction): InitialState => {
  switch (action.type) {
    case 'setPage':
      return { ...state, ...action.payload };
    case 'setList':
      return { ...state, list: action.payload };
    case 'setAll':
      return action.payload;
    default:
      return state;
  }
};

function Warehouse({
  visible,
  selectWarehouse,
  warehouseList,
  onCloseWarehouse,
  confirm,
}: PropsWithChildren<FilterWarehousePropps>) {
  const [categoryDrawer, setCategoryDrawer] = useState(visible);
  const [checkedIds, setCheckedIds] = useState<Array<number | string | boolean>>([]);
  const [checkedList, setCheckedList] = useState<WareListRusult[]>([]);
  const [state, dispatch] = useReducer(reducer, initialState);

  const onClose = () => {
    setCategoryDrawer(false);
    onCloseWarehouse();
  };

  const getWarehouse = useCallback((argument: InitialState) => {
    postPlatWareHouse({
      pageNo: argument.pageNo,
      pageSize,
      key: argument.key,
    }).then((res) => {
      dispatch({
        type: 'setAll',
        payload: {
          key: argument.key,
          pageNo: argument.pageNo + 1,
          list: [...argument.list, ...res.list],
          totalPage: Math.ceil(res.pagination.count / pageSize),
        },
      });
    });
  }, []);

  // 选择
  const onChangeWarehouse = useCallback(
    (item: WareListRusult) => {
      const i = checkedIds.indexOf(item.warehouseNo || '');
      if (i === -1) {
        checkedIds.push(item.warehouseNo || '');
        checkedList.push(item);
      } else {
        checkedIds.splice(i, 1);
        checkedList.splice(i, 1);
      }
      setCheckedIds([...checkedIds]);
      setCheckedList([...checkedList]);
    },
    [checkedIds, checkedList]
  );

  const del = (warehouseNo: string) => {
    setCheckedIds(checkedIds.filter((item) => item !== warehouseNo));
    setCheckedList(
      checkedList ? checkedList.filter((item) => item.warehouseNo !== warehouseNo) : []
    );
  };

  const loadMore = () => {
    if (!(state.pageNo <= state.totalPage)) return;
    getWarehouse(state);
  };

  useEffect(() => {
    setCategoryDrawer(visible);
    if (visible) {
      setCheckedIds(selectWarehouse);
      setCheckedList([...(warehouseList || [])]);
      getWarehouse(initialState);
    }
  }, [visible]); //eslint-disable-line

  return (
    <Drawer
      onClose={onClose}
      visible={categoryDrawer}
      placement="bottom"
      width={375}
      className={styles.drawer}
      getContainer={false}
      mask={categoryDrawer}
      title="选择仓库"
      extra={
        <div
          role="button"
          tabIndex={0}
          onClick={() => {
            confirm(checkedIds, checkedList);
          }}
        >
          确定
        </div>
      }
    >
      <Search
        placeholder="输入文本"
        className={styles.search}
        onSearch={(e) => {
          state.key = e;
          state.pageNo = 1;
          state.list = [];
          getWarehouse(state);
        }}
      />
      <div className={styles.select}>
        {checkedList
          ? checkedList.map((item) => (
              <span key={item.warehouseNo} className={styles.item}>
                <span className={styles.name}>{item.warehouseName}</span>
                <Icon
                  onClick={() => {
                    del(item.warehouseNo as string);
                  }}
                  className={styles.del}
                  name="close-circle"
                />
              </span>
            ))
          : null}
      </div>

      <div className={styles.list} id="list">
        <InfiniteScroll
          dataLength={state.list.length}
          hasMore={state.pageNo <= state.totalPage}
          loader={
            <div className="text-center">
              <Spin tip="加载中..." />
            </div>
          }
          next={loadMore}
          scrollableTarget="list"
        >
          {state.list
            ? state.list.map((item) => (
                <div className={styles.label} key={item.warehouseNo}>
                  <Checkbox
                    checked={checkedIds.includes(item.warehouseNo || '')}
                    onChange={() => onChangeWarehouse(item)}
                  >
                    {item.warehouseName}
                  </Checkbox>
                </div>
              ))
            : null}
        </InfiniteScroll>
      </div>
    </Drawer>
  );
}

Warehouse.defaultProps = {
  warehouseList: [],
};

export default Warehouse;
