@import 'styles/mixins/mixins';

.detail {
  :global {
    .ant-drawer-mask {
      opacity: 0 !important;
    }
  }
}

.card {
  margin-bottom: 6px;
  padding: 0 20px 16px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.blurCard {
  padding: 0 20px 16px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  // padding-bottom: 16px;
  // background: linear-gradient(180deg, #1b5aff 0%, #1b5aff 0%, #57bdfd 100%, #57bdfd 100%);
}

.whiteCard {
  background: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  margin-bottom: 20px;
}

.goodsInfo {
  display: flex;
  padding: 20px 0 12px;
  // border-bottom: 1px solid #f3f3f3;
}

.goodsImg {
  width: 64px;
  height: 64px;
  margin-right: 12px;
  border-radius: 12px;
  border: 1px solid #fff;
  object-fit: contain;
}

.goodsText {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goodsName {
  color: #000;
  width: 224px;
  .text-overflow(2);
}

.goodsStandard {
  color: #888b98;
  font-size: 12px;
  width: 224px;
  opacity: 0.7;
  margin-bottom: 2px;
  .text-overflow();
}

.goodsStandard:last-child {
  margin-bottom: 0;
}

.goodsStock {
  display: flex;
  height: 56px;
  padding: 12px 0;
  background: #fff;
  border-radius: 12px;

  & .stock {
    display: flex;
    width: 50%;
    justify-content: center;
    align-items: center;
    border-left: 1px solid #f3f3f3;
  }
}

.goodsNum {
  color: #008cff;
}

.export {
  color: #008cff;
  cursor: pointer;
}

// 列表
.cardList {
  // padding: 12px 12px 0;
  padding: 20px;
}

.list {
  max-height: 200px;
  overflow: auto;
}

.title,
.item {
  display: flex;
  padding: 9px 0;
  justify-content: space-between;
  align-items: center;

  & .each {
    width: 96px;
    padding: 0 10px;
    text-align: center;
    position: relative;
    .text-overflow(2);

    .screenSelect {
      position: absolute;
      top: -2px;
      right: -2px;
    }

    .screenSelectUnit {
      position: absolute;
      top: -2px;
      right: 10px;
    }
    // .text-overflow();
  }
}

.title {
  border-radius: 8px;
  background: #f5f6fa;
}

.item {
  padding: 16px 0;
}

// 记录
.record {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.time {
  color: #888b98;
  margin-bottom: 8px;
}

.recordItem {
  width: 100%;
  margin-top: 8px;
  padding: 12px;
  border-radius: 12px;
  background: #f5f6fa;

  & .affirm {
    color: #008cff;
    font-size: 12px;
    margin-left: 4px;
    padding: 4px;
    background: #d9eeff;
  }
}

.recordItemDetail {
  display: flex;

  .recordItemDetailBtn {
    color: #008cff;
    margin-left: 14px;
    cursor: pointer;
    user-select: none;
  }
}

.recordItemDetailList {
  color: #888b98;
  display: flex;
  width: 528px;
  height: 54px;
  padding: 16px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f3f3;
}

.cell {
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;

  &:last-child {
    margin-bottom: 0;
  }

  .labelTitle {
    margin-right: 4px;
  }

  .label {
    display: flex;
    align-items: center;
  }

  & .text {
    color: #888b98;
    font-size: 12px;
  }

  & .num {
    color: #040919;
  }

  .unit {
    color: #888b98;
  }
}

.nodata {
  padding: 60px 0;
}

.orderInfoImg {
  height: 18px;
}

.dimension {
  color: #888b98;
  font-size: 14px;
  display: flex;
  margin-bottom: 10px;
  justify-content: space-around;
  align-items: center;
  cursor: pointer;
}

.dimensionFoo {
  color: #008cff;
  font-size: 600;
  position: relative;

  &::before {
    content: '';
    width: 20px;
    height: 4px;
    position: absolute;
    bottom: -6px;
    left: 19px;
    border-radius: 4px;
    background: #008cff;
  }
}
