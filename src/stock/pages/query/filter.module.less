@import 'styles/mixins/mixins';

.filter {
  overflow: visible;
  transition: none !important;
  transform: none !important;

  :global {
    .ant-drawer-mask {
      border-radius: 12px;
    }

    .ant-drawer-content {
      overflow: hidden;
    }
  }
}

.time {
  :global {
    .ant-picker-suffix {
      display: none;
    }

    .ant-picker.ant-picker-borderless {
      width: 140px;
      height: 28px;
      border-radius: 10px;
      background: #f3f3f3 !important;
    }

    .ant-picker-input > input {
      text-align: center;
      color: #040919;
    }

    .ant-form-item {
      display: inline-block;
      width: 140px !important;
      margin-bottom: 7px;
    }
  }

  .line {
    display: inline-block;
    width: 12px;
    margin: 13px 4px;
    border: 1px solid #d8d8d8;
    vertical-align: middle;
  }
}

.card {
  margin-bottom: 20px;
  padding: 0 20px 8px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .title {
    padding: 20px 0;
  }

  .layout {
    display: flex;
    margin-bottom: 12px;
    justify-content: space-between;
    align-items: center;
  }

  .bgWhite {
    color: #f3f3f3;
  }

  :global {
    .ant-input-number {
      width: 140px;
    }

    .ant-input-number-input {
      width: 140px;
      height: 28px;
      border-radius: 10px;
      background: #f3f3f3;
      text-align: center;
      border: none;
    }
  }
}

.list {
  display: flex;
  flex-wrap: wrap;
}

.item,
.itemActive {
  display: flex;
  width: 92px;
  height: 28px;
  margin-right: 12px;
  margin-bottom: 12px;
  padding: 0 10px;
  justify-content: center;
  align-items: center;
  text-align: center;
  border-radius: 10px;
  background: #f3f3f3;
  cursor: pointer;

  &:nth-child(3n) {
    margin-right: 0;
  }

  & .name {
    .text-overflow();
  }
}

.itemActive,
.itemMore {
  background: #d9eeff;
  border: 1px solid #008cff;
  color: #008cff;
}

.flooder {
  display: flex;
  width: 100%;
  height: 86px;
  justify-content: space-around;
  position: fixed;
  bottom: 0;
  left: 0;
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 90%) 36%,
    rgb(255 255 255 / 90%) 36%
  );

  .flooderBtn {
    color: #888b98;
    width: 160px;
    height: 38px;
    line-height: 38px;
    text-align: center;
    border-radius: 10px;
    background: #f3f3f3;
    cursor: pointer;
  }

  .flooderBtnAdd {
    color: #fff;
    background: linear-gradient(257deg, #00c6ff 0%, #00c6ff 0%, #008cff 100%, #008cff 100%);
  }
}
