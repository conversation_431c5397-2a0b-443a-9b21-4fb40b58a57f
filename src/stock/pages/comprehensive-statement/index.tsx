import {
  postPlatExportReportSynthesis,
  postPlatQueryReportAllotPage,
  PlatReportAllotParams,
  postPlatQueryReportAllotHead,
  PlatReportAllotRusult,
} from '@/apis';
import { Context, Icon, Empty, List } from '@/components';
import { initPrice2 } from '@/src/order-rewrite/utils';
import { checkPermission } from '@/utils/permission';
import { useMount, useUnmount } from 'ahooks';
import { Dropdown, Menu, Radio, RadioChangeEvent, Table } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import StatementReportFilter from '../../containers/statement-report-filter';
import styles from './index.module.less';

const menuListOption = [
  { value: 1, label: '日汇总' },
  { value: 2, label: '月汇总' },
  { value: 3, label: '年汇总' },
];

const isPrice = [
  'periodAmount',
  'buyAmount',
  'callInAmount',
  'inventoryWinAmount',
  'saleReturnAmount',
  'otherInAmount',
  'saleAmount',
  'callOutAmount',
  'buyReturnAmount',
  'inventoryLossAmount',
  'receiveAmount',
  'otherOutAmount',
];

function ComprehensiveStatement() {
  const navigate = useNavigate();
  const [reportVisible, setReportVisible] = useState(false);
  const wrapEl = useRef(null as unknown as HTMLDivElement);
  const [contextHeight, setContextHeight] = useState(0);
  const [emptyWidth, setEmptyWidth] = useState(0);
  const [pageSize, setPageSize] = useState({
    pageSize: 10,
    total: 10,
  });
  const [headList, setHeadList] = useState<PlatReportAllotRusult[]>([]);
  const [filterParams, setFilterParams] = useState<PlatReportAllotParams>({
    key: '',
    startDate: String(dayjs().subtract(1, 'month').valueOf()),
    endDate: String(dayjs().valueOf()),
    pageNo: 1,
    warehouseIdList: [],
    collectType: 1,
    collectMode: 2,
    isShowAmount: 0,
  });
  const filterOptions = useRef([
    { label: '批次库存查询', value: `/stock/batch` },
    { label: '批次成本报表', value: `/stock/report` },
    { label: '综合查询报表', value: `/stock/comprehensive-statement` },
    { label: '调拨单明细报表', value: `/stock/transfer-report` },
  ]);
  const selectBatch = useRef({
    skuId: 0,
    skuBatchNo: '',
    unitName: '',
  });

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const onExport = () => {
    postPlatExportReportSynthesis(filterParams).then((res) => {
      if (res.type !== 'application/json') {
        const URL = window.webkitURL || window.URL;
        const url = URL.createObjectURL(res);
        const startTime = dayjs(Number(filterParams.startDate)).format('YYYYMMDD');
        const endTime = dayjs(Number(filterParams.endDate)).format('YYYYMMDD');
        urlDownload(url, `${startTime}-${endTime} 综合查询报表.xls`);
      }
    });
  };

  const onChange = (e: RadioChangeEvent) => {
    setFilterParams((val) => ({
      ...val,
      collectType: e.target.value || 1,
    }));
  };

  const quickFilterNode = (
    <Context.QuickFilter
      label="显示"
      dropdownMatchSelectWidth={130}
      options={filterOptions.current}
      defaultValue="/stock/comprehensive-statement"
      // value={currentUlr.current}
      onChange={(value) => {
        if (value) navigate(value);
      }}
    />
  );

  const head = (
    <Context.Head
      onSearch={(e) => {
        setFilterParams((val) => ({ ...val, key: e.trim() }));
      }}
      onFilter={() => {
        // if (testPerm('AV_001_013_003')) {
        setReportVisible(true);
        // }
      }}
      isFilterActive
      placeholder="商品信息"
      title={[
        {
          title: '仓库管理',
          to: '/stock',
        },
        '查询报表',
      ]}
      quickFilter={quickFilterNode}
      extra={
        checkPermission('AV_001_013_001_002') ? (
          <Context.HeadTool>
            <Dropdown
              overlay={
                <Menu
                  items={[
                    {
                      key: '2',
                      label: (
                        <div role="button" tabIndex={0} onClick={onExport}>
                          导出全部
                        </div>
                      ),
                    },
                  ]}
                />
              }
              placement="top"
            >
              <button type="button" className={styles.button}>
                <Icon name="shangchuan" size={20} />
              </button>
            </Dropdown>
          </Context.HeadTool>
        ) : null
      }
    />
  );

  const columns: ColumnsType<PlatReportAllotRusult> = [
    {
      title: '日期',
      width: '135px',
      align: 'center',
      fixed: true,
      render: (item) => <div className={styles.labelItem}>{item.dateTime || '--'}</div>,
    },
    {
      title: '商品信息',
      align: 'center',
      // fixed: true,
      className: styles.shopMessage,
      children: [
        filterParams.collectMode === 3 || filterParams.collectMode === 2
          ? {
              title: '商品编码',
              width: '235px',
              align: 'center',
              fixed: true,
              className: `shopMessageFoo`,
              render: (item) => (
                <div className={styles.labelItem} title={item.skuCode}>
                  {item.skuCode}
                </div>
              ),
            }
          : {},
        filterParams.collectMode === 3 || filterParams.collectMode === 2
          ? {
              title: '商品名称',
              width: '235px',
              align: 'center',
              fixed: true,
              className: `shopMessageFoo`,
              render: (item) => (
                <div className={styles.labelItem} title={item.skuName}>
                  {item.skuName}
                </div>
              ),
            }
          : {},
        filterParams.collectMode === 3 || filterParams.collectMode === 2
          ? {
              title: '规格',
              width: '230px',
              align: 'center',
              fixed: true,
              className: `shopMessageFoo`,
              render: (item) => (
                <div>
                  {JSON.parse(item.standardJson || '[]').map(
                    (m: { name: string; value: string }) => (
                      <div>
                        <span>{m.name}</span>:<span>{m.value}</span>
                      </div>
                    )
                  )}
                </div>
              ),
            }
          : {},
      ],
    },
    {
      title: '',
      align: 'center',
      className: styles.shopMessage,
      children: [
        filterParams.collectMode === 3 || filterParams.collectMode === 2
          ? {
              title: '条形码',
              width: '165px',
              align: 'center',
              className: `shopMessageFoo`,
              render: (item) => <div className={styles.labelItem}>{item.barsCode}</div>,
            }
          : {},
        filterParams.collectMode === 3 || filterParams.collectMode === 2
          ? {
              title: '单位',
              width: '65px',
              align: 'center',
              className: `shopMessageFoo`,
              render: (item) => <div className={styles.labelItem}>{item.unitName || '--'}</div>,
            }
          : {},
      ],
    },
    filterParams.collectMode === 3 || filterParams.collectMode === 1
      ? {
          title: '仓库',
          width: '130px',
          align: 'center',
          render: (item) => <div className={styles.labelItem}>{item.warehouseName || '--'}</div>,
        }
      : {},
    {
      title: '期初',
      align: 'center',
      className: styles.shopMessage,
      children: [
        {
          title: '期初数量',
          width: '135px',
          align: 'center',
          dataIndex: 'periodQuantity',
          className: `shopMessageFoo`,
          render: (_, item) => <div className={styles.labelItem}>{item.periodQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '期初金额',
              width: '135px',
              align: 'center',
              dataIndex: 'periodAmount',
              className: `shopMessageFoo`,
              render: (_, item) => <div className={styles.labelItem}>￥ {item.periodAmount}</div>,
            }
          : {},
      ],
    },
    {
      title: '入库',
      align: 'center',
      children: [
        {
          title: '采购数量',
          align: 'center',
          width: '135px',
          dataIndex: 'buyQuantity',
          render: (_, item) => <div className={styles.labelItem}>{item.buyQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '采购金额',
              align: 'center',
              width: '135px',
              dataIndex: 'buyAmount',
              render: (_, item) => <div className={styles.labelItem}>￥ {item.buyAmount}</div>,
            }
          : {},
        {
          title: '调入数量',
          align: 'center',
          width: '135px',
          dataIndex: 'callInQuantity',
          render: (_, item) => <div className={styles.labelItem}>{item.callInQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '调入金额',
              align: 'center',
              width: '135px',
              dataIndex: 'callInAmount',
              render: (_, item) => <div className={styles.labelItem}>￥ {item.callInAmount}</div>,
            }
          : {},
        {
          title: '盘赢数量',
          align: 'center',
          width: '135px',
          dataIndex: 'inventoryWinQuantity',
          render: (_, item) => <div className={styles.labelItem}>{item.inventoryWinQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '盘赢金额',
              align: 'center',
              width: '135px',
              dataIndex: 'inventoryWinAmount',
              render: (_, item) => (
                <div className={styles.labelItem}>￥ {item.inventoryWinAmount}</div>
              ),
            }
          : {},
        {
          title: '销售退货数量',
          align: 'center',
          width: '135px',
          dataIndex: 'saleReturnQuantity',
          render: (_, item) => <div className={styles.labelItem}>{item.saleReturnQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '销售退货金额',
              align: 'center',
              width: '135px',
              dataIndex: 'saleReturnAmount',
              render: (_, item) => (
                <div className={styles.labelItem}>￥ {item.saleReturnAmount}</div>
              ),
            }
          : {},
        {
          title: '其他入库数量',
          align: 'center',
          width: '135px',
          dataIndex: 'otherInQuantity',
          render: (_, item) => <div className={styles.labelItem}>{item.otherInQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '其他入库金额',
              align: 'center',
              width: '135px',
              dataIndex: 'otherInAmount',
              render: (_, item) => <div className={styles.labelItem}>￥ {item.otherInAmount}</div>,
            }
          : {},
      ],
    },
    {
      title: '出库',
      align: 'center',
      className: styles.shopMessage,
      children: [
        {
          title: '销售数量',
          align: 'center',
          width: '135px',
          dataIndex: 'saleQuantity',
          className: `shopMessageFoo`,
          render: (_, item) => <div className={styles.labelItem}>{item.saleQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '销售金额',
              align: 'center',
              width: '135px',
              dataIndex: 'saleAmount',
              className: `shopMessageFoo`,
              render: (_, item) => <div className={styles.labelItem}>￥ {item.saleAmount}</div>,
            }
          : {},
        {
          title: '调出数量',
          align: 'center',
          width: '135px',
          dataIndex: 'callOutQuantity',
          className: `shopMessageFoo`,
          render: (_, item) => <div className={styles.labelItem}>{item.callOutQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '调出金额',
              align: 'center',
              width: '135px',
              dataIndex: 'callOutAmount',
              className: `shopMessageFoo`,
              render: (_, item) => <div className={styles.labelItem}>￥ {item.callOutAmount}</div>,
            }
          : {},
        {
          title: '采购退货数量',
          align: 'center',
          width: '135px',
          dataIndex: 'buyReturnQuantity',
          className: `shopMessageFoo`,
          render: (_, item) => <div className={styles.labelItem}>{item.buyReturnQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '采购退货金额',
              align: 'center',
              width: '135px',
              dataIndex: 'buyReturnAmount',
              className: `shopMessageFoo`,
              render: (_, item) => (
                <div className={styles.labelItem}>￥ {item.buyReturnAmount}</div>
              ),
            }
          : {},
        {
          title: '盘亏数量',
          align: 'center',
          width: '135px',
          dataIndex: 'inventoryLossQuantity',
          className: `shopMessageFoo`,
          render: (_, item) => <div className={styles.labelItem}>{item.inventoryLossQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '盘亏金额',
              align: 'center',
              width: '135px',
              dataIndex: 'inventoryLossAmount',
              className: `shopMessageFoo`,
              render: (_, item) => (
                <div className={styles.labelItem}>￥ {item.inventoryLossAmount}</div>
              ),
            }
          : {},
        {
          title: '领用数量',
          align: 'center',
          width: '135px',
          dataIndex: 'receiveQuantity',
          className: `shopMessageFoo`,
          render: (_, item) => <div className={styles.labelItem}>{item.receiveQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '领用金额',
              align: 'center',
              width: '135px',
              dataIndex: 'receiveAmount',
              className: `shopMessageFoo`,
              render: (_, item) => <div className={styles.labelItem}>￥ {item.receiveAmount}</div>,
            }
          : {},
        {
          title: '其他出库数量',
          align: 'center',
          width: '135px',
          dataIndex: 'otherOutQuantity',
          className: `shopMessageFoo`,
          render: (_, item) => <div className={styles.labelItem}>{item.otherOutQuantity}</div>,
        },
        filterParams.isShowAmount
          ? {
              title: '其他出库金额',
              align: 'center',
              width: '135px',
              dataIndex: 'otherOutAmount',
              className: `shopMessageFoo`,
              render: (_, item) => <div className={styles.labelItem}>￥ {item.otherOutAmount}</div>,
            }
          : {},
      ],
    },
    {
      title: '结存',
      align: 'center',
      children: [
        {
          title: '结存数量',
          width: '135px',
          align: 'center',
          dataIndex: 'surplusQuantity',
          render: (_, item) => <div className={styles.labelItem}>{item.surplusQuantity}</div>,
        },
      ],
    },
    {
      title: '待出库数量',
      width: '130px',
      align: 'center',
      dataIndex: 'waitOutQuantity',
      className: `shopMessageFoo`,
      render: (_, item) => <div className={styles.labelItem}>{item.waitOutQuantity}</div>,
    },
    {
      title: '待入库数量',
      width: '130px',
      align: 'center',
      dataIndex: 'waitInQuantity',
      render: (_, item) => <div className={styles.labelItem}>{item.waitInQuantity}</div>,
    },
    {
      title: '可用库存',
      width: '130px',
      align: 'center',
      dataIndex: 'availableQuantity',
      className: `shopMessageFoo`,
      render: (_, item) => <div className={styles.labelItem}>{item.availableQuantity}</div>,
    },
  ];

  const summary = useCallback(
    () => {
      if (!headList.length) return undefined;
      const headObj = headList[0];
      return (
        <Table.Summary fixed>
          <div className={styles.summaryBox}>
            {columns.map((item) => {
              // @ts-ignore
              if (item.children) {
                return (
                  <>
                    {
                      // @ts-ignore
                      item.children.map((itemChild) => (
                        <span
                          className={styles.summary}
                          style={{ minWidth: itemChild.width, textAlign: itemChild.align }}
                        >
                          {
                            // @ts-ignore
                            // eslint-disable-next-line no-nested-ternary
                            isPrice.includes(itemChild?.dataIndex) ? '￥' : ''
                          }
                          {
                            // @ts-ignore
                            // eslint-disable-next-line no-nested-ternary
                            itemChild.title === '日期'
                              ? '总计'
                              : itemChild?.dataIndex
                              ? // @ts-ignore
                                headObj[itemChild?.dataIndex]
                              : ''
                          }
                        </span>
                      ))
                    }
                  </>
                );
              }
              return (
                <span
                  className={styles.summary}
                  style={{ minWidth: item.width, textAlign: item.align }}
                >
                  {
                    // @ts-ignore
                    // eslint-disable-next-line no-nested-ternary
                    isPrice.includes(item?.dataIndex) ? '￥' : ''
                  }
                  {
                    // @ts-ignore
                    // eslint-disable-next-line no-nested-ternary
                    isPrice.includes(item?.dataIndex || '')
                      ? // @ts-ignore
                        // eslint-disable-next-line no-nested-ternary
                        initPrice2(headObj[item?.dataIndex])
                      : // @ts-ignore
                      // eslint-disable-next-line no-nested-ternary
                      item.title === '日期'
                      ? '全部'
                      : // @ts-ignore
                      // eslint-disable-next-line no-nested-ternary
                      item?.dataIndex
                      ? // @ts-ignore
                        // eslint-disable-next-line no-nested-ternary
                        headObj[item?.dataIndex]
                      : ''
                  }
                </span>
              );
            })}
          </div>
        </Table.Summary>
      );
    },
    [headList, filterParams] // eslint-disable-line
  );

  const resize = debounce(() => {
    if (wrapEl.current) {
      setContextHeight(wrapEl.current.offsetHeight);
      setEmptyWidth(wrapEl.current.offsetWidth);
    }
  }, 100);

  useEffect(() => {
    postPlatQueryReportAllotHead(filterParams).then((res) => {
      setHeadList(res ? [res] : []);
    });
  }, [filterParams]);

  useMount(() => {
    resize();
    window.addEventListener('resize', resize, false);
  });

  useUnmount(() => {
    window.removeEventListener('resize', resize, false);
  });

  return (
    <Context
      head={head}
      style={{ minWidth: '900px' }}
      wrapStyle={{ overflowX: 'auto' }}
      permission="AV_001_013_001"
      className={styles.statement}
    >
      <div className={styles.radio}>
        <Radio.Group
          options={menuListOption}
          onChange={onChange}
          value={filterParams.collectType}
          optionType="button"
          buttonStyle="solid"
        />
      </div>
      <List
        columns={columns}
        rowKey={(data, index) => `${data.skuId}_${index}`}
        className={classNames(styles.table, {
          [styles.tablePageSize20]: pageSize.pageSize === 20,
          [styles.tablePageSize50]: pageSize.pageSize === 50,
          [styles.tablePageSize100]: pageSize.pageSize === 100,
        })}
        request={postPlatQueryReportAllotPage}
        params={filterParams}
        onRow={(e) => ({
          onClick: () => {
            selectBatch.current = {
              skuId: e.skuId,
              skuBatchNo: e.skuBatchNo,
              unitName: e.unitName,
            };
          },
        })}
        onChange={(e) => {
          setPageSize({
            pageSize: e.pageSize || 10,
            total: e.total || 10,
          });
        }}
        locale={{
          emptyText: (
            <div
              className={styles.emptyBox}
              style={{
                height: `${(((contextHeight || 600) > 200 && contextHeight - 2) || 600) - 130}px`,
                width: `${emptyWidth - 47}px`,
              }}
            >
              <Empty />
            </div>
          ),
        }}
        summary={summary}
      />
      <StatementReportFilter
        visible={reportVisible}
        data={filterParams}
        confirm={(e) => {
          setFilterParams((val) => ({ ...val, ...e }));
          setReportVisible(false);
        }}
        onClose={() => setReportVisible(false)}
      />
    </Context>
  );
}

export default ComprehensiveStatement;
