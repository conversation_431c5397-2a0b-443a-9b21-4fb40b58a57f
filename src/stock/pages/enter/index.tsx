import React, { useCallback, useRef, useState, useEffect, useMemo } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Context, Icon, List, ListInstance } from '@/components';
import { Button, Popover, message, Modal, Dropdown, Menu } from 'antd';
import {
  stockEnterList,
  stockEnterAdd,
  stockEnterConfirm,
  stockEnterDetail,
  stockEnterDelete,
  stockEnterEdit,
  postPlatInOrderAudit,
  postPlatOrderSubmit,
} from '@/apis';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';
import classNames from 'classnames';
import OrderFilter from '../../containers/order-filter';
import OrderAdd from '../../containers/order-add';
import OrderDetail from '../../containers/order-detail';
import styles from './index.module.less';

import getStateTag from '../../utils/get-state-tag';
import OrderUpload from '../../containers/order-upload';
import orderPermission from '../../utils/order-permission';
import HomeBtnPerm from '../../containers/home-btn-perm';
import PrintPage, { PrintPageEv } from '../../components/react-to-print';
import ImportDetail from '../../containers/import-detail/import';

interface FilterParams {
  key?: string | null;
  orderStatus?: number | null;
  startDate?: number;
  endDate?: number;
  pageNo?: number;
  warehouseIds?: number[];
  businessType?: number | null;
}

function Enter() {
  const navigator = useNavigate();
  const [showOrderAdd, setShowOrderAdd] = useState(false);
  const [showOrderFilter, setShowOrderFilter] = useState(false);
  const [showOrderDeatil, setShowOrderDetail] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const [orderId, setOrderId] = useState(0);
  const [ids, setIds] = useState<number[]>([]);
  const [id, setId] = useState(0);
  const refreshRef = useRef(null as unknown as ListInstance);
  const [orderStatusParams] = useSearchParams({ orderStatus: '' });
  const orderStatusStr = orderStatusParams.get('orderStatus');
  const showSearch = orderStatusParams.get('showSearch') || '';
  const [orderStatus, setOrderStatus] = useState(orderStatusStr || '');
  const [showOrderUpload, setShowOrderUpload] = useState(false);
  const [orderName, setOrderName] = useState('');
  const [filter, setFilter] = useState<FilterParams>({
    key: null,
    // @ts-ignore
    orderStatus: orderStatusStr,
    startDate: 0,
    endDate: 0,
    pageNo: 1,
    warehouseIds: [],
    businessType: null,
  });
  const printPageRef = useRef(null as null | PrintPageEv);
  // eslint-disable-next-line no-shadow,consistent-return
  const batchHandle = (ids: number[], status: 0 | 1) => {
    if (ids.length === 0) return message.warning('请先选择订单');
    if (!orderPermission(9, status ? '审批' : '取消')) {
      message.error('暂无权限，请联系公司管理员开通!');
    } else {
      Modal.confirm({
        title: '提示',
        icon: '',
        okText: '确认',
        cancelText: '取消',
        centered: true,
        content: `是否批量${status ? '审批' : '取消'}`,
        onOk: () => {
          stockEnterConfirm({
            ids,
            orderStatus: status,
          }).then((res) => {
            const {
              isNeedInitWorkflowForm,
              processDefinitionKey,
              processDefinitionName,
              businessOrderNo,
              businessType,
            } = res;
            if (isNeedInitWorkflowForm) {
              window.open(
                `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                  {
                    businessOrderNo,
                    businessType,
                  }
                )}`
              );
            } else {
              message.success(`批量${status ? '操作' : '取消'}成功`);
            }
            refreshRef.current.refresh();
          });
        },
      });
    }
  };

  const editEntryList = (storageId: number, name: string) => {
    if (!orderPermission(9, '编辑')) {
      message.error('暂无权限，请联系公司管理员开通');
    } else {
      setId(storageId);
      setOrderName(name);
      setShowOrderAdd(true);
    }
  };

  const isFilter = useMemo(() => {
    if (filter.endDate || filter.startDate || filter?.warehouseIds?.length || filter.businessType) {
      return true;
    }
    return false;
  }, [filter]);

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <div
            role="button"
            tabIndex={0}
            className={styles.batchItem}
            onClick={() => {
              batchHandle(ids, 1);
            }}
          >
            批量审批
          </div>
        }
      >
        <span className={styles.batchBtn}>批量操作</span>
      </Popover>
    ),
    [ids]
  );

  useEffect(() => {
    if (orderStatusParams.get('type') === 'add') {
      setTimeout(() => {
        setShowOrderAdd(true);
      });
    }
    if (orderStatusParams.get('id')) {
      setOrderId(Number(orderStatusParams.get('id')));
      setShowOrderDetail(true);
    }
  }, [orderStatusParams]);

  const filterList = [
    { label: '全部', value: '' },
    { label: '已审批', value: '1' },
    { label: '待审批', value: '0' },
    { label: '草稿', value: '2' },
    { label: 'OA审批中', value: '3' },
  ];

  return (
    <Context
      id="content"
      permission="AV_001_002"
      head={
        <Context.Head
          showSearch={!!showSearch}
          onSearch={(e) => {
            setFilter({
              key: e,
            });
          }}
          onFilter={() => {
            setShowOrderFilter(true);
          }}
          isFilterActive={isFilter}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              value={orderStatus}
              // @ts-ignore
              options={filterList}
              dropdownMatchSelectWidth={100}
              className={styles.selectItem}
              onChange={(e) => {
                setOrderStatus(e);
                setIds([]);
                setFilter({
                  ...filter,
                  // eslint-disable-next-line no-nested-ternary
                  orderStatus: e,
                });
              }}
            />
          }
          placeholder="业务员 | 公司 | 订单编号"
          title={[
            {
              title: '仓库管理',
              to: '/stock',
            },
            '入库单',
          ]}
          extra={
            checkPermission('AV_001_002_001') ? (
              <Dropdown
                className={styles.btn}
                overlay={
                  <Menu
                    items={[
                      {
                        key: '1',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            className={styles.importDataC}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              // setId(0);
                              // setShowOrderAdd(true);
                              navigator('/stock/create?stockType=enter');
                            }}
                          >
                            普通新建
                          </div>
                        ),
                      },
                      {
                        key: '2',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              // if (!checkPermission('AV_001_004_006')) {
                              //   message.error('暂无权限，请联系公司管理员开通');
                              // } else {
                              //   setShowImport(true);
                              // }
                              setShowImport(true);
                            }}
                          >
                            导入新建
                          </div>
                        ),
                      },
                    ]}
                  />
                }
                placement="bottom"
              >
                <Context.HeadTool>
                  <Button size="small" type="primary" onClick={() => {}}>
                    新建入库单
                  </Button>
                </Context.HeadTool>
              </Dropdown>
            ) : null
          }
        />
      }
    >
      <List
        // @ts-ignore
        ref={refreshRef}
        footer={footer}
        onRow={(tags) => ({
          onClick: () => {
            // setOrderId(tags?.id);
            // setOrderName(tags?.businessName);
            // setShowOrderDetail(true);
            navigator(`/stock/detail?stockType=enter&id=${tags.id}`);
          },
        })}
        request={stockEnterList}
        params={filter}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          selectedRowKeys: ids,
          onChange(e) {
            // @ts-ignore
            setIds(e);
          },
          getCheckboxProps: (record) => ({
            disabled: record.orderStatus === 3 || record.orderStatus === 1,
          }),
        }}
        rowKey="id"
        columns={[
          {
            title: '订单信息',
            width: '30%',
            render: (tags) => (
              <div>
                <div className={styles.orderInfo}>
                  <img className={styles.orderInfoImg} src={getStateTag(tags.orderStatus)} alt="" />
                  <span
                    className={styles.companyName}
                    title={tags?.customerCompanyName ? tags?.customerCompanyName : tags?.orderNo}
                  >
                    {tags?.customerCompanyName ? tags?.customerCompanyName : tags?.orderNo}
                  </span>
                </div>
                {tags?.customerCompanyName ? (
                  <div className={classNames(styles.orderColor, 'text-left')} title={tags?.orderNo}>
                    {tags?.orderNo}
                  </div>
                ) : null}
              </div>
            ),
          },
          {
            title: '入库仓库',
            width: '20%',
            align: 'center',
            render: (tags) => <div className={styles.companyName}>{tags?.warehouseName}</div>,
          },
          {
            title: '单据类型',
            width: '20%',
            align: 'center',
            render: (tags) => tags.businessName,
          },
          {
            title: '操作信息',
            width: '20%',
            align: 'center',
            render: (tags) => (
              <div>
                <div>{tags?.operator}</div>
                {tags.orderDate ? (
                  <div className={styles.grey}>
                    {dayjs(tags.orderDate).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                ) : null}
              </div>
            ),
          },
          {
            title: '操作',
            width: '70px',
            align: 'center',
            render: (tags) =>
              ((tags.orderStatus !== 1 && tags.businessType !== 3) ||
                (tags.orderStatus !== 1 && tags.businessType !== 10) ||
                (tags.orderStatus !== 1 && tags.businessType !== 12) ||
                (tags.orderStatus !== 1 && tags.businessType !== 8)) &&
              tags.orderStatus !== 3 ? (
                <Popover
                  placement="bottom"
                  trigger="hover"
                  content={
                    <HomeBtnPerm
                      orderTypeId={tags.businessType}
                      orderType={9}
                      status={tags.orderStatus}
                      isCanEdit={tags.isCanEdit}
                      ifCanDelete={tags.ifCanDelete}
                      btnChange={(name: string) => {
                        if (name === '审批' || name === '取消审批') {
                          if (tags.orderStatus === 3) {
                            message.warning('OA审批中');
                            return;
                          }
                          if (!tags?.orderStatus) {
                            postPlatInOrderAudit({
                              orderStatus: tags?.orderStatus === 1 ? 0 : 1,
                              id: tags?.id,
                            }).then((res) => {
                              const {
                                isNeedInitWorkflowForm,
                                processDefinitionKey,
                                processDefinitionName,
                                businessOrderNo,
                                businessType,
                              } = res;
                              if (isNeedInitWorkflowForm) {
                                window.open(
                                  `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                                    {
                                      businessOrderNo,
                                      businessType,
                                    }
                                  )}`
                                );
                                refreshRef.current.refresh();
                                return;
                              }
                              refreshRef.current.refresh();
                              const { isNeedSecStep, isNeedApprovoal, description } = res;
                              if (description.length > 0) {
                                message.success(description);
                              } else {
                                message.success(`${tags?.orderStatus === 1 ? '取消' : '操作'}成功`);
                              }
                              if (
                                isNeedSecStep &&
                                isNeedApprovoal === 0 &&
                                tags?.businessName !== '退货入库单'
                              ) {
                                setOrderId(tags?.id);
                                setShowOrderUpload(true);
                              }
                            });
                          } else {
                            stockEnterConfirm({
                              orderStatus: tags?.orderStatus === 1 ? 0 : 1,
                              ids: [tags?.id],
                            }).then((res) => {
                              const {
                                isNeedInitWorkflowForm,
                                processDefinitionKey,
                                processDefinitionName,
                                businessOrderNo,
                                businessType,
                              } = res;
                              if (isNeedInitWorkflowForm) {
                                window.open(
                                  `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                                    {
                                      businessOrderNo,
                                      businessType,
                                    }
                                  )}`
                                );
                              } else {
                                message.success(`${tags?.orderStatus === 1 ? '取消' : '操作'}成功`);
                              }
                              refreshRef.current.refresh();
                            });
                          }
                        } else if (name === '提交') {
                          postPlatOrderSubmit({ id: tags?.id }).then(() => {
                            message.success(`提交成功`);
                            refreshRef.current.refresh();
                          });
                        } else if (name === '删除') {
                          stockEnterDelete(tags.id).then(() => {
                            message.success('删除成功');
                            refreshRef.current.refresh();
                          });
                        } else if (name === '编辑') {
                          // editEntryList(tags.id, tags.businessName);
                          navigator(`/stock/create?stockType=enter&id=${tags.id}`);
                        }
                      }}
                    />
                  }
                >
                  <Icon className={styles.icon} name="zu13366" />
                </Popover>
              ) : (
                <Icon className={classNames(styles.icon, styles.iconColor)} name="zu13366" />
              ),
          },
        ]}
      />
      <OrderAdd
        OrderCreact={id ? stockEnterEdit : stockEnterAdd}
        orderDeteil={stockEnterDetail}
        visible={showOrderAdd}
        orderType={9}
        orderName={orderName}
        id={id}
        onClose={() => {
          setShowOrderAdd(false);
        }}
        openUpload={() => {
          setShowOrderAdd(false);
          setShowOrderUpload(true);
        }}
        submit={() => {
          setShowOrderAdd(false);
          setFilter({ ...filter, pageNo: 1 });
        }}
      />
      <OrderFilter
        visible={showOrderFilter}
        orderType={9}
        startDate={filter.startDate}
        endDate={filter.endDate}
        status={orderStatusParams.get('orderStatus') || null}
        onClose={() => {
          setShowOrderFilter(false);
        }}
        confirm={(e) => {
          setShowOrderFilter(false);
          setFilter({
            ...e,
            warehouseIds: e.warehouseIds?.includes(0) ? [] : e?.warehouseIds,
            orderStatus: filter.orderStatus,
            businessType: e.businessType || null,
          });
        }}
      />
      <OrderDetail
        orderDetele={stockEnterDelete}
        orderConfirm={stockEnterConfirm}
        orderSubmit={postPlatOrderSubmit}
        confirm={() => {
          setShowOrderDetail(false);
          refreshRef.current.refresh();
        }}
        id={orderId}
        visible={showOrderDeatil}
        onClose={() => {
          setShowOrderDetail(false);
        }}
        orderDeteil={stockEnterDetail}
        orderType={9}
        openUpload={() => {
          setShowOrderAdd(false);
          setShowOrderDetail(false);
          setShowOrderUpload(true);
        }}
        editEntryList={() => {
          editEntryList(orderId, orderName);
        }}
        orderPrintPdf={() => {
          printPageRef.current?.handlePrint();
        }}
      />
      <OrderUpload
        visible={showOrderUpload}
        orderType={9}
        id={orderId}
        onClose={() => {
          refreshRef.current.refresh();
          setShowOrderUpload(false);
        }}
      />
      <PrintPage ref={printPageRef} />
      <ImportDetail importValue={0} visible={showImport} onClose={() => setShowImport(false)} />
    </Context>
  );
}

export default Enter;
