@import 'styles/mixins/mixins';

.detail {
  :global {
    .ant-drawer-mask {
      opacity: 0 !important;
    }
  }
}

.card {
  margin-bottom: 6px;
  padding: 0 20px 16px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.blurCard {
  padding: 0 20px 16px;
  //   background: #fff;
  padding-bottom: 16px;
  overflow: hidden;
  border-radius: 18px;
  background: linear-gradient(180deg, #1b5aff 0%, #1b5aff 0%, #57bdfd 100%, #57bdfd 100%);
}

.whiteCard {
  background: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  margin-bottom: 20px;
}

.goodsInfo {
  display: flex;
  padding: 20px 0 12px;
  // border-bottom: 1px solid #f3f3f3;
}

.goodsImg {
  width: 64px;
  height: 64px;
  margin-right: 12px;
  border-radius: 12px;
  // border: 1px solid #fff;
}

.goodsText {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.goodsName {
  color: #fff;
  width: 224px;
  .text-overflow(2);
}

.goodsStandard {
  color: #fff;
  font-size: 12px;
  width: 224px;
  opacity: 0.7;
  .text-overflow();
}

.goodsStock {
  display: flex;
  height: 56px;
  padding: 12px 0;
  background: #fff;
  border-radius: 12px;

  & .stock {
    display: flex;
    width: 50%;
    justify-content: center;
    align-items: center;
    border-left: 1px solid #f3f3f3;
  }
}

.reportStock {
  display: flex;
  height: 56px;
  align-items: center;
  border-radius: 12px;
  background: #fff;

  & .stock {
    display: flex;
    width: 50%;
    padding-left: 10px;
  }

  & :nth-child(1).stock {
    position: relative;

    & ::after {
      content: '';
      width: 0;
      height: 34px;
      margin: auto;
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      border-left: 1px solid #f3f3f3;
    }
  }
}

.goodsNum {
  color: #008cff;
}

.export {
  color: #008cff;
  cursor: pointer;
}

// 列表
.cardList {
  padding: 12px 12px 0;
}

.list {
  overflow: auto;
}

.title,
.item {
  display: flex;
  padding: 9px 0;
  justify-content: space-between;

  & .each {
    width: 96px;
    padding: 0 10px;
    text-align: center;
    position: relative;

    .screenSelect {
      position: absolute;
      top: -2px;
      right: -6px;
    }

    .screenSelectUnit {
      position: absolute;
      top: -2px;
      right: 10px;
    }

    // .text-overflow();
  }
}

.title {
  border-radius: 8px;
  background: #f5f6fa;
}

.item {
  padding: 16px 0;
}

// 记录
.record {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.time {
  color: #888b98;
  margin-bottom: 8px;
}

.recordItem {
  width: 100%;
  margin-bottom: 8px;
  padding: 12px;
  border-radius: 12px;
  background: #f5f6fa;

  & .affirm {
    color: #008cff;
    font-size: 12px;
    margin-left: 4px;
    padding: 4px;
    background: #d9eeff;
  }
}

.cell {
  display: flex;
  margin-bottom: 10px;
  justify-content: space-between;

  &:last-child {
    margin-bottom: 0;
  }

  .labelTitle {
    display: flex;
    align-items: center;
  }

  .label {
    display: flex;
    align-items: center;
  }

  & .text {
    color: #888b98;
    font-size: 12px;
  }

  & .num {
    color: #040919;
  }

  .unit {
    color: #888b98;
  }
}

.nodata {
  padding: 60px 0;
}

.orderInfoImg {
  height: 18px;
}

.dimension {
  color: #888b98;
  font-size: 14px;
  display: flex;
  margin-bottom: 10px;
  justify-content: space-around;
  align-items: center;
  cursor: pointer;
}

.dimensionFoo {
  color: #008cff;
  font-size: 600;
  position: relative;

  &::before {
    content: '';
    width: 20px;
    height: 4px;
    position: absolute;
    bottom: -6px;
    left: 19px;
    border-radius: 4px;
    background: #008cff;
  }
}

.deductText {
  color: #888b98;
  font-size: 12px;
  display: flex;
  margin-top: 23px;
  justify-content: space-between;
  position: relative;
  align-items: center;

  & ::before {
    content: '';
    width: 100%;
    height: 0;
    position: absolute;
    top: -12px;
    left: 0;
    border-top: 1px solid #b1b3be;
    opacity: 0.2;
  }
}

.tagBox {
  color: #ea1c26;
  font-size: 10px;
  font-weight: bold;
  display: inline-block;
  height: 20px;
  line-height: 20px;
  margin-left: 8px;
  padding: 0 4px;
  text-align: center;
  border-radius: 2px;
  background: #fcdddf;
}
