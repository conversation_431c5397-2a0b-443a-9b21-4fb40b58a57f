import { PropsWithChildren, useEffect, useState } from 'react';
import { Drawer, Icon } from '@/components';
import { InputNumber, message } from 'antd';
import { skuListParams, postPlatWareHouse, WareListRusult } from '@/apis';
import classNames from 'classnames';
import Warehouse from '../query/filter-warehouse';
import styles from '../query/filter.module.less';

interface FilterPropps {
  visible: boolean;
  onClose: () => void;
  // eslint-disable-next-line no-unused-vars
  setSkuParams: (list: skuListParams) => void;
}

function Filter({ visible, setSkuParams, onClose, ...props }: PropsWithChildren<FilterPropps>) {
  const [warehouses, setWarehouse] = useState<WareListRusult[]>([]);
  const [selectWarehouse, setSelectWarehouse] = useState<string[]>(['0']);
  const [showWarehouse, setShowWarehouse] = useState(false);
  const [warehouseList, setWarehouseList] = useState<WareListRusult[]>([]);

  const [count, setCount] = useState(0);
  const [searchList, setSearchList] = useState({
    minAvailableStock: '',
    maxAvailableStock: '',
    minActualStock: '',
    maxActualStock: '',
  });

  const onReset = () => {
    setSelectWarehouse(['0']);
    setSearchList({
      minAvailableStock: '',
      maxAvailableStock: '',
      minActualStock: '',
      maxActualStock: '',
    });
  };

  const selectWarehouses = (warehouseNo: string) => {
    const list = [...selectWarehouse];
    warehouses.forEach((item) => {
      if (item.warehouseNo === warehouseNo) {
        if (list.indexOf(warehouseNo) > -1) {
          list.splice(list.indexOf(warehouseNo), 1);
          warehouseList.splice(list.indexOf(warehouseNo), 1);
        } else {
          list.push(item.warehouseNo);
          warehouseList.push(item);
        }
      }
    });
    setSelectWarehouse(warehouseNo !== '0' ? list.filter((item) => item !== '0') : ['0']);
    setWarehouseList([...warehouseList]);
  };

  const numberChange = (data: Record<string, number | string>) => {
    setSearchList({
      ...searchList,
      ...data,
    });
  };

  useEffect(() => {
    if (visible) {
      postPlatWareHouse({
        pageNo: 1,
        pageSize: 4,
      }).then((res) => {
        setWarehouse([
          { warehouseNo: '0', warehouseName: '全部仓库', warehouseStatus: 0 },
          ...res.list,
        ]);
        setCount(res.pagination.count);
      });
    }
  }, [visible]); //eslint-disable-line
  return (
    <Drawer
      title="筛选"
      visible={visible}
      {...props}
      className={styles.filter}
      onClose={() => {
        // if (searchList.minAvailableStock > searchList.maxAvailableStock)
        //   return message.error('可用库存最小值不能大于最大值');
        // if (searchList.minActualStock > searchList.maxActualStock)
        //   return message.error('实际库存最小值不能大于最大值');
        // setSkuParams({ ...searchList });
        // return false;
        onClose();
      }}
    >
      <div className={styles.card}>
        <div className={styles.title}>仓库名称</div>
        <div className={styles.list}>
          {warehouses.map((item, index) => (
            <span
              className={
                selectWarehouse.includes(item.warehouseNo as string)
                  ? styles.itemActive
                  : styles.item
              }
              role="button"
              key={item.warehouseNo}
              tabIndex={index}
              onClick={() => {
                selectWarehouses(item.warehouseNo as string);
              }}
            >
              <span className={styles.name}>{item.warehouseName}</span>
            </span>
          ))}
          {count > 4 ? (
            <span
              role="button"
              tabIndex={0}
              className={styles.itemActive}
              onClick={() => {
                setShowWarehouse(true);
              }}
            >
              更多仓库
              <Icon name="right" />
            </span>
          ) : null}
        </div>
      </div>
      <div className={styles.card}>
        <div className={styles.title}>可用库存范围</div>
        <div className={styles.layout}>
          <InputNumber
            placeholder="最小值"
            className={styles.select}
            bordered={false}
            controls={false}
            min="1"
            max="99999999"
            value={searchList.minAvailableStock}
            onChange={(e) => {
              numberChange({ minAvailableStock: e || 0 });
            }}
          />
          <span className={styles.bgWhite}>—</span>
          <InputNumber
            placeholder="最大值"
            className={styles.select}
            bordered={false}
            controls={false}
            min="1"
            max="99999999"
            value={searchList.maxAvailableStock}
            onChange={(e) => {
              numberChange({ maxAvailableStock: e || 0 });
            }}
          />
        </div>
      </div>
      <div className={styles.card}>
        <div className={styles.title}>实际库存范围</div>
        <div className={styles.layout}>
          <InputNumber
            placeholder="最小值"
            className={styles.select}
            bordered={false}
            controls={false}
            min="1"
            max="99999999"
            value={searchList.minActualStock}
            onChange={(e) => {
              numberChange({ minActualStock: e || 0 });
            }}
          />
          <span className={styles.bgWhite}>—</span>
          <InputNumber
            placeholder="最大值"
            className={styles.select}
            bordered={false}
            controls={false}
            min="1"
            max="99999999"
            value={searchList.maxActualStock}
            onChange={(e) => {
              numberChange({ maxActualStock: e || 0 });
            }}
          />
        </div>
      </div>
      <div className={styles.flooder}>
        <div role="button" tabIndex={0} className={styles.flooderBtn} onClick={onReset}>
          重置
        </div>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.flooderBtn, styles.flooderBtnAdd)}
          onClick={() => {
            if (searchList.minAvailableStock > searchList.maxAvailableStock) {
              message.error('可用库存最小值不能大于最大值');
              return;
            }

            if (searchList.minActualStock > searchList.maxActualStock) {
              message.error('实际库存最小值不能大于最大值');
              return;
            }
            setSkuParams({
              ...searchList,
              warehouseNos: selectWarehouse,
            });
          }}
        >
          确定
        </div>
      </div>
      <Warehouse
        confirm={(e, value) => {
          setShowWarehouse(false);
          // @ts-ignore
          setSelectWarehouse(e);
          setWarehouseList(value);
        }}
        visible={showWarehouse}
        selectWarehouse={selectWarehouse}
        onCloseWarehouse={() => {
          setShowWarehouse(false);
        }}
        warehouseList={warehouseList}
      />
    </Drawer>
  );
}

export default Filter;
