import { useMemo, useRef, useState } from 'react';
import { Context, Icon, List } from '@/components';
import { Dropdown, Menu, Space, Button, message } from 'antd';
import {
  stockBatchList,
  StockBatchListResult,
  stockBatchExportDetail,
  stockBatchExport,
  StockBatchExportData,
  skuListParams,
} from '@/apis';
import GoodsInfo from '@@/stock/components/goods-info';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import { useNavigate } from 'react-router-dom';
import { checkPermission } from '@/utils/permission';
import BatchFilter from './batch-filter';
import styles from './index.module.less';

function Batch() {
  const navigate = useNavigate();
  const [requestParams, setRequltParams] = useState({} as unknown as skuListParams);
  const [showFilter, setShowFilter] = useState(false);

  const filterOptions = useRef([
    { label: '批次库存查询', value: `/stock/batch` },
    { label: '批次成本报表', value: `/stock/report` },
    { label: '综合查询报表', value: `/stock/comprehensive-statement` },
    { label: '调拨单明细报表', value: `/stock/transfer-report` },
  ]);

  const selectBatch = useRef({
    skuId: 0,
    skuBatchNo: '',
    unitName: '',
  });

  const batchExportData = useRef<StockBatchExportData>({
    skuStockDetailList: [],
  });

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const exportDetail = (skuName: string, skuId: number, skuBatchNo: string, unitName: string) => {
    stockBatchExportDetail({
      skuId,
      skuBatchNo,
      unitName,
    }).then((res) => {
      if (res.type !== 'application/json') {
        const URL = window.webkitURL || window.URL;
        const url = URL.createObjectURL(res);
        urlDownload(url, `${skuName}-批次库存信息.xls`);
      }
    });
  };

  const onExport = () => {
    if (!batchExportData.current.skuStockDetailList.length) {
      message.warning('请先选择批次！');
      return;
    }
    stockBatchExport(batchExportData.current).then((res) => {
      if (res.type !== 'application/json') {
        const URL = window.webkitURL || window.URL;
        const url = URL.createObjectURL(res);
        urlDownload(url, `批量-批次库存信息.xls`);
      }
    });
  };

  const standard = (item: { standardJson: string }) => {
    const stand = JSON.parse(item.standardJson);
    return stand.map((item1: { name: string; value: string }) => (
      <span>
        {item1.name}: {item1.value},
      </span>
    ));
  };

  const isFilter = useMemo(() => {
    if (
      requestParams?.minAvailableStock ||
      requestParams?.maxAvailableStock ||
      requestParams?.minActualStock ||
      requestParams?.maxActualStock ||
      requestParams?.warehouseNos?.length
    ) {
      return true;
    }
    return false;
  }, [requestParams]);

  const quickFilterNode = (
    <Context.QuickFilter
      label="显示"
      dropdownMatchSelectWidth={130}
      options={filterOptions.current}
      defaultValue="/stock/batch"
      // value={currentUlr.current}
      onChange={(value) => {
        if (value) navigate(value);
      }}
    />
  );

  const head = (
    <Context.Head
      onSearch={(e) => {
        setRequltParams({ ...requestParams, keyword: e });
      }}
      onFilter={() => setShowFilter(true)}
      isFilterActive={isFilter}
      placeholder="批次号 | 订单号 | 商品信息"
      title={[
        {
          title: '仓库管理',
          to: '/stock',
        },
        '查询报表',
      ]}
      quickFilter={quickFilterNode}
      extra={
        checkPermission('AV_001_013_003_002') ? (
          <Context.HeadTool>
            <Button type="primary" size="small" className={styles.headButton} onClick={onExport}>
              导出
            </Button>
          </Context.HeadTool>
        ) : null
      }
    />
  );

  const columns: ColumnsType<StockBatchListResult> = [
    {
      title: '商品信息',
      width: '35%',
      render: (item) => (
        <div>
          <GoodsInfo
            img={item.images}
            name={item.skuName}
            standard={standard(item)}
            number={item.skuCode}
            barsCode={item.barsCode}
            standardId={1}
            skuCode={item.skuCode}
          />
        </div>
      ),
    },
    {
      title: '库存信息',
      render: (item) => (
        <div>
          <div>实际: {item.actualStock || '--'}</div>
          <div>可用: {item.availableStock || '--'}</div>
        </div>
      ),
    },
    {
      title: '批次信息',
      width: '20%',
      align: 'center',
      render: (item) => (
        <div>
          生产日期 {item.productionTime ? dayjs(item.productionTime).format('YYYY.MM.DD') : '--'}
        </div>
      ),
    },
    {
      title: '批次号',
      width: '15%',
      align: 'center',
      dataIndex: 'skuBatchNo',
    },
    {
      title: '操作',
      align: 'center',
      render: (item) => (
        <div className={styles.text}>
          <Dropdown
            overlay={
              <Menu
                items={[
                  {
                    key: '1',
                    label: (
                      <div
                        role="button"
                        tabIndex={0}
                        onClick={(e) => {
                          e.stopPropagation();
                          exportDetail(item.skuName, item.skuId, item.skuBatchNo, item.unitName);
                        }}
                      >
                        导出详情
                      </div>
                    ),
                  },
                ]}
              />
            }
            placement="top"
          >
            <Space onClick={(e) => e.stopPropagation()} className={styles.handle}>
              <Icon className={styles.icon} name="zu13366" />
            </Space>
          </Dropdown>
        </div>
      ),
    },
  ];

  // const footer = () => (
  //   <Popover
  //     placement="top"
  //     trigger="hover"
  //     className={styles.popover}
  //     content={
  //       <div role="button" tabIndex={0} className={styles.batchItem} onClick={onExport}>
  //         批量导出
  //       </div>
  //     }
  //   >
  //     <span className={styles.batchBtn}>批量操作</span>
  //   </Popover>
  // );

  return (
    <Context head={head} permission="AV_001_013_003">
      <List
        request={stockBatchList}
        params={requestParams}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          onChange(e, list) {
            batchExportData.current = {
              skuStockDetailList: list.map((item: StockBatchListResult) => ({
                skuId: item.skuId,
                skuBatchNo: item.skuBatchNo,
                unitName: item.unitName,
              })),
            };
          },
        }}
        onRow={(e) => ({
          onClick: () => {
            selectBatch.current = {
              skuId: e.skuId,
              skuBatchNo: e.skuBatchNo,
              unitName: e.unitName,
            };
          },
        })}
        columns={columns}
        rowKey={(data, index) => `${data.skuId}_${index}`}
        // footer={footer}
      />
      <BatchFilter
        visible={showFilter}
        setSkuParams={(list) => {
          setRequltParams({
            ...list,
            warehouseNos: list.warehouseNos?.filter((item) => +item),
          });
          setShowFilter(false);
        }}
        onClose={() => setShowFilter(false)}
      />
    </Context>
  );
}

export default Batch;
