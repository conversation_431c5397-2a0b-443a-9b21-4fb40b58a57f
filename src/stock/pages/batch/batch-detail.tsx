import { PropsWithChildren, useEffect, useState } from 'react';
import { Drawer, Empty } from '@/components';
import { Spin } from 'antd';
import classNames from 'classnames';
import {
  stockBatchDetailResult,
  recordListResult,
  stockBatchDetailRecord,
  stockBatchExportDetail,
  stockBatchChangeRecord,
  postBatchRxportCostDetail,
} from '@/apis';
import InfiniteScroll from 'react-infinite-scroll-component';
import { checkPermission } from '@/utils/permission';
import stockBatchDetail from '@/apis/psi/stock-batch-detail';
import styles from './batch-detail.module.less';

interface DetailPropps {
  visible: boolean;
  type?: 'batch' | 'report'; // report 为批次成本报表
  skuId: number;
  skuBatchNo: string;
  unitName?: string;
  title?: string;
  onShow: () => void;
}

function BatchDetail({
  visible,
  onShow,
  skuId,
  title,
  type,
  unitName = '',
  skuBatchNo,
}: PropsWithChildren<DetailPropps>) {
  const [show, setShow] = useState(visible);
  const [skuList, setSkuList] = useState<stockBatchDetailResult>({
    actualStock: '',
    availableStock: '',
    images: '',
    standardJson: '',
    skuCode: '',
    skuId: 0,
    skuName: '',
    isDeductType: 0,
  });
  const [inventory, setInventory] = useState<recordListResult[]>([]);
  const [inventoryListParams, setInventoryListParams] = useState({
    skuId: 0,
    skuBatchNo,
    pageSize: 10,
    pageNo: 1,
  });
  const [inventoryTotal, setInventoryTotal] = useState(true);
  const [recordHeight, setRecordHeight] = useState(0);
  const onClose = () => {
    setShow(false);
    onShow();
  };

  const standard = (item?: string) => {
    if (item) {
      const stand: string[] = [];
      JSON.parse(item).forEach((res: { name: string; value: string }) => {
        stand.push(`${res.name}:${res.value};`);
      });
      return stand.join(' ');
    }
    return '';
  };

  const loadInventory = () => {
    setInventoryListParams({
      ...inventoryListParams,
      skuId,
      skuBatchNo,
      pageNo: (inventoryListParams.pageNo += 1),
    });
  };

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const exportSingle = () => {
    const fn = type === 'batch' ? stockBatchExportDetail : postBatchRxportCostDetail;
    const text = type === 'batch' ? '库存详情' : '批次成本变动记录';
    fn({ skuId, skuBatchNo, unitName }).then((res) => {
      const URL = window.webkitURL || window.URL;
      const url = URL.createObjectURL(res);
      urlDownload(url, `${skuList?.skuName}-${text}.xls`);
    });
  };

  // const orderName = (type: number) => {
  //   switch (type) {
  //     case 1:
  //       return '其他出库';
  //     case 2:
  //       return '其他入库';
  //     case 3:
  //       return '销售出库';
  //     case 4:
  //       return '采购入库';
  //     case 5:
  //       return '调拨';
  //     case 6:
  //       return '盘点';
  //     case 7:
  //       return '领用出库';
  //     case 8:
  //       return '生产入库';
  //     case 9:
  //       return '退货入库';
  //     case 10:
  //       return '退货出库';
  //     case 11:
  //       return '调拨入库';
  //     case 12:
  //       return '调拨出库';
  //     default:
  //       return '';
  //   }
  // };

  useEffect(() => {
    setShow(visible);
    if (!visible) {
      setInventoryListParams({ skuId, skuBatchNo, pageSize: 10, pageNo: 1 });
      setInventory([]);
    }
  }, [visible]); // eslint-disable-line

  useEffect(() => {
    if (visible) {
      if (!skuId) return;
      stockBatchDetail({
        skuId,
        skuBatchNo,
        unitName,
      }).then((res) => {
        setSkuList(res);
      });
    }
  }, [visible]); // eslint-disable-line

  useEffect(() => {
    if (visible) {
      const fn = type === 'batch' ? stockBatchDetailRecord : stockBatchChangeRecord;
      fn({
        ...inventoryListParams,
        skuId,
        skuBatchNo,
        unitName,
      }).then((res: any) => {
        const record = document.querySelector('.ant-drawer-body') as HTMLElement;
        setRecordHeight(record?.offsetHeight);
        setInventoryTotal(Math.ceil(res.total / res.size) > inventoryListParams.pageNo);
        setInventory(inventory.concat(res.list));
      });
    }
  }, [skuId, inventoryListParams, visible]); // eslint-disable-line

  return (
    <Drawer
      title={title || '库存详情'}
      onClose={onClose}
      visible={show}
      className={styles.detail}
      extra={
        (checkPermission('AV_001_006_002') && type === 'batch') ||
        (checkPermission('AV_001_012_002') && type === 'report') ? (
          <div role="button" tabIndex={0} className={styles.export} onClick={exportSingle}>
            导出详情
          </div>
        ) : null
      }
    >
      <div id="inventory">
        <InfiniteScroll
          dataLength={inventory.length}
          hasMore={inventoryTotal}
          loader={
            <div className="text-center">
              <Spin tip="加载中..." />
            </div>
          }
          next={loadInventory}
          scrollableTarget="inventory"
          height={recordHeight - 10}
        >
          <div className={styles.whiteCard}>
            <div className={classNames(styles.card, styles.blurCard)}>
              <div className={styles.goodsInfo}>
                <img
                  className={styles.goodsImg}
                  src={
                    skuList?.images || 'https://img.huahuabiz.com/default/image/default_holder.png'
                  }
                  alt=""
                />
                {/* {type === 'batch' && (
                  <div className={styles.goodsText}>
                    <div className={styles.goodsName}>{skuList?.skuName}</div>
                    <div className={styles.goodsStandard}>{standard(skuList?.standardJson)}</div>
                  </div>
                )}
                {(type === 'report' || !type) && (
                  <div className={styles.goodsText}>
                    <div className={styles.goodsName}>{skuList?.skuName}</div>
                    <div className={styles.goodsStandard}>
                      规格：{standard(skuList?.standardJson)}
                    </div>
                    <div className={styles.goodsStandard}>条形码：{skuList?.barsCode}</div>
                  </div>
                )} */}
                <div className={styles.goodsText}>
                  <div className={styles.goodsName}>{skuList?.skuName}</div>
                  <div className={styles.goodsStandard}>
                    规格：{standard(skuList?.standardJson)}
                  </div>
                  <div className={styles.goodsStandard}>条形码：{skuList?.barsCode || '--'}</div>
                </div>
              </div>
              {type === 'batch' && (
                <div className={styles.goodsStock}>
                  <div className={styles.stock}>
                    <span className="mr-1">实时库存</span>
                    <span className={styles.goodsNum}>{skuList?.actualStock}</span>
                  </div>
                  <div className={styles.stock}>
                    <span className="mr-1">可用库存</span>
                    <span className={styles.goodsNum}>{skuList?.availableStock}</span>
                  </div>
                </div>
              )}
              {(type === 'report' || !type) && (
                <div className={styles.reportStock}>
                  <div className={styles.stock}>
                    <span className="mr-1">实际库存</span>
                    <span className={styles.goodsNum}>{skuList?.actualStock}</span>
                  </div>
                  <div className={styles.stock}>
                    <span className="mr-1">库存金额</span>
                    <span className={styles.goodsNum}>{skuList?.stockAmount}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div
            className={classNames(
              styles.card,
              styles.cardList,
              inventory && inventory?.length >= 4 ? styles.cardLife : ''
            )}
          >
            <div className={styles.record}>{type === 'batch' ? '库存记录' : '批次变动记录'}</div>
            {inventory && inventory.length > 0 ? (
              inventory?.map((item, index) => (
                <div key={item.warehouseNo}>
                  {inventory[index - 1]?.createTimeStr !== inventory[index].createTimeStr ? (
                    <div className={styles.time}>{item.createTimeStr}</div>
                  ) : (
                    ''
                  )}
                  <div className={styles.recordItem}>
                    <div className={styles.cell}>
                      <div className={styles.label}>
                        <span className={styles.labelTitle}>
                          {item.businessTypeStr}
                          {!!item.isDeductType && <span className={styles.tagBox}>抵扣</span>}
                        </span>
                      </div>
                      <div className={styles.text}>订单 {item.businessOrderNo}</div>
                    </div>
                    <div className={styles.cell}>
                      <div className={styles.text}>{item.warehouseName}</div>
                      <div className={styles.num}>
                        {item.businessDirect === 1 ? '+' : '-'}
                        {item.quantity}
                        <span className={styles.unit}>
                          {item.auxiliaryUnit || item.unit || item.unitName}
                        </span>
                      </div>
                    </div>
                    {type === 'report' && !!item.isDeductType && (
                      <div className={styles.deductText}>
                        <span>抵扣批次成本</span>
                        <span>¥{item.deductBatchCost}</span>
                      </div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className={styles.nodata}>
                <Empty title="暂无库存记录" />
              </div>
            )}
          </div>
        </InfiniteScroll>
      </div>
    </Drawer>
  );
}

BatchDetail.defaultProps = {
  unitName: '',
  title: '',
  type: 'batch',
};

export default BatchDetail;
