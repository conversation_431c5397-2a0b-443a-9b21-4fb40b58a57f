import {
  BatchCostPageType,
  postBatchRxportCostPage,
  stockBatchCostPage,
  // StockBatchListResult,
} from '@/apis';
import { Context, Icon, Empty } from '@/components';
import { checkPermission, testPerm } from '@/utils/permission';
import { useMount, useUnmount } from 'ahooks';
import { Table, Pagination, Dropdown, Menu } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import dayjs from 'dayjs';
import { debounce } from 'lodash';
import { useCallback, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import GoodsInfo from '../../components/goods-info';
import ReportFilter from '../../containers/report-filter';
import BatchDetail from '../batch/batch-detail';
import styles from './index.module.less';

interface FilterParams {
  startDate?: number;
  endDate?: number;
}

function Report() {
  const navigate = useNavigate();
  const [dataSource, setDataSource] = useState<BatchCostPageType[]>([]);
  const [showDetail, setShowDetail] = useState(false);
  const [loading, setLoading] = useState(false);
  const [reportVisible, setReportVisible] = useState(false);
  const [total, setTotal] = useState(0);
  const wrapEl = useRef(null as unknown as HTMLDivElement);
  const [contextHeight, setContextHeight] = useState(0);
  const [emptyWidth, setEmptyWidth] = useState(0);
  const [filterParams, setFilterParams] = useState<FilterParams>({
    startDate: dayjs().subtract(1, 'month').add(1, 'day').startOf('day').valueOf(),
    endDate: dayjs().endOf('day').valueOf(),
  });
  const [apiParams, setApiParams] = useState({
    pageSize: 10,
    pageNo: 1,
  });
  const filterOptions = useRef([
    { label: '批次库存查询', value: `/stock/batch` },
    { label: '批次成本报表', value: `/stock/report` },
    { label: '综合查询报表', value: `/stock/comprehensive-statement` },
    { label: '调拨单明细报表', value: `/stock/transfer-report` },
  ]);
  const selectBatch = useRef({
    skuId: 0,
    skuBatchNo: '',
    unitName: '',
  });
  const search = useRef('');

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const onExport = () => {
    const data = {
      ...filterParams,
      keyword: search.current,
    };
    postBatchRxportCostPage(data).then((res) => {
      if (res.type !== 'application/json') {
        const URL = window.webkitURL || window.URL;
        const url = URL.createObjectURL(res);
        const startTime = dayjs(filterParams.startDate).format('YYYYMMDD');
        const endTime = dayjs(filterParams.endDate).format('YYYYMMDD');
        urlDownload(url, `${startTime}-${endTime} 批次成本报表.xls`);
      }
    });
  };

  const standard = (item: { standardJson: string }) => {
    const stand = JSON.parse(item.standardJson);
    return stand.map((item1: { name: string; value: string }) => (
      <span>
        {item1.name}: {item1.value},
      </span>
    ));
  };

  const getReportList = useCallback(
    (params: Partial<any> = {}) => {
      const newParams = { ...apiParams, ...filterParams, ...params };
      setApiParams(newParams);
      setLoading(true);
      stockBatchCostPage(newParams)
        .then((res) => {
          setDataSource(res.list);
          setApiParams({
            ...apiParams,
            pageSize: res.size,
            pageNo: res.page,
          });
          setTotal(res.total);
        })
        .finally(() => {
          setLoading(false);
        });
    },
    [apiParams, filterParams]
  );

  const quickFilterNode = (
    <Context.QuickFilter
      label="显示"
      dropdownMatchSelectWidth={130}
      options={filterOptions.current}
      defaultValue="/stock/report"
      // value={currentUlr.current}
      onChange={(value) => {
        if (value) navigate(value);
      }}
    />
  );

  const head = (
    <Context.Head
      onSearch={(e) => {
        getReportList({ pageNo: 1, keyword: e.trim() });
        search.current = e.trim();
      }}
      onFilter={() => {
        // if (testPerm('AV_001_012_003')) {
        setReportVisible(true);
        // }
      }}
      isFilterActive
      placeholder="批次号 | 订单号 | 商品信息"
      title={[
        {
          title: '仓库管理',
          to: '/stock',
        },
        '查询报表',
      ]}
      quickFilter={quickFilterNode}
      extra={
        checkPermission('AV_001_013_004_002') ? (
          <Context.HeadTool>
            <Dropdown
              overlay={
                <Menu
                  items={[
                    {
                      key: '2',
                      label: (
                        <div role="button" tabIndex={0} onClick={onExport}>
                          导出全部
                        </div>
                      ),
                    },
                  ]}
                />
              }
              placement="top"
            >
              <button type="button" className={styles.button}>
                <Icon name="shangchuan" size={20} />
              </button>
            </Dropdown>
          </Context.HeadTool>
        ) : null
      }
    />
  );

  const columns: ColumnsType<BatchCostPageType> = [
    {
      title: '商品信息',
      width: '240px',
      fixed: 'left',
      render: (item) => (
        <div>
          <GoodsInfo
            isReport
            img={item.images}
            name={item.skuName}
            standard={standard(item)}
            // unit={item.unitName}
            barsCode={item.barsCode}
            number={item.skuCode}
            standardId={1}
          />
        </div>
      ),
    },
    {
      title: '批次号',
      width: '135px',
      align: 'center',
      fixed: 'left',
      render: (item) => <div className={styles.labelItem}>{item.skuBatchNo}</div>,
    },
    {
      title: '批次日期',
      width: '130px',
      align: 'center',
      render: (item) => (
        <div className={styles.labelItem}>
          {item.orderDate ? dayjs(item.orderDate).format('YYYY-MM-DD') : '--'}
        </div>
      ),
    },
    {
      title: '业务类型',
      width: '130px',
      align: 'center',
      dataIndex: 'businessTypeStr',
    },
    {
      title: '单据号',
      width: '165px',
      align: 'center',
      render: (item) => <div className={styles.labelItem}>{item.orderNo}</div>,
    },
    {
      title: '批次期初数量',
      width: '130px',
      align: 'center',
      render: (item) => (
        <div className={styles.labelItem}>
          {item.businessDirect === 2 && <span>-</span>}
          {item.quantity}
        </div>
      ),
    },
    {
      title: '单位',
      width: '130px',
      align: 'center',
      dataIndex: 'unitName',
    },
    {
      title: '单位成本',
      width: '130px',
      align: 'center',
      render: (item) => <div className={styles.labelItem}>¥{item.unitCost}</div>,
    },
    {
      title: '期初成本金额',
      width: '130px',
      align: 'center',
      render: (item) => (
        <div className={styles.labelItem}>
          {item.businessDirect === 2 && Number(item.costAmount) !== 0 && <span>-</span>}
          {item.costAmount}
        </div>
      ),
    },
    {
      title: '实际库存',
      width: '130px',
      align: 'center',
      dataIndex: 'actualStock',
    },
    {
      title: '库存金额',
      width: '130px',
      align: 'center',
      render: (item) => <div className={styles.labelItem}>¥{item.stockAmount}</div>,
    },
    {
      title: '调整金额',
      width: '130px',
      align: 'center',
      render: (item) => <div className={styles.labelItem}>¥{item.trimAmount}</div>,
    },
  ];

  const resize = debounce(() => {
    if (wrapEl.current) {
      setContextHeight(wrapEl.current.offsetHeight);
      setEmptyWidth(wrapEl.current.offsetWidth);
    }
  }, 100);

  useMount(() => {
    resize();
    window.addEventListener('resize', resize, false);
    if (!testPerm('AV_001_013_004')) return;
    getReportList();
  });

  useUnmount(() => {
    window.removeEventListener('resize', resize, false);
  });

  return (
    <Context
      head={head}
      loading={loading}
      permission="AV_001_013_004"
      style={{ minWidth: '900px' }}
      wrapStyle={{ overflowX: 'auto' }}
    >
      <div className={styles.reportPage} ref={wrapEl}>
        <Table
          columns={columns}
          dataSource={dataSource}
          pagination={false}
          rowKey={(data, index) => `${data.skuId}_${index}`}
          scroll={{ y: contextHeight - 106 }}
          className={styles.table}
          // rowSelection={{
          //   type: 'checkbox',
          //   columnWidth: 40,
          //   onChange(e, list) {
          //     batchExportData.current = {
          //       skuStockDetailList: list.map((item: StockBatchListResult) => ({
          //         skuId: item.skuId,
          //         skuBatchNo: item.skuBatchNo,
          //         unitName: item.unitName,
          //       })),
          //     };
          //   },
          // }}
          onRow={(e) => ({
            onClick: () => {
              selectBatch.current = {
                skuId: e.skuId,
                skuBatchNo: e.skuBatchNo,
                unitName: e.unitName,
              };
              setShowDetail(true);
            },
          })}
          locale={{
            emptyText: (
              <div
                className={styles.emptyBox}
                style={{
                  height: `${(((contextHeight || 600) > 200 && contextHeight - 2) || 600) - 130}px`,
                  width: `${emptyWidth - 47}px`,
                }}
              >
                <Empty />
              </div>
            ),
          }}
        />
        <Pagination
          current={apiParams.pageNo}
          pageSize={apiParams.pageSize}
          total={total}
          showSizeChanger
          showQuickJumper
          showTotal={(n) => `共 ${n} 条`}
          className={styles.pagination}
          onChange={(pageNo, pageSize) => {
            getReportList({ pageNo, pageSize });
          }}
          style={{
            paddingBottom: '10px',
            position: 'absolute',
            right: '20px',
            bottom: '2px',
          }}
        />
      </div>
      <BatchDetail
        title="批次详情"
        type="report"
        visible={showDetail}
        skuId={selectBatch.current.skuId}
        skuBatchNo={selectBatch.current.skuBatchNo}
        unitName={selectBatch.current.unitName}
        onShow={() => setShowDetail(false)}
      />
      <ReportFilter
        visible={reportVisible}
        startDate={filterParams.startDate}
        endDate={filterParams.endDate}
        confirm={(e) => {
          setFilterParams(e);
          setReportVisible(false);
          getReportList(e);
        }}
        onClose={() => setReportVisible(false)}
      />
    </Context>
  );
}

export default Report;
