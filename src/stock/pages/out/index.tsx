import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Context, Icon, List, ListInstance, Modal } from '@/components';
import { Button, Dropdown, Menu, message, Popover } from 'antd';
import {
  stockOutList,
  stockOutConfirm,
  stockOutDetail,
  stockOutAdd,
  stockOutEdit,
  stockOutDelete,
  postPlatOutOrderAudit,
  postPlatOutOrderSubmit,
} from '@/apis';
import dayjs from 'dayjs';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { checkPermission } from '@/utils/permission';
import classNames from 'classnames';
import getStateTag from '@@/stock/utils/get-state-tag';
import goodsNoStock from '@@/order-rewrite/components/goods-no-stock';
import OrderFilter from '../../containers/order-filter';
import OrderAdd from '../../containers/order-add';
import OrderDetail from '../../containers/order-detail';
import styles from './index.module.less';

import OrderUpload from '../../containers/order-upload';
import orderPermission from '../../utils/order-permission';
import HomeBtnPerm from '../../containers/home-btn-perm/index';
import PrintPage, { PrintPageEv } from '../../components/react-to-print';
import ImportDetail from '../../containers/import-detail/import';

interface FilterParams {
  key?: string | null;
  orderStatus?: number | null;
  startDate?: number;
  endDate?: number;
  pageNo?: number;
  warehouseIds?: number[];
  businessType?: number | null;
}

function Out() {
  const navigator = useNavigate();
  const [showOrderAdd, setShowOrderAdd] = useState(false);
  const [showOrderFilter, setShowOrderFilter] = useState(false);
  const [showOrderDeatil, setShowOrderDetail] = useState(false);
  const [showOrderUpload, setShowOrderUpload] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const [orderId, setOrderId] = useState(0);
  const [ids, setIds] = useState<number[]>([]);
  const [id, setId] = useState(0);
  const [orderName, setOrderName] = useState('');
  const refreshRef = useRef(null as unknown as ListInstance);
  const [orderStatusParams] = useSearchParams({ orderStatus: '' });
  const showSearch = orderStatusParams.get('showSearch') || '';
  const orderStatusStr = orderStatusParams.get('orderStatus');
  const [orderStatus, setOrderStatus] = useState(orderStatusStr || '');
  const [filter, setFilter] = useState<FilterParams>({
    key: null,
    // @ts-ignore
    orderStatus: orderStatusStr,
    startDate: 0,
    endDate: 0,
    pageNo: 1,
    warehouseIds: [],
    businessType: null,
  });
  const printPageRef = useRef(null as null | PrintPageEv);
  // eslint-disable-next-line no-shadow,consistent-return
  const batchHandle = (ids: number[], status: 0 | 1) => {
    if (ids.length === 0) return message.warning('请先选择订单');
    if (!orderPermission(10, status ? '审批' : '取消')) {
      message.error('暂无权限，请联系公司管理员开通!');
    } else {
      Modal.confirm({
        title: '提示',
        content: `是否批量${status ? '审批' : '取消'}`,
        centered: true,
        cancelText: '取消',
        okText: '确认',
        onOk: () => {
          stockOutConfirm({
            ids,
            orderStatus: status,
          }).then((res) => {
            const {
              isNeedInitWorkflowForm,
              processDefinitionKey,
              processDefinitionName,
              businessOrderNo,
              businessType,
            } = res;
            if (isNeedInitWorkflowForm) {
              window.open(
                `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                  {
                    businessOrderNo,
                    businessType,
                  }
                )}`
              );
            } else {
              message.success(`批量${status ? '操作' : '取消'}成功`);
            }
            refreshRef.current.refresh();
          });
        },
      });
    }
  };

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <div
            role="button"
            tabIndex={0}
            className={styles.batchItem}
            onClick={() => {
              batchHandle(ids, 1);
            }}
          >
            批量审批
          </div>
        }
      >
        <span className={styles.batchBtn}>批量操作</span>
      </Popover>
    ),
    [ids]
  );

  const editEntryList = (storageId: number, name: string) => {
    if (!orderPermission(10, '编辑')) {
      message.error('暂无权限，请联系公司管理员开通');
    } else {
      setId(storageId);
      setOrderName(name);
      setShowOrderDetail(false);
      setShowOrderAdd(true);
    }
  };

  const isFilter = useMemo(() => {
    if (filter.endDate || filter.startDate || filter?.warehouseIds?.length || filter.businessType) {
      return true;
    }
    return false;
  }, [filter]);

  useEffect(() => {
    if (orderStatusParams.get('type') === 'add') {
      setTimeout(() => {
        setShowOrderAdd(true);
      });
    }
    if (orderStatusParams.get('id')) {
      setOrderId(Number(orderStatusParams.get('id')));
      setShowOrderDetail(true);
    }
  }, [orderStatusParams]);

  const filterList = [
    { label: '全部', value: '' },
    { label: '已审批', value: '1' },
    { label: '待审批', value: '0' },
    { label: '草稿', value: '2' },
    { label: 'OA审批中', value: '3' },
  ];

  return (
    <Context
      id="content"
      permission="AV_001_001"
      head={
        <Context.Head
          showSearch={!!showSearch}
          onSearch={(e) => {
            setFilter({
              key: e,
            });
          }}
          onFilter={() => {
            setShowOrderFilter(true);
          }}
          isFilterActive={isFilter}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              value={orderStatus}
              // @ts-ignore
              options={filterList}
              dropdownMatchSelectWidth={100}
              onChange={(e) => {
                setOrderStatus(e);
                setFilter({
                  ...filter,
                  // eslint-disable-next-line no-nested-ternary
                  orderStatus: e,
                });
              }}
            />
          }
          placeholder="业务员 | 公司 | 订单编号"
          title={[
            {
              title: '仓库管理',
              to: '/stock',
            },
            '出库单',
          ]}
          extra={
            checkPermission('AV_001_001_001') ? (
              <Dropdown
                className={styles.btn}
                overlay={
                  <Menu
                    items={[
                      {
                        key: '1',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            className={styles.importDataC}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              // setId(0);
                              // setShowOrderAdd(true);
                              navigator('/stock/create?stockType=out');
                            }}
                          >
                            普通新建
                          </div>
                        ),
                      },
                      {
                        key: '2',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              // if (!checkPermission('AV_001_004_006')) {
                              //   message.error('暂无权限，请联系公司管理员开通');
                              // } else {
                              //   setShowImport(true);
                              // }
                              setShowImport(true);
                            }}
                          >
                            导入新建
                          </div>
                        ),
                      },
                    ]}
                  />
                }
                placement="bottom"
              >
                <Context.HeadTool>
                  <Button size="small" type="primary" onClick={() => {}}>
                    新建出库单
                  </Button>
                </Context.HeadTool>
              </Dropdown>
            ) : null
          }
        />
      }
    >
      <List
        // @ts-ignore
        ref={refreshRef}
        footer={footer}
        onRow={(tags) => ({
          onClick: () => {
            // setOrderId(tags?.id);
            // setOrderName(tags?.businessName);
            // setShowOrderDetail(true);
            navigator(`/stock/detail?stockType=out&id=${tags.id}`);
          },
        })}
        request={stockOutList}
        params={filter}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          selectedRowKeys: ids,
          onChange(e) {
            // @ts-ignore
            setIds(e);
          },
          getCheckboxProps: (record) => ({
            disabled: record.orderStatus === 3 || record.orderStatus === 1,
          }),
        }}
        rowKey="id"
        columns={[
          {
            title: '订单信息',
            width: '30%',
            render: (tags) => (
              <div>
                <div className={styles.orderInfo}>
                  <img className={styles.orderInfoImg} src={getStateTag(tags.orderStatus)} alt="" />
                  <span
                    className={styles.companyName}
                    title={tags?.customerCompanyName ? tags?.customerCompanyName : tags?.orderNo}
                  >
                    {tags?.customerCompanyName ? tags?.customerCompanyName : tags?.orderNo}
                  </span>
                </div>
                {tags?.customerCompanyName ? (
                  <div className="text-left" title={tags?.orderNo}>
                    {tags?.orderNo}
                  </div>
                ) : null}
              </div>
            ),
          },
          {
            title: '出库仓库',
            width: '20%',
            align: 'center',
            render: (tags) => <div className={styles.companyName}>{tags?.warehouseName}</div>,
          },
          {
            title: '单据类型',
            width: '20%',
            align: 'center',
            render: (tags) => tags?.businessName,
          },
          {
            title: '操作信息',
            width: '20%',
            align: 'center',
            render: (tags) => (
              <div>
                <div>{tags.operator}</div>
                {tags.orderDate ? (
                  <div className={styles.grey}>
                    {dayjs(tags.orderDate).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                ) : null}
              </div>
            ),
          },
          {
            title: '操作',
            width: '70px',
            align: 'center',
            render: (tags) =>
              ((tags.orderStatus !== 1 && tags.businessType !== 3) ||
                (tags.orderStatus !== 1 && tags.businessType !== 10) ||
                (tags.orderStatus !== 1 && tags.businessType !== 12)) &&
              tags.orderStatus !== 3 ? (
                <Popover
                  placement="bottom"
                  trigger="hover"
                  content={
                    <HomeBtnPerm
                      orderTypeId={tags.businessType}
                      orderType={10}
                      isCanEdit={tags.isCanEdit}
                      status={tags.orderStatus}
                      ifCanDelete={tags.ifCanDelete}
                      btnChange={(name: string) => {
                        if (name === '审批' || name === '取消审批') {
                          if (tags.orderStatus === 3) {
                            message.warning('OA审批中');
                            return;
                          }
                          if (!tags?.orderStatus) {
                            postPlatOutOrderAudit({
                              orderStatus: tags?.orderStatus === 1 ? 0 : 1,
                              id: tags?.id,
                            })
                              .then((res) => {
                                const {
                                  isNeedInitWorkflowForm,
                                  processDefinitionKey,
                                  processDefinitionName,
                                  businessOrderNo,
                                  businessType,
                                } = res;
                                if (isNeedInitWorkflowForm) {
                                  window.open(
                                    `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                                      {
                                        businessOrderNo,
                                        businessType,
                                      }
                                    )}`
                                  );
                                  refreshRef.current.refresh();
                                  return;
                                }
                                refreshRef.current.refresh();
                                const { isNeedSecStep, isNeedApprovoal, description } = res;
                                if (description.length > 0) {
                                  message.success(description);
                                } else {
                                  message.success(
                                    `${tags?.orderStatus === 1 ? '取消' : '操作'}成功`
                                  );
                                }
                                if (
                                  isNeedSecStep &&
                                  isNeedApprovoal === 0 &&
                                  tags?.businessName !== '退货入库单'
                                ) {
                                  setOrderId(tags?.id);
                                  setShowOrderUpload(true);
                                }
                              })
                              .catch((e) => {
                                if (e.code === 5005012) {
                                  goodsNoStock({ list: JSON.parse(e.message) });
                                } else {
                                  message.error(e.message);
                                }
                              });
                          } else {
                            stockOutConfirm({
                              orderStatus: tags?.orderStatus === 1 ? 0 : 1,
                              ids: [tags?.id],
                            })
                              .then((res) => {
                                const {
                                  isNeedInitWorkflowForm,
                                  processDefinitionKey,
                                  processDefinitionName,
                                  businessOrderNo,
                                  businessType,
                                } = res;
                                if (isNeedInitWorkflowForm) {
                                  window.open(
                                    `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                                      {
                                        businessOrderNo,
                                        businessType,
                                      }
                                    )}`
                                  );
                                } else {
                                  message.success(
                                    `${tags?.orderStatus === 1 ? '取消' : '审批'}成功`
                                  );
                                }
                                refreshRef.current.refresh();
                              })
                              .catch((e) => {
                                if (e.code === 5005012) {
                                  goodsNoStock({ list: JSON.parse(e.message) });
                                } else {
                                  message.error(e.message);
                                }
                              });
                          }
                        } else if (name === '提交') {
                          postPlatOutOrderSubmit({ id: tags?.id }).then(() => {
                            message.success(`提交成功`);
                            refreshRef.current.refresh();
                          });
                        } else if (name === '删除') {
                          stockOutDelete(tags.id).then(() => {
                            message.success('删除成功');
                            refreshRef.current.refresh();
                          });
                        } else if (name === '编辑') {
                          // editEntryList(tags.id, tags.businessName);
                          navigator(`/stock/create?stockType=out&id=${tags.id}`);
                        }
                      }}
                    />
                  }
                >
                  <Icon className={styles.icon} name="zu13366" />
                </Popover>
              ) : (
                <Icon className={classNames(styles.icon, styles.iconColor)} name="zu13366" />
              ),
          },
        ]}
      />
      <OrderAdd
        OrderCreact={id ? stockOutEdit : stockOutAdd}
        orderDeteil={stockOutDetail}
        visible={showOrderAdd}
        orderType={10}
        orderName={orderName}
        id={id}
        onClose={() => {
          setShowOrderAdd(false);
        }}
        openUpload={() => {
          setShowOrderAdd(false);
          setShowOrderUpload(true);
        }}
        submit={() => {
          // refreshRef.current.refresh();
          setShowOrderAdd(false);
          setFilter({ ...filter, pageNo: 1 });
        }}
      />
      <OrderFilter
        orderType={10}
        visible={showOrderFilter}
        startDate={filter.startDate}
        endDate={filter.endDate}
        status={orderStatusParams.get('orderStatus') || null}
        onClose={() => {
          setShowOrderFilter(false);
        }}
        confirm={(e) => {
          setShowOrderFilter(false);
          setFilter({
            ...e,
            warehouseIds: e.warehouseIds?.includes(0) ? [] : e?.warehouseIds,
            orderStatus: filter.orderStatus,
            businessType: e.businessType || null,
          });
        }}
      />
      <OrderDetail
        orderDetele={stockOutDelete}
        orderConfirm={stockOutConfirm}
        orderSubmit={postPlatOutOrderSubmit}
        confirm={() => {
          setShowOrderDetail(false);
          refreshRef.current.refresh();
        }}
        openUpload={() => {
          setShowOrderAdd(false);
          setShowOrderDetail(false);
          setShowOrderUpload(true);
        }}
        id={orderId}
        visible={showOrderDeatil}
        onClose={() => {
          setShowOrderDetail(false);
        }}
        orderDeteil={stockOutDetail}
        orderType={10}
        editEntryList={() => {
          editEntryList(orderId, orderName);
        }}
        orderPrintPdf={() => {
          printPageRef.current?.handlePrint();
        }}
      />
      <OrderUpload
        visible={showOrderUpload}
        orderType={10}
        id={orderId}
        onClose={() => {
          refreshRef.current.refresh();
          setShowOrderUpload(false);
        }}
      />
      <PrintPage ref={printPageRef} />
      <ImportDetail importValue={1} visible={showImport} onClose={() => setShowImport(false)} />
    </Context>
  );
}

export default Out;
