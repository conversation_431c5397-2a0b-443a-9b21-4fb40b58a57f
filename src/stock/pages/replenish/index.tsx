import React, { useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Context, Icon, List, ListInstance } from '@/components';
import { Dropdown, Menu, Button, message, Modal } from 'antd';
import {
  stockReplenishList,
  StockReplenishListResult,
  StockGoodsListResult,
  stockReplenishDetele,
  stockReplenishAudit,
  stockReplenishSubmit,
} from '@/apis';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import getStateTag from '@@/stock/utils/get-state-tag';
import debounce from 'lodash/debounce';
import purchaseOrderCreate from '@@/stock/pages/replenish/purchase-order-create';
import { testPerm } from '@/utils/permission';
import SelectGoods from './replenish-create/select-goods/select-goods';
import ReplenishCreate from './replenish-create/replenish-create';
import goodsInfoSetup from './replenish-create/goods-info-setup';
import { replenishFilter } from './replenish-filter/replenish-filter';
// import { replenishDetail } from './replenish-detail/replenish-detail';
import { filterList, handleList } from './base-data';
import styles from './index.module.less';

function Replenish() {
  const navigate = useNavigate();
  const refreshRef = useRef(null as unknown as ListInstance);
  const [requestParams, setResultParams] = useState({
    pageNo: 1,
    orderStatus: '',
    key: '',
    replenishType: '',
    operatStartDate: '',
    operatEndDate: '',
    auditStartDate: '',
    auditEndDate: '',
  });
  const [showSelectGoods, setShowSelectGoods] = useState(false);
  const [showReplenishCreate, setShowReplenishCreate] = useState(false);
  const [hasSelectGoods, setHasSelectGoods] = useState<StockGoodsListResult[]>([]);
  const selectType = useRef(0);

  const onSelectGoods = (val: StockGoodsListResult[], type: number) => {
    selectType.current = type;
    goodsInfoSetup({
      isAdd: true,
      hasSelectGoods: [...val],
      onBack: (arr) => {
        if (selectType.current === 1) {
          setShowSelectGoods(true);
        }
        if (selectType.current === 2) {
          setShowReplenishCreate(true);
        }
        setHasSelectGoods([...arr]);
      },
      onSuccess: (arr) => {
        setHasSelectGoods([...arr]);
        setShowReplenishCreate(true);
        setShowSelectGoods(false);
      },
      onAddGoods: (arr) => {
        setHasSelectGoods([...arr]);
        setShowReplenishCreate(false);
        setShowSelectGoods(true);
      },
    });
  };

  const handleInfo = (state: number) => {
    switch (state) {
      case 0: // 待审批
        return handleList.filter((item) => [2, 3, 4].includes(item.key));
      case 1: // 待生成订单
        return handleList.filter((item) => [5].includes(item.key));
      case 2: // 草稿
        return handleList.filter((item) => [1, 2, 3, 4].includes(item.key));
      case 4: // 待生成订单
        return handleList.filter((item) => [5].includes(item.key));
      default:
        return [];
    }
  };

  const onAudit = (id: number) => {
    stockReplenishAudit({ id, orderStatus: 1 })
      .then((res) => {
        if (res.isNeedInitWorkflowForm) {
          navigate(
            `/admin/process/applyProcess?pKey=${res.processDefinitionKey}&name=${
              res.processDefinitionName
            }&backfillType=5&inputParamJsonStr=${JSON.stringify({
              businessOrderNo: res.businessOrderNo,
              businessType: res.businessType,
            })}`
          );
        } else {
          message.success('审批成功');
        }
      })
      .finally(() => {
        refreshRef.current.refresh();
      });
  };

  const onHandle = (key: number, id: number, orderNo: number, type?: number) => {
    switch (key) {
      case 1:
        if (!testPerm(['AV_001_010_005'])) return;
        Modal.confirm({
          title: '提示',
          content: `确定提交`,
          okText: '确定',
          icon: '',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            stockReplenishSubmit({ id }).then(() => {
              message.success('提交成功');
              refreshRef.current.refresh();
            });
          },
        });
        break;
      case 2:
        if (!testPerm(['AV_001_010_002'])) return;
        // goodsInfoSetup({
        //   id,
        //   onSuccess: () => {
        //     refreshRef.current.refresh();
        //   },
        //   onPerm: (val) => {
        //     testPerm(val);
        //   },
        // });
        navigate(`/stock/create?stockType=replenish&id=${id}`);
        break;
      case 3:
        if (!testPerm(['AV_001_010_006'])) return;
        if (type) {
          onAudit(id);
          return;
        }
        Modal.confirm({
          title: '提示',
          content: `确定审批`,
          okText: '确定',
          icon: '',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            onAudit(id);
          },
        });
        break;
      case 4:
        if (!testPerm(['AV_001_010_004'])) return;
        Modal.confirm({
          title: '提示',
          content: `确定删除该条补货单吗？`,
          okText: '确定',
          icon: '',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            stockReplenishDetele({ id }).then(() => {
              message.success('删除成功');
              refreshRef.current.refresh();
            });
          },
        });
        break;
      case 5:
        if (!testPerm(['AV_001_010_008'])) return;
        purchaseOrderCreate({
          id,
          orderNo,
          onConfirm: (val) => {
            if (val) {
              navigate(`/indent/submit?replenishOrderNo=${orderNo}`);
            } else {
              navigate(`/indent/list?orderStatus=0&region=0&open=0&replenishOrderNo=${orderNo}`);
            }
          },
        });
        break;
      default:
        break;
    }
  };

  const onFilter = () => {
    replenishFilter({
      info: {
        replenishType: requestParams.replenishType,
        operatStartDate: requestParams.operatStartDate,
        operatEndDate: requestParams.operatEndDate,
        auditStartDate: requestParams.auditStartDate,
        auditEndDate: requestParams.auditEndDate,
      },
      onConfirm: (val) => {
        setResultParams({
          ...requestParams,
          ...val,
        });
      },
    });
  };

  // const onDetail = (id: number, orderNo: number) => {
  //   replenishDetail({
  //     id,
  //     onConfirm: (type, idVallue, orderNoValue) => {
  //       if (type && idVallue && orderNoValue) {
  //         onHandle(type, idVallue, orderNoValue, 1);
  //       }
  //       refreshRef.current.refresh();
  //     },
  //     onGetOrder: (val) => {
  //       if (val) {
  //         navigate(`/indent/submit?replenishOrderNo=${orderNo}`);
  //       } else {
  //         navigate(`/indent/list?orderStatus=0&region=0&open=0&replenishOrderNo=${orderNo}`);
  //       }
  //     },
  //   });
  // };

  const onSearch = debounce((e: string) => {
    setResultParams({ ...requestParams, key: e });
  }, 300);

  const isFilter = useMemo(() => {
    if (
      requestParams?.operatStartDate ||
      requestParams?.operatEndDate ||
      requestParams?.auditStartDate ||
      requestParams?.auditEndDate ||
      requestParams?.replenishType !== ''
    ) {
      return true;
    }
    return false;
  }, [requestParams]);

  const head = (
    <Context.Head
      onSearch={(e) => onSearch(e)}
      onFilter={onFilter}
      isFilterActive={isFilter}
      quickFilter={
        <Context.QuickFilter
          label="显示"
          value={requestParams.orderStatus}
          options={filterList}
          dropdownMatchSelectWidth={120}
          onChange={(value) => {
            setResultParams({
              ...requestParams,
              orderStatus: value,
            });
          }}
        />
      }
      placeholder="业务员 | 订单编号"
      title={[
        {
          title: '仓库管理',
          to: '/stock',
        },
        '补货单',
      ]}
      extra={
        <Context.HeadTool>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              if (!testPerm(['AV_001_010_001'])) return;
              // setHasSelectGoods([]);
              // setShowSelectGoods(true);
              navigate('/stock/create?stockType=replenish');
            }}
          >
            新建补货单
          </Button>
        </Context.HeadTool>
      }
    />
  );

  const columns: ColumnsType<StockReplenishListResult> = [
    {
      title: '订单信息',
      width: '30%',
      render: (item) => (
        <div className={styles.orderInfo}>
          <img className={styles.status} src={getStateTag(item.orderStatus)} alt="" />
          <span title={item.orderNo}>{item.orderNo}</span>
        </div>
      ),
    },
    {
      title: '补货类型',
      align: 'center',
      width: '20%',
      dataIndex: 'replenishTypeStr',
    },
    {
      title: '审批日期',
      width: '20%',
      align: 'center',
      render: (item) => (
        <div>{item.auditTime ? dayjs(item.auditTime).format('YYYY-MM-DD') : '--'}</div>
      ),
    },
    {
      title: '操作信息',
      width: '20%',
      align: 'center',
      render: (item) => (
        <div>
          <div>{item.operator}</div>
          <div>{item.createTime ? dayjs(item.createTime).format('YYYY-MM-DD') : '--'}</div>
        </div>
      ),
    },
    {
      title: '操作',
      align: 'center',
      render: (item) => (
        <div className={styles.text}>
          {handleInfo(item.orderStatus).length > 0 ? (
            <Dropdown
              overlay={
                <Menu
                  onClick={({ key, domEvent }) => {
                    domEvent.stopPropagation();
                    onHandle(Number(key), item.id, item.orderNo);
                  }}
                  className={styles.menu}
                  items={handleInfo(item.orderStatus)}
                />
              }
              placement="bottom"
            >
              <Icon className={styles.handleIcon} name="zu13366" />
            </Dropdown>
          ) : (
            <Icon className={styles.handleIconDisabled} name="zu13366" />
          )}
        </div>
      ),
    },
  ];

  return (
    <Context head={head} permission="AV_001_010">
      <List
        ref={refreshRef}
        request={stockReplenishList}
        params={requestParams}
        onRow={(item) => ({
          onClick: () => {
            // onDetail(item.id, item.orderNo);
            navigate(`/stock/detail?stockType=replenish&id=${item.id}`);
          },
        })}
        columns={columns}
        rowKey={(data) => `${data.id}`}
      />
      <SelectGoods
        visible={showSelectGoods}
        hasSelectGoodsList={hasSelectGoods}
        onClose={() => setShowSelectGoods(false)}
        onConfirm={(val) => onSelectGoods(val, 1)}
      />
      <ReplenishCreate
        visible={showReplenishCreate}
        hasSelectGoods={hasSelectGoods}
        onClose={() => setShowReplenishCreate(false)}
        onAddGoods={(arr) => {
          setShowSelectGoods(true);
          setHasSelectGoods([...arr]);
        }}
        onConfirm={() => {
          setResultParams({
            ...requestParams,
            pageNo: 1,
          });
        }}
        onViewDetail={(val) => onSelectGoods(val, 2)}
      />
    </Context>
  );
}

export default Replenish;
