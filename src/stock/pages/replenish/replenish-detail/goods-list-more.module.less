@import 'styles/mixins/mixins';

.drawer {
  width: 375px !important;
  right: 0 !important;
  left: auto !important;

  :global {
    .ant-drawer-content-wrapper {
      height: 77% !important;
    }

    .ant-drawer-content {
      width: 375px !important;
      right: 0 !important;
      left: auto !important;
      box-shadow: none;
      background-color: #f5f6fa;
    }

    .ant-drawer-mask {
      height: 80% !important;
      border-radius: 12px;
      background: rgb(0 0 0 / 60%) !important;
    }
  }
}

.close {
  font-size: 20px;
  cursor: pointer;
}

.search {
  margin: 10px 0 20px;
}

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  border-radius: 18px;
  background-color: #fff;
}

.title {
  font-weight: 600;
  display: flex;
  padding: 16px 0;
  align-items: center;
}

.supplierName {
  .text-overflow(1);
}

.titleIcon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.noData {
  display: flex;
  height: calc(100% - 80px);
  justify-content: center;
  align-items: center;
}
