@import 'styles/mixins/mixins';

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.cardDetail {
  padding: 0 20px 10px;
  background: linear-gradient(110deg, #1b5aff 0%, #8657fd 99%);
}

.title {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  padding: 20px 0;
  align-items: center;
}

.goodsTitle {
  font-size: 16px;
  font-weight: 600;
  padding: 20px 0 4px;
}

.supplierTitle {
  font-weight: 600;
  display: flex;
  padding: 16px 0;
  align-items: center;
}

.supplierName {
  .text-overflow(1);
}

.titleIcon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.state {
  height: 18px;
  margin-right: 8px;
}

.item {
  color: #fff;
  display: flex;
  margin-bottom: 8px;
}

.footer {
  display: flex;
  padding: 24px 20px;

  :global {
    .ant-btn {
      width: 100%;
    }

    .ant-btn-default {
      margin-right: 16px;
    }
  }
}

.handleIcon {
  color: #000;
  font-size: 20px;
  cursor: pointer;
}

.menu {
  :global {
    .ant-dropdown-menu-item {
      text-align: center;
    }
  }
}

.more {
  display: flex;
  height: 54px;
  justify-content: flex-end;
  align-items: center;
}

.moreText {
  font-size: 20px;
  padding: 0 8px;
}

.lookMore {
  display: flex;
  align-items: center;
  margin-left: 4px;
  cursor: pointer;
}
