import React, { useMemo, useState, useEffect } from 'react';
import { Drawer, Empty, Icon, Search } from '@/components';
import { StockGoodsListResult } from '@/apis';
import ReplenishGoodsItem from '@@/stock/pages/replenish/replenish-create/replenish-goods-item';
import cloneDeep from 'lodash/cloneDeep';
import mergeArray from '../../../utils/merge-array';
import styles from './goods-list-more.module.less';

interface GoodsListMoreProps {
  visible: boolean;
  list: StockGoodsListResult[];
  onClose: () => void;
}

function GoodsListMore({ visible, list, onClose, ...props }: GoodsListMoreProps) {
  const [searchVal, setSearchVal] = useState('');
  const [searchList, setSearchList] = useState<StockGoodsListResult[]>([]);

  const onSearch = (val: string) => {
    setSearchVal(val);
    const arr = list.filter(
      (each: any) => each.supplierName?.includes(val) || each.skuName?.includes(val)
    );
    // @ts-ignore
    setSearchList(cloneDeep(mergeArray(arr)));
  };

  const goodsList = useMemo(() => {
    const arr = mergeArray(list);
    return searchVal.length > 0 ? searchList : arr;
  }, [list, searchList, searchVal]);

  useEffect(() => {
    if (visible) {
      setSearchVal('');
    }
  }, [visible]);

  return (
    <Drawer
      closable={false}
      onClose={onClose}
      visible={visible}
      className={styles.drawer}
      placement="bottom"
      title="全部商品"
      {...props}
      extra={<Icon className={styles.close} name="close" onClick={() => onClose()} />}
    >
      <div className={styles.search}>
        <Search placeholder="商品/补货供应商名称" onSearch={(e) => onSearch(e)} />
      </div>
      {goodsList.length > 0 && goodsList.some((item) => item.goodsList.length) ? (
        goodsList.map(
          (item) =>
            item.goodsList.length > 0 && (
              <div className={styles.card}>
                <div className={styles.title}>
                  <img
                    className={styles.titleIcon}
                    src="https://img.huahuabiz.com/user_files/2023619/1687145629532698.png"
                    alt=""
                  />
                  <span className={styles.supplierName} title={item.supplierName}>
                    补货供应商 {item.supplierName}
                  </span>
                </div>
                <div>
                  {item.goodsList.map((goods: any) => (
                    <ReplenishGoodsItem
                      key={goods.id}
                      supplierName={item.supplierName || ''}
                      image={
                        goods.images ||
                        'https://img.huahuabiz.com/PC/static/img/default-product.png'
                      }
                      name={goods.skuName || ''}
                      skuBelongType={Number(goods.skuBelongType)}
                      standard={JSON.parse(String(goods.standardJson))}
                      unit={goods.unitName}
                      number={goods.quantity}
                    />
                  ))}
                </div>
              </div>
            )
        )
      ) : (
        <div className={styles.noData}>
          <Empty message="暂⽆相关内容" description="换个关键词再试试吧！" />
        </div>
      )}
    </Drawer>
  );
}

export default GoodsListMore;
