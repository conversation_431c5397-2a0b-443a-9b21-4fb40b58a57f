import React, { useEffect, useMemo, useState } from 'react';
import { useRequest } from 'ahooks';
import { Drawer, Icon } from '@/components';
import { drawerPopup } from '@/utils/popup';
import { DrawerProps, Button, Dropdown, Menu, message, Modal } from 'antd';
import classNames from 'classnames';
import {
  stockReplenishDetail,
  StockReplenishDetailResult,
  stockReplenishDetele,
  stockReplenishSubmit,
} from '@/apis';
import ReplenishGoodsItem from '@@/stock/pages/replenish/replenish-create/replenish-goods-item';
import GoodsListMore from '@@/stock/pages/replenish/replenish-detail/goods-list-more';
import goodsInfoSetup from '@@/stock/pages/replenish/replenish-create/goods-info-setup';
import purchaseOrderCreate from '@@/stock/pages/replenish/purchase-order-create';
import { testPerm } from '@/utils/permission';
import cloneDeep from 'lodash/cloneDeep';
import mergeArray from '@@/stock/utils/merge-array';
import getStateTag from '../../../utils/get-state-tag';
import { handleList } from '../base-data';
import styles from './replenish-detail.module.less';

interface RaplenishDetailProps extends DrawerProps {
  id: number;
  onClose?: () => void;
  onConfirm: MultipleParamsFn<[type?: number, id?: number, orderNo?: number]>;
  onGetOrder: MultipleParamsFn<[type: number]>;
}

function ReplenishDetail({ id, onClose, onConfirm, onGetOrder, ...props }: RaplenishDetailProps) {
  const [info, setInfo] = useState<StockReplenishDetailResult>({
    auditId: 0,
    auditName: '',
    auditTime: '',
    createTime: '',
    id: 0,
    operator: '',
    operatorId: 0,
    orderNo: 0,
    orderStatus: 2,
    orderStatusStr: '',
    quantityKind: 0,
    replenishPlanSkuVOS: [],
    replenishType: 0,
    replenishTypeStr: '',
  });
  const [showGoodsListMore, setShowGoodsListMore] = useState(false);

  const { run } = useRequest(stockReplenishDetail, {
    manual: true,
    defaultParams: [{ id }],
    onSuccess: (res) => {
      setInfo({ ...res });
    },
  });

  const onHandle = (key: number) => {
    switch (key) {
      case 1:
        if (!testPerm(['AV_001_010_005'])) return;
        Modal.confirm({
          title: '提示',
          content: `确定提交`,
          okText: '确定',
          icon: '',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            stockReplenishSubmit({ id }).then(() => {
              message.success('提交成功');
              onClose?.();
              onConfirm();
            });
          },
        });
        break;
      case 2:
        if (!testPerm(['AV_001_010_002'])) return;
        goodsInfoSetup({
          id,
          onSuccess: () => {
            onConfirm();
            run({ id });
          },
          onPerm: (val) => {
            testPerm(val);
          },
        });
        break;
      case 3:
        if (!testPerm(['AV_001_010_006'])) return;
        Modal.confirm({
          title: '提示',
          content: `确定审批`,
          okText: '确定',
          icon: '',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            onConfirm(3, info.id, info.orderNo);
          },
        });
        break;
      case 4:
        if (!testPerm(['AV_001_010_004'])) return;
        Modal.confirm({
          title: '提示',
          content: `确定删除该条补货单吗？`,
          okText: '确定',
          icon: '',
          cancelText: '取消',
          centered: true,
          onOk: () => {
            stockReplenishDetele({ id }).then(() => {
              message.success('删除成功');
              onClose?.();
              onConfirm();
            });
          },
        });
        break;
      case 5:
        if (!testPerm(['AV_001_010_008'])) return;
        purchaseOrderCreate({
          id,
          orderNo: info.orderNo,
          onConfirm: (val) => onGetOrder(val),
        });
        break;
      default:
        break;
    }
  };

  const onCloseDrawer = () => {
    if (showGoodsListMore) {
      setShowGoodsListMore(false);
    } else {
      onClose?.();
    }
  };

  const goodsList = useMemo(() => {
    const arr = info.replenishPlanSkuVOS.filter((item, index) => index < 2);
    return {
      arr: cloneDeep(mergeArray(arr)),
      length: info.replenishPlanSkuVOS.length,
    };
  }, [info]);

  useEffect(() => {
    run({ id });
  }, [id, run]);

  const extra = (
    <Dropdown
      overlay={
        <Menu
          onClick={({ key }) => {
            onHandle(Number(key));
          }}
          className={styles.menu}
          items={handleList.filter((item: any) => [2, 4].includes(item.key))}
        />
      }
      placement="bottom"
    >
      <Icon className={styles.handleIcon} name="zu13366" />
    </Dropdown>
  );

  const footer = (
    <div className={styles.footer}>
      {info.orderStatus === 2 && (
        <Button type="default" onClick={() => onHandle(1)}>
          提交
        </Button>
      )}
      {[0, 2].includes(info.orderStatus) && (
        <Button type="primary" onClick={() => onHandle(3)}>
          审批
        </Button>
      )}
      {info.orderStatus === 4 && (
        <Button type="primary" onClick={() => onHandle(5)}>
          生成采购单
        </Button>
      )}
    </div>
  );

  return (
    <Drawer
      onClose={onCloseDrawer}
      push={false}
      {...props}
      title="补货详情"
      footer={info.id && footer}
      extra={[0, 2].includes(info.orderStatus) && extra}
    >
      <div className={classNames(styles.card, styles.cardDetail)}>
        <div className={styles.title}>
          <img className={styles.state} src={getStateTag(info.orderStatus)} alt="" />
          {info.replenishType === 1 ? '手动补货' : '自动补货'}
        </div>
        <div className={styles.item}>
          <span className="mr-3">单据编号</span>
          <span>{info.orderNo}</span>
        </div>
        <div className={styles.item}>
          <span className="mr-3">操作人</span>
          <span>{info.operator}</span>
        </div>
        {[4, 5].includes(info.orderStatus) && (
          <div className={styles.item}>
            <span className="mr-3">审核人</span>
            <span>{info.auditName}</span>
          </div>
        )}
      </div>
      <div className={styles.card}>
        <div className={styles.goodsTitle}>商品信息</div>
        {goodsList.arr.map((item) => (
          <>
            <div className={styles.supplierTitle}>
              <img
                className={styles.titleIcon}
                src="https://img.huahuabiz.com/user_files/2023619/1687145629532698.png"
                alt=""
              />
              <span className={styles.supplierName} title={item.supplierName}>
                补货供应商 {item.supplierName}
              </span>
            </div>
            <div>
              {item.goodsList.map((goods: any) => (
                <ReplenishGoodsItem
                  key={goods.id}
                  supplierName={item.supplierName || ''}
                  image={
                    goods.images || 'https://img.huahuabiz.com/PC/static/img/default-product.png'
                  }
                  name={goods.skuName || ''}
                  skuBelongType={Number(goods.skuBelongType)}
                  standard={JSON.parse(String(goods.standardJson))}
                  unit={goods.unitName}
                  number={goods.quantity}
                  barsCode={goods.barsCode}
                />
              ))}
            </div>
          </>
        ))}
        {goodsList.length > 2 && (
          <div className={styles.more}>
            <span>共</span>
            <span className={styles.moreText}>{info.quantityKind}</span>
            <span>种商品</span>
            <div
              role="button"
              tabIndex={0}
              className={styles.lookMore}
              onClick={() => setShowGoodsListMore(true)}
            >
              <span>查看全部</span>
              <Icon name="right" />
            </div>
          </div>
        )}
      </div>
      <GoodsListMore
        visible={showGoodsListMore}
        onClose={() => setShowGoodsListMore(false)}
        list={info.replenishPlanSkuVOS}
      />
    </Drawer>
  );
}

ReplenishDetail.defaultProps = {
  onClose: () => {},
};

export const replenishDetail = (props: RaplenishDetailProps) => {
  drawerPopup(ReplenishDetail, props);
};

export default replenishDetail;
