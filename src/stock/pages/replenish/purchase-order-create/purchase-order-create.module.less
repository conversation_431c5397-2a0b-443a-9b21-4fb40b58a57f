@import 'styles/mixins/mixins';

.modal {
  :global {
    .ant-modal-title {
      font-size: 16px;
      font-weight: 600;
      position: relative;

      &::before {
        content: '*';
        color: red;
        position: absolute;
        top: 2px;
        left: -8px;
      }
    }

    .ant-modal-header {
      padding: 20px;
      border: none;
    }

    .ant-modal-body {
      padding: 0 20px;
    }
  }
}

.content {
  height: 450px;
  overflow: auto;
}

.title {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  padding: 8px 0 16px;
  align-items: center;
}

.titleIcon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
}

.text-align-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header {
  display: flex;
  height: 38px;
  background: #f5f6fa;
  border-radius: 10px;
}

.goodsInfo {
  display: flex;
  width: 302px;
  padding: 0 20px;
  .text-align-center();
}

.price {
  width: 128px;
  .text-align-center();
}

.num {
  width: 148px;
  .text-align-center();

  :global {
    .ant-input-number-input {
      text-align: center;
      padding: 0 10px;
    }
  }
}

.payWay {
  width: 210px;
  .text-align-center();

  padding: 0 10px;

  :global {
    .ant-select {
      width: 100%;
    }
  }
}

.goodsItem {
  display: flex;
  padding: 12px 0;
}

.goodsInfoImg {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  margin-right: 12px;
}

.goodsInfoContent {
  display: flex;
  height: 64px;
  justify-content: space-between;
  flex: 1;
  flex-direction: column;
}

.name {
  .text-overflow(2);
}

.nameTag {
  width: 28px;
  height: 18px;
  margin-right: 4px;
  vertical-align: middle;
}

.standard {
  color: #888b98;
  .text-overflow(1);
}

.standardItem:last-child .standardTag {
  display: none;
}

.totalPrice {
  color: #ea1c26;
  display: flex;
  padding: 20px 0;
  justify-content: flex-end;
  align-items: center;
}

.priceVal {
  font-size: 18px;
}
