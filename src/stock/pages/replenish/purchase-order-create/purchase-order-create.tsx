import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { Modal, ModalProps, Select, message, Spin } from 'antd';
import { stockReplenishGetOrder, ReplenishShopVOList } from '@/apis';
import { cloneDeep } from 'lodash';
import isFunction from 'lodash/isFunction';
import GoodsItemStandard from '../../../components/goods-item-standard';
import styles from './purchase-order-create.module.less';

export interface PurchaseOrderCreateProps extends ModalProps {
  id: number;
  orderNo: number;
  onConfirm: MultipleParamsFn<[type: number]>;
}

function PurchaseOrderCreate({ id, orderNo, onConfirm, ...props }: PurchaseOrderCreateProps) {
  const [list, setList] = useState<ReplenishShopVOList[]>([]);

  const { run, loading } = useRequest(stockReplenishGetOrder, {
    manual: true,
    defaultParams: [{ id }],
    onSuccess: (res) => {
      setList(res.replenishShopVOList);
    },
  });

  const onChangePayWay = (val: string, shopSkuId: number) => {
    list.forEach((shop) => {
      shop.replenishPlanSkuVOS.forEach((good) => {
        const goods = good;
        if (goods.shopSkuId === shopSkuId) {
          goods.haveSetPayWayNo = val;
        }
      });
    });
    setList(cloneDeep(list));
    // stockReplenishPayWay({ orderNo, payWayNo: val, shopSkuId }).then(() => {});
  };

  const onOk: MouseEventHandler<HTMLElement> = (e) => {
    if (!list.length) {
      run({ id });
      return;
    }
    if (list.some((shop) => shop.replenishPlanSkuVOS.some((item) => !item.haveSetPayWayNo))) {
      message.warning('请选择付款方式');
      return;
    }
    const skuBelongType = list.some((item) =>
      item.replenishPlanSkuVOS.some((each) => each.skuBelongType)
    )
      ? 1
      : 0;

    const data = list.map((item) => ({
      shopId: item.shopId,
      replenishOrderNo: orderNo,
      productParamList: item.replenishPlanSkuVOS.map((goods) => ({
        shopSkuId: goods.shopSkuId,
        skuId: goods.skuId,
        payWayNo: goods.haveSetPayWayNo,
        number: goods.quantity,
        productUnit: goods.unitName,
      })),
    }));
    if (skuBelongType) {
      localStorage.setItem('CARTLIST', JSON.stringify(data));
    } else {
      localStorage.setItem('ORDERCREATE', JSON.stringify(list));
    }

    if (isFunction(props.onCancel)) {
      props.onCancel(e);
    }
    onConfirm(skuBelongType);
  };

  useEffect(() => {
    run({ id });
  }, [id, run]);

  return (
    <Modal
      visible={props.visible}
      {...props}
      title="采购清单"
      width={700}
      centered
      wrapClassName={styles.modal}
      okText="去结算"
      onOk={onOk}
    >
      <Spin spinning={loading}>
        <div className={styles.content}>
          {list.map((shop) => (
            <div>
              <div className={styles.title}>
                <img
                  className={styles.titleIcon}
                  src="https://img.huahuabiz.com/user_files/2023619/1687145629532698.png"
                  alt=""
                />
                <div>{shop.supplierName}</div>
              </div>
              <div className={styles.header}>
                <div className={styles.goodsInfo}>商品属性</div>
                <div className={styles.price}>单位</div>
                <div className={styles.num}>补货数量</div>
                <div className={styles.payWay}>付款方式</div>
              </div>
              {shop.replenishPlanSkuVOS.map((goods) => (
                <div className={styles.goodsItem}>
                  <div className={styles.goodsInfo}>
                    <img className={styles.goodsInfoImg} src={goods.images} alt="" />
                    <div className={styles.goodsInfoContent}>
                      <div className={styles.name}>
                        {goods.skuBelongType === 1 && (
                          <img
                            className={styles.nameTag}
                            src="https://img.huahuabiz.com/user_files/2023625/1687686014486265.png"
                            alt=""
                          />
                        )}
                        <span>{goods.skuName}</span>
                      </div>
                      <GoodsItemStandard standard={JSON.parse(goods.standardJson)} />
                    </div>
                  </div>
                  <div className={styles.price}>{goods.unitName || '--'}</div>
                  <div className={styles.num}>
                    {goods.quantity}
                    {goods.unit}
                  </div>
                  <div className={styles.payWay}>
                    <Select
                      placeholder="请选择"
                      value={Number(goods.haveSetPayWayNo) || null}
                      options={goods.replenishPayWayVOList}
                      fieldNames={{
                        label: 'name',
                        value: 'payWayNo',
                      }}
                      onChange={(e) => onChangePayWay(`${e}`, goods.shopSkuId)}
                    />
                  </div>
                </div>
              ))}
            </div>
          ))}
        </div>
      </Spin>
    </Modal>
  );
}

export default PurchaseOrderCreate;
