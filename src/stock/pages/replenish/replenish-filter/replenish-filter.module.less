.card {
  margin-bottom: 20px;
  padding: 0 20px;
  overflow: hidden;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.title {
  font-weight: 600;
  padding: 16px 0;
}

.list {
  margin-bottom: 20px;
}

.item {
  display: inline-block;
  width: 92px;
  height: 28px;
  line-height: 28px;
  text-align: center;
  margin-right: 12px;
  border-radius: 10px;
  background: #f5f6fa;
  cursor: pointer;

  &:nth-child(3n) {
    margin-right: 0;
  }
}

.itemActive {
  color: #008cff;
  background: #d9eeff;
  border: 1px solid #008cff;
}

.time {
  color: #888b98;
  display: flex;
  justify-content: space-between;
  align-items: center;

  :global {
    .ant-picker {
      width: 138px !important;
      height: 28px;
      padding: 0 !important;
      background-color: #f5f6fa !important;
    }

    .ant-picker-input > input {
      text-align: center !important;
    }

    .ant-picker-suffix {
      display: none;
    }
  }

  &:last-child {
    margin-bottom: 20px;
  }
}
