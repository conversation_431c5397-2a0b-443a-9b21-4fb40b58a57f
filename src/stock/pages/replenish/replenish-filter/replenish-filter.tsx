import { useEffect, useState } from 'react';
import { DatePicker, Drawer } from '@/components';
import { drawerPopup } from '@/utils/popup';
import { DrawerProps, message } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import styles from './replenish-filter.module.less';

interface SelectParams {
  replenishType: string;
  operatStartDate: string;
  operatEndDate: string;
  auditStartDate: string;
  auditEndDate: string;
}

interface ReplenishFilterProps extends DrawerProps {
  info: SelectParams;
  onClose?: () => void;
  onConfirm: MultipleParamsFn<[info: SelectParams]>;
}

const stateList = [
  {
    id: '',
    label: '全部',
  },
  {
    id: '0',
    label: '自动补货',
  },
  {
    id: '1',
    label: '手动补货',
  },
];

const initData = {
  replenishType: '',
  operatStartDate: '',
  operatEndDate: '',
  auditStartDate: '',
  auditEndDate: '',
};

function ReplenishFilter({ info, onClose, onConfirm, ...props }: ReplenishFilterProps) {
  const [selectParams, setSelectParams] = useState<SelectParams>({ ...info });

  const onSelectParams = (val: string) => {
    setSelectParams({
      ...selectParams,
      replenishType: val,
    });
  };

  const onChangeTime = (e: any, type: string) => {
    if (type === 'operatStartDate' || type === 'auditStartDate') {
      setSelectParams({
        ...selectParams,
        [type]: e ? dayjs(e).startOf('day').valueOf() : '',
      });
    } else {
      setSelectParams({
        ...selectParams,
        [type]: e ? dayjs(e).endOf('day').valueOf() : '',
      });
    }
  };

  const onCancel = () => {
    setSelectParams({ ...initData });
  };

  const onOk = () => {
    if (
      selectParams.operatStartDate &&
      selectParams.operatEndDate &&
      selectParams.operatStartDate > selectParams.operatEndDate
    ) {
      message.warning('操作开始时间不能大于结束时间');
      return;
    }
    if (
      selectParams.auditStartDate &&
      selectParams.auditEndDate &&
      selectParams.auditStartDate > selectParams.auditEndDate
    ) {
      message.warning('审核开始时间不能大于结束时间');
      return;
    }
    onConfirm({ ...selectParams });
    onClose?.();
  };

  useEffect(() => {
    if (props.visible) {
      setSelectParams({ ...info });
    }
  }, [info, props.visible]);

  const footer = <Drawer.Footer okText="确定" cancelText="重置" onCancel={onCancel} onOk={onOk} />;

  return (
    <Drawer onClose={onClose} {...props} title="筛选" footer={footer}>
      <div className={styles.card}>
        <div className={styles.title}>补货计划</div>
        <div className={styles.list}>
          {stateList.map((item, index) => (
            <span
              role="button"
              tabIndex={index}
              className={classNames(styles.item, {
                [styles.itemActive]: selectParams.replenishType === item.id,
              })}
              key={item.id}
              onClick={() => onSelectParams(item.id)}
            >
              {item.label}
            </span>
          ))}
        </div>
      </div>
      <div className={styles.card}>
        <div className={styles.title}>操作时间</div>
        <div className={styles.time}>
          <DatePicker
            value={selectParams.operatStartDate ? dayjs(selectParams.operatStartDate) : null}
            onChange={(e) => onChangeTime(e, 'operatStartDate')}
            placeholder="开始时间"
            bordered={false}
            disabledDate={(current) =>
              current && current > dayjs(selectParams.operatEndDate).endOf('day')
            }
          />
          <span className={styles.line}>-</span>
          <DatePicker
            value={selectParams.operatEndDate ? dayjs(selectParams.operatEndDate) : null}
            onChange={(e) => onChangeTime(e, 'operatEndDate')}
            placeholder="结束时间"
            bordered={false}
            disabledDate={(current) =>
              current && current < dayjs(selectParams.operatStartDate).subtract(0, 'day')
            }
          />
        </div>
        <div className={styles.title}>审核时间</div>
        <div className={styles.time}>
          <DatePicker
            value={selectParams.auditStartDate ? dayjs(selectParams.auditStartDate) : null}
            onChange={(e) => onChangeTime(e, 'auditStartDate')}
            placeholder="开始时间"
            bordered={false}
            disabledDate={(current) =>
              current && current > dayjs(selectParams.auditEndDate).endOf('day')
            }
          />
          <span className={styles.line}>-</span>
          <DatePicker
            value={selectParams.auditEndDate ? dayjs(selectParams.auditEndDate) : null}
            onChange={(e) => onChangeTime(e, 'auditEndDate')}
            placeholder="结束时间"
            bordered={false}
            disabledDate={(current) =>
              current && current < dayjs(selectParams.auditStartDate).subtract(0, 'day')
            }
          />
        </div>
      </div>
    </Drawer>
  );
}

ReplenishFilter.defaultProps = {
  onClose: () => {},
};

export const replenishFilter = (props: ReplenishFilterProps) => {
  drawerPopup(ReplenishFilter, props);
};

export default replenishFilter;
