@import 'styles/mixins/mixins';

.replenishCreate {
  height: calc(100% - 1);
}

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  border-radius: 18px;
  background-color: #fff;
}

.cardAdd {
  display: flex;
  align-items: center;
  padding: 16px 20px;
}

.add {
  display: flex;
  width: 36px;
  height: 36px;
  margin-right: 12px;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  background: #d9eeff;
  cursor: pointer;
}

.submit {
  cursor: pointer;
}

.more {
  display: flex;
  height: 54px;
  justify-content: space-between;
  align-items: center;
}

.moreDetail {
  cursor: pointer;
  color: #008cff;
}

.footer {
  display: flex;
  padding: 24px 20px;

  :global {
    .ant-btn {
      width: 100%;
    }

    .ant-btn:first-child {
      margin-right: 15px;
    }
  }
}

.noData {
  display: flex;
  height: calc(100% - 100px);
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.supplierTitle {
  font-weight: 600;
  display: flex;
  padding: 16px 0;
  align-items: center;
}

.supplierName {
  .text-overflow(1);
}

.titleIcon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}
