import React, { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { StockGoodsListResult, stockReplenishCreate } from '@/apis';
import { Drawer, Icon, Empty } from '@/components';
import { Button, message, Modal } from 'antd';
import classNames from 'classnames';
import cloneDeep from 'lodash/cloneDeep';
import debounce from 'lodash/debounce';
import mergeArray from '@@/stock/utils/merge-array';
import { testPerm } from '@/utils/permission';
import ReplenishGoodsItem from './replenish-goods-item';
import styles from './replenish-create.module.less';

export interface ReplenishCreateProps {
  visible: boolean;
  hasSelectGoods: StockGoodsListResult[];
  onClose: () => void;
  onAddGoods: MultipleParamsFn<[arr: StockGoodsListResult[]]>;
  onConfirm: () => void;
  onViewDetail: MultipleParamsFn<[arr: StockGoodsListResult[]]>;
}

function ReplenishCreate({
  visible,
  hasSelectGoods,
  onClose,
  onAddGoods,
  onConfirm,
  onViewDetail,
  ...props
}: ReplenishCreateProps) {
  const navigate = useNavigate();
  const [showLoad, setShowLoad] = useState(false);
  const [list, setList] = useState<StockGoodsListResult[]>([...hasSelectGoods]);

  const onDelete = (id: number) => {
    Modal.confirm({
      title: '提示',
      icon: '',
      centered: true,
      width: 290,
      okText: '确定',
      cancelText: '取消',
      content: `确定删除该商品?`,
      getContainer: document.querySelector('.replenish-create') as HTMLElement,
      onOk: () => {
        const arr = list.filter((item) => item.id !== id);
        setList(cloneDeep(arr));
      },
    });
  };

  const onDetail = () => {
    onViewDetail([...list]);
  };

  const onOk = debounce((state: number) => {
    let code = '';
    switch (state) {
      case 0:
        code = 'AV_001_010_005';
        break;
      case 1:
        code = 'AV_001_010_006';
        break;
      case 2:
        code = 'AV_001_010_003';
        break;
      default:
        break;
    }
    if (!testPerm(code)) {
      return;
    }
    if (!list.length) {
      message.warning('请先选择商品');
      return;
    }
    setShowLoad(true);
    stockReplenishCreate({
      orderStatus: state,
      replenishSkuParams: list.map((item) => ({
        shopSkuId: item.shopSkuId || item.id,
        skuId: item.skuId,
        auxiliaryUnit: item.unit,
        unitName: item.unit,
        quantity: Number(item.number),
        supplierId: Number(item.supplierId),
        supplierName: String(item.supplierName),
        supplierCompanyId: Number(item.supplierCompanyId),
      })),
    })
      .then((res) => {
        let str = '';
        switch (state) {
          case 0:
            str = '提交';
            break;
          case 1:
            str = '审核';
            break;
          case 2:
            str = '保存';
            break;
          default:
            break;
        }
        onClose();
        onConfirm();
        if (res.isNeedInitWorkflowForm) {
          navigate(
            `/admin/process/applyProcess?pKey=${res.processDefinitionKey}&name=${
              res.processDefinitionName
            }&backfillType=5&inputParamJsonStr=${JSON.stringify({
              businessOrderNo: res.businessOrderNo,
              businessType: res.businessType,
            })}`
          );
          return;
        }
        message.success(`${str}成功`);
      })
      .finally(() => {
        setShowLoad(false);
      });
  }, 300);

  const goodsList = useMemo(() => {
    const arr = list.filter((item, index) => index < 2);
    return {
      arr: cloneDeep(mergeArray(arr)),
      length: list.length,
    };
  }, [list]);

  useEffect(() => {
    setList([...hasSelectGoods]);
  }, [hasSelectGoods]);

  const titleExtra = (
    <div
      role="button"
      aria-disabled={showLoad}
      tabIndex={0}
      className={styles.submit}
      onClick={() => onOk(2)}
    >
      保存
    </div>
  );

  const footer = (
    <div className={styles.footer}>
      <Button type="default" loading={showLoad} disabled={showLoad} onClick={() => onOk(0)}>
        保存并提交
      </Button>
      <Button type="primary" loading={showLoad} disabled={showLoad} onClick={() => onOk(1)}>
        保存并审批
      </Button>
    </div>
  );

  return (
    <Drawer
      title="新建补货单"
      visible={visible}
      onClose={onClose}
      {...props}
      extra={titleExtra}
      footer={footer}
    >
      <div className={classNames('replenish-create', styles.replenishCreate)}>
        <div className={classNames(styles.card, styles.cardAdd)}>
          <span
            role="button"
            tabIndex={0}
            className={styles.add}
            onClick={() => {
              onAddGoods([...list]);
              onClose();
            }}
          >
            <Icon name="plus" size={18} color="#008CFF" />
          </span>
          <span>添加商品</span>
        </div>
        {list.length > 0 ? (
          <div className={styles.card}>
            {goodsList.arr.map((shop) => (
              <>
                <div className={styles.supplierTitle}>
                  <img
                    className={styles.titleIcon}
                    src="https://img.huahuabiz.com/user_files/2023619/1687145629532698.png"
                    alt=""
                  />
                  <span className={styles.supplierName} title={shop.supplierName}>
                    补货供应商 {shop.supplierName}
                  </span>
                </div>
                {shop.goodsList.map((item: any) => (
                  <ReplenishGoodsItem
                    key={item.id}
                    supplierName={item.supplierName || ''}
                    image={
                      item.imagesList.length
                        ? item.imagesList[0]
                        : 'https://img.huahuabiz.com/PC/static/img/default-product.png'
                    }
                    name={item.name}
                    skuBelongType={Number(item.skuBelongType)}
                    standard={item.standardList}
                    unit={item.unit}
                    number={item.number}
                    isDelete
                    onDelete={() => onDelete(item.id)}
                    isLookDetail
                    onLookDetail={onDetail}
                  />
                ))}
              </>
            ))}
            {list.length > 0 && (
              <div className={styles.more}>
                <span>已设置{list.length}种商品的数量</span>
                <span role="button" tabIndex={0} className={styles.moreDetail} onClick={onDetail}>
                  <span>查看详情</span>
                  <Icon name="right" />
                </span>
              </div>
            )}
          </div>
        ) : (
          <div className={styles.noData}>
            <Empty />
          </div>
        )}
      </div>
    </Drawer>
  );
}

export default ReplenishCreate;
