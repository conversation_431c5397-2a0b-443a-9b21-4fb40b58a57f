@import 'styles/mixins/mixins';

.box {
  display: flex;
  padding: 16px 16px 16px 12px;
  position: relative;
}

.categoryListSecond {
  display: flex;
  align-items: center;
}

.categoryListSecondItem {
  display: inline-block;
  max-width: 120px;
  height: 28px;
  line-height: 28px;
  margin-right: 12px;
  padding: 0 16px;
  text-align: center;
  border-radius: 10px;
  background: #f3f3f3;
  cursor: pointer;
  .text-overflow(1) !important;

  &:last-child {
    margin-right: 0;
  }
}

.categoryListSecondItemActive {
  background: #d9eeff;
  border: 1px solid #008cff;
  color: #008cff;
}

.moreBtn {
  width: 20px;
  height: 20px;
  cursor: pointer;
}

.moreBox {
  display: flex;
  width: 100%;
  max-height: 85px;
  padding: 0 16px 0 12px;
  overflow: auto;
  position: absolute;
  top: 60px;
  left: 0;
  z-index: 9999;
  flex-wrap: wrap;
  border-radius: 0 0 18px 18px;
  background: #fff;
  box-shadow: 0 8px 6px rgb(0 0 0 / 5%);
}
