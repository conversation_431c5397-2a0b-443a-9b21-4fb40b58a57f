import { Checkbox } from 'antd';
import { StandardItem } from '@/apis';
import { useMemo } from 'react';
import isString from 'lodash/isString';
import styles from './goods-item.module.less';

interface GoodsItemProps {
  isChecked: boolean;
  image: string;
  name: string;
  standard: StandardItem[];
  stock: string;
  onTheWaySkuNumber: string;
  skuBelongType: number;
  onSelectGoods: MultipleParamsFn<[val: boolean]>;
  barsCode?: string;
}

function GoodsItem({
  isChecked,
  name,
  image,
  standard,
  stock,
  onTheWaySkuNumber,
  skuBelongType,
  onSelectGoods,
  barsCode,
}: GoodsItemProps) {
  const isShowStock = false;
  const standardVal = useMemo(() => {
    let str = '';
    if (isString(standard)) {
      str = standard;
    } else {
      standard?.forEach((item, index) => {
        str += `${item.name}: ${item.value}${index === standard.length - 1 ? '' : ','}`;
      });
    }
    return str;
  }, [standard]);

  return (
    <div className={styles.goodsItem}>
      <img className={styles.image} src={image} alt="" />
      <div className={styles.goodsItemContent}>
        <div className={styles.name} title={name}>
          {skuBelongType === 1 && (
            <img
              className={styles.nameTag}
              src="https://img.huahuabiz.com/user_files/2023625/1687686014486265.png"
              alt=""
            />
          )}
          <span>{name}</span>
        </div>
        <div className={styles.itemInfo} title={standardVal}>
          {standard.map((item, index) => (
            <span className={styles.standardItem} key={`${index + 1}`}>
              {item.name}: {item.value}
              <span className={styles.standardTag}>，</span>
            </span>
          ))}
        </div>
        {isShowStock && (
          <>
            <div className={styles.itemInfo}>库存 {stock}</div>
            <div className={styles.itemInfo}>在途库存 {onTheWaySkuNumber}</div>
          </>
        )}
        {barsCode && <div className={styles.itemInfo}>条形码：{barsCode || '--'}</div>}
      </div>
      <Checkbox checked={Boolean(isChecked)} onChange={(e) => onSelectGoods(e.target.checked)} />
    </div>
  );
}

GoodsItem.defaultProps = {
  barsCode: '',
};

export default GoodsItem;
