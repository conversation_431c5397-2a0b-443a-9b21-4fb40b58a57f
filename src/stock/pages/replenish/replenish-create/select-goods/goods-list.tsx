import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { Spin, Divider } from 'antd';
import { Empty } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import { stockGoodsList, StockGoodsListResult } from '@/apis';
import cloneDeep from 'lodash/cloneDeep';
import GoodsItem from './goods-item';
import styles from './goods-list.module.less';

export interface StandardProps {
  name: string;
  value: string[];
}

interface GoodsListProps {
  hasSelectGoodsList: StockGoodsListResult[];
  categoryId: number;
  keyword: string;
  standardFilterParams: StandardProps[];
  shopSkuNames: string[];
  onChangeData: MultipleParamsFn<[arr: StockGoodsListResult[]]>;
  clearAllChecked?: () => void;
}

interface SelectGoodsRefs {
  // eslint-disable-next-line no-unused-vars
  allChange: (val: boolean) => void;
}

const GoodsList = forwardRef<SelectGoodsRefs, GoodsListProps>(
  (
    {
      hasSelectGoodsList,
      categoryId,
      keyword,
      standardFilterParams,
      shopSkuNames,
      onChangeData,
      clearAllChecked,
    },
    ref
  ) => {
    const [list, setList] = useState<StockGoodsListResult[]>([]);
    const hasSelectGoods = useRef<StockGoodsListResult[]>([]);
    const params = useRef({
      pageNo: 1,
      pageSize: 10,
      businessSourceType: 3,
      keyword,
      customizeCategorySet: categoryId ? [categoryId] : [],
      standardFilterParams,
      shopSkuNames,
      sort: 'desc',
    });
    const totalPages = useRef(0);

    const { run, loading } = useRequest(stockGoodsList, {
      defaultParams: [params.current],
      manual: true,
      onSuccess: (result) => {
        result.list.forEach((item) => {
          const items = item;
          hasSelectGoods.current.forEach((goods) => {
            if (item.skuId === goods.skuId) {
              items.isChecked = true;
              items.id = goods.shopSkuId || goods.id;
            }
          });
        });
        totalPages.current = result.pagination.total;
        if (params.current.pageNo === 1) {
          setList(result.list);
        } else {
          setList([...list, ...result.list]);
        }
        params.current.pageNo += 1;
        if (clearAllChecked) clearAllChecked();
      },
    });

    const onLoadMore = () => {
      if (totalPages.current >= Number(params.current.pageNo)) {
        run({ ...params.current });
      }
    };

    const onSelectGoods = (val: boolean, skuId: number) => {
      list.forEach((item) => {
        const items = item;
        if (item.skuId === skuId) {
          items.isChecked = val;
          if (val) {
            hasSelectGoods.current.push(item);
          } else {
            hasSelectGoods.current = hasSelectGoods.current.filter(
              (goods) => goods.skuId !== skuId
            );
          }
        }
      });
      if (!val && clearAllChecked) clearAllChecked();
      setList([...list]);
      onChangeData(hasSelectGoods.current);
    };

    const allChange = (val: boolean) => {
      let listGoods = hasSelectGoods.current;
      const listFoo = list;
      if (val) {
        listFoo.forEach((f) => {
          const ids = listGoods.map((m) => m.skuId);
          if (!ids.includes(f.skuId)) {
            listGoods.push(f);
          }
        });
      } else {
        listGoods = listGoods.filter((f) => !listFoo.map((m) => m.skuId).includes(f.skuId));
      }
      hasSelectGoods.current = listGoods;
      setList([
        ...listFoo.map((m) => ({
          ...m,
          isChecked: val,
        })),
      ]);
      onChangeData(listGoods);
    };

    useImperativeHandle(ref, () => ({
      allChange: (val: boolean) => allChange(val),
    }));

    useEffect(() => {
      params.current = {
        ...params.current,
        pageNo: 1,
        keyword,
        customizeCategorySet: categoryId ? [categoryId] : [],
        standardFilterParams,
        shopSkuNames,
      };
      run({ ...params.current });
    }, [run, categoryId, keyword, standardFilterParams, shopSkuNames]);

    useEffect(() => {
      hasSelectGoods.current = cloneDeep(hasSelectGoodsList);
    }, [hasSelectGoodsList]);

    return (
      <div className={styles.goodsList} id="list">
        <Spin wrapperClassName={styles.box} spinning={params.current.pageNo === 1 && loading}>
          {list.length > 0 ? (
            <InfiniteScroll
              dataLength={list.length}
              hasMore={params.current.pageNo < totalPages.current}
              loader={
                <div className="text-center">
                  <Spin tip="加载中..." />
                </div>
              }
              next={onLoadMore}
              scrollableTarget="list"
              endMessage={
                <div className={styles.divider}>
                  <Divider plain>
                    <span className={styles.endMessage}>加载到底了</span>
                  </Divider>
                </div>
              }
            >
              {list.map((item) => (
                <GoodsItem
                  key={item.id}
                  isChecked={item.isChecked || false}
                  image={
                    item.imagesList.length
                      ? item.imagesList[0]
                      : 'https://img.huahuabiz.com/PC/static/img/default-product.png'
                  }
                  name={item.name}
                  standard={item.standardList}
                  stock={item.stock}
                  onTheWaySkuNumber={item.onTheWaySkuNumber}
                  skuBelongType={Number(item.skuBelongType)}
                  onSelectGoods={(val) => onSelectGoods(val, item.skuId)}
                  barsCode={item.barsCode}
                />
              ))}
            </InfiniteScroll>
          ) : (
            <div className={styles.noData}>
              <Empty />
            </div>
          )}
        </Spin>
      </div>
    );
  }
);

GoodsList.defaultProps = {
  clearAllChecked: () => null,
};

export default GoodsList;
