.selectGoods {
  :global {
    .ant-drawer-body {
      display: flex;
      flex-direction: column;
      padding: 0 !important;
    }

    .ant-drawer-wrapper-body {
      background-color: #fff;
    }

    .ant-input-affix-wrapper,
    .ant-input-affix-wrapper .ant-input {
      background-color: #f5f6fa;
    }
  }
}

.search {
  padding: 8px 20px 12px;
  border-bottom: 1px solid #f3f3f3;
}

.content {
  flex: 1;
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.contentRight {
  flex: 1;
  //height: 100%;
  display: flex;
  flex-direction: column;
}

.stockTip {
  color: #f9ae08;
  font-size: 12px;
  display: flex;
  width: 100%;
  height: 54px;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
  background-color: #fef3da;
}

.stockTipImg {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.stockTipDelete {
  color: #999eb2;
  font-size: 22px;
  margin-left: 12px;
  cursor: pointer;
}

.footer {
  display: flex;
  width: 100%;
  height: 54px;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  box-shadow: 0 1px 0 0 rgb(245 246 250 / 30%);
}

.totalNum {
  margin-left: 16px;
}

.totalNumText {
  color: #008cff;
  font-size: 18px;
  font-weight: 600;
  margin-left: 8px;
}

.goodsFilter {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.img {
  margin-left: 6px;
}
