import { useEffect, useRef, useState } from 'react';
import { Button, Checkbox, message } from 'antd';
import { Drawer, Search, Icon } from '@/components';
import debounce from 'lodash/debounce';
import { StockGoodsListResult } from '@/apis';
import cloneDeep from 'lodash/cloneDeep';
import { selectGoodsFilter } from '@/src/order/containers/select-goods-filter';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import CategoryList from './category-list';
import CategoryListSecond from './category-list-second';
import GoodsList, { StandardProps } from './goods-list';
import styles from './select-goods.module.less';

export interface SelectGoodsProps {
  visible: boolean;
  hasSelectGoodsList: StockGoodsListResult[];
  onClose: () => void;
  onConfirm: MultipleParamsFn<[arr: StockGoodsListResult[]]>;
}

interface SelectGoodsRefs {
  // eslint-disable-next-line no-unused-vars
  allChange: (val: boolean) => void;
}

function SelectGoods({
  visible,
  hasSelectGoodsList,
  onClose,
  onConfirm,
  ...props
}: SelectGoodsProps) {
  const goodsRef = useRef(null as unknown as SelectGoodsRefs);
  const [hasSelectGoods, setHasSelectGoods] = useState<StockGoodsListResult[]>([]);
  const [currentCategoryId, setCurrentCategoryId] = useState(0);
  const [currentCategorySecondId, setCurrentCategorySecondId] = useState(0);
  const [keyword, setKeyword] = useState('');
  const [showStockTip, setShowStockTip] = useState(true);
  const [allChecked, setAllChecked] = useState(false);
  const [shopSkuNames, setShopSkuNames] = useState<string[]>([]);
  const [standardFilterParams, setStandardFilterParams] = useState<StandardProps[]>([]);

  const initFilter = () => {
    setShopSkuNames([]);
    setStandardFilterParams([]);
  };

  const onSearch = debounce((val: string) => {
    initFilter();
    setKeyword(val);
  }, 300);

  const onFilter = () => {
    selectGoodsFilter({
      visible: true,
      businessSourceType: 3,
      names: shopSkuNames,
      standardArr: standardFilterParams,
      onClose: () => {},
      onConfirm: (standards, names) => {
        setStandardFilterParams(standards);
        setShopSkuNames(names);
      },
    });
  };

  const onChangeData = (val: StockGoodsListResult[]) => {
    setHasSelectGoods([...val]);
  };

  const allCheckedChange = (e: CheckboxChangeEvent) => {
    const checked = e?.target?.checked;
    setAllChecked(checked);
    goodsRef.current.allChange(checked);
  };

  const onOk = () => {
    if (hasSelectGoods.length === 0) {
      message.warning('请先选择商品!');
      return;
    }
    onConfirm([...hasSelectGoods]);
    onClose();
  };

  useEffect(() => {
    if (visible) {
      setShowStockTip(false);
      setCurrentCategoryId(0);
      setCurrentCategorySecondId(0);
      setKeyword('');
      initFilter();
      setHasSelectGoods(cloneDeep(hasSelectGoodsList));
    } else {
      setAllChecked(false);
    }
  }, [hasSelectGoodsList, visible]);

  const footer = (
    <div>
      {showStockTip && (
        <div className={styles.stockTip}>
          <img
            className={styles.stockTipImg}
            src="https://img.huahuabiz.com/user_files/2023616/1686898724715212.png"
            alt=""
          />
          <span>在途库存=未生成仓库单的采购订单-未生成仓库单的销售订单。</span>
          <Icon
            name="close"
            className={styles.stockTipDelete}
            onClick={() => setShowStockTip(false)}
          />
        </div>
      )}
      <div className={styles.footer}>
        <div>
          {/* <img src="https://img.huahuabiz.com/user_files/202361/1685611352043388.png" alt="" /> */}
          <Checkbox checked={allChecked} onChange={allCheckedChange} />
          <span className={styles.img}>全选当前页</span>
          <span className={styles.totalNum}>
            <span>已选</span>
            <span className={styles.totalNumText}>{hasSelectGoods.length}</span>
          </span>
        </div>
        <Button type="primary" size="small" onClick={onOk}>
          选好了
        </Button>
      </div>
    </div>
  );

  const extra = (
    <div role="button" tabIndex={0} className={styles.goodsFilter} onClick={onFilter}>
      筛选
    </div>
  );

  return (
    <Drawer
      title="添加补货商品"
      visible={visible}
      onClose={onClose}
      {...props}
      footer={footer}
      className={styles.selectGoods}
      extra={extra}
    >
      <div className={styles.search}>
        <Search placeholder="搜索商品名称" onSearch={(e) => onSearch(e)} />
      </div>
      <div className={styles.content}>
        <CategoryList
          onConfirm={(val) => {
            initFilter();
            setCurrentCategoryId(val);
            if (allChecked) setAllChecked(false);
          }}
        />
        <div className={styles.contentRight}>
          <CategoryListSecond
            categoryId={currentCategoryId}
            categorySecondId={currentCategorySecondId}
            onConfirm={(val) => {
              initFilter();
              setCurrentCategorySecondId(val);
            }}
          />
          <GoodsList
            ref={goodsRef}
            hasSelectGoodsList={hasSelectGoods}
            categoryId={currentCategorySecondId || currentCategoryId}
            keyword={keyword}
            shopSkuNames={shopSkuNames}
            standardFilterParams={standardFilterParams}
            onChangeData={onChangeData}
            clearAllChecked={() => {
              if (allChecked) setAllChecked(false);
            }}
          />
        </div>
      </div>
    </Drawer>
  );
}

export default SelectGoods;
