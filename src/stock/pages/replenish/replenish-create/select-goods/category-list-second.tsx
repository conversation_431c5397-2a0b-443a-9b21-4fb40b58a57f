import { useEffect, useMemo, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { GetCategoryCustomeItemResult, getCategoryCustomeList } from '@/apis';
import classNames from 'classnames';
import styles from './category-list-second.module.less';

interface CategoryListSecondProps {
  categoryId: number;
  categorySecondId: number;
  onConfirm: MultipleParamsFn<[id: number]>;
}

function CategoryListSecond({ categoryId, categorySecondId, onConfirm }: CategoryListSecondProps) {
  const [list, setList] = useState<GetCategoryCustomeItemResult[]>([]);
  const params = useRef({
    pageNo: 1,
    pageSize: 99,
    parentId: categoryId,
  });
  const [selectCategorySecondId, setSelectCategorySecondId] = useState(categorySecondId);
  const [showMore, setShowMore] = useState(false);

  const { run } = useRequest(getCategoryCustomeList, {
    manual: true,
    defaultParams: [{ ...params.current, parentId: categoryId }],
    onSuccess: (res) => {
      setList(res.list);
    },
  });

  const onSelectCategory = (val: number) => {
    if (selectCategorySecondId === val) {
      setSelectCategorySecondId(0);
      onConfirm(0);
    } else {
      setSelectCategorySecondId(val);
      onConfirm(val);
    }
  };

  const categoryList = useMemo(() => {
    let arr: GetCategoryCustomeItemResult[] = [];
    let arrMore: GetCategoryCustomeItemResult[] = [];
    arr = list.filter((item, index) => index < 2);
    arrMore = list.filter((item, index) => index > 2);
    return {
      arr,
      arrMore,
    };
  }, [list]);

  useEffect(() => {
    setShowMore(false);
    setSelectCategorySecondId(categorySecondId);
  }, [categorySecondId]);

  useEffect(() => {
    if (categoryId) {
      run({ ...params.current, parentId: categoryId });
    } else {
      setList([]);
    }
  }, [categoryId, run]);

  return (
    <div
      className={classNames({
        [styles.box]: categoryList.arr.length > 0,
        'pt-4': !categoryList.arr.length,
      })}
    >
      <div className={styles.categoryListSecond}>
        {categoryList.arr.map((item, index) => (
          <span
            role="button"
            tabIndex={index}
            key={item.id}
            className={classNames(styles.categoryListSecondItem, {
              [styles.categoryListSecondItemActive]: item.id === selectCategorySecondId,
            })}
            onClick={() => onSelectCategory(Number(item.id))}
            title={item.categoryName}
          >
            {item.categoryName}
          </span>
        ))}
        {categoryList.arrMore.length > 0 && (
          <div
            className={styles.moreBtn}
            role="button"
            tabIndex={0}
            onClick={() => setShowMore(!showMore)}
          >
            <img src="https://img.huahuabiz.com/user_files/2023616/1686884809409216.png" alt="" />
          </div>
        )}
      </div>
      {showMore && (
        <div className={styles.moreBox}>
          {categoryList.arrMore.map((item, index) => (
            <span
              role="button"
              tabIndex={index}
              key={item.id}
              className={classNames(styles.categoryListSecondItem, 'mb-4', {
                [styles.categoryListSecondItemActive]: item.id === selectCategorySecondId,
              })}
              onClick={() => onSelectCategory(Number(item.id))}
              title={item.categoryName}
            >
              {item.categoryName}
            </span>
          ))}
        </div>
      )}
    </div>
  );
}

export default CategoryListSecond;
