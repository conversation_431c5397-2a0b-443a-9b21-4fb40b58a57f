@import 'styles/mixins/mixins';

.goodsItem {
  display: flex;
  margin-bottom: 20px;
  padding: 0 16px 0 12px;
  align-items: center;

  &:last-child {
    margin-bottom: 0;
  }
}

.name {
  width: 150px;
  .text-overflow(1);

  margin-bottom: 6px;
}

.nameTag {
  width: 28px;
  height: 18px;
  margin-right: 4px;
  vertical-align: middle;
}

.itemInfo {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 6px;
  .text-overflow(1);

  &:last-child {
    margin-bottom: 0;
  }
}

.standardItem:last-child .standardTag {
  display: none;
}

.image {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  margin-right: 12px;
}

.goodsItemContent {
  flex: 1;
  height: 80px;
}
