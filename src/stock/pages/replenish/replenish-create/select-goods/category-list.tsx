import { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { GetCategoryCustomeItemResult, getCategoryCustomeList } from '@/apis';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Spin } from 'antd';
import classNames from 'classnames';
import styles from './category-list.module.less';

interface CategoryListProps {
  onConfirm: MultipleParamsFn<[id: number]>;
}

const initList = [
  {
    id: 0,
    parentId: 0,
    categoryName: '全部',
  },
];

function CategoryList({ onConfirm }: CategoryListProps) {
  const params = useRef({
    pageNo: 1,
    pageSize: 15,
  });
  const totalPages = useRef(0);
  const [list, setList] = useState<GetCategoryCustomeItemResult[]>([]);
  const [selectCategoryId, setSelectCategoryId] = useState(0);

  const { run } = useRequest(getCategoryCustomeList, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      totalPages.current = res.pagination.total;
      if (params.current.pageNo === 1) {
        setList([...initList, ...res.list]);
      } else {
        setList([...list, ...res.list]);
      }
      params.current.pageNo += 1;
    },
  });

  const onLoadMore = () => {
    if (!(params.current.pageNo <= totalPages.current)) return;
    run({ ...params.current });
  };

  const onSelectCategory = (val: number) => {
    setSelectCategoryId(val);
    onConfirm(val);
  };

  useEffect(() => {
    run({ ...params.current });
  }, [run]);

  return (
    <div className={styles.scroll} id="category-scroll">
      <InfiniteScroll
        scrollableTarget="category-scroll"
        dataLength={list.length}
        hasMore={params.current.pageNo < totalPages.current}
        loader={
          <div className={styles.loader}>
            <Spin tip="加载中..." />
          </div>
        }
        endMessage={null}
        next={onLoadMore}
      >
        {list.map((item, index) => (
          <div
            role="button"
            tabIndex={index}
            key={item.id}
            className={classNames(styles.item, {
              [styles.itemActive]: item.id === selectCategoryId,
            })}
            onClick={() => onSelectCategory(Number(item.id))}
            title={item.categoryName}
          >
            {item.categoryName}
          </div>
        ))}
      </InfiniteScroll>
    </div>
  );
}

export default CategoryList;
