@import 'styles/mixins/mixins';

.item {
  padding-bottom: 16px;
  position: relative;
  border-bottom: 1px solid #f3f3f3;
}

.itemBottom {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.delete {
  color: rgb(0 0 0 / 30%);
  font-size: 16px;
  position: absolute;
  top: 10px;
  right: -10px;
  cursor: pointer;
  z-index: 99;
}

.title {
  font-weight: 600;
  display: flex;
  padding: 16px 0;
  align-items: center;
}

.supplierName {
  .text-overflow(1);
}

.titleIcon {
  width: 18px;
  height: 18px;
  margin-right: 4px;
}

.content {
  display: flex;
  margin-bottom: 20px;
}

.image {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  margin-right: 16px;
}

.goodsItemContent {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.name {
  .text-overflow(2);
}

.nameTag {
  width: 28px;
  height: 18px;
  margin-right: 4px;
  vertical-align: middle;
}

.itemInfo {
  color: #888b98;
  .text-overflow(1);
}

.standardItem:last-child .standardTag {
  display: none;
}

.tableTitle {
  display: flex;
  border-radius: 8px;
  background-color: rgb(245 246 250 / 80%);
  margin-bottom: 16px;
}

.tableTitleEach {
  font-weight: 600;
  display: flex;
  height: 38px;
  justify-content: center;
  flex: 1;
  align-items: center;
}

.table {
  display: flex;
}

.tableEach {
  display: flex;
  height: 32px;
  justify-content: center;
  flex: 1;
  align-items: center;
}

.tableEachNum {
  display: flex;
  width: 100px;
  height: 32px;
  justify-content: center;
  border-radius: 6px;
  align-items: center;
  cursor: pointer;
}
