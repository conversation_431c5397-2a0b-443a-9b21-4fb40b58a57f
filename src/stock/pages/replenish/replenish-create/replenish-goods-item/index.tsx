import { useMemo } from 'react';
import { StandardItem } from '@/apis';
import { Icon } from '@/components';
import isString from 'lodash/isString';
import isArray from 'lodash/isArray';
import classNames from 'classnames';
import styles from './index.module.less';

interface ReplenishGoodsItemProps {
  isSupplier?: boolean;
  supplierName: string;
  image: string;
  name: string;
  skuBelongType: number;
  standard: StandardItem[] | string;
  unit: string;
  number: number;
  isDelete?: boolean;
  onDelete?: () => void;
  isLookDetail?: boolean;
  onLookDetail?: () => void;
  barsCode?: string;
}

function ReplenishGoodsItem({
  isSupplier,
  supplierName,
  image,
  name,
  skuBelongType,
  standard,
  unit,
  number,
  isDelete,
  onDelete,
  isLookDetail,
  onLookDetail,
  barsCode,
}: ReplenishGoodsItemProps) {
  const standardVal = useMemo(() => {
    let str = '';
    if (isString(standard)) {
      str = standard;
    } else {
      standard?.forEach((item, index) => {
        str += `${item.name}: ${item.value}${index === standard.length - 1 ? '' : ','}`;
      });
    }
    return str;
  }, [standard]);

  return (
    <div className={classNames(styles.item, { [styles.itemBottom]: !isSupplier })}>
      {isDelete && <Icon name="close-circle2" className={styles.delete} onClick={onDelete} />}
      {isSupplier && (
        <div className={styles.title}>
          <img
            className={styles.titleIcon}
            src="https://img.huahuabiz.com/user_files/2023619/1687145629532698.png"
            alt=""
          />
          <span className={styles.supplierName} title={supplierName}>
            补货供应商 {supplierName}
          </span>
        </div>
      )}
      <div className={styles.content}>
        <img className={styles.image} src={image} alt="" />
        <div className={styles.goodsItemContent}>
          <div className={styles.name} title={name}>
            {skuBelongType === 1 && (
              <img
                className={styles.nameTag}
                src="https://img.huahuabiz.com/user_files/2023625/1687686014486265.png"
                alt=""
              />
            )}
            <span>{name}</span>
          </div>
          <div>
            <div className={styles.itemInfo} title={standardVal}>
              {isArray(standard)
                ? standard.map((item, index) => (
                    <span className={styles.standardItem} key={`${index + 1}`}>
                      {item.name}: {item.value}
                      <span className={styles.standardTag}>，</span>
                    </span>
                  ))
                : standard}
            </div>
            <div className={styles.itemInfo} title={barsCode}>
              条形码：{barsCode || '--'}
            </div>
          </div>
        </div>
      </div>
      <div className={styles.tableTitle}>
        <div className={styles.tableTitleEach}>单位</div>
        <div className={styles.tableTitleEach}>补货数量</div>
      </div>
      <div className={styles.table}>
        <div className={styles.tableEach}>{unit}</div>
        <div className={styles.tableEach}>
          {isLookDetail ? (
            <div role="button" tabIndex={0} className={styles.tableEachNum} onClick={onLookDetail}>
              {number}
            </div>
          ) : (
            number
          )}
        </div>
      </div>
    </div>
  );
}

ReplenishGoodsItem.defaultProps = {
  isSupplier: false,
  isDelete: false,
  onDelete: () => {},
  isLookDetail: false,
  onLookDetail: () => {},
  barsCode: '',
};

export default ReplenishGoodsItem;
