@import 'styles/mixins/mixins';

.modal {
  :global {
    .ant-modal-title {
      position: relative;

      &::before {
        content: '*';
        color: red;
        position: absolute;
        top: 2px;
        left: -8px;
      }
    }

    .ant-modal-header {
      padding: 20px;
      border: none;
    }

    .ant-modal-body {
      padding: 0 20px;
    }

    .ant-select {
      width: 80%;
    }
  }
}

.search {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.searchBox {
  width: 280px;
  height: 32px;
  margin-left: 12px;
}

.title {
  display: flex;
  height: 38px;
  background: #f5f6fa;
  border-radius: 10px;
}

.text-align-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.goodsInfo {
  display: flex;
  width: 238px;
  padding: 0 20px;
  .text-align-center();
}

.goodsInfoImg {
  width: 64px;
  height: 64px;
  border-radius: 6px;
  margin-right: 12px;
}

.goodsInfoContent {
  display: flex;
  // height: 64px;
  justify-content: space-between;
  flex: 1;
  flex-direction: column;
}

.name {
  width: 125px;
  .text-overflow(2);
}

.nameTag {
  width: 28px;
  height: 18px;
  margin-right: 4px;
  vertical-align: middle;
}

.standard {
  color: #888b98;
  width: 125px;
  .text-overflow(1);
}

.barsCode {
  color: #888b98;
  width: 125px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.standardItem:last-child .standardTag {
  display: none;
}

.supplier {
  width: 168px;
  .text-align-center();
}

.select {
  width: 100%;
}

.unit {
  // width: 196px;
  flex: 1;
  .text-align-center();
}

.num {
  width: 84px;
  .text-align-center();

  & .input {
    width: 68px;
    text-align: center;
  }

  :global {
    .ant-input-number-input {
      text-align: center;
      padding: 0 10px;
    }
  }
}

.handle {
  width: 54px;
  .text-align-center();
}

.delete {
  color: #ea1c26;
  cursor: pointer;
}

.list {
  width: 100%;
  height: 352px;
  margin-bottom: 4px;
  overflow: auto;
}

.item {
  display: flex;
  height: 88px;
  padding: 12px 0;
}

.footer {
  display: flex;
  justify-content: space-between;
}

.addGoods {
  color: #008cff;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.addGoodsIcon {
  margin-right: 2px;
}

.noData {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
