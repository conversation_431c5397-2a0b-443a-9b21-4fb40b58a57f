import { useEffect, useMemo, useRef, useState } from 'react';
import { Modal, ModalProps, Select, InputNumber, Button, message, Spin } from 'antd';
import { Icon, Search, Empty } from '@/components';
import {
  StockGoodsListResult,
  StandardItem,
  stockReplenishSupplier,
  StockReplenishSupplierResult,
  stockReplenishDetail,
  stockReplenishEdit,
  FormulaConvertManyconvertResult,
  getFormulaConvertAllUnit,
} from '@/apis';
import isString from 'lodash/isString';
import classNames from 'classnames';
import { useRequest } from 'ahooks';
import cloneDeep from 'lodash/cloneDeep';
import styles from './goods-info-setup.module.less';

export interface GoodsInfoSetupProps extends ModalProps {
  id?: number;
  hasSelectGoods?: StockGoodsListResult[];
  onCancel?: () => void;
  onBack?: MultipleParamsFn<[arr: StockGoodsListResult[]]>;
  onSuccess: MultipleParamsFn<[arr: StockGoodsListResult[]]>;
  isAdd?: boolean;
  onAddGoods?: MultipleParamsFn<[arr: StockGoodsListResult[]]>;
  onPerm?: MultipleParamsFn<[code: string]>;
}

function GoodsInfoSetup({
  id,
  hasSelectGoods,
  onCancel,
  onBack,
  onSuccess,
  isAdd,
  onAddGoods,
  onPerm,
  ...props
}: GoodsInfoSetupProps) {
  const [list, setList] = useState<StockGoodsListResult[]>([]);
  const [unitList, setUnitList] = useState<FormulaConvertManyconvertResult[]>([]);
  // const [isCheck, setIsCheck] = useState(false);
  const [searchVal, setSearchVal] = useState('');
  const [searchList, setSearchList] = useState<StockGoodsListResult[]>([]);
  const [customerList, setCustomerList] = useState<StockReplenishSupplierResult[]>([]);
  const params = useRef({
    shopSkuId: 0,
    skuId: 0,
    quantity: 0,
  });
  const unitParams = useRef({
    templateId: 0,
    mainUnitName: '',
  });
  const currentId = useRef(0);
  const currentOrderStatus = useRef(0);

  const { run, loading } = useRequest(stockReplenishSupplier, {
    manual: true,
    defaultParams: [{ ...params.current }],
    onSuccess: (res) => {
      list.forEach((item) => {
        const items = item;
        items.customerList = items.customerList || [];
      });
      setList(cloneDeep(list));
      setCustomerList(res.list);
    },
    onError: () => {
      list.forEach((item) => {
        const items = item;
        items.customerList = [];
      });
      setList(cloneDeep(list));
    },
  });

  const { run: runDetail } = useRequest(stockReplenishDetail, {
    manual: true,
    defaultParams: [{ id: Number(id) }],
    onSuccess: (res) => {
      if (res.orderStatus === 3) {
        onCancel?.();
        message.warning('OA审批中，无法编辑');
        return;
      }
      const arr: StockGoodsListResult[] = [];
      res.replenishPlanSkuVOS.forEach((item) => {
        arr.push({
          ...item,
          id: item.shopSkuId,
          shopSkuId: item.shopSkuId,
          name: item.skuName || '',
          number: item.quantity,
          standardList: JSON.parse(String(item.standardJson)),
          imagesList: [item.images],
          customerList: item.supplierCompanyId
            ? [
                {
                  supplierCompanyId: item.supplierCompanyId || 0,
                  supplierName: item.supplierName || '',
                },
              ]
            : [],
        });
      });
      setList(cloneDeep(arr));
      currentOrderStatus.current = res.orderStatus;
    },
  });

  const getChangeSupplierList = (arrVal: StockGoodsListResult[], e: number, idVal: number) => {
    const arr = customerList.filter((item) => item.supplierCompanyId === e);
    if (arr.length > 0) {
      arrVal.forEach((item) => {
        const items = item;
        if (items.id === idVal) {
          items.shopSkuId = arr[0].shopSkuId;
          items.supplierId = arr[0].supplierId;
          items.supplierCompanyId = e;
          items.supplierName = arr[0].supplierName;
          items.customerList = [
            {
              supplierCompanyId: e,
              supplierName: arr[0].supplierName,
            },
          ];
        }
      });
    }
    return arrVal;
  };

  const onChangeSupplier = (e: number, idVal: number) => {
    setList(cloneDeep(getChangeSupplierList(list, e, idVal)));
    setSearchList(cloneDeep(getChangeSupplierList(searchList, e, idVal)));
  };

  const onChangeNum = (val: number | null, idVal: number) => {
    list.forEach((item) => {
      const items = item;
      if (item.id === idVal) {
        items.number = val as number;
        items.supplierCompanyId = 0;
        items.supplierName = '';
      }
    });
    searchList.forEach((item) => {
      const items = item;
      if (item.id === idVal) {
        items.number = val as number;
        items.supplierCompanyId = 0;
        items.supplierName = '';
      }
    });
    setList(cloneDeep(list));
    setSearchList(cloneDeep(searchList));
  };

  const onSearch = (val: string) => {
    setSearchVal(val);
    const arr: StockGoodsListResult[] = [];
    list.forEach((item) => {
      if (
        item.supplierName?.includes(val) ||
        item.name.includes(val) ||
        item.barsCode?.includes(val)
      ) {
        arr.push(item);
      }
    });
    setCustomerList([]);
    setSearchList(cloneDeep(arr));
  };

  const standardVal = (val: StandardItem[]) => {
    let str = '';
    if (isString(val)) {
      str = val;
    } else {
      val?.forEach((item, index) => {
        str += `${item.name}: ${item.value}${index === val.length - 1 ? '' : ','}`;
      });
    }
    return str;
  };

  const onDelete = (idVal: number) => {
    Modal.confirm({
      title: '提示',
      content: `确定删除该商品?`,
      okText: '确定',
      icon: '',
      cancelText: '取消',
      centered: true,
      onOk: () => {
        setList([...list.filter((item) => (item.shopSkuId || item.id) !== idVal)]);
        setSearchList([...searchList.filter((item) => (item.shopSkuId || item.id) !== idVal)]);
      },
    });
  };

  const onOk = () => {
    if (list.some((item) => !item.supplierCompanyId)) {
      message.warning('请选择补货供应商');
      return;
    }
    if (list.some((item) => !item.number && item.number !== 0)) {
      message.warning('请输入补货数量');
      return;
    }
    if (list.some((item) => Number(item.number) === 0)) {
      message.warning('补货数量需大于0');
      return;
    }
    if (id) {
      stockReplenishEdit({
        id: Number(id),
        orderStatus: currentOrderStatus.current,
        replenishSkuParams: list.map((item) => ({
          shopSkuId: item.shopSkuId || item.id,
          skuId: item.skuId,
          unitName: item.unit || item.unitName,
          auxiliaryUnit: item.unit,
          quantity: Number(item.number),
          supplierId: Number(item.supplierId),
          supplierName: item.supplierName || '',
          supplierCompanyId: Number(item.supplierCompanyId),
        })),
      }).then(() => {
        message.success('编辑成功');
        onCancel?.();
        onSuccess([]);
      });
    } else {
      onCancel?.();
      onSuccess([...list]);
    }
  };

  const onClose = () => {
    onCancel?.();
    onBack?.([...(hasSelectGoods || [])]);
  };

  const addGoods = () => {
    onAddGoods?.([...list]);
    onCancel?.();
  };

  const { run: runUnit } = useRequest(getFormulaConvertAllUnit, {
    manual: true,
    defaultParams: [{ ...unitParams.current }],
    onSuccess: (res) => {
      setUnitList(res.list);
    },
  });

  const onChangeUnit = (e: string, item: StockGoodsListResult) => {
    list.forEach((each) => {
      const eachItem = each;
      if (eachItem.id === item.id) {
        eachItem.unit = e;
      }
    });
    searchList.forEach((each) => {
      const eachItem = each;
      if (eachItem.id === item.id) {
        eachItem.unit = e;
      }
    });
    setList(cloneDeep(list));
    setSearchList(cloneDeep(searchList));
  };

  const goodsList = useMemo(
    () => (searchVal.length > 0 ? searchList : list),
    [list, searchList, searchVal]
  );

  useEffect(() => {
    if (props.visible && id) {
      runDetail({ id: Number(id) });
    }
  }, [id, props.visible, runDetail]);

  useEffect(() => {
    if (hasSelectGoods && hasSelectGoods?.length > 0) {
      setList([
        ...(hasSelectGoods || []).map((item) => ({
          ...item,
          supplierCompanyId: item.supplierCompanyId,
          customerList: item.supplierCompanyId
            ? [
                {
                  supplierCompanyId: item.supplierCompanyId || 0,
                  supplierName: item.supplierName || '',
                },
              ]
            : [],
        })),
      ]);
    }
  }, [hasSelectGoods, id]);

  const footer = (
    <div className={styles.footer}>
      {isAdd ? (
        <div role="button" tabIndex={0} className={styles.addGoods} onClick={addGoods}>
          <Icon name="plus" size={16} className={styles.addGoodsIcon} />
          <span>添加商品</span>
        </div>
      ) : (
        <span />
      )}
      <div>
        <Button type="default" onClick={onClose}>
          取消
        </Button>
        <Button type="primary" disabled={!list.length} onClick={onOk}>
          确定
        </Button>
      </div>
    </div>
  );

  return (
    <Modal
      visible={props.visible}
      {...props}
      title="设置商品信息"
      width={780}
      centered
      wrapClassName={styles.modal}
      onOk={onOk}
      onCancel={onCancel}
      footer={footer}
    >
      <div className={styles.search}>
        <span>商品信息检索</span>
        <Search
          placeholder="商品/补货供应商名称"
          className={styles.searchBox}
          theme="grey"
          onSearch={(e) => onSearch(e)}
        />
      </div>
      <div className={styles.title}>
        <div className={styles.goodsInfo}>商品属性</div>
        <div className={styles.unit}>单位</div>
        <div className={styles.num}>补货数量</div>
        <div className={styles.supplier}>补货供应商</div>
        <div className={styles.handle}>操作</div>
      </div>
      <div className={styles.list}>
        {goodsList.length > 0 ? (
          goodsList.map((item, index) => (
            <div className={styles.item} key={item.id}>
              <div className={styles.goodsInfo}>
                <img
                  className={styles.goodsInfoImg}
                  src={
                    item.imagesList && item.imagesList.length
                      ? item.imagesList[0]
                      : 'https://img.huahuabiz.com/PC/static/img/default-product.png'
                  }
                  alt=""
                />
                <div className={styles.goodsInfoContent}>
                  <div className={styles.name}>
                    {item.skuBelongType === 1 && (
                      <img
                        className={styles.nameTag}
                        src="https://img.huahuabiz.com/user_files/2023625/1687686014486265.png"
                        alt=""
                      />
                    )}
                    <span title={item.name}>{item.name}</span>
                  </div>
                  <div>
                    <div className={styles.standard} title={standardVal(item.standardList)}>
                      {item.standardList.map((each, index1) => (
                        <span className={styles.standardItem} key={`${index1 + 1}`}>
                          {each.name}: {each.value}
                          <span className={styles.standardTag}>，</span>
                        </span>
                      ))}
                    </div>
                    <div className={styles.barsCode} title={item.barsCode}>
                      条形码：{item.barsCode || '--'}
                    </div>
                  </div>
                </div>
              </div>
              <div className={styles.unit}>
                <Select
                  value={item.auxiliaryUnit || item.unit || item.unitName || null}
                  options={unitList}
                  fieldNames={{ label: 'unitName', value: 'unitName' }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  // status={!(item.auxiliaryUnit || item.unit) && isCheck ? 'error' : ''}
                  onFocus={() => {
                    if (item.unitTemplateId) {
                      runUnit({
                        templateId: item.unitTemplateId,
                      });
                    } else {
                      setUnitList([{ unitName: item.unit, scale: +item.saleGroup }]);
                    }
                  }}
                  onChange={(e) => onChangeUnit(e, item)}
                  placeholder="请选择"
                />
              </div>
              <div className={styles.num}>
                <InputNumber
                  controls={false}
                  value={item.number}
                  disabled={!item.unit && !item.unitName}
                  max={*********}
                  placeholder="请输入"
                  className={styles.input}
                  onChange={(e) => onChangeNum(e, item.id)}
                />
              </div>
              <div className={styles.supplier}>
                <Select
                  value={item.supplierCompanyId || null}
                  suffixIcon={<Icon name="down" size={16} />}
                  disabled={!item.number}
                  placeholder={!item.number ? '请先输入补货数量' : '请选择'}
                  dropdownMatchSelectWidth={false}
                  className={styles.select}
                  options={
                    customerList.length > 0 && !searchVal.length ? customerList : item.customerList
                  }
                  fieldNames={{
                    label: 'supplierName',
                    value: 'supplierCompanyId',
                  }}
                  onFocus={() => {
                    setCustomerList([]);
                    currentId.current = item.id;
                    run({
                      shopSkuId: item.shopSkuId || item.id,
                      skuId: item.skuId,
                      quantity: item.number,
                    });
                  }}
                  dropdownStyle={{ padding: '5px 0', paddingLeft: 0, paddingRight: 0 }}
                  notFoundContent={
                    <Spin spinning={loading}>
                      {item.skuBelongType === 1 ? '未找到符合“数量”供应商' : '暂无可选选项'}
                    </Spin>
                  }
                  onChange={(e) => onChangeSupplier(e, item.id)}
                />
              </div>
              <div
                role="button"
                tabIndex={index}
                className={classNames(styles.handle, styles.delete)}
                onClick={() => onDelete(item.shopSkuId || item.id)}
              >
                删除
              </div>
            </div>
          ))
        ) : (
          <div className={styles.noData}>
            <Empty message="暂⽆相关内容" description={searchVal ? '换个关键词再试试吧！' : null} />
          </div>
        )}
      </div>
    </Modal>
  );
}

GoodsInfoSetup.defaultProps = {
  id: 0,
  hasSelectGoods: [],
  onCancel: () => {},
  onBack: () => {},
  isAdd: false,
  onAddGoods: () => {},
  onPerm: () => {},
};

export default GoodsInfoSetup;
