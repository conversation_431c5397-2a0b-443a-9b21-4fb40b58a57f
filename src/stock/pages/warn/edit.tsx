import { PropsWithChildren, useEffect, useState, useMemo } from 'react';
import { Button, Input, InputNumber, message, Modal } from 'antd';
import { Drawer } from '@/components';
import { OrderResult, postSkuEditStock, postSkuEditBatchStock } from '@/apis';
import classNames from 'classnames';
import cloneDeep from 'lodash/cloneDeep';
import GoodsInfo from '../../components/goods-info';
import styles from './edit.module.less';

interface EditPropps {
  visible: boolean;
  skuIdFoo: OrderResult[];
  onShow: () => void;
  onSure: () => void;
}

function Edit({ visible, onShow, skuIdFoo, onSure }: PropsWithChildren<EditPropps>) {
  const [show, setShow] = useState(visible);
  const [stock, setStock] = useState<OrderResult[]>([]);
  const [stockStr, setStockStr] = useState('');

  const onClose = () => {
    if (stockStr !== JSON.stringify(stock)) {
      Modal.confirm({
        title: '提示',
        icon: '',
        cancelText: '离开',
        okText: '继续编辑',
        centered: true,
        content: '您编辑的内容尚未保存，确定要离开吗？',
        onOk: () => {},
        onCancel: () => {
          setShow(false);
          setStock(skuIdFoo);
          onShow();
        },
      });
    } else {
      setShow(false);
      setStock(skuIdFoo);
      onShow();
    }
  };

  const lowStock = (e: { target: { value: string } }, id: number | string) => {
    let list = [];
    if (id) {
      list = stock.map((item) => {
        if (item.skuId === id) {
          return {
            ...item,
            minStock: e.target.value,
          };
        }
        return { ...item };
      });
    } else {
      list = stock.map((item) => ({
        ...item,
        minStock: e.target.value,
      }));
    }
    setStock(list);
  };

  const tallStock = (e: { target: { value: string } }, id: number | string) => {
    let list = [];
    if (id) {
      list = stock.map((item) => {
        if (item.skuId === id) {
          return {
            ...item,
            maxStock: e.target.value,
          };
        }
        return { ...item };
      });
    } else {
      list = stock.map((item) => ({
        ...item,
        maxStock: e.target.value,
      }));
    }
    setStock(list);
  };

  const onChangeData = (val: any, id?: number) => {
    stock.forEach((item) => {
      const items = item;
      if (item.skuId === id && id) {
        items.replenishQuantity = val;
      }
      if (!id) {
        items.replenishQuantity = val;
      }
    });
    setStock(cloneDeep(stock));
  };

  const standard = (standardJson: string) => {
    const stand = JSON.parse(standardJson).map((data: { value: string }) => data.value);
    return stand.join(' ');
  };

  // eslint-disable-next-line consistent-return
  const editWarn = () => {
    // @ts-ignore
    if (stock.some((item) => Number(item.minStock) > Number(item.maxStock))) {
      return message.warning('最高库存不能低于最低库存数量');
    }
    if (stock.some((item) => item.replenishQuantity === 0)) {
      return message.warning('补货数量不能小于0');
    }
    if (stock.length === 1) {
      const data = {
        skuId: stock[0].skuId ? stock[0].skuId : 0,
        minStock: Number(stock[0].minStock),
        maxStock: Number(stock[0].maxStock),
        replenishQuantity: Number(stock[0].replenishQuantity) || 0,
      };
      postSkuEditStock(data).then(() => {
        message.success('修改成功');
        onSure();
      });
    } else {
      const stockWarningList = stock.map((m) => ({
        skuId: m.skuId || 0,
        minStock: Number(m.minStock),
        maxStock: Number(m.maxStock),
        replenishQuantity: Number(m.replenishQuantity) || 0,
      }));
      postSkuEditBatchStock({ stockWarningList }).then(() => {
        message.success('修改成功');
        onSure();
      });
    }
  };

  const isSuccess = useMemo(() => {
    const num = stock.some(
      (item) =>
        ((item.minStock || item.maxStock) && Number(item.minStock) > Number(item.maxStock)) ||
        (!item.minStock && !item.maxStock && stockStr === JSON.stringify(stock))
    );
    const quantity = stock.some(
      (item) => Number(item.replenishQuantity) === Number(item.replenishNumber)
    );
    return num && quantity;
  }, [stock, stockStr]);

  useEffect(() => {
    setShow(visible);
    if (visible) {
      const arr = cloneDeep(skuIdFoo);
      arr.forEach((item) => {
        const items = item;
        items.minStock = item.minStock || '';
        items.maxStock = item.maxStock || '';
        items.replenishQuantity = item.replenishQuantity || '';
        items.replenishNumber = item.replenishQuantity || '';
      });
      setStockStr(JSON.stringify(cloneDeep(arr)));
      setStock(cloneDeep(arr));
    }
  }, [skuIdFoo, visible]);

  return (
    <Drawer
      title="编辑"
      onClose={onClose}
      visible={show}
      className={styles.detail}
      footer={
        <div className={styles.footer}>
          <Button
            block
            type="primary"
            disabled={stockStr === JSON.stringify(stock) || isSuccess}
            onClick={editWarn}
          >
            保存
          </Button>
        </div>
      }
    >
      {skuIdFoo.length > 1 ? (
        <div className={classNames(styles.card, 'pt-5', 'pb-5')}>
          <div className={styles.batchSet}>批量设置</div>
          <div className={styles.set}>
            <div>
              <span className={styles.label}>最低库存</span>
              {/* @ts-ignore */}
              <Input className={styles.value} placeholder="请输入" onChange={lowStock} />
            </div>
            <div>
              <span className={styles.label}>最高库存</span>
              {/* @ts-ignore */}
              <Input className={styles.value} placeholder="请输入" onChange={tallStock} />
            </div>
          </div>
          <div className={styles.title}>自动补货</div>
          <div className={styles.cell}>
            <span className={styles.label}>补货数量</span>
            <InputNumber
              controls={false}
              max={999999999}
              placeholder="请输入"
              className={styles.value}
              // value={replenishQuantity}
              onChange={(e) => onChangeData(e)}
            />
          </div>
        </div>
      ) : (
        ''
      )}
      <div className={styles.card}>
        {stock
          ? stock.map((item) => (
              <div className={styles.item} key={item.skuId}>
                <GoodsInfo
                  img={item.images}
                  name={item.skuName}
                  standard={standard(item.standardJson ? item.standardJson : '')}
                  number={item.skuCode}
                  standardId={0}
                  unit={item.unit}
                />
                <div className={styles.title}>库存预警值</div>
                <div>
                  <div className={styles.set}>
                    <div>
                      <span className={styles.label}>最低库存</span>
                      <Input
                        type="number"
                        placeholder="请输入"
                        className={classNames(
                          // @ts-ignore
                          item.maxStock && Number(item.minStock) > Number(item.maxStock)
                            ? styles.valueError
                            : styles.value
                        )}
                        value={item.minStock}
                        onChange={(value) => lowStock(value, Number(item.skuId))}
                      />
                    </div>
                    <div>
                      <span className={styles.label}>最高库存</span>
                      <Input
                        type="number"
                        placeholder="请输入"
                        className={classNames(
                          // @ts-ignore
                          item.maxStock && Number(item.minStock) > Number(item.maxStock)
                            ? styles.valueError
                            : styles.value
                        )}
                        value={item.maxStock}
                        onChange={(value) => tallStock(value, Number(item.skuId))}
                      />
                    </div>
                    {/* @ts-ignore */}
                    {item.maxStock && Number(item.minStock) > Number(item.maxStock) ? (
                      <div className={styles.error}>最高库存不能低于最低库存数量</div>
                    ) : null}
                  </div>
                  <div className={styles.title}>自动补货</div>
                  <div className={styles.cell}>
                    <span className={styles.label}>补货数量</span>
                    <InputNumber
                      controls={false}
                      className={styles.value}
                      max={999999999}
                      placeholder="请输入"
                      value={item.replenishQuantity}
                      onChange={(e) => onChangeData(e, item.skuId)}
                    />
                  </div>
                </div>
              </div>
            ))
          : null}
      </div>
    </Drawer>
  );
}

export default Edit;
