import { Context, Icon, ListUnit } from '@/components';
import { ListUnitRef, columnsProps, shopColumnsProps } from '@/components/list-unit';
import { Popover, Dropdown, Menu, message, Tooltip } from 'antd';
import { MouseEvent, useCallback, useMemo, useRef, useState } from 'react';
import { user } from '@/store';
import { stockWarnList, postPlatExportWarn, skuListParams, OrderResult } from '@/apis';
import classNames from 'classnames';
import { useSearchParams } from 'react-router-dom';
import { checkPermission } from '@/utils/permission';
import Filter from './filter';
import GoodsInfo from '../../components/goods-info';
import styles from '../../styles/list.module.less';
import Edit from './edit';
// import lack from '../../assets/imgs/lack.png';
// import surpass from '../../assets/imgs/surpass.png';
import BatchEdit from './batch-edit';

function Warn() {
  const [stockStatusParams] = useSearchParams();
  const statusUrl = stockStatusParams.get('stockStatus');
  const param: skuListParams = {
    keyword: '',
    categoryIdList: [],
    // @ts-ignore
    stockStatus: statusUrl,
    pageNo: 1,
    pageSize: 10,
  };

  const [showEdit, setShowEdit] = useState(false);
  const [showFilter, setShowFilter] = useState(false);
  const [batchVisible, setBatchVisible] = useState(false);
  const [skuIds, setSkuIds] = useState<Array<number | string>>([]);
  const [skuIdFoo, setSkuIdFoo] = useState<OrderResult[]>([]);
  const [skuIdOneFoo, setSkuIdOneFoo] = useState<OrderResult[]>([]);
  const [skuParams, setSkuParams] = useState(param);
  const refreshRef = useRef(null as unknown as ListUnitRef);
  const [status, setStatus] = useState(
    // eslint-disable-next-line no-nested-ternary
    statusUrl === '2' ? '超出' : statusUrl === '3' ? '短缺' : '全部'
  );

  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);

  const standard = (item: { standardJson: string }) => {
    const stand = JSON.parse(item.standardJson);
    return stand.map((item1: { name: string; value: string }) => (
      <span>
        {item1.name}: {item1.value},
      </span>
    ));
  };

  const standardFoo = (item: { standardJson: string }) => {
    const stand = JSON.parse(item.standardJson);
    let str = '';
    stand.forEach((item1: { name: string; value: string }) => {
      str += `${item1.name}: ${item1.value},`;
    });
    return str;
  };

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const exportAll = (e: MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    if (!checkPermission('AV_001_005_003')) {
      message.error('暂无权限，请联系公司管理员开通');
    } else {
      postPlatExportWarn().then((res) => {
        const URL = window.webkitURL || window.URL;
        const url = URL.createObjectURL(res);
        urlDownload(url, `${user.companyName}-库存预警信息.xls`);
      });
    }
  };

  const editOneOrAll = useCallback(
    (edit: number, value?: OrderResult) => {
      if (checkPermission('AV_001_005_001')) {
        setSkuIds(skuIds);
        if (value?.skuId) {
          setSkuIdOneFoo([value]);
        } else {
          setSkuIdOneFoo([]);
          const data = refreshRef.current.getAllList();
          const ids = skuIdFoo.map((m) => m.skuId);
          const dataFoo = data.filter((f) => ids.includes(f.skuId));
          setSkuIdFoo(dataFoo);
        }
        setBatchVisible(true);
      } else {
        message.error('暂无权限，请联系公司管理员开通');
      }
    },
    [skuIds] // eslint-disable-line
  );

  const categoryList = (list: any) => {
    let str;
    if (typeof list === 'string') {
      return list || '--';
    }
    if (list.length === 0) {
      str = '--';
    }
    if (list.length > 0 && list.length <= 2) {
      str = list.map((item: any) => (
        <div key={item.customizeCategoryId}>{item.customizeCategoryName}</div>
      ));
    }
    if (list.length > 2) {
      str = (
        <>
          {list
            .filter((item: any, index: any) => index < 2)
            .map((item: any) => (
              <div
                key={item.customizeCategoryId}
                className={styles.categoryItem}
                title={item.customizeCategoryName}
              >
                {item.customizeCategoryName}
              </div>
            ))}
          <Tooltip
            placement="bottom"
            trigger="hover"
            title={list
              .filter((item: any, index: any) => index >= 2)
              .map((item: any) => (
                <span key={item.customizeCategoryId} className={styles.categoryItemPopover}>
                  {item.customizeCategoryName}
                  <span className={styles.punctuate}>，</span>
                </span>
              ))}
          >
            <div
              role="button"
              tabIndex={0}
              className={styles.lookMore}
              onClick={(e) => e.stopPropagation()}
            >
              查看更多
              <Icon name="down" size={18} />
            </div>
          </Tooltip>
        </>
      );
    }
    return str;
  };

  const columns: columnsProps[] = [
    {
      title: '单位',
      align: 'center',
      dataIndex: 'unitName',
    },
    {
      title: '最低预警值',
      align: 'center',
      dataIndex: 'minStock',
    },
    {
      title: '最高预警值',
      align: 'center',
      dataIndex: 'maxStock',
    },
    {
      title: '实际库存',
      align: 'center',
      render: (value) => <div>{value.actualStock || '--'}</div>,
    },
    {
      title: '库存状态',
      align: 'center',
      dataIndex: 'availableStock',
      render: (value) => (
        <div
          className={classNames(styles.thresholdStock, {
            [styles.replenishQuantity]: value.stockStatus === 2,
          })}
        >
          {value.thresholdStock === '0' ? (
            <span style={{ color: '#000' }}>正常</span>
          ) : (
            <>
              <img
                src={
                  value.stockStatus === 3
                    ? 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023825/1692929591313967.png'
                    : 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023825/1692928627936102.png'
                }
                alt=""
              />
              <div className={styles.stockStatus}>{value.stockStatus === 3 ? '缺' : '超'}</div>
              {value.thresholdStock}
            </>
          )}
        </div>
      ),
    },
    {
      title: '自动补货数',
      align: 'center',
      dataIndex: 'replenishQuantity',
    },
  ];

  const shopColumns: shopColumnsProps[] = [
    {
      align: 'left',
      width: '25%',
      padding: '0 24px 0 0',
      render: (item) => (
        <GoodsInfo
          img={item.images}
          name={item.skuName}
          unit=""
          number={item.skuCode}
          standardId={item.stockStatus}
          imgWidth={52}
          imgHeight={52}
          isShowBarsCode={false}
        />
      ),
    },
    {
      align: 'left',
      width: '25%',
      render: (item) => (
        <div className={styles.standard}>
          <span className={styles.cords}>规格: </span>
          <span className={styles.grey} title={standardFoo(item)}>
            {standard(item)}
          </span>
        </div>
      ),
    },
    {
      align: 'left',
      width: '20%',
      render: (item) => (
        <div className={styles.standard}>
          <span className={styles.cords}>条码: </span>
          <span className={styles.grey}>{item.barsCode || '--'}</span>
        </div>
      ),
    },
    {
      align: 'left',
      width: '20%',
      render: (item) => (
        <div className={styles.standard}>
          <span className={styles.cords}>分类:</span>
          <span className={styles.grey}>
            {categoryList(
              item?.customizeCategoryList && item?.customizeCategoryList?.length
                ? item.customizeCategoryList
                : item.categoryName
            )}
          </span>
        </div>
      ),
    },
    {
      align: 'center',
      width: '10%',
      render: (value) =>
        checkPermission('AV_001_005_001') ? (
          <div className={styles.text}>
            <div
              role="button"
              tabIndex={0}
              className={styles.headItem}
              onClick={() => {
                editOneOrAll(0, value);
              }}
            >
              编辑
            </div>
          </div>
        ) : null,
    },
  ];

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <div
            role="button"
            tabIndex={0}
            className={styles.batchItem}
            onClick={() => {
              editOneOrAll(1);
            }}
          >
            批量编辑
          </div>
        }
      >
        {skuIds.length > 0 && (
          <div className={classNames(styles.batchBtn, styles.batchBtnInline)}>批量操作</div>
        )}
      </Popover>
    ),
    [editOneOrAll, skuIds.length]
  );

  const filterList = [
    { label: '全部', value: '全部' },
    { label: '短缺', value: '短缺' },
    { label: '超出', value: '超出' },
  ];

  const isFilter = useMemo(() => {
    if (skuParams?.categoryIdList?.length || skuParams?.warehouseNos?.length) {
      return true;
    }
    return false;
  }, [skuParams]);

  return (
    <Context
      permission="AV_001_005"
      head={
        <Context.Head
          onSearch={(e) => {
            setSkuParams({ ...skuParams, pageNo: 1, keyword: e });
          }}
          onFilter={() => {
            setShowFilter(true);
          }}
          isFilterActive={isFilter}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              value={status}
              // @ts-ignore
              options={filterList}
              onChange={(e) => {
                setStatus(e);

                setSkuParams({
                  ...skuParams,
                  pageNo: 1,
                  // @ts-ignore
                  // eslint-disable-next-line no-nested-ternary
                  stockStatus: e === '全部' ? null : e === '短缺' ? 3 : 2,
                });
              }}
            />
          }
          placeholder="商品名称 | 商品条形码"
          title={[
            {
              title: '仓库管理',
              to: '/stock',
            },
            '库存预警',
          ]}
          extra={
            checkPermission('AV_001_005_003') ? (
              <Context.HeadTool>
                <Dropdown
                  overlay={
                    <Menu
                      items={[
                        {
                          key: '2',
                          label: (
                            <div role="button" tabIndex={0} onClick={(e) => exportAll(e)}>
                              导出列表
                            </div>
                          ),
                        },
                      ]}
                    />
                  }
                  placement="top"
                >
                  <button type="button" className={styles.button}>
                    <Icon name="shangchuan" size={20} />
                  </button>
                </Dropdown>
              </Context.HeadTool>
            ) : null
          }
        />
      }
      style={{ backgroundColor: 'rgba(0,0,0,0)' }}
    >
      <ListUnit
        ref={refreshRef}
        footer={footer}
        request={stockWarnList}
        params={skuParams}
        rowSelection={{
          columnWidth: '40px',
          selectedRowKeys,
          onChange: (value, info) => {
            setSkuIdFoo(info);
            setSelectedRowKeys(value);
            setSkuIds(value);
          },
        }}
        rowKey="skuId"
        columnsKey="unitConfigList"
        columns={columns}
        shopColumns={shopColumns}
      />
      <Filter
        visible={showFilter}
        status={stockStatusParams.get('stockStatus') || 0}
        setSkuParams={(data) => {
          setSkuParams({
            ...data,
            pageNo: 1,
            keyword: skuParams.keyword,
            stockStatus: skuParams.stockStatus,
            categoryIdList: data.categoryIdList?.filter((item) => item),
          });
          setShowFilter(false);
        }}
        onClose={() => {
          setShowFilter(false);
        }}
      />
      <Edit
        visible={showEdit}
        skuIdFoo={skuIdOneFoo.length ? skuIdOneFoo : skuIdFoo}
        onSure={() => {
          setShowEdit(false);
          setSkuParams(skuParams);
          setSelectedRowKeys([]);
          refreshRef.current.refresh();
        }}
        onShow={() => {
          setShowEdit(false);
        }}
      />
      <BatchEdit
        items={skuIdOneFoo.length ? skuIdOneFoo : skuIdFoo}
        visible={batchVisible}
        onClose={() => {
          setBatchVisible(false);
        }}
        onConfirm={() => {
          refreshRef.current.refresh();
        }}
      />
    </Context>
  );
}

export default Warn;
