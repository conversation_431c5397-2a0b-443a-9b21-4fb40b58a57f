import { PropsWithChildren, useCallback, useEffect, useState } from 'react';
import { Checkbox, Drawer, Spin } from 'antd';
import { Search, Icon } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import { getCustomCategoryList, GetCategoryItemResult } from '@/apis';
import classNames from 'classnames';
import styles from './filter-category.module.less';

interface FilterCategoryPropps {
  visible: boolean;
  cateGoryId: Array<number | string>;
  onCloseCategory: () => void;
  setCateGoryId: MultipleParamsFn<[ids: Array<number | string>, list: GetCategoryItemResult[]]>;
  categoryList?: GetCategoryItemResult[];
}

function FilterCategory({
  visible,
  onCloseCategory,
  cateGoryId,
  setCateGoryId,
  categoryList,
}: PropsWithChildren<FilterCategoryPropps>) {
  const cateList = {
    pageNo: 1,
    pageSize: 10,
    categoryName: '',
    grade: 1,
    parentId: null,
  };

  const tierList = [
    {
      categoryName: '选择一级分类',
      id: null,
      index: 0,
    },
  ];

  const [categoriesData, setCategoriesData] = useState(cateList);
  const [tier, setTier] = useState(tierList);
  const [categoryDrawer, setCategoryDrawer] = useState(visible);
  const [cateNo, setCateNo] = useState(true);
  const [categories, setCategoryIes] = useState<GetCategoryItemResult[]>([]);
  const [checkedIds, setCheckedIds] = useState<Array<number | string>>([]);
  const [checkedList, setCheckedList] = useState<GetCategoryItemResult[]>([]);

  const tierIndex = (tierId: number) => {
    switch (tierId) {
      case 1:
        return '一';
      case 2:
        return '二';
      case 3:
        return '三';
      case 4:
        return '四';
      default:
        return null;
    }
  };

  const onClose = () => {
    setCategoryDrawer(false);
    setCategoriesData(cateList);
    setTier(tierList);
    setCategoryIes([]);
    onCloseCategory();
  };

  const levelClick = (id: number, isExistChildren: boolean) => {
    if (checkedIds.includes(id) || !isExistChildren) {
      return;
    }
    setCategoryIes([]);
    const data = {
      ...categoriesData,
      parentId: id,
      grade: 2,
      pageNo: 1,
    };
    const tierListF = tier.splice(0, tier.length - 1);
    const findTier: any = categories?.find((item) => item.id === id);
    tierListF.push({
      categoryName: findTier.categoryName,
      id: findTier.id,
      index: tier.length - 1,
    });
    tierListF.push({
      categoryName: `选择${tierIndex(tier.length + 1)}级分类`,
      id: null,
      index: tier.length,
    });
    // @ts-ignore
    setCategoriesData(data);
    setTier(tierListF);
  };

  // 选择
  const onChangeCategory = useCallback(
    (item: GetCategoryItemResult) => {
      const i = checkedIds.indexOf(item.id);
      if (i === -1) {
        checkedIds.push(item.id);
        checkedList.push(item);
      } else {
        checkedIds.splice(i, 1);
        checkedList.splice(i, 1);
      }
      setCheckedIds([...checkedIds]);
      setCheckedList([...checkedList]);
    },
    [checkedIds, checkedList]
  );

  const del = (id: number | string | boolean) => {
    setCheckedIds(checkedIds.filter((item) => item !== id));
    setCheckedList(checkedList ? checkedList.filter((item) => item.id !== id) : []);
  };

  const tierClick = (id: number | null, index: number) => {
    if (!id) return;
    setCategoryIes([]);
    if (id) {
      if (index === 0) {
        const list = {
          pageNo: 1,
          pageSize: 10,
          categoryName: categoriesData.categoryName,
          grade: 1,
          parentId: null,
        };
        const tierListF = [
          {
            categoryName: '选择一级分类',
            id: null,
            index: 0,
          },
        ];
        setCategoriesData(list);
        setTier(tierListF);
      } else {
        const list = {
          ...categoriesData,
          parentId: id,
          grade: 0,
        };
        const tierListF = tier.splice(0, index);
        tierList.push({
          categoryName: `选择${tierIndex(index)}级分类`,
          id: null,
          index,
        });
        // @ts-ignore
        setCategoriesData(list);
        setTier(tierListF);
      }
    }
  };

  const cateFatherIds = () => {
    setCheckedIds(cateGoryId);
    setCheckedList([...(categoryList || [])]);
  };

  const searchName = (value: string) => {
    setCategoryIes([]);
    setCategoriesData({ ...categoriesData, categoryName: value, pageNo: 1 });
  };

  const loadMore = () => {
    const list = {
      ...categoriesData,
      pageNo: (categoriesData.pageNo += 1),
    };
    setCategoriesData(list);
  };

  useEffect(() => {
    setCategoryDrawer(visible);
    if (visible) {
      cateFatherIds();
      getCustomCategoryList(categoriesData).then((res) => {
        setCategoryIes(categories.concat(res.list));
        setCateNo(res.list.length === 10);
      });
    }
  }, [visible, categoriesData]); // eslint-disable-line

  return (
    <Drawer
      onClose={onClose}
      visible={categoryDrawer}
      placement="bottom"
      width={375}
      className={styles.drawer}
      getContainer={false}
      mask={categoryDrawer}
      title="选择分类"
      extra={
        <div
          role="button"
          tabIndex={0}
          className={styles.level}
          onClick={() => {
            setTier(tierList);
            setCategoriesData(cateList);
            setCateGoryId(checkedIds, checkedList);
            setCategoryIes([]);
          }}
        >
          确定
        </div>
      }
    >
      <Search
        placeholder=""
        className={styles.search}
        onSearch={(value) => {
          searchName(value);
        }}
      />
      <div className={styles.select}>
        {checkedList
          ? checkedList.map((item) => (
              <span key={item.id} className={styles.item}>
                <span className={styles.text}>{item.categoryName}</span>
                <Icon
                  onClick={() => {
                    del(item.id);
                  }}
                  className={styles.del}
                  name="close-circle"
                />
              </span>
            ))
          : null}
      </div>
      <div className={styles.tier}>
        {tier
          ? tier.map((item, index) => (
              <span
                role="button"
                tabIndex={index}
                className={classNames(styles.text, item.id ? '' : styles.itemActive)}
                onClick={() => tierClick(item.id, item.index)}
              >
                {item.categoryName}
              </span>
            ))
          : null}
      </div>
      <div className={styles.list} id="categories">
        <InfiniteScroll
          dataLength={categories.length}
          hasMore={cateNo}
          loader={
            <div className="text-center">
              <Spin tip="加载中..." />
            </div>
          }
          next={loadMore}
          scrollableTarget="categories"
          height={400}
        >
          {categories
            ? categories.map((item) => (
                <div className={styles.label} key={item.id}>
                  <Checkbox
                    checked={checkedIds.includes(item.id)}
                    disabled={checkedIds.length >= 5 && !checkedIds.includes(item.id)}
                    onChange={() => onChangeCategory(item)}
                  >
                    {item.categoryName}
                  </Checkbox>
                  <span
                    role="button"
                    tabIndex={item.id}
                    className={classNames(
                      styles.level,
                      checkedIds.includes(item.id) || !item.isExistChildren ? styles.gray : ''
                    )}
                    onClick={() => {
                      levelClick(item.id, item.isExistChildren as boolean);
                    }}
                  >
                    下级
                  </span>
                </div>
              ))
            : null}
        </InfiniteScroll>
      </div>
    </Drawer>
  );
}

FilterCategory.defaultProps = {
  categoryList: [],
};

export default FilterCategory;
