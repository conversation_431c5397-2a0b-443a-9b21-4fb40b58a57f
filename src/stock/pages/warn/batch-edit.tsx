import { Button, InputNumber, Modal, message } from 'antd';
import { PropsWithChildren, useEffect, useMemo, useState } from 'react';
import { OrderResult, StandardItem, postSkuEditBatchStock } from '@/apis';
import classNames from 'classnames';
import { cloneDeep } from 'lodash';
import { stockParams } from '@/apis/psi/post-sku-edit-batch-stock';
import type { UnitConfigItem } from '@/apis/psi/get-sku-list-page';
import styles from './batch-edit.module.less';

interface batchProps {
  visible: boolean;
  items: OrderResult[];
  onClose: () => void;
  onConfirm: () => void;
}

function BatchEdit({ visible, items, onClose, onConfirm }: PropsWithChildren<batchProps>) {
  const [batchList, setBatchList] = useState<OrderResult[]>([]);
  const [cloneBatch, setCloneBatch] = useState<OrderResult[]>([]);

  const editShop = <K extends keyof UnitConfigItem>(
    e: number | null,
    eFoo: OrderResult,
    eIndex: number,
    eValue: K
  ) => {
    const arr = batchList.map((item) => {
      if (item.skuCode === eFoo.skuCode) {
        const unit = item.unitConfigList[eIndex];
        if (unit) {
          const value = +(e || 0) < 0 ? 0 : e;
          if (eValue === 'replenishQuantity' && e === 0) {
            unit[eValue] = '0' as never;
            return item;
          }
          // 限制输入框的值不为负数
          if (+(value || 0) < 0) {
            return item;
          }
          unit[eValue] = value as never;
        }
      }
      return item;
    });
    setBatchList(arr);
  };

  const editAllShop = <K extends keyof UnitConfigItem>(e: number, eValue: K) => {
    const arr = batchList.map((item) => {
      item.unitConfigList.forEach((unit) => {
        const value = +e < 0 ? 0 : e;
        // eslint-disable-next-line no-param-reassign
        unit[eValue] = value as never;
      });
      return item;
    });
    setBatchList(arr);
  };

  const metaTitle = (arr: StandardItem[]) => {
    let str = '';
    arr?.forEach((item, index) => {
      str += `${item.name}: ${item.value}${index === arr.length - 1 ? '' : ','}`;
    });
    return str;
  };

  const onOk = () => {
    const submitList: stockParams[] = [];
    batchList.forEach((f) => {
      f.unitConfigList.forEach((item) => {
        submitList.push({
          skuId: f.skuId || 0,
          unitName: item.unitName,
          minStock: item.minStock,
          maxStock: item.maxStock,
          replenishQuantity: item.replenishQuantity,
        });
      });
    });

    const quantity = submitList.some((item) => item.replenishQuantity === '0');
    if (quantity) {
      message.error('补货数量不能为0');
      return;
    }
    postSkuEditBatchStock({ stockWarningList: submitList }).then(() => {
      message.info('修改成功');
      onClose();
      onConfirm();
    });
  };

  const isOk = useMemo(
    () => JSON.stringify(cloneBatch) === JSON.stringify(batchList),
    [batchList, cloneBatch]
  );

  const footer = (
    <div className={styles.footer}>
      <Button type="default" onClick={onClose}>
        取消
      </Button>
      <Button type="primary" disabled={isOk} onClick={onOk}>
        保存
      </Button>
    </div>
  );

  useEffect(() => {
    if (visible) {
      const arr = cloneDeep(items);
      arr.forEach((f) => {
        f.unitConfigList.forEach((unitC) => {
          // eslint-disable-next-line no-param-reassign
          unitC.replenishQuantity = +unitC.replenishQuantity > 0 ? unitC.replenishQuantity : 0;
          // eslint-disable-next-line no-param-reassign
          unitC.minStock = String(unitC.minStock > 0 ? unitC.minStock : '') as never;
          // eslint-disable-next-line no-param-reassign
          unitC.maxStock = String(unitC.maxStock > 0 ? unitC.maxStock : '') as never;
        });
      });
      setCloneBatch(cloneDeep(arr));
      setBatchList(cloneDeep(arr));
    } else {
      setBatchList([]);
    }
  }, [visible, items]);

  return (
    <Modal
      visible={visible}
      wrapClassName={styles.modal}
      title="编辑"
      width={788}
      onCancel={onClose}
      centered
      footer={footer}
    >
      {(batchList.length > 1 || batchList.some((s) => s.unitConfigList.length > 1)) && (
        <div className={styles.modalTop}>
          <div className={styles.title}>批量设置</div>
          <div className={styles.content}>
            <div className={styles.item}>
              <div className={styles.label}>最低库存</div>
              <InputNumber
                placeholder="请输入"
                className={styles.input}
                min={0}
                onChange={(e) => editAllShop(e || 0, 'minStock')}
              />
            </div>
            <div className={styles.item}>
              <div className={styles.label}>最高库存</div>
              <InputNumber
                placeholder="请输入"
                className={styles.input}
                onChange={(e) => editAllShop(e || 0, 'maxStock')}
                min={0}
              />
            </div>
            <div className={styles.item}>
              <div className={styles.label}>补货数量</div>
              <InputNumber
                placeholder="请输入"
                className={styles.input}
                onChange={(e) => editAllShop(e || 0, 'replenishQuantity')}
                min={0}
              />
            </div>
          </div>
        </div>
      )}
      <div>
        <div className={styles.modalBody} style={{ height: '38px' }}>
          <div className={styles.shopAttribute}>商品属性</div>
          <div className={styles.lostUnit}>
            <div className={styles.shopUnit}>单位</div>
            <div className={styles.shopPrice}>最低预警值</div>
            <div className={styles.shopPrice}>最高预警值</div>
            <div className={styles.shopPrice}>自动补货数</div>
          </div>
        </div>
        <div className={styles.modalScroll}>
          {batchList.map((item) => (
            <div className={classNames(styles.modalBody, styles.modalBodyFoo)}>
              <div className={styles.shopAttribute}>
                <img className={styles.img} src={item.images} alt="" />
                <div className={styles.goods}>
                  <div className={styles.name} title={item.skuName}>
                    {item.skuName}
                  </div>
                  <div
                    className={styles.meta}
                    title={metaTitle(JSON.parse(item.standardJson || '[]'))}
                  >
                    规格：
                    {JSON.parse(item.standardJson || '[]') &&
                      JSON.parse(item.standardJson || '[]').length > 0 &&
                      JSON.parse(item.standardJson || '[]').map(
                        (each: { name: string; value: string }) => (
                          <span className={styles.metaItem} key={each.name}>
                            {each.name}: {each.value}
                            <span className={styles.metaTag}>;</span>
                          </span>
                        )
                      )}
                  </div>
                  <div className={styles.barsCode}>条形码：{item?.barsCode || '--'}</div>
                </div>
              </div>
              <div className={styles.lostUnitFoo}>
                {item.unitConfigList.map((lostItem, lostIndex) => (
                  <>
                    <div className={classNames(styles.lostUnit)}>
                      <div className={styles.shopUnit}>{lostItem.unitName || '--'}</div>
                      <div className={styles.shopPrice}>
                        <InputNumber
                          value={lostItem.minStock || undefined}
                          placeholder="请输入"
                          className={styles.input}
                          status={+lostItem.maxStock < +lostItem.minStock ? 'error' : ''}
                          onChange={(e) => editShop(e || 0, item, lostIndex, 'minStock')}
                          min={0}
                        />
                      </div>
                      <div className={styles.shopPrice}>
                        <InputNumber
                          value={lostItem.maxStock || undefined}
                          placeholder="请输入"
                          className={styles.input}
                          status={+lostItem.maxStock < +lostItem.minStock ? 'error' : ''}
                          onChange={(e) => editShop(e || 0, item, lostIndex, 'maxStock')}
                          min={0}
                        />
                      </div>
                      <div className={styles.shopPrice}>
                        <InputNumber
                          value={lostItem.replenishQuantity || undefined}
                          placeholder="请输入"
                          className={styles.input}
                          onChange={(e) => editShop(e, item, lostIndex, 'replenishQuantity')}
                          min={0}
                        />
                      </div>
                    </div>
                    {+lostItem.maxStock < +lostItem.minStock && (
                      <div className={styles.hint}>最高库存不能低于最低库存数量</div>
                    )}
                  </>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </Modal>
  );
}

export default BatchEdit;
