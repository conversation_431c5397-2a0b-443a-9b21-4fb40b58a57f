.card {
  margin-bottom: 20px;
  padding: 0 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  overflow: hidden;

  &:last-child {
    margin-bottom: 0;
  }
}

.batchSet {
  margin-bottom: 16px;
}

.set {
  display: flex;
  justify-content: space-between;
  position: relative;

  :global {
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      appearance: none;
    }

    input[type='number'] {
      appearance: textfield;
    }
  }
}

.label {
  font-size: 12px;
  margin-right: 4px;
}

.value,
.valueError {
  color: #040919;
  width: 92px;
  height: 28px;
  background: #f3f3f3;
  border: none;
  border-radius: 6px;
}

.valueError {
  border: 1px solid #ea1c26 !important;
  box-shadow: none !important;
}

.error {
  color: #ea1c26;
  font-size: 12px;
  position: absolute;
  bottom: -18px;
}

.item {
  margin-top: 20px;
  padding-bottom: 20px;
  position: relative;
  border-bottom: 1px solid #f3f3f3;

  &:last-child {
    border: none;
  }
}

.title {
  color: #888b98;
  margin: 16px 0 8px;
}

.footer {
  display: flex;
  width: 100%;
  height: 86px;
  line-height: 86px;
  padding: 0 20px;
  align-items: center;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 90%) 36%,
    rgb(255 255 255 / 90%) 36%
  );

  .footer {
    color: #fff;
    width: 100%;
    height: 38px;
    line-height: 38px;
    text-align: center;
    border-radius: 10px;
    background: linear-gradient(257deg, #00c6ff 0%, #00c6ff 0%, #008cff 100%, #008cff 100%);
    cursor: pointer;
  }
}

.stockBox {
  position: relative;
}

.cell {
  display: flex;
  align-items: center;
  margin-top: 20px;
}
