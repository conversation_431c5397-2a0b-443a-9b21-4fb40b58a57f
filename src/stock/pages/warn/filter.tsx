import { PropsWithChildren, useEffect, useState } from 'react';
import { Drawer, Icon } from '@/components';
import { message } from 'antd';
import {
  GetCategoryItemResult,
  getCustomCategoryList,
  GetCustomCategoryListResult,
  skuListParams,
} from '@/apis';
import classNames from 'classnames';
import FilterCategory from '../query/filter-category';
import styles from './filter.module.less';

interface FilterPropps {
  visible: boolean;
  onClose: () => void;
  status: number | string;
  // eslint-disable-next-line no-unused-vars
  setSkuParams: (kuList: skuListParams) => void;
}

function Filter({
  visible,
  status,
  setSkuParams,
  onClose,
  ...props
}: PropsWithChildren<FilterPropps>) {
  const cateGoryC: Array<GetCustomCategoryListResult> = [];
  const [categoryList, setCategoryList] = useState<GetCategoryItemResult[]>([]);
  const [cateGory, setCateGory] = useState(cateGoryC);
  const [count, setCount] = useState(0);
  const [cateGoryId, setCateGoryId] = useState<Array<number | string>>([0]);
  const [showCategory, setShowCategory] = useState(false);
  // const [stockStatus, setStockStatus] = useState(+status);
  const [categoriesData] = useState({
    pageNo: 1,
    pageSize: 10,
    categoryName: '',
    // grade: 1,
    // parentId: 0,
  });

  const selectWarehouse = (id: number) => {
    if (cateGoryId.length >= 5 && !cateGoryId.includes(id)) {
      message.warning('最多选择5个分类');
      return;
    }
    let arr: Array<number | string> = [...cateGoryId];
    let arrList: GetCategoryItemResult[] = [...categoryList];
    if (cateGory) {
      cateGory.forEach((item) => {
        if (item.id === id) {
          if (cateGoryId.indexOf(id) > -1) {
            arr = cateGoryId.filter((items) => items !== id);
            arrList = categoryList.filter((items) => items.id !== id);
          } else {
            arr.push(item.id);
            arrList.push(item);
          }
        }
      });
    }
    setCateGoryId(id ? arr.filter((item) => item) : [0]);
    setCategoryList([...arrList]);
  };

  const reset = () => {
    setCateGoryId([0]);
    setCategoryList([]);
  };

  useEffect(() => {
    getCustomCategoryList(categoriesData).then((res) => {
      setCount(res.list.length);
      // @ts-ignore
      setCateGory([{ id: 0, categoryName: '全部' }, ...res.list.splice(0, 4)]);
    });
  }, [categoriesData, visible]); // eslint-disable-line

  return (
    <Drawer
      title="筛选"
      visible={visible}
      {...props}
      className={styles.filter}
      onClose={() => {
        setSkuParams({
          // stockStatus,
          categoryIdList: cateGoryId,
          keyword: '',
        });
      }}
    >
      <div className={styles.card}>
        <div className={styles.title}>商品分类</div>
        <div className={styles.list}>
          {cateGory
            ? cateGory.map((item, index) => (
                <span
                  className={classNames(
                    styles.item,
                    cateGoryId.includes(item.id) ? styles.itemActive : ''
                  )}
                  role="button"
                  key={item.id}
                  tabIndex={index}
                  onClick={() => {
                    selectWarehouse(item.id);
                  }}
                >
                  <span className={styles.name}>{item.categoryName}</span>
                </span>
              ))
            : null}
          {count > 4 ? (
            <span
              role="button"
              tabIndex={0}
              className={classNames(styles.item, styles.itemMore)}
              onClick={() => {
                setShowCategory(true);
              }}
            >
              更多分类
              <Icon name="right" />
            </span>
          ) : null}
        </div>
      </div>
      <div className={styles.flooder}>
        <div
          role="button"
          tabIndex={0}
          className={styles.flooderBtn}
          onClick={() => {
            reset();
          }}
        >
          重置
        </div>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.flooderBtn, styles.flooderBtnAdd)}
          onClick={() => {
            setSkuParams({
              // stockStatus,
              categoryIdList: cateGoryId,
              keyword: '',
            });
          }}
        >
          确定
        </div>
      </div>
      <FilterCategory
        visible={showCategory}
        cateGoryId={cateGoryId.filter((item) => item !== 0)}
        checkedMax={5}
        // @ts-ignore
        setCateGoryId={(list: Array<number | string>, value: GetCategoryItemResult[]) => {
          setCateGoryId(list);
          setCategoryList(value || []);
          setShowCategory(false);
        }}
        onCloseCategory={() => {
          setShowCategory(false);
        }}
        // @ts-ignore
        categoryList={categoryList}
      />
    </Drawer>
  );
}

export default Filter;
