@import 'styles/mixins/mixins';

.footer {
  display: flex;
  justify-content: right;
}

.modal {
  :global {
    .ant-modal-header {
      border-bottom: none;
      position: relative;
    }

    .ant-modal-body {
      padding: 0 16px 16px;
    }

    .ant-modal-footer {
      border-top: none;
    }

    .ant-modal-header::after {
      content: '*';
      color: red;
      font-size: 16px;
      position: absolute;
      top: 16px;
      left: 9px;
    }

    .ant-input-number-handler-wrap {
      display: none !important;
    }
  }
}

.modalTop {
  margin-bottom: 20px;
  padding: 12px 20px;
  background: #f9fafc;
  // opacity: 0.5;
  border-radius: 12px;

  :global {
    .ant-input {
      width: 155px;
    }

    .ant-input-number {
      width: 155px;
    }
  }

  .title {
    color: #040919;
    font-size: 600;
    font-size: 14px;
    margin-bottom: 16px;
  }

  .content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .item {
    display: flex;
    justify-content: space-between;
    align-items: center;

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      appearance: none !important;
    }
  }

  .label {
    margin-right: 12px;
  }
}

.modalBody {
  display: flex;
  padding: 9px 20px;
  background-color: #f5f6fa;
  border-radius: 10px;

  .shopAttribute {
    width: 240px;
  }

  .lostUnit {
    display: flex;
    align-items: center;
    margin: 8px 0;

    .shopUnit {
      width: 118px;
      text-align: center;
    }

    .shopPrice {
      width: 120px;
      text-align: center;
    }
  }
}

.modalScroll {
  min-height: 132px;
  max-height: 304px;
  overflow-x: hidden;
  overflow-y: auto;
}

.modalBodyFoo {
  background-color: #fefeff;
  border-bottom: 1px solid #ebeef5;

  .shopAttribute {
    display: flex;
    align-items: center;

    & .img {
      width: 64px;
      height: 64px;
      border-radius: 6px;
      margin-right: 12px;
    }

    & .goods {
      width: calc(100% - 86px);
    }

    & .name {
      margin-bottom: 8px;
      .text-overflow(2);
    }

    & .meta {
      color: #888b98;
      font-size: 12px;
      margin-bottom: 4px;
      .text-overflow(1);
    }

    & .saleGroup {
      color: #888b98;
      font-size: 12px;
    }

    & .barsCode {
      color: #888b98;
      font-size: 12px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .shopUnit {
    :global {
      .ant-select-selector {
        width: 130px;
        text-align: left;
      }
    }
  }

  .shopPrice {
    :global {
      .ant-input {
        width: 102px;
      }
    }

    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      appearance: none !important;
    }
  }

  .lostUnitFoo {
    display: flex;
    flex-direction: column;
    justify-content: center;

    :global {
      .ant-input-number {
        width: 110px;
      }
    }
  }

  .hint {
    color: red;
    padding-left: 156px;
  }
}

.modalBodyFoo:last-child {
  border-bottom: none;
}
