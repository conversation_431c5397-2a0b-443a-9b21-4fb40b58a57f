import { useCallback, useState, Key, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Context, Icon, List, ListInstance } from '@/components';
import { Button, Dropdown, Menu, Popover, message, Modal } from 'antd';
import { stockHomeWarehouseQueryList, WareListRusult, stockWarehouseBatchStart } from '@/apis';
import classNames from 'classnames';
import { checkPermission } from '@/utils/permission';
import Edit from './warehouse-create';
import styles from '../../styles/list.module.less';
import up from '../../assets/imgs/up.png';
import down from '../../assets/imgs/down.png';
import disable from '../../assets/imgs/disable.png';
import enable from '../../assets/imgs/enable.png';
// import { warehouseDetail } from './warehouse-detail';

const filterList = [
  { label: '全部', value: '全部' },
  { label: '启用', value: '启用' },
  { label: '停用', value: '停用' },
];

function Warehouse() {
  const navigate = useNavigate();
  const [loading] = useState(false);
  const [showEdit, setShowEdit] = useState(false);
  const [id] = useState(0); // setIds
  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [dataTransfer] = useState<WareListRusult>(); // setDataTransfer
  const [parameter, setParameter] = useState({});
  const refreshRef = useRef(null as unknown as ListInstance);
  const [warehouseStatus, setWarehouseStatus] = useState('全部');

  const editWarehouse = (record: WareListRusult) => {
    // setDataTransfer({ ...record });
    // // @ts-ignore
    // setIds(record.id);
    // setShowEdit(true);
    navigate(`/stock/warehouse/add?id=${record.id}`);
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const batchHandle = (status: number) => {
    if (selectedRowKeys.length <= 0) {
      message.error('请选择仓库');
      return;
    }
    if (!checkPermission('AV_001_007_003')) {
      message.error('暂无权限，请联系公司管理员开通');
    } else {
      Modal.confirm({
        title: '提示',
        icon: '',
        okText: '确认',
        cancelText: '取消',
        centered: true,
        content: `确定批量${status ? '停用' : '启用'}仓库？`,
        onOk: () => {
          stockWarehouseBatchStart({
            ids: selectedRowKeys,
            warehouseStatus: status,
          }).then(() => {
            setParameter({ ...parameter });
            refreshRef.current.refresh();
          });
        },
      });
    }
  };

  const sorter = (sort: string) => {
    setParameter({
      sort,
    });
  };

  const head = (
    <Context.Head
      quickFilter={
        <Context.QuickFilter
          label="显示"
          value={warehouseStatus}
          // @ts-ignore
          options={filterList}
          onChange={(e) => {
            setWarehouseStatus(e);
            setParameter({
              ...parameter,
              // eslint-disable-next-line no-nested-ternary
              warehouseStatus: e === '全部' ? null : e === '启用' ? 0 : 1,
            });
          }}
        />
      }
      onSearch={(e) => {
        setParameter({
          ...parameter,
          key: e,
        });
      }}
      placeholder="搜索仓库"
      title={[
        {
          title: '仓库管理',
          to: '/stock',
        },
        '仓库列表',
      ]}
      extra={
        checkPermission('AV_001_007_002') ? (
          <Context.HeadTool>
            <Button
              size="small"
              type="primary"
              onClick={() => {
                // setIds(0);
                // setShowEdit(true);
                navigate('/stock/warehouse/add');
              }}
            >
              新建仓库
            </Button>
          </Context.HeadTool>
        ) : null
      }
    />
  );

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <>
            <div
              className={styles.batchItem}
              role="button"
              tabIndex={0}
              onClick={() => {
                batchHandle(0);
              }}
            >
              批量启用
            </div>
            <div
              className={styles.batchItem}
              role="button"
              tabIndex={0}
              onClick={() => {
                batchHandle(1);
              }}
            >
              批量停用
            </div>
          </>
        }
      >
        <div className={classNames(styles.batchBtn, styles.batchBtnInline)}>批量操作</div>
      </Popover>
    ),
    [batchHandle, selectedRowKeys.length] // eslint-disable-line
  );

  return (
    <Context permission="AV_001_007" loading={loading} head={head}>
      <List
        // @ts-ignore
        ref={refreshRef}
        footer={footer}
        request={stockHomeWarehouseQueryList}
        params={parameter}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          onChange: (value) => {
            setSelectedRowKeys(value);
          },
          getCheckboxProps: (record) => ({
            disabled: record.belongCompanyId && record.belongCompanyId !== record.companyId,
          }),
        }}
        rowKey="id"
        onRow={(e: any) => ({
          onClick: () => {
            navigate(`/stock/info/${e.id}`);
            // warehouseDetail({
            //   id: e.id,
            //   onEdit: (idVal: number) => {
            //     setIds(idVal);
            //     setShowEdit(true);
            //   },
            // });
          },
        })}
        columns={[
          {
            title: '仓库名称',
            width: '30%',
            render: (item) => (
              <div className={styles.warehouseName}>
                <div className={styles.tableItem}>{item.warehouseName}</div>
                {!!item.defaultWarehouse && !item.warehouseBelongType && (
                  <img
                    className={styles.defaultImg}
                    src="https://img.huahuabiz.com/user_files/2023919/1695113858789645.png"
                    alt=""
                  />
                )}
              </div>
            ),
          },
          {
            title: '仓库状态',
            align: 'center',
            width: '20%',
            render: (record) => (
              <div className={styles.warehouseState}>
                <img
                  className={styles.stateImg}
                  src={record.warehouseStatus === 0 ? enable : disable}
                  alt=""
                />
                {record.warehouseBelongType === 1 && (
                  <img
                    className={styles.stateImg}
                    src="https://img.huahuabiz.com/user_files/2023417/1681695269114994.png"
                    alt=""
                  />
                )}
              </div>
            ),
          },
          {
            // eslint-disable-next-line react/no-unstable-nested-components
            title: () => (
              <div className={styles.numBox}>
                <span>库存数量</span>
                <div className={styles.num}>
                  {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
                  <img
                    className={styles.numImg}
                    src={up}
                    alt=""
                    onClick={() => {
                      sorter('desc');
                    }}
                  />
                  {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
                  <img
                    className={styles.numImg}
                    src={down}
                    alt=""
                    onClick={() => {
                      sorter('asc');
                    }}
                  />
                </div>
              </div>
            ),
            width: '20%',
            align: 'center',
            dataIndex: 'warehouseStock',
            render: (warehouseStock) => (
              <div className={classNames(styles.tableItem, styles.blue)}>
                {warehouseStock && warehouseStock !== 0
                  ? warehouseStock.toString().replace(/(\d)(?=(\d{3})+$)/g, '$1,')
                  : 0}
              </div>
            ),
          },
          {
            title: '仓库地址',
            width: '30%',
            align: 'center',
            render: (warehouseAddress) => (
              <div className={styles.tableItem}>
                {warehouseAddress.warehouseAddress ? (
                  warehouseAddress.warehouseAddress
                ) : (
                  <span>--</span>
                )}
              </div>
            ),
          },
          {
            title: '操作',
            width: '70px',
            align: 'right',
            dataIndex: 'warehouseAddress',
            render: (value, record) =>
              (record.belongCompanyId && record.belongCompanyId === record.companyId) ||
              !record.belongCompanyId ? (
                <Dropdown
                  overlay={
                    <Menu
                      items={[
                        {
                          key: '1',
                          label: (
                            <div
                              role="button"
                              tabIndex={0}
                              className={styles.batchItem}
                              onClick={(e) => {
                                e.stopPropagation();
                                if (record.defaultWarehouse && record.warehouseStatus === 0) {
                                  message.warning('该仓库为默认仓库，不允许禁用!');
                                  return;
                                }
                                if (checkPermission('AV_001_007_003')) {
                                  Modal.confirm({
                                    title: '提示',
                                    icon: '',
                                    okText: '确认',
                                    cancelText: '取消',
                                    centered: true,
                                    content: `确定${
                                      record?.warehouseStatus ? '启用' : '停用'
                                    }仓库？`,
                                    onOk: () => {
                                      stockWarehouseBatchStart({
                                        warehouseStatus: record?.warehouseStatus ? 0 : 1,
                                        ids: [record?.id],
                                      }).then(() => {
                                        message.success(
                                          `${record?.warehouseStatus ? '启用' : '停用'}成功`
                                        );
                                        refreshRef.current.refresh();
                                      });
                                    },
                                  });
                                } else {
                                  message.error('暂无权限，请联系公司管理员开通');
                                }
                              }}
                            >
                              {record.warehouseStatus === 0 ? '停用' : '启用'}
                            </div>
                          ),
                        },
                        {
                          key: '2',
                          label: (
                            <div
                              role="button"
                              tabIndex={0}
                              className={styles.batchItem}
                              onClick={(e) => {
                                e.stopPropagation();
                                if (checkPermission('AV_001_007_002')) {
                                  editWarehouse(record);
                                } else {
                                  message.error('暂无权限，请联系公司管理员开通');
                                }
                              }}
                            >
                              编辑
                            </div>
                          ),
                        },
                      ]}
                    />
                  }
                >
                  <Icon className={classNames(styles.icon, styles.fontSize)} name="zu13366" />
                </Dropdown>
              ) : (
                <Icon
                  className={classNames(styles.icon, styles.fontSize, styles.iconColor)}
                  name="zu13366"
                />
              ),
          },
        ]}
      />
      <Edit
        visible={showEdit}
        id={id}
        dataTransfer={dataTransfer}
        onClose={() => {
          setShowEdit(false);
        }}
        setParameter={() => {
          setParameter({ ...parameter });
        }}
      />
    </Context>
  );
}

export default Warehouse;
