import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { Form, Input, Switch, Button, message } from 'antd';
import { Drawer, GaoDeMap, Icon, Modal, SelectedMapInfo } from '@/components';
import {
  stockWarehouseNew,
  WareListRusult,
  stockWarehouseEditor,
  StockWarehouseCompanyListResult,
  stockWarehouseDetail,
} from '@/apis';
import type { DefaultOptionType } from 'antd/es/select';
import { checkPermission, testPerm } from '@/utils/permission';
import selectCompany from '@@/stock/containers/select-company';
import classNames from 'classnames';
import { selectUsers } from '@/src/home/<USER>';
import { useRequest } from 'ahooks';
import { OrderParams } from '@/apis/psi/stock-warehouse-new';
import { editorParams } from '@/apis/psi/stock-warehouse-editor';
import { user } from '@/store';
import PopupModal from '../../components/modal/index';
import styles from './warehouse-create.module.less';

interface EditPropps {
  visible: boolean;
  id?: number;
  onClose: () => void;
  setParameter: () => void;
  dataTransfer: WareListRusult | undefined;
}

function Edit({
  visible,
  id,
  onClose,
  dataTransfer,
  setParameter,
  ...props
}: PropsWithChildren<EditPropps>) {
  const [form] = Form.useForm();
  const selectMapInfo = useRef({} as SelectedMapInfo);
  const [headItem, setHeadItem] = useState<any[]>([]);
  const [address, setAddress] = useState<DefaultOptionType[]>([]);
  // const [addressList, setAddressList] = useState<recordParams[]>([]);
  const [modal, setModal] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [oldFromValue, serOldFromValue] = useState('');
  const [locationAddress, setLocationAddress] = useState('');
  const [hasSelectList, setHasSelectList] = useState<StockWarehouseCompanyListResult[]>([]);
  const [warehouseShareStatus, setWarehouseShareStatus] = useState(false);
  const [warehouseHead, setWarehouseHead] = useState<any[]>([]);
  const [warehouseState, setWarehouseState] = useState(true);
  const [defaultWarehouse, setDefaultWarehouse] = useState(0);

  const [location, setLocation] = useState({ longitude: null, latitude: null } as {
    longitude: number | null;
    latitude: number | null;
  });

  const { run: runDetail } = useRequest(stockWarehouseDetail, {
    manual: true,
    defaultParams: [{ id: id || 0 }],
    onSuccess: (res) => {
      const { longitude, latitude } = res;
      const value = {
        name: res.warehouseName,
        state: res.warehouseStatus === 0,
        address: res.provinceId ? [res.provinceId, res.cityId, res.regionId] : [],
        detail: res.warehouseAddress,
        companyCode: res.companyCode,
        defaultWarehouse: res.defaultWarehouse === 1,
      };
      setAddress([
        {
          label: res.provinceName,
          value: res.provinceId,
        },
        {
          label: res.cityName,
          value: res.cityId,
        },
        {
          label: res.region,
          value: res.regionId,
        },
      ]);
      form.setFieldsValue({ ...value });
      if (res.headId && res.headName) {
        setHeadItem([{ memberId: res.headId, label: res.headName, id: res.headUserId || 0 }]);
      } else {
        setHeadItem([]);
      }
      setLocation({ longitude, latitude });
      setLocationAddress(res.warehouseAddress);
      setWarehouseShareStatus(Boolean(res.warehouseShareStatus));
      setHasSelectList([...res.shareCompanyVOS]);
      serOldFromValue(JSON.stringify({ ...value }));
      setWarehouseState(res.warehouseStatus === 0);
      setDefaultWarehouse(res.defaultWarehouse);
      setWarehouseHead(
        res.wareWorkerVos.map((item) => ({
          ...item,
          label: item.name,
          id: item.userId,
        }))
      );
    },
  });

  const submit = () => {
    const fieldsValue = form.getFieldsValue();
    if (!fieldsValue.name) return message.error('请填写仓库名称');
    if (warehouseShareStatus && hasSelectList.length === 0) {
      message.error('请选择共享企业');
      return false;
    }

    let list = {};
    const params: OrderParams = {
      warehouseName: fieldsValue.name,
      warehouseStatus: fieldsValue.state ? 0 : 1,
      warehouseAddress: fieldsValue.detail,
      warehouseShareStatus,
      shareCompanyIds: hasSelectList.map((item) => item.shareCompanyId),
      workerMemberIdList: warehouseHead.map((item) => item.memberId),
      defaultWarehouse: fieldsValue.defaultWarehouse ? 1 : 0,
    };
    if (headItem.length) {
      params.headId = headItem[0].memberId;
    }
    if (fieldsValue.companyCode) {
      params.companyCode = fieldsValue.companyCode;
    }
    if (selectMapInfo.current.latitude && selectMapInfo.current.longitude) {
      params.longitude = selectMapInfo.current.longitude;
      params.latitude = selectMapInfo.current.latitude;
    }

    if (address && address.length > 0) {
      list = {
        provinceId: address[0].value,
        provinceName: address[0].label,
        cityId: address[1].value,
        cityName: address[1].label,
        regionId: address[2].value,
        region: address[2].label,
      };
    }

    if (!id) {
      if (fieldsValue.name) {
        stockWarehouseNew(address ? { ...params, ...list } : { ...params }).then(() => {
          onClose();
          setParameter();
          form.setFieldsValue({
            name: undefined,
            state: 0,
            address: undefined,
            detail: undefined,
          });
        });
      }
    } else {
      if (id && !checkPermission('AV_001_007_003')) {
        params.warehouseStatus = dataTransfer?.warehouseStatus || 0;
      }
      params.id = id;
      if (!address || address.length === 0) {
        list = {
          provinceId: null,
          provinceName: '',
          cityId: null,
          cityName: '',
          regionId: null,
          region: '',
        };
      }

      stockWarehouseEditor({ ...(params as editorParams), ...list }).then(() => {
        onClose();
        setParameter();
        form.setFieldsValue({
          name: undefined,
          state: 1,
          address: undefined,
          detail: undefined,
        });
      });
    }
    return false;
  };

  const onExitDrawer = () => {
    const fromValue = Object.values(form.getFieldsValue(['name', 'address', 'detail']));
    const idEdit = fromValue.every((item) => item === undefined || item === '');
    if (!id) {
      if (!idEdit) {
        setModal(true);
      } else {
        setModal(false);
        onClose();
      }
    } else {
      const newValue = JSON.stringify({ ...form.getFieldsValue() });
      if (oldFromValue === newValue) {
        setModal(false);
        onClose();
      } else {
        setModal(true);
      }
    }
  };

  const onSelectCompany = () => {
    selectCompany({
      hasSelect: hasSelectList,
      onSuccess: (arr) => {
        setHasSelectList(arr);
      },
    });
  };

  const onChangeWarehouseStatus = (e: boolean) => {
    if (testPerm('AV_001_007_004')) {
      setWarehouseShareStatus(e);
    }
  };

  // 添加联系人
  const addMembers = () => {
    selectUsers({
      title: '选择仓库负责人',
      userIdKey: 'userId',
      onlyCompany: false,
      onlySelectUser: true,
      companyId: user.company.id,
      ignore: true,
      max: 1,
      isDisabledConfirm: false,
      defaults: headItem,
      onConfirm: (instance, close) => {
        setHeadItem(instance.getList());
        close();
      },
    });
  };

  // 选择协作人
  const onSelectWarehouseHead = () => {
    selectUsers({
      title: '选择仓库协作人',
      userIdKey: 'userId',
      onlyCompany: false,
      onlySelectUser: true,
      companyId: user.company.id,
      ignore: true,
      isDisabledConfirm: false,
      defaults: warehouseHead,
      onConfirm: (instance, close) => {
        setWarehouseHead([...instance.getList()]);
        close();
      },
    });
  };

  useEffect(() => {
    if (!id) {
      setAddress([]);
      setWarehouseShareStatus(false);
      setHasSelectList([]);
      setHeadItem([]);
      setWarehouseHead([]);
      setWarehouseState(true);
      form.setFieldsValue({
        name: undefined,
        state: true,
        address: undefined,
        detail: undefined,
        defaultWarehouse: undefined,
        companyCode: undefined,
      });
    } else {
      runDetail({ id });
    }
  }, [form, id, runDetail, visible]);

  return (
    <Drawer
      title={`${id ? '编辑' : '新建'}仓库`}
      visible={visible}
      {...props}
      className={styles.edit}
      onClose={onExitDrawer}
    >
      <Form form={form} layout="vertical" className={styles.form}>
        <div className={styles.card}>
          <div className={styles.nameTitle}>
            <span className={styles.red}>*</span>
            <span>仓库名称</span>
          </div>
          <Form.Item
            name="name"
            initialValue=""
            className={styles.borderNo}
            rules={[{ required: true, message: '请输入仓库名称' }]}
          >
            <Input.TextArea
              autoSize={{ minRows: 3, maxRows: 5 }}
              placeholder="请输入仓库名称"
              maxLength={20}
              showCount
              bordered={false}
              className={styles.textarea}
            />
          </Form.Item>
          <div
            className={classNames(styles.state, styles.stateFoo)}
            role="button"
            tabIndex={0}
            onClick={addMembers}
          >
            <div>仓库负责人</div>
            <Form.Item name="stateName" valuePropName="checked">
              <div className={styles.head}>
                {headItem.length > 0 ? (
                  <span style={{ color: '#000' }}>{headItem[0].label}</span>
                ) : (
                  <span>请选择仓库负责人</span>
                )}

                <Icon name="right" size={14} className={styles.iconRight} />
              </div>
            </Form.Item>
          </div>
          <div className={styles.cell}>
            <div className={styles.label}>仓库协作人</div>
            <div
              role="button"
              tabIndex={0}
              className={styles.value}
              onClick={onSelectWarehouseHead}
            >
              <div className={styles.valueText}>
                {warehouseHead.length ? (
                  warehouseHead.map((item) => (
                    <span className={styles.valueItem}>
                      <span>{item.label}</span>
                      <span className={styles.valueHeadDesc}>，</span>
                    </span>
                  ))
                ) : (
                  <span className={styles.placeholder}>请选择仓库协作人</span>
                )}
              </div>
              <Icon name="right" size={14} className={styles.iconRight} />
            </div>
          </div>
          {checkPermission('AV_001_007_003') && (
            <div className={classNames(styles.state)}>
              <div>仓库状态</div>
              <Form.Item name="state" valuePropName="checked">
                <Switch
                  defaultChecked
                  onChange={(e) => {
                    setWarehouseState(e);
                    if (id && !e && defaultWarehouse) {
                      message.warning('该仓库为默认仓库，不允许禁用!');
                      form.setFieldsValue({ defaultWarehouse: true });
                      form.setFieldsValue({ state: true });
                      setWarehouseState(true);
                      return;
                    }
                    if (!e) {
                      form.setFieldsValue({ defaultWarehouse: false });
                      setDefaultWarehouse(0);
                    }
                  }}
                />
              </Form.Item>
            </div>
          )}
          {warehouseState && (
            <div className={classNames(styles.state, styles.stateDefault)}>
              <div>默认仓库</div>
              <Form.Item name="defaultWarehouse" valuePropName="checked">
                <Switch
                  onChange={(e) => {
                    if (id && !e && defaultWarehouse) {
                      message.warning('至少要有一个默认仓库!');
                      form.setFieldsValue({ defaultWarehouse: true });
                    }
                  }}
                />
              </Form.Item>
            </div>
          )}
        </div>
        <div className={styles.card}>
          <div className={styles.address}>
            <span>仓库地址</span>
            <div
              role="button"
              tabIndex={0}
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              <img
                className={styles.addressImg}
                src="https://img.huahuabiz.com/user_files/2022819/166089932312223.png"
                alt=""
              />
            </div>
          </div>
          <Form.Item label="" name="detail">
            <Input.TextArea
              autoSize={{ minRows: 1 }}
              placeholder="请输入仓库地址"
              bordered={false}
              className={styles.textarea}
            />
          </Form.Item>
        </div>
        <div className={styles.card}>
          <div className={classNames(styles.state, styles.stateHead, styles.bottomBorder)}>
            <div>仓库公司代码</div>
            <Form.Item name="companyCode">
              <Input maxLength={30} placeholder="请输入仓库公司代码" bordered={false} />
            </Form.Item>
          </div>
          <div className={styles.formItem}>
            <div>共享仓库</div>
            <Switch checked={warehouseShareStatus} onChange={(e) => onChangeWarehouseStatus(e)} />
          </div>
          {warehouseShareStatus && (
            <div className={styles.formItem}>
              <span>共享企业</span>
              <div
                role="button"
                tabIndex={0}
                className={styles.formItemNoData}
                onClick={onSelectCompany}
              >
                {hasSelectList.length ? (
                  <div className={styles.companyImgList}>
                    {hasSelectList
                      .filter((item, index) => index < 5)
                      .map((item) => (
                        <img
                          className={styles.companyImg}
                          key={item.shareCompanyId}
                          src={
                            item.logoUrl ||
                            'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
                          }
                          alt=""
                        />
                      ))}
                    {hasSelectList.length > 5 && (
                      <div className={styles.companyImgListNum}>+{hasSelectList.length - 5}</div>
                    )}
                  </div>
                ) : (
                  <span className="mr-1">请选择</span>
                )}
                <Icon name="right" />
              </div>
            </div>
          )}
        </div>
        <div className={styles.footer}>
          <Form.Item>
            <Button type="primary" htmlType="submit" onClick={submit}>
              保存
            </Button>
          </Form.Item>
        </div>
      </Form>
      <PopupModal
        visible={modal}
        onDetermine={() => {
          setModal(false);
        }}
        onclose={() => {
          form.setFieldsValue({
            name: undefined,
            state: 1,
            address: undefined,
            detail: undefined,
          });
          setModal(false);
          onClose();
        }}
      />

      {/* 地图 */}
      {isModalOpen && (
        <Modal
          title="选择地址"
          visible={isModalOpen}
          onOk={() => {
            const {
              address: addressFoo,
              longitude,
              latitude,
              // province,
              // city,
              // district,
            } = selectMapInfo.current;
            form.setFieldsValue({
              detail: addressFoo,
            });
            setLocation({ longitude, latitude });
            // setAddressRegion({ provinceName: province, cityName: city, districtName: district });
            setIsModalOpen(false);
            setLocationAddress(addressFoo);
          }}
          onCancel={() => {
            setIsModalOpen(false);
          }}
        >
          <GaoDeMap
            width="660px"
            height="500px"
            showCircle={false}
            location={location}
            address={locationAddress}
            onSelectChanged={(val) => {
              selectMapInfo.current = val;
            }}
          />
        </Modal>
      )}
    </Drawer>
  );
}

Edit.defaultProps = {
  id: 0,
};

export default Edit;
