@import 'styles/mixins/mixins';

.editBtn {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.card {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.title {
  margin-bottom: 16px;
}

.textarea {
  width: 100%;
  height: 84px;
  margin-bottom: 16px;
  padding: 12px;
  background: #fafcfe;
  border: 1px solid #f3f3f3;
  border-radius: 12px;
}

.cell {
  display: flex;
  padding: 14px 0;
  justify-content: space-between;
  border-top: 1px solid #f3f3f3;

  &:last-child {
    padding: 14px 0 0;
  }
}

.stateFoo {
  margin-bottom: 14px;
  padding: 14px 0;
  border-top: 1px solid #f3f3f3;
  border-bottom: 1px solid #f3f3f3;
}

.borderBottom {
  border-bottom: 1px solid #f3f3f3;
}

.value {
  font-size: 16px;
  max-width: 180px;
  text-align: right;
  .text-overflow(1);
}

.valueItem:last-child {
  .valueItemDesc {
    display: none;
  }
}

.formValue {
  font-size: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;
}

.formValueLast {
  font-size: 16px;
  max-width: 180px;
  .text-overflow(1);
}

.formValueAddress {
  font-size: 16px;
}

.formCell {
  display: flex;
  justify-content: space-between;
}

.formItemCell {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;
}

.formItem {
  display: flex;
  margin-bottom: 16px;
  padding-bottom: 16px;
  justify-content: space-between;
  border-bottom: 1px solid #f3f3f3;

  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
}

.formItemNoData {
  color: #b1b3be;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.companyImgList {
  margin-right: 10px;
  position: relative;
  text-align: right;
}

.companyImgListNum {
  color: #fff;
  font-weight: 600;
  width: 34px;
  height: 34px;
  line-height: 34px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99;
  text-align: center;
  background: linear-gradient(0deg, rgb(0 0 0 / 40%), rgb(0 0 0 / 40%));
  border-radius: 50%;
}

.companyImg {
  width: 34px;
  height: 34px;
  margin-right: -16px;
  float: right;
  border-radius: 50%;

  &:first-child {
    margin-right: 0;
  }
}

.noData {
  color: #888b98;
  font-size: 16px;
}

.noDataAddress {
  color: #888b98;
  font-size: 16px;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;
}
