import { useEffect, useMemo, useState } from 'react';
import { Drawer, Icon } from '@/components';
import { drawerPopup } from '@/utils/popup';
import { DrawerProps } from 'antd';
import { useRequest } from 'ahooks';
import { stockWarehouseDetail, StockWarehouseDetailResult } from '@/apis';
import classNames from 'classnames';
import { testPerm } from '@/utils/permission';
import { selectCompanyList } from '../../containers/select-company/select-company-list';
import styles from './warehouse-detail.module.less';

interface WarehouseDetailProps extends DrawerProps {
  id: number;
  onClose?: () => void;
  // eslint-disable-next-line no-unused-vars
  onEdit: (id: number) => void;
}

function WarehouseDetail({ id, onClose, onEdit, ...props }: WarehouseDetailProps) {
  const [warehouseInfo, setWarehouseInfo] = useState<StockWarehouseDetailResult>({
    belongCompanyId: 0,
    cityId: 0,
    provinceId: 0,
    regionId: 0,
    id: 0,
    warehouseName: '',
    provinceName: '',
    cityName: '',
    region: '',
    warehouseStatus: 0,
    shareCompanyVOS: [],
    warehouseAddress: '',
    warehouseShareStatus: 0,
    companyId: 0,
    headName: '',
    companyCode: '',
    headId: 0,
    longitude: null,
    latitude: null,
    defaultWarehouse: 0,
    wareWorkerVos: [],
    warehouseBelongType: 0,
    headUserId: 0,
  });

  const { run } = useRequest(stockWarehouseDetail, {
    manual: true,
    defaultParams: [{ id }],
    onSuccess: (res) => {
      setWarehouseInfo(res);
    },
  });

  const lookCompanyList = () => {
    selectCompanyList({ list: warehouseInfo.shareCompanyVOS });
  };

  const workTitle = useMemo(() => {
    let str = '';
    warehouseInfo.wareWorkerVos.forEach((item) => {
      str += `${item.name}，`;
    });
    return str;
  }, [warehouseInfo.wareWorkerVos]);

  useEffect(() => {
    run({ id });
  }, [id, run]);
  return (
    <Drawer
      onClose={onClose}
      {...props}
      title="仓库详情"
      extra={
        !(
          warehouseInfo.belongCompanyId && warehouseInfo.belongCompanyId !== warehouseInfo.companyId
        ) ? (
          <div
            role="button"
            tabIndex={0}
            className={styles.editBtn}
            onClick={() => {
              if (!testPerm('AV_001_007_002')) {
                return;
              }
              onEdit(warehouseInfo.id);
              onClose?.();
            }}
          >
            编辑
          </div>
        ) : null
      }
    >
      <div className={styles.card}>
        <div className={styles.title}>仓库名称</div>
        <div className={styles.textarea}>{warehouseInfo.warehouseName}</div>
        <div className={classNames(styles.cell)}>
          <span className={styles.label}>仓库负责人</span>
          <span className={styles.value}>{warehouseInfo.headName || '未填写'}</span>
        </div>
        {!warehouseInfo.warehouseBelongType && (
          <div className={classNames(styles.cell)}>
            <span className={styles.label}>企业协作人</span>
            <span className={styles.value} title={workTitle}>
              {warehouseInfo.wareWorkerVos.length
                ? warehouseInfo.wareWorkerVos.map((item) => (
                    <span className={styles.valueItem}>
                      <span>{item.name}</span>
                      <span className={styles.valueItemDesc}>，</span>
                    </span>
                  ))
                : '未填写'}
            </span>
          </div>
        )}
        <div className={styles.cell}>
          <span className={styles.label}>仓库状态</span>
          <span className={styles.value}>已{warehouseInfo.warehouseStatus ? '关闭' : '开启'}</span>
        </div>
        {!warehouseInfo.warehouseBelongType && (
          <div className={classNames(styles.cell)}>
            <span className={styles.label}>默认仓库</span>
            <span className={styles.value}>
              已{!warehouseInfo.defaultWarehouse ? '关闭' : '开启'}
            </span>
          </div>
        )}
      </div>
      <div className={styles.card}>
        {/* <div className={styles.title}>仓库地址</div>
        {warehouseInfo.provinceId ? (
          <div className={styles.formValue}>
            {warehouseInfo.provinceName} {warehouseInfo.cityName} {warehouseInfo.region}
          </div>
        ) : (
          <div className={styles.noDataAddress}>未填写</div>
        )} */}
        <div className={styles.title}>详细地址</div>
        {warehouseInfo.warehouseAddress.length ? (
          <div className={styles.formValueAddress} title={warehouseInfo.warehouseAddress}>
            {warehouseInfo.warehouseAddress}
          </div>
        ) : (
          <div className={styles.noData}>未填写</div>
        )}
      </div>
      <div className={styles.card}>
        <div className={classNames(styles.formCell, styles.formItemCell)}>
          <span className={styles.label}>仓库公司代码</span>
          {warehouseInfo.companyCode ? (
            <div className={styles.formValueLast} title={warehouseInfo.companyCode}>
              {warehouseInfo.companyCode}
            </div>
          ) : (
            <span className={styles.value}>未填写</span>
          )}
        </div>
        <div
          className={classNames(styles.formCell, {
            [styles.formItemCell]: warehouseInfo.warehouseShareStatus,
          })}
        >
          <span className={styles.label}>共享仓库</span>
          <span className={styles.value}>
            {warehouseInfo.warehouseShareStatus ? ' 已开启' : '未开启'}
          </span>
        </div>
        {warehouseInfo.warehouseShareStatus > 0 && (
          <div className={styles.formItem}>
            <span>共享企业</span>
            <div
              role="button"
              tabIndex={0}
              className={styles.formItemNoData}
              onClick={lookCompanyList}
            >
              {warehouseInfo.shareCompanyVOS.length ? (
                <div className={styles.companyImgList}>
                  {warehouseInfo.shareCompanyVOS
                    .filter((item, index) => index < 5)
                    .map((item) => (
                      <img
                        className={styles.companyImg}
                        key={item.shareCompanyId}
                        src={
                          item.logoUrl ||
                          'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
                        }
                        alt=""
                      />
                    ))}
                  {warehouseInfo.shareCompanyVOS.length > 5 && (
                    <div className={styles.companyImgListNum}>
                      +{warehouseInfo.shareCompanyVOS.length - 5}
                    </div>
                  )}
                </div>
              ) : (
                <span className="mr-1">请选择</span>
              )}
              <Icon name="right" />
            </div>
          </div>
        )}
      </div>
    </Drawer>
  );
}

WarehouseDetail.defaultProps = {
  onClose: () => {},
};

export const warehouseDetail = (props: WarehouseDetailProps) => {
  drawerPopup(WarehouseDetail, props);
};

export default warehouseDetail;
