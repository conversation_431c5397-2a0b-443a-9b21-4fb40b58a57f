import React, { useCallback, useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Context, Icon, List, ListInstance } from '@/components';
import { Button, Popover, message, Modal, Dropdown, Menu } from 'antd';
import {
  stockAllotList,
  stockAllotConfirm,
  stockAllotDetail,
  stockAllotDelete,
  stockAllotEdit,
  stockAllotAdd,
  postPlatAllotOrderSubmit,
} from '@/apis';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';
import classNames from 'classnames';
import goodsNoStock from '@@/order-rewrite/components/goods-no-stock';
import Filter from './filter';
import AllotAdd from '../../containers/allot-add';
import OrderDetail from '../../containers/order-detail';
import styles from './index.module.less';

import ImportDetail from '../../containers/import-detail/import';
import OrderMessage from '../../components/order-message';
import orderPermission from '../../utils/order-permission';
import HomeBtnPerm from '../../containers/home-btn-perm';
import PrintPage, { PrintPageEv } from '../../components/react-to-print';

interface FilterParams {
  key?: string | null;
  orderStatus?: number | null;
  startDate?: number;
  endDate?: number;
  pageNo?: number;
  destinationWarehouseIds?: number[];
  orignWarehouseIds?: number[];
}

function Enter() {
  const navigator = useNavigate();
  const [showOrderAdd, setShowOrderAdd] = useState(false);
  const [showOrderFilter, setShowOrderFilter] = useState(false);
  const [showOrderDeatil, setShowOrderDetail] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const [orderId, setOrderId] = useState(0);
  const [ids, setIds] = useState<number[]>([]);
  const [id, setId] = useState(0);
  const refreshRef = useRef(null as unknown as ListInstance);
  const [orderStatusParams] = useSearchParams({ orderStatus: '' });
  const showSearch = orderStatusParams.get('showSearch') || '';
  const orderStatusStr = orderStatusParams.get('orderStatus');
  const [orderStatus, setOrderStatus] = useState(orderStatusStr || '');
  const [filter, setFilter] = useState<FilterParams>({
    key: null,
    // @ts-ignore
    orderStatus: orderStatusStr,
    startDate: 0,
    endDate: 0,
    pageNo: 1,
    destinationWarehouseIds: [],
    orignWarehouseIds: [],
  });
  const printPageRef = useRef(null as null | PrintPageEv);
  // eslint-disable-next-line no-shadow,consistent-return
  const batchHandle = (ids: number[], status: 0 | 1) => {
    if (ids.length === 0) return message.warning('请先选择订单');
    if (!orderPermission(13, status ? '审批' : '取消')) {
      message.error('暂无权限，请联系公司管理员开通!');
    } else {
      Modal.confirm({
        title: '提示',
        icon: '',
        okText: '确认',
        cancelText: '取消',
        centered: true,
        content: `是否批量${status ? '审批' : '取消'}`,
        onOk: () => {
          stockAllotConfirm({
            ids,
            orderStatus: status,
          })
            .then((res) => {
              const {
                isNeedInitWorkflowForm,
                processDefinitionKey,
                processDefinitionName,
                businessOrderNo,
                businessType,
              } = res;
              if (isNeedInitWorkflowForm) {
                window.open(
                  `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                    {
                      businessOrderNo,
                      businessType,
                    }
                  )}`
                );
              } else {
                message.success(`批量${status ? '审批' : '取消'}成功`);
              }
              refreshRef.current.refresh();
            })
            .catch((e) => {
              if (e.code === 5005012) {
                goodsNoStock({ list: JSON.parse(e.message) });
              } else {
                message.error(e.message);
              }
            });
        },
      });
    }
  };

  const editEntryList = (storageId: number) => {
    if (!orderPermission(13, '编辑')) {
      message.error('暂无权限，请联系公司管理员开通');
    } else {
      setId(storageId);
      setShowOrderAdd(true);
    }
  };

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <>
            <div
              role="button"
              tabIndex={0}
              className={styles.batchItem}
              onClick={() => {
                batchHandle(ids, 0);
              }}
            >
              批量取消
            </div>
            <div
              role="button"
              tabIndex={0}
              className={styles.batchItem}
              onClick={() => {
                batchHandle(ids, 1);
              }}
            >
              批量审批
            </div>
          </>
        }
      >
        <span className={styles.batchBtn}>批量操作</span>
      </Popover>
    ),
    [ids]
  );

  const isFilter = useMemo(() => {
    if (
      filter.endDate ||
      filter.startDate ||
      filter?.destinationWarehouseIds?.length ||
      filter?.orignWarehouseIds?.length
    ) {
      return true;
    }
    return false;
  }, [filter]);

  useEffect(() => {
    if (orderStatusParams.get('type') === 'add') {
      setTimeout(() => {
        setShowOrderAdd(true);
      });
    }
    if (orderStatusParams.get('id')) {
      setOrderId(Number(orderStatusParams.get('id')));
      setShowOrderDetail(true);
    }
  }, [orderStatusParams]);

  const filterList = [
    { label: '全部', value: '' },
    { label: '已审批', value: '1' },
    { label: '待审批', value: '0' },
    { label: '草稿', value: '2' },
    { label: 'OA审批中', value: '3' },
  ];

  return (
    <Context
      id="content"
      permission="AV_001_003"
      head={
        <Context.Head
          showSearch={!!showSearch}
          onSearch={(e) => {
            setFilter({
              key: e,
            });
          }}
          onFilter={() => {
            setShowOrderFilter(true);
          }}
          isFilterActive={isFilter}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              value={orderStatus}
              // @ts-ignore
              options={filterList}
              dropdownMatchSelectWidth={100}
              className={styles.selectItem}
              onChange={(e) => {
                setOrderStatus(e);
                setFilter({
                  ...filter,
                  // eslint-disable-next-line no-nested-ternary
                  orderStatus: e,
                });
              }}
            />
          }
          placeholder="业务员 | 订单编号"
          title={[
            {
              title: '仓库管理',
              to: '/stock',
            },
            '调拨单',
          ]}
          extra={
            checkPermission('AV_001_003_001') ? (
              <Dropdown
                className={styles.btn}
                overlay={
                  <Menu
                    items={[
                      {
                        key: '1',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            className={styles.importDataC}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              // setId(0);
                              // setShowOrderAdd(true);
                              navigator('/stock/create?stockType=allot');
                            }}
                          >
                            普通新建
                          </div>
                        ),
                      },
                      {
                        key: '2',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              // if (!checkPermission('AV_001_004_006')) {
                              //   message.error('暂无权限，请联系公司管理员开通');
                              // } else {
                              //   setShowImport(true);
                              // }
                              setShowImport(true);
                            }}
                          >
                            导入新建
                          </div>
                        ),
                      },
                    ]}
                  />
                }
                placement="bottom"
              >
                <Context.HeadTool>
                  <Button size="small" type="primary" onClick={() => {}}>
                    新建调拨单
                  </Button>
                </Context.HeadTool>
              </Dropdown>
            ) : null
          }
        />
      }
    >
      <List
        // @ts-ignore
        ref={refreshRef}
        footer={footer}
        onRow={(tags) => ({
          onClick: () => {
            // setOrderId(tags?.id);
            // setShowOrderDetail(true);
            navigator(`/stock/detail?stockType=allot&id=${tags.id}`);
          },
        })}
        request={stockAllotList}
        params={filter}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          selectedRowKeys: ids,
          onChange(e) {
            // @ts-ignore
            setIds(e);
          },
          getCheckboxProps: (record) => ({
            disabled: record.orderStatus === 3 || record.orderStatus === 1,
          }),
        }}
        rowKey="id"
        columns={[
          {
            title: '订单信息',
            width: '30%',
            render: (tags) => <OrderMessage item={tags} />,
          },
          {
            title: '调出仓库',
            width: '20%',
            align: 'center',
            render: (tags) => <div className={styles.companyName}>{tags.orignWarehouseName}</div>,
          },
          {
            title: '调入仓库',
            width: '20%',
            align: 'center',
            render: (tags) => (
              <div className={styles.companyName}>{tags.destinationWarehouseName}</div>
            ),
          },
          {
            title: '操作信息',
            width: '20%',
            align: 'center',
            render: (tags) => (
              <div>
                <div>{tags?.operator}</div>
                {tags.orderDate ? (
                  <div className={styles.grey}>
                    {dayjs(tags.orderDate).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                ) : null}
              </div>
            ),
          },
          {
            title: '操作',
            width: '70px',
            align: 'center',
            render: (tags) =>
              tags.orderStatus !== 1 && tags.orderStatus !== 3 ? (
                <Popover
                  placement="bottom"
                  trigger="hover"
                  content={
                    <HomeBtnPerm
                      orderTypeId={tags.businessType}
                      orderType={13}
                      status={tags.orderStatus}
                      btnChange={(name: string) => {
                        if (name === '审批' || name === '取消审批') {
                          if (tags.orderStatus === 3) {
                            message.warning('OA审批中');
                            return;
                          }
                          stockAllotConfirm({
                            orderStatus: tags?.orderStatus === 1 ? 0 : 1,
                            ids: [tags?.id],
                          })
                            .then((res) => {
                              const {
                                isNeedInitWorkflowForm,
                                processDefinitionKey,
                                processDefinitionName,
                                businessOrderNo,
                                businessType,
                              } = res;
                              if (isNeedInitWorkflowForm) {
                                window.open(
                                  `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                                    {
                                      businessOrderNo,
                                      businessType,
                                    }
                                  )}`
                                );
                              } else {
                                message.success(`${tags?.orderStatus === 1 ? '取消' : '审批'}成功`);
                              }
                              refreshRef.current.refresh();
                            })
                            .catch((e) => {
                              if (e.code === 5005012) {
                                goodsNoStock({ list: JSON.parse(e.message) });
                              } else {
                                message.error(e.message);
                              }
                            });
                        } else if (name === '提交') {
                          postPlatAllotOrderSubmit({ id: tags?.id }).then(() => {
                            message.success(`提交成功`);
                            refreshRef.current.refresh();
                          });
                        } else if (name === '删除') {
                          stockAllotDelete(tags.id).then(() => {
                            message.success('删除成功');
                            refreshRef.current.refresh();
                          });
                        } else if (name === '编辑') {
                          // editEntryList(tags.id);
                          navigator(`/stock/create?stockType=allot&id=${tags.id}`);
                        }
                      }}
                    />
                  }
                >
                  <Icon className={styles.icon} name="zu13366" />
                </Popover>
              ) : (
                <Icon className={classNames(styles.icon, styles.iconColor)} name="zu13366" />
              ),
          },
        ]}
      />
      <AllotAdd
        // @ts-ignore
        OrderCreact={id ? stockAllotEdit : stockAllotAdd}
        // @ts-ignore
        orderDeteil={stockAllotDetail}
        visible={showOrderAdd}
        orderType={13}
        id={id}
        onClose={() => {
          setShowOrderAdd(false);
        }}
        submit={() => {
          // refreshRef.current.refresh();
          setShowOrderAdd(false);
          setFilter({ ...filter, pageNo: 1 });
        }}
      />
      <Filter
        visible={showOrderFilter}
        onClose={() => {
          setShowOrderFilter(false);
        }}
        startDate={filter.startDate}
        endDate={filter.endDate}
        confirm={(e) => {
          setShowOrderFilter(false);
          setFilter({
            ...e,
            orderStatus: filter.orderStatus,
            key: filter.key,
          });
        }}
      />
      <OrderDetail
        orderDetele={stockAllotDelete}
        orderConfirm={stockAllotConfirm}
        orderSubmit={postPlatAllotOrderSubmit}
        confirm={() => {
          setShowOrderDetail(false);
          refreshRef.current.refresh();
        }}
        id={orderId}
        visible={showOrderDeatil}
        onClose={() => {
          setShowOrderDetail(false);
        }}
        // @ts-ignore
        orderDeteil={stockAllotDetail}
        orderType={13}
        editEntryList={() => {
          editEntryList(orderId);
        }}
        orderPrintPdf={() => {
          printPageRef.current?.handlePrint();
        }}
      />
      <PrintPage ref={printPageRef} />
      <ImportDetail importValue={2} visible={showImport} onClose={() => setShowImport(false)} />
    </Context>
  );
}

export default Enter;
