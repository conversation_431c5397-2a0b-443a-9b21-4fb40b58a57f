import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { DatePicker, Drawer, Icon } from '@/components';
import {
  postPlatWareHouse,
  StockWarehouseResult,
  WareListRusult,
  warehouseListAllApi,
} from '@/apis';
import { Button, Form, FormInstance, Spin, message } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useRequest } from 'ahooks';
import Warehouse from '../../containers/filter-warehouse';
import styles from './filter.module.less';

interface FilterParams {
  startDate?: number;
  endDate?: number;
  destinationWarehouseIds?: number[];
  orignWarehouseIds?: number[];
}

interface FilterPropps {
  visible: boolean;
  onClose: () => void;
  startDate?: number;
  endDate?: number;
  // eslint-disable-next-line no-unused-vars
  confirm: (arg: FilterParams) => void;
}

function Filter({
  visible,
  confirm,
  startDate,
  endDate,
  ...props
}: PropsWithChildren<FilterPropps>) {
  const [warehouses, setWarehouse] = useState<WareListRusult[]>([]);
  const [warehouseEnter, setWarehouseEnter] = useState<StockWarehouseResult[]>([]);
  const [selectInWarehouse, setSelectInWarehouse] = useState<number[]>([0]);
  const [selectOutWarehouse, setSelectOutWarehouse] = useState<number[]>([0]);
  const [selectType, setSelectType] = useState('');
  const [showWarehouse, setShowWarehouse] = useState(false);
  const [count, setCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const formRef = useRef(null as unknown as FormInstance);
  const [filterParams, setFilterParams] = useState<FilterParams>({
    startDate: 0,
    endDate: 0,
    destinationWarehouseIds: [],
    orignWarehouseIds: [],
  });
  const paramsWarehouseEnter = useRef({
    pageNo: 1,
    pageSize: 10,
  });

  const selectOutWarehouses = (id: number) => {
    const list = [...selectOutWarehouse];
    warehouses.forEach((item) => {
      if (item.id === id) {
        if (list.indexOf(id) > -1) {
          list.splice(list.indexOf(id), 1);
        } else {
          list.push(item.id);
        }
      }
    });
    setFilterParams({
      ...filterParams,
      orignWarehouseIds: id ? list.filter((item) => item) : [],
    });
    setSelectOutWarehouse(id ? list.filter((item) => item) : [0]);
  };

  const selectInWarehouses = (id: number) => {
    const list = [...selectInWarehouse];
    warehouseEnter.forEach((item) => {
      if (item.id === id) {
        if (list.indexOf(id) > -1) {
          list.splice(list.indexOf(id), 1);
        } else {
          list.push(item.id);
        }
      }
    });
    setFilterParams({
      ...filterParams,
      destinationWarehouseIds: id ? list.filter((item) => item) : [],
    });
    setSelectInWarehouse(id ? list.filter((item) => item) : [0]);
  };

  const disabledDateStart = (current: Dayjs) =>
    current &&
    current >
      dayjs(
        formRef.current.getFieldsValue().endDate
          ? formRef.current.getFieldsValue().endDate
          : '2033-08'
      ).endOf('day');

  const disabledDateEnd = (current: Dayjs) =>
    current &&
    current <
      dayjs(
        formRef.current.getFieldsValue().startDate
          ? formRef.current.getFieldsValue().startDate
          : '1999-08'
      ).subtract(0, 'days');

  const { run: runWarehouseEnter } = useRequest(warehouseListAllApi, {
    defaultParams: [paramsWarehouseEnter.current],
    manual: true,
    onSuccess: (result) => {
      setWarehouseEnter(
        [
          { id: 0, warehouseName: '全部仓库', warehouseStatus: 0, warehouseNo: '' },
          ...result.list,
        ].splice(0, 5)
      );
    },
  });

  useEffect(() => {
    if (visible) {
      setLoading(true);
      postPlatWareHouse({
        pageNo: 1,
        pageSize: 10,
      })
        .then((res) => {
          setWarehouse(
            [{ id: 0, warehouseName: '全部仓库', warehouseStatus: 0 }, ...res.list].splice(0, 5)
          );
          setCount(res.pagination.count);
          if (startDate) {
            formRef.current.setFieldsValue({
              startDate: dayjs(startDate),
            });
          }
          if (endDate) {
            formRef.current.setFieldsValue({
              endDate: dayjs(endDate),
            });
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
    runWarehouseEnter({ ...paramsWarehouseEnter.current });
  }, [visible, runWarehouseEnter]); //eslint-disable-line

  return (
    <Drawer
      title="筛选"
      className={styles.filter}
      visible={visible}
      {...props}
      footer={
        <div className={styles.footer}>
          <Button
            size="large"
            block
            className="mr-4"
            onClick={() => {
              setSelectInWarehouse([0]);
              setSelectOutWarehouse([0]);
              formRef.current.resetFields();
              setFilterParams({
                startDate: 0,
                endDate: 0,
                destinationWarehouseIds: [],
                orignWarehouseIds: [],
              });
            }}
          >
            重置
          </Button>
          <Button
            type="primary"
            size="large"
            block
            onClick={() => {
              const formInfo = formRef.current.getFieldsValue();
              if (
                dayjs(formInfo.startDate).valueOf() > dayjs(formInfo.endDate).valueOf() &&
                dayjs(formInfo.startDate).format('YYYY-MM-DD') !==
                  dayjs(formInfo.endDate).format('YYYY-MM-DD')
              )
                return message.error('开始时间不能大于结束时间');
              confirm(filterParams);
              return false;
            }}
          >
            确定
          </Button>
        </div>
      }
    >
      <Spin spinning={loading}>
        <div className={styles.card}>
          <div className={styles.title}>调出仓库</div>
          <div className={styles.list}>
            {warehouses.map((item, index) => (
              <span
                className={
                  selectOutWarehouse.includes(item.id as number) ? styles.itemActive : styles.item
                }
                role="button"
                key={item.id}
                tabIndex={index}
                onClick={() => {
                  selectOutWarehouses(item.id as number);
                }}
              >
                <span className={styles.name}>{item.warehouseName}</span>
              </span>
            ))}
            {count >= 4 ? (
              <span
                role="button"
                tabIndex={0}
                className={styles.itemActive}
                onClick={() => {
                  setSelectType('out');
                  setShowWarehouse(true);
                }}
              >
                更多仓库
                <Icon name="right" />
              </span>
            ) : null}
          </div>
        </div>
        <div className={styles.card}>
          <div className={styles.title}>调入仓库</div>
          <div className={styles.list}>
            {warehouseEnter.map((item, index) => (
              <span
                className={
                  selectInWarehouse.includes(item.id as number) ? styles.itemActive : styles.item
                }
                role="button"
                key={item.id}
                tabIndex={index}
                onClick={() => {
                  selectInWarehouses(item.id as number);
                }}
              >
                <span className={styles.name}>{item.warehouseName}</span>
              </span>
            ))}
            {count >= 4 ? (
              <span
                role="button"
                tabIndex={0}
                className={styles.itemActive}
                onClick={() => {
                  setSelectType('in');
                  setShowWarehouse(true);
                }}
              >
                更多仓库
                <Icon name="right" />
              </span>
            ) : null}
          </div>
        </div>
        <div className={styles.card}>
          <div className={styles.title}>操作时间</div>
          <div className={styles.time}>
            <Form ref={formRef}>
              <Form.Item name="startDate">
                <DatePicker
                  onChange={(e) => {
                    setFilterParams({
                      ...filterParams,
                      startDate: dayjs(e).startOf('day').valueOf() || 0,
                    });
                  }}
                  disabledDate={disabledDateStart}
                  placeholder="请选择开始时间"
                  bordered={false}
                />
              </Form.Item>

              <span className={styles.line} />
              <Form.Item name="endDate">
                <DatePicker
                  onChange={(e) => {
                    setFilterParams({
                      ...filterParams,
                      endDate: dayjs(e).startOf('day').valueOf() + 86399999 || 0,
                    });
                  }}
                  disabledDate={disabledDateEnd}
                  placeholder="请选择结束时间"
                  bordered={false}
                />
              </Form.Item>
            </Form>
          </div>
        </div>
      </Spin>
      <Warehouse
        visible={showWarehouse}
        onCloseWarehouse={() => {
          setShowWarehouse(false);
        }}
        confirm={(e) => {
          setShowWarehouse(false);
          // @ts-ignore
          // eslint-disable-next-line no-unused-expressions
          selectType === 'out' ? setSelectOutWarehouse(e) : setSelectInWarehouse(e);
          setFilterParams({
            ...filterParams,
            destinationWarehouseIds:
              selectType === 'out'
                ? filterParams.destinationWarehouseIds
                : filterParams.destinationWarehouseIds?.concat(e),
            orignWarehouseIds:
              selectType === 'out'
                ? filterParams.orignWarehouseIds?.concat(e)
                : filterParams.orignWarehouseIds,
          });
        }}
        selectWarehouse={selectType === 'out' ? selectOutWarehouse : selectInWarehouse}
        // @ts-ignore
        selectApi={selectType === 'out' ? postPlatWareHouse : warehouseListAllApi}
      />
    </Drawer>
  );
}
Filter.defaultProps = {
  startDate: 0,
  endDate: 0,
};

export default Filter;
