@import 'styles/mixins/mixins';

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  overflow: hidden;
  position: relative;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-select {
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #f3f3f3;
    }

    .ant-select-selector {
      padding: 0 !important;
    }

    .ant-upload-list {
      position: absolute;
      bottom: 50px;
      left: 50%;
      transform: translate(-50%);
    }

    .ant-select-selection-search {
      left: 0 !important;
    }
  }
}

.title {
  padding: 20px 0;
}

.label {
  color: #888b98;
  margin-bottom: 8px;
}

.down {
  margin-top: 16px;
  margin-bottom: 20px;
  border-top: 1px solid #f3f3f3;

  & .tip {
    padding: 16px 0 10px;
  }

  & .btn {
    text-align: right;
  }
}

.upload {
  width: 300px;
  height: 138px;
  margin-bottom: 20px;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  text-align: center;
  border-radius: 10px;
  border: 1px dashed rgb(177 179 190 / 50%);

  & .text {
    color: #888b98;
    font-size: 12px;
  }

  & .img {
    margin-top: 32px;
    margin-bottom: 15px;
  }
}

.progress {
  display: flex;
  width: 100%;
  justify-content: center;

  :global {
    .ant-progress {
      width: 200px;
    }

    .ant-progress-text {
      display: none;
    }
  }
}

.icon {
  position: absolute;
  top: 10px;
  right: 10px;
  cursor: pointer;
}

.size {
  color: #888b98;
  font-size: 12px;
  margin-top: 4px;
}

.footer {
  padding: 20px;
}

.importData {
  :global {
    .ant-modal-body {
      padding: 20px !important;
    }

    .ant-btn-default {
      display: none !important;
    }
  }

  & .errorList {
    max-height: 350px;
    overflow: auto;
  }

  & .errorItem {
    margin-bottom: 12px;
  }
}

.startRed {
  position: relative;

  &::after {
    content: '*';
    color: red;
    position: absolute;
    top: 14px;
    left: -6px;
  }
}
