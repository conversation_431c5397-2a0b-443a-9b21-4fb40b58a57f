import { PropsWithChildren, useEffect, useRef, useState, useMemo } from 'react';
import { Drawer, Icon } from '@/components';
import { Button, Upload, Select, Progress, message, Modal, Cascader } from 'antd';
import type { UploadProps } from 'antd';
import type { DefaultOptionType } from 'antd/es/cascader';
import {
  stockTakingExport,
  stockWarehouseList,
  StockWarehouseResult,
  stockTakingImport,
  getBaseCustomizeCategoryList,
  CustomerCategoryListResult,
} from '@/apis';
import { useRequest } from 'ahooks';
import classNames from 'classnames';

import filePng from '../../assets/imgs/file.png';
import styles from './import.module.less';

interface ImportPropps {
  visible: boolean;
  // eslint-disable-next-line no-unused-vars
  onClose: () => void;
  onImport: () => void;
}

interface fileInfoResult {
  size: number;
  name: string;
  originFileObj: any;
}

function Import({ visible, onImport, onClose, ...props }: PropsWithChildren<ImportPropps>) {
  const paramsWarehouse = useRef({ pageNo: 1, totalPages: 0, pageSize: 10, warehouseStatus: 0 });
  const [warehouses, setWarehouses] = useState<StockWarehouseResult[]>([]);
  const [warehouseNo, setWarehouseNo] = useState('');
  const [customizeCategoryCode, setCustomizeCategoryCode] = useState<number>();

  const [categoryList, setCategoryList] = useState<CustomerCategoryListResult[]>([]);

  const [fileInfo, setFileInfo] = useState<fileInfoResult>({
    size: 0,
    name: '',
    originFileObj: null,
  });
  const [percent, setPercent] = useState(0);

  const { run: runWarehouse, loading: loadingWarehouse } = useRequest(stockWarehouseList, {
    defaultParams: [paramsWarehouse.current],
    manual: true,
    onSuccess: (result) => {
      paramsWarehouse.current.pageNo += 1;
      paramsWarehouse.current.totalPages = result.pagination.total;
      setWarehouses([...warehouses, ...result.list]);
    },
  });

  const uploadProps: UploadProps = {
    multiple: true,
    maxCount: 1,
    showUploadList: false,
    beforeUpload() {},
    onChange(e) {
      // @ts-ignore
      setFileInfo(e.file);
      if (e.event) {
        const { event } = e;
        // @ts-ignore
        setPercent(Math.floor((event.loaded / event.total) * 100));
      }
    },
  };

  const fileSize = useMemo(() => {
    if (fileInfo.size / 1024 / 1024 > 1) {
      return `${(fileInfo.size / 1024 / 1024).toFixed(2)}M`;
    }
    return `${(fileInfo.size / 1024).toFixed(2)}KB`;
  }, [fileInfo.size]);

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const downTemplate = () => {
    if (!warehouseNo) return message.error('请先选择仓库');
    stockTakingExport(warehouseNo, customizeCategoryCode).then((res) => {
      const URL = window.webkitURL || window.URL;
      const url = URL.createObjectURL(res);
      urlDownload(url, `盘点单导入模板.xlsx`);
    });
    return false;
  };

  const importData = () => {
    const formData = new FormData();
    formData.append('file', fileInfo.originFileObj);
    // @ts-ignore
    stockTakingImport(formData).then((res) => {
      if (res.list && res.list.length > 0) {
        Modal.confirm({
          title: '导入错误',
          icon: '',
          okText: '确认',
          centered: true,
          className: styles.importData,
          content: (
            <div className={styles.errorList}>
              {res.list.map((item: any) => (
                <div className={styles.errorItem}>{item.errorMessage}</div>
              ))}
            </div>
          ),
          closable: true,
          onOk: () => {
            onImport();
            onClose();
          },
        });
      } else {
        message.success('数据处理中，导入结果会发送到消息列表，届时请关注');
        setFileInfo({
          size: 0,
          name: '',
          originFileObj: null,
        });
        onImport();
        onClose();
      }
    });
  };

  const onCloseDrawer = () => {
    setFileInfo({
      size: 0,
      name: '',
      originFileObj: null,
    });
    onClose();
  };

  const footer = (
    <div className={styles.footer}>
      <Button disabled={!fileInfo.originFileObj} type="primary" block onClick={importData}>
        确认
      </Button>
    </div>
  );

  useEffect(() => {
    if (visible) {
      runWarehouse(paramsWarehouse.current);
    }
  }, [runWarehouse, visible]);
  interface Option {
    value: string | number;
    label: string;
    children?: Option[];
  }
  useEffect(() => {
    if (visible) {
      getBaseCustomizeCategoryList().then((res) => {
        const data = res.list;
        setCategoryList(data);
      });
    }
  }, [visible]);
  const filter = (inputValue: string, path: DefaultOptionType[]) =>
    path.some(
      (option) => (option.label as string).toLowerCase().indexOf(inputValue.toLowerCase()) > -1
    );

  return (
    <Drawer
      title="导入"
      className={styles.filter}
      visible={visible}
      onClose={onCloseDrawer}
      {...props}
      footer={footer}
    >
      <div className={styles.card}>
        <div className={styles.title}>请选择需要盘点的仓库</div>
        <Select
          bordered={false}
          allowClear
          showSearch
          suffixIcon={<Icon name="down" size={16} />}
          placeholder="请选择仓库"
          onChange={(e) => {
            setWarehouseNo(e);
          }}
          onPopupScroll={() => {
            if (
              loadingWarehouse ||
              paramsWarehouse.current.pageNo > paramsWarehouse.current.totalPages
            )
              return;
            runWarehouse(paramsWarehouse.current);
          }}
          options={warehouses.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseNo,
          }))}
        />
        <div className={styles.title}>请选择需要盘点的分类</div>

        <Cascader
          bordered={false}
          options={categoryList.map((item) => ({
            label: item.label,
            value: item.code,
            children: item.children,
          }))}
          placeholder="请选择分类"
          showSearch={{ filter }}
          // onSearch={(value) => window.console.log(value)}
          onChange={(e) => {
            setCustomizeCategoryCode(e[1] as number);
          }}
        />
        <div className={styles.title}>注意事项</div>
        <div className={styles.label}>1、模版中表头名称不能更改，表头行不能删除</div>
        <div className={styles.label}>2、其中标*为必填项，必须填写</div>
        <div className={styles.label}>3、导入文件请勿超过20MB</div>
        <div className={styles.down}>
          <div className={styles.tip}>请按照数据模版的格式准备导入。点击下载《盘 点导入模板》</div>
          <div className={styles.btn}>
            <Button
              type="primary"
              size="small"
              onClick={() => {
                downTemplate();
              }}
            >
              下载模板
            </Button>
          </div>
        </div>
      </div>
      <div className={styles.card}>
        <div className={classNames(styles.title, styles.startRed)}>上传文件</div>
        <Upload {...uploadProps}>
          <div className={styles.upload}>
            {fileInfo.name ? (
              <Icon
                name="close-outline1"
                className={styles.icon}
                size={16}
                color="#999EB2"
                onClick={(e) => {
                  e.stopPropagation();
                  e.preventDefault();
                  setFileInfo({
                    size: 0,
                    name: '',
                    originFileObj: '',
                  });
                }}
              />
            ) : null}
            <img className={styles.img} src={filePng} alt="" />
            {!fileInfo.name ? (
              <div className={styles.text}>选择或拖拽文件到此处</div>
            ) : (
              <div>{fileInfo.name}</div>
            )}
            {percent && percent !== 100 ? (
              <div className={styles.progress}>
                <Progress percent={percent} strokeColor="#6484FE" trailColor="#F5F6FA" />
                <span>{percent}%</span>
              </div>
            ) : null}
            {fileInfo.size ? <div className={styles.size}>{fileSize}</div> : null}
          </div>
        </Upload>
      </div>
    </Drawer>
  );
}

export default Import;
