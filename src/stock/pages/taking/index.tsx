import React, { useCallback, useRef, useState, useEffect, useMemo } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Context, Icon, List, ListInstance } from '@/components';
import { Button, Popover, message, Modal, Dropdown, Menu } from 'antd';
import {
  stockTakingList,
  stockTakingAdd,
  stockTakingConfirm,
  stockTakingDetail,
  stockTakingDelete,
  stockTakingEdit,
  postPlatInventoryOrderSubmit,
} from '@/apis';
import dayjs from 'dayjs';
import { checkPermission } from '@/utils/permission';
import classNames from 'classnames';
import Filter from './filter';
import TakingAdd from '../../containers/taking-add';
import OrderDetail from '../../containers/order-detail';
import Import from './import';
import styles from './index.module.less';

import OrderMessage from '../../components/order-message';
import orderPermission from '../../utils/order-permission';
import HomeBtnPerm from '../../containers/home-btn-perm';
import PrintPage, { PrintPageEv } from '../../components/react-to-print';

interface FilterParams {
  key?: string | null;
  orderStatus?: number | null;
  startDate?: number;
  endDate?: number;
  pageNo?: number;
  warehouseIds?: number[];
  businessType?: number | null;
}

function Enter() {
  const navigator = useNavigate();
  const [showOrderAdd, setShowOrderAdd] = useState(false);
  const [showOrderFilter, setShowOrderFilter] = useState(false);
  const [showOrderDeatil, setShowOrderDetail] = useState(false);
  const [showImport, setShowImport] = useState(false);
  const [orderId, setOrderId] = useState(0);
  const [ids, setIds] = useState<number[]>([]);
  const [id, setId] = useState(0);
  const refreshRef = useRef(null as unknown as ListInstance);
  const [orderStatusParams] = useSearchParams({ orderStatus: '' });
  const showSearch = orderStatusParams.get('showSearch') || '';
  const orderStatusStr = orderStatusParams.get('orderStatus');
  const [orderStatus, setOrderStatus] = useState(orderStatusStr || '');
  const [filter, setFilter] = useState<FilterParams>({
    key: null,
    // @ts-ignore
    orderStatus: orderStatusStr,
    startDate: 0,
    endDate: 0,
    pageNo: 1,
    warehouseIds: [],
  });
  const printPageRef = useRef(null as null | PrintPageEv);
  // eslint-disable-next-line no-shadow,consistent-return
  const batchHandle = (ids: number[], status: 0 | 1) => {
    if (ids.length === 0) return message.warning('请先选择订单');
    if (!orderPermission(11, status ? '审批' : '取消')) {
      message.error('暂无权限，请联系公司管理员开通!');
    } else {
      Modal.confirm({
        title: '提示',
        icon: '',
        okText: '确认',
        cancelText: '取消',
        centered: true,
        content: `是否批量${status ? '审批' : '取消'}`,
        onOk: () => {
          stockTakingConfirm({
            ids,
            orderStatus: status,
          }).then((res) => {
            const {
              isNeedInitWorkflowForm,
              processDefinitionKey,
              processDefinitionName,
              businessOrderNo,
              businessType,
            } = res;
            if (isNeedInitWorkflowForm) {
              window.open(
                `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                  {
                    businessOrderNo,
                    businessType,
                  }
                )}`
              );
            } else {
              message.success(`批量${status ? '审批' : '取消'}成功`);
            }
            refreshRef.current.refresh();
          });
        },
      });
    }
  };

  const editEntryList = (storageId: number) => {
    if (!orderPermission(11, '编辑')) {
      message.error('暂无权限，请联系公司管理员开通');
    } else {
      setId(storageId);
      setShowOrderAdd(true);
    }
  };

  const footer = useCallback(
    () => (
      <Popover
        placement="top"
        trigger="hover"
        className={styles.popover}
        content={
          <>
            <div
              role="button"
              tabIndex={0}
              className={styles.batchItem}
              onClick={() => {
                batchHandle(ids, 0);
              }}
            >
              批量取消
            </div>
            <div
              role="button"
              tabIndex={0}
              className={styles.batchItem}
              onClick={() => {
                batchHandle(ids, 1);
              }}
            >
              批量审批
            </div>
          </>
        }
      >
        <span className={styles.batchBtn}>批量操作</span>
      </Popover>
    ),
    [ids]
  );

  const isFilter = useMemo(() => {
    if (filter.endDate || filter.startDate || filter?.warehouseIds?.length) {
      return true;
    }
    return false;
  }, [filter]);

  useEffect(() => {
    if (orderStatusParams.get('type') === 'add') {
      setTimeout(() => {
        setShowOrderAdd(true);
      });
    }
    if (orderStatusParams.get('id')) {
      setOrderId(Number(orderStatusParams.get('id')));
      setShowOrderDetail(true);
    }
  }, [orderStatusParams]);

  const filterList = [
    { label: '全部', value: '' },
    { label: '已审批', value: '1' },
    { label: '待审批', value: '0' },
    { label: '草稿', value: '2' },
    { label: 'OA审批中', value: '3' },
  ];

  return (
    <Context
      id="content"
      permission="AV_001_004"
      head={
        <Context.Head
          showSearch={!!showSearch}
          onSearch={(e) => {
            setFilter({
              key: e,
            });
          }}
          onFilter={() => {
            setShowOrderFilter(true);
          }}
          isFilterActive={isFilter}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              value={orderStatus}
              // @ts-ignore
              options={filterList}
              dropdownMatchSelectWidth={100}
              className={styles.selectItem}
              onChange={(e) => {
                setOrderStatus(e);
                setFilter({
                  ...filter,
                  // eslint-disable-next-line no-nested-ternary
                  orderStatus: e,
                });
              }}
            />
          }
          placeholder="业务员 | 订单编号"
          title={[
            {
              title: '仓库管理',
              to: '/stock',
            },
            '盘点单',
          ]}
          extra={
            checkPermission('AV_001_004_001') ? (
              <Dropdown
                className={styles.btn}
                overlay={
                  <Menu
                    items={[
                      {
                        key: '1',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            className={styles.importDataC}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              // setId(0);
                              // setShowOrderAdd(true);
                              navigator('/stock/create?stockType=taking');
                            }}
                          >
                            普通新建
                          </div>
                        ),
                      },
                      {
                        key: '2',
                        label: (
                          <div
                            role="button"
                            tabIndex={0}
                            style={{ textAlign: 'center' }}
                            onClick={() => {
                              if (!checkPermission('AV_001_004_006')) {
                                message.error('暂无权限，请联系公司管理员开通');
                              } else {
                                setShowImport(true);
                              }
                            }}
                          >
                            导入新建
                          </div>
                        ),
                      },
                    ]}
                  />
                }
                placement="bottom"
              >
                <Context.HeadTool>
                  <Button size="small" type="primary" onClick={() => {}}>
                    新建盘点单
                  </Button>
                </Context.HeadTool>
              </Dropdown>
            ) : null
          }
        />
      }
    >
      <List
        // @ts-ignore
        ref={refreshRef}
        footer={footer}
        onRow={(tags) => ({
          onClick: () => {
            // setOrderId(tags?.id);
            // setShowOrderDetail(true);
            navigator(`/stock/detail?stockType=taking&id=${tags.id}`);
          },
        })}
        request={stockTakingList}
        params={filter}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          selectedRowKeys: ids,
          onChange(e) {
            // @ts-ignore
            setIds(e);
          },
          getCheckboxProps: (record) => ({
            disabled: record.orderStatus === 3 || record.orderStatus === 1,
          }),
        }}
        rowKey="id"
        columns={[
          {
            title: '订单信息',
            width: '30%',
            render: (tags) => <OrderMessage item={tags} />,
          },
          {
            title: '盘点仓库',
            width: '30%',
            align: 'center',
            dataIndex: 'warehouseName',
          },
          // {
          //   title: '盘点结果',
          //   width: '20%',
          //   align: 'center',
          //   dataIndex: 'checkStatusStr',
          // },
          {
            title: '操作信息',
            width: '30%',
            align: 'center',
            render: (tags) => (
              <div>
                <div>{tags?.operator}</div>
                {tags.orderDate ? (
                  <div className={styles.grey}>
                    {dayjs(tags.orderDate).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                ) : null}
              </div>
            ),
          },
          {
            title: '操作',
            width: '70px',
            align: 'center',
            render: (tags) =>
              tags.orderStatus !== 1 && tags.orderStatus !== 3 ? (
                <Popover
                  placement="bottom"
                  trigger="hover"
                  content={
                    <HomeBtnPerm
                      orderTypeId={tags.businessType}
                      orderType={11}
                      status={tags.orderStatus}
                      btnChange={(name: string) => {
                        if (name === '审批' || name === '取消审批') {
                          if (tags.orderStatus === 3) {
                            message.warning('OA审批中');
                            return;
                          }
                          stockTakingConfirm({
                            orderStatus: tags?.orderStatus === 1 ? 0 : 1,
                            ids: [tags?.id],
                          }).then((res) => {
                            const {
                              isNeedInitWorkflowForm,
                              processDefinitionKey,
                              processDefinitionName,
                              businessOrderNo,
                              businessType,
                            } = res;
                            if (isNeedInitWorkflowForm) {
                              window.open(
                                `/admin/process/applyProcess?pKey=${processDefinitionKey}&name=${processDefinitionName}&backfillType=5&inputParamJsonStr=${JSON.stringify(
                                  {
                                    businessOrderNo,
                                    businessType,
                                  }
                                )}`
                              );
                            } else {
                              message.success(`${tags?.orderStatus === 1 ? '取消' : '审批'}成功`);
                            }
                            refreshRef.current.refresh();
                          });
                        } else if (name === '提交') {
                          postPlatInventoryOrderSubmit({ id: tags?.id }).then(() => {
                            message.success(`提交成功`);
                            refreshRef.current.refresh();
                          });
                        } else if (name === '删除') {
                          stockTakingDelete(tags.id).then(() => {
                            message.success('删除成功');
                            refreshRef.current.refresh();
                          });
                        } else if (name === '编辑') {
                          // editEntryList(tags.id);
                          navigator(`/stock/create?stockType=taking&id=${tags.id}`);
                        }
                      }}
                    />
                  }
                >
                  <Icon className={styles.icon} name="zu13366" />
                </Popover>
              ) : (
                <Icon className={classNames(styles.icon, styles.iconColor)} name="zu13366" />
              ),
          },
        ]}
      />
      <TakingAdd
        // @ts-ignore
        OrderCreact={id ? stockTakingEdit : stockTakingAdd}
        // @ts-ignore
        orderDeteil={stockTakingDetail}
        visible={showOrderAdd}
        orderType={11}
        id={id}
        onClose={() => {
          setShowOrderAdd(false);
        }}
        submit={() => {
          // refreshRef.current.refresh();
          setShowOrderAdd(false);
          setFilter({ ...filter, pageNo: 1 });
        }}
        importDataClick={() => {
          setShowImport(true);
        }}
      />
      <Filter
        visible={showOrderFilter}
        orderType={11}
        startDate={filter.startDate}
        endDate={filter.endDate}
        status={orderStatusParams.get('orderStatus') || null}
        onClose={() => {
          setShowOrderFilter(false);
        }}
        confirm={(e) => {
          setShowOrderFilter(false);
          setFilter({
            ...e,
            orderStatus: filter.orderStatus,
          });
        }}
      />
      <OrderDetail
        orderDetele={stockTakingDelete}
        orderConfirm={stockTakingConfirm}
        orderSubmit={postPlatInventoryOrderSubmit}
        confirm={() => {
          setShowOrderDetail(false);
          refreshRef.current.refresh();
        }}
        id={orderId}
        visible={showOrderDeatil}
        onClose={() => {
          setShowOrderDetail(false);
        }}
        // @ts-ignore
        orderDeteil={stockTakingDetail}
        orderType={11}
        editEntryList={() => {
          editEntryList(orderId);
        }}
        orderPrintPdf={() => {
          printPageRef.current?.handlePrint();
        }}
      />
      <Import
        visible={showImport}
        onClose={() => {
          setShowImport(false);
        }}
        onImport={() => {
          setShowOrderAdd(false);
          refreshRef.current.refresh();
        }}
      />
      <PrintPage ref={printPageRef} />
    </Context>
  );
}

export default Enter;
