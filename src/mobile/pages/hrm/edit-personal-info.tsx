import {
  Form,
  Input,
  Picker,
  Button,
  Popup,
  Calendar,
  DatePicker,
  ImageUploader,
  Checkbox,
  TextArea,
  Toast,
} from 'antd-mobile';
import { hrmSaveOnbordingInfo, hrmRosterDetail, hrmGetQueryReport } from '@/apis';
import { Fragment, useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';
import { Icon } from '@/components';
import date from './assets/date.png';
import addImgUrl from './assets/add.png';
import deleteImgUrl from './assets/delete.png';
import tipsImgUrl from './assets/tips.png';
import arrowImgUrl from './assets/arrow.png';
import styles from './edit-personal-info.module.less';
import selectInfo from './util';
import sendMessage from '../../utils/utils';

const formatDate = (fdate: Date | string | number, type?: string) => {
  if (!fdate) return null;
  if (type) {
    return new Date(fdate);
  }
  return dayjs(fdate).valueOf();
};
// const formateArray = (vals: string[] | string, type?: string) => {
//   const val = `${vals}`;
//   if (type && !val) return [];
//   if (!val) return null;
//   if (type) {
//     return [`${vals}`];
//   }
//   return vals?.length > 0 && vals[0];
// };
const formateArray = (vals: string[] | string, type?: string) => {
  if (type && (vals === null || !`${vals}`)) return [];
  if (!type && vals?.length === 0) return null;
  if (type) {
    return [`${vals}`];
  }
  return vals?.length > 0 && vals[0];
};
const formateImg = (val?: Record<string, string>[], urlStr?: string) => {
  if (val && val?.length === 0 && !urlStr) return [];
  if (!val) return null;
  if (urlStr) {
    return [
      {
        url: urlStr,
      },
    ];
  }
  return val?.length > 0 && val[0].url;
};

let websitePath = 'https://www.huahuabiz.com/';
if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
  websitePath = 'https://www.uoum.com.cn/';
}

function EditPersonalInfo() {
  const [form] = Form.useForm();
  const [searchParams] = useSearchParams();
  const companyId = searchParams.get('companyId'); // 215568
  const from = searchParams.get('from');
  if (from !== 'huahuabiz') {
    // eslint-disable-next-line no-restricted-globals
    location.href = websitePath;
  }
  const [formData, setFormData] = useState<Record<string, string | unknown>>({});
  const [popVisible, setPopvisible] = useState(false);
  const [checkFlag, setCheckFlag] = useState(false);
  const phone = useRef('');
  const [isAuth, setIsauth] = useState(0); // 是否认证
  const [stateType, setStateType] = useState(1);
  const selectKey = useRef('');

  const initForm = (value: any) => {
    // const value = {
    //   birthday: 1628762470000,
    //   cardEndtime: 1628762470000,
    //   firstWork: 1628762470000,
    //   imgCardF: 'https://img.huahuabiz.com/user_files/202283/1659518549645286.png',
    //   imgCardR: 'https://img.huahuabiz.com/user_files/202283/1659518549645286.png',
    //   imgEducation: 'https://img.huahuabiz.com/user_files/202283/1659518549645286.png',
    //   imgQualification: 'https://img.huahuabiz.com/user_files/202283/1659518549645286.png',
    //   imgLeave: 'https://img.huahuabiz.com/user_files/202283/1659518549645286.png',
    //   imgStaff: 'https://img.huahuabiz.com/user_files/202283/1659518549645286.png',
    //   nation: '汉族',
    //   sex: '0',
    //   maritalStatus: '0',
    //   kosekiType: '0',
    //   dormitoryInfo: {
    //     roomNumber: '301',
    //     roomType: '不带阳台单人间',
    //     checkInTime: 1628762470000,
    //   },
    //   degreeInfo: [
    //     {
    //       degreeSchool: '清华',
    //       degreeCareer: '设计',
    //       degreeTime: 1628762470000,
    //       degreeInfo: '本科',
    //     },
    //   ],
    //   urgencyInfo: [
    //     {
    //       urgencyName: '小陈',
    //       urgencyPhone: '13028832483',
    //       urgencyRelation: '其他',
    //     },
    //   ],
    //   homeInfo: [
    //     {
    //       homeName: '小李',
    //       homePhone: '13028832486',
    //       homeSex: '0',
    //       homeRelation: '其他',
    //       homeBirthday: 1628762470000,
    //     },
    //   ],
    // };
    const degreeInfo = value?.degreeInfo?.map((item: Record<string, string> | any) => ({
      ...item,
      degreeTime: formatDate(item.degreeTime, 'reverse'),
      degreeInfo: formateArray(item.degreeInfo, 'reverse'),
    }));
    const urgencyInfo = value?.urgencyInfo?.map((item: Record<string, string>) => ({
      ...item,
      urgencyRelation: formateArray(item?.urgencyRelation, 'reverse'),
    }));
    const homeInfo = value?.homeInfo?.map((item: Record<string, string | string[]> | any) => ({
      ...item,
      homeBirthday: formatDate(item.homeBirthday, 'reverse'),
      homeRelation: formateArray(item?.homeRelation, 'reverse'),
      homeSex: formateArray(item.homeSex, 'reverse'),
    }));
    // 计算司龄
    const dealSiling = (val: string | number) => {
      if (!val) return 0;
      const months = dayjs().diff(val, 'months');
      return `${parseInt(`${months / 12}`, 10)}年${months % 12}月`;
    };
    // 过滤类型
    const filterValue = (key: string, fvalue: string | number) => {
      if (!`${fvalue}` || fvalue === null) return '';
      const arr = selectInfo[key as keyof typeof selectInfo] as Record<string, unknown>[];
      const findResult = arr.find((item: Record<string, unknown>) => item.value === fvalue);
      return findResult?.label;
    };
    const formateUseDayjs = (uval: string | number) => {
      if (!uval) return '';
      return dayjs(uval).format('YYYY-MM-DD');
    };

    // eslint-disable-next-line no-console
    const param = {
      ...value,
      birthday: formatDate(value.birthday, 'reverse'),
      cardEndtime: formatDate(value.cardEndtime, 'reverse'),
      firstWork: formatDate(value.firstWork, 'reverse'),
      nation: formateArray(value?.nation, 'reverse'),
      maritalStatus: formateArray(value?.maritalStatus, 'reverse'),
      sex: formateArray(value?.sex, 'reverse'),
      kosekiType: formateArray(value?.kosekiType, 'reverse'),
      politics: formateArray(value?.politics, 'reverse'),
      imgCardF: formateImg([], value.imgCardF),
      imgCardR: formateImg([], value.imgCardR),
      imgEducation: formateImg([], value?.imgEducation),
      imgQualification: formateImg([], value?.imgQualification),
      imgLeave: formateImg([], value?.imgLeave),
      imgStaff: formateImg([], value?.imgStaff),
      roomNumber: value?.dormitoryInfo?.roomNumber,
      roomType: formateArray(value?.dormitoryInfo?.roomType, 'reverse'),
      checkInTime: formatDate(value?.dormitoryInfo?.checkInTime, 'reverse'),
      degreeInfo: degreeInfo || [],
      urgencyInfo: urgencyInfo || [],
      homeInfo: homeInfo || [],
      name: value?.name || value?.cardName,
      phone: value?.phone || phone.current,
      deptList: value?.deptList?.map((item: Record<string, string>) => item.orgName).join(','),
      entryTime: value?.entryTime && dayjs(value?.entryTime).format('YYYY-MM-DD'),
      entryTimes: dealSiling(value?.entryTime),
      planTime: value?.planTime && dayjs(value?.planTime).format('YYYY-MM-DD'),
      officeAddress: value?.officeAddress && JSON.parse(value?.officeAddress).join(''),
      staffType: filterValue('staffTypeList', value?.staffType),
      probationPeriod: filterValue('probationPeriodList', value?.probationPeriod),
      realityTime: value?.realityTime && dayjs(value?.realityTime).format('YYYY-MM-DD'),
      recruitmentBy: value?.recruitmentInfo?.recruitmentBy,
      recruitmentSource: value?.recruitmentInfo?.recruitmentSource,
      contractStartime: formateUseDayjs(value?.contractStartime),
      contractEndtime: formateUseDayjs(value?.contractEndtime),
      nowContractStartime: formateUseDayjs(value?.nowContractStartime),
      nowContractEndtime: formateUseDayjs(value?.nowContractEndtime),
    };
    form.setFieldsValue(param);
  };

  const getRosterDetail = () => {
    hrmRosterDetail({ companyId: companyId as string, phone: phone.current }).then((res) => {
      if (res) {
        setIsauth(res.isAuth);
        setFormData(res);
        initForm(res);
      }
    });
  };
  const getQueryReport = () => {
    hrmGetQueryReport({ companyId: companyId as string, phone: phone.current }).then((res) => {
      if (res) {
        const { whetherToSubmit, state } = res;
        if (state) {
          setStateType(4);
          return;
        }
        if (!whetherToSubmit) {
          setStateType(1);
        } else {
          setStateType(2);
        }
        getRosterDetail();
      }
    });
  };
  useEffect(() => {
    sendMessage('getUserInfo', (el) => {
      setHeader('satype', 'IOS');
      setToken(el.authorization);
      form.setFieldsValue({ phone: el.phone as string });
      localStorage.setItem(
        'USERINFO',
        JSON.stringify({
          avatar: el.avatar,
          company: el.companyName,
        })
      );
      phone.current = el.phone as string;
      getQueryReport();
    });
    // phone.current = '17674017602';
    // getQueryReport();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const formFinish = (value: any) => {
    const degreeInfo = value.degreeInfo?.map((item: Record<string, string>) => ({
      ...item,
      degreeTime: formatDate(item.degreeTime),
      degreeInfo: item.degreeInfo?.[0] || '',
    }));
    const dormitoryInfo = {
      roomNumber: value.roomNumber,
      roomType: formateArray(value.roomType),
      checkInTime: value.checkInTime && dayjs(value.checkInTime).valueOf(),
    };
    const urgencyInfo = value.urgencyInfo.map((item: Record<string, string>) => ({
      ...item,
      urgencyRelation: item.urgencyRelation?.length && item.urgencyRelation[0],
    }));
    const homeInfo = value.homeInfo?.map((item: Record<string, string | string[]>) => ({
      ...item,
      homeBirthday: formatDate(item.homeBirthday as string),
      homeRelation: (item.homeRelation?.length && item.homeRelation[0]) || '',
      homeSex: formateArray(item.homeSex as string[]),
    }));
    const param = {
      ...value,
      cardName: formData.cardName || formData.name,
      birthday: formatDate(value.birthday),
      cardEndtime: formatDate(value.cardEndtime),
      firstWork: formatDate(value.firstWork),
      degreeInfo,
      dormitoryInfo,
      urgencyInfo,
      homeInfo,
      maritalStatus: formateArray(value.maritalStatus),
      nation: formateArray(value.nation),
      sex: formateArray(value.sex),
      kosekiType: formateArray(value.kosekiType),
      politics: formateArray(value.politics),
      imgCardF: value.imgCardF?.length ? formateImg(value.imgCardF) : null,
      imgCardR: value.imgCardR?.length ? formateImg(value.imgCardR) : null,
      imgEducation: value.imgEducation?.length ? formateImg(value.imgEducation) : null,
      imgQualification: value.imgQualification?.length ? formateImg(value.imgQualification) : null,
      imgLeave: value.imgLeave?.length ? formateImg(value.imgLeave) : null,
      imgStaff: value.imgStaff?.length ? formateImg(value.imgStaff) : null,
      companyId,
      id: formData.id || 0,
    };
    hrmSaveOnbordingInfo(param).then((res: any) => {
      if (res) {
        setStateType(2);
      }
    });
  };
  const calendarChange = (val: Date | null) => {
    const value = val ? dayjs(val) : dayjs();
    const temp = {
      [selectKey.current]: value.format('YYYY-MM-DD'),
    };
    setFormData({
      ...formData,
      ...temp,
    });
    setPopvisible(false);
  };
  const timeSelects = (val?: string) => (
    <div className={styles.timeSelect}>
      <span className={val ? styles.tips : styles.tipsoff}>{val || '请选择'}</span>
      <img className={styles.img} src={date} alt="" />
    </div>
  );
  const pickSelect = (val?: string) => (
    <div className={styles.pickSelect}>
      <span className={val ? styles.tips : styles.tipsoff}>{val || '请选择'}</span>
    </div>
  );
  const formAdd = (val: string) => (
    <div role="button" tabIndex={0} className={styles.formAdd}>
      <img className={styles.formAddImg} src={addImgUrl} alt="" />
      {val}
    </div>
  );
  const handleUpload = (type: string) => {
    sendMessage('getPhoto', { maxCount: 1 }, (el: Record<string, unknown | any>) => {
      if (el?.url) {
        // form.setFieldsValue({ type: [{ url: el.url }] });
        form.setFieldsValue({ [type]: [{ url: el.url }] });
      } else {
        Toast.show({
          icon: 'fail',
          content: '上传失败',
        });
      }
    });
  };

  const handleAuth = () => {
    sendMessage(
      'authentication',
      {
        isAuth: formData.isAuth,
        name: formData.name,
        idCard: formData.idCard,
      },
      (val: Record<string, string>) => {
        if (val.success) getRosterDetail();
      }
    );
  };

  const back = () => {
    sendMessage('back', {
      isClose: true,
    });
  };

  const checkMobile = (_: any, value: string) => {
    const reg = /^1\d{10}$/;
    if (reg.test(value)) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('手机号不符合规则!'));
  };

  const handleAlter = () => {
    setStateType(1);
  };

  return (
    <div className={styles.personalWrapper}>
      <div className={styles.title}>
        <Icon name="left" size={28} color="#333333" className={styles.titleLeft} onClick={back} />
        编辑个人档案
      </div>
      {stateType === 1 && (
        <>
          <span className={styles.heaserTips}>请如实填写以下信息，如有疑问，请联系公司HR</span>
          <Form
            form={form}
            onFinish={formFinish}
            onFinishFailed={() => {
              Toast.show({
                content: '请填写必填字段!',
              });
            }}
            footer={
              <Button
                disabled={!checkFlag}
                color="primary"
                className={styles.fButton}
                type="submit"
              >
                提交
              </Button>
            }
            layout="horizontal"
          >
            <div className={styles.formItem}>
              <div className={`${styles.headerTitle} ${styles.borderBottom}`}>个人信息</div>
              <div
                role="button"
                tabIndex={0}
                className={styles.auth}
                onClick={() => {
                  handleAuth();
                }}
              >
                <div className={styles.authTop}>
                  <span className={styles.authTopLabel}>实名认证</span>
                  <span>
                    <span className={styles.authTopTag}>{isAuth ? '已认证' : '未认证'}</span>
                    <img className={styles.authTopImg} src={arrowImgUrl} alt="" />
                  </span>
                </div>
                <div className={styles.authTips}>通过人脸比对验证身份</div>
              </div>
              <Form.Item name="name" label="姓名" rules={[{ required: true }]}>
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item name="phone" label="手机号" rules={[{ required: true }]}>
                <Input placeholder="请输入" disabled maxLength={11} />
              </Form.Item>
              <Form.Item
                name="sex"
                label="性别"
                trigger="onConfirm"
                rules={[{ required: true }]}
                onClick={(_e, pickerRef) => {
                  pickerRef.current.open();
                }}
              >
                <Picker columns={[selectInfo.sexList]}>
                  {(pvalues) =>
                    pvalues.length > 0 ? pickSelect(pvalues[0]?.label as string) : pickSelect()
                  }
                </Picker>
              </Form.Item>
              <Form.Item name="stageName" label="企业昵称" rules={[{ required: false }]}>
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item name="email" label="个人邮箱" rules={[{ required: false }]}>
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item
                className={styles.datePickerItem}
                name="birthday"
                label="出生日期"
                trigger="onConfirm"
                onClick={(e, daterRef) => {
                  daterRef.current.open();
                }}
                rules={[{ required: true }]}
              >
                <DatePicker min={dayjs().year(1900).toDate()} max={dayjs().year(2050).toDate()}>
                  {(dvalue) =>
                    dvalue ? timeSelects(dayjs(dvalue).format('YYYY-MM-DD')) : timeSelects()
                  }
                </DatePicker>
              </Form.Item>
              <Form.Item
                name="maritalStatus"
                label="婚姻状况"
                rules={[{ required: true }]}
                trigger="onConfirm"
                onClick={(e, pickerRef) => {
                  pickerRef.current.open();
                }}
              >
                <Picker columns={selectInfo.maritalStatusList}>
                  {(pvalues) =>
                    pvalues.length > 0 ? pickSelect(pvalues[0]?.label as string) : pickSelect()
                  }
                </Picker>
              </Form.Item>
              <Form.Item
                name="nation"
                label="民族"
                trigger="onConfirm"
                onClick={(e, pickerRef) => {
                  pickerRef.current.open();
                }}
                rules={[{ required: true }]}
              >
                <Picker columns={selectInfo.nationList}>
                  {(pvalues) => pickSelect(pvalues[0]?.label as string)}
                </Picker>
              </Form.Item>
              <Form.Item
                name="kosekiType"
                onClick={(e, pickerRef) => {
                  pickerRef.current.open();
                }}
                label="户籍类型"
                trigger="onConfirm"
                rules={[{ required: true }]}
              >
                <Picker columns={selectInfo.kosekiTypeList}>
                  {(pvalues) => pickSelect(pvalues[0]?.label as string)}
                </Picker>
              </Form.Item>
              <Form.Item name="cardName" label="身份证姓名" rules={[{ required: true }]}>
                <Input placeholder="请输入" disabled={!!isAuth} maxLength={18} />
              </Form.Item>
              <Form.Item name="idCard" label="证件号码" rules={[{ required: true }]}>
                <Input placeholder="请输入" disabled={!!isAuth} maxLength={18} />
              </Form.Item>
              <Form.Item
                name="cardEndtime"
                className={styles.datePickerItem}
                onClick={(e, daterRef) => {
                  daterRef.current.open();
                }}
                trigger="onConfirm"
                label="证件有效期"
                rules={[{ required: true }]}
              >
                <DatePicker min={dayjs().year(1900).toDate()} max={dayjs().year(2050).toDate()}>
                  {(dvalue) =>
                    dvalue ? timeSelects(dayjs(dvalue).format('YYYY-MM-DD')) : timeSelects()
                  }
                </DatePicker>
              </Form.Item>
              <Form.Item
                name="cardAddress"
                layout="vertical"
                label="身份证地址"
                rules={[{ required: true }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item
                name="address"
                layout="vertical"
                label="现居住地址"
                rules={[{ required: true }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item
                name="politics"
                onClick={(e, pickerRef) => {
                  pickerRef.current.open();
                }}
                label="政治面貌"
                trigger="onConfirm"
                rules={[{ required: true }]}
              >
                <Picker columns={[selectInfo.politicsList]}>
                  {(pvalues) => pickSelect(pvalues[0]?.label as string)}
                </Picker>
              </Form.Item>
              <Form.Item name="social" label="个人社保账号" rules={[{ required: false }]}>
                <Input type="number" placeholder="请输入" />
              </Form.Item>
              <Form.Item name="providentFund" label="个人公积金账号" rules={[{ required: false }]}>
                <Input type="number" placeholder="请输入" />
              </Form.Item>
              <Form.Item
                trigger="onConfirm"
                name="roomType"
                onClick={(e, pickerRef) => {
                  pickerRef.current.open();
                }}
                label="宿舍类型"
                rules={[{ required: false }]}
              >
                <Picker columns={[selectInfo.roomTypeList]}>
                  {(pvalues) =>
                    pvalues.length > 0 ? pickSelect(pvalues[0]?.label as string) : pickSelect()
                  }
                </Picker>
              </Form.Item>
              <Form.Item name="roomNumber" label="宿舍房间号" rules={[{ required: false }]}>
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item
                name="checkInTime"
                className={styles.datePickerItem}
                trigger="onConfirm"
                label="入住日期"
                rules={[{ required: false }]}
                onClick={(e, daterRef) => {
                  daterRef.current.open();
                }}
              >
                <DatePicker min={dayjs().year(1900).toDate()} max={dayjs().year(2050).toDate()}>
                  {(dvalue) =>
                    dvalue ? timeSelects(dayjs(dvalue).format('YYYY-MM-DD')) : timeSelects()
                  }
                </DatePicker>
              </Form.Item>
              <Form.Item
                name="firstWork"
                label="首次参加工作时间"
                className={styles.datePickerItem}
                trigger="onConfirm"
                rules={[{ required: false }]}
                onClick={(e, daterRef) => {
                  daterRef.current.open();
                }}
              >
                <DatePicker min={dayjs().year(1900).toDate()} max={dayjs().year(2050).toDate()}>
                  {(dvalue) =>
                    dvalue ? timeSelects(dayjs(dvalue).format('YYYY-MM-DD')) : timeSelects()
                  }
                </DatePicker>
              </Form.Item>
            </div>
            <div className={styles.formItem}>
              <div className={styles.headerTitle}>工作信息</div>
              <Form.Item
                name="deptList"
                layout="vertical"
                label="部门"
                rules={[{ required: false }]}
              >
                <TextArea placeholder="请输入内容" disabled autoSize={{ minRows: 1, maxRows: 5 }} />
              </Form.Item>
              <Form.Item name="mainDeptName" label="主部门" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item
                name="custodianName"
                layout="vertical"
                label="直属主管"
                rules={[{ required: false }]}
              >
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="position" label="职位" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="wkno" label="工号" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="extPhone" label="分机号" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="officeAddress" label="办公地点" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="entryTime" label="入职时间" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="entryTime" label="司龄(系统计算)" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="staffType" label="员工类型" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="probationPeriod" label="试用期" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="realityTime" label="实际转正日期" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="planTime" label="计划转正日期" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="postRank" label="岗位职级" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="recruitmentBy" label="招聘专员" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="recruitmentSource" label="招聘来源" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
            </div>
            <div className={`${styles.formItem} ${styles.formCard}`}>
              <Form.Array
                initialValue={[{}]}
                name="degreeInfo"
                renderAdd={() =>
                  form.getFieldValue('degreeInfo').length >= 3 ? null : formAdd('新增学历信息')
                }
                onAdd={(operation: any) => {
                  // eslint-disable-next-line no-console
                  operation.add({});
                }}
                renderHeader={({ index }, { remove }) => (
                  <div className={`${styles.headerTitle} ${styles.borderBottom}`}>
                    <span className={styles.headerTitleSpan}>学历信息{index + 1}</span>
                    <span
                      role="button"
                      tabIndex={0}
                      onClick={() => {
                        // if (index === 0) return;
                        remove(index);
                      }}
                    >
                      <img className={styles.headerTitleImg} src={deleteImgUrl} alt="删除" />
                    </span>
                  </div>
                )}
              >
                {(fields) =>
                  fields.map(({ index }) => (
                    <Fragment key={index}>
                      <Form.Item
                        name={[index, 'degreeInfo']}
                        label="学历"
                        trigger="onConfirm"
                        rules={[{ required: true }]}
                        onClick={(e, pickerRef) => {
                          pickerRef.current.open();
                        }}
                      >
                        <Picker columns={selectInfo.degreeList}>
                          {(pvalues) =>
                            pvalues.length > 0
                              ? pickSelect(pvalues[0]?.label as string)
                              : pickSelect()
                          }
                        </Picker>
                      </Form.Item>
                      <Form.Item
                        name={[index, 'degreeSchool']}
                        label="毕业院校"
                        rules={[{ required: true }]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                      <Form.Item
                        name={[index, 'degreeCareer']}
                        label="所学专业"
                        rules={[{ required: true }]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                      <Form.Item
                        name={[index, 'degreeTime']}
                        className={styles.datePickerItem}
                        onClick={(e, daterRef) => {
                          daterRef.current.open();
                        }}
                        label="毕业时间"
                        trigger="onConfirm"
                        rules={[{ required: true }]}
                      >
                        <DatePicker
                          min={dayjs().year(1900).toDate()}
                          max={dayjs().year(2050).toDate()}
                        >
                          {(dvalue) =>
                            dvalue ? timeSelects(dayjs(dvalue).format('YYYY-MM-DD')) : timeSelects()
                          }
                        </DatePicker>
                      </Form.Item>
                    </Fragment>
                  ))
                }
              </Form.Array>
            </div>
            <div className={styles.formItem}>
              <div className={styles.headerTitle}>银行卡信息</div>
              <Form.Item name="bankNo" label="银行卡号" rules={[{ required: false }]}>
                <Input type="number" placeholder="请输入" />
              </Form.Item>
              <Form.Item name="openBank" label="开户行" rules={[{ required: false }]}>
                <Input placeholder="请输入" />
              </Form.Item>
            </div>
            <div className={styles.formItem}>
              <div className={`${styles.headerTitle} ${styles.borderBottom}`}>合同信息</div>
              <Form.Item name="contractCompany" label="合同公司" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="contractType" label="合同类型" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item
                name="contractStartime"
                label="首次合同起始日"
                rules={[{ required: false }]}
              >
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item
                name="contractEndtime"
                label="首次合同到期日"
                rules={[{ required: false }]}
              >
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item
                name="nowContractStartime"
                label="现合同起始日"
                rules={[{ required: false }]}
              >
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item
                name="nowContractEndtime"
                label="现合同到期日"
                rules={[{ required: false }]}
              >
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="contractTime" label="合同期限" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
              <Form.Item name="renew" label="续签次数" rules={[{ required: false }]}>
                <Input placeholder="请输入" disabled />
              </Form.Item>
            </div>
            <div className={`${styles.formItem} ${styles.formCard}`}>
              <Form.Array
                initialValue={[{}]}
                name="urgencyInfo"
                renderAdd={() =>
                  form.getFieldValue('urgencyInfo').length >= 3 ? null : formAdd('新增紧急联系人')
                }
                renderHeader={({ index }, { remove }) => (
                  <div className={`${styles.headerTitle} ${styles.borderBottom}`}>
                    <span className={styles.headerTitleSpan}>紧急联系人{index + 1}</span>
                    <span
                      role="button"
                      tabIndex={0}
                      onClick={() => {
                        // if (index === 0) return;
                        remove(index);
                      }}
                    >
                      <img className={styles.headerTitleImg} src={deleteImgUrl} alt="删除" />
                    </span>
                  </div>
                )}
              >
                {(fields) =>
                  fields.map(({ index }) => (
                    <Fragment key={index}>
                      <Form.Item
                        name={[index, 'urgencyName']}
                        label="联系人姓名"
                        rules={[{ required: true }]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                      <Form.Item
                        name={[index, 'urgencyPhone']}
                        label="联系人电话"
                        validateTrigger="onBlur"
                        rules={[{ required: true }, { validator: checkMobile }]}
                      >
                        <Input maxLength={11} placeholder="请输入" />
                      </Form.Item>
                      <Form.Item
                        name={[index, 'urgencyRelation']}
                        label="联系人关系"
                        trigger="onConfirm"
                        rules={[{ required: true }]}
                        onClick={(e, pickerRef) => {
                          pickerRef.current.open();
                        }}
                      >
                        <Picker columns={[selectInfo.urgencyList]}>
                          {(pvalues) =>
                            pvalues.length > 0
                              ? pickSelect(pvalues[0]?.label as string)
                              : pickSelect()
                          }
                        </Picker>
                      </Form.Item>
                    </Fragment>
                  ))
                }
              </Form.Array>
            </div>
            <div className={`${styles.formItem} ${styles.formCard}`}>
              <Form.Array
                name="homeInfo"
                initialValue={[{}]}
                renderAdd={() =>
                  form.getFieldValue('homeInfo').length >= 3 ? null : formAdd('新增家庭信息')
                }
                renderHeader={({ index }, { remove }) => (
                  <div className={`${styles.headerTitle} ${styles.borderBottom}`}>
                    <span className={styles.headerTitleSpan}>家庭信息{index + 1}</span>
                    <span
                      role="button"
                      tabIndex={0}
                      onClick={() => {
                        remove(index);
                      }}
                    >
                      <img className={styles.headerTitleImg} src={deleteImgUrl} alt="删除" />
                    </span>
                  </div>
                )}
              >
                {(fields) =>
                  fields.map(({ index }) => (
                    <Fragment key={index}>
                      <Form.Item
                        name={[index, 'homeName']}
                        label="姓名(家人)"
                        rules={[{ required: false }]}
                      >
                        <Input placeholder="请输入" />
                      </Form.Item>
                      <Form.Item
                        name={[index, 'homePhone']}
                        label="电话(家人)"
                        validateTrigger="onBlur"
                        rules={[{ required: false }]}
                      >
                        <Input placeholder="请输入" maxLength={11} />
                      </Form.Item>
                      <Form.Item
                        name={[index, 'homeSex']}
                        label="性别"
                        trigger="onConfirm"
                        rules={[{ required: false }]}
                        onClick={(e, pickerRef) => {
                          pickerRef.current.open();
                        }}
                      >
                        <Picker columns={[selectInfo.sexList]}>
                          {(pvalues) =>
                            pvalues.length > 0
                              ? pickSelect(pvalues[0]?.label as string)
                              : pickSelect()
                          }
                        </Picker>
                      </Form.Item>
                      <Form.Item
                        name={[index, 'homeRelation']}
                        label="关系(家人)"
                        trigger="onConfirm"
                        rules={[{ required: false }]}
                        onClick={(e, pickerRef) => {
                          pickerRef.current.open();
                        }}
                      >
                        <Picker columns={[selectInfo.urgencyList]}>
                          {(pvalues) =>
                            pvalues.length > 0
                              ? pickSelect(pvalues[0]?.label as string)
                              : pickSelect()
                          }
                        </Picker>
                      </Form.Item>
                      <Form.Item
                        name={[index, 'homeBirthday']}
                        label="生日(家人)"
                        trigger="onConfirm"
                        className={styles.datePickerItem}
                        onClick={(e, daterRef) => {
                          daterRef.current.open();
                        }}
                        rules={[{ required: false }]}
                      >
                        <DatePicker
                          min={dayjs().year(1900).toDate()}
                          max={dayjs().year(2050).toDate()}
                        >
                          {(dvalue) =>
                            dvalue ? timeSelects(dayjs(dvalue).format('YYYY-MM-DD')) : timeSelects()
                          }
                        </DatePicker>
                      </Form.Item>
                    </Fragment>
                  ))
                }
              </Form.Array>
            </div>
            <div className={styles.formItem}>
              <div className={styles.headerTitle}>
                <div>材料附件</div>
                <div className={styles.headerTitle_tips}>
                  图片仅支持上传：JPG、PNG格式，大小不超过10M
                </div>
              </div>
              <div className={styles.uploadItem}>
                <Form.Item
                  name="imgCardF"
                  layout="vertical"
                  className={styles.picItem}
                  label="身份证照片(人像面)"
                  rules={[{ required: true }]}
                  onClick={() => {
                    handleUpload('imgCardF');
                  }}
                >
                  <ImageUploader
                    disableUpload
                    maxCount={1}
                    accept="image/jpeg,image/jpg,image/png"
                    upload={() =>
                      Promise.resolve({
                        url: 'https://img.huahuabiz.com/user_files/202288/1659937765232295.jpg',
                      })
                    }
                  />
                </Form.Item>
              </div>
              <div className={styles.uploadItem}>
                <Form.Item
                  name="imgCardR"
                  layout="vertical"
                  trigger="onChange"
                  className={styles.picItem}
                  label="身份证照片(国徽像)"
                  rules={[{ required: true }]}
                  onClick={() => {
                    handleUpload('imgCardR');
                  }}
                >
                  <ImageUploader
                    disableUpload
                    maxCount={1}
                    accept="image/jpeg,image/jpg,image/png"
                    upload={() =>
                      Promise.resolve({
                        url: 'https://img.huahuabiz.com/user_files/202288/1659937765232295.jpg',
                      })
                    }
                  />
                </Form.Item>
              </div>
              <div className={styles.uploadItem}>
                <Form.Item
                  name="imgEducation"
                  layout="vertical"
                  className={styles.picItem}
                  label="学历证书"
                  rules={[{ required: false }]}
                  onClick={() => {
                    handleUpload('imgEducation');
                  }}
                >
                  <ImageUploader
                    maxCount={1}
                    disableUpload
                    accept="image/jpeg,image/jpg,image/png"
                    upload={() =>
                      Promise.resolve({
                        url: 'https://img.huahuabiz.com/user_files/202288/1659937765232295.jpg',
                      })
                    }
                  />
                </Form.Item>
              </div>
              <div className={styles.uploadItem}>
                <Form.Item
                  name="imgQualification"
                  layout="vertical"
                  className={styles.picItem}
                  label="学位证书"
                  rules={[{ required: false }]}
                  onClick={() => {
                    handleUpload('imgQualification');
                  }}
                >
                  <ImageUploader
                    disableUpload
                    maxCount={1}
                    accept="image/jpeg,image/jpg,image/png"
                    upload={() =>
                      Promise.resolve({
                        url: 'https://img.huahuabiz.com/user_files/202288/1659937765232295.jpg',
                      })
                    }
                  />
                </Form.Item>
              </div>
              <div className={styles.uploadItem}>
                <Form.Item
                  name="imgLeave"
                  layout="vertical"
                  className={styles.picItem}
                  label="前公司离职证明"
                  rules={[{ required: false }]}
                  onClick={() => {
                    handleUpload('imgLeave');
                  }}
                >
                  <ImageUploader
                    maxCount={1}
                    disableUpload
                    accept="image/jpeg,image/jpg,image/png"
                    upload={() =>
                      Promise.resolve({
                        url: 'https://img.huahuabiz.com/user_files/202288/1659937765232295.jpg',
                      })
                    }
                  />
                </Form.Item>
              </div>
              <div className={styles.uploadItem}>
                <Form.Item
                  name="imgStaff"
                  layout="vertical"
                  className={styles.picItem}
                  label="员工照片"
                  rules={[{ required: true }]}
                  onClick={() => {
                    handleUpload('imgStaff');
                  }}
                >
                  <ImageUploader
                    disableUpload
                    maxCount={1}
                    accept="image/jpeg,image/jpg,image/png"
                    upload={() =>
                      Promise.resolve({
                        url: 'https://img.huahuabiz.com/user_files/202288/1659937765232295.jpg',
                      })
                    }
                  />
                </Form.Item>
              </div>
            </div>
            <div className={styles.rule}>
              <Checkbox
                checked={checkFlag}
                onChange={(val) => {
                  setCheckFlag(val);
                }}
                className={styles.ruleRadio}
              />
              <div className={styles.ruleContent}>
                本人承诺本表中所填信息全部真实有效。若有虚假，接受按照公司规章制度执行处理，包括且不限于与本人解除劳动关系。
              </div>
            </div>
          </Form>
        </>
      )}
      {stateType === 2 && (
        <div className={styles.tipsWrapper}>
          <img className={styles.tipsImg} src={tipsImgUrl} alt="" />
          <span className={styles.tipsS1}>已提交个人档案</span>
          <Button
            onClick={() => {
              handleAlter();
            }}
            className={styles.tipsButton}
          >
            修改信息
          </Button>
        </div>
      )}
      {stateType === 4 && (
        <div className={styles.tipsWrapper}>
          <img className={styles.tipsImg} src={tipsImgUrl} alt="" />
          <span className={styles.tipsS1}>已从当前企业离职，无需填写</span>
        </div>
      )}

      <Popup
        visible={popVisible}
        destroyOnClose
        onMaskClick={() => {
          setPopvisible(false);
        }}
        bodyStyle={{ height: '40vh' }}
      >
        <Calendar defaultValue={new Date()} selectionMode="single" onChange={calendarChange} />
      </Popup>
    </div>
  );
}

export default EditPersonalInfo;
