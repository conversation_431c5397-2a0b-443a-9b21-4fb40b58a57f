export default {
  staffTypeList: [
    {
      value: 0,
      label: '请选择',
    },
    {
      value: 1,
      label: '全职',
    },
    {
      value: 2,
      label: '兼职',
    },
    {
      value: 3,
      label: '实习',
    },
    {
      value: 4,
      label: '劳务派遣',
    },
    {
      value: 5,
      label: '退休返聘',
    },
    {
      value: 6,
      label: '劳务外包',
    },
    {
      value: 7,
      label: '其他',
    },
  ],
  staffStateList: [
    {
      value: null,
      label: '请选择',
    },
    {
      value: 0,
      label: '试用',
    },
    {
      value: 1,
      label: '正式',
    },
  ],
  probationPeriodList: [
    {
      value: 0,
      label: '无试用期',
    },
    {
      value: 1,
      label: '1个月',
    },
    {
      value: 2,
      label: '2个月',
    },
    {
      value: 3,
      label: '3个月',
    },
    {
      value: 4,
      label: '4个月',
    },
    {
      value: 5,
      label: '5个月',
    },
    {
      value: 6,
      label: '6个月',
    },
  ],
  sexList: [
    {
      value: '0',
      label: '男',
    },
    {
      value: '1',
      label: '女',
    },
    {
      value: '2',
      label: '保密',
    },
  ],
  nationList: [
    [
      {
        label: '汉族',
        value: '汉族',
      },
      {
        label: '满族',
        value: '满族',
      },
      {
        label: '蒙古族',
        value: '蒙古族',
      },
      {
        label: '回族',
        value: ' 回族',
      },
      {
        label: '藏族',
        value: '藏族',
      },
      {
        label: '维吾尔族',
        value: '维吾尔族',
      },
      {
        label: '彝族',
        value: '彝族',
      },
      {
        label: '壮族',
        value: '壮族',
      },
      {
        label: '布依族',
        value: '布依族',
      },
      {
        label: '侗族',
        value: '侗族',
      },
      {
        label: '瑶族',
        value: '瑶族',
      },
      {
        label: '白族',
        value: '白族',
      },
      {
        label: '土家族',
        value: '土家族',
      },
      {
        label: '哈尼族',
        value: '哈尼族',
      },
      {
        label: '哈萨克族',
        value: '哈萨克族',
      },
      {
        label: '傣族',
        value: '傣族',
      },
      {
        label: '黎族',
        value: '黎族',
      },
      {
        label: '傈僳族',
        value: '傈僳族',
      },
      {
        label: '佤族',
        value: '佤族',
      },
      {
        label: '畲族',
        value: '畲族',
      },
      {
        label: '高山族',
        value: '高山族',
      },
      {
        label: '拉祜族',
        value: '拉祜族',
      },
      {
        label: '水族',
        value: '水族',
      },
      {
        label: '东乡族',
        value: '东乡族',
      },
      {
        label: '纳西族',
        value: '纳西族',
      },
      {
        label: '景颇族',
        value: '景颇族',
      },
      {
        label: '柯尔克孜族',
        value: '柯尔克孜族',
      },
      {
        label: '土族',
        value: '土族',
      },
      {
        label: '达斡尔族',
        value: '达斡尔族',
      },
      {
        label: '仫佬族',
        value: '仫佬族',
      },
      {
        label: '羌族',
        value: '羌族',
      },
      {
        label: '布朗族',
        value: '布朗族',
      },
      {
        label: '撒拉族',
        value: '撒拉族',
      },
      {
        label: '毛南族',
        value: '毛南族',
      },
      {
        label: '仡佬族',
        value: '仡佬族',
      },
      {
        label: '锡伯族',
        value: '锡伯族',
      },
      {
        label: '阿昌族',
        value: '阿昌族',
      },
      {
        label: '普米族',
        value: '普米族',
      },
      {
        label: '朝鲜族',
        value: '朝鲜族',
      },
      {
        label: '塔吉克族',
        value: '塔吉克族',
      },
      {
        label: '怒族',
        value: '怒族',
      },
      {
        label: '乌孜别克族',
        value: '乌孜别克族',
      },
      {
        label: '俄罗斯族',
        value: '俄罗斯族',
      },
      {
        label: '鄂温克族',
        value: '鄂温克族',
      },
      {
        label: '德昂族',
        value: '德昂族',
      },
      {
        label: '保安族',
        value: '保安族',
      },
      {
        label: '裕固族',
        value: '裕固族',
      },
      {
        label: '京族',
        value: '京族',
      },
      {
        label: '塔塔尔族',
        value: '塔塔尔族',
      },
      {
        label: '独龙族',
        value: '独龙族',
      },
      {
        label: '鄂伦春族',
        value: '鄂伦春族',
      },
      {
        label: '赫哲族',
        value: '赫哲族',
      },
      {
        label: '门巴族',
        value: '门巴族',
      },
      {
        label: '珞巴族',
        value: '珞巴族',
      },
      {
        label: '基诺族',
        value: '基诺族',
      },
    ],
  ],
  maritalStatusList: [
    [
      {
        value: '0',
        label: '未婚',
      },
      {
        value: '1',
        label: '已婚',
      },
    ],
  ],
  kosekiTypeList: [
    [
      {
        value: '本地城镇',
        label: '本地城镇',
      },
      {
        value: '本地农村',
        label: '本地农村',
      },
      {
        value: '外地城镇（省内）',
        label: '外地城镇（省内）',
      },
      {
        value: '外地农村（省内）',
        label: '外地农村（省内）',
      },
      {
        value: '外地城镇（省外）',
        label: '外地城镇（省外）',
      },
      {
        value: '外地农村（省外）',
        label: '外地农村（省外）',
      },
    ],
  ],
  politicsList: [
    {
      value: '团员',
      label: '团员',
    },
    {
      value: '党员',
      label: '党员',
    },
    {
      value: '群众',
      label: '群众',
    },
    {
      value: '其他',
      label: '其他',
    },
  ],

  degreeList: [
    [
      {
        value: '小学',
        label: '小学',
      },
      {
        value: '初中',
        label: '初中',
      },
      {
        value: '高中',
        label: '高中',
      },
      {
        value: '中专',
        label: '中专',
      },
      {
        value: '大专',
        label: '大专',
      },
      {
        value: '本科',
        label: '本科',
      },
      {
        value: '硕士',
        label: '硕士',
      },
      {
        value: '博士',
        label: '博士',
      },
      {
        value: '其他',
        label: '其他',
      },
    ],
  ],
  contractList: [
    '固定期限合同',
    '无固定期限合同',
    '实习协议',
    '劳务协议',
    '劳务派遣合同',
    '返聘协议',
    '短期劳动合同',
    '其他',
  ],
  contractTimeList: ['无', '6个月', '12个月', '24个月', '36个月及以上'],
  urgencyList: [
    {
      label: '父母',
      value: '父母',
    },
    {
      label: '子女',
      value: '子女',
    },
    {
      label: '配偶',
      value: '配偶',
    },
    {
      label: '其他',
      value: '其他',
    },
  ],
  homeList: [
    {
      label: '父母',
      value: '父母',
    },
    {
      label: '子女',
      value: '子女',
    },
    {
      label: '配偶',
      value: '配偶',
    },
    {
      label: '其他',
      value: '其他',
    },
  ],
  roomTypeList: [
    {
      label: '带阳台单人间',
      value: '带阳台单人间',
    },
    {
      label: '不带阳台单人间',
      value: '不带阳台单人间',
    },
    {
      label: '4人间(上床下桌)',
      value: '4人间(上床下桌)',
    },
    {
      label: '6人间(上下铺)',
      value: '6人间(上下铺)',
    },
  ],
  recruitmentSource: [
    '智联',
    '前程无忧',
    '拉钩',
    'Boss直聘',
    '猎聘',
    '员工推荐',
    '校园招聘',
    '其他',
  ],
};
