interface SelectValueProp {
  list: Record<string, unknown>[];
  value: number | string;
}

function SelectValue(props: SelectValueProp) {
  const { list, value } = props;
  const flag =
    typeof value === 'number' || (typeof value === 'string' && (value as string).length > 0);
  const detail = () => {
    const find: any = list.find((item) => item.value === value);
    return <span>{find.label}</span>;
  };
  return flag ? detail() : <span>请选择</span>;
}
export default SelectValue;
