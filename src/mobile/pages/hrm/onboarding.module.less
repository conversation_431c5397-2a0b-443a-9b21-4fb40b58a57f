.onboardingWrapper {
  width: 100%;
  min-height: 100%;
  padding: 0 16px;
  background-color: #f5f6fa;
  // 修改antd样式
  :global {
    .adm-list-body {
      background-color: transparent;
    }

    .adm-input-element {
      font-size: 16px;
    }

    .adm-form-item-horizontal {
      .adm-input-element {
        text-align: right;
      }
    }

    .adm-form-item-label {
      color: #040919;
      font-size: 16px !important;
      font-weight: 600;
      // white-space: nowrap;
    }

    .adm-input-element {
      font-size: 16px;
    }

    .adm-form-item-child-inner {
      text-align: right;
    }

    .adm-list-card {
      margin: 0;
      background-color: #fff;
    }

    .adm-checkbox .adm-checkbox-icon {
      width: 18px;
      height: 18px;
    }

    .adm-list-item-content-arrow {
      color: #999eb2;
      font-size: 14px;
    }

    .adm-list-item-description {
      text-align: right;
    }

    .adm-list-header {
      padding: 0;
    }

    a.adm-list-item:active {
      background-color: #fff !important;
    }

    .adm-list-default .adm-list-body {
      border: none;
    }
  }
}

.title {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
  height: 44px;
  line-height: 44px;
  position: sticky;
  top: 0;
  z-index: 99;
  text-align: center;
  background-color: #f5f6fa;
}

.heaserTips {
  color: #888b98;
  font-size: 14px;
  display: inline-block;
  margin-top: 8px;
  margin-bottom: 12px;
}

.titleLeft {
  position: absolute;
  left: -16px;
}

.formItem {
  width: 100%;
  // background-color: #fff;
  border-radius: 12px;
  // padding: 0 16px;
  margin-bottom: 16px;
  overflow: hidden;
}

.formCard {
  :global {
    .adm-list:last-child {
      margin-bottom: 0;
    }

    .adm-list:nth-child(4) {
      display: none;
    }

    .adm-list {
      border-radius: 12px;
      margin-bottom: 16px;
      overflow: hidden;
    }
  }
}

.headerTitle {
  font-size: 16px;
  font-weight: 600;
  width: 100%;
  padding: 20px 16px;
  position: relative;
  background-color: #fff;

  .headerTitleImg {
    width: 18px;
    height: 16px;
    position: absolute;
    top: 50%;
    right: 18px;
    transform: translateY(-50%);
  }

  .headerTitleSpan {
    color: #040919;
  }
}

.headerTitle_tips {
  color: #888b98;
  font-size: 14px;
  font-weight: 400;
  margin-top: 14px;
}

.timeSelect {
  display: flex;
  width: 122px;
  height: 32px;
  padding: 0 8px;
  float: right;
  position: relative;
  border: 1px solid #b1b3be;
  border-radius: 6px;
  flex-direction: row;
  align-items: center;

  .tips {
    color: #040919;
    font-size: 14px;
  }

  .tipsoff {
    color: #ccc;
    font-size: 14px;
  }

  .img {
    width: 16px;
    height: 16px;
    position: absolute;
    top: 50%;
    right: 8px;
    transform: translateY(-50%);
  }
}

.pickSelect {
  .tipsoff {
    color: #ccc;
    font-size: 16px;
  }

  .tips {
    color: #040919;
    font-size: 16px;
  }
}

.formAdd {
  color: #6484fe;
  font-size: 16px;
  display: flex;
  width: 100%;
  height: 30px;
  justify-content: center;
  flex-direction: row;
  align-items: center;
  // border: 1px solid #6484fe;
  border-radius: 12px;
  border: none;
  outline: none;

  .formAddImg {
    width: 18px;
    height: 18px;
    margin-right: 4px;
    position: relative;
  }
}

.uploadItemTitle {
  color: #040919;
  font-size: 16px;
  width: 100%;
  height: 54px;
  position: relative;
}

.rule {
  position: relative;

  .ruleRadio {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 50;
  }

  .ruleContent {
    color: #888b98;
    font-size: 14px;
    padding-left: 26px;
    position: relative;
  }
}

.fButton {
  width: 100%;
  border-radius: 22px;
}

.datePickerItem,
.picItem {
  :global {
    .adm-list-item-content-arrow {
      display: none;
    }
  }
}

.borderBottom {
  border-bottom: 1px solid #f5f5f5;
}

.auth {
  display: flex;
  width: 100%;
  padding: 20px 16px;
  position: relative;
  flex-direction: column;
  background-color: #fff;
  border: none;
  outline: none;

  .authTop {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 14px;
    justify-content: space-between;
  }

  .authTopLabel {
    color: #040919;
    font-size: 16px;
    font-weight: 600;
  }

  .authTopTag {
    color: #ec4f4a;
    font-size: 10px;
    height: 17px;
    line-height: 17px;
    padding: 2px 6px;
    position: relative;
    text-align: center;
    background-color: #fde2df;
    border-radius: 2px;
  }

  .authTopImg {
    width: 6px;
    height: 10px;
    margin-left: 10px;
  }

  .authTips {
    color: #b1b3be;
    font-size: 14px;
  }
}

.tipsWrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 157px;

  .tipsImg {
    width: 40px;
    height: 40px;
    margin-bottom: 20px;
    position: relative;
  }

  .tipsS1 {
    color: #040919;
    font-size: 16px;
  }

  .tipsButton {
    color: #fff;
    width: 164px;
    height: 44px;
    margin-top: 24px;
    position: relative;
    border-radius: 22px;
    background: #6484fe !important;
  }
}
