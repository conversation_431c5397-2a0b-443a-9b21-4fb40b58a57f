.wrap {
  height: 100vh;
  background-color: #f5f6fa;
}

.createWrap {
  padding-top: 80px;
}

.headerWrap {
  display: flex;
  width: 100%;
  height: 88px;
  padding: 22px 32px;
}

.headerIconTo {
  line-height: 44px;
}

.headerIcon {
  color: #000;
}

.headerTitle {
  color: #040919;
  font-size: 32px;
  font-weight: 500;
  width: 100%;
  line-height: 44px;
  margin-left: 50%;
  transform: translateX(-69%);
  font-family: PingFangSC, PingFangSC-Medium;
  text-align: center;
}

.desc {
  color: #040919;
  font-size: 36px;
  font-weight: 500;
  width: 100%;
  line-height: 50px;
  font-family: PingFangSC, PingFangSC-Medium;
  text-align: center;
}

.main {
  margin: 40px 40px 0;
}

.mainBox {
  display: flex;
  width: 100%;
  height: 108px;
  justify-content: space-between;
  background: #fff;
  border-radius: 24px;

  :global {
    .ant-input {
      padding: 5.9px 0 5.9px 15px;
      cursor: auto;

      &::placeholder {
        color: #888b98;
      }
    }
  }
}

.mainBox:last-child {
  margin-top: 32px;
}

.mainName {
  color: #040919;
  font-size: 32px;
  font-weight: 500;
  line-height: 108px;
  margin-left: 32px;
  font-family: PingFangSC, PingFangSC-Medium;
}

.select {
  font-size: 32px;
  width: 466px;
  line-height: 108px;
  margin-right: 24px;
  overflow: hidden;
  text-align: right;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.selectChange {
  color: #040919;
}

.selectColor {
  color: #888b98;
}

.drawer {
  :global {
    .ant-drawer-content-wrapper {
      height: 100% !important;
    }

    .ant-drawer-header {
      height: 108px !important;
      background-color: #f5f6fa !important;
    }

    .ant-drawer-content {
      border-top-left-radius: 24px !important;
      border-top-right-radius: 24px !important;
      background: #f5f6fa;
    }

    .ant-drawer-header-title {
      text-align: center;
    }

    .ant-drawer-title {
      color: #040919;
      font-size: 32px;
      font-weight: 500;
      line-height: 76px;
      font-family: PingFangSC, PingFangSC-Medium;
    }

    .ant-drawer-body {
      padding: 24px 40px !important;
    }
  }
}

.create {
  color: #fff;
  font-size: 32px;
  font-weight: 400;
  width: 686px;
  height: 88px;
  line-height: 80px;
  margin: 0 32px;
  position: fixed;
  bottom: 32px;
  background: linear-gradient(135deg, #ec6585, #f90400);
  border-radius: 44px;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: center;
}

.inputBorder {
  :global {
    .ant-input:placeholder-shown {
      font-size: 26px;
      height: 70px;
      padding-left: 65px;
      background-color: #fff;
      border-radius: 35px;
    }

    .ant-input-prefix {
      color: #999eb2;
      line-height: 70px;
      margin: -1px 0 0 26px;
      position: absolute;
      z-index: 1;
    }

    .ant-input-affix-wrapper {
      padding: 0;
    }

    .ant-input-affix-wrapper > input.ant-input {
      font-size: 26px;
      line-height: 70px;
      padding: 0 0 0 65px;
      padding-left: 61px;
      background-color: #fff;
      border-radius: 35px;
    }
  }
}

.searchIcon {
  color: #999eb2;
}

.line {
  border-bottom: 1px solid #b1b3be;
  margin-top: 14px;
}

.choose {
  color: #888b98;
  font-size: 26px;
  font-weight: 400;
  line-height: 40px;
  margin-top: 24px;
  position: relative;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
}

.choosed {
  margin-top: 16px;
}

.redDots,
.whiteDots {
  width: 15px;
  height: 15px;
  margin-top: 5px;
  margin-left: 16px;
  border-radius: 50%;
}

.redDots {
  background-color: #f90400;
}

.whiteDots {
  border: 1px solid #f90400;
}

.dotName,
.dotName1 {
  display: flex;
}

.dotName1 {
  margin-top: 40px;
  padding-bottom: 8px;
}

.choosedName,
.chooseSecond {
  color: #000;
  font-size: 32px;
  font-weight: 400;
  line-height: 26px;
  margin-left: 15px;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
}

.chooseSecond {
  color: #f90400;
}

.lineSecond {
  height: 50px;
  margin-top: -6px;
  margin-left: 23px;
  position: absolute;
  border-left: 1px solid #f90400;
}

.firstName {
  color: #040919;
  font-size: 32px;
  font-weight: 400;
  line-height: 44px;
  padding: 20px 0;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
}
