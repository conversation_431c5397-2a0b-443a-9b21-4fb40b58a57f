import { createCompanyTeam, getCompanyCategories, GetCategoryResult } from '@/apis';
import { Icon } from '@/components';
import { Input, Drawer, Space } from 'antd';
import { useSearchParams } from 'react-router-dom';
import classNames from 'classnames';
import {
  BaseSyntheticEvent,
  MouseEventHandler,
  ReactChild,
  ReactFragment,
  ReactPortal,
  useState,
} from 'react';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';
import CreateDrawer from '../../components/create-drawer';
import styles from './create-team.module.less';

document.title = '创建团队';
let timer: number | null = null;

function CreateTeam() {
  const [change, setChange] = useState(false);
  const [changeProgramme, setChangeProgramme] = useState('请选择行业类型');
  const [visible, setVisible] = useState(false);
  const [showVisible, setShowVisible] = useState(false);
  const [chooseFirst, setChooseFirst] = useState(true);
  const [chooseSecond, setChooseSecond] = useState(true);
  const [checkedFirst, setCheckedFirst] = useState('' as string | null);
  const [checkedSecond, setCheckedSecond] = useState('' as string | null);
  const [secondData, setSecondData] = useState([] as GetCategoryResult[]);
  const [industryData, setIndustryData] = useState([] as GetCategoryResult[]);
  const [isData, setIsData] = useState([] as GetCategoryResult[]);
  const [storageSecondData, setStorageSecondData] = useState([] as GetCategoryResult[]);
  const [teamName, setTeamName] = useState('');
  const [searchType, setSearchType] = useState('');
  let isChoose;
  let isSecondChoose;
  let isFirstSecond;

  const [searchParams] = useSearchParams({ token: '', satype: '', wxPath: '' });
  const wxPath = decodeURIComponent(searchParams.get('wxPath') || '');
  const token = decodeURIComponent(searchParams.get('token') || '');
  const satype = searchParams.get('satype') || '';
  if (token) setToken(token);
  if (satype) setHeader(satype, satype);
  const backWx = (companyId: number) => {
    window.wx.miniProgram.getEnv((data: { miniprogram: boolean }) => {
      if (data.miniprogram) {
        window.wx.miniProgram.redirectTo({
          url: `${wxPath}&companyId=${companyId}`,
        });
      }
    });
  };

  const onHideCreateDrawer = () => {
    setVisible(false);
    setShowVisible(false);
    setSearchType('');
  };
  const chackProgramme = () => {
    setSearchType('');
    setVisible(true);
    const index = changeProgramme.indexOf('/');
    if (changeProgramme !== '请选择行业类型') {
      setCheckedFirst(changeProgramme.slice(0, index));
      setCheckedSecond(changeProgramme.slice(index + 1));
      setChooseFirst(false);
      setChooseSecond(false);
      industryData.forEach((item) => {
        if (item.label === changeProgramme.slice(0, index)) {
          if (item.children) {
            setSecondData(item.children);
            setStorageSecondData(item.children);
          }
        }
      });
    } else {
      getCompanyCategories().then((res) => {
        setIndustryData(res.list);
        setIsData(res.list);
      });
    }
  };
  const create = () => {
    createCompanyTeam({
      companyName: teamName,
      industry: `${checkedFirst}/${checkedSecond}`,
    }).then((res) => {
      if (wxPath) {
        backWx(res.companyId);
        return;
      }
      setShowVisible(true);
      localStorage.setItem('TAMENAME', teamName);
    });
  };
  const changeName: MouseEventHandler<HTMLDivElement> = (e) => {
    const firstName = e.currentTarget.getAttribute('data-firstname');
    if (firstName?.includes('/')) {
      setChangeProgramme(firstName);
      setVisible(false);
      setChange(true);
    } else {
      setCheckedFirst(firstName);
      setChooseFirst(false);
      industryData.forEach((item: GetCategoryResult) => {
        if (item.label === firstName) {
          const { children } = item;
          if (children) {
            setSecondData(children);
            setStorageSecondData(children);
          }
        }
      });
    }
  };
  const changeSecondName: MouseEventHandler<HTMLDivElement> = (e) => {
    const secondName = e.currentTarget.getAttribute('data-secondname');
    setChooseSecond(false);
    setChange(true);
    setCheckedSecond(secondName);
    setChangeProgramme(`${checkedFirst}/${secondName}`);
    setVisible(false);
  };
  const reCheckFirst = () => {
    setChooseFirst(true);
    setIsData(industryData);
  };
  const getName = (e: BaseSyntheticEvent) => {
    setTeamName(e.target.value);
  };

  const getType = (searchName: string) => {
    if (chooseFirst) {
      if (searchName) {
        const newArr: GetCategoryResult[] = [];
        industryData.forEach((item) => {
          if (item.label.includes(searchName)) {
            newArr.push(item);
          }
          item.children?.forEach((el) => {
            if (el.label.includes(searchName)) {
              newArr.push({
                ...el,
                label: `${item.label}/${el.label}`,
              });
            }
          });
        });
        setIsData(newArr);
      } else {
        setIsData(industryData);
      }
    }
    if (searchName) {
      const newArr: GetCategoryResult[] = [];
      secondData.forEach((item) => {
        if (item.label.includes(searchName)) {
          newArr.push(item);
        }
      });
      setSecondData(newArr);
    } else {
      setSecondData(storageSecondData);
    }

    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  const getTypeTimer = (e: BaseSyntheticEvent) => {
    const { value } = e.target;
    setSearchType(value);
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      getType(value);
    }, 500) as unknown as number;
  };

  if (chooseFirst) {
    isChoose = <div className={styles.choose}>请选择</div>;
    isFirstSecond = (
      <div>
        {isData.map(
          (item: {
            label: boolean | ReactChild | ReactFragment | ReactPortal | null | undefined;
            value: number;
          }) => (
            <div key={item.value}>
              <div
                className={styles.firstName}
                data-firstname={item.label}
                onClick={changeName}
                role="button"
                tabIndex={0}
              >
                {item.label}
              </div>
            </div>
          )
        )}
      </div>
    );
  } else {
    if (chooseSecond) {
      isSecondChoose = (
        <div className={styles.dotName1}>
          <div className={styles.whiteDots} />
          <div className={styles.chooseSecond}>请选择</div>
        </div>
      );
    } else {
      isSecondChoose = (
        <div className={styles.dotName1}>
          <div className={styles.redDots} />
          <div className={styles.choosedName}>{checkedSecond}</div>
        </div>
      );
    }
    isChoose = (
      <div>
        <div className={styles.choose}>已选行业</div>
        <div className={styles.choosed}>
          <div className={styles.dotName}>
            <div className={styles.redDots} />
            <div className={styles.choosedName} onClick={reCheckFirst} role="button" tabIndex={0}>
              {checkedFirst}
            </div>
          </div>
          <div className={styles.lineSecond} />
          {isSecondChoose}
        </div>
      </div>
    );
    isFirstSecond = (
      <div>
        {secondData &&
          secondData.map((item) => (
            <div key={item.value}>
              <div
                className={styles.firstName}
                data-secondname={item.label}
                onClick={changeSecondName}
                role="button"
                tabIndex={0}
              >
                {item.label}
              </div>
            </div>
          ))}
      </div>
    );
  }

  const openClasses = classNames(styles.select, change ? styles.selectChange : styles.selectColor);

  return (
    <main className={styles.wrap}>
      <div className={styles.createWrap}>
        <div className={styles.desc}>填写团队信息，开启高效协作</div>
        <div className={styles.main}>
          <div className={styles.mainBox}>
            <div className={styles.mainName}>团队名称</div>
            <Input
              className={styles.select}
              placeholder="请输入团队名称"
              bordered={false}
              onChange={getName}
              value={teamName}
              maxLength={50}
            />
          </div>
          <div className={styles.mainBox}>
            <div className={styles.mainName}>行业类型</div>
            <div className={openClasses} onClick={chackProgramme} tabIndex={0} role="button">
              <span>{changeProgramme}</span>
              <Icon name="right" size={36} className={styles.teamIcon} />
            </div>
          </div>
        </div>
        <div className={styles.create} onClick={create} role="button" tabIndex={0}>
          创建
        </div>
      </div>
      <Drawer
        visible={visible}
        onClose={onHideCreateDrawer}
        title="选择行业"
        placement="bottom"
        className={styles.drawer}
        closeIcon={<div />}
        extra={
          <Space>
            <Icon name="close" size={48} onClick={onHideCreateDrawer} className={styles.chaIcon} />
          </Space>
        }
      >
        <div className={styles.inputBorder}>
          <Input
            placeholder="搜索行业类型"
            bordered={false}
            prefix={<Icon name="search" size={30} className={styles.searchIcon} />}
            value={searchType}
            onChange={getTypeTimer}
          />
        </div>
        <div>{isChoose}</div>
        <div className={styles.line} />
        {isFirstSecond}
      </Drawer>
      <CreateDrawer
        visible={showVisible}
        onClose={onHideCreateDrawer}
        title="创建成功"
        isCreate={4}
        height={726}
        createTeam={undefined}
        programmeArr={[]}
        teamsArr={[]}
        payData={[]}
      />
    </main>
  );
}
export default CreateTeam;
