.wrap {
  height: 100vh;
  background-color: #f5f6fa;

  :global {
    .ant-message .anticon {
      font-size: 30px !important;
    }
  }
}

.sWrap {
  padding-top: 16px;
}

.titleWrap {
  width: 100%;
  height: 80px;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #f5f6fa;
}

.icon {
  color: #000;
  margin-top: 10px;
  margin-left: 32px;
  position: absolute;
}

.title {
  color: #040919;
  font-size: 32px;
  font-weight: 500;
  line-height: 80px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  font-family: PingFangSC, PingFangSC-Medium;
  text-align: center;
}

.selectWrap {
  background: #fff;
  border-radius: 24px;
  margin: 0 32px 32px;
}

.selectItem {
  display: flex;
  height: 110px;
  line-height: 108px;
  margin: 0 32px;
  position: relative;
  border-bottom: 2px solid #f5f6fa;

  :global {
    .ant-input {
      color: #040919;
      font-weight: 400;
      padding: 0 0 0 20px !important;
      text-align: right;
      font-family: PingFang<PERSON>, PingFangSC-Regular;
      cursor: pointer;

      &::placeholder {
        color: #b1b3be;
      }
    }

    .ant-input:last-child {
      width: 425px;
      cursor: auto;
    }

    .ant-input-affix-wrapper {
      padding: 5.9px 0 5.9px 15px;
    }
  }
}

.selectItem:last-child {
  border-bottom: none;
  height: 108px;
}

.selectName {
  color: #040919;
  font-size: 32px;
  font-weight: 500;
  width: 170px;
  font-family: PingFangSC, PingFangSC-Medium;
  text-align: left;
}

.input {
  font-size: 32px;
  width: 420px;
  line-height: 113px;
  // color: #b1b3be;
  overflow: hidden;
  text-align: right;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
}

.inputChange {
  color: #040919;
}

.inputColor {
  color: #b1b3be;
}

.teamIcon {
  color: #999eb2;
  position: absolute;
  right: 0;
}

.permission {
  margin: 0 32px 32px;
  padding: 32px;
  background-color: #fff;
  border-radius: 24px;
}

.appPermission {
  color: #040919;
  font-size: 32px;
  font-family: PingFangSC, PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  line-height: 44px;
  margin-bottom: 16px;
}

.footer {
  width: 100%;
  height: 236px;
  position: fixed;
  bottom: 0;
  background-color: #fff;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
}

.read {
  font-size: 24px;
  font-weight: 400;
  display: flex;
  height: 112px;
  line-height: 90px;
  padding: 16px 32px;
  font-family: PingFangSC, PingFangSC-Regular;

  :global {
    .adm-radio.adm-radio-checked .adm-radio-icon {
      border-color: #f90400 !important;
      background-color: #f90400 !important;
    }

    .adm-radio {
      vertical-align: 0;
    }
  }
}

.checked {
  margin-left: 16px;
}

.text {
  color: #040919;
}

.agreement {
  color: #6484fe;
  cursor: pointer;
}

.bottom {
  display: flex;
  // padding: 16px 32px 18px 48px;
  height: 124px;
  position: relative;
  border-top: 2px solid #f5f6fa;
}

.pInfo {
  display: flex;
  width: 100%;
}

.pInfo-icon {
  width: 40px;
  height: 40px;
}

.content {
  color: #888b98;
  font-size: 28px;
  font-weight: 400;
  width: calc(100% - 40px);
  text-align: left;
  word-wrap: break-word;
  word-break: break-all;
  white-space: wrap;
}

.priceName {
  font-size: 28px;
  font-weight: 400;
  line-height: 124px;
  margin-left: 48px;
  font-family: PingFangSC, PingFangSC-Regular;
}

.name {
  color: #040919;
  vertical-align: 2px;
}

.unit {
  color: #f90400;
}

.num {
  color: #f90400;
  font-size: 40px;
}

.button {
  color: #fff;
  font-size: 32px;
  font-weight: 400;
  width: 368px;
  height: 88px;
  line-height: 85px;
  margin-top: 18px;
  position: absolute;
  right: 32px;
  background: #6484fe;
  border-radius: 44px;
  font-family: PingFangSC, PingFangSC-Regular;
  border: none;
  outline: none;

  :global {
    .ant-spin {
      color: #fff;
      margin-left: 16px;
    }
  }
}

.customClass {
  font-size: 30px;
}
