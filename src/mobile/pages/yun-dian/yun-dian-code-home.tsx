import { getAppDetail } from '@/apis';
import { useEffect, useState } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';
import styles from './yun-dian-code-home.module.less';
import { getAppDetailResult } from '../../../../apis/get-app-detail';

function Mobile() {
  const [show, setShow] = useState(true);
  const [homeData, setHomeData] = useState({} as getAppDetailResult);
  const [searchParams] = useSearchParams({
    token: '',
    satype: '',
    phone: '',
    channel: '',
    inviteCode: '',
    createUser: '',
    userId: '',
  });
  let token = searchParams.get('token') || '';
  const satype = searchParams.get('satype') || '';
  const phone = searchParams.get('phone') || '';
  const channel = searchParams.get('channel') || '';
  const inviteCode = searchParams.get('inviteCode') || '';
  const createUser = searchParams.get('createUser') || '';
  const userId = searchParams.get('userId') || '';
  const params = {
    phone,
    channel,
    inviteCode,
    createUser,
    userId,
  };

  localStorage.setItem('PARAMS', JSON.stringify(params));
  token = decodeURIComponent(token);
  if (token) {
    setToken(token);
    let url = window.location.href;
    url = url.replace('token=', 'oldToken=');
    window.history.replaceState(null, '', url);
  }
  if (satype) setHeader(satype, satype);

  const toDetail = () => {
    setShow(true);
    window.scrollTo(0, 704);
  };

  const tocharge = () => {
    setShow(false);
    const top = document.getElementById('feeUrl')?.offsetTop;
    if (top) {
      window.scrollTo(0, top);
    }
  };

  useEffect(() => {
    setTimeout(() => {
      getAppDetail({ coding: 'yunDianCode' }).then((res) => {
        setHomeData(res);
      });
    }, 100);
  }, []);

  const imgList: [] | undefined = homeData.appDescList;
  const { labelList } = homeData;

  let isVideo;
  if (homeData.guideType === 1) {
    isVideo = <img className={styles.headerImg} src={homeData.guideUrl} alt="" />;
  } else {
    isVideo = (
      <video width="750" height="400" controls src={homeData.guideUrl}>
        <track default kind="captions" srcLang="en" src="/video/php/friday.vtt" />
        抱歉，您的浏览器不支持嵌入视频！
      </video>
    );
  }

  return (
    <>
      <div className={styles.headerWrap}>{isVideo}</div>
      <div className={styles.titleWrap}>
        <img className={styles.logoImg} src={homeData.appIcon} alt="" />
        <div className={styles.titleRight}>
          <div className={styles.appTitle}>{homeData.name}</div>
          <div className={styles.companyName}>{homeData.developer}</div>
          <div className={styles.label}>
            {labelList &&
              labelList.map((item: { content: string; color: string }) => (
                <span className={styles.label1} style={{ color: 'item.color' }}>
                  {item.content}
                </span>
              ))}
          </div>
          <div className={styles.titleContent}>{homeData.detail}</div>
        </div>
      </div>
      <div className={styles.detailWrap}>
        <div className={styles.changeBtn}>
          <span
            tabIndex={0}
            role="button"
            className={show ? styles.changed : styles.change}
            onClick={toDetail}
          >
            应用详情
          </span>
          <span
            tabIndex={0}
            role="button"
            className={show ? styles.change : styles.changed}
            onClick={tocharge}
          >
            收费方案
          </span>
        </div>
        {imgList && imgList.map((item) => <img className={styles.addImg} src={item} alt="" />)}
        <img id="feeUrl" className={styles.freeImg} src={homeData.feeUrl} alt="" />
      </div>
      <div className={styles.footer}>
        <a className={styles.phone} href={`tel:${homeData.phone}`}>
          <img src="https://img.huahuabiz.com/user_files/2022523/1653277375203257.png" alt="" />
          <div>联系服务商</div>
        </a>
        <Link to="/m/app-detail-confirmation">
          <button className={styles.btn} type="button">
            立即开通
          </button>
        </Link>
      </div>
    </>
  );
}

export default Mobile;
