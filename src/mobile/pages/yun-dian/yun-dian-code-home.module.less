.headerWrap {
  width: 100%;
  height: 400px;
  position: relative;
}

.headerImg {
  width: 100%;
  height: 400px;
  position: absolute;
  background-color: #fff;
}

.headerReturn {
  width: 52px;
  height: 52px;
  line-height: 52px;
  margin: 26px 0 0 32px;
  padding-left: 4px;
  position: absolute;
  opacity: 0.9;
  background: #fff;
  border-radius: 50%;
}

.titleWrap {
  display: flex;
  width: 100%;
  height: 306px;
  padding: 40px 32px;
  border-bottom: 2px solid #f5f6fa;
}

.logoImg {
  width: 220px;
  height: 220px;
  border-radius: 20px;
}

.titleRight {
  margin-left: 24px;
}

.appTitle {
  color: #040919;
  font-size: 36px;
  font-weight: 600;
  line-height: 50px;
  font-family: PingFangSC, PingFangSC-Semibold;
  text-align: left;
}

.companyName {
  color: #888b98;
  font-size: 28px;
  font-weight: 400;
  line-height: 40px;
  margin-top: 8px;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
}

.label {
  font-size: 20px;
  line-height: 36px;
  margin-top: 8px;
}

.label1 {
  margin-right: 16px;
  padding: 8px;
  background: #e0ebff;
  border-radius: 4px;
  text-align: center;
}

.label1:last-child {
  margin-right: 16px;
}
// .label2 {
//   height: 36px;
//   background: #fff2d0;
//   border-radius: 4px;
//   margin-left: 16px;
//   padding: 8px;
//   text-align: center;
// }

.titleContent {
  color: #040919;
  font-size: 24px;
  font-weight: 400;
  line-height: 34px;
  margin-top: 8px;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
}

.footer {
  display: flex;
  width: 100%;
  height: 124px;
  padding: 0 40px;
  justify-content: space-between;
  position: fixed;
  bottom: 0;
  background-color: #fff;
}

.phone {
  display: block;
  text-align: center;
  cursor: pointer;

  img {
    display: inline-block;
    width: 40px;
    height: 40px;
    margin: 25px 0 9px;
  }

  div {
    color: #888b98;
    font-size: 20px;
    font-weight: 400;
    line-height: 28px;
    font-family: PingFangSC, PingFangSC-Regular;
    text-align: left;
  }
}

.btn {
  color: #fff;
  font-size: 32px;
  font-weight: 400;
  width: 412px;
  height: 88px;
  line-height: 88px;
  margin: 18px 0 0;
  background: #6484fe;
  border-radius: 44px;
  border: none;
  outline: none;
  font-family: PingFangSC, PingFangSC-Regular;
  text-align: left;
  text-align: center;
  cursor: pointer;
}

.changeBtn {
  text-align: center;
  padding: 34px 0 24px;
  position: sticky;
  top: 0;
  background-color: #fff;

  span {
    font-size: 28px;
    margin: 0 88px;
    cursor: pointer;
  }
}

.change {
  color: #888b98;
  font-weight: 400;
  line-height: 40px;
  font-family: PingFangSC, PingFangSC-Regular;
}

.changed {
  color: #f90400;
}

.changed::after {
  content: ' ';
  width: 40px;
  height: 4px;
  margin: 46px 0 0 -75px;
  position: absolute;
  background: #f90400;
  border-radius: 4px;
}

.detailWrap {
  padding-bottom: 134px;
}

.addImg,
.freeImg {
  width: 686px;
  margin-top: 16px;
  margin-left: 32px;
}

.addImg:nth-child(2) {
  margin-top: 0;
}

.customClass {
  font-size: 30px;
}
