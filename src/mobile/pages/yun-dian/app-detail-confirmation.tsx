import { BaseSyntheticEvent, useEffect, useState } from 'react';
// import { Link } from 'react-router-dom';
import { Icon } from '@/components';
import { Checkbox, message, Spin } from 'antd';
import classNames from 'classnames';
import {
  billingOrderCreate,
  getAppDetail,
  getSkuPrice,
  joinCompanies,
  switchUserCompany,
} from '@/apis';
import { JoinCompanyResult } from '@/apis/join-companies';
import { skuPriceResultPayWayNoList } from '@/apis/get-sku-price';
import { setToken } from '@/utils/auth';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import styles from './app-detail-confirmation.module.less';
import layoutsStyles from '../../layouts/layouts.module.less';
import CreateDrawer from '../../components/create-drawer';

document.title = '开通确认';

function Confirmation() {
  // const textArr: string[] = [
  //   '以应用身份访问通讯录（历史版本）',
  //   '读取通讯录',
  //   '获取部门基础信息',
  //   '获取用户 union ID',
  //   '获取用户发给机器人的单聊消息（历史版本）',
  //   '访问审批应用',
  //   '给多个用户批量发消息',
  //   '以应用的身份发消息（历史版本）',
  //   '获取用户基本信息',
  //   '获取用户雇佣信息',
  //   '获取用户手机号',
  //   '获取部门组织架构信息',
  //   '获取用户组织架构信息',
  //   '获取用户性别',
  //   '获取用户在群聊中@机器人的消息（历史版本）',
  //   '通过手机号或邮箱获取用户 ID',
  //   '校验用户是否为应用管理员',
  //   '获取用户邮箱信息',
  // ];
  const [visible, setVisible] = useState(false);
  const [isCreate, setIsCreate] = useState(1);
  const [changeTeam, setChackTeam] = useState('请选择团队');
  const [changeProgramme, setChangeProgramme] = useState('请选择开通方案');
  const [changePaymentTitle, setChangePaymentTitle] = useState('请选择付款比例');
  const [title, setTitle] = useState('开通团队');
  const [height, setHeight] = useState(668);
  const [change, setChange] = useState(false);
  const [changeOk, setChangeOk] = useState(false);
  const [changePaymentClass, setChangePayment] = useState(false);
  const [programmeArr, setProgrammeArr] = useState([]);
  const [teamsArr, setTeamsArr] = useState([] as JoinCompanyResult[]);
  const [price, setPrice] = useState(0);
  const [effectiveDate, setEffectiveDate] = useState('请先选择方案');
  const [companyId, setCompanyId] = useState(0);
  const [memberId, setMemberId] = useState(0);
  const [shopSkuId, setShopSkuId] = useState(0);
  const [payWayNo, setPayWayNo] = useState(0);
  const [skuId, setSkuId] = useState(0);
  const [shopId, setShopId] = useState(0);
  const [payData, setPayData] = useState([] as skuPriceResultPayWayNoList[]);
  const [clickBtn, setClickBtn] = useState(true);
  const [unit, setUnit] = useState('');
  const [loading, setLoading] = useState(false);
  const [checked, setChecked] = useState(false);
  let btn;
  const antIcon = <LoadingOutlined style={{ fontSize: 24 }} spin />;
  useEffect(() => {
    const companyNameList: JoinCompanyResult[] = [];
    const tameName = localStorage.getItem('TAMENAME');
    joinCompanies().then((res) => {
      res.list.forEach((item) => {
        if (item.isAdmin) {
          companyNameList.push(item);
          setTeamsArr(companyNameList);
        }
        if (tameName) {
          setChackTeam(tameName);
          setChangeOk(true);
          if (tameName === item.companyName) {
            setCompanyId(item.companyId);
          }
        } else if (changeTeam === '请选择团队') {
          setChackTeam(companyNameList[0]?.companyName);
          setChangeOk(true);
          setCompanyId(companyNameList[0]?.companyId);
          setMemberId(companyNameList[0]?.memberId);
        }
        localStorage.removeItem('TAMENAME');
      });
    });
    getAppDetail({ coding: 'yunDianCode' }).then((res: any) => {
      if (res.goodsList) {
        setProgrammeArr(res.goodsList);
      }
      if (changeProgramme === '请选择开通方案') {
        setChangeProgramme(res.goodsList[0].name);
        setChange(true);
        setEffectiveDate(res.goodsList[0].effectiveDate);
        setShopSkuId(res.goodsList[0].shopSkuId);
        setSkuId(res.goodsList[0].skuId);
        setShopId(res.goodsList[0].shopId);
        if (res.goodsList[0].effectiveDateUnit === 1) {
          setUnit('年');
        } else if (res.goodsList[0].effectiveDateUnit === 2) {
          setUnit('个月');
        } else {
          setUnit('天');
        }
      }
    });
    if (companyId && shopSkuId) {
      getSkuPrice({
        targetCompanyId: companyId,
        shopSkuId,
      }).then((res) => {
        setPayData(res.payWayNoList);
        setPrice(res.price);
        setChangePaymentTitle(res.payWayNoList[0].title);
        setPayWayNo(res.payWayNoList[0].id);
        setChangePayment(true);
      });
    }
  }, [changeProgramme, changeTeam, companyId, shopSkuId]);

  const onHideCreateDrawer = () => {
    setVisible(false);
  };
  const chackTeam = () => {
    setVisible(true);
    setIsCreate(1);
    setTitle('开通团队');
  };
  const chackProgramme = () => {
    setVisible(true);
    setIsCreate(2);
    setTitle('开通方案');
    setHeight(668);
  };
  const handleTeam = (e: BaseSyntheticEvent) => {
    setChackTeam(e.target.innerText);
    setVisible(false);
    setChangeOk(true);
    setCompanyId(Number(e.target.dataset.companyid));
    setMemberId(Number(e.target.dataset.memberid));
  };
  const handleProgramme = (e: BaseSyntheticEvent) => {
    setChangeProgramme(e.target.innerText);
    setVisible(false);
    setChange(true);
    programmeArr.forEach(
      (item: {
        name: string;
        marketPrice: number;
        effectiveDate: number;
        shopSkuId: number;
        payWayNo: number;
        skuId: number;
        shopId: number;
        effectiveDateUnit: number;
      }) => {
        if (item.name === e.target.innerText) {
          setPrice(item.marketPrice);
          const getEffectiveDate = `${item.effectiveDate}`;
          setEffectiveDate(getEffectiveDate);
          setShopSkuId(item.shopSkuId);
          setSkuId(item.skuId);
          setShopId(item.shopId);
          if (item.effectiveDateUnit === 1) {
            setUnit('年');
          } else if (item.effectiveDateUnit === 2) {
            setUnit('个月');
          } else {
            setUnit('天');
          }
        }
      }
    );
  };
  const chackPayment = () => {
    if (changeProgramme !== '请选择开通方案') {
      setVisible(true);
      setIsCreate(5);
      setTitle('选择付款比例');
    } else {
      message.open({
        content: '请先选择开通方案',
        className: styles.customClass,
        type: 'info',
      });
    }
  };

  const changePayment = (e: EventTarget & HTMLDivElement) => {
    const newTitle = e.dataset.title || '';
    const newId = e.dataset.id ? +e.dataset.id : 0;
    if (newTitle === '选择付款比例') {
      setChangePayment(false);
    } else {
      setChangePayment(true);
    }
    setChangePaymentTitle(newTitle);
    setPayWayNo(newId);
    setVisible(false);
  };

  // eslint-disable-next-line no-unused-vars
  const isChack = (val: any) => {
    setChecked(val.target.checked);
  };
  const orderCreate = () => {
    setLoading(true);
    const params = JSON.parse(localStorage.getItem('PARAMS') || '');
    if (changeTeam === '请选择团队') {
      message.open({
        content: '请先选择团队',
        className: layoutsStyles.messageSize,
        type: 'info',
        duration: 3,
      });
      setLoading(false);
    } else if (changeProgramme === '请选择开通方案') {
      message.open({
        content: '请先选择开通方案',
        className: layoutsStyles.messageSize,
        type: 'info',
        duration: 3,
      });
      setLoading(false);
    } else if (changePaymentTitle === '请选择付款比例') {
      message.open({
        content: '请先选择付款比例',
        className: layoutsStyles.messageSize,
        type: 'info',
        duration: 3,
      });
      setLoading(false);
    } else if (!checked) {
      message.open({
        content: '请先阅读《服务商ISV应用软件许可使用与服务协议》',
        className: layoutsStyles.messageSize,
        type: 'info',
        duration: 3,
      });
      setLoading(false);
    } else {
      setClickBtn(false);
      billingOrderCreate({
        companyId,
        payWayNo,
        skuId,
        shopSkuId,
        shopId,
        mobile: Number(params.phone),
        orderSource: 3,
        memberId,
      })
        .then((res: { payNo: number; amount: number; orderStatus: number; userId: number }) => {
          const { payNo, amount, orderStatus } = res;
          switchUserCompany(companyId).then((el: any) => {
            setToken(`${el.tokenType} ${el.accessToken}`);
            window.wx.miniProgram.getEnv((data: { miniprogram: boolean }) => {
              if (data.miniprogram) {
                window.wx.miniProgram.redirectTo({
                  url: `/packageA/pages/purchase/index?channel=${params.channel}&createUser=${params.createUser}&inviteCode=${params.inviteCode}&payNo=${payNo}&userId=${params.userId}&orderStatus=${orderStatus}&amount=${amount}&isYunDian=1&tokenType=${el.tokenType}&accessToken=${el.accessToken}`,
                });
              }
            });
          });
          setClickBtn(true);
          setLoading(false);
        })
        .catch(() => {
          setClickBtn(true);
          setLoading(false);
        });
    }
  };
  if (clickBtn) {
    btn = (
      <button className={styles.button} type="button" onClick={orderCreate}>
        确认开通
        <Spin indicator={antIcon} spinning={loading} />
      </button>
    );
  } else {
    btn = (
      <button className={styles.button} type="button" disabled onClick={orderCreate}>
        确认开通
        <Spin indicator={antIcon} spinning={loading} />
      </button>
    );
  }

  useEffect(() => {
    if (!teamsArr.length) {
      setIsCreate(3);
      setTitle('开通团队');
      setHeight(522);
    } else if (teamsArr.length === 1) {
      setHeight(296);
    } else if (teamsArr.length === 2) {
      setHeight(418);
    } else if (teamsArr.length === 3) {
      setHeight(540);
    } else if (teamsArr.length === 4) {
      setHeight(662);
    } else if (teamsArr.length === 5) {
      setHeight(784);
    } else if (teamsArr.length === 6) {
      setHeight(906);
    } else if (teamsArr.length > 6) {
      setHeight(1028);
    }
  }, [teamsArr.length]);

  const openClasses = classNames(styles.input, change ? styles.inputChange : styles.inputColor);
  const changeClasses = classNames(styles.input, changeOk ? styles.inputChange : styles.inputColor);
  const paymentClasses = classNames(
    styles.input,
    changePaymentClass ? styles.inputChange : styles.inputColor
  );
  return (
    <main className={styles.wrap}>
      <div className={styles.sWrap}>
        <div className={styles.selectWrap}>
          <div className={styles.selectItem}>
            <div className={styles.selectName}>选择团队</div>
            <div className={changeClasses} onClick={chackTeam} tabIndex={0} role="button">
              <span>{changeTeam}</span>
              <Icon name="right" size={36} className={styles.teamIcon} />
            </div>
          </div>
          <div className={styles.selectItem}>
            <div className={styles.selectName}>开通方案</div>
            <div className={openClasses} onClick={chackProgramme} tabIndex={0} role="button">
              <span>{changeProgramme}</span>
              <Icon name="right" size={36} className={styles.teamIcon} />
            </div>
          </div>
          <div className={styles.selectItem}>
            <div className={styles.selectName}>有效期</div>
            <div className={styles.input}>
              {effectiveDate}
              {unit}
            </div>
          </div>
          <div className={styles.selectItem}>
            <div className={styles.selectName}>付款比例</div>
            <div className={paymentClasses} onClick={chackPayment} tabIndex={0} role="button">
              <span>{changePaymentTitle}</span>
              <Icon name="right" size={36} className={styles.teamIcon} />
            </div>
          </div>
        </div>
      </div>
      <div className={styles.footer}>
        <div className={styles.read}>
          <div>
            <Checkbox onChange={isChack} />
          </div>
          <div className={styles.checked}>
            <span className={styles.text}>我已阅读并同意</span>
            {/* <Link to=""> */}
            <span className={styles.agreement}>
              <a
                href="https://epoimages.obs.cn-south-1.myhuaweicloud.com/agreement/华华生意圈功能模块订购协议.html"
                target="_blank"
                rel="noreferrer"
                style={{ color: '#6484FE' }}
              >
                《服务商ISV应用软件许可使用与服务协议》
              </a>
            </span>
            {/* </Link> */}
          </div>
        </div>
        <div className={styles.bottom}>
          <div className={styles.priceName}>
            <span className={styles.name}>总价：</span>
            <span className={styles.unit}>￥</span>
            <span className={styles.num}>{price}</span>
          </div>
          {btn}
        </div>
      </div>
      <CreateDrawer
        visible={visible}
        onClose={onHideCreateDrawer}
        title={title}
        isCreate={isCreate}
        handleTeam={handleTeam}
        handleProgramme={handleProgramme}
        height={height}
        createTeam={undefined}
        programmeArr={programmeArr}
        teamsArr={teamsArr}
        changePayment={changePayment}
        payData={payData}
      />
    </main>
  );
}

export default Confirmation;
