/* stylelint-disable */

.wrap {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;

  :global {
    .ant-input-number .ant-input-number-input-wrap .ant-input-number-input {
      padding-right: 12px !important;
    }
  }
}

.content {
  flex: 1;
  overflow: auto;
}

.first {
  width: 100%;
  height: 250px;
  padding: 49.5px 20px 0;
  padding-top: 49.5px;
  position: sticky;
  top: 0;
  box-sizing: border-box;
  // -webkit-overflow-scrolling:;
  background-image: url(https://img.huahuabiz.com/user_files/2022723/1658547709024480.png);
}

.left {
  display: flex;
  width: 100%;
  height: 44px;
  position: fixed;
  z-index: 200;
  background: linear-gradient(180deg, #f5f6fa, rgb(255 255 255 / 70%));
}

.back {
  line-height: 44px;
  padding-left: 16px;
}

.close {
  width: 24px;
  height: 24px;
  margin-top: 9px;
  margin-left: 12px;
}

.avatar {
  width: 67px;
  height: 75px;
  margin: 0 auto 4px;
  background-image: url(https://img.huahuabiz.com/user_files/2022723/1658554610462547.png);
}

.img {
  width: 62px;
  height: 62px;
  border-radius: 50%;
  margin: 3px 0 0 3px;
}

.placeName {
  color: #040919;
  font-size: 18px;
  font-weight: 500;
  display: -webkit-box;
  width: 100%;
  line-height: 25px;
  overflow: hidden;
  text-align: center;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.adress,
.time {
  font-size: 14px;
  display: -webkit-box;
  width: 100%;
  height: 20px;
  line-height: 20px;
  overflow: hidden;
  text-align: center;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.adress {
  color: #888b98;
  margin: 6px 0 12px;
}

.time {
  color: #040919;
}

.second {
  width: 100%;
  padding: 16px 16px 96px;
  position: sticky;
  top: 250px;
  // position: relative;
  z-index: 100;
  background-color: #f5f6fa;
  // overflow-y: scroll;
}

.companyName {
  color: #040919;
  font-size: 14px;
  font-weight: 400;
  display: -webkit-box;
  height: 20px;
  line-height: 20px;
  margin-left: 4px;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.visitObject {
  width: 100%;
  margin-top: 8px;
  padding: 16px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 6%);

  :global {
    .ant-input {
      text-align: right;
      color: #040919;
      font-size: 14px;
      border: none;
      cursor: pointer;

      &::placeholder {
        color: #888b98;
        font-size: 14px;
      }
    }

    .adm-text-area-element {
      font-size: 14px;

      &::placeholder {
        color: #888b98;
        font-size: 14px;
      }
    }

    .adm-text-area-count {
      font-size: inherit;
    }
  }
}

.people {
  display: flex;
  padding-bottom: 15px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f5f6fa;
}

.peopleFoo {
  display: flex;
  padding-bottom: 15px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f5f6fa;
  padding: 8px 0;
}

.title {
  color: #040919;
  font-size: 16px;
  font-weight: 500;
  height: 22px;
  line-height: 22px;
  text-align: left;
}

.titleBaz {
  position: relative;
}

.titleFoo::before {
  content: '*';
  position: absolute;
  left: -8px;
  top: -2px;
  color: red;
}

.titleWx::before {
  content: '*';
  position: absolute;
  left: -8px;
  top: 10px;
  color: red;
}

.input {
  width: 70%;
}

.tips {
  margin-top: 20px;
}

.textarea {
  margin-top: 12px;
}

.updatepicture {
  width: 100%;
  margin-top: 15px;
  padding: 16px;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 6%);
}

.picture {
  padding-bottom: 15px;
  border-bottom: 1px solid #f5f6fa;
}

.num {
  color: #888b98;
  font-size: 14px;
  height: 20px;
}

.btn {
  color: #fff;
  font-size: 16px;
  height: 44px;
  line-height: 44px;
  margin: 16px;
  background: #6484fe;
  border-radius: 22px;
  text-align: center;
}

.btnWrap {
  width: 100%;
  height: 76px;
  z-index: 300;
  background: #f5f6fa;
}

.upload {
  display: inline-block;
  margin-top: 12px;
}

.add {
  font-size: 28px;
  width: 85px;
  height: 85px;
  margin-top: 5px;
  background-image: url(https://img.huahuabiz.com/user_files/2022727/1658908430541896.png);
  outline: none;
  user-select: none;
  background-size: 85px;
}

.imgsFlexBox {
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  margin-top: 7px;

  .imgdelBox {
    width: 85px;
    height: 85px;
    margin-top: 5px;
    margin-right: 5px;
    position: relative;

    .imgsItem {
      width: 85px;
      height: 85px;
      border-radius: 6px;
    }
  }
}

.deleteIcon {
  display: block;
  width: 16px;
  height: 16px;
  position: absolute;
  top: 3px !important;
  right: 4px;
  z-index: 10;
}
