/* eslint-disable jsx-a11y/control-has-associated-label */
import { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Input, Image, message } from 'antd';
import { TextArea } from 'antd-mobile';
import cloneDeep from 'lodash/cloneDeep';
import classNames from 'classnames';
import { Icon } from '@/components';
import { createCustomerSignInsert } from '@/apis';
import { base64ToFile } from '@/utils/file/transform';
import { generateUploadFile } from '@/utils/file';
import sendMessage from '../../utils/utils';
import styles from './crm-add-sign.module.less';

declare let wx: any;

function AddSign() {
  const navigate = useNavigate();
  const [signParams] = useSearchParams();
  const userInfo = JSON.parse(localStorage.getItem('USERINFO') || '{}');
  const customId = signParams.get('customId') || '';
  const customIdName = signParams.get('customIdName') || '';
  const address = localStorage.getItem('ADDRESS') || '';
  const allAddress = localStorage.getItem('ALLADRESS') || '';
  const height = localStorage.getItem('HEIGHT') || '';
  const [tips, setTips] = useState('');
  const [isWx, setIsWx] = useState(false);
  const [isShowBtn, setIsShowBtn] = useState(true);
  // const time = new Date(
  //   localStorage
  //     .getItem('TIME')
  //     ?.replace('年', '-')
  //     .replace('月', '-')
  //     .replace('日', '')
  //     .slice(0, -3) || ''
  // ).getTime();
  const showTime =
    localStorage.getItem('TIME')?.replace('/', '年').replace('/', '月').slice(0, -3) || '';
  const [imgs, setImgs] = useState([] as string[]);
  const [isBtn, setIsBtn] = useState(true);
  let btn;

  const changeTips = (e: string) => {
    setTips(e);
  };

  const deleteCha = (index: number) => {
    const newImgs = cloneDeep(imgs);
    newImgs.splice(index, 1);
    setImgs([...newImgs]);
  };

  const handleCamera = () => {
    if (imgs.length < 9) {
      if (isWx) {
        console.warn('wx.chooseImage before');
        wx.chooseImage({
          count: Math.min(9, 9 - imgs.length),
          sizeType: ['original', 'compressed'],
          sourceType: ['camera'],
          success(res: any) {
            console.warn('wx.chooseImage success', res);
            wx.getLocalImgData({
              localId: res.localIds[0],
              success(res1: any) {
                console.warn('wx.getLocalImgData success', res1);
                let { localData } = res1;
                if (localData.indexOf('data:image') !== 0) {
                  localData = `data:image/jpeg;base64,${localData}`;
                }
                localData = localData
                  .replace(/\r|\n/g, '')
                  .replace('data:image/jgp', 'data:image/jpeg');
                const file = base64ToFile(localData);
                console.warn('upload before', file);
                const uploadFile = generateUploadFile(file, false);
                uploadFile.upload().then(() => {
                  console.warn('upload success', uploadFile);
                  imgs.push(`${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${uploadFile.filePath}`);
                  setImgs([...imgs]);
                });
              },
            });
          },
        });
      } else {
        sendMessage('takePhoto', { address: allAddress }, (el: Record<string, string>) => {
          imgs.push(`${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${el.url}`);
          setImgs([...imgs]);
        });
      }
    }
  };
  const previewImg = (src: string) => {
    sendMessage('interfaceJump', {
      pageCode: '10210',
      param: {
        photos: [src],
        index: 0,
        isPreView: false,
      },
    });
  };

  const prompt = (text: string) => {
    message.error(text);
    setIsShowBtn(true);
  };

  const submit = () => {
    setIsShowBtn(false);
    const latitude = localStorage.getItem('LATITUDE') || '22.68873';
    const longitude = localStorage.getItem('LONGITUDE') || '114.1342';

    if (!imgs.length) {
      prompt('图片至少上传一张');
      return;
    }

    if (!customIdName.length) {
      prompt('拜访对象不能为空');
      return;
    }

    const params = {
      relatedId: Number(customId),
      relatedType: 0,
      eventTime: null,
      content: tips,
      source: 1,
      imageParams:
        imgs.length > 0
          ? imgs.map((item) => ({
              filePath: item,
            }))
          : null,
      longitude: Number(longitude),
      latitude: Number(latitude),
      address: allAddress,
    };
    if (!latitude || !longitude) {
      setIsShowBtn(true);
      message.error('未获取到位置');
    } else {
      createCustomerSignInsert(params)
        .then(() => {
          sendMessage('back', {
            toJump: true,
          });
          setIsBtn(true);
          message.success('签到成功');
          setTimeout(() => {
            wx.miniProgram.navigateBack();
          }, 1500);
        })
        .catch(() => {
          setIsShowBtn(true);
          message.error('签到失败');
        });
    }
  };
  if (isBtn) {
    if (isShowBtn) {
      btn = (
        <div className={styles.btn} onClick={submit} role="button" tabIndex={0}>
          提交
        </div>
      );
    } else {
      btn = <div className={styles.btn}>提交</div>;
    }
  } else {
    btn = <div />;
  }

  const back = () => {
    navigate(-1);
  };

  useEffect(() => {
    window.wx.miniProgram.getEnv((data: { miniprogram: boolean }) => {
      setIsWx(data.miniprogram);
    });
  }, []);

  return (
    <div className={styles.wrap}>
      <div className={styles.content}>
        <div className={styles.left} style={{ top: Number(height) }}>
          <Icon name="left" size={28} color="#333333" onClick={back} className={styles.back} />
        </div>

        <div className={styles.first}>
          <div className={styles.avatar}>
            <img className={styles.img} src={userInfo.avatar} alt="" />
          </div>
          <div className={styles.placeName}>{address}</div>
          <div className={styles.adress}>{allAddress}</div>
          <div className={styles.time}>{showTime}</div>
        </div>
        <div className={styles.second}>
          <div className={styles.companyName}>
            <span>当前组织：</span>
            <span>{userInfo.company}</span>
          </div>

          <div className={styles.visitObject}>
            <div className={classNames(styles.people, styles.titleBaz)}>
              <span className={classNames(styles.title, styles.titleFoo)}>拜访对象</span>
              <Input
                id="input"
                className={styles.input}
                maxLength={30}
                bordered={false}
                value={customIdName}
              />
            </div>
            <div className={styles.tips}>
              <span className={styles.title}>备注</span>
              <TextArea
                id="textarea"
                rows={4}
                maxLength={200}
                showCount
                placeholder="请填写签到备注"
                className={styles.textarea}
                onChange={changeTips}
                autoSize
              />
            </div>
          </div>

          <div className={styles.updatepicture}>
            <div className={classNames(styles.picture, styles.titleBaz)}>
              <span className={classNames(styles.title, styles.titleFoo)}>图片 </span>
              <span className={styles.num}>({imgs.length}/9)</span>
            </div>
            <div className={styles.imgsFlexBox}>
              {imgs.map((item, index) => (
                <div className={styles.imgdelBox} key={item}>
                  <span
                    className={styles.deleteIcon}
                    onClick={() => deleteCha(index)}
                    role="button"
                    tabIndex={0}
                  >
                    <img
                      src="https://img.huahuabiz.com/user_files/2022727/1658918538726776.png"
                      alt=""
                    />
                  </span>
                  <Image
                    className={styles.imgsItem}
                    src={item}
                    preview={false}
                    alt=""
                    onClick={() => previewImg(item)}
                  />
                </div>
              ))}
              {imgs.length >= 9 ? null : (
                <div className={styles.add} onClick={handleCamera} role="button" tabIndex={0} />
              )}
            </div>
          </div>
        </div>
      </div>
      <div className={styles.btnWrap}>{btn}</div>
      <Helmet>
        <title>签到</title>
      </Helmet>
    </div>
  );
}

export default AddSign;
