import { getCollectList } from '@/apis';
import { Context, List, ListInstance } from '@/components';
// import dayjs from 'dayjs';
import { useMemo, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { ColumnsType } from 'antd/es/table';
import { ItemType } from '@/apis/company-admin/upload-members-excel';
import { collectParams, collectResult } from '@/apis/gmall/get-collect-list';
import styles from './collection.module.less';

import SheetDetail from './containers/sheet-detail';
import getColValue from '../../purpose';

function Collection() {
  const refreshRef = useRef(null as unknown as ListInstance);
  const [searchType] = useSearchParams();
  const stateKey = Number(searchType.get('state')) || 0;
  const tenantId = searchType.get('tenantId') || '';

  const [btnItem, setBtnItem] = useState({} as collectResult);
  const [sheetDetailShow, setSheetDetailShow] = useState(false);
  const [collectParam, setCollectParam] = useState<collectParams>({
    tag: '',
    pageNo: 1,
    pageSize: 10,
    tenantId,
  });

  // const stateType = (state: number) => {
  //   switch (state) {
  //     case 1:
  //       return '商户';
  //     case 2:
  //       return '工长';
  //     case 3:
  //       return '设计师';
  //     case 4:
  //       return '装修公司';
  //     default:
  //       return '';
  //   }
  // };

  // 区分是商户管理 还是 新入驻申请
  const title = useMemo(() => {
    let name = '';

    if (stateKey === 0) {
      name = '户型诊断';
    } else if (stateKey === 1) {
      name = '免费设计';
    } else if (stateKey === 2) {
      name = '智能报价';
    } else {
      name = '智能匹配';
    }

    setCollectParam({
      ...collectParam,
      tag: name,
    });

    return [name];
  }, [searchType]); // eslint-disable-line

  // 列表字段
  const columns: ColumnsType<ItemType> = useMemo(
    () => [
      {
        title: '序号',
        width: '10%',
        align: 'center',
        render: (item, unknown, index) => (
          <div>
            {refreshRef.current.getPagination().pageSize *
              (refreshRef.current.getPagination().current - 1) +
              (index + 1)}
          </div>
        ),
      },
      {
        title: '称呼',
        width: '40%',
        align: 'center',
        render: (item) => (
          <div className={styles.shopName}>{getColValue(item, { colName: 'name' })}</div>
        ),
      },
      {
        title: '手机号',
        width: '20%',
        align: 'center',
        render: (item) => (
          <div className={styles.shopName}>{getColValue(item, { colName: 'phone' })}</div>
        ),
      },

      {
        title: '填写时间',
        width: '20%',
        align: 'center',
        render: (item) => (
          <div className={styles.shopName}>{getColValue(item, { colName: 'createTime' })}</div>
        ),
      },
    ],
    [] // eslint-disable-line
  );

  return (
    <Context head={<Context.Head placeholder="搜索领用人员 公司 订单编号" title={title} />}>
      <List
        // @ts-ignore
        ref={refreshRef}
        onRow={(res) => ({
          onClick: () => {
            setBtnItem(res);
            setSheetDetailShow(true);
          },
        })}
        request={getCollectList}
        params={collectParam}
        rowKey="id"
        columns={columns}
      />
      <SheetDetail
        visible={sheetDetailShow}
        btnItem={btnItem}
        onClose={() => {
          setSheetDetailShow(false);
        }}
      />
    </Context>
  );
}

export default Collection;
