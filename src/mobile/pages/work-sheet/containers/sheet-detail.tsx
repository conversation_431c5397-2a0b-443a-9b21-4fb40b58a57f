import { PropsWithChildren, useEffect, useRef, useState } from 'react';
import { Drawer } from '@/components';
import { Form, FormInstance, Spin, Image } from 'antd';
import {
  FormTemplateComponentsResult,
  getFormTemplateComponents,
  getProvinceCityCounty,
} from '@/apis';
import { collectResult } from '@/apis/gmall/get-collect-list';
import getColValue from '@@/mobile/purpose';
import style from './sheet-detail.module.less';

interface Details {
  visible: boolean;
  btnItem: collectResult;
  onClose: () => void;
}

interface cityResult {
  label: string;
  pinyin: string;
  value: number;
  children: cityResult[];
}
const urlRef = /^(https?:)?\/\//;

function SheetDetail({ visible, btnItem, onClose, ...props }: PropsWithChildren<Details>) {
  const formRef = useRef(null as unknown as FormInstance);
  const [cityList, setCityList] = useState<cityResult[]>([]);
  const [formList, setFormList] = useState<FormTemplateComponentsResult>();
  const [loading, setLoading] = useState(false);

  // 初始化数据
  const initData = () => {
    setLoading(true);
    Promise.all([
      getFormTemplateComponents({ formCode: btnItem.formCode }).then((res) => {
        setFormList(res);
      }),
      getProvinceCityCounty().then((res) => {
        setCityList(res.list);
      }),
    ]).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    if (visible) {
      initData();
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      title="详情"
      visible={visible}
      {...props}
      className={style.filter}
      onClose={() => {
        onClose();
      }}
    >
      <Spin spinning={loading}>
        <div className={style.formCard}>
          <div className={style.fixForm}>
            <Form layout="horizontal" ref={formRef}>
              {formList?.components.map((item) => (
                <Form.Item label="" name={item.colName}>
                  <div>{item.attr.label as string}</div>
                  {getColValue(btnItem, item).includes('test') ||
                  getColValue(btnItem, item).includes('user_files') ? (
                    <div className={style.text}>
                      {getColValue(btnItem, item)
                        .split('、')
                        .map((img: string, index: number) => (
                          <Image
                            className={style.customImg}
                            key={img}
                            style={
                              !((index + 1) % 3) ? { marginRight: '0' } : { marginRight: '19px' }
                            }
                            src={
                              urlRef.test(img)
                                ? img
                                : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${img}`
                            }
                            alt=""
                          />
                        ))}
                    </div>
                  ) : (
                    <div className={style.text}>
                      {item.colName === 'area'
                        ? getColValue(btnItem, item, cityList)
                        : getColValue(btnItem, item)}
                    </div>
                  )}
                </Form.Item>
              ))}
            </Form>
          </div>
        </div>
      </Spin>
    </Drawer>
  );
}

export default SheetDetail;
