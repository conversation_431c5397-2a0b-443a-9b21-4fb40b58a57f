import Icon from '@/components/icon';
import { getQueryString } from '@/utils/utils';
import style from './head.module.less';

function Head({ title, back }: { title: string; back: () => void }) {
  const headHeight = `${getQueryString('headHeight') || '44'}px`;

  return (
    <div
      className={style.head}
      style={{
        height: headHeight,
        lineHeight: headHeight,
      }}
    >
      <Icon name="left" className={style.leftIcon} size={24} onClick={back} />
      <span className={style.title}>{title}</span>
    </div>
  );
}
export default Head;
