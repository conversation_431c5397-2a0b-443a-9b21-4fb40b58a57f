:global .ant-cascader-dropdown {
  max-width: 100%;
}

:global .ant-select-item-option-content {
  & > div {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

:global {
  .ant-drawer {
    .ant-drawer-content-wrapper {
      .ant-drawer-content {
        background-color: #f5f6fa;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;

        .ant-drawer-body {
          padding: 0 16px;
        }
      }
    }
  }
}

:global {
  .ant-input-textarea.ant-input-textarea-show-count.ant-input-textarea-in-form-item {
    margin-bottom: 18px;
  }

  .ant-form-item {
    flex-direction: column;
  }
}

.formCard {
  display: flex;
  padding-bottom: 70px;
  justify-content: space-between;
  flex-direction: column;

  .img {
    padding: 16px;
  }

  .imgPhoto {
    border-radius: 16px;
  }

  .title {
    color: #040919;
    font-size: 17px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-left: 16px;
  }

  :global .ant-select-selection-item {
    & > div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  :global .ant-form-item-explain-error {
    font-size: 12px;
  }

  :global .ant-form-item-with-help {
    margin-bottom: 24px;
  }

  .fixForm {
    padding-left: 16px;

    :global .ant-form-item {
      display: flex !important;
      margin-bottom: 12px !important;
      justify-content: space-between !important;
    }

    :global .ant-form-item-label {
      flex: none !important;
      font-size: 17px;
      font-weight: 600;
    }

    :global .ant-form-item-control {
      flex: 1 !important;
    }

    :global .ant-form-item-required::before {
      margin-right: 0 !important;
    }

    :global .ant-input-textarea {
      padding-right: 16px !important;
    }

    :global .ant-form-item-explain {
      opacity: 0;
    }

    &.submited {
      :global .ant-form-item-explain {
        opacity: 1;
      }
    }
  }

  .formRender {
    :global .ant-form-item-control {
      margin-top: -5px;
      margin-left: -2px;
    }

    :global [role='button'] {
      width: 90px;
      height: 90px;
      margin-left: 16px;

      div {
        width: 100%;
        height: 100%;
        line-height: 90px;
      }

      .anticon-plus {
        font-size: 30px;
        vertical-align: middle;
      }
    }

    :global .ant-image {
      margin-left: 10px;
    }

    :global .ant-select-multiple .ant-select-selection-placeholder {
      margin-left: -2px;
    }

    // :global .ant-select-selector{
    //   margin-left: -3px;
    // }
  }
}

.submitBox {
  height: 76px;
  padding-top: 19px;
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background: #f5f6fa;
}

.submitBtn {
  color: #fff;
  height: 38px;
  line-height: 38px;
  margin: 0 16px;
  border-radius: 10px;
  background: #ff5923;
  text-align: center;
}

.drawerBox {
  .drawerTitleBox {
    display: flex;
    height: 54px;
    justify-content: space-between;
    align-items: center;

    .drawerTitle {
      font-size: 17px;
      font-weight: bolder;
    }

    .drawerIcon {
      font-size: 17px;
    }
  }

  .drawerDescribe {
    font-size: 18px;
    font-weight: bolder;
    margin-top: 36px;
    text-align: center;
  }
}

.drawerForm {
  margin-top: 18px;

  .formBox {
    display: flex;
    height: 56px;
    margin-bottom: 20px;
    padding-left: 16px;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-radius: 12px;

    .formLabel {
      font-size: 15px;
      font-weight: 600;
    }

    .formValue {
      margin-top: 25px;

      :global .ant-input {
        text-align: right;
      }

      :global .ant-input:placeholder-shown {
        text-align: right;
      }

      :global
        .ant-select-status-error.ant-select:not(.ant-select-disabled, .ant-select-customize-input)
        .ant-select-selector {
        border: 1px solid #fff !important;
      }
    }
  }
}

.btnBox {
  width: 90%;
  position: fixed;
  bottom: 0;
  z-index: 1;
}

.formContent {
  flex: 1;
}
