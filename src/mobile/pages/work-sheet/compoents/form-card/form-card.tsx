import { FormRender, FormRenderInstance } from '@@/form-engine/containers';
import { postMallCollect } from '@/apis';
import { isWeb } from '@/utils/js-bridge';
import { useState, useRef } from 'react';
import { message } from 'antd';
import classNames from 'classnames';
import { collectParams } from '@/apis/gmall/post-mall-collect';
import { showLoading, hideLoading } from '../../../../components/loading';
import sendMessage from '../../../../utils/utils';
import Card from '../card/card';
import style from './form-card.module.less';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare let wx: any;

const imgS = [
  'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202316/1672997748487950.png', // 户型诊断
  'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202316/1672997932993153.png', // 0元领取
  'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202316/1672997976659639.png', // 装修
  'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202316/1672998002720757.png', // 一键
];

function FormCard({ formCode }: { formCode: string }) {
  const FormRenderRef = useRef(null as unknown as FormRenderInstance);

  const [isSubmit, setSubmitStatus] = useState(false);

  const back = () => {
    sendMessage('back', {
      isClose: true,
    });
    // const org = window.location.origin;
    const org = '*';
    window.parent.postMessage({ type: 'success' }, org);
    if (wx) {
      wx.miniProgram.navigateBack();
    }
  };

  const getUrlParams = () => {
    // 通过 ? 分割获取后面的参数字符串
    const urlStr = window.location.href.split('?')[1];
    // 创建空对象存储参数
    const obj = {} as any;
    // 再通过 & 将每一个参数单独分割出来
    const paramsArr = urlStr.split('&');
    for (let i = 0, len = paramsArr.length; i < len; i += 1) {
      // 再通过 = 将每一个参数分割为 key:value 的形式
      const arr = paramsArr[i].split('=');
      // eslint-disable-next-line prefer-destructuring
      obj[arr[0]] = arr[1];
    }
    return obj;
  };

  const requireApplyMall = (params: collectParams) => {
    showLoading();
    postMallCollect(params, { tenantId: getUrlParams().tenantId })
      .then(() => {
        message.success('提交成功');
        setTimeout(() => {
          back();
        }, 1500);
      })
      .finally(() => {
        hideLoading();
      });
  };

  const getName = (name: string) => {
    if (name === '智能报价') {
      return '提交';
    }
    if (name === '免费设计') {
      return '0元领取设计方案';
    }
    if (name === '户型诊断') {
      return '立即诊断';
    }
    return '一键匹配';
  };

  const getArrayString = (key: string) => {
    const name = decodeURIComponent(getUrlParams().btnName);

    if (
      key === 'name' ||
      key === 'phone' ||
      key === 'column11' ||
      key === 'column4' ||
      key === 'column1'
    ) {
      if (name === '智能报价' && key === 'column1') {
        return '';
      }
      if (name === '智能报价' && key === 'column4') {
        return [];
      }
      if (name === '智能匹配' && key === 'column1') {
        return [];
      }
      return '';
    }
    return [];
  };

  // 提交
  const submit = () => {
    setSubmitStatus(true);
    FormRenderRef.current
      .submit()
      .then(
        (data: {
          values: Record<string, unknown>;
          version: number;
          labels: Record<string, string>;
        }) => {
          const extList = [] as {
            colName: string;
            colValue: unknown;
          }[];
          Object.keys(data.values).forEach((key: string) => {
            extList.push({
              colName: key,
              colValue: data.values[key] ? data.values[key] : getArrayString(key),
            });
          });
          const params = {
            formCode,
            tag: decodeURIComponent(getUrlParams().btnName),
            versionNumber: data.version,
            extList,
          } as collectParams;
          requireApplyMall(params);
        }
      );
  };

  // const setCapture = () => {
  //   setTimeout(() => {
  //     const ipt = document.querySelector("input[type='file']");
  //     if (ipt) {
  //       ipt.setAttribute('capture', 'camera');
  //     }
  //   }, 2000);
  // };

  return (
    <div className={style.formCard}>
      <div className={style.img}>
        <img className={style.imgPhoto} src={imgS[getUrlParams().imgId]} alt="" />
      </div>
      <Card style={{ padding: '16px 0', flex: 1 }} className={style.formContent}>
        <div className={classNames(style.fixForm, isSubmit && style.submited)}>
          {formCode && (
            <div className={style.formRender}>
              {getUrlParams().tenantId ? (
                <FormRender
                  theme={false}
                  isMobile
                  layout="horizontal"
                  formCode={formCode || null}
                  ref={FormRenderRef}
                  params={{ tenantId: getUrlParams().tenantId }}
                />
              ) : (
                <FormRender
                  theme={false}
                  isMobile
                  layout="horizontal"
                  formCode={formCode || null}
                  ref={FormRenderRef}
                />
              )}
            </div>
          )}
        </div>
      </Card>

      <div className={style.submitBox}>
        <div
          className={style.submitBtn}
          role="button"
          tabIndex={0}
          onClick={submit}
          style={{ background: isWeb ? '#0099ff' : '#ff5923' }}
        >
          {getName(decodeURIComponent(getUrlParams().btnName))}
        </div>
      </div>
    </div>
  );
}

export default FormCard;
