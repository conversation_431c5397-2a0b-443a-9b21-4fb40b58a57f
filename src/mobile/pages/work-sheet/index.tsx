import { useSearchParams } from 'react-router-dom';
import { useMount } from 'ahooks';
import { isIOS, isWeb } from '@/utils/js-bridge';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';
import { useCallback, useState } from 'react';
import { Helmet } from 'react-helmet';
import sendMessage from '../../utils/utils';
import Head from './compoents/head/head';
import style from './index.module.less';
import FormCard from './compoents/form-card/form-card';

function WorkSheet() {
  const [searchParams] = useSearchParams({ headHeight: '44' });
  const [isWx, setIsWx] = useState(true);
  const headHeight = Number(searchParams.get('headHeight')) || 44;
  const formCode = searchParams.get('formCode') || 'FORMoPrx5ES1667986342746';

  // 返回
  const onBack = useCallback(() => {
    if (!import.meta.env.SSR) {
      window.parent.postMessage({ type: 'back' }, '*');
    }
    sendMessage('back', {
      isClose: true,
    });
  }, []);
  // 选择角色
  // const selectId = () => {};

  const sendLoadedWebMessage = () => {
    if (!import.meta.env.SSR) {
      window.parent.postMessage({ type: 'load' }, '*');
    }
  };

  useMount(() => {
    const token = searchParams.get('token');
    if (token) {
      setHeader('satype', 'ech-aop');
      setToken(token);
      setIsWx(true);
    } else {
      setIsWx(false);
      if (!isWeb) {
        setHeader('satype', isIOS ? 'IOS' : 'ANDROID');
      }

      // sendMessage('getUserInfo', () => {
      //   // setToken(el.authorization);
      // });
      sendLoadedWebMessage();
    }
  });

  const title = decodeURIComponent(searchParams.get('btnName') || '');
  return (
    <div
      className={style.container}
      style={{
        paddingTop: isWx ? '8px' : `${headHeight + 8}px`,
      }}
    >
      {!isWx ? (
        <Head title={title} back={onBack} />
      ) : (
        <Helmet>
          <title>{title}</title>
        </Helmet>
      )}
      <FormCard formCode={formCode} />
    </div>
  );
}

export default WorkSheet;
