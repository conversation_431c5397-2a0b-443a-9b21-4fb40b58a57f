.wrap {
  width: 100vw;
  min-height: 100vh;
  padding-top: 100px;
  padding-bottom: 1px;
  background-color: #f5f6fa;
}

.header {
  width: 100vw;
  min-width: 1440px;
  height: 100px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  background-color: #fff;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
}

.headerCenter {
  display: flex;
  width: 1440px;
  height: 100px;
  margin: 0 auto;
  padding: 0 100px;
  justify-content: center;
  position: relative;
  align-items: center;
}

.backBtn {
  color: #008cff;
  position: absolute;
  top: 37px;
  left: 0;
  cursor: pointer;
}

.title {
  font-size: 22px;
  font-weight: 600;
  text-align: center;
}

.card {
  width: 1440px;
  margin: 20px auto;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
  background: #fff;
  border-radius: 4px;
  padding: 32px 44px;
}

.cardTitle {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
}
