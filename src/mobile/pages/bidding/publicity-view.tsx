import { useMount, useUnmount } from 'ahooks';
import { useMemo, useState } from 'react';
import { FileList, FileListItem, Empty, Icon } from '@/components';
import MyEditor from '@/src/bidding/components/editor';
import { useTranslation } from 'react-i18next';
import { StateType } from '@/src/bidding/components/send-modal';
import styles from './publicity-view.module.less';

function BiddingViewPage() {
  const { t } = useTranslation();
  const [state, setState] = useState<StateType | null>(null);
  const files = useMemo(() => {
    if (!state) return [];
    return state.fileList.map((file) => ({
      uid: file.id,
      name: file.name,
      type: file.type,
      url: file.fileUrl,
      size: file.fileSize,
      status: 'done',
    }));
  }, [state]);

  useMount(() => {
    let data = window.localStorage.getItem('BiddingPublicity');
    if (!data) return;
    try {
      data = JSON.parse(data);
    } catch (err) {
      console.error(err);
    }
    setState(data as unknown as StateType);
  });

  useUnmount(() => {
    setState(null);
    window.localStorage.removeItem('BiddingPublicity');
  });

  return (
    <div className={styles.wrap}>
      {state ? (
        <>
          <div className={styles.header}>
            <div className={styles.headerCenter}>
              <span
                className={styles.backBtn}
                role="button"
                tabIndex={0}
                onClick={() => {
                  window.opener = null;
                  window.open('', '_self');
                  window.close();
                }}
              >
                <Icon name="left" size={20} />
                {t('bidding_return_edit')}
              </span>
              <div className={styles.title}>{state.title}</div>
            </div>
          </div>
          <div className={styles.card}>
            <MyEditor readOnly htmlValue={state.article} />
          </div>
          {!!files.length && (
            <div className={styles.card}>
              <div className={styles.cardTitle}>{t('supplier_attachment')}</div>
              <FileList
                listType="list"
                layout="horizontal"
                download
                fileList={files as FileListItem[]}
              />
            </div>
          )}
        </>
      ) : (
        <Empty />
      )}
    </div>
  );
}

BiddingViewPage.displayName = 'BiddingViewPage';

BiddingViewPage.defaultProps = {};

export default BiddingViewPage;
