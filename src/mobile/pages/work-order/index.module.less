@import 'styles/mixins/mixins';

.container {
  max-width: 768px;
  height: 100%;
  margin: 0 auto;
  background-color: @background-colors[normal];
  overflow-y: auto;
}

.wrap {
  padding: 12px 20px 0;

  :global {
    .ant-form-item {
      margin-bottom: 0;

      &-control-input {
        min-height: auto;
      }
    }
  }
}

.footer {
  width: 100%;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: @white;
}

.item {
  margin-bottom: 16px;
  padding: 12px 16px;
  border-radius: @border-radius-base;
  background-color: @white;

  :global {
    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-input,
    .ant-picker,
    .ant-select-selector,
    .ant-input-affix-wrapper {
      padding-right: 0 !important;
      padding-left: 0 !important;
      resize: none;
      outline: none;
      box-shadow: none !important;
      border-color: transparent !important;
    }

    .ant-input-textarea {
      margin-bottom: 18px;
    }
  }
}
