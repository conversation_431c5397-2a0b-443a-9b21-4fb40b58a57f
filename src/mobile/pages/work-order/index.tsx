import { use<PERSON>allback, useEffect, useState, useMemo, ReactNode } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { Button, Form, message } from 'antd';
import { FormRender, FormRenderProps } from '@@/form-engine/containers';
import { submitWorkOrder } from '@/apis';
import { setLogout } from '@/utils/http';
import { user } from '@/store';
import { isWeb } from '@/utils/js-bridge';
import { useTranslation } from 'react-i18next';
import { ViewChildrenProps } from '@@/form-engine/containers/form-render/view';
import { ItemCommonAttribute } from '@@/form-engine/containers/form-render/interface';
import useMiniProgram from '../../hooks/use-mini-program';
// import Container from '../../components/container';
import Head from '../../components/head/head';
import styles from './index.module.less';
import sendMessage from '../../utils/utils';

function WorkOrderFormItem({
  children,
  component,
}: ViewChildrenProps<ItemCommonAttribute> & { children: ReactNode }) {
  if (component.type === 'instruction') {
    return <div className="mb-4">{children}</div>;
  }
  return <div className={styles.item}>{children}</div>;
}

function WorkOrder() {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const params = useParams<{ formCode: string; title: string }>();
  const [newParams] = useSearchParams();
  const navigate = useNavigate();
  const [submitting, setSubmitting] = useState(false);
  const [title, setTitle] = useState('');

  const isUmWebsite = useMemo(() => newParams.get('from') === 'um-website', [newParams]);

  const onSubmit = useCallback(() => {
    form.submit();
  }, [form]);

  const back = useCallback(() => {
    if (isWeb) {
      navigate(-1);
    } else {
      window.wx?.miniProgram.navigateBack();
      sendMessage('back', { isClose: true });
    }
  }, [navigate]);

  const onFinish: FormRenderProps['onFinish'] = useCallback(
    (values, version) => {
      setSubmitting(true);
      const extMap: Record<string, unknown> = {};
      Object.keys(values).forEach((key) => {
        extMap[key] = values[key];
      });
      submitWorkOrder({
        extMap,
        versionNumber: version,
        source: 1,
        formCode: params.formCode || '',
      })
        .then(() => {
          message.success(t('work_order_submit_success'));
          if (!import.meta.env.SSR) {
            setTimeout(() => {
              if (isWeb) {
                if (isUmWebsite) {
                  navigate(-1);
                } else {
                  window.close();
                }
              } else {
                window.wx?.miniProgram.navigateBack();
                sendMessage('back', { isClose: true });
              }
            }, 2000);
          }
        })
        .finally(() => {
          setSubmitting(false);
        });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [isUmWebsite, navigate, params.formCode]
  );
  useMiniProgram();

  useEffect(() => {
    setLogout(() => {
      user.logout().then((to) => {
        navigate(to);
      });
    });
    return () => {
      setLogout(null);
    };
  }, [navigate]);

  useEffect(() => {
    const paramTitle = newParams.get('title');
    if (paramTitle) setTitle(paramTitle);
  }, [newParams]);

  return (
    // <Container
    //   footer={
    //     <div className={`py-5 px-4 ${styles.footer}`}>
    //       <Button block loading={submitting} type="primary" htmlType="submit" onClick={onSubmit}>
    //         提交
    //       </Button>
    //     </div>
    //   }
    //   className={styles.container}
    // >
    <div className={styles.container}>
      <Helmet>
        {/* <title>{title}</title> */}
        <title>请填写</title>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1, maximum-scale=1, user-scalable=0"
        />
      </Helmet>
      {isUmWebsite && <Head title={title} back={back} />}
      <div
        className={styles.wrap}
        style={{
          paddingTop: isUmWebsite ? '44px' : '',
          height: isUmWebsite ? 'calc(100% - 76px)' : '100%',
        }}
      >
        <FormRender
          isMobile
          form={form}
          formCode={params.formCode}
          className={styles.form}
          onFinish={onFinish}
        >
          {WorkOrderFormItem as any}
        </FormRender>
        <div style={{ height: '76px' }} />
      </div>
      <div className={`py-5 px-4 ${styles.footer}`}>
        <Button block loading={submitting} type="primary" htmlType="submit" onClick={onSubmit}>
          {t('mobile_submit')}
        </Button>
      </div>
    </div>
  );
}

export default WorkOrder;
