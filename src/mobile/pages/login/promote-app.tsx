import { useNavigate } from 'react-router-dom';
import { Button } from 'antd';
import { NavBar } from 'antd-mobile';
import { Icon } from '@/components';
import styles from './promote-app.module.less';
import imgUrl from './images/logo.png';

function PromoteApp() {
  const navigate = useNavigate();

  return (
    <div className={styles.wrap}>
      <NavBar
        back="返回"
        backArrow={<Icon name="left" color="#040919" size={32} />}
        onBack={() => {
          navigate(-1);
        }}
        className={styles.navbar}
      >
        echOS
      </NavBar>
      <img src={imgUrl} alt="" className={styles.image} />
      <div className={styles.title}>下载echOS客户端</div>
      <div className={styles.subtitle}>随时随地聊生意</div>
      <Button
        type="primary"
        className={styles.button}
        onClick={() => {
          window.location.href =
            'https://a.app.qq.com/o/simple.jsp?pkgname=com.echronos.huaandroid';
        }}
      >
        立即下载
      </Button>
      <div className={styles.message}>想要更加深入的了解我们？</div>
      <div className={styles.link}>
        <a href="https://www.echronos.com/">
          查看更多
          <Icon name="right" className={styles.icon} />
        </a>
      </div>
    </div>
  );
}

PromoteApp.displayName = 'PromoteAppPage';

export default PromoteApp;
