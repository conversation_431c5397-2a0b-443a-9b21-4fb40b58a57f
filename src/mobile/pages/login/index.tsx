import { ChangeEvent<PERSON><PERSON><PERSON>, useEffect, useRef, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useUnmount, useThrottleFn } from 'ahooks';
import { Form, Checkbox, Button, Tooltip, Input, message } from 'antd';
import classNames from 'classnames';
import { setToken } from '@/utils/auth';
import { Icon } from '@/components';
import { sendLoginVerifyCode, phoneVerifyCodeLogin, sendInvitationMessage } from '@/apis';
import { AuthResult } from '@/apis/utils/auth';
import styles from './index.module.less';
import layoutsStyles from '../../layouts/layouts.module.less';

const phoneRegExp = /^1((3[0-9])|(4[1579])|(5[0-9])|(6[6])|(7[0-9])|(8[0-9])|(9[0-9]))\d{8}$/;
const codeRegExp = /^[0-9]{6}$/;

function Countdown({ phone }: { phone: string }) {
  const timer = useRef<ReturnType<typeof setTimeout> | null>(null);
  const [loading, setLoading] = useState(false);
  const [timeCount, setTimeCount] = useState(0);

  // 获取验证码
  const getCode = useThrottleFn(
    () => {
      setLoading(true);
      sendLoginVerifyCode(phone)
        .then((res) => {
          message.open({
            content: `验证码已通过短信发送至+86 ${phone}`,
            className: layoutsStyles.messageSize,
            type: 'success',
            duration: 3,
          });

          let count = res.time;
          setTimeCount(count);
          timer.current = setInterval(() => {
            count -= 1;
            setTimeCount(count);
            if (count <= 0 && timer.current) {
              // @ts-ignore
              clearInterval(timer.current);
              timer.current = null;
            }
          }, 1000);
        })
        .catch(() => {
          message.open({
            content: '获取验证码频繁，请休息一下再试!',
            className: layoutsStyles.messageSize,
            type: 'error',
            duration: 3,
          });
        })
        .finally(() => {
          setLoading(false);
        });
    },
    { wait: 10000 }
  ).run;

  useUnmount(() => {
    if (timer.current) {
      // @ts-ignore
      clearInterval(timer.current);
      timer.current = null;
    }
  });

  if (timeCount > 0) return <span className={styles.countdown}>{timeCount}s 后重新获取</span>;

  if (phone && phoneRegExp.test(phone))
    return (
      <span className={styles.linkBtn} role="button" tabIndex={0} onClick={getCode}>
        {loading ? <Icon name="loading" className="mr-1" /> : null}
        获取验证码
      </span>
    );

  return <span className={styles.code}>获取验证码</span>;
}

function Login() {
  const [searchParams] = useSearchParams({ memberId: '', type: '' });
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const phone = Form.useWatch('phone', form);
  const code = Form.useWatch('code', form);
  const agree = useRef(false);
  const [loading, setLoading] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [iframeSrc, setIframeSrc] = useState('');
  const loginNumber = useRef(0);
  const isLogin = useRef(false);
  const isPhone = useRef(false);
  const [newPhone, setNewPhone] = useState('');

  useEffect(() => {
    if (isPhone.current && !!phone.length) {
      let phoneArr = phone;
      phoneArr = phoneArr?.split(' ').join('');
      if (!phoneRegExp.test(phoneArr)) {
        form.setFields([{ errors: ['手机号错误，请重新输入'], name: 'phone' }]);
      } else {
        form.setFields([{ errors: [], name: 'phone' }]);
      }
      setNewPhone(phoneArr);
    } else {
      form.setFields([{ errors: [], name: 'phone' }]);
    }
  }, [phone, form]);

  // 输入长度控制
  const testValue = (changedValues: Record<string, string>) => {
    if (isLogin.current) {
      form.setFields([
        { errors: [], name: 'phone' },
        { errors: [], name: 'code' },
      ]);
    }
    if ('code' in changedValues && changedValues.code.length > 6) {
      // form.setFieldsValue({ code: changedValues.code.slice(0, 6) });
    } else if ('phone' in changedValues && changedValues.phone.length > 11) {
      // form.setFieldsValue({ phone: changedValues.phone.slice(0, 11) });
    }
  };

  // 记录
  const onLog = (result: AuthResult) => {
    setToken(`${result.tokenType} ${result.accessToken}`);
    sendInvitationMessage({
      invitationMemberId: Number(searchParams.get('memberId')) || 0,
      invitationType: Number(searchParams.get('type')) || 1,
    });
  };

  // 登录
  const onLogin = () => {
    if (!agree.current) {
      setTooltipVisible(true);
      return;
    }

    loginNumber.current += 1;
    setLoading(true);
    phoneVerifyCodeLogin(newPhone, code)
      .then((result) => {
        onLog(result);
        navigate('/m/login/promote-app');
        loginNumber.current = 0;
      })
      .catch(() => {
        isLogin.current = true;
        if (loginNumber.current > 3) {
          form.setFields([{ errors: ['错误次数过多或验证码过期，请重获验证码'], name: 'code' }]);
        } else {
          form.setFields([
            { errors: ['手机号或验证码错误，请重新输入'], name: 'phone' },
            { errors: ['手机号或验证码错误，请重新输入'], name: 'code' },
          ]);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 查看协议
  const openProtocol = (name: string) => {
    setIframeSrc(`${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/agreement/${name}.html`);
  };

  const iframeNode = (
    <div
      className={classNames(styles.iframeBox, {
        [styles.iframeVisible]: !!iframeSrc,
      })}
    >
      <div className={classNames('text-right', 'pt-3', 'px-4')}>
        <Icon
          name="close"
          className={styles.iframeClose}
          onClick={() => {
            setIframeSrc('');
          }}
        />
      </div>
      <iframe title="协议" src={iframeSrc} className={styles.iframe} />
    </div>
  );

  // 手机号处理
  const phoneChangeHandler: ChangeEventHandler<HTMLInputElement> = (e) => {
    isPhone.current = true;
    if (Number(e.target.value?.trim())) {
      if (e.target.value) {
        const value = e.target.value
          .match(/[0-9]/g)
          ?.join('')
          .replace(/\d{1,3}(?=(\d{4})+$)/g, '$& ');
        form.setFieldsValue({ phone: value });
      }
    }
  };

  // 验证码处理
  const codeChangeHandler: ChangeEventHandler<HTMLInputElement> = (e) => {
    if (Number(e.target.value?.trim())) {
      if (e.target.value) {
        const value = e.target.value.match(/[0-9]/g)?.join('');
        form.setFieldsValue({ code: value });
      }
    }
  };

  return (
    <div className={styles.wrap}>
      <div className={styles.view}>
        <div className={styles.title}>验证码登录</div>
        <div className={styles.subtitle}>未注册的手机号码验证通过后将自动注册</div>
        <Form form={form} className={styles.from} onValuesChange={testValue}>
          <Form.Item
            name="phone"
            className={styles.formItem}
            getValueFromEvent={(e) => e.target.value.replace(/[^0-9]/gi, '')}
          >
            <Input
              prefix={<span className={styles.phone}>+86</span>}
              maxLength={13}
              placeholder="请输入手机号"
              className={styles.input}
              onChange={phoneChangeHandler}
            />
          </Form.Item>
          <Form.Item
            name="code"
            rules={[{ pattern: codeRegExp, message: '验证码错误，请重新输入' }]}
            className={styles.formItem}
            getValueFromEvent={(e) => e.target.value.replace(/[^0-9]/gi, '')}
          >
            <Input
              suffix={<Countdown phone={newPhone} />}
              maxLength={6}
              placeholder="请输入验证码"
              className={styles.input}
              onChange={codeChangeHandler}
            />
          </Form.Item>
        </Form>
        <Button
          type="primary"
          loading={loading}
          disabled={!phone || !code}
          className={styles.btn}
          onClick={onLogin}
        >
          登录
        </Button>
        <div className={styles.footer}>
          <Tooltip
            visible={tooltipVisible}
            placement="topLeft"
            title="请先阅读并同意协议"
            color="#fff"
            overlayClassName={styles.tooltip}
          >
            <Checkbox
              className={styles.checkbox}
              onChange={(e) => {
                agree.current = e.target.checked;
                if (e.target.checked) {
                  setTooltipVisible(false);
                }
              }}
            >
              <span className={styles.checkboxLable}>
                已阅读并同意
                <span
                  className={styles.link}
                  role="button"
                  tabIndex={0}
                  onClick={(e) => {
                    e.stopPropagation();
                    openProtocol('华世界用户注册协议');
                  }}
                >
                  《用户协议》
                </span>
                和
                <span
                  className={styles.link}
                  role="button"
                  tabIndex={0}
                  onClick={(e) => {
                    e.stopPropagation();
                    openProtocol('华世界隐私权政策');
                  }}
                >
                  《隐私协议》
                </span>
              </span>
            </Checkbox>
          </Tooltip>
        </div>
        {iframeNode}
      </div>
    </div>
  );
}

Login.displayName = 'MobileLoginPage';

export default Login;
