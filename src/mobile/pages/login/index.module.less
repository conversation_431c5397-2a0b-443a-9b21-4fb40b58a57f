.wrap {
  width: 100vw;
  height: 100vh;
  background-image: url('./images/background.png');
  background-size: 100vw 100vh;
  position: relative;
}

.view {
  width: 686px;
  height: 1116px;
  margin: auto;
  padding: 146px 56px 36px;
  overflow: hidden;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgb(255 255 255 / 80%);
  backdrop-filter: blur(272px);
  border-radius: 36px;
}

.title {
  font-size: 48px;
  font-weight: 500;
  line-height: 58px;
  margin-bottom: 24px;
}

.subtitle {
  color: #888b98;
  font-size: 28px;
  line-height: 34px;
  margin-bottom: 80px;
}

.btn {
  font-size: 28px;
  width: 100%;
  height: 76px;
  border-radius: 20px;
}

.footer {
  margin-bottom: 0;
  position: absolute;
  bottom: 36px;
  left: 56px;
}

.checkbox {
  display: flex;
  align-items: center;
  height: 32px;

  :global {
    .ant-checkbox-inner {
      width: 32px !important;
      height: 32px !important;
    }

    .ant-checkbox {
      top: 0;
    }

    .ant-checkbox-inner::after {
      width: 8px;
      height: 14px;
      top: 44%;
      left: 32%;
      border-width: 3px;
    }
  }
}

.tooltip {
  :global {
    .ant-tooltip-inner {
      color: #040919;
      font-size: 28px;
      width: 302px;
      line-height: 44px;
      padding: 10px 24px;
    }
  }
}

.checkboxLable {
  color: #888b98;
  font-size: 24px;
  line-height: 32px;
}

.link {
  color: #008cff;
}

.from {
  position: relative;

  :global {
    .ant-form-item-has-error + .ant-form-item-has-error .ant-form-item-explain {
      display: none;
    }
  }
}

.formItem {
  margin-bottom: 40px;

  & + & {
    margin-bottom: 72px;
  }

  :global {
    .ant-form-item-control {
      position: static;
    }

    .ant-form-item-explain {
      font-size: 24px;
      line-height: 28px;
      position: absolute;
      bottom: -48px;
    }
  }
}

.input {
  font-size: 32px;
  height: 96px;
  padding: 28px 32px;
  border-radius: 20px;
  border-color: #fff;

  :global {
    .ant-input {
      font-size: 32px;
    }
  }
}

.phone {
  color: #040919;
  margin-right: 20px;
  padding-right: 32px;
  border-right: 2px solid #e5e5e6;
}

.code {
  color: #b1b3be;
}

.countdown {
  color: #888b98;
}

.linkBtn {
  color: #008cff;
}

.iframeBox {
  display: none;
  width: 100%;
  height: 1116px;
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9;
  background-color: #fff;
}

.iframeVisible {
  display: block;
}

.iframe {
  width: 100%;
  height: calc(100% - 100px);
  border: none;
}

.iframeClose {
  font-size: 32px;
  cursor: pointer;
}
