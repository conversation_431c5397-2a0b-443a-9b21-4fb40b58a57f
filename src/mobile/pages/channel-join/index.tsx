import { Helmet } from 'react-helmet';
import { Select, Button, message, Drawer, Form, Input } from 'antd';
import { setToken } from '@/utils/auth';
import { useSearchParams } from 'react-router-dom';
import React, { useEffect, useRef, useState } from 'react';
import {
  ICompany,
  queryCompanyList,
  iformEnableDefault,
  channelJoinSubmit,
  ChannelJoinValid,
  channelDetailInfo,
  GetCategoryResult,
  getCompanyCategories,
  createCompanyTeam,
} from '@/apis';
import { useRequest } from 'ahooks';
import { FormRender, FormRenderInstance } from '@@/form-engine/containers';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import { isWeb } from '@/utils/js-bridge';
import { debounce } from 'lodash';
import Cascader from 'antd/es/cascader';
import { Icon } from '@/components';
import { useTranslation } from 'react-i18next';
import Company from '../login/images/company.png';
import CheckCircle from '../login/images/checkCircle.png';
import styles from './index.module.less';

const { Option } = Select;
function ChannelJoin() {
  const { t } = useTranslation();
  const [params] = useSearchParams({ token: '' });
  const [creatForm] = Form.useForm();
  const ref = useRef(null as unknown as FormRenderInstance);
  const [companyList, setCompanyList] = useState<ICompany[]>([]);
  const [categories, setCategories] = useState<GetCategoryResult[]>([]);
  const [isDisabled, setIsDisabled] = useState(true);
  const [loading, setLoading] = useState(false);
  const [isCreateTeamShow, setIsCreateTeamShow] = useState(false);
  const [companyId, setCompanyId] = useState<null | number>(null);
  const [items, setItems] = useState<any>([]);
  const formInfo = useRef({
    formCode: '',
    formVersion: 0,
  });
  const [formObj, setFormObj] = useState<Record<string, unknown> | null>(null);

  const { run } = useRequest(queryCompanyList, {
    manual: true,
    defaultParams: [{ appId: params.get('appId') || '' }],
    onSuccess: (result) => {
      setCompanyList(result.list);
    },
  });

  const closeCreateDrawer = () => {
    creatForm.resetFields();
    setIsCreateTeamShow(false);
  };

  const onChangeCompany = (val: number, e: { label: string } | { label: string }[]) => {
    const enterCompanyLabel = e as { label: string; value: string };
    if (enterCompanyLabel.label === 'noCompany' && enterCompanyLabel.value === 'noCompany') {
      setCompanyId(null);
      // ref.current.form.resetFields(['enterCompanyId']);
      return;
    }
    if (
      enterCompanyLabel.label === 'createCompany' &&
      enterCompanyLabel.value === 'createCompany'
    ) {
      setCompanyId(null);
      // ref.current.form.resetFields(['enterCompanyId']);
      setIsCreateTeamShow(true);
      return;
    }
    ChannelJoinValid({ companyId: val, appId: params.get('appId') || '' }).then(() => {
      setCompanyId(val);
    });
  };

  const { run: getDetail } = useRequest(channelDetailInfo, {
    manual: true,
    defaultParams: [{ id: Number(params.get('id')), actionType: 2 }],
    onSuccess: (res) => {
      const obj: Record<string, unknown> = {};
      if (res.extendVOS && res.extendVOS.length) {
        res.extendVOS.forEach((item) => {
          obj[item.colName] = item.colValue;
        });
      }
      setCompanyId(res.companyId);
      setFormObj({ ...obj });
    },
  });

  const { run: getForm } = useRequest(iformEnableDefault, {
    manual: true,
    defaultParams: [{ type: 5, appId: params.get('appId') || '' }],
    onSuccess: (result) => {
      setItems(result.components);
      formInfo.current = {
        formCode: result.formCode,
        formVersion: result.versionNumber,
      };
    },
  });

  const onSubmit = () => {
    if (!companyId) {
      message.warning(t('channel_select_identity'));
      return;
    }
    ref.current?.submit().then((res) => {
      const arr = Object.keys(res.values).map((item) => ({
        colName: item,
        colValue: res.values[item],
      })) as [];
      channelJoinSubmit({
        channelExtendList: arr.filter((item: any) => item.colValue),
        companyId: Number(companyId),
        formCode: formInfo.current.formCode,
        formVersion: formInfo.current.formVersion,
        settleType: 1,
        appId: params.get('appId') || '',
        id: Number(params.get('id')) || null,
      }).then(() => {
        message.success(t('channel_submit_success'));
        setTimeout(() => {
          window.wx?.miniProgram.navigateBack();
        }, 1500);
      });
    });
  };

  const onValuesChange = () => {
    const formInfoo = creatForm.getFieldsValue();
    if (formInfoo.companyName && formInfoo.industry) {
      setIsDisabled(false);
      return;
    }
    setIsDisabled(true);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onCreatFinish = debounce((value: any) => {
    if (!categories) return;
    const { industry } = value;
    const parent = categories.find((cate) => cate.value === industry[0]);
    if (!parent) return;
    const child = (parent.children || []).find((cate) => cate.value === industry[1]);
    if (!child) return;
    if (loading) return;
    setLoading(true);
    createCompanyTeam({
      companyName: value.companyName,
      industry: `${parent.label}/${child.label}`,
    })
      .then(() => {
        closeCreateDrawer();
        run({ appId: params.get('appId') || '' });
        message.open({
          content: (
            <div>
              <img src={CheckCircle} alt="" className={styles.checkCircleIcon} />
              {t('channel_create_success')}
            </div>
          ),
        });
      })
      .finally(() => {
        setTimeout(() => {
          setLoading(false);
        });
      });
  }, 500);

  useEffect(() => {
    const token = params.get('token');
    if (token) {
      setToken(token || '');
    }
    run({ appId: params.get('appId') || '' });
    getForm({ type: 5, appId: params.get('appId') || '' });
    getDetail({ id: Number(params.get('id')), actionType: 2 });
  }, [params, run, getForm, getDetail]);

  useEffect(() => {
    getCompanyCategories().then(({ list = [] }) => {
      const globalCategories = list.map((item) => {
        if (item.children && item.children.length > 0) {
          item.children.forEach((child) => {
            // eslint-disable-next-line no-param-reassign
            child.children = undefined;
          });
        }
        return item;
      });
      setCategories(globalCategories);
    });
  }, []);

  return (
    <div className={styles.body}>
      <Helmet>
        <title>{t('channel_apply_become_partner')}</title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.from}>
          <div className={styles.selectBox}>
            <div className={styles.label}>{t('channel_select_identity')}</div>
            <Select
              disabled={Number(params.get('id')) > 0}
              className={styles.select}
              value={companyId}
              placeholder={t('channel_select_organization')}
              onChange={onChangeCompany}
            >
              {companyList.length ? (
                companyList.map((item) => (
                  <Option value={item.companyId} label={item.companyName} key={item.companyId}>
                    <div className={styles.companyList}>
                      <span className={styles.companyName}>{item.companyName}</span>
                    </div>
                  </Option>
                ))
              ) : (
                <Option value="noCompany" label="noCompany" style={{ background: '#fff' }}>
                  <div className={styles.noCompanyBox}>
                    <div>{t('channel_no_organization')}</div>
                    <div>{t('channel_click_create_below')}</div>
                  </div>
                </Option>
              )}
              <Option value="createCompany" label="createCompany" key="createCompany">
                <div className={styles.createCompanyBox}>
                  <div>
                    <img src={Company} alt="" className={styles.companyIcon} />
                  </div>
                  <div>{t('channel_create_organization')}</div>
                </div>
              </Option>
            </Select>
          </div>
          <div className={styles.fromList}>
            <FormRender ref={ref} formCode={null} items={items} values={formObj} />
          </div>
        </div>
      </div>
      <div className={styles.footer}>
        <Button type="primary" onClick={onSubmit}>
          {t('channel_submit_application')}
        </Button>
      </div>
      <Drawer
        placement="bottom"
        closable={false}
        onClose={() => closeCreateDrawer()}
        visible={isCreateTeamShow}
        height="80%"
      >
        <div className={styles.drawerBox}>
          <div className={styles.drawerTitleBox}>
            <div />
            <div className={styles.drawerTitle}>{t('channel_create_team')}</div>
            <CloseOutlined className={styles.drawerIcon} onClick={() => closeCreateDrawer()} />
          </div>
          <div className={styles.drawerDescribe}>{t('channel_team_info_tip')}</div>
        </div>
        <div className={styles.drawerForm}>
          <Form form={creatForm} onFinish={onCreatFinish} onValuesChange={onValuesChange}>
            <div className={styles.formBox}>
              <div className={styles.formLabel}>{t('channel_organization_name')}</div>
              <div className={styles.formValue}>
                <Form.Item name="companyName" rules={[{ required: true, message: '' }]}>
                  <Input
                    maxLength={50}
                    bordered={false}
                    placeholder={t('channel_enter_organization_name')}
                  />
                </Form.Item>
              </div>
            </div>

            <div className={styles.formBox}>
              <div className={styles.formLabel}>{t('channel_industry_type')}</div>
              <div className={styles.formValue}>
                <Form.Item name="industry" rules={[{ required: true, message: '' }]}>
                  <Cascader
                    bordered={false}
                    options={categories || []}
                    placeholder={t('channel_select_industry_type')}
                    loading={!categories}
                    allowClear={false}
                    showSearch
                    suffixIcon={<Icon name="down" size={16} />}
                  />
                </Form.Item>
              </div>
            </div>

            {isCreateTeamShow && (
              <div className={styles.btnBox}>
                <Form.Item wrapperCol={{ span: 16 }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    block
                    // eslint-disable-next-line no-nested-ternary
                    style={{ background: isDisabled ? '#c6ccd8' : isWeb ? '#0099ff' : '#ff5923' }}
                    disabled={isDisabled}
                  >
                    {t('channel_confirm')}
                  </Button>
                </Form.Item>
              </div>
            )}
          </Form>
        </div>
      </Drawer>
    </div>
  );
}

export default ChannelJoin;
