.coupon {
  .drawerContentLeft {
    // height: calc(100vh - 48px);
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .coupon_list {
    display: flex;
    padding: 4px;
    justify-content: space-between;
    flex-wrap: wrap;

    & ::-webkit-scrollbar {
      display: none;
    }

    :global {
      .ant-spin-nested-loading {
        width: 100%;
      }
    }
  }

  .m_product_card {
    width: 48%;
    margin-bottom: 12px;
    overflow: hidden;
    border-radius: 12px;
    background: #fff;
    box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

    .image_box {
      width: 100%;
      height: 165px;
      position: relative;

      &.image_higher {
        height: 185px;
      }

      .tips_box {
        color: #fff;
        font-size: 12px;
        height: 24px;
        line-height: 24px;
        padding: 0 12px;
        position: absolute;
        bottom: 8px;
        left: 8px;
        border-radius: 8px;
        background: rgb(0 0 0 / 30%);
        backdrop-filter: blur(5px);

        .apply_discount {
          display: inline-block;
          min-width: 63px;
          height: 100%;
          margin-right: 7px;
          padding: 0 8px;
          text-align: center;
          background: linear-gradient(90deg, #ff2201 0%, #ff833c 100%);
          border-radius: 8px;
          box-shadow: inset 0 0.7px 0 0 #faa;
        }

        &.multiple {
          // width: 150px;
          padding: 0;
        }
      }
    }

    .image {
      width: 100%;
      height: 100%;
      border-top-left-radius: 12px;
      border-top-right-radius: 12px;
    }

    .info_box {
      padding: 8px 12px 20px;

      .name {
        color: #040919;
        font-size: 14px;
        font-weight: 600;
        display: flex;
        display: -webkit-box;
        height: 36px;
        line-height: 20px;
        margin-bottom: 8px;
        overflow: hidden;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
      }

      .seckill_icon {
        width: 38px;
        height: 16px;
        vertical-align: middle;
        margin-right: 3px;
      }

      .ring_price {
        width: 38px;
        height: 16px;
        vertical-align: middle;
        margin-right: 3px;
      }

      .des {
        color: #888b98;
        font-size: 12px;
        line-height: 16px;
        margin-bottom: 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .price_ele {
      display: inline-block;

      .unit {
        color: #040919;
        font-size: 12px;
        font-weight: bold;
        line-height: 20px;
        vertical-align: super;
      }

      .integer {
        color: #040919;
        font-size: 24px;
        font-weight: bold;
        line-height: 20px;

        &.small {
          font-size: 16px;
        }
      }

      .decimal {
        color: #040919;
        font-size: 12px;
        font-weight: bold;
        line-height: 23px;
        margin-left: 2px;
        vertical-align: super;
      }
    }

    .org_price {
      color: #888b98;
      font-size: 10px;
      line-height: 12px;
      margin-left: 4px;
      text-decoration: line-through;
    }
  }

  .load_tip_text {
    color: #b1b3be;
    font-size: 12px;
    width: 100%;
    text-align: center;
  }
}
