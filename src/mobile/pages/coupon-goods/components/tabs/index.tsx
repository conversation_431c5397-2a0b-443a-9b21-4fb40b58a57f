import classNames from 'classnames';
import { CategoryListResult } from '@/apis';
import { PropsWithChildren, useState } from 'react';
import styles from './index.module.less';

interface TabsProps {
  categoryList: CategoryListResult[];
  defaultType?: string;
  // eslint-disable-next-line no-unused-vars
  changeTab: (key: string) => Promise<void> | void;
}

let isLock = false;

function Tabs({ categoryList, defaultType, changeTab }: PropsWithChildren<TabsProps>) {
  const [type, setType] = useState(defaultType);

  const switchTab = (code: string) => {
    if (code === type || isLock) return;
    setType(code);
    isLock = true;
    if (changeTab) {
      const p = changeTab(code);
      if (!p) {
        isLock = false;
      } else
        p.then(() => {
          isLock = false;
        });
    }
  };

  return (
    <div className={styles.tabs}>
      {categoryList.map((item) => (
        <div
          className={classNames(styles.tab, { [styles.tab_active]: item.page.code === type })}
          key={item.page.code}
          tabIndex={0}
          role="button"
          onClick={() => switchTab(item.page.code)}
        >
          {item.title}
        </div>
      ))}
    </div>
  );
}

Tabs.defaultProps = {
  defaultType: '1',
};

export default Tabs;
