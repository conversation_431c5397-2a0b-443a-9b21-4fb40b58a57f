.tabs {
  display: flex;
  height: 40px;
  overflow: auto;
  justify-content: left;
  align-items: center;
  background-color: #fff;
  white-space: nowrap;
  // 滚动条隐藏
  &::-webkit-scrollbar {
    display: none;
  }

  .tab {
    color: #333;
    font-size: 14px;
    height: 100%;
    line-height: 40px;
    padding: 0 5px;
    text-align: center;
  }

  .tab_active {
    color: #ff5923;
    font-size: 20px;
    position: relative;

    &::after {
      content: '';
      width: 24px;
      height: 3px;
      position: absolute;
      bottom: 0;
      bottom: 5%;
      left: 50%;
      transform: translate(-50%, 0);
      border-radius: 3px;
      background: linear-gradient(90deg, #ff2201 0%, #ff833c 100%);
    }
  }
}
