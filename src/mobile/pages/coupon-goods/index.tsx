import { Helmet } from 'react-helmet';
import { useSearchParams } from 'react-router-dom';
import {
  GetMallLevelCategoryResult,
  getMallLevelCategory,
  CategoryListResult,
  MallGood,
  postActivityGoodsList,
  GetMallGoodsPageListParams,
} from '@/apis';
import { useEffect, useRef, useState } from 'react';
import { Empty } from '@/components';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Spin } from 'antd';
import classNames from 'classnames';
import Tabs from './components/tabs';
import seckillIcon from './images/seckill.png';
import styles from './index.module.less';
import { setToken } from '../../utils/utils';

function CouponGoods() {
  const [searchParams] = useSearchParams();
  const activityType = searchParams.get('type') || '2'; // 1 拼团 2秒杀
  const tenantId = searchParams.get('tenantId') || '';
  const mchType = useRef<string>('1');
  const packageParams = useRef({
    activityType: Number(activityType),
    pageNo: 1,
    pageSize: 10,
  });

  const [loadTheComplete, setLoadTheComplete] = useState(true);
  const [spinning, setSpinning] = useState(false);
  const [categoryList, setCategoryList] = useState<CategoryListResult[]>([]);
  const [dataList, setDataList] = useState<MallGood[]>([]);

  const toThousands = (num = 0) =>
    num.toString().replace(/\d+/, (n) => n.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,'));

  const priceEle = (price: number | string) => {
    const priceStrArr = price.toString().split('.');
    const integer = toThousands(Number(priceStrArr[0]));
    const decimal = priceStrArr[1] || '00';
    return (
      <div className="price-ele">
        <span className="unit">￥</span>
        <span className={`integer ${integer.length > 8 && 'small'}`}>{integer}</span>
        <span className="decimal">.{decimal}</span>
      </div>
    );
  };

  const onCardClick = (goods: MallGood) => {
    window.wx.miniProgram.navigateTo({
      url: `/subpackages/goods/pages/goods-detail/index?shopSkuId=${goods.id}&shopId=${goods.shopId}`,
    });
  };

  const getMallList = () => {
    if (categoryList.length > 1) return;
    getMallLevelCategory({ tenantId }).then((res) => {
      const list =
        res.list?.map((item: GetMallLevelCategoryResult) => ({
          title: item.categoryName,
          page: { code: item.code },
        })) || [];
      setCategoryList([{ title: '全部', page: { code: '1' } }, ...list]);
    });
  };

  const getGoodsList = () => {
    setSpinning(true);
    const params: GetMallGoodsPageListParams = {
      ...packageParams.current,
    };
    if (mchType.current !== '1') {
      params.mallCatLabelCodes = [mchType.current];
    }
    postActivityGoodsList(params, { tenantId })
      .then((res) => {
        if (res.list.length < 10) {
          setLoadTheComplete(false);
        }
        const newLists =
          ((res.list &&
            res.list?.map((item: MallGood) => ({
              id: item.id,
              shopId: item.shopId,
              image: item.images[0],
              name: item.name,
              des: item.standardList?.map((it) => it.value)?.join(' | '),
              price: item.priceStr || item.price,
              applyDiscount: item.skuType !== 30 && !item.hasLink,
              ringPrice: item.hasLink,
              orgPrice: item.marketPrice,
              minimumOrder: Number(item.minimumStr),
              skuActivityInfo: item.skuActivityInfo,
              ext: {
                sourceData: item,
              },
              unit: item.unit,
            }))) as unknown as MallGood[]) || [];

        setDataList((prev) => [...prev, ...newLists]);
      })
      .finally(() => {
        setSpinning(false);
      });
  };

  const loadMoreCategory = () => {
    packageParams.current.pageNo += 1;
    getGoodsList();
  };

  const changeTab = (code: string) => {
    mchType.current = code;
    packageParams.current.pageNo = 1;
    setDataList([]);
    setLoadTheComplete(true);
    getGoodsList();
  };

  useEffect(() => {
    getMallList();
    getGoodsList();
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, []); // eslint-disable-line

  return (
    <div className={styles.coupon}>
      {categoryList.length > 1 && (
        <Tabs categoryList={categoryList} defaultType="1" changeTab={changeTab} />
      )}
      <div
        className={styles.drawerContentLeft}
        style={{ height: `calc(100vh - ${categoryList.length > 1 ? 40 : 0}px)` }}
        id="drawerContentLeft"
      >
        <InfiniteScroll
          dataLength={dataList.length}
          next={loadMoreCategory}
          hasMore={loadTheComplete}
          className={styles.coupon_list}
          loader={
            !spinning && (
              <div className={styles.textCenter}>
                <Spin tip="加载中..." />
              </div>
            )
          }
          scrollableTarget="drawerContentLeft"
        >
          <Spin spinning={spinning}>
            <div className={styles.coupon_list}>
              {dataList.length ? (
                dataList?.map((item: MallGood) => (
                  <div
                    key={item.id}
                    className={styles.m_product_card}
                    tabIndex={0}
                    role="button"
                    onClick={() => onCardClick(item)}
                  >
                    <div
                      className={classNames(styles.image_box, {
                        [styles.image_higher]: item?.imageHigher,
                      })}
                    >
                      <img className={styles.image} src={item?.image} alt="" />
                      {activityType === '1' && (
                        <div
                          className={classNames(styles.tips_box, {
                            [styles.multiple]: item?.skuType !== 30 && !item?.hasLink,
                          })}
                        >
                          <div className={styles.apply_discount}>
                            {item?.skuActivityInfo?.groupSize} 人团
                          </div>
                          已拼 {item?.skuActivityInfo?.groupPiece} 件
                        </div>
                      )}
                    </div>
                    <div className={styles.info_box}>
                      <div className={styles.name}>
                        {activityType === '2' && (
                          <img className={styles.seckill_icon} src={seckillIcon} alt="" />
                        )}
                        {item.name}
                      </div>
                      <div className={styles.des}>
                        {item.standardList?.map((it) => it.value)?.join(' | ')}
                      </div>
                      <div>
                        {priceEle(item.price)}
                        {item.hasLink && (
                          <span className={styles.org_price}>￥{item.marketPrice}</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div style={{ width: '100%', margin: '25% 0 0 8%' }}>
                  <Empty message="暂无数据" />
                </div>
              )}
              {!!dataList.length && !loadTheComplete && (
                <div className={styles.load_tip_text}>数据已加载完~</div>
              )}
            </div>
          </Spin>
        </InfiniteScroll>
      </div>
      <Helmet>
        <title>{activityType === '1' ? '拼团' : '秒杀商品'}</title>
      </Helmet>
    </div>
  );
}

export default CouponGoods;
