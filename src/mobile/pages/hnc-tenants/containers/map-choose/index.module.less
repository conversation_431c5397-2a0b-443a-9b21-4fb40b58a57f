


.map {
    :global {
        .amap-logo {
            display: none !important;
        }
        .ant-input,
        .ant-input-affix-wrapper {
            background-color: #ededed;
        }
        .amap-copyright {
            display: none !important;
        }
    }
}
.map {
    height: 100vh;
    position: relative;
}

.box {
    position: relative;
    height: 65vh;
}

// #map-choose {
//     position: relative;
// }

.punctuation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}


.goBack {
    width: 28px;
    height: 28px;
    line-height: 28px;
    background: #fffdff;
    text-align: center;
    cursor: pointer;
    position: absolute;
    bottom: 10%;
    left: 20px;
    border-radius: 4px;
    box-shadow: 2px 2px 2px;
}

.btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: absolute;
    top: 0;
    width: 100%;
    padding: 5% 20px;
    background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.00));
}

.btnCancel {
    color: #fff;
}

.btnAdd {
    padding: 6px 10px;
    border-radius: 4px;
    color: #fff;
    background: #58bd6b;
}

.searchAddress {
    position: fixed;
    bottom: 0px;
    height: calc(35vh + 10px);
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 10px 10px 0 0 ;
    background: #fff;
    padding: 10px 5px 0 5px;
    width: 100%;
}

.chooseAddress {
    height: calc(35vh - 40px);
    overflow-y: auto;
}

.chooseAddress::-webkit-scrollbar {
    display: none;
}

.body {
    position: relative;
    padding-right: 15%;
    padding: 10px 0;
    margin: 0 10px;
    padding-right: 15%;
    border-bottom: 1px solid #e9e9e9;
}

.body:last-child {
    border: none;
}

.addressName {
    font-size: 14px;
    color: #040919;
    margin-bottom: 4px;
    // font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.addressDetail {
    font-size: 12px;
    color: #888b98;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tick {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}