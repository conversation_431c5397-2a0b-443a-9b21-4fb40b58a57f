import { Helmet } from 'react-helmet';
import { useEffect, useRef, useState } from 'react';
import loadMap from '@/utils/map';
// @ts-ignore
import type AMap from 'amap-js-api-typings';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Empty, Search } from '@/components';
import { InputRef } from 'antd';
import { useMemoizedFn } from 'ahooks';
import styles from './index.module.less';

interface GaoDeRes {
  address: string;
  distance: number;
  id: string;
  name: string;
  shopinfo: string;
  tel: string;
  type: string;
  location: {
    lat: number;
    lng: number;
    Q: number;
    R: number;
  };
}

const type =
  '汽车服务|汽车销售|汽车维修|摩托车服务|餐饮服务|购物服务|生活服务|体育休闲服务|医疗保健服务|住宿服务|风景名胜|商务住宅|政府机构及社会团体|科教文化服务|交通设施服务|金融保险服务|公司企业|道路附属设施|地名地址信息|公共设施';

function MapChoose() {
  const mapRef = useRef<AMap>();
  const form: any = localStorage.getItem('HNC_FORM');
  const navigation = useNavigate();
  const inputRef = useRef(null as unknown as InputRef);
  const [userParams] = useSearchParams();
  const tenantId = userParams.get('tenantId') || '';
  const identity = userParams.get('identity') || '';
  const [initPos, setInitPos] = useState<number[]>([116.40396, 39.91512]);
  const [gaoDeList, setGaoDeList] = useState<GaoDeRes[]>([]);
  const [noSetPos, setNoSetPos] = useState(false);
  const [poiId, setPoiId] = useState('');
  const [storageItem, setStorageItem] = useState<GaoDeRes>(null as unknown as GaoDeRes);

  // const getWxLocation = () => {
  //   AMap.plugin(['AMap.Geolocation'], () => {
  //     const options = {
  //       showButton: false, // 是否显示定位按钮
  //       position: 'LB', // 定位按钮的位置
  //       /* LT LB RT RB */
  //       offset: [10, 20], // 定位按钮距离对应角落的距离
  //       showMarker: true, // 是否显示定位点
  //       markerOptions: {
  //         // 自定义定位点样式，同Marker的Options
  //         offset: new AMap.Pixel(-18, -36),
  //         content: ' ',
  //       },
  //       showCircle: true, // 是否显示定位精度圈
  //       circleOptions: {
  //         // 定位精度圈的样式
  //         strokeColor: '#0093FF',
  //         noSelect: true,
  //         strokeOpacity: 0.5,
  //         strokeWeight: 1,
  //         fillColor: '#02B0FF',
  //         fillOpacity: 0.25,
  //       },
  //     };
  //     const geolocation = new AMap.Geolocation(options);
  //     mapRef.current.addControl(geolocation);
  //     geolocation.getCurrentPosition(
  //       (status: string, result: { position: { lng: number; lat: number } }) => {
  //         if (status === 'complete') {
  //           setInitPos([result.position.lng, result.position.lat]);
  //         } else {
  //           message.error('定位失败');
  //         }
  //       }
  //     );
  //   });
  // };

  const onSearch = (val: string, isUpdate?: boolean) => {
    setTimeout(() => {
      AMap.plugin('AMap.PlaceSearch', () => {
        const placeSearch = new AMap.PlaceSearch({
          pageSize: 15,
          pageIndex: 1,
          city: '全国',
          type,
        });
        placeSearch.search(val, (status: any, result: any) => {
          const posi = result.poiList.pois;
          if (!posi.length) return;
          setGaoDeList(posi);
          if (isUpdate && posi.length) {
            const { id } = posi[0];
            setPoiId(id);
            setStorageItem(posi[0]);
          }
        });
      });
    });
  };

  const getGaoDeAddress = (item: number[]) => {
    const geocoder = new window.AMap.Geocoder();
    geocoder.getAddress(
      item,
      (status: string, result: { regeocode: { formattedAddress: any } }) => {
        if (status === 'complete' && result.regeocode) {
          // 获取到了地址信息，可以进行后续操作
          const { formattedAddress } = result.regeocode;
          onSearch(formattedAddress, true);
        }
      }
    );
  };

  const goBack = () => {
    // setNoSetPos(false);
    const params = JSON.parse(form);
    const navigationAddress = params.storesOrHncRefList?.navigationAddress || '';
    mapRef.current.panTo(initPos);
    // eslint-disable-next-line no-unused-expressions
    navigationAddress ? onSearch(navigationAddress, true) : getGaoDeAddress(initPos);
  };

  const setPos = useMemoizedFn(() => {
    if (noSetPos) return;
    const center = mapRef.current.getCenter();
    getGaoDeAddress(center);
  });

  const openSetPos = useMemoizedFn(() => {
    setNoSetPos(false);
  });

  const orientate = (item: GaoDeRes) => {
    setNoSetPos(true);
    setPoiId(item.id);
    const { lng, lat } = item.location;
    setStorageItem(item);
    setTimeout(() => {
      mapRef.current.panTo([lng, lat]);
    });
  };

  const back = () => {
    navigation(`/m/hnc-submit?identity=${identity}&tenantId=${tenantId}`);
  };

  const btnAdd = () => {
    const { lng, lat } = storageItem.location;
    const coordinate = {
      longitude: lng,
      latitude: lat,
    };
    if (form) {
      const formObj = JSON.parse(form);
      const newForm = {
        ...formObj,
        storesOrHncRefList: {
          ...formObj.storesOrHncRefList,
          navigationAddress: storageItem.name,
        },
        coordinate,
      };
      localStorage.setItem('HNC_FORM', JSON.stringify(newForm));
      back();
    }
  };

  const onFocus = () => {
    window.scrollBy(0, 1);
  };

  useEffect(() => {
    loadMap().then(({ Map }) => {
      // 初始化地图
      const mapOption = {
        zoom: 16,
        resizeEnable: true,
      };
      const params = JSON.parse(form);
      const lng = userParams.get('longitude') || 0;
      const lat = userParams.get('latitude') || 0;
      const initPosFoo = lng && lat ? [+lng, +lat] : initPos;
      // @ts-ignore
      mapOption.center = initPosFoo;
      const map = new Map('map-choose', mapOption);
      mapRef.current = map;

      const navigationAddress = params?.storesOrHncRefList?.navigationAddress || '';
      // eslint-disable-next-line no-unused-expressions
      navigationAddress ? onSearch(navigationAddress, true) : getGaoDeAddress(initPosFoo);
      if (lng && lat) {
        setInitPos([+lng, +lat]);
      }

      // if (!lng && !lat) {
      //   setTimeout(() => {
      //     getWxLocation();
      //   });
      // }

      map.on('moveend', setPos);
      map.on('dragstart', openSetPos);
    });

    return () => {
      mapRef.current?.destroy();
    };
  }, []); // eslint-disable-line

  return (
    <div className={styles.map}>
      <div className={styles.box}>
        <div id="map-choose" style={{ height: '65vh' }} />
        <img
          className={styles.punctuation}
          src="https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png"
          alt=""
        />
        <div className={styles.goBack} tabIndex={0} role="button" onClick={() => goBack()}>
          <img src="https://img.huahuabiz.com/user_files/202368/168621412169624.png" alt="" />
        </div>
        <div className={styles.btn}>
          <div className={styles.btnCancel} tabIndex={0} role="button" onClick={back}>
            取消
          </div>
          <div className={styles.btnAdd} tabIndex={0} role="button" onClick={() => btnAdd()}>
            确定
          </div>
        </div>
      </div>
      <div className={styles.searchAddress}>
        <Search
          realTimeSearch
          placeholder="搜索地点"
          ref={inputRef}
          className={styles.searchInput}
          onSearch={onSearch}
          onFocus={onFocus}
        />
        {gaoDeList.length ? (
          <div className={styles.chooseAddress}>
            {gaoDeList.map((item) => (
              <div
                className={styles.body}
                key={item.id}
                tabIndex={0}
                role="button"
                onClick={() => orientate(item)}
              >
                <div className={styles.addressName}>{item.name}</div>
                <div className={styles.addressDetail}>{item.address}</div>
                {poiId === item.id && (
                  <img
                    className={styles.tick}
                    src="https://img.huahuabiz.com/user_files/202368/1686214176249808.png"
                    alt=""
                  />
                )}
              </div>
            ))}
          </div>
        ) : (
          <Empty
            style={{ width: '50%', height: '50%', marginLeft: '25%' }}
            description="请换个关键词搜索"
            className={styles.empty}
          />
        )}
      </div>
      <Helmet>
        <title>选择导航地址</title>
      </Helmet>
    </div>
  );
}

export default MapChoose;
