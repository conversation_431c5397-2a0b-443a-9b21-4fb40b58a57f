.offline {
    padding: 8px 16px;

    :global {
        .ant-input-affix-wrapper,
        .ant-input {
            background: #f5f6fa;
        }
    }


    .drawerContentLeft {
        height: calc(100vh - 48px);
        overflow-y: auto;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    .store {
        // height: 64px;
        padding: 11px 0;
        border-bottom: 1px solid #e9e9e9;
    }
    .store:last-child {
        border-bottom: none;
    }   

    .storeName {
        color: #040919;
        font-size: 17px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .storeAddress {
        color: #888B98;
        font-size: 13px;
        margin-top: 2px;
    }

    .empty {
        margin-top: 30%;
    }

    .textCenter {
        margin-top: 6px;
        text-align: center;
    }

}