import { Empty, Search } from '@/components';
import { Helmet } from 'react-helmet';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { RecordsResult, getSalesOrderList } from '@/apis';
import { useNavigate } from 'react-router-dom';
import styles from './index.module.less';

function OfflineStoreSearch() {
  const navigate = useNavigate();
  const page = useRef({
    pageSize: 15,
    pageNo: 1,
    key: '',
  });
  const [categoryHasMore, setCategoryHasMore] = useState(true);
  const [showEmpty, setShowEmpty] = useState(false);
  const [storeList, setStoreList] = useState<RecordsResult[]>([]);

  const getList = () => {
    getSalesOrderList(page.current).then((res) => {
      const list = res.records.map((item) => {
        const { key } = page.current;
        const buyerCompanyName = key.length
          ? item.buyerCompanyName.replace(
              new RegExp(key, 'g'),
              `<span style="color: #FF2636">${key}</span>`
            )
          : item.buyerCompanyName;

        return {
          ...item,
          key: item.orderNo,
          newBuyerCompanyName: buyerCompanyName,
        };
      });
      setStoreList((val) => [...val, ...list]);
      setShowEmpty(true);
      if (res.records.length < 15) {
        setCategoryHasMore(false);
      }
    });
  };

  const onSearch = (val: string) => {
    page.current.key = val;
    if (page.current.pageNo !== 1) page.current.pageNo = 1;
    if (!categoryHasMore) setCategoryHasMore(true);
    if (storeList.length) setStoreList([]);
    setShowEmpty(false);
    getList();
  };

  const loadMoreCategory = () => {
    page.current.pageNo += 1;
    getList();
  };

  const saveForm = (item: RecordsResult) => {
    const form = localStorage.getItem('HNC_FORM');
    if (form) {
      const formObj = JSON.parse(form);
      formObj.storesOrHncRefList = {
        ...formObj.storesOrHncRefList,
        storeName: item.buyerCompanyName,
        storeProvince: '广东省深圳市',
        storeProjectArea: '龙岗区',
        storeFloor: '27搂',
        storeBuilding: 'A栋',
        storeNumber: 'A-01',
      };
      localStorage.setItem('HNC_FORM', JSON.stringify(formObj));
      navigate('/m/hnc-submit');
    }
  };

  useEffect(() => {
    getList();
  }, []); // eslint-disable-line

  return (
    <div className={styles.offline}>
      <Search
        placeholder="请输入关键字"
        onSearch={(word) => {
          onSearch(word);
        }}
      />
      <div className={styles.drawerContentLeft} id="drawerContentLeft">
        <InfiniteScroll
          dataLength={storeList.length}
          next={loadMoreCategory}
          hasMore={categoryHasMore}
          loader={
            <div className={styles.textCenter}>
              <Spin tip="加载中..." />
            </div>
          }
          scrollableTarget="drawerContentLeft"
        >
          {storeList.length ? (
            <>
              {storeList.map((item) => (
                <div
                  className={styles.store}
                  key={item.orderNo}
                  tabIndex={0}
                  role="button"
                  onClick={() => saveForm(item)}
                >
                  <div
                    className={styles.storeName}
                    // @ts-ignore
                    // eslint-disable-next-line react/no-danger
                    dangerouslySetInnerHTML={{ __html: item.newBuyerCompanyName }}
                  />
                  <div className={styles.storeAddress}>{item.sellerCompanyName}</div>
                </div>
              ))}
            </>
          ) : (
            <div>
              {showEmpty && (
                <Empty
                  message="暂无相关店铺"
                  description="请联系客服400-XXXXXXX"
                  className={styles.empty}
                />
              )}
            </div>
          )}
        </InfiniteScroll>
      </div>
      <Helmet>
        <title>选择线下店铺名称</title>
      </Helmet>
    </div>
  );
}

export default OfflineStoreSearch;
