import { Helmet } from 'react-helmet';
import { useSearchParams } from 'react-router-dom';
import { useEffect } from 'react';
import { setToken } from '@/utils/auth';
import styles from './index.module.less';
import IdentityCard from './components/identity-card/identity-card';

function HncTenants() {
  const [searchParams] = useSearchParams();

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, [searchParams]);

  return (
    <div className={styles.tenants}>
      <Helmet>
        <title>商家入驻</title>
      </Helmet>
      <IdentityCard />
    </div>
  );
}

export default HncTenants;
