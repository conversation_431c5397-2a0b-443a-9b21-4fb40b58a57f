import Icon from '@/components/icon';
import { useNavigate, useSearchParams } from 'react-router-dom';
import style from './identity-card.module.less';
import Card from '../card/card';

// eslint-disable-next-line no-unused-vars
function IdentityCard() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '4aa5d800-94c9-463f-97b9-a4de3187286d';

  const title = [{ name: '', start: 0, end: 2 }];

  const identitys = [
    {
      label: '华南城商家入驻',
      key: 6,
      text: '轻松入驻，为门店引流',
      photo:
        'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202329/1675935141822804.png',
    },
    {
      label: '门店商家',
      key: 7,
      text: '增加实体门店线上曝光',
      photo:
        'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202329/1675935141822804.png',
    },
  ];

  const gotoNav = (key: number) => {
    localStorage.removeItem('HNC_FORM');
    navigate(`/m/hnc-submit?identity=${key}&tenantId=${tenantId}`);
  };

  return (
    <>
      {title.map((item) => (
        <div key={item.name}>
          <div className={style.company}>{item.name}</div>
          <Card>
            {identitys.slice(item.start, item.end).map((it) => (
              <div
                className={style.identityItem}
                key={it.key}
                role="button"
                tabIndex={0}
                onClick={() => {
                  gotoNav(it.key);
                }}
              >
                <div className={style.chooseLeft}>
                  {/* <div>
                    <img className={style.photo} src={it.photo} alt="" />
                  </div> */}
                  <div className={style.textBody}>
                    <div className={style.label}>{it.label}</div>
                    <div className={style.text}>{it.text}</div>
                  </div>
                </div>
                <Icon name="right" size={16} />
              </div>
            ))}
          </Card>
        </div>
      ))}
    </>
  );
}

export default IdentityCard;
