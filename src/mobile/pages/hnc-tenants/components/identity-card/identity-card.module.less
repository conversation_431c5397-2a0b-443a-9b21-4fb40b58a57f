.identityItem {
  display: flex;
  padding: 16px 0;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f3f3;

  &:last-child {
    padding-bottom: 0;
    border-bottom: none;
  }

  &:first-child {
    padding-top: 0;
  }

  .label {
    color: #040919;
    font-size: 17px;
    margin-bottom: 4px;
  }

  .chooseLeft {
    display: flex;
    align-items: center;

    .photo {
      min-width: 40px;
      max-width: 40px;
      min-height: 40px;
      max-height: 40px;
      border-radius: 50%;
    }
  }

  .textBody {
    // margin-left: 12px;

    .text {
      color: #888b98;
      font-size: 13px;
    }
  }
}

.company {
  color: #888b98;
  font-size: 14px;
  margin-top: 20px;
  margin-bottom: 6px;
  padding: 0 16px;
}
