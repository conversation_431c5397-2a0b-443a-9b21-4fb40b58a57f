import { Form, FormInstance, Input } from 'antd';
import {
  MutableRefObject,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { FileListItem, SimpleUploadInstance, Upload } from '@/components';
import { getProvinceCityCounty } from '@/apis';
import { ProvinceCityCounty } from '@/apis/get-province-city-county';
import getMallCategoryTreeList, {
  mallTreeListParams,
} from '@/apis/gmall/get-mall-category-tree-list';
import Cascader from 'antd/es/cascader';
// import { UploadFile } from '@/utils/file';
import classNames from 'classnames';
import Card from '../card/card';
import style from '../form-card/form-card.module.less';

interface StoresOrHncProps {
  type: string;
  tenantId: string;
  // eslint-disable-next-line no-unused-vars
  jumpUrl?: (name: string) => void;
}

interface StoresOrHncRef {
  formRef: MutableRefObject<FormInstance>;
  uploadRef: MutableRefObject<SimpleUploadInstance>;
}

interface formDataParams {
  navigationAddress: string;
  storeAddress: string;
  storeName: string;
  storeProvince: string;
  storeProjectArea: string;
  storeFloor: string;
  storeBuilding: string;
  storeNumber: string;
  businessScope: string;
  storeIntroduce: string;
  storeImages: string[];
}

const StoresOrHnc = forwardRef<StoresOrHncRef, StoresOrHncProps>(
  ({ type, tenantId, jumpUrl }, ref) => {
    const formRef = useRef(null as unknown as FormInstance);
    const uploadRef = useRef(null as unknown as SimpleUploadInstance);
    const [formData, setFormData] = useState({} as unknown as formDataParams);
    const [fileList] = useState([] as unknown as FileListItem[]);
    const [cityList, setCityList] = useState<ProvinceCityCounty[]>([]);
    const [shopList, setShopList] = useState<mallTreeListParams[]>([]);

    useImperativeHandle(
      ref,
      () => ({
        formRef,
        uploadRef,
      }),
      []
    );

    // const beforeUpload = (file: UploadFile) => {
    //   setFileList((val) => [...val, file]);
    //   return true;
    // };

    const open = (name: string) => {
      if (jumpUrl) {
        jumpUrl(name);
      }
    };

    const getCityCounty = () => {
      getProvinceCityCounty().then((res) => {
        setCityList(res.list);
      });
    };

    const getScopeBusiness = () => {
      getMallCategoryTreeList({ tenantId }).then((res) => {
        setShopList(res.list);
      });
    };

    useEffect(() => {
      getCityCounty();
      getScopeBusiness();
      setTimeout(() => {
        const HNC_FORM = localStorage.getItem('HNC_FORM');
        if (HNC_FORM) {
          const params = JSON.parse(HNC_FORM);
          const storesOrHncRefList = params.storesOrHncRefList || {};
          setFormData((val) => ({
            ...val,
            ...storesOrHncRefList,
          }));
        }
      });
    }, []); // eslint-disable-line

    const hnc = (
      <Card style={{ padding: '16px 16px 1px 16px' }}>
        <div className={style.title}>关联华南城线下门店</div>
        <div className={classNames(style.fixForm)}>
          <Form layout="horizontal" ref={formRef} style={{ marginLeft: '-8px' }}>
            <Form.Item
              label="导航地址"
              name="navigationAddress"
              rules={[{ required: true, message: '请选择导航地址' }]}
            >
              <div className={style.nav} tabIndex={0} role="button" onClick={() => open('address')}>
                {formData.navigationAddress ? (
                  <span className={style.navAddress} style={{ color: '#000' }}>
                    {formData.navigationAddress}
                  </span>
                ) : (
                  <span className={style.navAddress}>请选择导航地址</span>
                )}
                <img
                  src="https://img.huahuabiz.com/user_files/202362/1685692427173730.png"
                  alt=""
                />
              </div>
            </Form.Item>
            <Form.Item
              label="线下店铺名称"
              name="storeName"
              rules={[{ required: true, message: '请输入线下店铺名称' }]}
            >
              {/* <div
              className={style.nav}
              tabIndex={0}
              role="button"
              onClick={() => open('offline-store')}
            >
              {formData.storeName ? (
                <span className={style.navAddress} style={{ color: '#000' }}>
                  {formData.storeName}
                </span>
              ) : (
                <span className={style.navAddress}>请选择线下店铺名称</span>
              )}
              <img src="https://img.huahuabiz.com/user_files/202365/1685950484491562.png" alt="" />
            </div> */}

              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入线下店铺名称"
              />
            </Form.Item>
            <Form.Item
              label="省市区"
              name="storeProvince"
              rules={[{ required: true, message: '请选择省市区' }]}
            >
              {/* <div className={style.nav}>
                  {formData.storeProvince ? (
                    <span className={style.navAddress} style={{ color: '#000' }}>
                      {formData.storeProvince}
                    </span>
                  ) : (
                    <span className={style.navAddress}>请选择线下店铺名称</span>
                  )}
                </div> */}
              <Cascader
                style={{ width: '100%' }}
                options={cityList}
                placeholder="请选择省市区"
                fieldNames={{
                  label: 'label',
                  value: 'label',
                  children: 'children',
                }}
              />
            </Form.Item>
            <Form.Item
              label="项目区域"
              name="storeProjectArea"
              rules={[{ required: true, message: '请输入项目区域' }]}
            >
              {/* <div className={style.nav}>
              {formData.storeProjectArea ? (
                <span className={style.navAddress} style={{ color: '#000' }}>
                  {formData.storeProjectArea}
                </span>
              ) : (
                <span className={style.navAddress}>请选择线下店铺名称</span>
              )}
            </div> */}
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入项目区域"
              />
            </Form.Item>
            <Form.Item
              label="楼层"
              name="storeFloor"
              rules={[{ required: true, message: '请输入楼层' }]}
            >
              {/* <div className={style.nav}>
              {formData.storeFloor ? (
                <span className={style.navAddress} style={{ color: '#000' }}>
                  {formData.storeFloor}
                </span>
              ) : (
                <span className={style.navAddress}>请选择线下店铺名称</span>
              )}
            </div> */}
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入楼层"
              />
            </Form.Item>
            <Form.Item
              label="楼栋"
              name="storeBuilding"
              rules={[{ required: true, message: '请输入楼栋' }]}
            >
              {/* <div className={style.nav}>
              {formData.storeBuilding ? (
                <span className={style.navAddress} style={{ color: '#000' }}>
                  {formData.storeBuilding}
                </span>
              ) : (
                <span className={style.navAddress}>请选择线下店铺名称</span>
              )}
            </div> */}
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入楼栋"
              />
            </Form.Item>
            <Form.Item
              label="房号"
              name="storeNumber"
              rules={[{ required: true, message: '请输入房号' }]}
            >
              {/* <div className={style.nav}>
              {formData.storeNumber ? (
                <span className={style.navAddress} style={{ color: '#000' }}>
                  {formData.storeNumber}
                </span>
              ) : (
                <span className={style.navAddress}>请选择线下店铺名称</span>
              )}
            </div> */}
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入房号"
              />
            </Form.Item>
            <Form.Item
              label="经营范围"
              name="businessScope"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Cascader
                style={{ width: '100%' }}
                options={shopList}
                multiple
                getPopupContainer={(getPopupContainer) => getPopupContainer.parentNode}
                showCheckedStrategy="SHOW_CHILD"
                maxTagCount="responsive"
                placeholder="请选择经营范围"
                fieldNames={{
                  label: 'name',
                  value: 'id',
                  children: 'childrenList',
                }}
              />
            </Form.Item>
            <Form.Item
              label="店铺介绍"
              name="storeIntroduce"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入店铺介绍，包括经营范围，所售物品种类，主营业务......"
              />
            </Form.Item>
            <Form.Item
              label="店铺门头照"
              name="storeImages"
              rules={[{ required: true, message: '请输入联系人' }]}
            >
              <Upload
                fileList={fileList}
                className={style.upload}
                ref={uploadRef}
                listType="card"
                maxCount={6}
                multiple
                onChange={() => {}}
                listClassName={style.listClassName}
              />
            </Form.Item>
          </Form>
        </div>
      </Card>
    );

    const stores = (
      <Card style={{ padding: '16px 16px 1px 16px' }}>
        {/* <div className={style.title}>关联华南城线下门店</div> */}
        <div className={classNames(style.fixForm)}>
          <Form layout="horizontal" ref={formRef} style={{ marginLeft: '-8px' }}>
            <Form.Item
              label="导航地址"
              name="navigationAddress"
              rules={[{ required: true, message: '请选择导航地址' }]}
            >
              <div className={style.nav} tabIndex={0} role="button" onClick={() => open('address')}>
                {formData.navigationAddress ? (
                  <span className={style.navAddress} style={{ color: '#000' }}>
                    {formData.navigationAddress}
                  </span>
                ) : (
                  <span className={style.navAddress}>请选择导航地址</span>
                )}
                <img
                  src="https://img.huahuabiz.com/user_files/202362/1685692427173730.png"
                  alt=""
                />
              </div>
            </Form.Item>
            <Form.Item
              label="线下店铺名称"
              name="storeName"
              rules={[{ required: true, message: '请输入线下店铺名称' }]}
            >
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入线下店铺名称"
              />
            </Form.Item>
            <Form.Item
              label="店铺地址"
              name="storeAddress"
              rules={[{ required: true, message: '请输入店铺地址' }]}
            >
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入店铺地址"
              />
            </Form.Item>
            <Form.Item
              label="经营范围"
              name="businessScope"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Cascader
                style={{ width: '100%' }}
                options={shopList}
                getPopupContainer={(getPopupContainer) => getPopupContainer.parentNode}
                multiple
                showCheckedStrategy="SHOW_CHILD"
                maxTagCount="responsive"
                placeholder="请选择经营范围"
                fieldNames={{
                  label: 'name',
                  value: 'id',
                  children: 'childrenList',
                }}
              />
            </Form.Item>
            <Form.Item
              label="店铺介绍"
              name="storeIntroduce"
              rules={[{ required: true, message: '请填写店铺介绍' }]}
            >
              <Input.TextArea
                bordered={false}
                autoSize={{ minRows: 1, maxRows: 5 }}
                placeholder="请输入店铺介绍，包括经营范围，所售物品种类，主营业务......"
              />
            </Form.Item>
            <Form.Item
              label="店铺门头照"
              name="storeImages"
              rules={[{ required: true, message: '请输入联系人' }]}
            >
              <Upload
                className={style.upload}
                fileList={fileList}
                ref={uploadRef}
                listType="card"
                maxCount={6}
                multiple
                listClassName={style.listClassName}
                onChange={() => {}}
              />
            </Form.Item>
          </Form>
        </div>
      </Card>
    );

    return type === '6' ? hnc : stores;
  }
);

StoresOrHnc.defaultProps = {
  jumpUrl: () => {},
};

export default StoresOrHnc;
