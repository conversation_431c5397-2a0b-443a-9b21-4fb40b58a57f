import { useState, useRef, useEffect, MutableRefObject } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useMount } from 'ahooks';
import debounce from 'lodash/debounce';
import {
  queryCompanyList,
  ApplyMallParam,
  getCompanyStatus,
  getCompanyShop,
  applyMall,
  applyGroup,
  GetCategoryResult,
  createCompanyTeam,
  getCompanyCategories,
  getFormTemplateComponents,
} from '@/apis';
import { isPhone } from '@/utils/utils';
import { isWeb } from '@/utils/js-bridge';
import { IShop } from '@/apis/mcs/get-company-shop';
import { ICompany } from '@/apis/user/query-company-list';
import { FormRender, FormRenderInstance } from '@/src/form-engine/containers';
import {
  Select,
  Form,
  FormInstance,
  Modal,
  message,
  Input,
  Drawer,
  <PERSON>r,
  <PERSON><PERSON>,
  Spin,
} from 'antd';
import { Icon, SimpleUploadInstance } from '@/components';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import classNames from 'classnames';
import { showLoading, hideLoading } from '../../../../components/loading';
import StoresOrHnc from '../stores-or-hnc/stores-or-hnc';
import Card from '../card/card';
import Company from '../../../login/images/company.png';
import CheckCircle from '../../../login/images/checkCircle.png';
import style from './form-card.module.less';

const { Option } = Select;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
declare let wx: any;
let globalCategories = null as null | GetCategoryResult[];

interface StoresOrHncRef {
  formRef: MutableRefObject<FormInstance>;
  uploadRef: MutableRefObject<SimpleUploadInstance>;
}

// const certificateList = [
//   { name: '身份证', id: 1 },
//   { name: '港澳通行证', id: 2 },
//   { name: '护照', id: 3 },
//   { name: '军官证', id: 4 },
// ];

function FormCard() {
  const navigation = useNavigate();
  const [searchParams] = useSearchParams();
  // const tenantId = searchParams.get('tenantId') || '';
  const tenantId = searchParams.get('tenantId') || '';
  const storesOrHncType = searchParams.get('identity') || '6';
  const mallId = Number(searchParams.get('id')) || null;
  const isApplet = searchParams.get('isApplet') === '1';

  const [creatForm] = Form.useForm();
  const formRef = useRef(null as unknown as FormInstance);
  const storesOrHncRef = useRef(null as unknown as StoresOrHncRef);
  const formRenderRef = useRef(null as unknown as FormRenderInstance);

  const [isDisabled, setIsDisabled] = useState(true);
  const [btnLoading, setBtnLoading] = useState(false);
  const [isSubmit, setSubmitStatus] = useState(false);
  const [isCreateTeamShow, setIsCreateTeamShow] = useState(false);

  const [companyList, setCompanyList] = useState([] as unknown as ICompany[]);
  const [shopList, setShopList] = useState([] as unknown as IShop[]);
  const [formParams, setFormParams] = useState({} as unknown as ApplyMallParam);
  const [categories, setCategories] = useState(globalCategories);
  const [formRenderData, setFormRenderData] = useState<{
    formCode: string;
    versionNumber: number;
    components: any[];
  }>({
    formCode: '',
    versionNumber: 0,
    components: [],
  });

  const closeCreateDrawer = () => {
    creatForm.resetFields();
    setIsCreateTeamShow(false);
  };

  const getQueryCompanyList = () => {
    queryCompanyList({}).then((res) => {
      const list = res.list.filter((item) => item.companyName !== '个人账号');
      setCompanyList(list);
    });
  };

  // 获取公司下的店铺
  const getCompanyShops = (id: number) => {
    getCompanyShop(id).then((res) => {
      setShopList([res]);
    });
  };

  const back = () => {
    if (wx) {
      window.wx.miniProgram.switchTab({
        url: `/pages/my/index`,
      });
    }
  };

  const onValuesChange = () => {
    const formInfo = creatForm.getFieldsValue();
    if (formInfo.companyName && formInfo.industry) {
      setIsDisabled(false);
      return;
    }
    setIsDisabled(true);
  };

  const typeName = (ty: number) => {
    switch (ty) {
      case 1:
        return '企业商户';
      case 3:
        return '设计公司';
      case 4:
        return '装修公司';
      case 5:
        return '城市运营商';
      case 6:
        return '华南城商户';
      case 7:
        return '门店商户';
      case 10:
        return '企业商户';
      case 50:
        return '独立工长';
      case 51:
        return '城市团长';
      case 52:
        return '独立设计师';
      default:
        return '';
    }
  };

  const getFormData = () => {
    getFormTemplateComponents({ formCode: 'FORMoPrx5ES1667986343654' }).then((res) => {
      setFormRenderData(res);
    });
  };

  const onCompanyChange = (id: number, e: { label: string } | { label: string }[]) => {
    const enterCompanyLabel = e as { label: string; value: string };
    if (enterCompanyLabel.label === 'noCompany' && enterCompanyLabel.value === 'noCompany') {
      formRef.current.resetFields(['enterCompanyId']);
      return;
    }
    if (
      enterCompanyLabel.label === 'createCompany' &&
      enterCompanyLabel.value === 'createCompany'
    ) {
      formRef.current.resetFields(['enterCompanyId']);
      setIsCreateTeamShow(true);
      return;
    }
    formRef.current.resetFields(['shopId']);
    getCompanyStatus({
      enterCompanyId: id,
      mallId: mallId as number,
      isApplet,
      type: Number(storesOrHncType),
    }).then((res) => {
      const detail = e as { label: string };
      // const identityMap = ['企业商户', '设计公司', '装修公司', '城市运营商'];
      if (res.isAdd) {
        message.info('你已成功入驻，无需重复申请');
        formRef.current.resetFields(['enterCompanyId', 'shopId']);
      } else {
        if (res.verifyStatus === 20) {
          Modal.confirm({
            title: '提示',
            icon: '',
            content: `【${detail.label}】已入驻，入驻身份为【${typeName(
              res.type
            )}】，将直接提交入群申请，是否确认？`,
            okText: '确定',
            cancelText: '取消',
            onOk: () => {
              applyGroup({ enterCompanyId: id, mallId: mallId as number, isApplet }).then(() => {
                message.success('提交成功，请耐心等待审核结果');
                setTimeout(() => {
                  back();
                }, 3000);
              });
            },
            onCancel: () => {
              formRef.current.resetFields(['enterCompanyId', 'shopId']);
            },
          });
        }
        if (res.verifyStatus === 10) {
          message.info('该公司入驻申请审核中，请耐心等待');
          formRef.current.resetFields(['enterCompanyId', 'shopId']);
          return;
        }
        getCompanyShops(id);
        setFormParams((par) => ({
          ...par,
          enterCompanyId: id,
          shopId: null as unknown as number,
        }));
      }
    });
  };

  const requireApplyMall = (params: ApplyMallParam) => {
    if (!isPhone(params.phone)) {
      message.error('手机号校验失败或不合法');
      return;
    }
    showLoading();
    applyMall(params, { tenantId })
      .then(() => {
        message.success('提交成功，请耐心等待审核结果');
        setTimeout(() => {
          back();
        }, 2000);
      })
      .finally(() => {
        hideLoading();
      });
  };

  // 提交
  const submit = () => {
    setSubmitStatus(true);
    setBtnLoading(false);
    formRef.current.validateFields().then((values) => {
      const { enterCompanyId, shopId, contact, phone } = values;
      const params = {
        enterCompanyId,
        shopId,
        contact,
        phone,
        type: Number(storesOrHncType),
        formCode: 'FORMoPrx5ES1667986343654',
      };
      storesOrHncRef.current.formRef.current.validateFields().then((val) => {
        const newParams = {
          ...params,
        } as ApplyMallParam;
        const hncForm = JSON.parse(localStorage.getItem('HNC_FORM') || '{}');
        newParams.physicalStoreInfoParam = {
          ...val,
          businessScope: val.businessScope.map((m: number[]) => m[m.length - 1]),
          storeProvince: val.storeProvince?.join('、') || '',
          longitude: hncForm.coordinate.longitude || 0,
          latitude: hncForm.coordinate.latitude || 0,
        };

        formRenderRef.current
          .submit()
          .then(
            (data: {
              values: Record<string, unknown>;
              version: number;
              labels: Record<string, string>;
            }) => {
              const mchExtendParams = [] as {
                label: string;
                colName: string;
                colValue: string;
              }[];
              Object.keys(data.values).forEach((key: string) => {
                mchExtendParams.push({
                  colName: key,
                  colValue: data.values[key] as string,
                  label: data.labels[key],
                });
              });
              newParams.versionNumber = formRenderData.versionNumber;
              newParams.mchExtendParams = mchExtendParams;
              requireApplyMall(newParams);
            }
          );
      });
    });
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onCreatFinish = debounce((value: any) => {
    if (!categories) return;
    const { industry } = value;
    const parent = categories.find((cate) => cate.value === industry[0]);
    if (!parent) return;
    const child = (parent.children || []).find((cate) => cate.value === industry[1]);
    if (!child) return;
    createCompanyTeam({
      companyName: value.companyName,
      industry: `${parent.label}/${child.label}`,
    }).then(() => {
      setCompanyList([]);
      closeCreateDrawer();
      getQueryCompanyList();
      message.open({
        content: (
          <div>
            <img src={CheckCircle} alt="" className={style.checkCircleIcon} />
            创建成功
          </div>
        ),
      });
    });
  }, 500);

  const setHncForm = () => {
    const formRefList = formRef.current.getFieldsValue();
    const storesOrHncRefList = storesOrHncRef.current.formRef.current.getFieldsValue();
    const certificateFormRefList = formRenderRef.current.form.getFieldsValue();
    const params = {
      formRefList,
      storesOrHncRefList,
      certificateFormRefList,
      coordinate: {
        latitude: 0,
        longitude: 0,
      },
    };
    const form = localStorage.getItem('HNC_FORM');
    if (form) {
      params.coordinate = JSON.parse(form).coordinate;
    }
    localStorage.setItem('HNC_FORM', JSON.stringify(params));
  };

  const getHncForm = () => {
    const form = localStorage.getItem('HNC_FORM');
    if (form) {
      const params = JSON.parse(form);
      formRef.current.setFieldsValue(params.formRefList);
      storesOrHncRef.current.formRef.current.setFieldsValue(params.storesOrHncRefList);
      setTimeout(() => {
        formRenderRef.current.form.setFieldsValue(params.certificateFormRefList);
      }, 4500);
      if (params.formRefList.enterCompanyId) {
        getCompanyShops(params.formRefList.enterCompanyId);
      }
    }
  };

  const jumpUrl = (name: string) => {
    switch (name) {
      case 'offline-store':
        navigation('/m/offline-store-search');
        break;
      case 'address':
        // eslint-disable-next-line no-case-declarations
        const form = localStorage.getItem('HNC_FORM');
        // eslint-disable-next-line no-case-declarations
        const url = `?tenantId=${tenantId}&identity=${storesOrHncType}`;
        if (form) {
          const params = JSON.parse(form);
          const { coordinate } = params;
          if (coordinate.latitude && coordinate.longitude) {
            navigation(
              `/m/map-choose?latitude=${coordinate.latitude}&longitude=${coordinate.longitude}`
            );
          } else {
            window.wx.miniProgram.navigateTo({
              url: `/subpackages/other/pages/get-wx-location/index${url}`,
            });
          }
        } else {
          window.wx.miniProgram.navigateTo({
            url: `/subpackages/other/pages/get-wx-location/index${url}`,
          });
        }
        break;
      default:
        break;
    }
    setHncForm();
  };

  useMount(() => {
    getQueryCompanyList();
  });

  useEffect(() => {
    if (globalCategories) return;
    getCompanyCategories().then(({ list = [] }) => {
      globalCategories = list.map((item) => {
        if (item.children && item.children.length > 0) {
          item.children.forEach((child) => {
            // eslint-disable-next-line no-param-reassign
            child.children = undefined;
          });
        }
        return item;
      });
      setCategories(globalCategories);
    });
  }, []);

  useEffect(() => {
    getHncForm();
    getFormData();
  }, []); // eslint-disable-line

  return (
    <div className={classNames(style.formCard, style.fixForm)}>
      <Card style={{ padding: '16px 16px 1px 16px' }}>
        <div className={style.title}>商户入驻申请资料</div>
        <div className={classNames(style.fixForm, isSubmit && style.submited)}>
          <Form layout="horizontal" ref={formRef} style={{ marginLeft: '-8px' }}>
            <Form.Item
              label="企业名称"
              name="enterCompanyId"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Select
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                placeholder="请选择企业"
                bordered={false}
                onChange={onCompanyChange}
              >
                {companyList.length ? (
                  companyList.map((item) => (
                    <Option value={item.companyId} label={item.companyName} key={item.companyId}>
                      <div className={style.companyList}>
                        <span className={style.companyName}>{item.companyName}</span>
                        <img
                          style={{ width: '16px', height: '16px', marginLeft: '4px' }}
                          src={
                            item.status === 1
                              ? 'https://img.huahuabiz.com/user_files/2023320/1679303518372975.svg'
                              : 'https://img.huahuabiz.com/user_files/2023320/1679303523390407.svg'
                          }
                          alt=""
                        />
                      </div>
                    </Option>
                  ))
                ) : (
                  <Option value="noCompany" label="noCompany" style={{ background: '#fff' }}>
                    <div className={style.noCompanyBox}>
                      <div>您暂未创建组织</div>
                      <div>点击下方按钮立即创建</div>
                    </div>
                  </Option>
                )}
                <Option value="createCompany" label="createCompany">
                  <div className={style.createCompanyBox}>
                    <div>
                      <img src={Company} alt="" className={style.companyIcon} />
                    </div>
                    <div>创建组织</div>
                  </div>
                </Option>
              </Select>
            </Form.Item>
            <Form.Item
              label="店铺名称"
              name="shopId"
              rules={[{ required: true, message: '请选择' }]}
            >
              <Select
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                placeholder="请选择店铺"
                value={formParams.shopId}
                bordered={false}
                notFoundContent={<div>暂无数据，请先选择企业</div>}
                onChange={(val) => {
                  setFormParams((par) => ({ ...par, shopId: val }));
                }}
              >
                {shopList.map((item) => (
                  <Option value={item.id} label={item.name} key={item.id}>
                    <div className="option-label-item">{item.name}</div>
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              label="联系人"
              name="contact"
              rules={[{ required: true, message: '请输入联系人' }]}
            >
              <Input
                bordered={false}
                placeholder="请输入联系人"
                onChange={(val) => {
                  setFormParams((par) => ({ ...par, contact: val.target.value }));
                }}
              />
            </Form.Item>
            <Form.Item
              label="手机号码"
              name="phone"
              rules={[
                { required: true, message: '' },
                {
                  validator(_rule, value) {
                    if (isPhone(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('手机号校验失败或不合法'));
                  },
                },
              ]}
            >
              <Input
                bordered={false}
                placeholder="请输入手机号码"
                type="tel"
                maxLength={11}
                onChange={(val) => {
                  setFormParams((par) => ({ ...par, phone: val.target.value }));
                }}
              />
            </Form.Item>
          </Form>
        </div>
      </Card>

      {/* 门店或华南城表单 */}
      <StoresOrHnc
        tenantId={tenantId}
        jumpUrl={jumpUrl}
        ref={storesOrHncRef}
        type={storesOrHncType}
      />

      <Card style={{ padding: '16px 16px 1px 16px' }}>
        <div className={classNames(style.fixForm, isSubmit && style.submited)}>
          <div className={style.formRender}>
            <FormRender
              theme={false}
              isMobile
              layout="horizontal"
              formCode="FORMoPrx5ES1667986343654"
              ref={formRenderRef}
            />
          </div>
        </div>
      </Card>

      <div className={style.submitBox}>
        <Spin spinning={btnLoading}>
          <div
            className={style.submitBtn}
            role="button"
            tabIndex={0}
            onClick={submit}
            style={{ background: isWeb ? '#0099ff' : '#ff5923' }}
          >
            提交申请
          </div>
        </Spin>
      </div>
      <Drawer
        placement="bottom"
        closable={false}
        onClose={() => closeCreateDrawer()}
        visible={isCreateTeamShow}
        height="80%"
      >
        <div className={style.drawerBox}>
          <div className={style.drawerTitleBox}>
            <div />
            <div className={style.drawerTitle}>创建团队</div>
            <CloseOutlined className={style.drawerIcon} onClick={() => closeCreateDrawer()} />
          </div>
          <div className={style.drawerDescribe}>填写团队/企业信息，开启高效协作</div>
        </div>
        <div className={style.drawerForm}>
          <Form form={creatForm} onFinish={onCreatFinish} onValuesChange={onValuesChange}>
            <div className={style.formBox}>
              <div className={style.formLabel}>组织名称</div>
              <div className={style.formValue}>
                <Form.Item name="companyName" rules={[{ required: true, message: '' }]}>
                  <Input maxLength={50} bordered={false} placeholder="请输入组织名称" />
                </Form.Item>
              </div>
            </div>

            <div className={style.formBox}>
              <div className={style.formLabel}>行业类型</div>
              <div className={style.formValue}>
                <Form.Item name="industry" rules={[{ required: true, message: '' }]}>
                  <Cascader
                    bordered={false}
                    options={categories || []}
                    placeholder="请选择行业类型"
                    loading={!categories}
                    allowClear={false}
                    showSearch
                    suffixIcon={<Icon name="down" size={16} />}
                  />
                </Form.Item>
              </div>
            </div>

            {isCreateTeamShow && (
              <div className={style.btnBox}>
                <Form.Item wrapperCol={{ span: 16 }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    block
                    // eslint-disable-next-line no-nested-ternary
                    style={{ background: isDisabled ? '#c6ccd8' : isWeb ? '#0099ff' : '#ff5923' }}
                    disabled={isDisabled}
                  >
                    确定
                  </Button>
                </Form.Item>
              </div>
            )}
          </Form>
        </div>
      </Drawer>
    </div>
  );
}

export default FormCard;
