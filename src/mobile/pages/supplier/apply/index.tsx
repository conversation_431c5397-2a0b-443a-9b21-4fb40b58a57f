import React, { useEffect, useState, useRef, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Form, Input, Cascader, Button, Row, message, Spin } from 'antd';
import { Icon } from '@/components';
import FromItemTags from '@@/mobile/components/tags/tags';
import debounce from 'lodash/debounce';
import type { DefaultOptionType } from 'antd/es/select';
import flattenDeep from 'lodash/flattenDeep';
import {
  getFormTemplateComponents,
  getProvinceCityCounty,
  getSupplierClassList,
  filledSupplierInfo,
  getSupplierDetail,
  getSupplierData,
  SupplierParams,
  recordParams,
  SupplierClassifyListResult,
  getCompanyInfo,
  getCompanyUser,
} from '@/apis';
import { GetCompanyInfoResult } from '@/apis/user/get-company-info';
import isString from 'lodash/isString';
// import { UserItem } from '@/pages/home/<USER>/select-users/util';
// import { selectUsers } from '@/pages/home/<USER>';
import { FormRender, FormRenderInstance } from '@@/form-engine/containers';
import { Helmet } from 'react-helmet';
import SupplierDetail from '../detail';
import styles from './index.module.less';

const toJSON = (data: unknown) => {
  if (isString(data) && /^(\[|\{).*(]|})$/.test(data)) {
    try {
      return JSON.parse(data);
    } catch (e) {
      console.warn(e);
    }
  }
  return data;
};

const getNoData = (type: string) => {
  if (type === 'image') {
    return [];
  }
  return '';
};

const toCategoryClass = (tree: any) => {
  const res: any = [];
  const recursiveFn = (list: any[], arr: any[]) => {
    list.forEach((item) => {
      const ids = [...arr, item.id];
      if (item.childList.length) {
        recursiveFn(item.childList, ids);
      } else {
        res.push(ids);
      }
    });
  };
  recursiveFn(tree, []);
  return JSON.stringify(res);
};

function SupplierApply() {
  const [form] = Form.useForm();
  const refForm = useRef(null as unknown as FormRenderInstance);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const companyId = Number(searchParams.get('companyId'));
  const customerCompanyId = Number(searchParams.get('customerCompanyId'));
  const currentUseId = Number(searchParams.get('useId'));
  const [loading, setLoading] = useState(false);
  const [addressList, setAddressList] = useState<recordParams[]>([]);
  const [classifyList, setClassifyList] = useState<SupplierClassifyListResult[]>([]);
  const [items, setItems] = useState<any>([]);
  const [address, setAddress] = useState<DefaultOptionType[]>([]);
  const [filledData, setFilledData] = useState([]);
  const [customerInfo, setCustomerInfo] = useState({} as unknown as SupplierParams);
  const [canSubmit, setCanSubmit] = useState<boolean>(false);
  const [canEdit, setCanEdit] = useState<boolean>(true);
  const [members, setMembers] = useState<any>([]);
  const [currentCompany, setCurrentCompany] = useState({} as GetCompanyInfoResult);
  const [loginMemberId, setLoginMemberId] = useState(0);

  // 添加联系人
  const addMembers = useCallback(() => {
    navigate('/m/select/user');
    localStorage.setItem('SELECT_USERS', JSON.stringify(members));
    localStorage.setItem('FORM_INFO', JSON.stringify(form.getFieldsValue()));
  }, [form, members, navigate]);

  // 删除联系人
  const deleteMember = useCallback(
    (member: any, index: number) => {
      members.splice(index, 1);
      setMembers([...members]);
    },
    [members]
  );

  // 处理自定义表单数据
  const washList = (arrayItem: any, extMap: any) => {
    const customList: any = [];
    const washData = (arrayItemFoo: any, extMapFoo: any) => {
      arrayItemFoo.forEach((item: any) => {
        let colValue: string = extMapFoo[item.colName] ? extMapFoo[item.colName] : '';
        if (colValue === '[]' || colValue === '[{}]') colValue = '';
        const arrayFoo = {
          ...item,
          ...item.attr,
          value: colValue || getNoData(item.type),
        };
        customList.push(arrayFoo);
      });
    };
    washData(arrayItem, extMap);
    setFilledData(customList);
  };

  // 修改
  const onJumpEdit = () => {
    setCanEdit(true);
    form.setFieldsValue({
      ...customerInfo,
      corporateName: currentCompany.legalPerson,
      taxNum: currentCompany.companyCreditCode,
      address: currentCompany.companyAddr,
      customerName: currentCompany.companyName,
      categoryId: customerInfo?.categoryClass ? JSON.parse(customerInfo?.categoryClass) : null,
      region: customerInfo?.provinceCode
        ? [customerInfo?.provinceCode, customerInfo?.cityCode, customerInfo?.districtCode]
        : null,
    });
    setMembers(
      customerInfo.contactParamList
        .filter((contactItem) => contactItem.contactMemberId && contactItem.contactUserId)
        .map((contactItem) => ({
          id: contactItem.contactMemberId,
          label: contactItem.contactName,
          phone: contactItem.phone,
          type: 'users',
          userId: contactItem.contactUserId,
          memberId: contactItem.contactMemberId,
        }))
    );
    // 自定义表单编辑回显
    const editDataObj: any = {};
    const editData = customerInfo.extMap;
    Object.keys(editData).forEach((item) => {
      editDataObj[item] = toJSON(editData[item]);
    });
    setTimeout(() => {
      refForm.current.setValues(editDataObj);
    }, 2000);
  };

  // 提交
  const onFinish = debounce((values: any) => {
    const extMap: any = refForm.current.getValues();
    refForm.current
      .submit()
      .then(() => {
        filledSupplierInfo({
          ...values,
          memberId: null,
          loginMemberId,
          companyId,
          customerCompanyId: Number(customerCompanyId),
          customerCategory: 1,
          dataSource: 3,
          customerSource: 3,
          customerType: 1,
          categoryIds: flattenDeep(
            values?.categoryId?.map((item: number[]) => item[item.length - 1])
          ),
          province: address?.[0]?.label || customerInfo.province,
          city: address?.[1]?.label || customerInfo.city,
          district: address?.[2]?.label || customerInfo.district,
          provinceCode: values?.region?.[0],
          cityCode: values?.region?.[1],
          districtCode: values?.region?.[2],
          categoryClass: JSON.stringify(values?.categoryId),
          contactParamList: members.map((mebmer: any) => ({
            contactName: mebmer.label,
            contactMemberId: mebmer.id,
            contactUserId: mebmer.userId,
            phone: mebmer.phone,
          })),
          formCode: 'FORMS2320Gq1668667062934',
          extMap,
        }).then(() => {
          message.success('提交成功');
          navigate(-1);
        });
      })
      .catch(() => {
        message.warning('请完善必填项');
      });
  }, 500);

  // 校验必填必填，按钮置灰
  const onValuesChange = () => {
    const formInfo = form.getFieldsValue();
    if (formInfo?.categoryId?.length > 0 && formInfo?.region && members.length > 0) {
      setCanSubmit(true);
      return;
    }
    setCanSubmit(false);
  };

  useEffect(() => {
    onValuesChange();
  }, [members]); // eslint-disable-line

  // 页面数据初始化
  useEffect(() => {
    // 个人组织|组织未认证|非管理员|相同公司
    (async () => {
      const formInfoJson = localStorage.getItem('FORM_INFO');
      if (formInfoJson) {
        const formInfo = JSON.parse(formInfoJson);
        form.setFieldsValue({ ...formInfo });
        localStorage.removeItem('FORM_INFO');
      }
      setLoading(true);
      const result = await getCompanyUser({ companyId: customerCompanyId, userId: currentUseId });
      setLoginMemberId(result.id);
      if (!result.isMaster) {
        message.warning('暂无权限，请联系公司管理员开通');
      }
      if (!result.isMaster || result.companyId === companyId) {
        navigate(-1);
        return;
      }
      // 查询入住公司的信息并将查询到的公司信息回填到页面中
      const res = await getCompanyInfo(customerCompanyId);
      if (res.companyNature === 1 || res.status !== 1) {
        navigate(-1);
        return;
      }
      setCurrentCompany(res);
      form.setFieldsValue({
        corporateName: res.legalPerson,
        taxNum: res.companyCreditCode,
        address: res.companyAddr,
        customerName: res.companyName,
      });
      const res1 = await getProvinceCityCounty();
      const res2 = await getSupplierClassList({ companyId });
      const res3 = await getFormTemplateComponents({
        formCode: 'FORMS2320Gq1668667062934',
        companyId,
      });
      let res4 = await getSupplierDetail({
        companyId,
        customerCompanyId: Number(customerCompanyId),
      });
      setAddressList(res1.list);
      setClassifyList(res2.list);
      setItems(res3.components);
      if (res4 && res4.checkStatus === 1) {
        const res5 = await getSupplierData({
          companyId,
          customerCompanyId: Number(customerCompanyId),
        });
        res4 = {
          ...res4,
          ...res5,
          categoryClass: toCategoryClass(res5.categoryTreeList),
        };
      }
      if (res4 && res4.id) {
        setCustomerInfo(res4);
        washList(res3.components, res4.extMap);
        if (res4.checkStatus === 2 || (searchParams.get('edit') && res4.checkStatus === 1)) {
          form.setFieldsValue({
            ...res4,
            corporateName: res.legalPerson,
            taxNum: res.companyCreditCode,
            address: res.companyAddr,
            customerName: res.companyName,
            categoryId: res4.categoryClass ? JSON.parse(res4.categoryClass) : null,
            region: res4.provinceCode
              ? [res4.provinceCode, res4.cityCode, res4.districtCode]
              : null,
          });
          setMembers(
            res4.contactParamList
              .filter((contactItem) => contactItem.contactMemberId && contactItem.contactUserId)
              .map((contactItem) => ({
                id: contactItem.contactMemberId,
                label: contactItem.contactName,
                phone: contactItem.phone,
                type: 'users',
                userId: contactItem.contactUserId,
                memberId: contactItem.contactMemberId,
              }))
          );
          // 自定义表单编辑回显
          const editDataObj: any = {};
          const editData = res4.extMap;
          Object.keys(editData).forEach((item) => {
            editDataObj[item] = toJSON(editData[item]);
          });
          setTimeout(() => {
            refForm.current.setValues(editDataObj);
          }, 2000);
        } else {
          setCanEdit(false);
        }
      } else {
        setMembers([
          {
            id: result.id,
            label: result.realName,
            phone: result.phone,
            type: 'users',
            userId: result.userId,
            memberId: result.id,
          },
        ]);
      }
      const memberArrJson = localStorage.getItem('SELECT_USERS');
      if (memberArrJson) {
        const memberArr = JSON.parse(memberArrJson);
        setMembers([
          ...members,
          ...memberArr.map((item: any) => ({
            id: item.id,
            label: item.realName || item.label,
            phone: item.phone,
            userId: item.userId,
            memberId: item.id,
          })),
        ]);
      }
      setLoading(false);
    })();
  }, []); // eslint-disable-line

  return (
    <div className={styles.apply}>
      <Spin spinning={loading}>
        <Helmet>
          <title>{canEdit ? '填写入驻资料' : '入驻资料详情'}</title>
        </Helmet>
        <div className={styles.formBox}>
          {canEdit ? (
            <Form
              form={form}
              layout="vertical"
              autoComplete="off"
              onFinish={onFinish}
              onValuesChange={onValuesChange}
            >
              <Form.Item
                name="customerName"
                label="供应商名称"
                rules={[{ required: true, message: '请选择供应商名称' }]}
              >
                <Input placeholder="请选择供应商名称" disabled />
              </Form.Item>
              <Form.Item name="corporateName" label="法人代表姓名">
                <Input placeholder="请输入法人代表姓名" maxLength={10} disabled />
              </Form.Item>
              <Form.Item name="taxNum" label="统一社会信用代码">
                <Input
                  placeholder="请输入统一社会信用代码"
                  maxLength={18}
                  minLength={18}
                  disabled
                />
              </Form.Item>
              <Form.Item
                name="categoryId"
                label="供应商分类"
                rules={[{ required: true, message: '请选择供应商分类' }]}
              >
                <Cascader
                  showSearch
                  placeholder="请选择/搜索供应商分类"
                  options={classifyList}
                  multiple
                  fieldNames={{
                    label: 'name',
                    value: 'id',
                    children: 'childList',
                  }}
                  showCheckedStrategy={Cascader.SHOW_CHILD}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                />
              </Form.Item>
              <Form.Item
                name="region"
                label="所在地区"
                rules={[{ required: true, message: '请选择所在地区' }]}
              >
                <Cascader
                  options={addressList}
                  placeholder="请选择所在地区"
                  bordered
                  onChange={(value, selectedOptions) => {
                    setAddress(selectedOptions);
                  }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                />
              </Form.Item>
              <Form.Item name="address" label="详细地址">
                <Input placeholder="请输入详细地址" maxLength={200} disabled />
              </Form.Item>
              <Form.Item name="openBank" label="开户行">
                <Input placeholder="请输入开户行" maxLength={70} />
              </Form.Item>
              <Form.Item name="bankAccount" label="银行账户">
                <Input placeholder="请输入银行账户" maxLength={30} />
              </Form.Item>
              <Form.Item name="remark" label="备注信息">
                <Input placeholder="请输入备注信息" maxLength={200} />
              </Form.Item>
              <Form.Item label="联系人" required>
                <div className={styles.FromItemTags}>
                  <FromItemTags
                    max={0}
                    tags={members}
                    fieldNames={{ value: 'memberId', label: 'label' }}
                    showArrow={false}
                    onClose={deleteMember}
                  >
                    <span
                      role="button"
                      tabIndex={0}
                      onClick={addMembers}
                      className={styles.membersBtn}
                    >
                      <Icon name="plus" color="#008CFF" size="16" className="mr-1" />
                      <span>添加</span>
                    </span>
                  </FromItemTags>
                </div>
              </Form.Item>
              <FormRender ref={refForm} items={items} />
              <Row justify="center">
                <Form.Item>
                  <Button
                    className={styles.cancelBtn}
                    onClick={() => {
                      if (customerInfo?.checkStatus === 1) {
                        setCanEdit(false);
                        return;
                      }
                      navigate(-1);
                    }}
                  >
                    取消
                  </Button>
                  <Button
                    className={styles.submitBtn}
                    type="primary"
                    htmlType="submit"
                    disabled={!canSubmit}
                  >
                    提交
                  </Button>
                </Form.Item>
              </Row>
            </Form>
          ) : (
            <div>
              <SupplierDetail
                customerInfo={customerInfo}
                filledData={filledData}
                cityList={addressList}
              />
              {customerInfo?.checkStatus === 1 && (
                <Row justify="center">
                  <div
                    role="button"
                    tabIndex={-1}
                    className={styles.updataBtn}
                    onClick={() => onJumpEdit()}
                  >
                    修改
                  </div>
                </Row>
              )}
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
}

export default SupplierApply;
