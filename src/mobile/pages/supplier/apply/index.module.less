.apply {
  width: 100%;
  height: 100%;
  padding: 12px 16px 0;
  background-color: #f5f6f7;
}

.headerauditStatus {
  display: flex;
  margin-bottom: 20px;
  padding: 16px;
  justify-content: space-between;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .rejectionIcon {
    width: 24px;
    height: 24px;
    margin-top: 6px;
  }

  .rejectionBox {
    display: flex;
    flex: 1;
    align-items: center;

    .rejectionText {
      font-size: 16px;
      font-weight: bolder;
      margin-left: 8px;
    }
  }

  .rejectionBtn {
    width: 80px;
    margin-left: 12px;
  }
}

.headerInfoBox {
  padding: 16px;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  position: relative;

  .invitationInfo {
    display: flex;
    align-items: center;

    .companyName {
      font-weight: bolder;
      margin-left: 12px;
    }
  }

  .selectCompany {
    margin-top: 31px;
  }

  .auditStatus {
    width: 100px;
    height: 78px;
    position: absolute;
    top: 20px;
    right: 20px;
  }
}

.formBox {
  padding: 16px;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  overflow-y: scroll;

  .switchLabel {
    margin-right: 14px;
    margin-bottom: 22px;
  }

  .cancelBtn {
    margin-right: 6px;
  }

  .submitBtn {
    margin-left: 6px;
  }
}

.textLabel {
  color: #888b98;
  width: 150px;
}

.textValue {
  flex: 1;
}

.textBox {
  display: flex;
  margin-bottom: 20px;

  .tagBox {
    .textValueTag {
      margin-right: 5px;
    }
  }
}

.imgBox {
  display: flex;
  margin-bottom: 20px;

  .listBox {
    display: flex;
    margin-bottom: 20px;

    .photo {
      width: 100px;
      height: 100px;
      margin-right: 5px;
    }
  }
}

.textareaBox {
  display: flex;
  margin-bottom: 20px;

  .textareaLabel {
    color: #888b98;
    width: 150px;
  }

  .textareaValue {
    flex: 1;
  }
}

.updataBtn {
  width: 88px;
  height: 38px;
  line-height: 38px;
  border-radius: 10px;
  background-color: #f3f3f3;
  text-align: center;
  cursor: pointer;
}

.childFormBox {
  margin-bottom: 20px;
  overflow: hidden;
  overflow-x: scroll;

  .childFormTitle {
    color: #888b98;
    margin-bottom: 5px;
  }

  .titleBox {
    display: flex;
    border-left: 1px solid #ebeef5;

    .titleItem {
      width: 200px;
      min-height: 40px;
      line-height: 40px;
      background-color: #f5f6fa;
      border: 1px solid #ebeef5;
      border-left: none;
      text-align: center;
      flex-shrink: 0;
    }
  }

  .bobyBox {
    display: flex;
    border-left: 1px solid #ebeef5;

    .bobyItem {
      width: 200px;
      min-height: 50px;
      line-height: 50px;
      border: 1px solid #ebeef5;
      border-top: none;
      border-left: none;
      flex-shrink: 0;
      text-align: center;

      .textFormValue {
        word-break: break-all;
      }
    }
  }
}

.FromItemTags {
  border: 1px solid #b1b3be;
  border-radius: 6px;
  padding: 4px 12px;
}

.membersBtn {
  color: #008cff;
  display: inline-block;
  line-height: 22px;
  margin-bottom: 12px;
  cursor: pointer;
}
