/* eslint-disable consistent-return */
/* eslint-disable no-nested-ternary */
/* eslint-disable @typescript-eslint/no-explicit-any */
import dayjs from 'dayjs';
import isString from 'lodash/isString';
import { FileList, FileListItem } from '@/components';
import styles from '../apply/index.module.less';

function SupplierDetail(props: any) {
  const { customerInfo, filledData, cityList } = props;

  const toJSON = (data: unknown) => {
    if (isString(data) && /^(\[|\{).*(]|})$/.test(data)) {
      try {
        return JSON.parse(data);
      } catch (e) {
        console.warn(e);
      }
    }
    return data;
  };

  const regularData = [
    { id: '1', supplierLabel: '供应商名称', supplierValue: customerInfo?.customerName },
    { id: '2', supplierLabel: '法人代表姓名', supplierValue: customerInfo?.corporateName },
    { id: '3', supplierLabel: '统一社会信用代码', supplierValue: customerInfo?.taxNum },
    {
      id: '4',
      supplierLabel: '供应商分类',
      supplierValue: customerInfo?.categoryList?.map((item: any) => item?.name).join('、'),
    },
    {
      id: '5',
      supplierLabel: '所在地区',
      supplierValue: `${customerInfo?.province || ''}${customerInfo?.city || ''}${
        customerInfo?.district || ''
      }`,
    },
    { id: '6', supplierLabel: '详细地址', supplierValue: customerInfo?.address },
    { id: '7', supplierLabel: '开户行', supplierValue: customerInfo?.openBank },
    { id: '8', supplierLabel: '银行账户', supplierValue: customerInfo?.bankAccount },
    { id: '9', supplierLabel: '备注信息', supplierValue: customerInfo?.remark },
    {
      id: '10',
      supplierLabel: '同时新建联系人',
      supplierValue: customerInfo?.contactParamList?.[0]?.contactName ? '是' : '否',
    },
    {
      id: '11',
      supplierLabel: '联系人',
      supplierValue: customerInfo?.contactParamList,
    },
  ];

  const childFormShow = (item: any, data: any) => {
    const urlRef = /^(https?:)?\/\//;
    if (data.type === 'select') {
      return (
        <div>
          {typeof item[data.colName] === 'string'
            ? JSON.parse(/\[.*?\]/g.test(item[data.colName]) ? item[data.colName] : '[]').join(
                '、'
              ) || '--'
            : item[data.colName].join('、') || '--'}
        </div>
      );
    }
    if (data.type === 'text' || data.type === 'textarea' || data.type === 'number') {
      return <div className={styles.textFormValue}>{item[data.colName] || '--'}</div>;
    }
    if (data.type === 'date') {
      if (data.dateType === 'year') {
        return (
          <div>{item[data.colName] ? dayjs(Number(item[data.colName])).format('YYYY') : '--'}</div>
        );
      }
      if (data.dateType === 'month') {
        return (
          <div>
            {item[data.colName] ? dayjs(Number(item[data.colName])).format('YYYY-MM') : '--'}
          </div>
        );
      }
      if (data.dateType === 'date') {
        return (
          <div>
            {item[data.colName] ? dayjs(Number(item[data.colName])).format('YYYY-MM-DD') : '--'}
          </div>
        );
      }
      if (data.dateType === 'time') {
        return (
          <div>
            {item[data.colName]
              ? dayjs(Number(item[data.colName])).format('YYYY-MM-DD HH:mm:ss')
              : '--'}
          </div>
        );
      }
    }
    if (data.type === 'area') {
      const area = cityList
        ?.find(
          (f: any) => f.value === JSON.parse(item[data.colName] ? item[data.colName] : '[]')?.[0]
        )
        ?.children.find(
          (t: any) => t.value === JSON.parse(item[data.colName] ? item[data.colName] : '[]')?.[1]
        );
      return <div>{item[data.colName] ? area?.label : '--'}</div>;
    }
    if (data.type === 'image') {
      return (
        <div className={styles.imgBox}>
          {item[data.colName].length < 5
            ? '--'
            : JSON.parse(item[data.colName].length < 5 ? '[]' : item[data.colName])?.map(
                (img: string) => (
                  <div key={img}>
                    <img
                      className={styles.photo}
                      src={
                        urlRef.test(img) ? img : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${img}`
                      }
                      alt=""
                    />
                  </div>
                )
              )}
        </div>
      );
    }
    if (data.type === 'attachment') {
      const fileLists =
        Object.prototype.toString.call(item[data.colName]) === '[object Array]'
          ? item[data.colName]?.map((every: any) => ({
              ...every,
              url: urlRef.test(every?.url)
                ? every?.url
                : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${every?.url}`,
              type: every.ext,
            }))
          : JSON.parse(item[data?.colName] ? item[data.colName] : '[]')?.map((every: any) => ({
              ...every,
              url: urlRef.test(every?.url)
                ? every?.url
                : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${every?.url}`,
              type: every.ext,
            }));
      return (
        <div className={styles.bobyItem}>
          {fileLists?.length > 0 ? (
            <FileList
              fileList={fileLists as FileListItem[]}
              listType="list"
              layout="vertical"
              download
            />
          ) : (
            '--'
          )}
        </div>
      );
    }
  };

  const onCustomFormDisplay = (item: any) => {
    const urlRef = /^(https?:)?\/\//;
    if (item.type === 'select') {
      return (
        <div className={styles.textBox}>
          <div className={styles.textLabel}>{item.label}</div>
          <div>
            {typeof item.value === 'string'
              ? JSON.parse(/\[.*?\]/g.test(item.value) ? item.value : '[]').join('、') || '--'
              : item.value.join('、') || '--'}
          </div>
        </div>
      );
    }
    if (item.type === 'text' || item.type === 'textarea' || item.type === 'number') {
      return (
        <div className={styles.textareaBox}>
          <div className={styles.textareaLabel}>{item.label}</div>
          <div className={styles.textareaValue}>{toJSON(item.value) || '--'}</div>
        </div>
      );
    }
    if (item.type === 'date') {
      if (item.dateType === 'year') {
        return (
          <div className={styles.textBox}>
            <div className={styles.textLabel}>{item.label}</div>
            <div className={styles.textValue}>
              {item.value ? dayjs(Number(item.value)).format('YYYY') : '--'}
            </div>
          </div>
        );
      }
      if (item.dateType === 'month') {
        return (
          <div className={styles.textBox}>
            <div className={styles.textLabel}>{item.label}</div>
            <div className={styles.textValue}>
              {item.value ? dayjs(Number(item.value)).format('YYYY-MM') : '--'}
            </div>
          </div>
        );
      }
      if (item.dateType === 'date') {
        return (
          <div className={styles.textBox}>
            <div className={styles.textLabel}>{item.label}</div>
            <div className={styles.textValue}>
              {item.value ? dayjs(Number(item.value)).format('YYYY-MM-DD') : '--'}
            </div>
          </div>
        );
      }
      if (item.dateType === 'time') {
        return (
          <div className={styles.textBox}>
            <div className={styles.textLabel}>{item.label}</div>
            <div className={styles.textValue}>
              {item.value ? dayjs(Number(item.value)).format('YYYY-MM-DD HH:mm:ss') : '--'}
            </div>
          </div>
        );
      }
    }
    if (item.type === 'area') {
      const area = cityList
        ?.find((f: any) => f.value === JSON.parse(item.value ? item.value : '[]')?.[0])
        ?.children.find((t: any) => t.value === JSON.parse(item.value ? item.value : '[]')?.[1]);
      return (
        <div className={styles.textBox}>
          <div className={styles.textLabel}>{item.label}</div>
          <div className={styles.textValue}>{item.value ? area?.label : '--'}</div>
        </div>
      );
    }
    if (item.type === 'image') {
      return (
        <div className={styles.imgBox}>
          <div className={styles.textLabel}>{item.label}</div>
          {item.value.length < 5
            ? '--'
            : JSON.parse(item.value.length < 5 ? '[]' : item.value)?.map((img: string) => (
                <div key={img} className={styles.listBox}>
                  <img
                    className={styles.photo}
                    src={urlRef.test(img) ? img : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${img}`}
                    alt=""
                  />
                </div>
              ))}
        </div>
      );
    }
    if (item.type === 'attachment') {
      const fileLists =
        Object.prototype.toString.call(item.value) === '[object Array]'
          ? item.value?.map((every: any) => ({
              ...every,
              url: urlRef.test(every?.url)
                ? every?.url
                : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${every?.url}`,
              type: every.ext,
            }))
          : JSON.parse(item.value.length < 5 ? '[]' : item.value)?.map((every: any) => ({
              ...every,
              url: urlRef.test(every?.url)
                ? every?.url
                : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${every?.url}`,
              type: every.ext,
            }));
      return (
        <div className={styles.textBox}>
          <div className={styles.textLabel}>{item.label}</div>
          {fileLists?.length > 0 ? (
            <FileList
              fileList={fileLists as FileListItem[]}
              listType="list"
              layout="horizontal"
              download
            />
          ) : (
            '--'
          )}
        </div>
      );
    }
    if (item.type === 'childForm') {
      const childFormData = item.children.map((element: any) => ({
        colName: element.colName,
        label: element.attr.label,
        value: item.value,
        type: element.type,
        dateType: element.attr.dateType,
      }));
      return (
        <div className={styles.childFormBox}>
          <div className={styles.childFormTitle}>{item.label}</div>
          <div className={styles.titleBox}>
            {childFormData.map((every: any) => (
              <div className={styles.titleItem}>{every.label}</div>
            ))}
          </div>
          <div>
            {JSON.parse(item.value ? item.value : '[]').map((every: any) => (
              <div className={styles.bobyBox}>
                {childFormData.map((it: any) => (
                  <div className={styles.bobyItem}>{childFormShow(every, it)}</div>
                ))}
              </div>
            ))}
          </div>
        </div>
      );
    }
  };

  return (
    <div>
      {regularData.map((item: { id: string; supplierLabel: string; supplierValue: string }) => {
        if (item.id === '11') {
          return (
            <>
              <div className={styles.textBox} key={item.id}>
                <div className={styles.textLabel}>{item.supplierLabel}</div>
              </div>
              {(item.supplierValue as unknown as any[]).map((contact) => (
                <div className={styles.textBox} key={contact.contactMemberId}>
                  <div className={styles.textLabel}>{contact.contactName}</div>
                  <div className={styles.textValue}>{contact.phone}</div>
                </div>
              ))}
            </>
          );
        }
        return (
          <div className={styles.textBox} key={item.id}>
            <div className={styles.textLabel}>{item.supplierLabel}</div>
            <div className={styles.textValue}>{item?.supplierValue || '--'}</div>
          </div>
        );
      })}
      {/* 自定义表单数据的渲染 */}
      <div>
        {filledData.map((item: any) => (
          <div key={String(item.id)}>
            <div>{onCustomFormDisplay(item)}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default SupplierDetail;
