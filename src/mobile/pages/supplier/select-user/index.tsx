import React, { ReactNode, useState, Fragment, useMemo, useEffect } from 'react';
import { Checkbox, Button, Avatar } from 'antd';
import { useMount } from 'ahooks';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { searchAllRelationUsers, getCompanyOrganizationsApi, getUserCardInfo } from '@/apis';
import type {
  GetCompanyOrganizationsParamsType,
  MemberVOType,
  OrganizationVOType,
  CrumbsListType,
} from '@/apis';
import { Search, Icon, Empty } from '@/components';
import { Helmet } from 'react-helmet';
import { debounce, cloneDeep } from 'lodash';
import './index.less';

interface SelectedType extends Record<string, any> {
  value: number;
  label: string;
}

function SelectUsers() {
  const urlParams = useParams();
  const [paramsUrl] = useSearchParams();
  const navigate = useNavigate();
  const [safeAreaInsetBottom] = useState(0);
  const [currentCompanyId, setCurrentCompanyId] = useState(0);
  const [max, setMax] = useState(0);
  const [selectOne, setSelectOne] = useState(0);
  const [allowSelectMember, setAllowSelectMember] = useState(true);
  const [allowSelectOrganization, setAllowSelectOrganization] = useState(false);
  const [isSearch, setIsSearch] = useState(false);
  const [searchMemberList, setSearchMemberList] = useState<Array<any>>([]);
  const [breadcrumbs, setBreadcrumbs] = useState<Array<CrumbsListType>>([]);
  const [memberList, setMumberList] = useState<Array<MemberVOType>>([]);
  const [organizationList, setOrganizationList] = useState<Array<OrganizationVOType>>([]);
  const [selectedMemberList, setSelectedMemberList] = useState<Array<SelectedType>>([]);
  const [selectedOrganizationList, setSelectedOrganizationList] = useState<Array<SelectedType>>([]);
  const [disabledUserIdList, setDisabledUserIdList] = useState<Array<number>>([]);
  const [disabledOrganizationIdList, setDisabledOrganizationIdList] = useState<Array<number>>([]);
  const selectedMemberIds = useMemo(
    () => selectedMemberList.map((item) => item.value),
    [selectedMemberList]
  );
  const selectedOrganizationIds = useMemo(
    () => selectedOrganizationList.map((item) => item.value),
    [selectedOrganizationList]
  );

  const getList = (params: GetCompanyOrganizationsParamsType) => {
    getCompanyOrganizationsApi(params).then((res) => {
      setBreadcrumbs(res.crumbsList);
      setMumberList(res.memberVO);
      setOrganizationList(res.organizationVO);
    });
  };

  const onSearch = debounce((search: string) => {
    if (!search) {
      setIsSearch(false);
      setSearchMemberList([]);
      return;
    }
    setIsSearch(true);
    searchAllRelationUsers({
      search,
      isFilter: 0,
      companyId: currentCompanyId,
    }).then((res) => {
      setSearchMemberList(cloneDeep(res.colleagueSearches) || []);
    });
  }, 300);

  const onSelectOrganization = (item: OrganizationVOType) => {
    const data = urlParams;
    if (data?.onlySelectOrganization && data?.value && data?.label) {
      if (selectedOrganizationIds.includes(item.id)) {
        setSelectedOrganizationList([]);
        return;
      }
      setSelectedOrganizationList([{ ...item, value: item.id, label: item.orgName }]);
      return;
    }
    if (!selectedOrganizationIds.includes(item.id)) {
      if (max && selectedOrganizationIds.length > max) return;
      setSelectedOrganizationList([
        ...selectedOrganizationList,
        { ...item, value: item.id, label: item.orgName },
      ]);
    } else {
      setSelectedOrganizationList(selectedOrganizationList.filter((ite) => ite.value !== item.id));
    }
  };

  const onSelectMember = (item: any, key: string) => {
    const id = item[key];
    // 如果是单选 并且选择member id
    if (selectOne) {
      setSelectedMemberList([{ ...item, value: id, label: item.realName }]);
      return;
    }
    if (!selectedMemberIds.includes(id)) {
      if (max && selectedMemberIds && selectedMemberIds.length >= max) return;
      setSelectedMemberList([...selectedMemberList, { ...item, value: id, label: item.realName }]);
    } else {
      setSelectedMemberList(selectedMemberList.filter((ite) => ite.value !== id));
    }
  };

  const onConfirm = () => {
    localStorage.setItem('SELECT_USERS', JSON.stringify(selectedMemberList));
    navigate(-1);
  };

  useEffect(() => {
    // const res = Taro.getSystemInfoSync();
    // setSafeAreaInsetBottom(
    //   res.screenHeight - (res.safeArea?.height as number) - (res.statusBarHeight as number)
    // );
  }, []);

  useMount(() => {
    const data = urlParams;
    // if (data?.memberList) {
    //   setSelectedMemberList(JSON.parse(data.memberList) as SelectedType[]);
    // }
    // if (data?.organizationList) {
    //   setSelectedOrganizationList(
    //     JSON.parse(data.organizationList) as SelectedType[]
    //   );
    // }
    if (paramsUrl.get('selectOne')) {
      setSelectOne(Number(paramsUrl.get('selectOne')));
    }
    if (data?.onlySelectMember) {
      setAllowSelectOrganization(false);
    }
    if (data?.onlySelectOrganization) {
      setAllowSelectMember(false);
      if (Number(data?.value) && data?.label) {
        setSelectedOrganizationList([
          {
            value: Number(data.value),
            label: data.label,
          },
        ]);
      }
    }
    if (data?.disabledUserIdList) {
      setDisabledUserIdList(JSON.parse(data.disabledUserIdList) as number[]);
    }
    if (data?.disabledOrganizationIdList) {
      setDisabledOrganizationIdList(JSON.parse(data.disabledOrganizationIdList) as number[]);
    }
    if (data?.max) {
      setMax(Number(data.max));
    }
    const personVal = localStorage.getItem('SELECT_USERS');
    if (personVal && personVal.length) {
      const memberArr = JSON.parse(personVal);
      setSelectedMemberList(
        memberArr.map((item: any) => ({
          ...item,
          value: item.userId || item.id,
          label: item.realName || item.label,
        }))
      );
    }
    localStorage.removeItem('SELECT_USERS');
    getUserCardInfo().then((res) => {
      setCurrentCompanyId(res.companyId);
      getList({ companyId: res.companyId });
    });
  });

  let content: ReactNode = null;
  if (!isSearch && (organizationList.length || memberList.length)) {
    content = (
      <>
        {allowSelectOrganization ? (
          <Checkbox.Group
            key="organizationList"
            value={selectedOrganizationIds as unknown as string[]}
            className="select-users-checkbox-group"
          >
            {organizationList.map((item) => (
              <div
                role="button"
                tabIndex={0}
                key={item.id}
                onClick={() => {
                  onSelectOrganization(item);
                }}
              >
                <Checkbox
                  value={item.id}
                  disabled={disabledOrganizationIdList.includes(item.id)}
                  className="select-users-organization-item"
                >
                  <div className="select-users-organization-item-label">
                    {item.orgName}（{item.amount}）
                  </div>
                  <div
                    role="button"
                    tabIndex={0}
                    className="select-users-organization-item-next"
                    onClick={(e) => {
                      e.stopPropagation();
                      if (!selectedOrganizationIds.includes(item.id)) {
                        getList({ id: item.id });
                      }
                    }}
                  >
                    <img
                      src={
                        selectedOrganizationIds.includes(item.id)
                          ? `https://img.huahuabiz.com/user_files/2024112/170504590578310.png`
                          : `https://img.huahuabiz.com/user_files/2024112/170504345492024.png`
                      }
                      className="select-users-organization-item-next-icon"
                      alt=""
                    />
                    <div
                      className={
                        selectedOrganizationIds.includes(item.id)
                          ? 'select-users-organization-item-next-text-disabled'
                          : 'select-users-organization-item-next-text'
                      }
                    >
                      下级
                    </div>
                  </div>
                </Checkbox>
              </div>
            ))}
          </Checkbox.Group>
        ) : (
          <div>
            {organizationList.map((item) => (
              <div key={item.id} className="select-users-organization-item">
                <div className="select-users-organization-item-label">
                  {item.orgName}（{item.amount}）
                </div>
                <div
                  role="button"
                  tabIndex={0}
                  className="select-users-organization-item-next"
                  onClick={() => {
                    getList({ id: item.id });
                  }}
                >
                  <img
                    src="https://img.huahuabiz.com/user_files/2024112/170504345492024.png"
                    className="select-users-organization-item-next-icon"
                    alt=""
                  />
                  <div className="select-users-organization-item-next-text">下级</div>
                </div>
              </div>
            ))}
          </div>
        )}
        {allowSelectMember && (
          <Checkbox.Group
            key="memberList"
            value={selectedMemberIds as unknown as string[]}
            className="select-users-checkbox-group"
          >
            {memberList.map((item) => (
              <div
                role="button"
                tabIndex={0}
                key={item.userId}
                onClick={() => {
                  if (disabledUserIdList.includes(item.userId)) return;
                  onSelectMember(item, 'userId');
                }}
              >
                <Checkbox
                  value={item.userId}
                  disabled={disabledUserIdList.includes(item.userId)}
                  className="select-users-member-item"
                >
                  {item.avatar ? (
                    <Avatar src={item.avatar} className="select-users-member-item-avatar" />
                  ) : (
                    <Avatar className="select-users-member-item-avatar">
                      {item.realName.charAt(0)}
                    </Avatar>
                  )}
                  <div className="select-users-member-item-label">
                    <div className="select-users-member-item-name">{item.realName}</div>
                    {!!item.position && (
                      <div className="select-users-member-item-position">{item.position}</div>
                    )}
                  </div>
                </Checkbox>
              </div>
            ))}
          </Checkbox.Group>
        )}
      </>
    );
  } else if (isSearch && searchMemberList.length) {
    content = (
      <Checkbox.Group key="searchReault" value={selectedMemberIds as unknown as string[]}>
        {searchMemberList.map((item) => (
          <div
            role="button"
            tabIndex={0}
            key={item.id}
            onClick={() => {
              if (disabledUserIdList.includes(item.id)) return;
              onSelectMember(item, 'id');
            }}
          >
            <Checkbox
              value={item.id}
              disabled={disabledUserIdList.includes(item.id)}
              className="select-users-member-item"
            >
              {item.avatar ? (
                <Avatar src={item.avatar} className="select-users-member-item-avatar" />
              ) : (
                <Avatar className="select-users-member-item-avatar">
                  {item.realName.charAt(0)}
                </Avatar>
              )}
              <div className="select-users-member-item-label">{item.realName}</div>
            </Checkbox>
          </div>
        ))}
      </Checkbox.Group>
    );
  }

  let selectedText = '未选择';
  if (selectedMemberIds && selectedMemberIds.length && selectedOrganizationIds.length) {
    selectedText = `已选择：${selectedMemberIds.length}人，${selectedOrganizationIds.length}部门`;
  } else if (selectedMemberIds && selectedMemberIds.length) {
    selectedText = `已选择：${selectedMemberIds.length}人`;
  } else if (selectedOrganizationIds.length) {
    selectedText = `已选择：${selectedOrganizationIds.length}部门`;
  }

  return (
    <div className="select-users-div">
      <Helmet>
        <title>选择人员</title>
      </Helmet>
      <div className="select-user-search-bar">
        <Search placeholder="搜索人员" disabled={!allowSelectMember} onSearch={onSearch} />
      </div>
      {!isSearch && (
        <div className="select-users-breadcrumbs">
          {breadcrumbs.map((item, idx) => (
            <Fragment key={item.id}>
              <div
                role="button"
                tabIndex={0}
                className={
                  idx !== breadcrumbs.length - 1
                    ? 'select-users-breadcrumb'
                    : 'select-users-last-breadcrumb'
                }
                onClick={() => {
                  if (idx !== breadcrumbs.length - 1) {
                    getList({ id: item.id });
                  }
                }}
              >
                {item.orgName}
              </div>
              {idx !== breadcrumbs.length - 1 && (
                <Icon name="right" size="12" className="select-users-breadcrumb-icon" />
              )}
            </Fragment>
          ))}
        </div>
      )}
      <div className="select-users-scroll-div">
        {content ? (
          <div className="select-users-scroll-div-content">{content}</div>
        ) : (
          <div className="select-users-no-data">
            <Empty title="暂无数据" />
          </div>
        )}
      </div>
      <div className="select-users-footer" style={{ paddingBottom: `${safeAreaInsetBottom}px` }}>
        <div className="select-users-footer-text">{selectedText}</div>
        <Button
          type="primary"
          size="small"
          onClick={onConfirm}
          disabled={!selectedMemberIds.length && !selectedOrganizationIds.length}
        >
          确定（{selectedMemberIds && selectedMemberIds.length + selectedOrganizationIds.length}）
        </Button>
      </div>
    </div>
  );
}

export default SelectUsers;
