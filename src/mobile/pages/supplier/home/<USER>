.wrap {
  padding: 16px;
}

.header {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;
}

.headerLogo {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  margin-right: 12px;
}

.headerTitle {
  font-weight: 500;
}

.option {
  display: flex;
  align-items: center;
}

.optionIcon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  margin-right: 4px;
}

.select {
  width: 100%;
  margin-top: 4px;

  & .optionIcon {
    display: none;
  }
}

.steps {
  padding-top: 40px;

  :global {
    .ant-steps-icon-dot {
      top: 0 !important;
      left: 0 !important;

      &::after {
        display: none;
      }
    }

    .ant-steps-item-active .ant-steps-icon-dot,
    .ant-steps-item-finish .ant-steps-icon-dot {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #d9eeff !important;
      position: relative;

      &::before {
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #008cff;
        position: absolute;
        top: 5px;
        left: 5px;
      }
    }

    .ant-steps-item-wait .ant-steps-icon-dot {
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background: #f5f6fa !important;
      position: relative;

      &::before {
        content: '';
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #b1b3be;
        position: absolute;
        top: 5px;
        left: 5px;
      }
    }

    .ant-steps-item-title {
      color: #888b98 !important;
      font-weight: 500;
      line-height: 20px !important;
    }

    .ant-steps-item-description {
      padding-bottom: 64px !important;
    }

    .ant-steps-icon {
      top: 0 !important;
    }

    .ant-steps-item-icon {
      width: 20px !important;
      height: 20px !important;
      line-height: 20px !important;
      margin-top: 0 !important;
      margin-right: 12px !important;
    }

    .ant-steps-item-tail {
      padding-top: 19px !important;
      padding-bottom: 0 !important;
      top: 0 !important;
      left: -2px !important;
    }
  }
}

.selectDropdownFooter {
  text-align: center;
  cursor: pointer;
  height: 32px;
  line-height: 32px;
  border-radius: 4px;

  &:hover {
    background-color: rgb(217 238 255 / 30%);
  }
}

.reason {
  color: #888b98;
  display: inline-flex;
  max-width: 610px;
  line-height: 22px;
  margin-bottom: 12px;
  padding: 16px;
  border-radius: 10px;
  background: #f5f6fa;

  & span + span {
    flex: 1;
    word-wrap: break-word;
    word-break: break-all;
  }
}

.warn,
.error,
.success {
  color: #ea1c26;
  font-weight: 500;
  display: flex;
  height: 22px;
  margin: 8px 0;
  align-items: center;
}

.success {
  color: #05d380;
}

.warn {
  color: #f9ae08;
}

.warnIcon {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.linkWrap {
  display: flex;
  align-items: center;
  height: 20px;
}

.link {
  color: #888b98;
  font-size: 12px;
  display: flex;
  margin-right: 8px;
  align-items: center;
  cursor: pointer;
}

.msg {
  color: #ea1c26;
  font-size: 12px;
  line-height: 20px;
  margin: 4px 0;
}
