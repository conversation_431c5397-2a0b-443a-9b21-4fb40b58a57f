import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Select, Button, Steps, message } from 'antd';
import { Icon } from '@/components';
import {
  joinCompanies,
  getCompanyInfo,
  getSupplierDetail,
  getCompanyUser,
  getDisposableCode,
  getCompanyAuthStatus,
  getTenantSiteInfo,
  getUserCardInfo,
} from '@/apis';
import { JoinCompanyResult } from '@/apis/join-companies';
import { GetCompanyInfoResult } from '@/apis/user/get-company-info';
import { Helmet } from 'react-helmet';
import createTeam from '@@/mobile/pages/supplier/home/<USER>';
import { getToken, setToken } from '@/utils/auth';
import styles from './index.module.less';

const { Step } = Steps;
const { Option } = Select;

function Supplier() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams({
    tenantId: '',
    token: '',
  });
  const tenantId = searchParams.get('tenantId') || '';
  const [inviterCompanyId, setInviterCompanyId] = useState(0);
  const [selectOpen, setSelectOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState({
    userId: 0,
    id: 0,
    companyNature: 0,
  });
  const [companyList, setCompanyList] = useState<JoinCompanyResult[]>([]);
  const [inviter, setInviter] = useState({} as GetCompanyInfoResult);
  const [enterStatus, setEnterStatus] = useState({
    checkStatus: -1,
    rejectReason: '',
  });
  const [companyStatus, setCompanyStatus] = useState({
    status: -1,
    rejectReason: '',
  });
  const [selectCompanyId, setSelectCompanyId] = useState(0);

  const currentStep = useMemo(() => {
    if (companyStatus.status === 1) return 2;
    if (selectCompanyId) return 1;
    return 0;
  }, [companyStatus.status, selectCompanyId]);

  // 邀请公司信息
  const getInviter = useCallback((idVal: number) => {
    getCompanyInfo(idVal).then((res) => {
      setInviter(res);
    });
  }, []);

  // 获取公司列表
  const getCompanyList = useCallback((idVal: number) => {
    joinCompanies().then((res) => {
      setCompanyList(
        res.list
          .map((every) => ({ ...every, id: every.companyId }))
          .filter((item) => item?.companyNature !== 1 && item.companyId !== idVal)
      );
    });
  }, []);

  // 获取入驻状态
  const getEnterStatus = useCallback(
    (companyId: number, idVal?: number) => {
      if (idVal || inviterCompanyId) {
        getSupplierDetail({
          companyId: idVal || inviterCompanyId,
          customerCompanyId: companyId,
        }).then((res) => {
          if (res && res.id) {
            setEnterStatus({
              checkStatus: res.checkStatus,
              rejectReason: res.rejectReason,
            });
          } else {
            setEnterStatus({
              checkStatus: -1,
              rejectReason: '',
            });
          }
        });
      }
    },
    [inviterCompanyId]
  );

  // 获取认证状态
  const getCompanyStatus = useCallback(
    (companyId: number) => {
      getCompanyAuthStatus({ companyId }).then((res) => {
        if (res && (res?.status === 0 || res?.status > 0)) {
          setCompanyStatus({
            status: res.status,
            rejectReason: res.rejectReason,
          });
          if (res.status === 1) {
            getEnterStatus(companyId);
          }
        } else {
          setCompanyStatus({
            status: -1,
            rejectReason: '',
          });
        }
      });
    },
    [getEnterStatus]
  );

  // 获取用户信息
  const getUserInfo = (idVal: number) => {
    const currentCompanyInfo = localStorage.getItem('SELECT_COMPANY_ID');
    if (currentCompanyInfo) {
      const info = JSON.parse(currentCompanyInfo);
      setSelectCompanyId(info.id);
      setCurrentCompany(info);
      getCompanyStatus(info.id);
      getEnterStatus(info.id, idVal);
      localStorage.removeItem('SELECT_COMPANY_ID');
      return;
    }
    getUserCardInfo().then((res) => {
      if (res.companyVO.companyNature !== 1 && res.companyId !== idVal) {
        setSelectCompanyId(res.companyId);
        getCompanyStatus(res.companyId);
      }
      setCurrentCompany({
        userId: res.userId,
        id: res.companyId,
        companyNature: res.companyVO.companyNature,
      });
      getEnterStatus(res.companyId, idVal);
    });
  };

  // 切换公司
  const currentCompanyChange = useCallback(
    (value: number) => {
      const selectCompany = companyList?.filter(
        (item) => Number(item.companyId) === Number(value)
      )?.[0];
      setCurrentCompany({ userId: currentCompany?.userId, ...selectCompany } as any);
      setSelectCompanyId(selectCompany.companyId);
      getCompanyStatus(value as number);
      if (selectCompany?.status === 1) {
        getEnterStatus(value as number);
      } else {
        setEnterStatus({
          checkStatus: -1,
          rejectReason: '',
        });
      }
    },
    [companyList, currentCompany?.userId, getCompanyStatus, getEnterStatus]
  );

  // 创建公司
  const createCompany = useCallback(() => {
    createTeam({
      onConfirm: () => {
        getCompanyList(inviterCompanyId);
      },
    });
  }, [getCompanyList, inviterCompanyId]);

  // 去入驻
  const goNextPage = useCallback(
    async (valId: number, isEdit = false) => {
      localStorage.removeItem('SELECT_USERS');
      localStorage.setItem('SELECT_COMPANY_ID', JSON.stringify(currentCompany));
      getCompanyUser({ companyId: valId, userId: currentCompany?.userId }).then((res) => {
        if (valId === inviterCompanyId) return;
        if (!res.isMaster) {
          message.warning('暂无权限，请联系公司管理员开通');
          return;
        }
        if (isEdit) {
          navigate(
            `/m/supplier/apply?companyId=${inviterCompanyId}&customerCompanyId=${selectCompanyId}&useId=${res.userId}&edit=1`
          );
        } else {
          navigate(
            `/m/supplier/apply?companyId=${inviterCompanyId}&customerCompanyId=${selectCompanyId}&useId=${res.userId}`
          );
        }
      });
    },
    [selectCompanyId, inviterCompanyId, navigate, currentCompany]
  );

  const onJumpAuthPage = () => {
    const toURL = import.meta.env.BIZ_AUTH_COMPANY_URL;
    let host = toURL.replace(/^https?:\/\//, '');
    const index = host.indexOf('/');
    if (index !== -1) {
      host = host.substring(0, index);
    }
    const protocol = toURL.includes('https:') ? 'https:' : 'http:';

    getDisposableCode({
      clientId: `web-${host}`,
      redirectUri: host,
    }).then((res) => {
      if (res && res.code) {
        // onWindowOpen(
        //   `${protocol}//${host}?clientId=web-${host}&code=${res.code}&companyId=${selectCompanyId}`
        // );
        localStorage.setItem('SELECT_COMPANY_ID', JSON.stringify(currentCompany));
        window.location.href = `${protocol}//${host}?clientId=web-${host}&code=${res.code}&companyId=${selectCompanyId}`;
      }
    });
  };

  useEffect(() => {
    const token = searchParams.get('token') || getToken();
    if (token) {
      setToken(token || '');
    }
    getTenantSiteInfo({ tenantId }).then((res) => {
      setInviterCompanyId(res.companyId);
      getCompanyList(res.companyId);
      getInviter(res.companyId);
      getUserInfo(res.companyId);
    });
  }, []); // eslint-disable-line

  useEffect(() => {
    const handlePageShow = () => {
      window.location.reload();
    };

    window.addEventListener('pageshow', handlePageShow);

    return () => {
      window.removeEventListener('pageshow', handlePageShow);
    };
  }, []);

  const dropdownRender = useCallback(
    (menu) => (
      <>
        {menu}
        <div
          role="button"
          tabIndex={0}
          className={styles.selectDropdownFooter}
          onClick={() => {
            setSelectOpen(false);
            createCompany();
          }}
        >
          创建企业
        </div>
      </>
    ),
    [createCompany]
  );

  let stepDescriptionNode1;
  if (companyList.length) {
    stepDescriptionNode1 = (
      <>
        {currentCompany?.companyNature === 1 && (
          <div className={styles.msg}>系统检测到您已创建了企业</div>
        )}
        <Select
          value={(selectCompanyId ? `${selectCompanyId}` : null) as any}
          open={selectOpen}
          placeholder="请选择入驻企业"
          className={styles.select}
          dropdownRender={dropdownRender}
          onDropdownVisibleChange={setSelectOpen}
          onChange={currentCompanyChange}
        >
          {companyList.map((companyItem) => (
            <Option
              key={companyItem.companyId}
              title={companyItem.companyName}
              vlaue={companyItem.companyId}
              className={styles.option}
            >
              <img src={companyItem.logoUrl} alt="" className={styles.optionIcon} />
              <span>{companyItem.companyName}</span>
            </Option>
          ))}
        </Select>
      </>
    );
  } else {
    stepDescriptionNode1 = (
      <Button type="primary" size="small" className="mt-2" onClick={createCompany}>
        创建企业
      </Button>
    );
  }

  let stepDescriptionNode2;
  if (companyStatus.status === 0) {
    stepDescriptionNode2 = (
      <>
        <div className={styles.warn}>
          <img
            src="https://img.huahuabiz.com/user_files/2023717/1689587929807219.png"
            alt=""
            className={styles.warnIcon}
          />
          <span>认证资料审核中</span>
        </div>
        <div className={styles.linkWrap}>
          <div
            className={styles.link}
            tabIndex={-1}
            role="button"
            onClick={() => {
              onJumpAuthPage();
            }}
          >
            <span>查看详情</span>
            <Icon name="right" size={16} />
          </div>
        </div>
      </>
    );
  } else if (companyStatus.status === 1) {
    stepDescriptionNode2 = (
      <>
        <div className={styles.success}>
          <img
            src="https://img.huahuabiz.com/user_files/2023719/1689745532605542.png"
            alt=""
            className={styles.warnIcon}
          />
          <span>企业认证已通过</span>
        </div>
        <div className={styles.linkWrap}>
          <div
            className={styles.link}
            tabIndex={-1}
            role="button"
            onClick={() => {
              onJumpAuthPage();
            }}
          >
            <span>查看详情</span>
            <Icon name="right" size={16} />
          </div>
        </div>
      </>
    );
  } else if (companyStatus.status === 2) {
    stepDescriptionNode2 = (
      <>
        <div className={styles.error}>
          <img
            src="https://img.huahuabiz.com/user_files/2023719/1689745532274119.png"
            alt=""
            className={styles.warnIcon}
          />
          <span>企业认证审核未通过</span>
        </div>
        <div className={styles.reason}>
          <span>驳回原因：</span>
          <span>{companyStatus.rejectReason}</span>
        </div>
        <div>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              onJumpAuthPage();
            }}
          >
            重新提交
          </Button>
        </div>
      </>
    );
  } else if (companyStatus.status === -1) {
    stepDescriptionNode2 = (
      <Button
        type="primary"
        size="small"
        disabled={currentStep < 1}
        className="mt-2"
        onClick={() => {
          onJumpAuthPage();
        }}
      >
        去认证
      </Button>
    );
  }

  let stepDescriptionNode3;
  if (enterStatus.checkStatus === 0) {
    stepDescriptionNode3 = (
      <>
        <div className={styles.warn}>
          <img
            src="https://img.huahuabiz.com/user_files/2023717/1689587929807219.png"
            alt=""
            className={styles.warnIcon}
          />
          <span>认证资料审核中</span>
        </div>
        <div className={styles.linkWrap}>
          <div
            role="button"
            tabIndex={0}
            className={styles.link}
            onClick={() => goNextPage(Number(selectCompanyId))}
          >
            <span>查看详情</span>
            <Icon name="right" size={16} />
          </div>
        </div>
      </>
    );
  } else if (enterStatus.checkStatus === 1) {
    stepDescriptionNode3 = (
      <>
        <div className={styles.success}>
          <img
            src="https://img.huahuabiz.com/user_files/2023719/1689745532605542.png"
            alt=""
            className={styles.warnIcon}
          />
          <span>已完成供应商入驻</span>
        </div>
        <div className={styles.linkWrap}>
          <div
            role="button"
            tabIndex={0}
            className={styles.link}
            onClick={() => goNextPage(Number(selectCompanyId))}
          >
            <span>查看详情</span>
            <Icon name="right" size={16} />
          </div>
          <div
            role="button"
            tabIndex={0}
            className={styles.link}
            onClick={() => goNextPage(Number(selectCompanyId), true)}
          >
            <span>修改资料</span>
            <Icon name="right" size={16} />
          </div>
        </div>
      </>
    );
  } else if (enterStatus.checkStatus === 2) {
    stepDescriptionNode3 = (
      <>
        <div className={styles.error}>
          <img
            src="https://img.huahuabiz.com/user_files/2023719/1689745532274119.png"
            alt=""
            className={styles.warnIcon}
          />
          <span>入驻资料审核未通过</span>
        </div>
        <div className={styles.reason}>
          <span>驳回原因：</span>
          <span>{enterStatus.rejectReason}</span>
        </div>
        <div>
          <Button type="primary" size="small" onClick={() => goNextPage(Number(selectCompanyId))}>
            重新提交
          </Button>
        </div>
      </>
    );
  } else if (enterStatus.checkStatus === -1) {
    stepDescriptionNode3 = (
      <Button
        type="primary"
        size="small"
        disabled={currentStep < 2}
        className="mt-2"
        onClick={() => goNextPage(Number(selectCompanyId))}
      >
        申请入驻
      </Button>
    );
  }

  return (
    <div>
      <Helmet>
        <title>供应商入驻</title>
      </Helmet>
      <div className={styles.wrap}>
        <div className={styles.header}>
          <img
            src={
              inviter.logoUrl ||
              'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
            }
            alt=""
            className={styles.headerLogo}
          />
          <span className={styles.headerTitle}>{inviter.companyName}--邀请你成为ta的供应商</span>
        </div>
        <Steps progressDot current={currentStep} direction="vertical" className={styles.steps}>
          <Step title="创建企业" description={stepDescriptionNode1} />
          <Step title="企业认证" description={stepDescriptionNode2} />
          <Step title="入驻供应商" description={stepDescriptionNode3} />
        </Steps>
      </div>
    </div>
  );
}

export default Supplier;
