import { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>r, Drawer, DrawerProps, Form, Input, message } from 'antd';
import CloseOutlined from '@ant-design/icons/CloseOutlined';
import { Icon } from '@/components';
import { isWeb } from '@/utils/js-bridge';
import { createCompanyTeam, GetCategoryResult, getCompanyCategories } from '@/apis';
import debounce from 'lodash/debounce';
import CheckCircle from '@@/mobile/pages/login/images/checkCircle.png';
import style from '@@/mobile/pages/hnc-tenants/components/form-card/form-card.module.less';
import { drawerPopup } from '@/utils/popup';
import isFunction from 'lodash/isFunction';

interface CreateTeamProps extends DrawerProps {
  onConfirm: () => void;
}

function CreateTeam({ onConfirm, ...props }: CreateTeamProps) {
  const [isDisabled, setIsDisabled] = useState(true);
  const [categories, setCategories] = useState<GetCategoryResult[]>([]);
  const [creatForm] = Form.useForm();

  const onValuesChange = () => {
    const formInfo = creatForm.getFieldsValue();
    if (formInfo.companyName && formInfo.industry) {
      setIsDisabled(false);
      return;
    }
    setIsDisabled(true);
  };

  const closeCreateDrawer = () => {
    if (isFunction(props.onClose)) {
      props.onClose();
    }
  };

  const onCreatFinish = debounce((value: any) => {
    if (!categories) return;
    const { industry } = value;
    const parent = categories.find((cate) => cate.value === industry[0]);
    if (!parent) return;
    const child = (parent.children || []).find((cate) => cate.value === industry[1]);
    if (!child) return;
    createCompanyTeam({
      companyName: value.companyName,
      industry: `${parent.label}/${child.label}`,
    }).then(() => {
      closeCreateDrawer();
      message.open({
        content: (
          <div>
            <img src={CheckCircle} alt="" className={style.checkCircleIcon} />
            创建成功
          </div>
        ),
      });
      onConfirm();
    });
  }, 500);

  useEffect(() => {
    if (props.visible) {
      getCompanyCategories().then(({ list = [] }) => {
        const arr = list.map((item) => {
          if (item.children && item.children.length > 0) {
            item.children.forEach((child) => {
              // eslint-disable-next-line no-param-reassign
              child.children = undefined;
            });
          }
          return item;
        });
        setCategories(arr);
      });
    }
  }, [props.visible]);

  return (
    <Drawer {...props} placement="bottom" closable={false} height="80%">
      <div className={style.drawerBox}>
        <div className={style.drawerTitleBox}>
          <div />
          <div className={style.drawerTitle}>创建团队</div>
          <CloseOutlined className={style.drawerIcon} onClick={() => closeCreateDrawer()} />
        </div>
        <div className={style.drawerDescribe}>填写团队/企业信息，开启高效协作</div>
      </div>
      <div className={style.drawerForm}>
        <Form form={creatForm} onFinish={onCreatFinish} onValuesChange={onValuesChange}>
          <div className={style.formBox}>
            <div className={style.formLabel}>组织名称</div>
            <div className={style.formValue}>
              <Form.Item name="companyName" rules={[{ required: true, message: '' }]}>
                <Input maxLength={50} bordered={false} placeholder="请输入组织名称" />
              </Form.Item>
            </div>
          </div>

          <div className={style.formBox}>
            <div className={style.formLabel}>行业类型</div>
            <div className={style.formValue}>
              <Form.Item name="industry" rules={[{ required: true, message: '' }]}>
                <Cascader
                  bordered={false}
                  options={categories || []}
                  placeholder="请选择行业类型"
                  loading={!categories}
                  allowClear={false}
                  showSearch
                  suffixIcon={<Icon name="down" size={16} />}
                />
              </Form.Item>
            </div>
          </div>
          {props.visible && (
            <div className={style.btnBox}>
              <Form.Item wrapperCol={{ span: 16 }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  block
                  // eslint-disable-next-line no-nested-ternary
                  style={{ background: isDisabled ? '#c6ccd8' : isWeb ? '#0099ff' : '#ff5923' }}
                  disabled={isDisabled}
                >
                  确定
                </Button>
              </Form.Item>
            </div>
          )}
        </Form>
      </div>
    </Drawer>
  );
}

function createTeam(config: CreateTeamProps) {
  drawerPopup(CreateTeam, {
    ...config,
  });
}

export default createTeam;
