import { useCallback, useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getUserVisitSignCountVisit } from '@/apis';
import { message } from 'antd';
import { useInterval, useMount, useTimeout } from 'ahooks';
import { setToken } from '@/utils/auth';
import { setHeader } from '@/utils/http';
import loadMap from '@/utils/map';
import { Helmet } from 'react-helmet';
import { Icon } from '@/components';
import styles from './home.module.less';
import sendMessage from '../../utils/utils';
import SignIn from './components/sign-in';

declare global {
  interface Window {
    AMap: any;
  }
}

const createMap = ({
  longitude,
  latitude,
  id,
}: {
  longitude?: number;
  latitude?: number;
  id: string;
}) =>
  loadMap(['AMap.Geocoder']).then(({ Map }) => {
    // 初始化地图
    const mapOption = {
      zoom: 16,
      resizeEnable: true,
    };
    if (longitude && latitude) {
      // @ts-ignore
      mapOption.center = [longitude, latitude];
    }
    return new Map(id, mapOption);
  });

function Home() {
  const navigate = useNavigate();
  const [signParams] = useSearchParams();
  const [time, setTime] = useState('');
  const [total, setTotal] = useState(0);
  // 114.128768,22.682471
  // 114.127105,22.682827
  const [latitude, setLatitude] = useState();
  const [longitude, setLongitude] = useState();
  const [addressName, setAddressName] = useState();
  const [allAddress, setAllAddress] = useState();
  const [height, setHeight] = useState();
  const [map, setMap] = useState();
  const [isWx, setIsWx] = useState(false);

  const createMark = useCallback(
    (lng, lat) => {
      if (map) {
        // @ts-ignore
        map.setCenter([lng, lat]);
      }
      const markerOption = {
        position: [lng, lat],
        icon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',
      };
      const marker = new window.AMap.Marker(markerOption);
      if (map) {
        // @ts-ignore
        map.add(marker);
        const geocoder = new window.AMap.Geocoder();
        geocoder.getAddress([lng, lat], (status: string, result: { regeocode: any }) => {
          if (status === 'complete' && result.regeocode) {
            const { regeocode } = result;
            let name = regeocode.formattedAddress;
            const { township } = regeocode.addressComponent;
            if (township && name.includes(township)) {
              name = name.split(township)[1] || regeocode.formattedAddress;
            }
            setAddressName(name);
            setAllAddress(regeocode.formattedAddress);
          } else {
            message.error('获取地址失败');
          }
        });
      }
    },
    [map]
  );

  useEffect(() => {
    setTimeout(() => {
      if (longitude && latitude) {
        createMark(longitude, latitude);
      }
    }, 500);
  }, [latitude, longitude, createMark]);

  useMount(() => {
    setTimeout(() => {
      window.wx.miniProgram.getEnv((res: { miniprogram: boolean }) => {
        setIsWx(res.miniprogram);
      });
    });
  });

  const toAdd = () => {
    if (time && addressName && allAddress) {
      const phone = signParams.get('phone');
      const realName = signParams.get('realName');
      const appId = signParams.get('appId');
      navigate(`/m/sign/add-sign?phone=${phone}&realName=${realName}&appId=${appId}`);
      localStorage.setItem('TIME', time);
      localStorage.setItem('ADDRESS', addressName || '');
      localStorage.setItem('ALLADRESS', allAddress || '');
      localStorage.setItem('HEIGHT', JSON.stringify(height));
    } else {
      message.warning('获取位置中', 0.5);
    }
  };
  const back = () => {
    if (isWx) {
      window.wx.miniProgram.navigateBack();
    } else {
      sendMessage('back', {
        isClose: true,
      });
    }
  };

  const showTime = (times: Date) => {
    const nowdate = times;
    const year = nowdate.getFullYear();
    const month = nowdate.getMonth() + 1;
    const date = nowdate.getDate();
    const h = nowdate.getHours() < 10 ? `0${nowdate.getHours()}` : nowdate.getHours();
    const m = nowdate.getMinutes() < 10 ? `0${nowdate.getMinutes()}` : nowdate.getMinutes();
    const s = nowdate.getSeconds() < 10 ? `0${nowdate.getSeconds()}` : nowdate.getSeconds();
    return `${year}年${month}月${date}日 ${h}:${m}:${s}`;
  };

  useTimeout(() => {
    createMap({ latitude, longitude, id: 'map-container' }).then((res) => {
      setMap(res);
    });
  }, 500);
  useInterval(() => {
    setTime(showTime(new Date()));
  }, 1000);
  useEffect(() => {
    setTimeout(async () => {
      if (signParams.get('isWx') === 'true') {
        const authorization = signParams.get('authorization');
        const avatar = signParams.get('avatar');
        const companyName = signParams.get('companyName');
        const latitudeWx = Number(signParams.get('LATITUDE')) as any;
        const longitudeWx = Number(signParams.get('LONGITUDE')) as any;
        const heightWx = signParams.get('height');

        if (authorization) {
          setToken(authorization);
          getUserVisitSignCountVisit().then((res) => {
            setTotal(res.total);
          });
        }
        if (longitudeWx && latitudeWx) {
          setLatitude(latitudeWx);
          setLongitude(longitudeWx);
          localStorage.setItem('LATITUDE', JSON.stringify(latitudeWx));
          localStorage.setItem('LONGITUDE', JSON.stringify(longitudeWx));
        }
        if (height) {
          setHeight(heightWx as any);
        }
        if (avatar && companyName) {
          localStorage.setItem(
            'USERINFO',
            JSON.stringify({
              avatar,
              companyName,
            })
          );
        }
      } else {
        const token = await new Promise((resolve) => {
          sendMessage('getUserInfo', (el) => {
            setHeader('satype', 'IOS');
            setToken(el.authorization);
            localStorage.setItem(
              'USERINFO',
              JSON.stringify({
                avatar: el.avatar,
                company: el.companyName,
              })
            );
            resolve(el.authorization);
          });
        });
        if (token) {
          getUserVisitSignCountVisit().then((res) => {
            setTotal(res.total);
          });
        }
        sendMessage('location', (el) => {
          setLatitude(el.latitude);
          setLongitude(el.longitude);
          localStorage.setItem('LATITUDE', JSON.stringify(el.latitude));
          localStorage.setItem('LONGITUDE', JSON.stringify(el.longitude));
        });
        sendMessage('statusBarHeight', (el) => {
          setHeight(el.height);
        });
      }
    }, 300);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.wrap}>
      {!isWx && (
        <Icon
          name="left"
          size={28}
          color="#333333"
          style={{ top: height }}
          className={styles.left}
          onClick={back}
        />
      )}
      <div id="map-container" className={styles.app} />
      <div className={styles.second}>
        <div className={styles.addressName}>
          <img
            className={styles.icon}
            src="https://img.huahuabiz.com/user_files/2022720/1658311103638992.png"
            alt=""
          />
          <div className={styles.placeName}>{addressName}</div>
        </div>
        <div className={styles.adress}>{allAddress}</div>
        <SignIn onClick={toAdd} time={time} />
        {total === 0 ? (
          <div className={styles.tips}>今日你还未签到</div>
        ) : (
          <div className={styles.tips}>
            <span className={styles.isSign} /> 今日已签到
            <span className={styles.num}>{total}</span>次
          </div>
        )}
      </div>
      <Helmet>
        <title>签到</title>
      </Helmet>
    </div>
  );
}

export default Home;
