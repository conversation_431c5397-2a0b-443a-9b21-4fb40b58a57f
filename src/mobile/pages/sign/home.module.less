.wrap {
  width: 100%;
  height: 100%;
  position: relative;
}

.app {
  width: 100%;
  height: 300px;
}

.left {
  line-height: 44px;
  position: absolute;
  left: 16px;
  // top: 35px;
  z-index: 90000;
}

.second {
  width: 100%;
  height: calc(100% - 260px);
  overflow: hidden;
  overflow-y: auto;
  position: fixed;
  bottom: 0;
  z-index: 9999;
  background-color: #f5f6fa;
  border-radius: 12px 12px 0 0;
}

.addressName {
  display: flex;
  margin-top: 38px;
  padding: 0 25px;
  justify-content: center;
  align-items: center;
}

.icon {
  width: 24px;
  height: 24px;
}

.placeName {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
  display: -webkit-box;
  height: 22px;
  line-height: 22px;
  margin-left: 2px;
  overflow: hidden;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.adress {
  color: #888b98;
  font-size: 14px;
  display: -webkit-box;
  width: 100%;
  height: 20px;
  line-height: 20px;
  margin-top: 8px;
  padding: 0 25px;
  overflow: hidden;
  text-align: center;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.num {
  color: #49d27d;
  font-weight: 500;
}

.isSign {
  display: inline-block;
  width: 14px;
  height: 14px;
  background-image: url('https://img.huahuabiz.com/user_files/2022727/1658914306888364.png');
  background-size: 14px;
  vertical-align: -3px;
}

.tips {
  color: #888b98;
  font-size: 14px;
  width: 100%;
  height: 16px;
  line-height: 16px;
  margin-top: 20px;
  text-align: center;
}
