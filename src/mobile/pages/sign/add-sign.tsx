/* eslint-disable jsx-a11y/control-has-associated-label */
import { useMount } from 'ahooks';
import { userVisitSignAddVisitSign } from '@/apis';
import { Icon } from '@/components';
import { Input, Image, message, InputNumber } from 'antd';
import { TextArea } from 'antd-mobile';
import cloneDeep from 'lodash/cloneDeep';
import { BaseSyntheticEvent, useState } from 'react';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import { useNavigate, useSearchParams } from 'react-router-dom';
import sendMessage from '../../utils/utils';
import styles from './add-sign.module.less';

declare let wx: any;

function AddSign() {
  const navigate = useNavigate();
  const [signParams] = useSearchParams();
  const userInfo = JSON.parse(localStorage.getItem('USERINFO') || '{}');
  const phone = signParams.get('phone') || '';
  const realName = signParams.get('realName') || '';
  const address = localStorage.getItem('ADDRESS') || '';
  const allAddress = localStorage.getItem('ALLADRESS') || '';
  const height = localStorage.getItem('HEIGHT') || '';
  const [visitObject, setVisitObject] = useState('');
  const [tips, setTips] = useState('');
  const [isShowBtn, setIsShowBtn] = useState(true);
  const time =
    localStorage.getItem('TIME')?.replace('/', '年').replace('/', '月').slice(0, -3) || '';
  const [imgs, setImgs] = useState([] as string[]);
  const [isBtn, setIsBtn] = useState(true);
  const [isWx, setIsWx] = useState(false);
  const [wxDate, setWxDate] = useState({
    phone,
    signUserName: realName,
    companyName: '',
  });
  let btn;
  const changeVisitObject = (e: BaseSyntheticEvent) => {
    setVisitObject(e.target.value);
  };
  const changeTips = (e: string) => {
    setTips(e);
  };
  const deleteCha = (index: number) => {
    const newImgs = cloneDeep(imgs);
    newImgs.splice(index, 1);
    setImgs([...newImgs]);
  };
  const handleCamera = () => {
    if (imgs.length < 9) {
      sendMessage('takePhoto', { address: allAddress }, (el: Record<string, string>) => {
        imgs.push(`${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${el.url}`);
        setImgs([...imgs]);
      });
    }
  };
  const previewImg = (src: string) => {
    sendMessage('interfaceJump', {
      pageCode: '10210',
      param: {
        photos: [src],
        index: 0,
        isPreView: false,
      },
    });
  };

  const prompt = (text: string) => {
    message.error(text);
    setIsShowBtn(true);
  };

  const submit = () => {
    setIsShowBtn(false);
    const latitude = localStorage.getItem('LATITUDE') || '';
    const longitude = localStorage.getItem('LONGITUDE') || '';
    const appId = signParams.get('appId') || '';
    if ((!visitObject.length || !imgs.length) && !isWx) {
      const text = !visitObject.length ? '拜访对象不能为空' : '图片至少上传一张';
      prompt(text);
      return;
    }
    const reg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
    if ((!wxDate.phone || !wxDate.signUserName.length) && isWx) {
      const text = !wxDate.phone.length ? '电话不能为空' : '姓名不能为空';
      prompt(text);
      return;
    }

    if (!reg.test(wxDate.phone) && isWx) {
      prompt('手机号有误');
      return;
    }
    const params = {
      visitNew: visitObject,
      visitRemark: tips,
      signAddressLatitude: latitude,
      signAddressLongitude: longitude,
      signAddress: allAddress,
      signPhoto: JSON.stringify(imgs),
      phone: wxDate.phone,
      signUserName: wxDate.signUserName,
      companyName: wxDate.companyName,
      appId,
    };
    if (!latitude || !longitude) {
      setIsShowBtn(true);
      message.error('未获取到位置');
    } else {
      userVisitSignAddVisitSign(params)
        .then(() => {
          sendMessage('back', {
            isClose: false,
          });
          setIsBtn(true);
          message.success('签到成功');
          setTimeout(() => {
            wx.miniProgram.navigateBack();
          }, 1500);
        })
        .catch(() => {
          setIsShowBtn(true);
          message.error('签到失败');
        });
    }
  };
  if (isBtn) {
    if (isShowBtn) {
      btn = (
        <div className={styles.btn} onClick={submit} role="button" tabIndex={0}>
          提交
        </div>
      );
    } else {
      btn = <div className={styles.btn}>提交</div>;
    }
  } else {
    btn = <div />;
  }

  // const focusInput = () => {
  //   setIsBtn(false);
  // };
  // const blurInput = () => {
  //   setIsBtn(true);
  // };
  const back = () => {
    navigate(-1);
    sendMessage('back', {
      isClose: true,
    });
  };
  const close = () => {
    if (isWx) {
      wx.miniProgram.navigateBack();
    } else {
      sendMessage('back', {
        isClose: true,
      });
    }
  };

  useMount(() => {
    setTimeout(() => {
      wx.miniProgram.getEnv((res: { miniprogram: boolean }) => {
        setIsWx(res.miniprogram);
      });
    });
  });

  return (
    <div className={styles.wrap}>
      <div className={styles.content}>
        {!isWx && (
          <div className={styles.left} style={{ top: Number(height) }}>
            <Icon name="left" size={28} color="#333333" onClick={back} className={styles.back} />
            <div onClick={close} className={styles.close} role="button" tabIndex={0}>
              <img src="https://img.huahuabiz.com/user_files/2022728/1658973025386686.png" alt="" />
            </div>
          </div>
        )}
        <div className={styles.first}>
          <div className={styles.avatar}>
            <img className={styles.img} src={userInfo.avatar} alt="" />
          </div>
          <div className={styles.placeName}>{address}</div>
          <div className={styles.adress}>{allAddress}</div>
          <div className={styles.time}>{time}</div>
        </div>
        <div className={styles.second}>
          {!isWx && (
            <div className={styles.companyName}>
              <span>当前组织：</span>
              <span>{userInfo.company}</span>
            </div>
          )}
          <div className={styles.visitObject}>
            {!isWx ? (
              <>
                <div className={classNames(styles.people, styles.titleBaz)}>
                  <span className={classNames(styles.title, styles.titleFoo)}>拜访对象</span>
                  <Input
                    id="input"
                    className={styles.input}
                    maxLength={30}
                    placeholder="请输入"
                    bordered={false}
                    onChange={changeVisitObject}
                    // onFocus={focusInput}
                    // onBlur={blurInput}
                  />
                </div>
                <div className={styles.tips}>
                  <span className={styles.title}>备注</span>
                  <TextArea
                    id="textarea"
                    rows={4}
                    maxLength={200}
                    showCount
                    placeholder="请填写签到备注"
                    className={styles.textarea}
                    onChange={changeTips}
                    // onFocus={focusInput}
                    // onBlur={blurInput}
                    autoSize
                  />
                </div>
              </>
            ) : (
              <>
                <div className={classNames(styles.peopleFoo, styles.titleBaz)}>
                  <span className={classNames(styles.title, styles.titleWx)}>姓名</span>
                  <Input
                    id="input"
                    className={styles.input}
                    defaultValue={wxDate.signUserName}
                    maxLength={11}
                    key="1"
                    placeholder="请输入姓名"
                    bordered={false}
                    onChange={(e) => {
                      setWxDate({
                        ...wxDate,
                        signUserName: e.target.value,
                      });
                    }}
                  />
                </div>
                <div className={classNames(styles.peopleFoo, styles.titleBaz)}>
                  <span className={classNames(styles.title, styles.titleWx)}>电话</span>
                  <InputNumber
                    style={{ width: '114px' }}
                    id="input"
                    className={styles.input}
                    defaultValue={wxDate.phone}
                    value={wxDate.phone}
                    key="2"
                    maxLength={11}
                    placeholder="请输入电话"
                    bordered={false}
                    onChange={(e) => {
                      setWxDate({
                        ...wxDate,
                        phone: e || '',
                      });
                    }}
                  />
                </div>
                <div className={classNames(styles.peopleFoo)} style={{ borderBottom: 'none' }}>
                  <span className={styles.title}>公司名称</span>
                  <Input
                    id="input"
                    defaultValue={wxDate.companyName}
                    className={styles.input}
                    key="3"
                    maxLength={30}
                    placeholder="请输入公司名称"
                    bordered={false}
                    onChange={(e) => {
                      setWxDate({
                        ...wxDate,
                        companyName: e.target.value,
                      });
                    }}
                  />
                </div>
              </>
            )}
          </div>
          {!isWx && (
            <div className={styles.updatepicture}>
              <div className={classNames(styles.picture, styles.titleBaz)}>
                <span className={classNames(styles.title, styles.titleFoo)}>图片 </span>
                <span className={styles.num}>({imgs.length}/9)</span>
              </div>
              <div className={styles.imgs}>
                {imgs.map((item, index) => (
                  <div className={styles.delete} key={item}>
                    <span
                      className={styles.cha}
                      onClick={() => deleteCha(index)}
                      role="button"
                      tabIndex={0}
                    >
                      <img
                        src="https://img.huahuabiz.com/user_files/2022727/1658918538726776.png"
                        alt=""
                      />
                    </span>
                    <Image
                      height={92}
                      width={92}
                      className={styles.imgsItem}
                      src={item}
                      preview={false}
                      alt=""
                      onClick={() => previewImg(item)}
                    />
                  </div>
                ))}
                {imgs.length >= 9 ? null : (
                  <div className={styles.add} onClick={handleCamera} role="button" tabIndex={0} />
                )}
              </div>
            </div>
          )}
        </div>
      </div>
      <div className={styles.btnWrap}>{btn}</div>
      <Helmet>
        <title>签到</title>
      </Helmet>
    </div>
  );
}

export default AddSign;
