import React, { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import { channelQueryJoinImage, channelQueryState, iformEnableDefault } from '@/apis';
import { Button, Modal, message } from 'antd';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { setToken } from '@/utils/auth';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import styles from './indesx.module.less';

function ChannelHome() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [params] = useSearchParams({ token: '', appId: '' });
  const [imageInfo, setImageInfo] = useState({
    companyId: 0,
    id: 0,
    invitePoster: '',
  });
  const [items, setItems] = useState<any>([]);

  const [stateInfo, setStateInfo] = useState({
    id: 0,
    refuseReason: '',
    status: 1,
  });

  const { run: getImage } = useRequest(channelQueryJoinImage, {
    manual: true,
    defaultParams: [{ appId: params.get('appId') || '' }],
    onSuccess: (result) => {
      setImageInfo({ ...result });
    },
  });

  const { run: getState } = useRequest(channelQueryState, {
    manual: true,
    defaultParams: [{ appId: params.get('appId') || '' }],
    onSuccess: (result) => {
      if (result.status === 2) {
        Modal.confirm({
          className: styles.modal,
          width: 250,
          title: t('channel_reject_reason'),
          content: result.refuseReason,
          icon: null,
          centered: true,
          cancelText: t('channel_close'),
          okText: t('channel_reapply'),
          onOk: () => {
            if (!items.length) {
              message.warning(t('channel_no_default_form'));
              return;
            }
            navigate(
              `/m/channel-join?token=${params.get('token')}&appId=${params.get('appId')}&id=${
                result.id
              }`
            );
          },
          onCancel: () => window.wx?.miniProgram.navigateBack(),
        });
      }
      setStateInfo({ ...result });
    },
  });

  const { run: getForm } = useRequest(iformEnableDefault, {
    manual: true,
    defaultParams: [{ type: 5, appId: params.get('appId') || '' }],
    onSuccess: (result) => {
      getState({ appId: params.get('appId') || '' });
      setItems(result.components);
    },
  });

  useEffect(() => {
    const token = params.get('token');
    if (token) {
      setToken(token || '');
    }
    getImage({ appId: params.get('appId') || '' });
    getForm({ type: 5, appId: params.get('appId') || '' });
  }, [getImage, params, getState, getForm]);

  return (
    <div className={styles.body}>
      <Helmet>
        <title>{t('channel_apply_become_partner')}</title>
      </Helmet>
      <div className={styles.image}>
        <img
          src={
            imageInfo.invitePoster
              ? imageInfo.invitePoster
              : 'https://img.huahuabiz.com/user_files/2023524/1684921395994590.png'
          }
          alt=""
        />
      </div>
      {stateInfo.status !== 2 && stateInfo.status !== 3 && (
        <div className={styles.footer}>
          <Button
            type="primary"
            disabled={stateInfo.status === 0}
            onClick={() => {
              if (!items.length) {
                message.warning(t('channel_no_default_form'));
                return;
              }
              navigate(`/m/channel-join?token=${params.get('token')}&appId=${params.get('appId')}`);
            }}
          >
            {stateInfo.status === 0
              ? t('channel_submitted_reviewing')
              : t('channel_apply_become_partner')}
          </Button>
        </div>
      )}
    </div>
  );
}

export default ChannelHome;
