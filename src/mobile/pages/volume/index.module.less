.volume {
  height: 100vh;
  padding: 16px;
  overflow-y: auto;
  background: #f5f6fa;

  &::-webkit-scrollbar {
    display: none;
  }

  .card {
    display: flex;
    height: 100px;
    margin-bottom: 16px;
    border-radius: 8px;
    background-color: #fff;
    overflow: hidden;
  }

  .cardLeft {
    color: #fff;
    display: flex;
    width: 100px;
    min-width: 100px;
    justify-content: center;
    background: linear-gradient(90deg, #ff2201 0%, #ff833c 100%);
    align-items: center;
    flex-direction: column;
  }

  .dollar {
    font-size: 12px;
    font-weight: bold;
  }

  .cardLeftPrice {
    font-size: 30px;
    font-weight: bold;
  }

  .cardRight {
    padding: 12px;
    overflow: hidden;
    flex: 1;
  }

  .cardRightTitle {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .cardRightDesc {
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
  }

  .price {
    font-size: 22px;
    font-weight: bold;
    margin-right: 2px;
  }

  .integral {
    color: #ff5923;
    font-size: 12px;
  }

  .time {
    color: #888b98;
    font-size: 12px;
    margin-top: 2px;
  }

  .exchange {
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    width: 48px;
    height: 28px;
    line-height: 28px;
    position: absolute;
    top: -6px;
    right: 0;
    text-align: center;
    border-radius: 8px;
    background: #ff5923;
  }

  .disabled {
    background: #c6ccd8;
  }
}
