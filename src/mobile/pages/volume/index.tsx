import { useState } from 'react';
import { Helmet } from 'react-helmet';
import { useSearchParams } from 'react-router-dom';
import { useMount } from 'ahooks';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';
import { Empty } from '@/components';
import dayjs from 'dayjs';
import { getPointChangeCoupon, postPointExchangeCoupon } from '@/apis';
import { GetPointChangeCouponResult } from '@/apis/user/get-point-change-coupon';
import { Modal, message } from 'antd';
import classNames from 'classnames';
import styles from './index.module.less';

function IntegralVolume() {
  const [params] = useSearchParams();
  const tenantId = params.get('tenantId') || '';
  const [cardList, setCardList] = useState<GetPointChangeCouponResult[]>([]);

  const getList = () => {
    getPointChangeCoupon({ tenantId, type: 0 }).then((res) => {
      setCardList(res.list);
    });
  };

  const exchange = (item: GetPointChangeCouponResult) => {
    if (!item.isPointEnough) return;
    const { tenantId: tenantIdFoo, needPoint, businessId, type } = item;
    const data = {
      tenantId: tenantIdFoo,
      needPoint,
      businessId,
      type,
    };
    Modal.confirm({
      title: '提示',
      content: '确定兑换吗？',
      centered: true,
      onOk: () => {
        postPointExchangeCoupon(data).then(() => {
          message.success('兑换成功');
          getList();
        });
      },
    });
  };

  // 进入页面
  useMount(() => {
    const token = params.get('token');
    if (token) {
      setHeader('satype', 'ech-aop');
      setToken(token);
    }
    getList();
  });
  return (
    <div className={styles.volume}>
      <Helmet>
        <title>积分兑好券</title>
      </Helmet>
      {cardList.length ? (
        cardList.map((item) => (
          <div className={styles.card}>
            <div className={styles.cardLeft}>
              <div className={styles.cardLeftPrice}>
                <span className={styles.dollar}>￥</span>
                {item.denomination}
              </div>
              <div>{item.couponName}</div>
            </div>
            <div className={styles.cardRight}>
              <div className={styles.cardRightTitle}>{item.name}</div>
              <div className={styles.cardRightDesc}>
                <div>
                  <div className={styles.integral}>
                    <span className={styles.price}>{item.needPoint}</span>积分
                  </div>
                  <div className={styles.time}>
                    {dayjs(item.couponBeginTime).format('YYYY-MM-DD')} 至
                    {dayjs(item.couponEndTime).format('YYYY-MM-DD')}
                  </div>
                </div>
                <div
                  className={classNames(styles.exchange, {
                    [styles.disabled]: !item.isPointEnough,
                  })}
                  tabIndex={0}
                  role="button"
                  onClick={() => exchange(item)}
                >
                  兑换
                </div>
              </div>
            </div>
          </div>
        ))
      ) : (
        <Empty message="暂无优惠券" />
      )}
    </div>
  );
}

export default IntegralVolume;
