import { createContext, Dispatch, SetStateAction } from 'react';
import { EMPTY_FN } from '@/utils/const';

export interface ContextDataOption {
  disId: number;
  planId: number;
  disCompany: number;
  isBindGoods: number;
}

export const defaultContext = { disId: 0, planId: 0, disCompany: 0, isBindGoods: 1 };

export interface DisManageContextProps {
  contextData: ContextDataOption;
  setContextData: Dispatch<SetStateAction<ContextDataOption>>;
}

export const DisManageContext = createContext({
  contextData: defaultContext,
  setContextData: EMPTY_FN,
} as DisManageContextProps);
