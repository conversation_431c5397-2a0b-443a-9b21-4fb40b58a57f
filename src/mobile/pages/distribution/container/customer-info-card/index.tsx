import dayjs from 'dayjs';
import styles from './index.module.less';
import Card from '../../components/card/card';

interface CustomerInfoCardProps {
  expirationTime: number;
  remainingDays: number;
  isYueAnJu?: boolean;
  leaderNumber?: number;
  isShowExpirationTime?: boolean;
  isShowRenewBtn?: boolean;
  onRenew?: MultipleParamsFn<[]>;
}

function CustomerInfoCard({
  expirationTime,
  remainingDays,
  isYueAnJu,
  leaderNumber,
  isShowExpirationTime,
  isShowRenewBtn,
  onRenew,
}: CustomerInfoCardProps) {
  return (
    <Card title="我的信息" className={styles.cusCard} isShowIcon={false}>
      <div className={styles.item}>
        {isShowExpirationTime && (
          <>
            <div className={styles.itemLeft} style={{ width: 155 }}>
              到期日期：{expirationTime ? dayjs(expirationTime).format('YYYY-MM-DD') : ''}
            </div>
            <div className={styles.itemRight}>
              {remainingDays >= 0 && remainingDays < 30 ? (
                <div className={styles.warnText}>不足{remainingDays}天</div>
              ) : null}
              {remainingDays < 0 ? <div className={styles.warnText}>已过期</div> : null}
              {isShowRenewBtn && (
                <div
                  className={styles.operateText}
                  role="presentation"
                  onClick={() => {
                    onRenew?.();
                  }}
                >
                  去续费
                </div>
              )}
            </div>
          </>
        )}
      </div>
      {isYueAnJu ? (
        <div className={styles.item} style={{ marginTop: 8 }}>
          <div className={styles.itemLeft}>剩余带客次数：{leaderNumber}次</div>
        </div>
      ) : null}
    </Card>
  );
}

CustomerInfoCard.defaultProps = {
  isYueAnJu: false,
  leaderNumber: 0,
  isShowExpirationTime: true,
  isShowRenewBtn: false,
  onRenew: () => {},
};

export default CustomerInfoCard;
