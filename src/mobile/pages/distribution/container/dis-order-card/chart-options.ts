const chartOptions = {
  type: 'gauge',
  radius: '140%',
  startAngle: 180,
  endAngle: 0,
  min: 0,
  center: ['50%', '85%'], // 仪表位置

  progress: {
    show: true,
    roundCap: true,
    width: 24,
  },

  axisTick: {
    show: false,
  },
  axisLabel: {
    show: false,
  },
  splitLine: {
    show: false, // 不显示网格线
  },
  pointer: {
    show: false,
  },
  detail: {
    backgroundColor: '#fff',
    borderColor: '#999',
    borderWidth: 2,
    width: '60%',
    lineHeight: 40,
    height: 40,
    borderRadius: 8,
    offsetCenter: [0, '35%'],
    valueAnimation: true,
    formatter() {
      return '';
    },
    rich: {
      value: {
        fontSize: 50,
        fontWeight: 'bolder',
        color: '#777',
      },
      unit: {
        fontSize: 20,
        color: '#999',
        padding: [0, 0, -20, 10],
      },
    },
  },
};

export default chartOptions;
