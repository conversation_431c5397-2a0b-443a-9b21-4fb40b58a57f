import classnames from 'classnames';
import { useEffect, useState } from 'react';
import { OrderTotalStatist } from '@/apis/order/get-total-statistic';
import { useLocation } from 'react-router-dom';
// import { useMount } from 'ahooks';
import { getTotalStatistic } from '@/apis';
import { Echarts } from '@/components';
import { useEcharts } from '@/hooks';
import { useTranslation } from 'react-i18next';
import { getCustomizeTime, getQueryVariable } from '../../utils/util';
import style from './dis-order-card.module.less';
import DropdownSelect from '../../components/dropdown-select/dropdown-select';
import Card from '../../components/card/card';
import chartOptions from './chart-options';

export interface IChartTypeMap {
  supplier: number;
  distributor: number;
  sales: number;
}
interface DisOrderCardProps {
  type: keyof IChartTypeMap;
  tenantId?: string;
  width?: string;
  isFullNetworkQuery?: number;
  isTenant?: number;
}

const ChartTypeMap: IChartTypeMap = {
  supplier: 1,
  distributor: 2,
  sales: 3,
};

function DisOrderCard({ type, tenantId, width, isFullNetworkQuery, isTenant }: DisOrderCardProps) {
  const echartRef = useEcharts();
  const { t } = useTranslation();
  // eslint-disable-next-line no-unused-vars
  const [result, setResult] = useState({} as OrderTotalStatist);
  const [seleTime, setSeleTime] = useState('month');
  const [currentIndex, setCurrentIndex] = useState(0);
  const { search } = useLocation();
  const distributionOrgId = getQueryVariable(search, '_shareOrgId');
  // const navigate = useNavigate();

  const months = [
    { label: t('distribution_order_month1'), key: '1' },
    { label: t('distribution_order_month2'), key: '2' },
    { label: t('distribution_order_month3'), key: '3' },
    { label: t('distribution_order_month4'), key: '4' },
    { label: t('distribution_order_month5'), key: '5' },
    { label: t('distribution_order_month6'), key: '6' },
    { label: t('distribution_order_month7'), key: '7' },
    { label: t('distribution_order_month8'), key: '8' },
    { label: t('distribution_order_month9'), key: '9' },
    { label: t('distribution_order_month10'), key: '10' },
    { label: t('distribution_order_month11'), key: '11' },
    { label: t('distribution_order_month12'), key: '12' },
  ];
  const options = [
    { label: t('distribution_rank_thisWeek'), key: 'week' },
    { label: t('distribution_rank_thisMonth'), key: 'month' },
    { label: t('distribution_rank_lastMonth'), key: 'lastMonth' },
    { label: t('distribution_rank_thisYear'), key: 'year' },
    { label: t('distribution_rank_lastYear'), key: 'lastYear' },
  ];

  const renderEchart = (value: number, max: number) => {
    const option = {
      series: [
        {
          ...chartOptions,
          max,
          data: [
            {
              value,
            },
          ],
          itemStyle: {
            color: max > 0 ? '#16DBCC' : '#DADBE0',
          },
          axisLine: {
            roundCap: true,
            lineStyle: {
              width: 24,
              color: [
                [1, max > 0 ? '#008CFF' : '#DADBE0'], // 90%-100%处的颜色
              ],
            },
          },
        },
      ],
    };
    echartRef.current?.setOption(option, true);
  };

  const getTotalStatisticData = (time: { startDate: string; endDate: string }) => {
    const { startDate } = time;
    const { endDate } = time;
    getTotalStatistic({
      chartType: ChartTypeMap[type],
      statisticType: 4,
      startDate: new Date(startDate.replace(/-/g, '/')).getTime(),
      endDate: new Date(endDate.replace(/-/g, '/')).getTime(),
      isTenant: isTenant || 0,
      ...(tenantId ? { tenantId } : {}),
      ...(isFullNetworkQuery ? { isFullNetworkQuery } : {}),
    }).then((res) => {
      setResult(res);
      renderEchart(res.doingTotalAmount, res.doingTotalAmount + res.endTotalAmount);
    });
  };

  const selectTime = (key: string) => {
    setSeleTime(key);
    const time = getCustomizeTime(key);
    getTotalStatisticData(time);
  };

  const opt = () => {
    const nowMonth = new Date().getMonth() + 1;
    const x = nowMonth - 3 >= 0 ? nowMonth - 3 : 12 - Math.abs(nowMonth - 3);
    const y = nowMonth - 4 >= 0 ? nowMonth - 4 : 12 - Math.abs(nowMonth - 4);
    options.splice(options.length - 2, 0, months[x]);
    options.splice(options.length - 2, 0, months[y]);
    return (
      <div>
        <DropdownSelect
          options={options}
          value={seleTime}
          defaultValue="month"
          select={(key) => {
            selectTime(key as string);
          }}
        />
      </div>
    );
  };

  const changeTab = (index: number) => {
    setCurrentIndex(index);
  };

  // const toOrder = (orderStatus: number) => {
  //   const time = getCustomizeTime(seleTime);
  //   if (type === 'supplier' && !distributionOrgId) {
  //     window.open(
  //       `/distribution/order?startTime=${new Date(
  //         time.startDate.replace(/-/g, '/')
  //       ).getTime()}&endTime=${new Date(
  //         time.endDate.replace(/-/g, '/')
  //       ).getTime()}&orderStatus=${orderStatus}&region=1&isOnlyDistributed=1`
  //     );
  //   } else if (distributionOrgId && type !== 'supplier') {
  //     window.open(
  //       `/distribution/order?startTime=${new Date(
  //         time.startDate.replace(/-/g, '/')
  //       ).getTime()}&endTime=${new Date(
  //         time.endDate.replace(/-/g, '/')
  //       ).getTime()}&orderStatus=${orderStatus}&region=1&distribution=${distributionOrgId}&isDistributionOrder=1`
  //     );
  //   }
  // };

  const splitAmount = (amount = 0) => {
    if (amount === 0) {
      return (
        <span>
          <span>0</span>
        </span>
      );
    }
    const arr = amount.toString().split('.');
    return (
      <span>
        <span>{arr[0]}</span>
        {arr[1] && <span>.</span>}
        <span className={style.decimal}>{arr[1]}</span>
      </span>
    );
  };

  useEffect(() => {
    getTotalStatisticData(getCustomizeTime(seleTime));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [type]);

  return (
    <Card
      title={t('distribution_order_title')}
      height="365px"
      width={width}
      opt={opt()}
      isShowIcon={false}
    >
      <div className={style.tabs}>
        <div
          className={classnames(style.tabItem, currentIndex === 0 && style.active)}
          role="button"
          tabIndex={0}
          onClick={() => {
            changeTab(0);
          }}
        >
          {t('distribution_orderStats_amountTab')}
        </div>
        <div
          className={classnames(style.tabItem, currentIndex === 1 && style.active)}
          role="button"
          tabIndex={0}
          onClick={() => {
            changeTab(1);
          }}
        >
          {t('distribution_orderStats_countTab')}
        </div>
      </div>
      <div className={style.echartBox}>
        <Echarts className={style.echart} ref={echartRef} />
        {currentIndex === 0 ? (
          <div className={style.echartInfoBox}>
            <div className={style.amount}>
              {result.totalAmount > 0 && <span className={style.sign}>¥</span>}
              {splitAmount(result.totalAmount || 0)}
            </div>
            <div className={style.title}>{t('distribution_orderStats_totalAmount')}</div>
          </div>
        ) : (
          <div className={style.echartInfoBox}>
            <div className={style.amount}>{result.totalCount || 0}</div>
            <div className={style.title}>{t('distribution_orderStats_totalCount')}</div>
          </div>
        )}
      </div>
      <div
        className={classnames(
          style.statisticsBox,
          ((type === 'supplier' && !distributionOrgId) ||
            (distributionOrgId && type !== 'supplier')) &&
            style.pointer
        )}
      >
        <div
          role="button"
          tabIndex={0}
          onClick={() => {
            // toOrder(2);
          }}
        >
          <div className={style.title}>
            <span className={style.greenPoint} />
            {t('distribution_orderStats_processing')}
          </div>
          {currentIndex === 0 ? (
            <div className={style.amount}>
              {result.doingTotalAmount > 0 && <span className={style.sign}>¥</span>}
              {splitAmount(result.doingTotalAmount || 0)}
            </div>
          ) : (
            <div className={style.amount}>{result.doingTotalCount || 0}</div>
          )}
        </div>
        <div
          role="button"
          tabIndex={0}
          onClick={() => {
            // toOrder(80);
          }}
        >
          <div className={style.title}>
            <span className={style.bluePoint} />
            {t('distribution_orderStats_completed')}
          </div>
          {currentIndex === 0 ? (
            <div className={style.amount}>
              {result.endTotalAmount > 0 && <span className={style.sign}>¥</span>}
              {splitAmount(result.endTotalAmount || 0)}
            </div>
          ) : (
            <div className={style.amount}>{result.endTotalCount || 0}</div>
          )}
        </div>
      </div>
    </Card>
  );
}

DisOrderCard.defaultProps = {
  width: 'auto',
  tenantId: '',
  isFullNetworkQuery: 0,
  isTenant: 1,
};
export default DisOrderCard;
