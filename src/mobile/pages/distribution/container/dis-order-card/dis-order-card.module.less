/* stylelint-disable no-descending-specificity */
.tabs {
  display: flex;
  justify-content: center;

  .tabItem {
    color: #040919;
    font-size: 14px;
    display: inline-block;
    width: 88px;
    height: 36px;
    line-height: 36px;
    background: #f5f6fa;
    text-align: center;

    &.active {
      background: #d9eeff;
      color: #008cff;
    }

    &:first-child {
      border-bottom-left-radius: 10px;
      border-top-left-radius: 10px;
    }

    &:last-child {
      border-bottom-right-radius: 10px;
      border-top-right-radius: 10px;
    }
  }
}

.echartBox {
  width: 100%;
  margin-bottom: 40px;
  position: relative;

  .echart {
    width: 100%;
    height: 160px;
    margin: 0 auto;
  }

  .echartInfoBox {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%);

    .amount {
      color: #040919;
      font-size: 22px;
      font-weight: 600;
      line-height: 26px;
      text-align: center;

      .decimal {
        font-size: 14px;
      }
    }

    .sign {
      font-size: 16px;
      font-weight: 600;
      line-height: 26px;
      text-align: center;
    }

    .title {
      text-align: center;
    }
  }
}

.title {
  color: #888b98;
  font-size: 14px;
  line-height: 16px;
  margin-bottom: 12px;
}

.point() {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.grayPoint {
  background: #888b98;
  .point();
}

.greenPoint {
  background: #16d9cd;
  .point();
}

.bluePoint {
  background: #008cff;
  .point();
}

.statisticsBox {
  display: flex;
  padding: 0 16px;
  justify-content: space-between;

  &.pointer {
    cursor: pointer;
  }

  .echartInfoBox {
    .amount {
      font-size: 22px;
    }
  }

  .amount {
    color: #3d3d3d;
    font-size: 20px;
    font-weight: 600;
    line-height: 20px;
    padding-left: 14px;

    .decimal {
      font-size: 14px;
    }
  }

  .sign {
    color: #040919;
    font-size: 14px;
    font-weight: 600;
    line-height: 26px;
    text-align: center;
  }
}
