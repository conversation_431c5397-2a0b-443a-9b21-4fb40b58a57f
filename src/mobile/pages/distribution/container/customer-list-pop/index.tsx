import { useEffect, useState, useImperativeHandle, forwardRef, useRef } from 'react';
import { Checkbox, Spin, Divider } from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Search, Empty } from '@/components';
import debounce from 'lodash/debounce';
import dayjs from 'dayjs';
import {
  queryMyCustomerList,
  // batchDeleteCustomer
} from '@/apis';
import { RecordListType } from '@/apis/ims/query-my-customer-list';
// import { testPerm } from '@/utils/permission';
import CustomerAvatar from '@/src/distribution/components/customer-avatar';
import styles from './index.module.less';

interface PropsType {
  visible: boolean;
  distributorId: number;
  onJumpDetail: MultipleParamsFn<[val: RecordListType]>;
  // onRefresh: MultipleParamsFn<[]>;
  tenantId: string;
  isDistributor: number;
}

const CustomerListPop = forwardRef(
  ({ visible, distributorId, onJumpDetail, tenantId, isDistributor }: PropsType, ref) => {
    const [loading, setLoading] = useState(false);
    const [customerList, setCustomerList] = useState([] as RecordListType[]);
    // const [currentItem, setCurrentItem] = useState<RecordListType>({
    //   customerCompanyId: 0,
    //   customerName: '',
    //   logoUrl: '',
    //   companyNature: 0,
    //   joinTime: 0,
    //   id: 0,
    //   customerCompanyName: '',
    //   remark: '',
    // });
    const [checkedCustomerCompanyIds, setCheckedCustomerCompanyIds] = useState<number[]>([]);
    const [checkedIds, setCheckedIds] = useState<number[]>([]);
    // const [checkAll, setCheckAll] = useState<boolean>(false);
    // const [indeterminate, setIndeterminate] = useState(false);
    const [isBatch, setIsBatch] = useState(false);
    // const [showDetailDrawer, setShowDetailDrawer] = useState(false);

    const [load, setLoad] = useState(false);
    const [searchName, setSearchName] = useState('');
    const [isHasMore, setIsHasMore] = useState(false);
    const customerParams = useRef<{ pageNo: number; pageSize: number; totalPages: number }>({
      pageNo: 1,
      pageSize: 9999,
      totalPages: 1,
    });
    const searchWord = useRef('');

    // 页面初始数据
    const initialPageList = () => {
      setLoading(true);
      queryMyCustomerList({
        isDistributor,
        disId: distributorId,
        pageNo: 1,
        pageSize: 1000,
        isTenant: 1,
        tenantId,
      })
        .then((res) => {
          setCustomerList(res.recordList);
        })
        .finally(() => {
          setLoading(false);
        });
    };

    useImperativeHandle(ref, () => ({
      initialPageList,
    }));

    // 获取我的客户列表
    const getMyCustomerList = () => {
      const { pageNo, pageSize } = customerParams.current;
      setLoad(true);
      queryMyCustomerList({
        isDistributor,
        disId: distributorId,
        keyWord: searchName,
        pageNo,
        pageSize,
        isTenant: 1,
        tenantId,
      }).then((res) => {
        setLoad(false);
        customerParams.current.totalPages = res.total / pageSize;
        setIsHasMore(pageNo < res.total / pageSize);
        if (pageNo === 1) {
          setCustomerList(res?.recordList || []);
        } else {
          setCustomerList((val) => val.concat(res.recordList));
        }
      });
    };

    // 搜索我的客户
    const onSearch = debounce((word: string, pageSize = 100) => {
      const par = {
        isDistributor,
        disId: distributorId,
        keyWord: word,
        pageNo: 1,
        pageSize,
        isTenant: 1,
        tenantId,
      };
      setSearchName(word);
      setLoad(true);
      queryMyCustomerList(par).then((res) => {
        setLoad(false);
        setCustomerList(res.recordList);
      });
    }, 500);

    // 下滑加载
    const onLoadMoreData = () => {
      customerParams.current.pageNo += 1;
      if (customerParams.current.pageNo > customerParams.current.totalPages) return;
      getMyCustomerList();
    };

    useEffect(() => {
      if (visible) {
        initialPageList();
      } else {
        setIsBatch(false);
        setCheckedIds([]);
        setCheckedCustomerCompanyIds([]);
        // setCheckAll(false);
        // setIndeterminate(false);
        customerParams.current = {
          pageNo: 1,
          pageSize: 100,
          totalPages: 1,
        };
        setCustomerList([]);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible]);

    return (
      <>
        <Spin spinning={loading}>
          <div className={styles.drawerHead}>
            <Search
              realTimeSearch
              placeholder="搜索客户名称"
              className={styles.searchInput}
              onSearch={(word) => {
                onSearch(word);
                searchWord.current = word;
              }}
            />
          </div>
          <div className={styles.addCustomerScroll} id="addCustomerScroll">
            {customerList.length ? (
              <InfiniteScroll
                dataLength={customerList.length}
                className={styles.drawerContentRightScroll}
                hasMore={isHasMore}
                loader={
                  load ? (
                    <div className="text-center">
                      <Spin tip="加载中..." />
                    </div>
                  ) : null
                }
                endMessage={
                  <div className={styles.divider}>
                    <Divider plain>
                      <span className={styles.endMessage}>加载到底了</span>
                    </Divider>
                  </div>
                }
                next={onLoadMoreData}
                scrollableTarget="addCustomerScroll"
              >
                <div className={styles.card}>
                  {customerList.map((item) => (
                    <div className={styles.item} key={item.customerCompanyId}>
                      {isBatch ? (
                        <Checkbox
                          checked={checkedCustomerCompanyIds.includes(item.customerCompanyId)}
                          onChange={(val) => {
                            const value = val.target.checked;
                            const index = checkedCustomerCompanyIds.indexOf(item.customerCompanyId);
                            if (value && index === -1) {
                              checkedCustomerCompanyIds.push(item.customerCompanyId);
                              checkedIds.push(item.id);
                            } else if (!value && index !== -1) {
                              checkedCustomerCompanyIds.splice(index, 1);
                              checkedIds.splice(index, 1);
                            }
                            setCheckedCustomerCompanyIds([...checkedCustomerCompanyIds]);
                            setCheckedIds([...checkedIds]);
                            // setCheckAll(customerList.length === checkedCustomerCompanyIds.length);
                            // setIndeterminate(
                            //   Boolean(
                            //     checkedCustomerCompanyIds.length &&
                            //       customerList.length !== checkedCustomerCompanyIds.length
                            //   )
                            // );
                          }}
                        />
                      ) : (
                        ''
                      )}

                      {/* <img
                      src={
                        item.logoUrl ||
                        'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
                      }
                      className={styles.itemImage}
                      alt=""
                    /> */}
                      <CustomerAvatar
                        item={{
                          companyNature: item.companyNature,
                          logoUrl: item.companyInfo.logoUrl,
                          customerCompanyName: item.customerCompanyName,
                        }}
                      />
                      <div className={styles.itemInfo}>
                        <div className={styles.itemInfoName} title={item.customerCompanyName}>
                          {item.customerCompanyName}
                        </div>
                        <div className={styles.itemInfoNickName} title={item.customerName}>
                          {item.customerName}
                        </div>
                        <div className={styles.itemInfoRemark} title={item.remark}>
                          {item.remark}
                        </div>
                        <div className={styles.itemInfoTime}>
                          加入时间：{dayjs(item.bindTime).format('YYYY-MM-DD')}
                        </div>
                      </div>
                      <div className={styles.itemRight}>
                        {isBatch ? (
                          ''
                        ) : (
                          <>
                            <div
                              className={styles.itemRightExamine}
                              role="presentation"
                              onClick={() => {
                                // if (testPerm('AZ_001_001_004_001_004_001')) {
                                // setShowDetailDrawer(true);
                                // setCurrentItem(item);
                                // }

                                onJumpDetail(item);
                              }}
                            >
                              查看
                            </div>
                            {/* <div className={styles.line} />
                            <div
                              className={styles.itemRightRemove}
                              role="presentation"
                              onClick={() => {
                                if (testPerm('AZ_001_001_004_001_004_003')) {
                                  onCusRemove([item.id]);
                                }
                              }}
                            >
                              删除
                            </div> */}
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </InfiniteScroll>
            ) : (
              <Empty />
            )}
          </div>
        </Spin>
        {/* <CustomerDetailDrawer
        visible={showDetailDrawer}
        // disId={distributorId}
        info={currentItem}
        onClose={() => {
          setShowDetailDrawer(false);
        }}
        onRefresh={(val) => {
          onRefresh();
          setCurrentItem({ ...val });
          onSearch(searchWord.current, customerList.length);
        }}
      /> */}
      </>
    );
  }
);

CustomerListPop.displayName = 'CustomerListPop';

CustomerListPop.defaultProps = {};

export default CustomerListPop;
