.card {
  padding: 12px 0;
  background: white;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
}

.drawerHead {
  margin-top: 10px;
  margin-bottom: 10px;
}

.addCustomerScroll {
  height: calc(100vh - 52px);
}

.drawerContentRightScroll {
  height: calc(100vh - 52px) !important;
}

.item {
  display: flex;
  width: 100%;
  min-height: 60px;
  padding: 0 20px;
  align-items: center;
  cursor: pointer;

  &Image {
    width: 44px;
    height: 44px;
    margin-right: 8px;
    margin-left: 10px;
    border-radius: 50%;
    object-fit: cover;
  }

  &Info {
    flex: 1;
    padding: 4px;

    &Name {
      color: #040919;
      font-size: 16px;
      width: 153px;
      word-break: break-all;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    &NickName {
      color: #040919;
      font-size: 14px;
      width: 153px;
      margin: 3px 0;
      word-break: break-all;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    &Remark {
      color: #888b98;
      font-size: 12px;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
    }

    &Time {
      color: #888b98;
      font-size: 12px;
    }
  }

  &Right {
    font-size: 12px;
    display: flex;
    width: 80px;
    justify-content: center;
    align-items: center;

    .itemRightExamine {
      color: #008cff;
      font-size: 12px;
    }

    .line {
      width: 2px;
      height: 16px;
      margin: 0 8px;
      background: #f3f3f3;
    }

    .itemRightRemove {
      color: #ea1c26;
      font-size: 12px;
    }
  }
}

.drawerFooter {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 16px;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 45%) 12%,
    rgb(255 255 255 / 72%) 25%,
    rgb(255 255 255 / 90%) 36%
  );

  &Text {
    color: #999eb2;
    font-size: 14px;
    display: flex;
    justify-content: flex-end;
    flex: 1;
  }

  &Btn {
    color: #ea1c26;
    font-size: 15px;
    margin-left: 12px;
    cursor: pointer;
  }
}

.extraBatchBtn {
  cursor: pointer;
  color: #008cff;
}
