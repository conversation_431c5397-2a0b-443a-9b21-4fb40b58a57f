/*
 * @Author: chenyuhao <EMAIL>
 * @Date: 2022-07-20 17:05:46
 * @LastEditors: chenyuhao <EMAIL>
 * @LastEditTime: 2022-08-26 11:33:47
 * @FilePath: \website\src\psi\container\data-report.tsx
 */
import { useMemo, useState, useCallback, useEffect, useRef } from 'react';
import {
  // Radio,
  Spin,
  Image,
} from 'antd';
// import type { RadioChangeEvent } from 'antd';
import { Echarts, EchartsProps, SecondaryTab } from '@/components';
import { getOrderPsiReportData } from '@/apis';
import type { GetOrderPsiReportDataRes } from '@/apis';
import { formatPrice } from '@/utils/utils';

import { useEcharts } from '@/hooks';
import styles from './sale-chart-card.module.less';

interface SecondaryTabOptionItem {
  label: string;
  value: string | number;
  url?: string;
  active?: boolean;
}

function SaleChartCard() {
  const echartsRef = useEcharts();
  const [showLoading, setShowLoading] = useState(false);
  const tabOption = useMemo(
    () => [
      { label: '本周', value: 2 },
      { label: '本月', value: 3 },
      { label: '本年', value: 4 },
    ],
    []
  );
  const [activeValue, setActiveValue] = useState<number>(3);

  const onTabChange = (val: SecondaryTabOptionItem) => {
    setActiveValue(+val.value);
  };

  // chart
  interface DataChartState {
    xData: string[];
    valueData: number[];
  }
  const [dataChartState, setDataChartState] = useState({
    xData: [],
    valueData: [],
  } as DataChartState);
  // const [reportData, setReportData] = useState({
  //   sellAmountTotal: '0',
  //   buyAmountTotal: '0',
  //   buyReportVos: [],
  //   sellReportVos: [],
  // } as GetOrderPsiReportDataRes);

  // const [chartType, setChartType] = useState(1);
  const chartTypeRef = useRef(1);
  const [isNoData, setIsNoData] = useState(false);

  const createDataChartState = useCallback((value: GetOrderPsiReportDataRes) => {
    const reportVosData = chartTypeRef.current === 1 ? value.sellReportVos : value.buyReportVos;
    const xData = reportVosData.map((item) => item.date);
    const valueData = reportVosData.map((item) => +item.amount);
    setDataChartState({
      xData,
      valueData,
    });
  }, []);

  const echartsOption: EchartsProps['option'] = useCallback(
    (echarts) => ({
      // color: ['#80FFA5'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: '#F90583',
          },
          shadowStyle: {
            color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
              {
                offset: 0,
                color: 'rgba(37, 112, 255, 0.5)',
              },
              {
                offset: 1,
                color: 'rgba(44, 122, 255, 0)',
              },
            ]),
            // color: 'red',
          },
        },
        backgroundColor: 'rgba(0, 140, 255, 1)',
        borderWidth: 0,
        borderRadius: 9,
        textStyle: {
          color: '#fff',
          fontSize: 16,
        },
        formatter(params: { value: number }[]) {
          return `${formatPrice(params[0].value)}`;
        },
      },
      grid: {
        left: '18px',
        right: '25px',
        bottom: '3%',
        top: '30px',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: dataChartState.xData,
        },
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
            },
          },
        },
      ],
      series: [
        {
          name: 'Line 1',
          type: 'line',
          stack: 'Total',
          smooth: true,
          // symbol: 'image://https://img.huahuabiz.com/user_files/2022721/1658382551507866.svg',
          symbol:
            'image://https://img.huahuabiz.com/user_files/1684141669919600788/circle-icon.svg',
          symbolSize: 6,
          lineStyle: {
            width: 3,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#4DAFFF',
              },
              // {
              //   offset: 0.5,
              //   color: '#713EF0',
              // },
              // {
              //   offset: 0.75,
              //   color: '#EC0A8D',
              // },
              {
                offset: 1,
                color: '#5CE6DB',
              },
            ]),
            // shadowColor: 'rgba(32,0,159,0.3)',
            // shadowColor: 'rgba(140,0,72,0.1)',
            // shadowOffsetY: 12,
          },
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#4DAFFF',
              },
              {
                offset: 0.99,
                color: '#5CE6DB',
              },
            ]),
            borderWidth: 6,
          },
          showSymbol: false,
          areaStyle: {
            opacity: 0.1,
            color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              {
                offset: 0,
                color: '#008CFF',
              },
              {
                offset: 0.99,
                color: '#19DBCD',
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
          data: dataChartState.valueData,
        },
      ],
    }),
    [dataChartState]
  );

  useEffect(() => {
    setShowLoading(true);
    getOrderPsiReportData({ type: activeValue })
      .then((res) => {
        const newIsNoData = !(res.buyAmountTotal && res.sellAmountTotal);
        setIsNoData(newIsNoData);
        // setReportData(res);
        createDataChartState(res);
      })
      .catch(() => {
        setIsNoData(true);
      })
      .finally(() => {
        setShowLoading(false);
      });
  }, [activeValue, createDataChartState]);

  // const onRadioChange = (e: RadioChangeEvent) => {
  //   setChartType(e.target.value);
  //   chartTypeRef.current = e.target.value;
  //   createDataChartState(reportData);
  // };

  return (
    <Spin spinning={showLoading}>
      <div className={styles.dataReportChart}>
        <div className={styles.card}>
          <div className={styles.chartTitle}>
            <div className={styles.chartTitleText}>销售额概况</div>
            <div className={styles.chartTitleFilter}>
              <SecondaryTab
                options={tabOption}
                value={activeValue}
                size="small"
                onClickItem={onTabChange}
              />
            </div>
          </div>
          {!isNoData ? (
            <div style={{ marginTop: 15 }}>
              {/* <div className={styles.chartType}>
                <Radio.Group onChange={onRadioChange} value={chartType}>
                  <Radio value={1}>销售金额￥{reportData.sellAmountTotal}</Radio>
                  <Radio value={2}>采购金额￥{reportData.buyAmountTotal}</Radio>
                </Radio.Group>
              </div> */}
              <Echarts
                ref={echartsRef}
                id="psiDataChart"
                option={echartsOption}
                style={{ height: '220px', width: '100%' }}
              />
            </div>
          ) : (
            <div className={styles.noData}>
              <div className={styles.noDataImage}>
                <Image
                  src="https://img.huahuabiz.com/user_files/1659422990033129702/content.7c084090.png"
                  preview={false}
                />
              </div>
              <div className={styles.noDataText}>暂无报表信息</div>
            </div>
          )}
        </div>
        {/* <Card height={315} style={{ marginBottom: 0 }}></Card> */}
      </div>
    </Spin>
  );
}

export default SaleChartCard;
