import { useState, useEffect, useRef } from 'react';
import { Icon } from '@/components';
import dayjs from 'dayjs';
import { queryMyCustomerList } from '@/apis';
import { RecordListType } from '@/apis/ims/query-my-customer-list';
import CustomerAvatar from '@/src/distribution/components/customer-avatar';
import styles from './index.module.less';
import Card from '../../components/card/card';
import CustomerListDrawer from '../customer-list-drawer';
import CustomerDetailDrawer from '../customer-detail-drawer';

interface CustomerCardProps {
  distributorId: number;
  onJumpList: MultipleParamsFn<[]>;
  onJumpDetail: MultipleParamsFn<[val: RecordListType]>;
  tenantId: string;
  isDistributor: number;
  actionType?: string;
}

function CustomerCard({
  distributorId,
  onJumpList,
  onJumpDetail,
  tenantId,
  isDistributor,
  actionType,
}: CustomerCardProps) {
  const [showListDrawer, setShowListDrawer] = useState(false);
  const [customerList, setCustomerList] = useState([] as RecordListType[]);
  const [totalNum, setTotalNum] = useState(0);
  const [showDetailDrawer, setShowDetailDrawer] = useState(false);
  const [currentItem, setCurrentItem] = useState<RecordListType>({
    customerCompanyId: 0,
    customerName: '',
    logoUrl: '',
    companyNature: 0,
    joinTime: 0,
    bindTime: 0,
    id: 0,
    customerCompanyName: '',
    remark: '',
    companyInfo: {
      companyAddr: '',
      companyName: '',
      companyNature: 1,
      id: 0,
      logoUrl: '',
    },
  });
  const refDrawer = useRef<any>();

  const getCustomerList = () => {
    if (!distributorId) {
      return;
    }
    queryMyCustomerList({
      isDistributor,
      disId: distributorId,
      pageNo: 1,
      pageSize: 3,
      isTenant: 1,
      tenantId,
    }).then((res) => {
      setCustomerList(res.recordList);
    });
  };

  useEffect(() => {
    if (distributorId) {
      queryMyCustomerList({
        isDistributor,
        disId: distributorId,
        pageNo: 1,
        pageSize: 3,
        isTenant: 1,
        tenantId,
      }).then((res) => {
        setCustomerList(res.recordList);
        setTotalNum(res.total || 0);
      });
    }
  }, [distributorId, tenantId, isDistributor]);

  return (
    <>
      <Card
        title=""
        titleNode={<div>我的客户</div>}
        className={styles.cusCard}
        opt={
          <div>
            {customerList.length ? (
              <div
                className={styles.opt}
                role="presentation"
                onClick={() => {
                  if (actionType === 'url') {
                    onJumpList();
                  } else {
                    setShowListDrawer(true);
                  }
                }}
              >
                全部{`（${totalNum}）`} <Icon name="right" />
              </div>
            ) : null}
          </div>
        }
        isShowIcon={false}
      >
        {customerList.map((item) => (
          <div
            className={styles.item}
            key={item.customerCompanyId}
            role="button"
            tabIndex={-1}
            onClick={() => {
              setCurrentItem(item);
              if (actionType === 'url') {
                onJumpDetail(item);
              } else {
                setShowDetailDrawer(true);
              }
            }}
          >
            <div>
              <CustomerAvatar
                item={{
                  companyNature: item.companyNature,
                  logoUrl: item.companyInfo.logoUrl,
                  customerCompanyName: item.customerCompanyName,
                }}
              />
            </div>
            <div className={styles.itemInfo}>
              <div className={styles.itemInfoName} title={item.customerCompanyName}>
                {item.customerCompanyName}
              </div>
              <div className={styles.itemInfoNickName} title={item.customerName}>
                {item.customerName}
              </div>
              <div className={styles.itemInfoTime}>
                加入时间{dayjs(item.bindTime).format('YYYY-MM-DD')}
              </div>
            </div>
          </div>
        ))}
        {!customerList.length ? <div className={styles.noData}>暂无客户</div> : ''}
      </Card>
      <CustomerListDrawer
        ref={refDrawer}
        visible={showListDrawer}
        distributorId={distributorId}
        tenantId={tenantId}
        onRefresh={() => {
          getCustomerList();
        }}
        onClose={() => {
          setShowListDrawer(false);
        }}
      />
      <CustomerDetailDrawer
        visible={showDetailDrawer}
        info={currentItem}
        onClose={() => {
          setShowDetailDrawer(false);
        }}
        onRefresh={() => {
          getCustomerList();
        }}
      />
    </>
  );
}

CustomerCard.defaultProps = {
  actionType: 'url',
};

export default CustomerCard;
