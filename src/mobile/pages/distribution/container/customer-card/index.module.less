.opt {
  color: #888b98;
  cursor: pointer;
}

.item {
  display: flex;
  width: 100%;
  min-height: 60px;
  align-items: center;
  cursor: pointer;

  &Image {
    width: 44px;
    height: 44px;
    margin-right: 8px;
    border-radius: 50%;
    object-fit: cover;
  }

  &Info {
    flex: 1;
    padding: 4px;

    &Name {
      color: #040919;
      font-size: 16px;
      width: 237px;
      word-break: break-all;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    &NickName {
      color: #040919;
      font-size: 14px;
      width: 237px;
      margin: 3px 0;
      word-break: break-all;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    &Time {
      color: #888b98;
      font-size: 12px;
      margin-bottom: 3px;
    }
  }

  &Right {
    font-size: 12px;
    display: flex;
    width: 70px;
    justify-content: center;
    align-items: center;
  }
}

.addBtn {
  color: #008cff;
  border-top: 1px solid #f3f3f3;
  font-size: 14px;
  display: flex;
  width: 100%;
  height: 50px;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}

.noData {
  color: #888b98;
  display: flex;
  justify-content: center;
  align-items: center;
}
