const ua = typeof window === 'object' ? window.navigator.userAgent : '';

let isIOSFlag = -1;
let isAndroidFlag = -1;

export function isIOS() {
  if (isIOSFlag === -1) {
    isIOSFlag = /iPhone|iPod|iPad/i.test(ua) ? 1 : 0;
  }
  return isIOSFlag === 1;
}

export function isAndroid() {
  if (isAndroidFlag === -1) {
    isAndroidFlag = /Android/i.test(ua) ? 1 : 0;
  }
  return isAndroidFlag === 1;
}

export function keyboardFn(upspringFn: MultipleParamsFn<[]>, packUpFn: MultipleParamsFn<[]>) {
  if (isAndroid()) {
    const { innerHeight } = window;
    window.addEventListener('resize', () => {
      const newInnerHeight = window.innerHeight;
      if (innerHeight > newInnerHeight) {
        // 键盘弹出事件处理
        window.console.log('android 键盘弹窗事件');
        upspringFn();
        // alert('android 键盘弹窗事件');
      } else {
        packUpFn();
        // 键盘收起事件处理
        window.console.log('android 键盘收起事件处理');
        // alert('android 键盘收起事件处理');
      }
    });
  } else if (isIOS()) {
    window.addEventListener('focusin', () => {
      // 键盘弹出事件处理
      window.console.log('iphone 键盘弹出事件处理');

      upspringFn();
    });
    window.addEventListener('focusout', () => {
      // 键盘收起事件处理
      window.console.log('iphone 键盘收起事件处理');
      packUpFn();
    });
  }
}
