import dayjs from 'dayjs';

export const CompanyDefaultLogo =
  'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png';

export function getQueryVariable(url: string, variable: string) {
  if (url) {
    const str = url.split('?');
    const query = str[1];
    const vars = query.split('&');
    for (let i = 0; i < vars.length; i += 1) {
      const pair = vars[i].split('=');
      if (pair[0] === variable) {
        return pair[1];
      }
    }
  }

  return '';
}

export function getCustomizeTime(key: string) {
  if (key === 'week') {
    // 本周
    return {
      startDate: dayjs().startOf('week').format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs().endOf('week').format('YYYY-MM-DD HH:mm:ss'),
    };
  }
  if (key === 'month') {
    // 本月
    return {
      startDate: dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
    };
  }
  if (key === 'lastMonth') {
    // 上月
    return {
      startDate: dayjs().add(-1, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs().add(-1, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss'),
    };
  }
  if (key === 'year') {
    // 本年
    return {
      startDate: dayjs().startOf('year').format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs().endOf('year').format('YYYY-MM-DD HH:mm:ss'),
    };
  }
  if (key === 'lastYear') {
    // 去年
    return {
      startDate: dayjs().add(-1, 'year').startOf('year').format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs().add(-1, 'year').endOf('year').format('YYYY-MM-DD HH:mm:ss'),
    };
  }
  if (key === '12' || key === '11' || key === '10') {
    return {
      startDate: dayjs()
        .add(-1, 'year')
        .month(Number(key) - 1)
        .startOf('month')
        .format('YYYY-MM-DD HH:mm:ss'),
      endDate: dayjs()
        .add(-1, 'year')
        .month(Number(key) - 1)
        .endOf('month')
        .format('YYYY-MM-DD HH:mm:ss'),
    };
  }
  return {
    startDate: dayjs()
      .month(Number(key) - 1)
      .startOf('month')
      .format('YYYY-MM-DD HH:mm:ss'),
    endDate: dayjs()
      .month(Number(key) - 1)
      .endOf('month')
      .format('YYYY-MM-DD HH:mm:ss'),
  };
}
