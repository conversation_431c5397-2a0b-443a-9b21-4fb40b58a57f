import { useState, useRef, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { TabBar } from 'antd-mobile';
import { message, Drawer } from 'antd';
import { useMount } from 'ahooks';
import classNames from 'classnames';
import { useDistributionBaseInfo } from '@@/distribution/hooks/index';
import { Modal } from '@/components';
import { getToken } from '@/utils/auth';
import {
  getAdmList,
  getImsDisCheckUserInfo,
  getIsShowInviteCustom,
  getShowRecruit,
  joinCompanies,
  postSendDisAdmin,
  getEnterNeedPayFee,
  getJoinOrderStatus,
  getJoinPayInfo,
  getDistributionJoinStatus,
  getImsDisRenewProduct,
} from '@/apis';
import type { admListResult, JoinCompanyResult, RecruitInfoType } from '@/apis';
// import { judgeClient } from '../../utils/utils';
import Head from '../../components/head/head';

import useMobileHeader from '../../hooks/use-mobile-header';
import styles from './apply.module.less';

declare let wx: any;
function DistributionApply() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams({ headHeight: '44' });
  const [isWx, setIsWx] = useState(false);
  const headHeight = Number(searchParams.get('headHeight')) || 44;
  const phoneNumber = searchParams.get('phoneNumber') || '';
  const businessName = searchParams.get('businessName') || '';

  // const type = searchParams.get('type');
  const isApp = searchParams.get('isApp') || false;
  const tenantId = searchParams.get('tenantId') || '';
  // const switchCompanyId = searchParams.get('switchCompanyId') || '';
  const appId = searchParams.get('appId') || '';
  const disCompanyId = searchParams.get('disCompanyId') || '';
  const [isMount, setMount] = useState(false);
  const [isShowInviteCustom, setIsShowInviteCustom] = useState(1);

  const [nextPageType, setNextPageType] = useState(0); // 1前往订单确认页面 2前往支付页面
  const [payStatus, setPayStatus] = useState(-1); // 1前往订单确认页面 2前往支付页面
  const [supplierDistributorId, setSupplierDistributorId] = useState(0);
  const [isNeedPay, setIsNeedPay] = useState('');
  const [companyNature, setCompanyNature] = useState('');
  const [companyName, setCompanyName] = useState('');
  const [userName, setUserName] = useState('');

  const { run } = useMobileHeader();

  // const isSwitchCompany = useRef(false);

  const checkUserInfo = useRef<{
    isPersIdentity: boolean;
    isDis: boolean;
    isIdInCurrentTenant: boolean;
    persIdentityCompanyId: number;
  }>({
    isPersIdentity: false,
    isDis: false,
    isIdInCurrentTenant: false,
    persIdentityCompanyId: 0,
  });

  const [btnDisabled, setBtnDisabled] = useState(false);
  const [recruitInfo, setRecruitInfo] = useState({} as RecruitInfoType);
  const [isSelectTeam, setIsSelectTeam] = useState(false);
  const [companyList, setCompanyList] = useState([] as JoinCompanyResult[]);
  const [isSelectAdmin, setIsSelectAdmin] = useState(false);
  const [admList, setAdmList] = useState([] as admListResult[]);

  const { data: baseConfigInfo, run: getDisBaseConfigInfo } = useDistributionBaseInfo();
  const defineDisNameValue = useMemo(
    () => baseConfigInfo?.defineDisName || '分销商',
    [baseConfigInfo?.defineDisName]
  );

  const btnText = useMemo(() => {
    if (recruitInfo.status === 1) return `您已经是${defineDisNameValue}`;
    if (payStatus === 1 && recruitInfo.status === 0) {
      return '已提交审核中';
    }
    if (nextPageType) return '去缴费';
    if (recruitInfo.status === 0) return '已提交审核中';
    if (recruitInfo.status === -1 || recruitInfo.status === 2 || recruitInfo.status === 3) {
      return `申请成为${defineDisNameValue}`;
    }
    return '';
  }, [nextPageType, recruitInfo.status, payStatus, defineDisNameValue]);

  const getCheckUserInfo = async () => {
    setBtnDisabled(true);
    getImsDisCheckUserInfo()
      .then((res) => {
        window.console.log(res, '获取分销商校验参数,判断当前身份');
        checkUserInfo.current = res;
      })
      .finally(() => {
        setBtnDisabled(false);
      });

    // 获取加入的公司团队列表
    joinCompanies().then((res) => {
      window.console.log('joinCompanies', res);
      setCompanyList(res.list.filter((item) => item.companyId === Number(disCompanyId)));
    });

    // 获取分销商状态
    const res0 = await getShowRecruit();
    window.console.log(res0, '申请页获取分销商状态,用于下面申请按钮和tab展示问题的');
    setRecruitInfo(res0);
    const res1 = await getEnterNeedPayFee({ tenantId });
    window.console.log(res1, '是否需要支付');
    if (res0.status === 2) {
      Modal.confirm({
        title: '驳回原因',
        content: `${res0.rejectReason}`,
        okText: '知道了',
        cancelText: '重新申请',
        centered: true,
        onCancel: () => {
          if (res0.isOpenMiniProgRecruitEnt) {
            navigate(
              `/m/submit-merchant?type=51&id=1&isApplet=1&isDis=true&isSite=true&tenantId=${tenantId}&formCode=${
                Number(searchParams.get('companyNature')) === 1
                  ? 'FORMoPrx5ES1667986342753'
                  : 'FORM06Jc4c81717643769819'
              }&applyCompanyId=${res0.applyCompanyId}&isNeedPay=${
                res1.flag ? '1' : ''
              }&companyNature=${searchParams.get('companyNature') || ''}&companyName=${
                searchParams.get('companyName') || ''
              }&userName=${searchParams.get('userName') || ''}&businessName=${
                businessName || searchParams.get('companyName')
              }&phoneNumber=${phoneNumber}&appId=${appId}`,
              {
                replace: true,
              }
            );
          } else {
            message.warning('该商家已关闭在线招募分销功能');
          }
        },
        onOk: () => {},
      });
    }

    if (res0.status !== 1 && res1.flag) {
      setIsNeedPay('1');
      const res2 = await getJoinOrderStatus({ tenantId });
      window.console.log(res2, '订单步骤');
      setPayStatus(res2.payStatus ?? -1);
      setSupplierDistributorId(res2.supplierDistributorId || 0);
      if (res2.genOrderFlag && res2.payStatus === 0 && res2.supplierDistributorId) {
        // 如果订单已生成，未支付，并且有分销商id，跳到支付台
        setNextPageType(2);
      } else if (res2.genDisFlag && (!res2.genOrderFlag || res2.payStatus === 2)) {
        // 如果分销商入驻信息已存在，未生成订单或者订单已取消,跳转到开通页面
        setNextPageType(1);
      }
    }

    // 获取是否展示邀请成为客户
    getIsShowInviteCustom().then((res) => {
      window.console.log(res, 'apply是否展示邀请成为客户');
      setIsShowInviteCustom(res.ifInviteCustom);
    });
  };

  // 个人直接跳转申请成为分销商,不是弹出选择团队
  const onApply = () => {
    if (
      btnDisabled ||
      !companyList.length ||
      recruitInfo.status === 1 ||
      (recruitInfo.status === 0 && !nextPageType)
    )
      return;
    if (nextPageType === 1) {
      getDistributionJoinStatus({
        supplierDistributorId,
        tenantId,
      }).then(() => {
        getImsDisRenewProduct({ tenantId, disCompanyId: Number(disCompanyId) }).then(() => {
          navigate(
            `/m/m-distribution/join-order?tenantId=${tenantId}&supplierDistributorId=${supplierDistributorId}&companyNature=${companyNature}&companyName=${encodeURIComponent(
              companyName
            )}&userName=${encodeURIComponent(
              userName
            )}&disCompanyId=${disCompanyId}&appId=${appId}`,
            {
              replace: true,
            }
          );
        });
      });
      return;
    }
    if (nextPageType === 2) {
      getJoinPayInfo({
        supplierDistributorId,
        tenantId,
      }).then((res) => {
        window.wx.miniProgram.navigateTo({
          url: `/subpackages/order/pages/pay/index?payNo=${res.payNo}&operateType=DIS_RENEW&supplierDistributorId=${supplierDistributorId}&backApi=reLaunch&backUrl=/pages/my/index`,
        });
      });
      return;
    }
    if (companyList.length) {
      setIsSelectTeam(true);
    }
  };

  // 邀请好友
  const toFriend = () => {
    if (checkUserInfo.current.isPersIdentity) {
      const { isDis, isIdInCurrentTenant } = checkUserInfo.current;
      navigate(
        `/m/m-sun-code/index?jumpTo=apply&appId=${appId}&tenantId=${tenantId}&disCompanyId=${disCompanyId}&showFaceRecruit=${Boolean(
          isDis || isIdInCurrentTenant
        )}&isShowInviteCustom=${isShowInviteCustom}&companyNature=${companyNature}&companyName=${encodeURIComponent(
          companyName
        )}&userName=${encodeURIComponent(userName)}`,
        {
          replace: true,
        }
      );
    } else {
      getImsDisCheckUserInfo()
        .then((res) => {
          checkUserInfo.current = res;
          if (res.isDis) {
            navigate(
              `/m/m-distribution/index?tenantId=${tenantId}&appId=${appId}&disCompanyId=${disCompanyId}`,
              {
                replace: true,
              }
            );
          } else {
            setTimeout(() => {
              navigate(
                `/m/m-sun-code/index?jumpTo=apply&appId=${appId}&tenantId=${tenantId}&disCompanyId=${disCompanyId}&isShowInviteCustom=${isShowInviteCustom}&companyNature=${companyNature}&companyName=${encodeURIComponent(
                  companyName
                )}&userName=${encodeURIComponent(userName)}`,
                {
                  replace: true,
                }
              );
            }, 500);
          }
        })
        .finally(() => {
          setBtnDisabled(false);
        });
    }
  };

  // 选择团队并判断是否为管理员，是的话直接添写入住资料
  const onSelectTeam = (companyItem: JoinCompanyResult) => {
    setIsSelectTeam(false);
    if (companyItem.isAdmin) {
      navigate(
        `/m/submit-merchant?type=51&id=1&isApplet=1&isDis=true&isSite=true&tenantId=${tenantId}&formCode=${
          companyItem.companyNature === 1 ? 'FORMoPrx5ES1667986342753' : 'FORM06Jc4c81717643769819'
        }&applyCompanyId=${companyItem.companyId}&isNeedPay=${isNeedPay}&companyNature=${
          companyItem.companyNature
        }&companyName=${encodeURIComponent(companyItem.companyName)}&userName=&businessName=${
          companyItem.companyName
        }&phoneNumber=${phoneNumber}&appId=${appId}`,
        {
          replace: true,
        }
      );
    } else {
      getAdmList({ companyId: companyItem.companyId }).then((res) => {
        setAdmList(res.list);
        setIsSelectAdmin(true);
      });
    }
  };

  // 返回
  const back = () => {
    window.history.back();
  };

  useMount(() => {
    setCompanyNature(searchParams.get('companyNature') || '');
    setCompanyName(searchParams.get('companyName') || '');
    setUserName(searchParams.get('userName') || '');

    document.title = ' ';
    if (isApp) {
      setIsWx(true);
    } else {
      setTimeout(() => {
        wx.miniProgram.getEnv((res: { miniprogram: boolean }) => {
          setIsWx(res.miniprogram);
        });
      });
    }

    const token = searchParams.get('token') || getToken();
    if (token) {
      run();
    }

    getDisBaseConfigInfo(tenantId);

    getCheckUserInfo();

    setTimeout(() => {
      // 解决ios滚动不了的问题
      setMount(true);
    }, 300);
  });

  const HelmetElement = (
    <Helmet>
      <title>{defineDisNameValue}</title>
    </Helmet>
  );

  const dom = () => (
    <div
      className={styles.container}
      style={{
        paddingTop: isWx ? '8px' : `${headHeight + 8}px`,
      }}
    >
      {!isWx && <Head title={defineDisNameValue} back={back} />}

      {recruitInfo.distAttachmentsInfoList?.length > 0 ? (
        <div>
          {recruitInfo.distAttachmentsInfoList?.map((item) => (
            <div key={item.uid}>
              <img src={item.attachOssAddr} alt="" className={styles.posterImg} />
            </div>
          ))}
        </div>
      ) : (
        <img
          src="https://img.huahuabiz.com/user_files/1685602430870915326/dis-poster.png"
          alt=""
          className={styles.posterImg}
        />
      )}

      <div
        style={{
          height: `${
            (recruitInfo.isOpenMiniProgRecruitEnt ? 70 : 0) + (recruitInfo.isRecruitSub ? 50 : 0)
          }px`,
        }}
      />
      <div style={{ position: 'fixed', bottom: '0' }}>
        {Boolean(recruitInfo?.isOpenMiniProgRecruitEnt) && (
          <div className={styles.applyBar}>
            <div
              className={classNames(
                btnDisabled || (!recruitInfo.status && !nextPageType) || recruitInfo.status === 1
                  ? styles.applyBarBtnDisable
                  : styles.applyBarBtn
              )}
              role="presentation"
              onClick={() => {
                onApply();
              }}
            >
              {btnText}
            </div>
          </div>
        )}
        {recruitInfo.isRecruitSub && (
          <div className={styles.bottomBar}>
            <TabBar
              activeKey="myReport"
              safeArea
              onChange={(val) => {
                if (val === 'friend' && !btnDisabled) {
                  toFriend();
                }
              }}
            >
              <TabBar.Item
                key="myReport"
                icon={
                  <img
                    src="https://img.huahuabiz.com/user_files/1685956573663262123/apply-i-ac.svg"
                    alt=""
                  />
                }
                title="申请"
              />
              <TabBar.Item
                key="friend"
                icon={
                  <img
                    src="https://img.huahuabiz.com/user_files/1685956611515754026/invite-i-def.svg"
                    alt=""
                  />
                }
                title="邀请好友"
              />
            </TabBar>
          </div>
        )}
        {isSelectTeam && (
          <Drawer
            placement="bottom"
            visible={isSelectTeam}
            closable={false}
            onClose={() => setIsSelectTeam(false)}
            className={styles.myDrawer}
          >
            <div className={styles.drawerTitleBox}>
              <div>请选择团队</div>
            </div>
            <div>
              {companyList?.map((item) => (
                <div
                  className={styles.flexRow}
                  key={item.companyId}
                  tabIndex={0}
                  role="button"
                  onClick={() => {
                    if (item.companyNature === 1) {
                      navigate(
                        `/m/submit-merchant?type=51&id=1&isApplet=1&isDis=true&isSite=true&tenantId=${tenantId}&formCode=FORMoPrx5ES1667986342753&applyCompanyId=${
                          item.companyId
                        }&isNeedPay=${isNeedPay}&companyNature=1&companyName=&userName=${encodeURIComponent(
                          item.memberName
                        )}&phoneNumber=${phoneNumber}&appId=${appId}`,
                        {
                          replace: true,
                        }
                      );
                    } else {
                      onSelectTeam(item);
                    }
                  }}
                >
                  <div className={styles.flexBox}>
                    <img src={item.logoUrl} alt="" className={styles.avatarImg} />
                    <div className={styles.companyName}>{item.companyName}</div>
                  </div>
                </div>
              ))}
            </div>
          </Drawer>
        )}
        {isSelectAdmin && (
          <Drawer
            placement="bottom"
            visible={isSelectAdmin}
            closable={false}
            onClose={() => setIsSelectAdmin(false)}
            className={styles.myDrawer}
          >
            <div className={styles.drawerTitleBox}>
              <div>请选择管理员</div>
            </div>
            <div>
              {admList?.map((item) => (
                <div
                  className={styles.admlist}
                  role="button"
                  tabIndex={-1}
                  onClick={() => {
                    Modal.confirm({
                      title: '提示',
                      content: `您确定发送消息给管理员${item.name}`,
                      okText: '确定',
                      cancelText: '取消',
                      centered: true,
                      keyboard: false,
                      maskClosable: false,
                      onCancel: () => {},
                      onOk: () => {
                        postSendDisAdmin({
                          adminUserId: item.userId,
                          appId,
                        }).then(() => {
                          setIsSelectAdmin(false);
                        });
                      },
                    });
                  }}
                  key={item.id}
                >
                  <img src={item.avatar} className={styles.admAvatar} alt="" />
                  <div className={styles.admName}>{item?.name}</div>
                </div>
              ))}
            </div>
          </Drawer>
        )}
      </div>
      {HelmetElement}
    </div>
  );

  return isMount ? dom() : null;
}

export default DistributionApply;
