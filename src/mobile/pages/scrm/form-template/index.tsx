import { useMemoizedFn, useMount } from 'ahooks';
import { useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { Button, Form, message } from 'antd';
import { useSearchParams } from 'react-router-dom';
import { FormRender, FormRenderProps, FormRenderInstance } from '@@/form-engine/containers';
import {
  createCustomer,
  updateCustomer,
  createBusiness,
  updateBusiness,
  getScrmStaging,
  createRecord,
  updateRecord,
} from '@/apis';
import useMiniProgram from '../../../hooks/use-mini-program';
import styles from './index.module.less';

function ScrmWebViewPage() {
  const [form] = Form.useForm();
  const formRenderRef = useRef(null as unknown as FormRenderInstance);
  const [searchParams] = useSearchParams({ key: '', saType: '', appId: '', token: '' });
  const [submitting, setSubmitting] = useState(false);
  const [state, setState] = useState<any>(null);
  const [currentVersionNumber, setCurrentVersionNumber] = useState(0);
  const [currentFormCode, setCurrentFormCode] = useState('');

  const onPrev = useMemoizedFn(() => {
    window.wx.miniProgram.navigateBack({ delta: 1 });
  });

  const onSubmit = useMemoizedFn(() => {
    form.submit();
  });

  const handleFormLoaded = useMemoizedFn(() => {
    if (state.apiParams.extMap) {
      const extMap: Record<string, unknown> = {};
      Object.keys(state.apiParams.extMap).forEach((key) => {
        const value = state.apiParams.extMap[key];
        if (value.charAt(0) === '[') {
          try {
            extMap[key] = JSON.parse(value);
          } catch (error) {
            extMap[key] = value;
          }
        } else {
          extMap[key] = value;
        }
      });
      formRenderRef.current?.setValues(extMap);
    }
  });

  const onFinish: FormRenderProps['onFinish'] = useMemoizedFn((values, versionNumber) => {
    setSubmitting(true);
    const extMap: Record<string, unknown> = {};
    Object.keys(values).forEach((key) => {
      extMap[key] = values[key];
    });
    let promise = Promise.resolve();
    if (state.fromPage === 'createCustomer') {
      promise = createCustomer({
        customerParam: { ...state.apiParams.customerParam, versionNumber },
        coordinatorUserIdList: state.apiParams.coordinatorUserIdList,
        contactParamList: state.apiParams.contactParamList,
        type: state.apiParams.type,
        publicId: state.apiParams.publicId,
        extMap,
        roleMembers: state.apiParams.roleMembers,
      });
    } else if (state.fromPage === 'updateCustomer') {
      promise = updateCustomer({
        customerParam: { ...state.apiParams.customerParam, versionNumber },
        extMap,
        contactParamList: state.apiParams.contactParamList,
      });
    } else if (state.fromPage === 'createBusiness') {
      promise = createBusiness({
        ...state.apiParams,
        versionNumber,
        customExtendList: extMap,
      });
    } else if (state.fromPage === 'updateBusiness') {
      promise = updateBusiness({
        ...state.apiParams,
        versionNumber,
        customExtendList: extMap,
      });
    } else if (state.fromPage === 'createRecord') {
      promise = createRecord({
        ...state.apiParams,
        versionNumber,
        customExtendList: extMap,
      });
    } else if (state.fromPage === 'updateRecord') {
      promise = updateRecord({
        ...state.apiParams,
        versionNumber,
        customExtendList: extMap,
      });
    }
    promise
      .then((res: any) => {
        message.success('操作成功！');
        setTimeout(() => {
          const obj = {
            ...state.apiParams,
            versionNumber,
            customExtendList: extMap,
            id: res?.id,
          };
          window.wx.miniProgram.postMessage({ data: obj });
        }, 1500);
      })
      .finally(() => {
        setSubmitting(false);
      });
  });

  useMiniProgram(searchParams.get('saType') as string);

  useMount(() => {
    const storeKey = searchParams.get('key') as string;
    if (!storeKey) return;
    getScrmStaging({ customerKey: storeKey }).then((res) => {
      const currentState = JSON.parse(res.stagCustomerJson);
      if (
        currentState.fromPage === 'createBusiness' ||
        currentState.fromPage === 'updateBusiness'
      ) {
        setCurrentFormCode(currentState.apiParams.formCode);
        if (currentState.apiParams.versionNumber) {
          setCurrentVersionNumber(currentState.apiParams.versionNumber);
        }
      } else if (
        currentState.fromPage === 'createCustomer' ||
        currentState.fromPage === 'updateCustomer'
      ) {
        setCurrentFormCode(currentState.apiParams.customerParam.formCode);
        if (currentState.apiParams.customerParam.versionNumber) {
          setCurrentVersionNumber(currentState.apiParams.customerParam.versionNumber);
        }
      }
      setState(currentState);
    });
  });
  if (!state) {
    return null;
  }
  return (
    <div className={styles.wrap}>
      <Helmet>
        {state.fromPage === 'createBusiness' && <title>新建商机</title>}
        {state.fromPage === 'updateBusiness' && <title>修改商机</title>}
        {state.fromPage === 'createCustomer' && <title>新建客户</title>}
        {state.fromPage === 'updateCustomer' && <title>修改客户</title>}
        {state.fromPage === 'createRecord' && <title>新建跟进记录</title>}
        {state.fromPage === 'updateRecord' && <title>修改跟进记录</title>}
      </Helmet>
      <FormRender
        ref={formRenderRef}
        form={form}
        isMobile
        formCode={currentFormCode}
        versionNumber={currentVersionNumber || undefined}
        className={styles.form}
        onTempComLoadFinish={handleFormLoaded}
        onFinish={onFinish}
      />
      <div className={styles.footer}>
        <Button onClick={onPrev}>上一步</Button>
        <Button loading={submitting} type="primary" htmlType="submit" onClick={onSubmit}>
          确定
        </Button>
      </div>
    </div>
  );
}

ScrmWebViewPage.displayName = 'ScrmWebViewPage';

ScrmWebViewPage.defaultProps = {};

export default ScrmWebViewPage;
