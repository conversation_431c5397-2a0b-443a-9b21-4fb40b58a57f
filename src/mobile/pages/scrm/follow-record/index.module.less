.page {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: space-between;
  position: relative;
  flex-direction: column;
  font-family: '苹方-简', sans-serif;
}

.messageBox {
  flex: 1;
  width: 100%;
  padding: 8px 16px 0;
  overflow-y: auto;
}

.recordBox {
  padding: 16px;
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 5.88%);

  &:nth-child(n + 2) {
    margin-top: 16px;
  }
}

.headName {
  color: #040919;
  font-size: 17px;
  font-weight: 500;
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  .avatarImg {
    width: 34px;
    height: 34px;
    border-radius: 50%;
    margin-right: 8px;
  }

  .avatarInit {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    line-height: 34px;
    text-align: center;
    background: #008cff;
  }
}

.baseMes {
  color: #888b98;
  font-size: 15px;
  display: flex;
  line-height: 22px;
  align-items: flex-start;
}

.fixedName {
  min-width: fit-content;
}

.message {
  color: #040919;
  white-space: pre-wrap; /* 保留换行 */
  overflow-wrap: break-word; /* 长单词换行 */
  word-break: break-all; /* 任何地方换行 */
}

.custormerName {
  color: #008cff;
}

.bodyName {
  color: #888b98;
  font-size: 17px;
  font-weight: 500;
  line-height: 40px;

  &:first-child {
    color: #040919;
  }
}

// 占位
.placeholder {
  height: 68px;
}

.infoCell {
  color: #888b98;
  font-size: 15px;
  display: flex;
  margin-bottom: 12px;
  flex-direction: column;
  align-items: flex-start;

  &:last-child {
    margin-bottom: 0;
  }

  .labelName {
    line-height: 20px;
  }

  .distance {
    margin-bottom: 8px;
  }

  .labelValue {
    color: #040919;
    line-height: normal;
    margin-top: 8px;
    white-space: pre-wrap;
    word-wrap: break-word;
    word-break: break-all;
  }
}

.fileList {
  margin-top: 12px;
  margin-bottom: 0;
}

.addressBox {
  display: flex;
  align-items: flex-start;
}

.address {
  color: #040919;
  line-height: normal;
  margin-left: 4px;
  padding-top: 4px;
  flex: 1;
  white-space: pre-wrap;
  word-wrap: break-word;
  word-break: break-all;
}

.moreButton {
  display: flex;
  align-items: center;

  .itemButton {
    display: flex;
    align-items: center;
    margin-right: 20px;
  }

  .itemName {
    min-width: fit-content;
    margin-left: 4px;
  }
}

.componentInput {
  :global {
    .icon_value {
      min-width: fit-content;
    }
  }
}
