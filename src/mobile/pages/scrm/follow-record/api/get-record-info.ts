import { AttachmentListType } from '@/apis/crm-new/create-record';
import http from '@/apis/http/crm-new';

export interface GetRecordInfoParamsType {
  id: number;
}

export interface GetRecordInfoResultType {
  id: number;
  companyId: number;
  customerId: number;
  customerName: string;
  customerCategory: number; // 1企业客户，2个人客户
  opportunityId: number;
  opportunityName: string;
  eventType: number; // (0.电联,1.面谈，2.聊天工具)
  eventTypeName: string;
  eventTime: number;
  content: string;
  imageList: AttachmentListType[];
  attachmentList: AttachmentListType[];
  longitude: number;
  latitude: number;
  address: string;
  provinceId: number;
  provinceName: string;
  cityId: number;
  cityName: string;
  districtId: number;
  districtName: string;
  createTime: number;
  updateTime: number;
  createUser: number;
  userName: string;
  avatar: string;
  formCode: string;
  versionNumber: number;
  customExtendMap: Record<string, any>;
  contactList: Array<{ contactId: number; contactName: string }>;
  followList: Array<{ userId: number; userName: string }>;
  commentList: Array<{
    id: number;
    companyId: number;
    eventRecordId: number;
    content: string;
    createUser: number;
    memberName: string;
    createTime: number;
  }>;
}

/**
 * 获取跟进记录详情
 */
function getRecordInfo(params: GetRecordInfoParamsType): Promise<GetRecordInfoResultType> {
  return http('/v2/customer/event/record/detail', { params, method: 'GET' });
}

export default getRecordInfo;
