import { PaginationResponse } from '@echronos/core';
import http from '@/apis/http/user';

interface dataType {
  id: number;
  realName: string;
  name: string;
  nickname: string;
  avatar: string;
}

/**
 * 获取成员
 */
function getMemberList(companyId: number): PaginationResponse<dataType> {
  return http('/v1/user/member/query/allByCompanyId', {
    params: { companyId },
    method: 'GET',
  });
}

export default getMemberList;
