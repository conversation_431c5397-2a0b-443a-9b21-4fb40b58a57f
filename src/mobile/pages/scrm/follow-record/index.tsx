import { useSearchParams } from 'react-router-dom';
import useMiniProgram from '@/src/mobile/hooks/use-mini-program';
import {
  CommentInput,
  CommentInputSend,
  CommentList,
} from '@echronos/editor/dist/component-comment';
import { Ref } from '@echronos/editor/dist/component-comment/comment-list';
import { CommentInputRef } from '@echronos/editor/dist/component-comment/comment-input-send';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef, useState } from 'react';
import { Spin } from 'antd';
import { Dialog, ImageViewer } from 'antd-mobile';
import { onReady } from '@echronos/core';
import { useMemoizedFn } from 'ahooks';
import { debounce, throttle } from 'lodash';
import { FileListItem } from '@/components';
import Icon from '@echronos/echos-icon';
import { judgeClient } from '../../../utils/utils';
import FileList from '../../../components/file-list';
import Upload from '../../../components/upload/upload';
import styles from './index.module.less';
import {
  commentDel,
  createComment,
  getMemberList,
  getRecordInfo,
  GetRecordInfoResultType,
} from './api';

interface MemberListType {
  id: number;
  display: string;
  avatar: string;
}

function FollowRecord() {
  const mentionsInputRef = useRef<any>(null);
  // const occupyBottomRef = useRef<any>(null);
  const [state, setState] = useState<GetRecordInfoResultType>({
    id: 0,
    companyId: 0,
    customerId: 0,
    customerName: '',
    customerCategory: 0,
    opportunityId: 0,
    opportunityName: '',
    eventType: -1,
    eventTypeName: '',
    eventTime: 0,
    content: '',
    imageList: [],
    attachmentList: [],
    longitude: 0,
    latitude: 0,
    address: '',
    provinceId: 0,
    provinceName: '',
    cityId: 0,
    cityName: '',
    districtId: 0,
    districtName: '',
    createTime: 0,
    updateTime: 0,
    createUser: 0,
    userName: '',
    avatar: '',
    formCode: '',
    versionNumber: 0,
    customExtendMap: {},
    contactList: [],
    followList: [],
    commentList: [],
  });
  const [searchParams] = useSearchParams({
    id: '',
    companyId: '',
    saType: '',
    appId: '',
    token: '',
  });
  const [loading, setLoading] = useState(false);
  const [isShow, setIsShow] = useState(false);
  const [visible, setVisible] = useState(false);
  const [imageViewerSrc, setImageViewerSrc] = useState('');
  const commentRef = useRef<CommentInputRef | null>(null); // 绑定评论ref
  const [memberList, setMemberList] = useState<MemberListType[]>([]); // 获取成员
  const commentListRef = useRef<Ref | null>(null);

  const getExt = (url: string) => {
    if (url) {
      const startIndex = url.lastIndexOf('.');
      return startIndex !== -1 ? url.substring(startIndex + 1, url.length).toLowerCase() : '';
    }
    return '';
  };

  // 编辑
  const onEdit = debounce(() => {
    window.wx.miniProgram.postMessage({ isEdit: true });
  }, 300);

  // 删除
  const onDelete = debounce(() => {
    Dialog.confirm({
      title: '删除跟进记录',
      content: `当前正在删除【${state?.userName}】的跟进记录，此操作不可撤销，是否删除？`,
      onConfirm: () => {
        window.wx.miniProgram.postMessage({ isdelete: true });
      },
    });
  }, 300);

  // 进入客户详情
  const gotoCustomerInfo = debounce(() => {
    window.wx.miniProgram.postMessage({
      customerId: state.customerId,
      customerName: state.customerName,
      customerCategory: state.customerCategory,
    });
  }, 300);

  const imageFile = useMemo(
    () =>
      state.imageList.map(
        (item) =>
          ({
            uid: item.fileId,
            type: 'image',
            url: item.filePath,
            filepath: item.filePath,
            size: item.size,
            name: item.fileName,
            ext: item.fileExt,
          } || [])
      ),
    [state.imageList]
  );

  const filesList = useMemo(() => {
    const imageReg = /^(a?png|p?jpe?g|gif|avif|bmp|ico|cur|jfif|pjp|svg|webp)$/;
    return state.attachmentList.map(
      (m) =>
        ({
          uid: m.fileId,
          url: m.filePath,
          filePath: m.localFilePath,
          type: imageReg.test(getExt(m.filePath)) ? 'image' : 'file',
          raw: {},
          events: {},
          name: m.fileName,
          size: m.size,
          ext: m.fileExt,
        } || [])
    );
  }, [state.attachmentList]);

  // 获取@的成员列表
  const queryAtUserList = (companyId: number) => {
    getMemberList(companyId).then((res) => {
      const list = res.list.map((item) => ({
        id: item.id,
        display: item?.name || item?.realName || item?.nickname,
        avatar: item.avatar,
      }));
      setMemberList(list);
    });
  };

  // 滚动触底加载更多
  const handleScroll = throttle((e: any) => {
    const { scrollHeight, scrollTop, clientHeight } = e.target;
    if (scrollHeight - scrollTop <= clientHeight) {
      commentListRef.current?.loadMore();
    }
  }, 500);

  const onSendComment = useMemoizedFn(async (useIdList: any[], valueText: string, replyed: any) => {
    try {
      const res = await createComment({
        businessType: 1,
        businessId: Number(searchParams.get('id')),
        content: valueText,
        parentReviewId: replyed?.id,
        mentionJson: useIdList.length
          ? JSON.stringify({
              mention: useIdList.map((item) => ({
                memberId: item,
              })),
            })
          : null,
      });

      // 返回发送的数据 !重要
      return res;
    } catch (error) {
      return '';
    }
  });

  const onImageClick = (val: string) => {
    setVisible(true);
    setImageViewerSrc(val);
    // const imageReg = /^(a?png|p?jpe?g|gif|avif|bmp|ico|cur|jfif|pjp|svg|webp)$/;
    // window.wx.miniProgram.postMessage({
    //   data: {
    //     type: 'previewImage',
    //     params: { urls: [val] },
    //   },
    // });
  };

  const onItemClick = (val: string) => {
    window.wx.miniProgram.postMessage({
      data: {
        type: 'previewFile',
        params: { urls: [val] },
      },
    });
  };

  const getMessage = () => {
    const companyId = Number(searchParams.get('companyId'));
    const id = Number(searchParams.get('id'));
    setLoading(true);
    getRecordInfo({ id })
      .then((res) => {
        setState(res);
        queryAtUserList(companyId || res.companyId);
      })
      .finally(() => {
        setLoading(false);
        // eslint-disable-next-line no-console
        console.log('finally------');
        onReady(() => {
          // eslint-disable-next-line no-console
          console.log('onReady-------');
          setIsShow(true);
        });
      });
  };

  useMiniProgram(searchParams.get('saType') as string);

  useEffect(() => {
    getMessage();
    const onListener = () => {
      if (!document.hidden) {
        getMessage();
      }
    };
    let onFocusin = () => {};
    if (judgeClient() === 'iOS') {
      onFocusin = () => {
        setTimeout(() => {
          mentionsInputRef.current.scrollIntoView(true);
          // const scrollLeft = document.documentElement.scrollLeft || document.body.scrollLeft;
          // const scrollTop = document.documentElement.scrollTop || document.body.scrollTop;
          // console.log(`当前水平滚动 x 位置：${scrollLeft}，当前垂直滚动 y 位置：${scrollTop}`);
          // const yPosition = occupyBottomRef.current.offsetTop * 0.43;
          // console.log('yPosition', yPosition);
          // window.scrollTo({ top: yPosition, behavior: 'smooth' });
        }, 800);
      };
      // 监听页面是否可见的变化
      document.addEventListener('focusin', onFocusin);
    }
    document.addEventListener('visibilitychange', onListener);
    return () => {
      document.removeEventListener('visibilitychange', onListener);
      if (judgeClient() === 'iOS') {
        document.removeEventListener('focusin', onFocusin);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <div className={styles.page}>
        <Helmet>
          <title>跟进记录</title>
        </Helmet>
        <div className={styles.messageBox} onScroll={handleScroll}>
          <Spin spinning={loading}>
            <div className={styles.recordBox}>
              <div className={styles.headName}>
                {state.avatar ? (
                  <img src={state.avatar} alt="" className={styles.avatarImg} />
                ) : (
                  <div className={classNames(styles.avatarImg, styles.avatarInit)}>
                    {state.userName.charAt(0)}
                  </div>
                )}
                <span>{`${state.userName}的跟进记录`}</span>
              </div>
              <div className={styles.baseMes}>
                跟进时间：
                <span className={styles.message}>
                  {dayjs(state.eventTime).format('YYYY-MM-DD')}
                </span>
              </div>
              <div className={styles.baseMes}>
                <span className={styles.fixedName}>跟进类型：</span>
                <span className={styles.message}>{state.eventTypeName}</span>
              </div>
              <div
                className={classNames(styles.baseMes, styles.highlight)}
                role="button"
                tabIndex={0}
                onClick={gotoCustomerInfo}
              >
                <span className={styles.fixedName}>客户名称：</span>
                <div className={classNames(styles.message, styles.custormerName)}>
                  {state.customerName}
                </div>
              </div>
              <div className={styles.baseMes}>
                <span className={styles.fixedName}>创&nbsp;建&nbsp;人&nbsp;：</span>
                <span className={styles.message}>{state.userName}</span>
              </div>
              <div className={styles.baseMes}>
                创建时间：
                <span className={styles.message}>
                  {dayjs(state.createTime).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              </div>
              {!!state.updateTime && (
                <div className={styles.baseMes}>
                  更新时间：
                  <span className={styles.message}>
                    {dayjs(state.updateTime).format('YYYY-MM-DD HH:mm:ss')}
                  </span>
                </div>
              )}
            </div>
            <div className={styles.recordBox}>
              <div className={styles.bodyName}>基本信息</div>
              {state.customerCategory === 1 && (
                <div className={styles.infoCell}>
                  <div className={styles.labelName}>客户联系人：</div>
                  <div className={styles.labelValue}>
                    {state.contactList.map((item) => item.contactName).join('、')}
                  </div>
                </div>
              )}
              <div className={styles.infoCell}>
                <div className={styles.labelName}>跟进摘要：</div>
                <div className={styles.labelValue}>{state.content}</div>
              </div>
              <div className={styles.addressBox}>
                <Icon name="positioning_line" size={17} />
                <div className={styles.address}>{state.address}</div>
              </div>
              {!!state.imageList.length && (
                <div className={classNames(styles.infoCell, styles.fileList)}>
                  <div className={classNames(styles.labelName, styles.distance)}>图片：</div>
                  <Upload
                    listType="mixin"
                    maxCount={0}
                    multiple
                    disabled
                    isDelete={false}
                    fileList={imageFile as unknown as FileListItem[]}
                    accept="image/*"
                    onImageClick={(val) => {
                      onImageClick(val);
                    }}
                  />
                </div>
              )}
              {!!state.attachmentList.length && (
                <div className={classNames(styles.infoCell, styles.fileList)}>
                  <div className={classNames(styles.labelName, styles.distance)}>附件：</div>
                  <FileList
                    fileList={filesList as unknown as FileListItem[]}
                    listType="mixin"
                    layout="horizontal"
                    colSpan={24}
                    download
                    canDownload={false}
                    isDelete={false}
                    onItemClick={(val) => {
                      if (val.type === 'image') {
                        onImageClick(val.url);
                      } else {
                        onItemClick(val.url);
                      }
                    }}
                  />
                </div>
              )}
            </div>
            <div className={styles.recordBox}>
              <div className={styles.bodyName}>评论</div>
              {isShow ? (
                <CommentList
                  // 业务 id
                  businessId={Number(searchParams.get('id'))}
                  // 业务类型
                  businessType={1}
                  // 此组件 ref
                  ref={commentListRef}
                  // 输入框组件 ref
                  inputRef={commentRef}
                  token={searchParams.get('token') || ''}
                  // 点击头像
                  onAvatar={() => {}}
                  // 确认点赞, 此动作组件内部会处理, 外部仅需要处理自身业务, 例如校验是否登录, 权限等
                  onZan={(resolve) => {
                    resolve();
                  }}
                  // 确认删除, 此动作组件内部会处理, 外部仅需要处理自身业务, 例如校验是否登录, 权限等
                  onDelete={(resolve, reject, commentItem) => {
                    commentDel({
                      businessId: commentItem.businessId,
                      businessType: commentItem.businessType,
                      reviewId: commentItem.id,
                    })
                      .then(() => {
                        resolve();
                      })
                      .catch(() => {
                        reject();
                      });
                  }}
                  // 确认回复, 此动作组件内部会处理, 外部仅需要处理自身业务, 例如校验是否登录, 权限等
                  onReplay={(resolve) => {
                    resolve();
                  }}
                  // 确认评论, 此动作组件内部会处理, 外部仅需要处理自身业务, 例如校验是否登录, 权限等
                  onComment={(resolve) => {
                    resolve();
                  }}
                />
              ) : null}
            </div>
            <ImageViewer
              image={imageViewerSrc}
              visible={visible}
              onClose={() => {
                setVisible(false);
              }}
            />
          </Spin>
        </div>
        <div className={styles.placeholder} />
      </div>
      <div className={styles.componentInput}>
        <CommentInput
          isLogin
          inputRef={commentRef}
          showLikes={false}
          onComment={(resolve) => {
            resolve();
          }}
          businessType={0}
          token={searchParams.get('token') || ''}
          businessId={Number(searchParams.get('id'))}
          customFuntionNode={
            <div className={styles.moreButton}>
              <div className={styles.itemButton} role="button" tabIndex={0} onClick={onEdit}>
                <Icon name="edit_line" size={20} color="#000" />
                <span className={styles.itemName}>编辑</span>
              </div>
              <div className={styles.itemButton}>
                <Icon name="delete_trash_line" size={20} color="#000" />
                <span className={styles.itemName} role="button" tabIndex={0} onClick={onDelete}>
                  删除
                </span>
              </div>
            </div>
          }
        />
      </div>

      <CommentInputSend
        atlist={memberList}
        ref={commentRef}
        commentListRef={commentListRef}
        mentionsInputRef={mentionsInputRef}
        onSend={onSendComment}
        onTouchBottom={() => {
          if (judgeClient() === 'iOS') {
            setTimeout(() => {
              mentionsInputRef.current.blur();
            }, 100);
          }
        }}
      />
      {/* <div ref={occupyBottomRef} /> */}
    </>
  );
}

export default FollowRecord;
