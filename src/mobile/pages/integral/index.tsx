import { useCallback, useState } from 'react';
import { Collapse, Spin } from 'antd';
import { Helmet } from 'react-helmet';
import { useNavigate, useSearchParams } from 'react-router-dom';
import dayjs from 'dayjs';
import { getUserInvitePointFlow, InvitePointFlowkeyType } from '@/apis';
import { useMount } from 'ahooks';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';
import styles from './index.module.less';

const { Panel } = Collapse;

function IntegralH5() {
  const [params] = useSearchParams();
  const navigate = useNavigate();
  const id = Number(params.get('id'));
  const tenantId = params.get('tenantId');
  const [loading, setLoading] = useState(false);
  const [streamList, setStreamList] = useState<any>({});
  const [data, setData] = useState({
    totalPoints: 0,
    total: 0,
    cumulative: 0,
  });
  // 查询会员积分流水
  const getPointFlow = useCallback(() => {
    if (id && tenantId) {
      setLoading(true);
      getUserInvitePointFlow({ id, tenantId })
        .then((res) => {
          setStreamList(res.map || {});
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [id, tenantId]);
  // 修正原因
  const getCorrectStr = (val: number) => {
    switch (val) {
      case 1:
        return '邀请用户奖励';
      case 2:
        return '邀请成为团长/分销商';
      case 3:
        return '积分修正-礼品兑换';
      case 4:
        return '积分核销-核销错误';
      case 5:
        return '积分修正-其他';
      case 6:
        return '积分兑券';
      case 7:
        return '消费积分';
      default:
        return '';
    }
  };

  const exchange = () => {
    navigate(`/m/integral/volume?tenantId=${tenantId}`);
  };

  // 进入页面
  useMount(() => {
    const token = params.get('token');
    const dataList = params.get('data')?.split('-');
    if (token) {
      setHeader('satype', 'ech-aop');
      setToken(token);
    }
    if (dataList?.length) {
      setData({
        totalPoints: +dataList[0],
        total: +dataList[1],
        cumulative: +dataList[2],
      });
    }
    getPointFlow();
  });
  return (
    <div className={styles.integralPage}>
      <Helmet>
        <title>会员积分</title>
      </Helmet>
      <Spin spinning={loading}>
        <div className={styles.integralInfo}>
          <div className={styles.integralInfoTop}>
            <span className={styles.integralTotal}>{data.totalPoints}</span>
            <span className={styles.integralText}>积分余额</span>
          </div>
          <div className={styles.integralInfoBot}>
            <div className={styles.integralInfoTop}>
              <span className={styles.integralNum}>{data.total}</span>
              <span className={styles.integralText}>历史总额</span>
            </div>
            <div className={styles.integralInfoTop}>
              <span className={styles.integralNum}>{data.cumulative}</span>
              <span className={styles.integralText}>累计兑换</span>
            </div>
          </div>
          <div className={styles.exchange} tabIndex={0} role="button" onClick={exchange}>
            兑换
          </div>
        </div>
        <Collapse expandIconPosition="right" ghost className={styles.collapse}>
          {Object.keys(streamList).map((item) => (
            <Panel
              header={`${item.split('-')[0]}年${item.split('-')[1]}月`}
              key={item}
              className={styles.panel}
            >
              {streamList[item].map((val: InvitePointFlowkeyType) => (
                <div className={styles.panelItem} key={val.pointsId}>
                  <div className={styles.panelItemTitle}>
                    <span className={styles.panelItemName}>{getCorrectStr(val.reason)}</span>
                    <span className={val.pm ? undefined : styles.panelItemAdd}>
                      {`${val.pm ? '-' : '+'}${val.amount}`}
                    </span>
                  </div>
                  <div className={styles.panelItemInfo}>
                    <span style={{ marginRight: '12px' }}>
                      {dayjs(val.createTime).format('M月DD日 HH:mm')}
                    </span>
                    {val.reason > 2 && <span>操作人：{val.memberName}</span>}
                  </div>
                </div>
              ))}
            </Panel>
          ))}
        </Collapse>
      </Spin>
    </div>
  );
}

export default IntegralH5;
