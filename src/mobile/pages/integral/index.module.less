@import 'styles/mixins/mixins';

.integralPage {
  min-height: 100vh;
  padding: 16px;
  background: #f5f6fa;
}

.integralInfo {
  color: #fff;
  display: flex;
  width: 100%;
  height: 162px;
  margin-bottom: 14px;
  padding: 20px;
  overflow: hidden;
  justify-content: space-between;
  position: relative;
  border-radius: 12px;
  background: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023911/1694396966098944.png')
    no-repeat center -26px;
  flex-direction: column;
  box-shadow: 0 20px 40px 0 rgb(3 17 55 / 15%);
}

.exchange {
  color: #ff5923;
  font-weight: 600;
  width: 48px;
  height: 28px;
  line-height: 28px;
  position: absolute;
  top: 20px;
  right: 20px;
  text-align: center;
  border-radius: 8px;
  background: #fff;
}

.integralBgimg {
  width: 100%;
  height: 162px;
  margin: auto;
  position: absolute;
  top: 0;
  left: 0;
  // object-fit: cover;
}

.integralInfoTop {
  display: flex;
  flex-direction: column;
  margin-right: 61px;
}

.integralInfoBot {
  display: flex;
}

.integralTotal {
  font-size: 32px;
  font-weight: 600;
  line-height: 32px;
}

.integralText {
  font-size: 14px;
  opacity: 0.7;
  margin-top: 4px;
}

.integralNum {
  font-size: 14px;
}

.collapse {
  :global {
    .ant-collapse-header {
      color: #888b98 !important;
      font-size: 14px !important;
      padding: 16px 0 !important;
    }

    .ant-collapse-arrow {
      right: 0 !important;
    }

    .ant-collapse-content-box {
      border-radius: 12px;
      background: #fff;
      padding-bottom: 0 0 16px 0 !important;
    }
  }
}

.card {
  border-radius: 12px;
  background: #fff;
  padding: 16px;
}

.panelItem {
  font-size: 14px;
  padding: 16px 16px 0;

  .panelItemTitle {
    color: #040919;
    font-weight: bold;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .panelItemName {
    font-size: 16px;
    line-height: 24px;
    margin-bottom: 6px;
  }

  .panelItemAdd {
    color: #008cff;
  }

  .panelItemInfo {
    color: #888b98;
  }
}
