import debounce from 'lodash/debounce';
import {
  addReportGroupInsertSnap,
  FormDetailType,
  getPartnerInfo,
  getReportGroupSnap,
  PartnerInfoType,
  reportGroupAdd,
  reportGroupDetail,
  queryReportPaymentInstall,
} from '@/apis';
import { Empty, Modal } from '@/components';
import AccompanyForm, { AccompanyFormRef } from '@/src/newspaper/components/accompany-form';
import { setToken } from '@/utils/auth';
import { setHeader } from '@/utils/http';
import { Button, Checkbox, message, Spin } from 'antd';
import classNames from 'classnames';
// import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { reportGroupDetailType } from '@/apis/ims/report-group-detail';
import Container from '../../components/container';
import styles from './application.module.less';
import ServiceAgreement from './service-agreement';

// declare let wx: any;

function Application() {
  const navigator = useNavigate();
  const [searchParams] = useSearchParams();
  const [type, setType] = useState<'edit' | 'check'>(
    (searchParams.get('type') as 'edit' | 'check') || 'check'
  );
  const [title, setTitle] = useState<string>(searchParams.get('title') || '悦安居参团报名');
  const [isSubmit, setIsSubmit] = useState(false);
  const companyId = searchParams.get('companyId') || 0;
  const groupId = searchParams.get('groupId') || 0;
  const tenantId = searchParams.get('tenantId') || '';
  const [reissue, setReissue] = useState<number | string>(0);
  const shareType = searchParams.get('shareType') || '';
  const errMessage = searchParams.get('errMessage') || '';
  // const isSelf = JSON.parse(searchParams.get('isSelf') || 'false');
  const userId = searchParams.get('userId') || '';
  const [cardState, setCardState] = useState({} as unknown as PartnerInfoType);
  const [state, setState] = useState({} as unknown as reportGroupDetailType);
  const [defalutVal, setDefalutVal] = useState<FormDetailType>({} as unknown as FormDetailType);
  const [loading, setLoading] = useState(false);
  const accompanyForm = useRef<AccompanyFormRef>(null as unknown as AccompanyFormRef);
  const [isAgreeWith, setIsAgreeWith] = useState(false); // 用户服务协议
  const [agreement, setIsAgreement] = useState(false); // 用户服务协议

  // 获取合伙人信息
  const getUserInfo = () => {
    setLoading(true);
    const params = {
      partnerCompanyId: +companyId,
      tenantId,
    };
    getPartnerInfo(params)
      .then((res) => {
        setCardState(res);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 获取详情
  const getDetailData = () => {
    setLoading(true);
    reportGroupDetail({ id: +groupId, queryType: 1 })
      .then((res) => {
        setDefalutVal(res.reportGroupFormDetailVO);
        setState(res);
        setCardState(res.partnerDetailVO);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 查询草稿
  const getDraftData = () => {
    getReportGroupSnap().then((res) => {
      setDefalutVal(res);
    });
  };

  // 保存草稿
  const seveDraftData = () => {
    addReportGroupInsertSnap(accompanyForm.current.getDraftValue()).then(() => {
      message.success('保存草稿成功');
    });
  };

  // 表单提交
  const onSubmit = debounce(() => {
    if (!isAgreeWith) {
      message.warning('请先阅读并同意协议内容');
      return;
    }
    accompanyForm.current
      .getFieldsValue()
      .then((formParam) => {
        if (loading) return;
        setLoading(true);
        const params = {
          id: reissue === '1' ? null : +state.id,
          formParam,
          insertType: (shareType ? 1 : 0) as 0 | 1,
          partnerCompanyId: +companyId,
          tenantId: shareType ? tenantId : undefined,
          partnerUserId: shareType ? userId : null,
        };
        if (isSubmit) return;
        // 调试
        setIsSubmit(true);
        reportGroupAdd(params)
          .then((res) => {
            message.success('报名成功');
            queryReportPaymentInstall().then((report) => {
              if (report.isOpen) {
                navigator(`/m/newspaper/confirmation?reportGroupId=${res.id}`, { replace: true });
              } else {
                navigator(`/m/newspaper?companyId=${companyId}&tenantId=${tenantId}`);
              }
            });
            // if (shareType) {
            //   navigator(`/m/newspaper?companyId=${companyId}&tenantId=${tenantId}`);
            // } else {
            //   navigator(-1);
            // }
          })
          .finally(() => {
            setLoading(false);
            setIsSubmit(false);
          });
      })
      .catch(message.error);
  }, 300);

  let footer = null;
  if (type === 'edit' && !errMessage) {
    footer = (
      <div className={styles.footerBox}>
        <Button
          type="text"
          className={styles.footerBtn}
          onClick={() => {
            const cancelHandler = () => {
              if (shareType) {
                navigator(`/m/newspaper?companyId=${companyId}&tenantId=${tenantId}`);
                return;
              }
              if (Object.keys(state).length && !reissue) {
                setType('check');
                setTitle(searchParams.get('title') || '参团报名详情');
                getDetailData();
                return;
              }
              navigator(-1);
            };
            if (accompanyForm.current.getFormChange()) {
              Modal.confirm({
                title: '提示',
                centered: true,
                content: '当前内容未保存，是否要保存为草稿？',
                onOk: () => {
                  seveDraftData();
                  cancelHandler();
                },
                onCancel: cancelHandler,
              });
            } else cancelHandler();
          }}
        >
          取消
        </Button>
        <Button
          type="text"
          htmlType="submit"
          className={classNames(styles.footerBtn, styles.footerSubmit)}
          onClick={() => {
            if (!isSubmit) {
              onSubmit();
            }
          }}
        >
          报名
        </Button>
      </div>
    );
  }
  if (type === 'edit' && state.verifyStatus === 10) {
    footer = (
      <div className={styles.footerBox}>
        <Button
          type="text"
          className={styles.footerBtn}
          onClick={() => {
            const cancelHandler = () => {
              if (Object.keys(state).length && !reissue) {
                setType('check');
                setTitle(searchParams.get('title') || '参团报名详情');
                getDetailData();
                return;
              }
              navigator(-1);
            };
            cancelHandler();
          }}
        >
          取消
        </Button>
        <Button
          type="text"
          htmlType="submit"
          className={classNames(styles.footerBtn, styles.footerSubmit)}
          onClick={() => {
            if (!isSubmit) {
              onSubmit();
            }
          }}
        >
          报名
        </Button>
      </div>
    );
  }
  if (type === 'check' && state.verifyStatus === 10 && state.payStatus === 0 && !state.hasOrder) {
    footer = (
      <div className={styles.footerBox}>
        <Button
          type="text"
          className={classNames(styles.footerBtn, styles.footerSubmit)}
          onClick={() => setType('edit')}
        >
          编辑
        </Button>
      </div>
    );
  }
  if (type === 'check' && (state.verifyStatus === 30 || state.verifyStatus === 40)) {
    footer = (
      <div className={styles.footerBox}>
        <Button
          type="text"
          className={styles.footerSubmit}
          onClick={() => {
            setType('edit');
            setReissue('1');
            setTitle('悦安居参团报名');
          }}
        >
          重新发起
        </Button>
      </div>
    );
  }

  useEffect(() => {
    const currentReissue = searchParams.get('reissue');
    if (currentReissue) {
      setReissue(currentReissue);
    }
    if ((groupId && type === 'check') || currentReissue) {
      getDetailData();
    }
    if (type === 'edit') {
      getDraftData();
    }
    if (companyId && type === 'edit') getUserInfo();
    const token = searchParams.get('token');
    if (token) {
      setHeader('satype', 'ech-aop');
      setToken(token);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groupId]);

  return (
    <Container className={styles.applicationEdit} footer={footer}>
      <Helmet>
        <title>{title}</title>
      </Helmet>
      <Spin spinning={loading}>
        {!errMessage ? (
          <>
            <AccompanyForm
              ref={accompanyForm}
              state={state}
              cardState={cardState}
              type={type}
              pageType="H5"
              defalutVal={defalutVal}
              isShare={false}
            />
            {type === 'edit' && (
              <div className={styles.agreement}>
                <Checkbox
                  checked={isAgreeWith}
                  onChange={(val) => {
                    setIsAgreeWith(val.target.checked);
                  }}
                />
                <div className={styles.agreementText}>
                  我已阅读并同意
                  <span
                    className={styles.serviceAgreement}
                    tabIndex={-1}
                    role="button"
                    onClick={() => {
                      setIsAgreement(true);
                    }}
                  >
                    《悦安居团购报名协议》
                  </span>
                </div>
              </div>
            )}
            <Modal
              // title="用户协议"
              visible={agreement}
              closable={false}
              destroyOnClose
              okText="我已阅读并同意"
              onOk={() => {
                setIsAgreeWith(true);
                setIsAgreement(false);
              }}
              onCancel={() => setIsAgreement(false)}
            >
              <div style={{ height: '500px', display: 'flex', flexDirection: 'column' }}>
                <div
                  style={{
                    overflow: 'auto',
                    paddingTop: '10px',
                    paddingBottom: '10px',
                    textAlign: 'center',
                    fontSize: '16px',
                    fontWeight: 'bold',
                  }}
                >
                  服务协议
                </div>
                <div
                  style={{ flex: 1, overflow: 'auto', paddingTop: '10px', paddingBottom: '10px' }}
                >
                  <ServiceAgreement />
                </div>
              </div>
            </Modal>
          </>
        ) : (
          <Empty message={errMessage} style={{ marginTop: 80 }} />
        )}
      </Spin>
    </Container>
  );
}

export default Application;
