import { useState, useMemo, ReactNode } from 'react';
import { useMemoizedFn, useDebounceFn, useMount } from 'ahooks';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Spin, Button, Drawer } from 'antd';
import { Helmet } from 'react-helmet';
import type { GraphicDescDrawerPropsDataItem } from '@/components';
import {
  // getReportGroupSku,
  postReportGroupCreateOrder,
  queryReportPaymentInstall,
  queryReportGroupSpu,
} from '@/apis';
import type { ReportGroupSpuResult } from '@/apis';
// import { GetReportGroupSkuResultType } from '@/apis/ims/get-report-group-sku';
import { Price, Icon, StepperInput } from '@/components';
import styles from './confirmation.module.less';

function NewspaperConfirmation() {
  const [searchParams] = useSearchParams({ reportGroupId: '' });
  const navigate = useNavigate();
  const [showLoading, setShowLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState<Array<ReportGroupSpuResult>>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [reportGroupId, setReportGroupId] = useState(0);
  const [count, setCount] = useState(0);
  const [graphicDescData, setGraphicDescData] = useState<GraphicDescDrawerPropsDataItem[]>([]);

  // 有shopSkuId代表之前有商品
  const shopSkuId = searchParams.get('shopSkuId') || '';
  const currentItem = useMemo(
    () => list[currentIndex] || { marketPrice: 0, id: 0, saleGroup: 0 },
    [list, currentIndex]
  );
  const amount = useMemo(
    () => (currentItem.marketPrice || currentItem.marketPrice) * count,
    [currentItem, count]
  );

  const { run: toPay } = useDebounceFn(
    () => {
      setShowLoading(true);
      window.console.log('postReportGroupCreateOrder params', {
        reportGroupId,
        shopSkuId: currentItem.id,
        number: count,
      });
      postReportGroupCreateOrder({
        reportGroupId,
        shopSkuId: currentItem.id,
        number: count,
      })
        .then((res) => {
          window.wx.miniProgram.getEnv((res1) => {
            if (res1.miniprogram) {
              window.wx.miniProgram.redirectTo({
                url: `/subpackages/order/pages/pay/index?payNo=${res.payNo}&orderNo=${
                  res.orderNo
                }&backUrl=${encodeURIComponent(
                  `/subpackages/other/pages/h5/index?url=${window.location.origin}/m/newspaper`
                )}`,
              });
            }
          });
        })
        .finally(() => {
          setShowLoading(false);
        });
    },
    { wait: 3000, leading: true, trailing: false }
  );

  // 处理生成商品详情的数据
  const createGraphicDescData = (detailList: ReportGroupSpuResult['detailList'] = []) => {
    const imageList: string[] = [];
    const textList: string[] = [];
    const graphicDescArr: GraphicDescDrawerPropsDataItem[] = [];
    detailList.forEach((item) => {
      if (item?.imgUrl && !item?.videoUrl) {
        imageList.push(item.imgUrl);
        graphicDescArr.push({ type: 'image', value: item.imgUrl });
      } else if (item.text) {
        textList.push(item.text);
        graphicDescArr.push({ type: 'text', value: item.text });
      } else {
        graphicDescArr.push({
          type: 'video',
          value: item.videoUrl,
          image: item?.imgUrl || '',
        });
      }
    });
    return graphicDescArr;
  };

  const handleToggleSku = useMemoizedFn((option: ReportGroupSpuResult, index: number) => {
    setVisible(false);
    if (index === currentIndex) return;
    setCurrentIndex(index);
    setCount(option.saleGroup);
    setGraphicDescData(createGraphicDescData(list?.[index]?.detailList || []));
  });

  // 查询商品列表
  const getReportGroupSpu = (id: number) => {
    queryReportGroupSpu({ shopSkuId: id }).then((res) => {
      if (res.list.length) {
        setList(res.list);
        setCount(res.list[0].saleGroup);
        setGraphicDescData(createGraphicDescData(res.list?.[0]?.detailList || []));
      } else {
        navigate(-1);
      }
    });
  };
  // 查询是否有开启付费商品
  const getReportPayment = () => {
    queryReportPaymentInstall().then((res) => {
      getReportGroupSpu(res.shopSkuId);
    });
  };

  useMount(() => {
    const rawReportGroupId = searchParams.get('reportGroupId');
    window.console.log('rawReportGroupId', rawReportGroupId);
    if (rawReportGroupId) {
      setReportGroupId(+rawReportGroupId);
    }
    if (shopSkuId) {
      getReportGroupSpu(+shopSkuId);
    } else {
      getReportPayment();
    }
  });

  return (
    <Spin spinning={showLoading} wrapperClassName={styles.spin}>
      <Helmet>团购服务付款</Helmet>

      {/* <img
        src="https://img.huahuabiz.com/media/0/ios/01712041420181058104/1712041420139.jpg"
        alt=""
        className={styles.img}
      /> */}

      <img src={currentItem.imageList?.[0]} alt="" className={styles.img} />
      <div className={styles.card}>
        <div
          role="presentation"
          className={styles.item}
          onClick={() => {
            setVisible(true);
          }}
        >
          <span className={styles.label}>规格</span>
          <span className={styles.value}>
            {currentItem?.standardList?.map((item) => item.value).join('、')}
          </span>
          <Icon name="right" size={16} className={styles.icon} />
        </div>
        <div className={styles.item}>
          <span className={styles.label}>数量</span>
          <StepperInput
            value={count}
            min={currentItem.saleGroup}
            max={999}
            regex={/^([1-9]\d*|0)(\.\d*)?$/}
            stepperNumber={currentItem.saleGroup}
            className={styles.stepperInput}
            onChange={(value) => {
              setCount(Math.max(currentItem.saleGroup, +value));
            }}
          />
        </div>
      </div>
      <div className={styles.infoDesc}>
        <div className={styles.infoDescTitle}>商品详情</div>
        {graphicDescData?.length ? (
          <div className={styles.infoDescContent}>
            {/* <meta
              name="viewport"
              content="width=device-width,initial-scale=1,target-densitydpi=[dpi-value|device-dpi|high-dpi|medium-dpi|low-dpi],user-scalable=no"
            /> */}
            {graphicDescData.map((item, index) => {
              let element: ReactNode | string = '';
              switch (item.type) {
                case 'image':
                  element = (
                    <img
                      src={item.value}
                      alt="图片"
                      className={styles.infoDescImageItem}
                      key={`${item.value + index}`}
                    />
                  );
                  break;
                case 'video':
                  element = (
                    // eslint-disable-next-line jsx-a11y/media-has-caption
                    <video
                      src={item.value}
                      className={styles.infoDescVideo}
                      controls
                      key={`${item.value + index}`}
                    />
                  );
                  break;
                case 'text':
                  element = (
                    <div className={styles.infoDescText} key={`${item.value + index}`}>
                      {item.value}
                    </div>
                  );
                  break;

                default:
                  break;
              }
              return element;
            })}
            {/* {graphicDescInfo.textList.map((item, index) => (
                <div className={styles.infoDescText} key={`${item + index}`}>
                  {item}
                </div>
              ))}
              <div className={styles.infoDescImage}>
                {graphicDescInfo.imageList.map((item, index) => (
                  <img
                    src={item}
                    alt="图片"
                    className={styles.infoDescImageItem}
                    key={`${item + index}`}
                  />
                ))}
              </div> */}
          </div>
        ) : null}
      </div>
      <div className={styles.occupyBottom} />

      <div className={styles.footerWrap}>
        <div className={styles.footerView}>
          <div>
            <span>合计：</span>
            <Price
              value={`${amount}`}
              separate
              className={styles.amountInt}
              deciClassName={styles.amountFloat}
              symbolClassName={styles.amountSymbol}
            />
          </div>
          <Button type="primary" disabled={!Number(amount?.toFixed(2))} danger onClick={toPay}>
            付款
          </Button>
        </div>
      </div>

      <Drawer
        title="规格"
        placement="bottom"
        visible={visible}
        closable={false}
        height="392px"
        extra={
          <Icon
            name="close"
            size={24}
            role="presentation"
            onClick={() => {
              setVisible(false);
            }}
          />
        }
        onClose={() => {
          setVisible(false);
        }}
        className={styles.drawer}
      >
        <div className={styles.options}>
          {list.map((option, index) => (
            <div
              className={styles.option}
              key={option.id}
              role="presentation"
              onClick={() => {
                handleToggleSku(option, index);
              }}
            >
              {option.standardList.map((item) => item.value).join('、')}
            </div>
          ))}
        </div>
      </Drawer>
    </Spin>
  );
}

export default NewspaperConfirmation;
