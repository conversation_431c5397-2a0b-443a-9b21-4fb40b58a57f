.textBox {
  display: flex;
  margin-top: 8px;
  justify-content: space-between;
  align-items: center;
}

.title {
  font-size: 18px;
  font-weight: bold;
  margin-top: 0;
  margin-bottom: 10px;
}

.toDetail {
  color: #008cff;
  font-size: 12px;
  font-weight: normal;
  cursor: pointer;
}

.status {
  color: #05d380;
}

.refuse {
  color: #888b98;
  margin-left: 17px;
}

.afresh {
  font-size: 12px;
  font-weight: 500;
  display: flex;
  width: 75px;
  height: 25px;
  justify-content: center;
  border-radius: 6px;
  background: #f3f3f3;
  align-items: center;
  cursor: pointer;

  & + & {
    margin-left: 16px;
  }
}

.btnBox {
  display: inline-flex;
}
