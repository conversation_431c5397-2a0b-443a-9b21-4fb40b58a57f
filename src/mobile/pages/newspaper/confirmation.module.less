.spin {
  :global {
    & .ant-spin-container {
      height: 100vh;
      padding: 8px 16px 0;
      background-color: #f5f6fa;
    }
  }
}

.img {
  display: block;
  width: 343px;
  // height: 500px;
  border-radius: 12px;
  margin-bottom: 16px;
}

.card {
  margin-bottom: 16px;
  padding: 0 16px;
  background: #fff;
  border-radius: 12px;
}

.item {
  display: flex;
  height: 56px;
  justify-content: space-between;
  align-items: center;

  & + & {
    border-top: 1px solid #f3f3f3;
  }
}

.label {
  color: #040919;
  font-size: 17px;
  font-weight: 500;
  padding-right: 16px;
}

.value {
  color: #040919;
  font-size: 17px;
  width: 0;
  padding-left: 20px;
  overflow: hidden;
  flex: 1;
  text-align: right;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.footerWrap {
  height: 0;
}

.footerView {
  display: flex;
  width: 100vw;
  height: 78px;
  padding: 20px 16px;
  justify-content: space-between;
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  align-items: center;
  background: #fff;
  box-shadow: inset 0 1px 0 0 #f3f3f3;
}

.icon {
  color: #999eb2;
  margin-left: 4px;
}

.stepperInput {
  width: 120px;

  :global {
    .ant-input {
      font-size: 12px;
    }
  }
}

.amountInt {
  font-size: 20px;
  font-weight: 500;
}

.amountSymbol,
.amountFloat {
  font-size: 14;
  font-weight: 500;
}

.drawer {
  :global {
    .ant-drawer-content {
      border-radius: 12px 12px 0 0;
      overflow: hidden;
      background-color: #f5f6fa;
      border-bottom: none;
    }

    .ant-drawer-header {
      height: 54px;
      padding: 15px 16px 15px 40px;
      background-color: #f5f6fa;
    }

    .ant-drawer-title {
      font-size: 17px;
      font-weight: 500;
      text-align: center;
    }

    .ant-drawer-body {
      padding: 8px 16px 28px;
    }
  }
}

.option {
  font-size: 17px;
  height: 54px;
  line-height: 24px;
  padding: 15px 16px;
  overflow: hidden;
  background: #fff;
  border-radius: 12px;
  text-overflow: ellipsis;
  white-space: nowrap;
  text-align: center;

  & + & {
    margin-top: 8px;
  }
}

.infoDesc {
  width: 100%;
  // height: calc(100% - 122px);
  // overflow: scroll;

  .infoDescTitle {
    font-size: 18px;
    margin-bottom: 10px;
  }

  .addGraphicDescBtn {
    color: @primary-color;
    display: flex;
    width: 100%;
    height: 80px;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    border: 1px dashed @primary-color;
    cursor: pointer;
    background: rgb(217 238 255 / 30%);
  }

  .infoDescText {
    // overflow: hidden;
    // text-overflow: ellipsis;
    // display: -webkit-box;
    // -webkit-line-clamp: 2;
    // -webkit-box-orient: vertical;
    color: #888b98;
    width: 100%;
    margin-bottom: 10px;
  }

  .infoDescImage {
    display: flex;
    width: 100%;
    height: 100%;
    overflow: scroll;
    flex-wrap: wrap;

    &Item {
      // width: 23%;
      // height: 23%;
      // max-height: 230px;
      // max-width: 230px;
      // width: 150px;
      // height: 150px;
      width: 100%;
      height: auto;
      margin-bottom: 10px;
      // margin: 10px;
      // box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
      // border-radius: 9px;
      // object-fit: cover;
    }
  }

  .infoDescVideo {
    width: 100%;
  }
}

.occupyBottom {
  height: 150px;
}
