import { getReportGroupSchedule, cancelGroupPurchase } from '@/apis';
import { Empty, Modal } from '@/components';
import { setToken } from '@/utils/auth';
import { setHeader } from '@/utils/http';
import { useMemoizedFn, useMount } from 'ahooks';
import { Button, Spin, Input, message } from 'antd';
import { useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ParamsType, GroupScheduleType } from '@/apis/ims/get-report-group-schedule';
import Container from '../../components/container';
import Tabs from '../../components/tabs';
import useCheckLogin from '../../hooks/use-check-login';
import ApplicationItem from './application-item';
import styles from './index.module.less';

const tabsList = [
  {
    label: '已通过',
    key: 20,
  },
  {
    label: '待付款',
    key: 0,
  },
  {
    label: '审核中',
    key: 10,
  },
  {
    label: '拒绝/取消',
    key: 30,
  },
];

function ApplicationSchedule() {
  const navigate = useNavigate();
  const [params] = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [tabsValue, setTabsValue] = useState<string | number>(20);
  const [dataList, setDataList] = useState<GroupScheduleType[]>([]);
  // const [total, setTotal] = useState(0);
  const scrollView = useRef(null as unknown as HTMLDivElement);
  const requestParams = useRef<ParamsType>({
    verifyStatusSet: 20,
    pageSize: 999,
    pageNo: 1,
  });
  const companyId = params.get('companyId') || 0;
  const tenantId = params.get('tenantId') || '';
  const { checkLogin } = useCheckLogin();

  // 获取报名进度列表
  const getApplicationList = () => {
    setLoading(true);
    window.console.log('requestParams', requestParams.current);
    getReportGroupSchedule(requestParams.current)
      .then((res) => {
        window.console.log('requestResult', res);
        // setTotal(res.pagination.count);
        if (dataList.length && requestParams.current.pageNo && requestParams.current.pageNo > 1) {
          setDataList([...dataList, ...res.list]);
        } else {
          setDataList(res.list);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const toPay = useMemoizedFn((arg: GroupScheduleType) => {
    if (arg.hasOrder && arg.orderNo && arg.payNo) {
      window.wx.miniProgram.getEnv((res) => {
        if (res.miniprogram) {
          window.wx.miniProgram.navigateTo({
            url: `/subpackages/order/pages/pay/index?payNo=${arg.payNo}&orderNo=${
              arg.orderNo
            }&backUrl=${encodeURIComponent(
              `/subpackages/other/pages/h5/index?url=${window.location.origin}/m/newspaper`
            )}`,
          });
        }
      });
    } else {
      navigate(
        `/m/newspaper/confirmation?reportGroupId=${arg.id}&shopSkuId=${arg.shopSkuId || ''}`
      );
    }
  });

  const onCancel = useMemoizedFn((arg: GroupScheduleType) => {
    let cancelReason = '';
    Modal.confirm({
      title: '参团取消',
      centered: true,
      content: (
        <>
          <div className={styles.modalLabel}>取消原因</div>
          <Input.TextArea
            placeholder="请填写取消原因（200字内）"
            maxLength={200}
            className={styles.modalTextArea}
            onChange={(e) => {
              cancelReason = e.target.value;
            }}
          />
        </>
      ),
      className: styles.modal,
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        setLoading(true);
        window.console.log('cancelReason', cancelReason);
        cancelGroupPurchase({
          id: arg.id,
          verifyStatus: 40,
          cancelReason,
        })
          .then(() => {
            const nextDataList = dataList.filter((item) => item.id !== arg.id);
            if (nextDataList.length) {
              setDataList([...nextDataList]);
            } else {
              requestParams.current.pageNo = 1;
              getApplicationList();
            }
            message.success('取消成功！');
          })
          .finally(() => {
            setLoading(false);
          });
      },
    });
  });

  // 去详情
  const toDetail = (groupId: number) => {
    navigate(
      `/m/application?companyId=${companyId}&tenantId=${tenantId}&groupId=${groupId}&type=check&title=参团报名详情`
    );
  };

  // 重新发起
  const reissue = (groupId: number) => {
    navigate(
      `/m/application?companyId=${companyId}&tenantId=${tenantId}&groupId=${groupId}&type=edit&reissue=1`
    );
  };

  useMount(() => {
    const token = params.get('token');
    if (token) {
      setHeader('satype', 'ech-aop');
      setToken(token);
    }
    if (tenantId) {
      setHeader('tenantId', tenantId);
    }
    checkLogin();
    getApplicationList();
  });

  return (
    <Container
      footer={
        <Button
          type="text"
          className={styles.footerBtn}
          onClick={() =>
            navigate(`/m/application?companyId=${companyId}&tenantId=${tenantId}&type=edit`)
          }
        >
          新建团购报名
        </Button>
      }
      className={styles.application}
    >
      <Helmet>
        <title>报名进度</title>
      </Helmet>
      <Spin spinning={loading}>
        <div className={styles.contentBox}>
          <Tabs
            tabs={tabsList}
            value={tabsValue}
            onChange={(key) => {
              if (key === tabsValue) return;
              setTabsValue(key);
              requestParams.current = {
                pageNo: 1,
                pageSize: 999,
              };
              if (key === 0) {
                requestParams.current.verifyStatusSet = 10;
                requestParams.current.payStatusSet = [0, 2];
              } else if (key === 20) {
                requestParams.current.verifyStatusSet = 20;
              } else if (key === 10) {
                requestParams.current.verifyStatusSet = 10;
                // @ts-ignore
                requestParams.current.payStatusSet = [1];
              } else if (key === 30) {
                // @ts-ignore
                requestParams.current.verifyStatusSet = [30, 40];
              }
              getApplicationList();
            }}
          />
          <div className={styles.scrollView} ref={scrollView}>
            {dataList.length ? (
              dataList.map((item) => (
                <ApplicationItem
                  data={item}
                  key={item.id}
                  toDetail={toDetail}
                  toPay={toPay}
                  reissue={reissue}
                  onCancel={onCancel}
                />
              ))
            ) : (
              <Empty style={{ marginTop: '150px' }} message="暂无相关数据" />
            )}
          </div>
        </div>
      </Spin>
    </Container>
  );
}

export default ApplicationSchedule;
