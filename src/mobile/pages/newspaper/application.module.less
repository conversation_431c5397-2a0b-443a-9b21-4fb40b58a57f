.applicationEdit {
  padding: 8px 0;
  background: #f5f6fa;

  :global {
    .ant-form-item-label {
      font-weight: bold;
      height: auto;
      padding-bottom: 8px;
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-select-selector {
      padding: 0 !important;
    }

    .ant-input {
      padding: 0 !important;
    }

    .ant-input-affix-wrapper {
      padding: 0 !important;
    }

    .ant-select-arrow {
      right: 0;
    }

    .adm-radio-content {
      font-size: 14px !important;
    }

    .adm-radio-icon {
      width: 18px !important;
      height: 18px !important;
    }

    .ant-btn-text[disabled] {
      background: #c6ccd8;
      color: #fff;
    }
  }
}

.footerBox {
  display: flex;
  width: 100%;
  padding: 16px;
  align-items: center;
  background: #f5f6fa;

  .footerBtn {
    width: 100%;
    background: #fff;
    margin-right: 16px;
  }

  .footerSubmit {
    color: #fff;
    font-size: 14px;
    width: 100%;
    margin: 0;
    z-index: 1;
    text-align: center;
    border-radius: 10px;
    background: #e60019;
    cursor: pointer;
  }
}
// 用户协议
.agreement {
  display: flex;
  width: 100%;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.agreementText {
  margin-left: 10px;
}

.serviceAgreement {
  color: #008cff;
  cursor: pointer;
}
