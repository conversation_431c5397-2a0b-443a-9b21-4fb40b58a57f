import { GroupScheduleType } from '@/apis';
import { useRef } from 'react';
import { Icon } from '@/components';
import { Divider } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import Card from '../../components/card/card';
import styles from './application-item.module.less';

interface ItemPropsType {
  data: GroupScheduleType;
  toDetail: SimpleFn<number>;
  toPay: SimpleFn<GroupScheduleType>;
  onCancel?: SimpleFn<GroupScheduleType>;
  reissue: SimpleFn<number>;
}

function ApplicationItem({ data, toDetail, toPay, onCancel, reissue }: ItemPropsType) {
  const statusLable = useRef({
    10: <span style={{ color: '#F9AE08' }}>审核中</span>,
    20: <span style={{ color: '#05D380' }}>已通过</span>,
    30: <span style={{ color: '#EA1C26' }}>已拒绝</span>,
    40: <span style={{ color: '#EA1C26' }}>已取消</span>,
  });
  const payLable = useRef({
    0: <span style={{ color: '#F9AE08' }}>待支付</span>,
    1: <span style={{ color: '#05D380' }}>已支付</span>,
    2: <span style={{ color: '#EA1C26' }}>已取消</span>,
  });

  let buttons = null;
  if (data.verifyStatus === 30 || data.verifyStatus === 40) {
    buttons = (
      <div className={styles.afresh} tabIndex={-1} role="button" onClick={() => reissue(data.id)}>
        重新发起
      </div>
    );
  } else if (data.verifyStatus === 10 && data.payStatus === 0) {
    if (data.hasOrder) {
      buttons = (
        <div className={styles.btnBox}>
          <div
            className={styles.afresh}
            tabIndex={-1}
            role="button"
            onClick={() => {
              onCancel?.(data);
            }}
          >
            取消
          </div>

          <div
            className={styles.afresh}
            tabIndex={-1}
            role="button"
            onClick={() => {
              toPay(data);
            }}
          >
            付款
          </div>
        </div>
      );
    } else {
      buttons = (
        <div className={styles.btnBox}>
          <div
            className={styles.afresh}
            tabIndex={-1}
            role="button"
            onClick={() => {
              onCancel?.(data);
            }}
          >
            取消
          </div>

          <div
            className={styles.afresh}
            tabIndex={-1}
            role="button"
            onClick={() => toDetail(data.id)}
          >
            编辑
          </div>

          <div
            className={styles.afresh}
            tabIndex={-1}
            role="button"
            onClick={() => {
              toPay(data);
            }}
          >
            付款
          </div>
        </div>
      );
    }
  } else if (data.verifyStatus === 10 && data.payStatus === 2) {
    buttons = (
      <div className={styles.btnBox}>
        <div
          className={styles.afresh}
          tabIndex={-1}
          role="button"
          onClick={() => {
            onCancel?.(data);
          }}
        >
          取消
        </div>

        <div
          className={styles.afresh}
          tabIndex={-1}
          role="button"
          onClick={() => {
            toPay(data);
          }}
        >
          付款
        </div>
      </div>
    );
  } else {
    buttons = (
      <div
        className={styles.afresh}
        tabIndex={-1}
        role="button"
        onClick={() => {
          onCancel?.(data);
        }}
      >
        取消
      </div>
    );
  }

  return (
    <Card>
      <div
        className={classNames(styles.title, styles.textBox)}
        role="button"
        tabIndex={-1}
        onClick={() => toDetail(data.id)}
        style={{ marginRight: '-5px' }}
      >
        <div>参团人：{data.name}</div>
        <div className={styles.toDetail}>
          查看详情
          <Icon name="right" size={16} />
        </div>
      </div>
      {data.verifyStatus === 20 && (
        <div className={styles.textBox}>
          <div>团导：</div>
          <div>
            {data.leaderNameList?.map((item) => (
              <span className="mr-1" key={item}>
                {item}
              </span>
            ))}
          </div>
        </div>
      )}
      <div className={styles.textBox}>
        <div>提交时间：</div>
        <div>{dayjs(data.createTime).format('YYYY-MM-DD HH:mm')}</div>
      </div>
      {data.verifyStatus !== 10 && (
        <div className={styles.textBox}>
          <div>审核时间：</div>
          <div>{dayjs(data.verifyTime).format('YYYY-MM-DD HH:mm')}</div>
        </div>
      )}
      <div className={styles.textBox}>
        <div>报名状态：</div>
        <div className={styles.status}>
          {statusLable.current[data.verifyStatus] || <span style={{ color: '#040919' }}>--</span>}
        </div>
      </div>
      {data.verifyStatus !== 20 && (
        <div className={styles.textBox}>
          <div>支付状态：</div>
          <div className={styles.status}>
            {/* @ts-ignored */}
            {data.verifyStatus === 20 && !data.payStatus
              ? '--'
              : payLable.current[data.payStatus] || <span style={{ color: '#040919' }}>--</span>}
          </div>
        </div>
      )}
      {/* {data.verifyStatus === 30 && (
        <div className={styles.textBox}>
          <div style={{ flexShrink: 0, alignSelf: 'flex-start' }}>拒绝理由：</div>
          <div className={styles.refuse}>{data.remark}</div>
        </div>
      )}
      {data.verifyStatus === 40 && (
        <div className={styles.textBox}>
          <div style={{ flexShrink: 0, alignSelf: 'flex-start' }}>取消原因：</div>
          <div className={styles.refuse}>{data.cancelReason}</div>
        </div>
      )} */}
      <Divider style={{ margin: '16px 0 12px' }} />
      <div className={styles.textBox}>
        <div />
        {buttons}
      </div>
    </Card>
  );
}

ApplicationItem.defaultProps = {
  onCancel: undefined,
};

export default ApplicationItem;
