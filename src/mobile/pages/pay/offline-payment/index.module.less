/* stylelint-disable font-family-no-missing-generic-family-keyword */
.payContent {
  width: 100%;
  height: calc(100vh - 70px);
  padding: 16px;
  overflow: auto;
  box-sizing: border-box;
  background: #f5f6fa;

  .payPriceBox {
    display: flex;
    width: 100%;
    height: 92px;
    margin-top: 44px;
    justify-content: center;
    flex-direction: column;
    align-items: center;

    .payPriceText {
      color: #3d3d3d;
      font-family: '苹方-简';
      font-size: 12px;
      margin-bottom: 4px;
    }

    .payPriceValue {
      color: #040919;
      font-family: '苹方-简';
      font-size: 28px;
      font-weight: 500;
    }
  }

  .payForm {
    padding: 16px;
    background: white;
    border-radius: 12px;

    .payMethod {
      display: flex;
      width: 100%;
      height: 50px;
      padding: 10px 0;
      box-sizing: border-box;
      align-items: center;

      .payMethodContent {
        flex: 1;
        display: flex;

        .payMethodLogo {
          width: 18px;
          height: 18px;
          margin-right: 6px;
        }
      }
    }

    .payMethodLabel {
      font-size: 14px;
      padding-bottom: 6px;

      .payMethodLabelRequired {
        color: #ff4d4f;
        font-size: 14px;
        display: inline-block;
        line-height: 1;
        margin-right: 4px;
        font-family: SimSun, sans-serif;
      }
    }

    .payVoucher {
      margin-bottom: 12px;
      padding-bottom: 14px;
      border-bottom: 1px solid #f3f3f3;
    }

    .imageGroup {
      margin-top: 10px;
    }

    .payVoucherDesc {
      color: #888b98;
      font-size: 12px;
      margin-top: 10px;
    }
  }
}

.footer {
  display: flex;
  height: 70px;
  padding: 16px;
  padding-bottom: calc(16px + env(safe-area-inset-bottom));
  justify-content: center;
  box-sizing: border-box;
  align-items: center;

  .payBtn {
    color: white;
    display: flex;
    width: 100%;
    height: 38px;
    justify-content: center;
    align-items: center;
    box-sizing: border-box;
    border-radius: 10px;
    background: linear-gradient(264deg, #00c6ff 0%, #008cff 100%);
  }
}

.payMethodPopupContent {
  .accountCard {
    width: 100%;
    margin-bottom: 18px;
    padding: 16px;
    padding-bottom: 0;
    position: relative;
    background: white;
    border-radius: 12px;

    .accountBank {
      font-size: 15px;
      font-weight: 500;
      display: flex;
      width: 100%;
      height: 44px;
      align-items: center;
      margin-bottom: 10px;

      .accountBankLogo {
        width: 28px;
        height: 28px;
        margin-right: 10px;
      }
    }

    .accountItem {
      display: flex;
      height: 40px;

      .accountItemLabel {
        color: #888b98;
        font-size: 12px;
        width: 60px;
      }

      .accountItemValue {
        color: #040919;
        font-size: 13px;
        width: calc(100% - 60px);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .payMethodPopupHeader {
    color: #040919;
    font-size: 17px;
    font-weight: 500;
    display: flex;
    height: 44px;
    padding-top: 16px;
    justify-content: center;
    align-items: center;
  }

  .payMethodPopupScroll {
    width: 100%;
    height: calc(80vh - 114px);
    padding: 16px;
    padding-bottom: 0;
    overflow: auto;
  }

  .payMethodPopupFooter {
    display: flex;
    width: 100%;
    height: 70px;
    justify-content: center;
    align-items: center;

    .payMethodPopupFooterBtn {
      color: #fff;
      display: flex;
      width: calc(100% - 32px);
      height: 38px;
      justify-content: center;
      background: linear-gradient(264deg, #00c6ff 0%, #008cff 100%);
      align-items: center;
      border-radius: 10px;
    }
  }

  .otherPayMethodBtn {
    display: flex;
    height: 64px;
    padding: 0 16px;
    position: relative;
    align-items: center;
    background: white;
    border-radius: 12px;

    .otherPayMethodBtnImage {
      width: 32px;
      height: 32px;
      margin-right: 6px;
    }
  }

  .activeSvg {
    width: 17px;
    height: 17px;
    position: absolute;
    right: 0;
    bottom: 0;
  }
}
