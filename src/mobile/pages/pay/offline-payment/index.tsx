import { useRef, useState } from 'react';
import { Spin, Form, InputNumber, Input, message, Button } from 'antd';
import { Helmet } from 'react-helmet';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useMount } from 'ahooks';
import { Popup } from 'antd-mobile';
import classNames from 'classnames';
import Icon from '@echronos/echos-icon';
// import { setHeader } from '@/utils/http';
// import { setToken } from '@/utils/auth';
import { paymentUnifiedCashierDesk, getBankCardPayList, paymentUnifiedUnionPay } from '@/apis';
import type { UnifiedCashierDeskResponse, BankCard } from '@/apis';
import { UploadButton } from '@/components/upload';
import { FileGroup } from '@/components';
import useMobileHeader from '../../../hooks/use-mobile-header';
import styles from './index.module.less';

const defaultPayMethodInfo = {
  text: '其他收款方式',
  logo: 'https://img.huahuabiz.com/user_files/1735009700813402035/other-pay-icon.svg',
};

function OfflinePayment() {
  const [params] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const urlParams = useRef<{
    tenantId: string;
    amount: number;
    payNo: string;
  }>({
    tenantId: '',
    amount: 0,
    payNo: '',
  });
  const [payDetail, setPayDetail] = useState<UnifiedCashierDeskResponse>();
  const [bankList, setBankList] = useState<BankCard[]>([]);
  const [payMethodVisible, setPayMethodVisible] = useState(false);
  const [currentPayMethod, setCurrentPayMethod] = useState<BankCard | null>(null);
  const [payMethodInfo, setPayMethodInfo] = useState(defaultPayMethodInfo);
  const [baseParams, setBaseParams] = useState({
    channel: '',
    accountNo: '',
    bankBranchName: '',
  });
  const [form] = Form.useForm();
  const [images, setImages] = useState<string[]>([]);
  const [btnLoading, setBtnLoading] = useState<boolean>(false);

  const { run } = useMobileHeader();

  const getBankList = (companyId: number) => {
    getBankCardPayList({ companyId }).then((res) => {
      const data = res.list || [];
      setBankList(data);
      let channel = '';
      if (data.length) {
        const currentItem = data[0];
        setCurrentPayMethod(currentItem);
        setPayMethodInfo({ text: currentItem.openBankName, logo: currentItem.openBankLogo });
        channel = '银行卡支付';
      } else {
        setPayMethodInfo(defaultPayMethodInfo);
      }
      setBaseParams({
        ...baseParams,
        channel,
        accountNo: '',
        bankBranchName: '',
      });
      form.setFieldsValue({ channel });
    });
  };

  const getPayDeskDetail = () => {
    setLoading(true);
    const { tenantId, amount, payNo } = urlParams.current;
    paymentUnifiedCashierDesk({
      amount,
      orderNos: [payNo],
      tenantId,
      domain: 'app',
      orderType: 0,
    })
      .then((res) => {
        setPayDetail(res);
        form.setFieldsValue({ offlineTransferAmount: res.paymentAmount });
        if (res.receiptInfo.upCompanyId) {
          getBankList(res.receiptInfo.upCompanyId);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onPlay = () => {
    form
      .validateFields()
      // eslint-disable-next-line consistent-return
      .then((value) => {
        if (!images.length) {
          message.error('请上传支付凭证！');
          return;
        }
        const { offlineTransferAmount, voucherNo } = value;
        const payParams = {
          amount: Number(payDetail?.paymentAmount || 0),
          payAmount: offlineTransferAmount ? Number(offlineTransferAmount) : 0,
          payOrderNo: payDetail?.payOrderNo || '',
          payMode: 'OFFLINE_TRANSFER',
          payWay: 'OFFLINE_TRANSFER',
          voucherUrls: images,
          voucherNo,
          domain: 'app',
          ...baseParams,
          ...(currentPayMethod ? { bankBranchId: currentPayMethod.id } : {}),
        };

        setBtnLoading(true);
        paymentUnifiedUnionPay(payParams)
          .then(() => {
            navigate('/m/pay/pay-success');
          })
          .finally(() => {
            setBtnLoading(false);
          });
      });
  };

  // 进入页面
  useMount(() => {
    const token = params.get('token');
    if (token) {
      run(true);
    }

    urlParams.current = {
      tenantId: params.get('tenantId') || '',
      amount: Number(params.get('amount') || 0),
      payNo: params.get('payNo') || '',
    };
    getPayDeskDetail();
  });

  return (
    <div className={styles.container}>
      <Helmet>
        <title>线下支付</title>
      </Helmet>
      <Spin spinning={loading}>
        <div className={styles.payContent}>
          <div className={styles.payPriceBox}>
            <div className={styles.payPriceText}>支付金额</div>
            <div className={styles.payPriceValue}>￥{payDetail?.receiptAmount}</div>
          </div>
          <div className={styles.payForm}>
            <Form form={form} colon={false} layout="vertical">
              <Form.Item noStyle>
                <div className={styles.payMethodLabel}>
                  <span className={styles.payMethodLabelRequired}>*</span>收款方式选择：
                </div>
                <div
                  className={styles.payMethod}
                  role="presentation"
                  onClick={() => {
                    setPayMethodVisible(true);
                  }}
                >
                  <div className={styles.payMethodContent}>
                    {payMethodInfo.logo && (
                      <img src={payMethodInfo.logo} className={styles.payMethodLogo} alt="" />
                    )}
                    {payMethodInfo.text}
                  </div>
                  <Icon name="right_arrow_line" size={16} style={{ marginRight: 8 }} />
                </div>
              </Form.Item>
              <Form.Item
                label="线下转账金额："
                name="offlineTransferAmount"
                rules={[
                  {
                    required: true,
                    message: '请输入金额',
                  },
                ]}
              >
                <InputNumber
                  disabled
                  controls={false}
                  prefix="￥"
                  placeholder="请输入支付金额"
                  style={{ width: '100%' }}
                  min="0"
                  max={payDetail?.paymentAmount}
                  precision={2}
                />
              </Form.Item>
              <Form.Item
                label="支付凭证单号："
                name="voucherNo"
                rules={[
                  {
                    required: true,
                    message: '请输入支付凭证单号',
                  },
                  {
                    pattern: /^[a-zA-Z0-9]+$/,
                    message: '只能输入数字和英文大小写字母',
                  },
                ]}
                getValueFromEvent={(e) => e.target.value.replace(/(^s*)|(s*$)/g, '')}
              >
                <Input autoComplete="off" placeholder="请输入支付凭证单号" maxLength={32} />
              </Form.Item>
              <Form.Item label="支付渠道：" name="channel">
                <Input
                  autoComplete="off"
                  placeholder="请输入支付渠道"
                  disabled={currentPayMethod === null}
                  maxLength={32}
                />
              </Form.Item>
              <Form.Item
                label="付款账号："
                name="accountNo"
                rules={[
                  {
                    pattern: /^[a-zA-Z0-9]+$/,
                    message: '只能输入数字和英文大小写字母',
                  },
                ]}
                getValueFromEvent={(e) => e.target.value.replace(/(^s*)|(s*$)/g, '')}
              >
                <Input autoComplete="off" placeholder="请输入付款账号" maxLength={32} />
              </Form.Item>
              <Form.Item label="开户支行：" name="bankBranchName">
                <Input autoComplete="off" placeholder="请输入开户支行" maxLength={32} />
              </Form.Item>
              <Form.Item noStyle>
                <div className={classNames(styles.payMethodLabel, styles.payVoucher)}>
                  <span className={styles.payMethodLabelRequired}>*</span>支付凭证
                </div>
                {images.length < 5 && (
                  <UploadButton
                    maxCount={5}
                    accept="image/*"
                    onChange={(e) => {
                      if (e.status === 'success') {
                        setImages([...images, e.file.url]);
                      }
                    }}
                  />
                )}
                <div className={styles.imageGroup}>
                  <FileGroup
                    value={images}
                    listType="card"
                    rootClassName={styles.images}
                    onChange={(values) => {
                      setImages(values.map((item) => item.url));
                    }}
                  />
                </div>
                <div className={styles.payVoucherDesc}>
                  支付凭证不能大于5M,最多可上传5张支持线下转账凭证、票据凭证、银行承兑汇票
                </div>
              </Form.Item>
            </Form>
          </div>
        </div>
        <div className={styles.footer}>
          <Button
            type="primary"
            className={styles.payBtn}
            loading={btnLoading}
            onClick={() => {
              onPlay();
            }}
          >
            去支付
          </Button>
        </div>
      </Spin>
      <Popup
        visible={payMethodVisible}
        onClose={() => {
          setPayMethodVisible(false);
        }}
        bodyStyle={{
          height: '80vh',
          background: '#F5F6FA',
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
        }}
        position="bottom"
        showCloseButton
      >
        <div className={styles.payMethodPopupContent}>
          <div className={styles.payMethodPopupHeader}>收款方收款方式选择</div>
          <div className={styles.payMethodPopupScroll}>
            <div className={styles.card}>
              {bankList?.map((item) => (
                <div
                  className={styles.accountCard}
                  role="presentation"
                  onClick={() => {
                    setCurrentPayMethod(item);
                  }}
                >
                  <div className={styles.accountBank}>
                    {item.openBankLogo && (
                      <img src={item.openBankLogo} className={styles.accountBankLogo} alt="" />
                    )}
                    {item.openBankName}
                  </div>
                  <div className={styles.accountItem}>
                    <div className={styles.accountItemLabel}>账户名</div>
                    <div className={styles.accountItemValue}>{item.bankAccountName}</div>
                  </div>
                  <div className={styles.accountItem}>
                    <div className={styles.accountItemLabel}>账号</div>
                    <div className={styles.accountItemValue}>{item.bankAccountNumber}</div>
                  </div>
                  <div className={styles.accountItem}>
                    <div className={styles.accountItemLabel}>开户支行</div>
                    <div className={styles.accountItemValue}>{item.branchName}</div>
                  </div>
                  {currentPayMethod?.id === item.id && (
                    <img
                      alt=""
                      className={styles.activeSvg}
                      src="https://img.huahuabiz.com/user_files/1735029346536279257/active-*************.svg"
                    />
                  )}
                </div>
              ))}
              <div
                className={styles.otherPayMethodBtn}
                role="presentation"
                onClick={() => {
                  setCurrentPayMethod(null);
                }}
              >
                <img
                  alt=""
                  className={styles.otherPayMethodBtnImage}
                  src="https://img.huahuabiz.com/user_files/1735009700813402035/other-pay-icon.svg"
                />
                <div>其他收款方式</div>
                {!currentPayMethod && (
                  <img
                    alt=""
                    className={styles.activeSvg}
                    src="https://img.huahuabiz.com/user_files/1735029346536279257/active-*************.svg"
                  />
                )}
              </div>
            </div>
          </div>
          <div className={styles.payMethodPopupFooter}>
            <div
              className={styles.payMethodPopupFooterBtn}
              role="presentation"
              onClick={() => {
                setPayMethodVisible(false);
                let channel = '';
                if (currentPayMethod) {
                  setPayMethodInfo({
                    text: currentPayMethod.openBankName,
                    logo: currentPayMethod.openBankLogo,
                  });
                  channel = '银行卡支付';
                } else {
                  setPayMethodInfo(defaultPayMethodInfo);
                }
                setBaseParams({
                  ...baseParams,
                  channel,
                });
                form.setFieldsValue({ channel });
              }}
            >
              确定
            </div>
          </div>
        </div>
      </Popup>
    </div>
  );
}

export default OfflinePayment;
