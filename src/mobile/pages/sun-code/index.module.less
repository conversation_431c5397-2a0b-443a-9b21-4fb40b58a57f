.container {
  width: 100%;
  padding-top: 52px;
  overflow: auto;
  position: absolute;
  top: 0;
  bottom: 0;
  background: #f5f6fa;
  user-select: none;
}

.tabCard {
  display: flex;
  height: 60px;
  margin-bottom: 16px;
  padding: 16px;
  justify-content: space-between;
  align-items: center;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 12px;

  .noTitleTabs {
    display: flex;
    width: 130px;
    height: 28px;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    background-color: #f5f6fa;
  }

  .selectTitleTabs {
    border: 1px solid #008cff;
    color: #008cff;
    background-color: #d9eeff;
  }
}

.tabBox {
  display: flex;
  height: 44px;
  justify-content: space-around;
  align-items: center;

  .selectTabs {
    color: #008cff;
    display: flex;
    justify-content: space-evenly;
    flex-direction: column;
    align-items: center;
    cursor: pointer;

    .line {
      width: 20px;
      height: 4px;
      border-radius: 4px;
      background-color: #008cff;
    }
  }

  .noTabs {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: center;
    cursor: pointer;

    .line {
      width: 20px;
      height: 4px;
      margin-top: 4px;
    }
  }
}

.cardWrap {
  padding: 0 16px;
  // margin-bottom: 24px;
}

.sunCodeBox {
  height: 365px;
  padding: 20px 22px;
  background: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.cover {
  width: 350px;
  height: 30px;
  background-color: #f5f6fa;
  position: relative;
  z-index: 999;
}

.buttonBox {
  display: flex;
  padding: 20px 45px;
  justify-content: space-between;
  align-items: center;
  text-align: center;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.btnImg {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-bottom: 8px;
}

.textOne {
  font-size: 18px;
  font-weight: bolder;
  margin-bottom: 4px;
}

.textTwo {
  color: #888b98;
  font-size: 15px;
}

.sunCodeImgBox {
  display: flex;
  margin-top: 16px;
  margin-bottom: 12px;
  justify-content: center;

  .sunCodeImg {
    width: 180px;
    height: 180px;
  }
}

.textThree {
  color: #040919;
  font-size: 15px;
  text-align: center;
  margin-bottom: 4px;
}

.textFour {
  color: #888b98;
  font-size: 15px;
  text-align: center;
}

.canvasBox {
  width: 320px;
  height: 319px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  position: relative;
  z-index: 1;
}

.canvasBox.low {
  z-index: -1;
}

.centerBox {
  display: flex;
  margin-bottom: 16px;
  justify-content: center;
}

.customCanvasBox {
  width: 251px;
  height: 483px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  position: relative;
  z-index: 1;
}

.customCanvasBox.low {
  z-index: -1;
}

.bottomBar {
  width: 100vw;
  position: fixed;
  bottom: 0;
  left: 0;
  background-color: white;
}

.empty {
  margin-top: 50px;
}
