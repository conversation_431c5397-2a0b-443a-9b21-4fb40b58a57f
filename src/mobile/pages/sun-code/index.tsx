/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useRef, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Spin } from 'antd';
import { TabBar } from 'antd-mobile';
import { createUuid, base64ToBlob } from '@/utils/utils';
import { generateUploadFile } from '@/utils/file';
import { getToken } from '@/utils/auth';
import { useMount } from 'ahooks';
import { Empty } from '@/components';
import classNames from 'classnames';
import {
  getRecruitSunCode,
  getFaceSunCode,
  postUserInviteRecord,
  getUserCurrentUserInfo,
  getShowRecruit,
  RecruitInfoType,
  getInviteCustomQrcode,
  getEnterNeedPayFee,
  getMemberPermissions,
  getIsDistribution,
} from '@/apis';
import { useDistributionBaseInfo } from '@@/distribution/hooks/index';
import useMobileHeader from '../../hooks/use-mobile-header';
import Head from '../../components/head/head';
import styles from './index.module.less';

const tabList = [
  {
    id: 1,
    appraiseStatus: 1,
    title: '招募海报',
  },
  {
    id: 2,
    appraiseStatus: 2,
    title: '面对面招募',
  },
];

const titletabList = [
  {
    id: 3,
    appraiseStatus: 1,
    title: '邀请成为客户',
  },
  {
    id: 4,
    appraiseStatus: 2,
    title: `邀请成为分销商`,
  },
];

declare let wx: any;
function SunCode() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams({ headHeight: '44' });
  const [isWx, setIsWx] = useState(false);
  const [selectTitleTab, setSelectTitleTab] = useState(2);
  const [selectTab, setSelectTab] = useState(1);
  const [isMount, setMount] = useState(false);
  const [loading, setLoading] = useState(true);
  const [faceCode, setFaceCode] = useState('');
  const [jumpLoading, setJumpLoading] = useState(false);
  const [recruitInfo, setRecruitInfo] = useState({} as RecruitInfoType);
  // const [isYueAnJu, setIsYueAnJu] = useState(false);
  const [hasPerm, setHasPerm] = useState(false);
  const [yueAnJuInvDisEnt, setYueAnJuInvDisEnt] = useState(false);

  const env = searchParams.get('env') || 'wx';
  const headHeight = Number(searchParams.get('headHeight')) || 44;
  const isApp = searchParams.get('isApp') || false;
  const isTransferCome = searchParams.get('isTransferCome') || false;
  const isIdInCurrentTenant = searchParams.get('isIdInCurrentTenant') || false;
  const jumpTo = searchParams.get('jumpTo');
  const appId = searchParams.get('appId') || '';
  const disCompanyId = searchParams.get('disCompanyId') || '';
  const tenantId = searchParams.get('tenantId') || '';
  const showFaceRecruit = searchParams.get('showFaceRecruit');
  const isShowInviteCustom = Number(searchParams.get('isShowInviteCustom'));

  const companyNameLineFeedNum = useRef(1);
  const cusQRCode = useRef('');
  window.console.log(isShowInviteCustom, '是否展示邀请成为客户');

  const { run } = useMobileHeader();

  const customCanvasRef = useRef<HTMLCanvasElement>(null);
  const previewCanvasRef = useRef<HTMLCanvasElement>(null);

  const { data: baseConfigInfo, run: getDisBaseConfigInfo } = useDistributionBaseInfo();
  const defineDisNameValue = useMemo(
    () => baseConfigInfo?.defineDisName || '分销商',
    [baseConfigInfo?.defineDisName]
  );

  const titletabListMome = useMemo(() => {
    if (!yueAnJuInvDisEnt) {
      return titletabList.filter((item) => item.appraiseStatus === 1);
    }
    // if (isYueAnJu) {
    return titletabList.map((item) => {
      if (item.appraiseStatus === 2) {
        return {
          ...item,
          title: `邀请成为${defineDisNameValue}`,
        };
      }
      return item;
    });
    // }
    // return titletabList;
  }, [defineDisNameValue, yueAnJuInvDisEnt]);

  // 文本换行
  const drawMultilineText = (
    ctx: any,
    text: string,
    x: number,
    y: number,
    maxWidth: number,
    lineHeight = 16
  ) => {
    const words = text.split('');
    let line = '';
    let row = 1; // 初始化行计数器

    for (let n = 0; n < words.length; n += 1) {
      const testLine = `${line + words[n]} `;
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;
      if (testWidth > maxWidth && n > 0) {
        ctx.fillText(line, x, y + row * lineHeight); // 绘制当前行
        line = `${words[n]} `;
        row += 1; // 当换行时增加计数器
      } else {
        line = testLine;
      }
    }
    companyNameLineFeedNum.current = row;
    ctx.fillText(line, x, y + row * lineHeight); // 绘制最后一行
    return row + 1; // 返
  };

  // 绘制邀请成为客户的小程序码
  const drawCustomCanvas = (
    posterImg: string,
    sunCodeImg: string,
    inviteName: string
    // isYAJ = false
  ) => {
    const canvas: any = document.getElementById('customCanvas');
    const upImg = new Image();
    const downImg = new Image();

    if (canvas?.getContext) {
      const ctx = canvas.getContext('2d');
      const devicePixelRatio = window.devicePixelRatio || 1;
      const backingStoreRatio = ctx.webkitBackingStorePixelRatio || 1;
      const ratio = devicePixelRatio / backingStoreRatio;
      canvas.width = 251 * ratio; // 实际渲染像素
      canvas.height = 483 * ratio; // 实际渲染像素
      canvas.style.width = '251px'; // 控制显示大小
      canvas.style.height = '483px'; // 控制显示大小
      ctx.scale(ratio, ratio);

      ctx.beginPath();
      ctx.clearRect(0, 0, 251, 483);
      ctx.rect(0, 0, 251, 482);
      ctx.strokeStyle = '#ffffff';
      ctx.fillStyle = '#ffffff';
      ctx.fill();

      upImg.onload = () => {
        ctx.drawImage(upImg, 0, 0, 251, 354);
      };
      upImg.src = `data:image/png;base64,${posterImg}`;

      downImg.onload = () => {
        ctx.drawImage(downImg, 12, 366, 86, 86);
      };
      downImg.src = `data:image/png;base64,${sunCodeImg}`;
      cusQRCode.current = `data:image/png;base64,${sunCodeImg}`;

      ctx.font = '10px PingFangSC-Medium';
      ctx.fillStyle = '#040919';
      drawMultilineText(ctx, inviteName, 115, 396, 120, 12);

      // ctx.fillText(`${inviteName}`, 115, 406);
      ctx.closePath();

      // if (!isYAJ) {
      //   ctx.font = '10px PingFangSC-Medium';
      //   ctx.fillStyle = '#040919';
      //   ctx.fillText(`邀请你成为他的客户`, 115, 410 + companyNameLineFeedNum.current * 16);
      //   ctx.closePath();
      // }

      ctx.font = '10px PingFangSC-Medium';
      ctx.fillStyle = '#888b98';
      ctx.fillText(
        '长按或扫码查看',
        115,
        // 410 + companyNameLineFeedNum.current * 16 + (isYAJ ? 0 : 16)
        410 + companyNameLineFeedNum.current * 16 + 16
      );
      ctx.closePath();
    }
  };

  // 绘制邀请成为分销商的小程序码
  const drawCanvas = (dataImg: string, isValue: boolean) => {
    const canvas: any = document.getElementById('myCanvas');
    const img = new Image();
    if (canvas?.getContext) {
      const ctx = canvas.getContext('2d');
      const devicePixelRatio = window.devicePixelRatio || 1;
      const backingStoreRatio = ctx.webkitBackingStorePixelRatio || 1;
      const ratio = devicePixelRatio / backingStoreRatio;
      canvas.width = 320 * ratio; // 实际渲染像素
      canvas.height = 319 * ratio; // 实际渲染像素
      canvas.style.width = '320px'; // 控制显示大小
      canvas.style.height = '319px'; // 控制显示大小
      ctx.scale(ratio, ratio);

      ctx.beginPath();
      ctx.clearRect(0, 0, 320, 319);
      ctx.rect(0, 0, 320, 319);
      ctx.strokeStyle = '#ffffff';
      ctx.fillStyle = '#ffffff';
      ctx.fill();

      ctx.font = '18px PingFangSC-Medium';
      ctx.fillStyle = '#040919';
      ctx.fillText(isValue ? '和我一起加入' : '和我一起加入分销', 16, 25);

      ctx.font = '15px PingFangSC-Medium';
      ctx.fillStyle = '#888b98';
      ctx.fillText('推广优质产品，轻松创造财富', 16, 50);

      img.onload = () => {
        ctx.drawImage(img, 70, 70, 180, 180);
      };
      img.src = `data:image/png;base64,${dataImg}`;
      ctx.font = '15px PingFangSC-Medium';
      ctx.fillStyle = '#888b98';
      ctx.fillText(`扫码/长按 立即成为${isValue ? '服务商' : '分销商'}`, 65, 299);
      ctx.closePath();
    }
  };

  const onChangeTitleTabs = (tab: number) => {
    setSelectTitleTab(tab);
    setLoading(true);
    if (tab === 1) {
      setTimeout(() => {
        // 邀请成为客户
        getInviteCustomQrcode({ appId }).then((res) => {
          // drawCustomCanvas(res.backgroundImgStr, res.qrcodeImgStr, res.inviteComName, isYAJ);
          drawCustomCanvas(res.backgroundImgStr, res.qrcodeImgStr, res.inviteComName);
          setLoading(false);
        });
      }, 500);
    }
    if (tab === 2) {
      // 此接口用于是否展示面对面招募
      getShowRecruit()
        .then((res) => {
          setRecruitInfo(res);
          if (!res.isScanCodeInviteAudit) {
            tabList.forEach((item, index) => {
              if (item.appraiseStatus === 2) {
                tabList.splice(index, 1);
              }
            });
          }
        })
        .finally(() => {
          if (showFaceRecruit === 'false') {
            tabList.forEach((item, index) => {
              if (item.appraiseStatus === 2) {
                tabList.splice(index, 1);
              }
            });
          }
        });
      // 招募海报
      setTimeout(() => {
        getUserCurrentUserInfo().then((succe) => {
          postUserInviteRecord().then((result) => {
            // 招募海报
            getRecruitSunCode({
              inviterMemberId: succe.memberId,
              inviterUserId: succe.userId,
              type: 1,
              inviteRecordId: result.inviteRecordId,
              qrCodeSource: 0,
              appId,
              tenantId,
            }).then((res) => {
              getEnterNeedPayFee({ tenantId }).then((it) => {
                drawCanvas(res.imgBase64Str, it.flag);
                setLoading(false);
              });
            });
            // 面对面招募
            getFaceSunCode({
              inviterMemberId: succe.memberId,
              inviterUserId: succe.userId,
              type: 1,
              inviteRecordId: result.inviteRecordId,
              qrCodeSource: 1,
              appId,
              tenantId,
            }).then((res) => {
              setFaceCode(`data:image/png;base64,${res.imgBase64Str}`);
            });
          });
        });
      }, 500);
    }
  };

  // 切换tab
  const onChangeTabs = (tab: number) => {
    setSelectTab(tab);
  };

  // 立即分享和保存海报
  const onShareSave = (type: number) => {
    if (jumpLoading) return;
    setJumpLoading(true);
    if (selectTitleTab === 1) {
      if (type === 4 && cusQRCode.current) {
        const blob = base64ToBlob(cusQRCode.current);
        const file = new File([blob], `${createUuid}.png`);
        generateUploadFile(file, false)
          .upload()
          .then((res) => {
            window.console.log(res, 'res');
            wx.miniProgram.navigateTo({
              url: `/subpackages/other/pages/share-page/index?type=${2}&poster=${res.url}`,
            });
            // 用来解决跳转报错的问题
            setTimeout(() => {
              setJumpLoading(false);
            }, 1000);
          });
      } else {
        customCanvasRef.current?.toBlob((blob) => {
          if (blob) {
            const file = new File([blob], `${createUuid}.png`);
            generateUploadFile(file, false)
              .upload()
              .then((res) => {
                window.console.log(res, 'res');
                wx.miniProgram.navigateTo({
                  url: `/subpackages/other/pages/share-page/index?type=${type}&poster=${res.url}`,
                });
                // 用来解决跳转报错的问题
                setTimeout(() => {
                  setJumpLoading(false);
                }, 1000);
              });
          }
        });
      }
    }
    if (selectTitleTab === 2) {
      previewCanvasRef.current?.toBlob((blob) => {
        if (blob) {
          const file = new File([blob], `${createUuid}.png`);
          generateUploadFile(file, false)
            .upload()
            .then((res) => {
              wx.miniProgram.navigateTo({
                url: `/subpackages/other/pages/share-page/index?type=${type}&poster=${res.url}`,
              });
              // 用来解决跳转报错的问题
              setTimeout(() => {
                setJumpLoading(false);
              }, 1000);
            });
        }
      });
    }
  };

  // 返回
  const back = () => {
    if (isTransferCome && env === 'wx') {
      wx.miniProgram.navigateBack({
        delta: 1,
      });
      return;
    }
    window.history.back();
  };

  useMount(async () => {
    const token = searchParams.get('token') || getToken();
    if (token) {
      run();
    }
    if (tenantId) {
      getDisBaseConfigInfo(tenantId);
    }
    const { isDis, showYueAnJuInvDisEnt } = await getIsDistribution();
    window.console.log('getIsDistribution isDis,showYueAnJuInvDisEnt', isDis, showYueAnJuInvDisEnt);
    setYueAnJuInvDisEnt(showYueAnJuInvDisEnt);
    if (isDis) {
      setHasPerm(true);
    } else {
      const res = await getMemberPermissions();
      // window.console.log(
      //   'getMemberPermissions',
      //   res.list.filter((item) => item.permCode === 'AZ_001_001')
      // );
      setHasPerm(
        res.list.some(
          (item) => item.permCode === 'AZ_001_001' && item.isCharge && item.isPermission
        )
      );
    }

    // const result = await getEnterNeedPayFee({ tenantId });
    // setIsYueAnJu(result.yajFlag);

    // onChangeTitleTabs(isShowInviteCustom ? 1 : 2, result.yajFlag);
    onChangeTitleTabs(isShowInviteCustom ? 1 : 2);

    document.title = ' ';
    if (isApp) {
      setIsWx(true);
    } else {
      setTimeout(() => {
        wx.miniProgram.getEnv((res: { miniprogram: boolean }) => {
          setIsWx(res.miniprogram);
        });
      });
    }

    setTimeout(() => {
      // 解决ios滚动不了的问题
      setMount(true);
    }, 300);
  });

  const HelmetElement = (
    <Helmet>
      <title>邀请成为{defineDisNameValue}</title>
    </Helmet>
  );

  const dom = () => (
    <div
      className={styles.container}
      style={{
        paddingTop: isWx ? '8px' : `${headHeight + 8}px`,
      }}
    >
      {!isWx && <Head title={`邀请成为${defineDisNameValue}`} back={back} />}
      <Spin spinning={loading}>
        {/* 邀请成为客户和分销商的tab  */}
        {isShowInviteCustom ? (
          <div className={styles.cardWrap}>
            <div className={styles.tabCard}>
              {titletabListMome.map((item) => (
                <div
                  key={item.id}
                  tabIndex={-1}
                  role="button"
                  className={classNames(
                    styles.noTitleTabs,
                    item.appraiseStatus === selectTitleTab && styles.selectTitleTabs
                  )}
                  // onClick={() => onChangeTitleTabs(item.appraiseStatus, isYueAnJu)}
                  onClick={() => onChangeTitleTabs(item.appraiseStatus)}
                >
                  <div>{item.title}</div>
                </div>
              ))}
            </div>
          </div>
        ) : null}

        {selectTitleTab === 1 && (
          <div>
            <div className={styles.cardWrap}>
              <div className={styles.centerBox}>
                <canvas
                  id="customCanvas"
                  ref={customCanvasRef}
                  width="251"
                  height="483"
                  className={styles.customCanvasBox}
                />
              </div>
            </div>
            <div className={styles.cardWrap}>
              <div className={styles.buttonBox}>
                <div tabIndex={-1} role="button" onClick={() => onShareSave(2)}>
                  <img
                    src="https://img.huahuabiz.com/user_files/20240827/1724750856791680.png"
                    className={styles.btnImg}
                    alt=""
                  />
                  <div>立即分享</div>
                </div>
                <div tabIndex={-1} role="button" onClick={() => onShareSave(4)}>
                  <img
                    src="https://img.huahuabiz.com/user_files/20240827/1724750812644237.png"
                    className={styles.btnImg}
                    alt=""
                  />
                  <div>保存小程序码</div>
                </div>
                <div tabIndex={-1} role="button" onClick={() => onShareSave(3)}>
                  <img
                    src="https://img.huahuabiz.com/user_files/2023517/1684310227674534.png"
                    className={styles.btnImg}
                    alt=""
                  />
                  <div>保存海报</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {selectTitleTab === 2 ? (
          <div>
            {/* tab栏切换 */}
            <div className={styles.tabBox}>
              {tabList.map((item) => (
                <div
                  key={item.id}
                  tabIndex={-1}
                  role="button"
                  className={classNames(
                    styles.noTabs,
                    item.appraiseStatus === selectTab && styles.selectTabs
                  )}
                  onClick={() => onChangeTabs(item.appraiseStatus)}
                >
                  <div>{item.title}</div>
                  <div className={styles.line} />
                </div>
              ))}
            </div>
            {hasPerm ? (
              <>
                {/* 招募海报-太阳码 */}
                <div
                  style={{ display: selectTab === 2 ? 'none' : 'block' }}
                  className={styles.cardWrap}
                >
                  <canvas
                    id="myCanvas"
                    ref={previewCanvasRef}
                    width="320"
                    height="319"
                    className={styles.canvasBox}
                  />
                </div>
                {/* 招募海报-按钮 */}
                {selectTab === 1 ? <div className={styles.cover} /> : null}
                <div className={styles.cardWrap}>
                  {selectTab === 1 && (
                    <div className={styles.buttonBox}>
                      <div tabIndex={-1} role="button" onClick={() => onShareSave(2)}>
                        <img
                          src="https://img.huahuabiz.com/user_files/2023517/1684310230384281.png"
                          className={styles.btnImg}
                          alt=""
                        />
                        <div>立即分享</div>
                      </div>
                      <div tabIndex={-1} role="button" onClick={() => onShareSave(3)}>
                        <img
                          src="https://img.huahuabiz.com/user_files/2023517/1684310227674534.png"
                          className={styles.btnImg}
                          alt=""
                        />
                        <div>保存海报</div>
                      </div>
                    </div>
                  )}
                </div>
                {/* 面对面招募-太阳码 */}
                <div className={styles.cardWrap}>
                  {selectTab === 2 && (
                    <div className={styles.sunCodeBox}>
                      <div className={styles.textOne}>和我一起加入分销</div>
                      <div className={styles.textTwo}>推广优质产品，轻松创造财富</div>
                      <div className={styles.sunCodeImgBox}>
                        <img src={faceCode} className={styles.sunCodeImg} alt="" />
                      </div>
                      <div className={styles.textThree}>面对面出示该二维码给您的好友</div>
                      <div className={styles.textFour}>
                        通过微信扫一扫，即可快速与他绑定分销关系，无需审核操作
                      </div>
                    </div>
                  )}
                </div>
              </>
            ) : (
              <Empty
                message={loading ? '加载中' : '暂无相关权限'}
                description={loading ? '' : '请联系公司管理员开通'}
                className={styles.empty}
              />
            )}
          </div>
        ) : null}

        {/* 底部tab栏 */}
        <div>
          {isIdInCurrentTenant && !recruitInfo.isRecruitSub ? null : (
            <div>
              <div style={{ height: '100px' }} />
              <div className={styles.bottomBar}>
                <TabBar
                  activeKey="friend"
                  safeArea
                  onChange={(val) => {
                    if (val === 'apply') {
                      navigate(
                        `/m/m-distribution/apply?appId=${appId}&tenantId=${tenantId}&disCompanyId=${disCompanyId}&companyNature=${
                          searchParams.get('companyNature') || ''
                        }&companyName=${searchParams.get('companyName') || ''}&userName=${
                          searchParams.get('userName') || ''
                        }`,
                        {
                          replace: true,
                        }
                      );
                    } else {
                      navigate(
                        `/m/m-distribution/index?appId=${appId}&tenantId=${tenantId}&isTabBar=true&disCompanyId=${disCompanyId}`,
                        {
                          replace: true,
                        }
                      );
                    }
                  }}
                >
                  <TabBar.Item
                    key={jumpTo === 'apply' ? 'apply' : 'myReport'}
                    icon={
                      <img
                        src={
                          jumpTo === 'apply'
                            ? 'https://img.huahuabiz.com/user_files/1685956902626526721/apply-ic-def.svg'
                            : 'https://img.huahuabiz.com/user_files/1685956811034753876/report-i-def.svg'
                        }
                        alt=""
                      />
                    }
                    title={jumpTo === 'apply' ? '申请' : '我的报表'}
                  />
                  <TabBar.Item
                    key="friend"
                    icon={
                      <img
                        src="https://img.huahuabiz.com/user_files/1685956950445391227/invite-i-a.svg"
                        alt=""
                      />
                    }
                    title="邀请好友"
                  />
                </TabBar>
              </div>
            </div>
          )}
        </div>
      </Spin>
      {HelmetElement}
    </div>
  );

  return isMount ? dom() : null;
}

export default SunCode;
