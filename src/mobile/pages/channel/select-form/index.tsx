import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet';
import { Radio, Button } from 'antd';
import { useRequest } from 'ahooks';
import { iformEnableList, IformEnableListResult } from '@/apis';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Empty } from '@/components';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

function SelectForm() {
  const { t } = useTranslation();
  const [params] = useSearchParams();
  const navigator = useNavigate();
  const formCode = params.get('formCode') || '';
  const name = params.get('name') || '';
  const [currentCodeInfo, setCurrentCodeInfo] = useState({
    formCode: '',
    name: '',
  });
  const [list, setList] = useState<IformEnableListResult[]>([]);

  const { run } = useRequest(iformEnableList, {
    manual: true,
    onSuccess: (res) => {
      setList(res.list);
    },
  });

  const onChangeCode = (val: string) => {
    const codeInfo = list.find((item) => item.formCode === val);
    setCurrentCodeInfo({
      formCode: val,
      name: codeInfo?.name || '',
    });
  };

  const onNavPreview = (code: string) => {
    navigator(`/m/select/form/preview?formCode=${code}`);
    localStorage.setItem('CURRENT_FORM_CODE', JSON.stringify(currentCodeInfo));
  };

  const onSubmit = () => {
    navigator(-1);
    localStorage.setItem('CURRENT_FORM_CODE', JSON.stringify(currentCodeInfo));
  };

  useEffect(() => {
    run({ type: 5 });
  }, [run]);

  useEffect(() => {
    const storageCode = localStorage.getItem('CURRENT_FORM_CODE');
    if (storageCode) {
      setCurrentCodeInfo(JSON.parse(storageCode));
      return;
    }
    setCurrentCodeInfo({
      formCode,
      name,
    });
  }, [formCode, name]);

  return (
    <div className={styles.page}>
      <Helmet>
        <title>{t('channel_select_join_form')}</title>
      </Helmet>
      <div className={styles.content}>
        {list && !!list.length ? (
          <Radio.Group
            value={currentCodeInfo.formCode}
            className={styles.group}
            onChange={(e) => onChangeCode(e.target.value)}
          >
            {list.map((item) => (
              <Radio key={item.formCode} value={item.formCode} className={styles.item}>
                <div className={styles.itemContent}>
                  <img
                    className={styles.itemImg}
                    src="https://img.huahuabiz.com/user_files/2024122/1733102225750518.png"
                    alt=""
                  />
                  <span className={styles.itemName}>{item.name}</span>
                  <span
                    role="button"
                    tabIndex={0}
                    className={styles.preview}
                    onClick={(e) => {
                      e.stopPropagation();
                      onNavPreview(item.formCode);
                    }}
                  >
                    {t('channel_preview')}
                  </span>
                </div>
              </Radio>
            ))}
          </Radio.Group>
        ) : (
          <div className={styles.noData}>
            <Empty />
          </div>
        )}
      </div>
      <div className={styles.footer}>
        <Button type="primary" onClick={onSubmit} disabled={!currentCodeInfo.formCode}>
          {t('channel_confirm')}
        </Button>
      </div>
    </div>
  );
}

export default SelectForm;
