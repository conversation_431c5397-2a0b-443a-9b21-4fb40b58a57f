@import 'styles/mixins/mixins';

.page {
  color: #040919;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: #f5f6fa;
}

.content {
  padding: 16px 16px 0;
  flex: 1;
  overflow: auto;
}

.group {
  width: 100%;
}

.item {
  display: flex;
  width: 100%;
  margin-right: 0 !important;
  margin-bottom: 16px !important;
  padding: 16px !important;
  align-items: center;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 5.88%);
  background-color: #fff;
  border-radius: 12px;

  & span:nth-child(2) {
    flex: 1;
    display: block;
  }
}

.itemContent {
  display: flex;
  align-items: center;
  flex: 1;
}

.itemName {
  flex: 1;
  .text-overflow(1);

  font-weight: 600;
}

.itemImg {
  width: 36px;
  height: 36px;
  margin-right: 8px;
}

.preview {
  color: #008cff;
  font-size: 12px;
}

.footer {
  padding: 16px;

  :global {
    .ant-btn {
      width: 100%;
    }
  }
}

.noData {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}
