.body {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f6fa;
}

.content {
  padding: 16px;
  overflow: auto;
  flex: 1;
}

.from {
  background-color: #fff;
  border-radius: 10px;
  padding: 16px 16px 0;
  overflow: hidden;
}

.selectBox {
  display: flex;
  margin-bottom: 16px;
  align-items: center;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important; /* 防止ios 出现点击div 出现选中效果 */
}

.footer {
  padding: 20px 16px;

  :global {
    .ant-btn {
      width: 100%;
    }
  }
}

.label {
  width: 70px;
  margin-right: 16px;
}

.select {
  flex: 1;
  overflow: hidden;
}

:global .ant-cascader-dropdown {
  max-width: 100%;
}

:global .ant-select-item-option-content {
  & > div {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

:global {
  .ant-drawer {
    .ant-drawer-content-wrapper {
      .ant-drawer-content {
        background-color: #f5f6fa;
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;

        .ant-drawer-body {
          padding: 0 16px;
        }
      }
    }
  }
}

.checkCircleIcon {
  width: 18px;
  margin-right: 4px;
}

.companyList {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .companyName {
    display: inline-block;
    width: 80%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}

.noCompanyBox {
  color: #888b98;
  height: 100px;
  padding-top: 25px;
  background-color: #fff;
  text-align: center;
}

.createCompanyBox {
  color: #fff;
  display: flex;
  height: 30px;
  justify-content: center;
  background: linear-gradient(262deg, #00c6ff 0%, #008cff 100%);
  border-radius: 10px;
  align-items: center;

  .companyIcon {
    width: 18px;
    height: 18px;
  }
}

.formCard {
  user-select: none;

  .title {
    color: #040919;
    font-size: 17px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-left: 16px;
    user-select: none;
  }

  :global .ant-checkbox-group {
    margin-left: 8px;
  }

  :global .ant-checkbox-wrapper + .ant-checkbox-wrapper {
    margin-left: 0;
  }

  :global .ant-radio-group {
    margin-left: 8px;
  }

  :global .ant-select-selection-item {
    & > div {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  :global .ant-form-item-explain-error {
    font-size: 12px;
  }

  :global .ant-form-item-with-help {
    margin-bottom: 24px;
  }

  .fixForm {
    padding-left: 16px;

    :global .ant-form-item {
      display: flex !important;
      margin-bottom: 12px !important;
      justify-content: space-between !important;
    }

    :global .ant-form-item-label {
      flex: none !important;
      font-size: 17px;
      font-weight: 600;
    }

    :global .ant-form-item-control {
      flex: 1 !important;
    }

    :global .ant-form-item-required::before {
      margin-right: 0 !important;
    }

    :global .ant-input-textarea {
      padding-right: 16px !important;
    }

    :global .ant-form-item-explain {
      opacity: 0;
    }

    &.submited {
      :global .ant-form-item-explain {
        opacity: 1;
      }
    }
  }

  .formRender {
    :global .ant-form-item-control {
      margin-top: -5px;
      margin-left: -2px;
    }

    :global [role='button'] {
      width: 90px;
      height: 90px;
      margin-left: 16px;

      div {
        width: 100%;
        height: 100%;
        line-height: 90px;
      }

      .anticon-plus {
        font-size: 30px;
        vertical-align: middle;
      }
    }

    :global .ant-image {
      margin-left: 10px;
    }

    :global .ant-select-multiple .ant-select-selection-placeholder {
      margin-left: -2px;
    }

    // :global .ant-select-selector{
    //   margin-left: -3px;
    // }
  }
}

.submitBox {
  height: 76px;
  padding-top: 19px;
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  background: #f5f6fa;
}

.submitBtn {
  color: #fff;
  height: 38px;
  line-height: 38px;
  margin: 0 16px;
  border-radius: 10px;
  background: #ff5923;
  text-align: center;
}

.drawerBox {
  .drawerTitleBox {
    display: flex;
    height: 54px;
    justify-content: space-between;
    align-items: center;

    .drawerTitle {
      font-size: 17px;
      font-weight: bolder;
    }

    .drawerIcon {
      font-size: 17px;
    }
  }

  .drawerDescribe {
    font-size: 18px;
    font-weight: bolder;
    margin-top: 36px;
    text-align: center;
  }
}

.drawerForm {
  margin-top: 18px;

  .formBox {
    display: flex;
    height: 56px;
    margin-bottom: 20px;
    padding-left: 16px;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border-radius: 12px;

    .formLabel {
      font-size: 15px;
      font-weight: bold;
    }

    .formValue {
      margin-top: 25px;

      :global .ant-input {
        text-align: right;
      }

      :global .ant-input:placeholder-shown {
        text-align: right;
      }

      :global
        .ant-select-status-error.ant-select:not(.ant-select-disabled, .ant-select-customize-input)
        .ant-select-selector {
        border: 1px solid #fff !important;
      }
    }
  }
}

.btnBox {
  width: 90%;
  position: fixed;
  bottom: 0;
  z-index: 1;
}
