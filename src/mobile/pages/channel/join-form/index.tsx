import { Helmet } from 'react-helmet';
import { Button, message } from 'antd';
import { setToken } from '@/utils/auth';
import { useSearchParams } from 'react-router-dom';
import React, { useEffect, useRef, useState } from 'react';
import {
  getFormTemplateComponents,
  postChannelJoinForm,
  channelJoinSubmit,
  getUserCardInfo,
  getChannelExistId,
} from '@/apis';
import { useRequest } from 'ahooks';
import { FormRender, FormRenderInstance } from '@@/form-engine/containers';
import sendMessage from '@@/mobile/utils/utils';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

function ChannelJoin() {
  const { t } = useTranslation();
  const [params] = useSearchParams({
    token: '',
    formCode: '',
    formVersion: '',
    settleType: '',
    belongMemberId: '',
  });
  const [showLoad, setShowLoad] = useState(false);
  const ref = useRef(null as unknown as FormRenderInstance);
  const [items, setItems] = useState<any>([]);
  const formInfo = useRef({
    businessInfoId: params.get('businessInfoId') || '',
    channelId: params.get('channelId') || '',
    formCode: params.get('formCode') || '',
    formVersion: params.get('formVersion') || '',
    belongMemberId: params.get('belongMemberId') || '',
  });
  const companyId = useRef(0);
  const channelId = useRef<number | null>(null);

  // 获取当前的公司信息
  const { run } = useRequest(getUserCardInfo, {
    manual: true,
    onSuccess: (res) => {
      companyId.current = res.companyId;
      getChannelExistId({
        companyId: res.companyId,
        supplierComId: Number(params.get('qrcodeComId')),
      })
        .then((val) => {
          channelId.current = val.channelId;
        })
        .finally(() => {
          setShowLoad(false);
        });
    },
  });

  const { run: getForm } = useRequest(getFormTemplateComponents, {
    manual: true,
    defaultParams: [
      { formCode: formInfo.current.formCode, versionNumber: Number(formInfo.current.formVersion) },
    ],
    onSuccess: (result) => {
      formInfo.current = {
        ...formInfo.current,
        formCode: result.formCode,
        formVersion: `${result.versionNumber}`,
      };
      setItems(result.components);
      if (params.get('settleType') === '4') {
        run();
      } else {
        setShowLoad(false);
      }
    },
  });

  const onSubmit = () => {
    ref.current?.submit().then((res) => {
      const arr = Object.keys(res.values).map((item) => ({
        colName: item,
        colValue: res.values[item] || '',
      })) as [];
      if (params.get('settleType') === '4') {
        channelJoinSubmit({
          id: channelId.current || null,
          companyId: companyId.current,
          qrcodeComId: Number(params.get('qrcodeComId')),
          channelExtendList: arr as any,
          formCode: formInfo.current.formCode,
          formVersion: formInfo.current.formVersion,
          settleType: 4,
          belongMemberId: Number(formInfo.current.belongMemberId) || '',
          businessInfoId: Number(formInfo.current.businessInfoId) || '',
        }).then(() => {
          message.success(t('channel_submit_success'));
          setTimeout(() => {
            sendMessage('back', {
              isClose: true,
            });
          }, 1500);
        });
        return;
      }
      postChannelJoinForm({
        businessInfoId: formInfo.current.businessInfoId,
        channelId: formInfo.current.channelId,
        formCode: formInfo.current.formCode,
        formVersion: formInfo.current.formVersion,
        channelExtendList: arr as any,
      }).then(() => {
        message.success(t('channel_submit_success'));
        setTimeout(() => {
          sendMessage('back', {
            isClose: true,
          });
        }, 1500);
      });
    });
  };

  useEffect(() => {
    const token = params.get('token');
    if (token) {
      setToken(token || '');
    }
    setShowLoad(true);
    getForm({
      formCode: formInfo.current.formCode,
      versionNumber: Number(formInfo.current.formVersion),
    });
  }, [params, getForm]);

  return (
    <div className={styles.body}>
      <Helmet>
        <title>{t('channel_apply_become_partner')}</title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.from}>
          <div className={styles.fromList}>
            <FormRender ref={ref} formCode={null} items={items} />
          </div>
        </div>
      </div>
      <div className={styles.footer}>
        <Button type="primary" disabled={showLoad} onClick={onSubmit}>
          {t('channel_submit_application')}
        </Button>
      </div>
    </div>
  );
}

export default ChannelJoin;
