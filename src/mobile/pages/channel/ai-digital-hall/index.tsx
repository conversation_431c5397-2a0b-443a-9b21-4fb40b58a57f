import { useState } from 'react';
import { Iframe } from '@/components';
import { Helmet } from 'react-helmet';
import { Spin } from 'antd';
import { useSearchParams } from 'react-router-dom';

function Aiframe() {
  const [showLoad, setShowLoad] = useState(true);
  const [params] = useSearchParams();

  const handleIframeLoad = () => {
    setShowLoad(false);
  };
  return (
    <>
      <Helmet>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
        />
      </Helmet>
      <Spin spinning={showLoad}>
        <Iframe
          title="数字大厅"
          src={`${import.meta.env.BIZ_FRONT_SITE}/ai-digital-hall/Mobile`}
          style={{ width: '100vw', height: '100vh' }}
          onLoad={handleIframeLoad}
          token={params.get('token') || ''}
        />
      </Spin>
    </>
  );
}
export default Aiframe;
