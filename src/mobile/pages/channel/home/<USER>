.card {
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 5.88%);
  margin-bottom: 16px;
  padding: 16px;
}

.title {
  font-weight: 600;
  margin-bottom: 4px;
}

.tip {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 14px;
}

.qrBox {
  display: flex;
  margin-bottom: 8px;
  justify-content: center;
}

.qrImg {
  width: 150px;
  height: 150px;
  border-radius: 12px;
}

.noCode {
  color: #b1b3be;
  font-size: 12px;
  display: flex;
  width: 150px;
  height: 150px;
  padding: 20px;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  border-radius: 12px;
  background: #f5f6fa;
}

.codeTip {
  display: flex;
  align-items: center;
}

.codeTipImg {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.cell {
  display: flex;
  justify-content: space-between;
}

.value {
  color: #888b98;
}

.footer {
  padding-bottom: 16px;
}

.footerText {
  color: #888b98;
  font-size: 12px;
}

.dataAuthority {
  color: #008cff;
  font-size: 12px;
  margin-top: 8px;
  padding-left: 24px;
}

.drawer {
  width: 100%;
}
