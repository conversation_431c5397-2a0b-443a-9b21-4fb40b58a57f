.page {
  color: #040919;
  height: 100%;
  padding: 8px 16px 0;
  background-color: #f5f6fa;
  overflow: auto;
}

.card {
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 5.88%);
  margin-bottom: 16px;
}

.bus {
  display: flex;
  align-items: center;
  padding: 8px 16px;
}

.busLogo {
  width: 40px;
  height: 40px;
  margin-right: 8px;
  border-radius: 50%;
}

.aiLogo {
  width: 40px;
  height: 40px;
  margin-right: 8px;
}

.busText {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
}

.busIcon {
  color: #040919;
  font-size: 16px;
}

.channel {
  padding: 0 16px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  padding: 12px 0;
  justify-content: space-between;
  border-bottom: 1px solid #f3f3f3;
}

.titleRight {
  font-size: 16px;
  font-weight: 400;
}

.channelContent {
  display: flex;
  padding: 16px 0;
}

.channelContentItem {
  margin-right: 70px;
}

.contentItemNum {
  margin-bottom: 4px;
}

.contentItemInfo {
  display: flex;
  align-items: center;
}

.contentItemText {
  color: #888b98;
  font-size: 12px;
  margin-right: 4px;
}

.contentItemTip {
  display: inline-block;
  width: 3px;
  height: 3px;
  background: #008cff;
  border-radius: 50%;
  margin-right: 4px;
}

.paveGoods {
  padding: 16px 0;
}

.paveGoodsContent {
  display: flex;
  margin-bottom: 30px;
}

.paveGoodsItem {
  flex: 1;
  text-align: center;
}

.paveGoodsNum {
  margin-bottom: 8px;
}

.paveGoodsText {
  color: #888b98;
  font-size: 12px;
}

.paveGoodsAdd {
  text-align: center;
}

.funcList {
  display: flex;
  margin-bottom: 10px;
  justify-content: center;
  align-items: center;
}
