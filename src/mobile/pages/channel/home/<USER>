import React, { useEffect, useState } from 'react';
import { useRequest } from 'ahooks';
import Icon from '@echronos/echos-icon';
import {
  generateQrCode,
  getPaveFormLast,
  getUserCardInfo,
  iformEnableList,
  PaveFormLastResult,
  saveFormLast,
} from '@/apis';
import { Checkbox, Spin } from 'antd';
import classNames from 'classnames';
import { useNavigate } from 'react-router-dom';
import DataPermissionContent from '@@/channel/containers/data-permission-content';
import { useTranslation } from 'react-i18next';
import styles from './business.module.less';

interface BusinessProps {
  businessInfoId: number;
  onChangeUserInfo: MultipleParamsFn<[val: { id: string; name: string }]>;
}

function Business({ businessInfoId, onChangeUserInfo }: BusinessProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showData, setShowData] = useState(false);
  const [companyInfo, setCompanyInfo] = useState({
    companyName: '',
    companyId: 0,
  });
  const [formCodeInfo, setFormCodeInfo] = useState<PaveFormLastResult>({
    formCode: '',
    name: '',
  });
  const [userInfo, setUserInfo] = useState({
    id: '',
    name: '',
  });
  const [qrCodeImage, setQrCodeImage] = useState('');
  const [showLoad, setShowLoad] = useState(false);

  // 获取二维码
  const getCode = (code: string, comId: number) => {
    setShowLoad(true);
    const url = `${window.location.protocol}//${
      window.location.host
    }/m/channel/join-state?formCode=${code}&businessInfoId=${businessInfoId || ''}&belongMemberId=${
      userInfo.id
    }&settleType=4&qrcodeComId=${comId}`;
    window.console.log(url);
    generateQrCode(11, url)
      .then((res) => {
        setQrCodeImage(`data:image/png;base64,${res.pictureBase}`);
      })
      .finally(() => {
        setShowLoad(false);
      });
  };

  // 获取表单列表
  const getFormCodeList = (code: string, comId: number) => {
    iformEnableList({ type: 5 }).then((result) => {
      let defaultInfo = null;
      if (code) {
        defaultInfo = result.list.find((item) => item.formCode === code);
      } else {
        defaultInfo = result.list.find((item) => item.isDefault);
      }
      if (defaultInfo) {
        setFormCodeInfo({
          formCode: defaultInfo.formCode,
          name: defaultInfo.name,
        });
        getCode(defaultInfo?.formCode || '', comId);
      }
      if (!defaultInfo && result.list && result.list.length > 0) {
        const firstInfo = result.list[0];
        setFormCodeInfo({
          formCode: firstInfo.formCode,
          name: firstInfo.name,
        });
        getCode(firstInfo?.formCode || '', comId);
      }
    });
  };

  // 获取最近提交的表单
  const { run: runFormCode } = useRequest(getPaveFormLast, {
    manual: true,
    onSuccess: (res) => {
      getUserCardInfo().then((result) => {
        setCompanyInfo({
          companyId: result.companyId,
          companyName: result.companyName,
        });
        getFormCodeList(formCodeInfo.formCode || res.formCode, result.companyId);
      });
    },
  });

  const onSelectForm = () => {
    navigate(`/m/select/form?formCode=${formCodeInfo.formCode}&name=${formCodeInfo.name}`);
    localStorage.setItem('SELECT_USERS', JSON.stringify([userInfo]));
  };

  const onSelectUser = () => {
    navigate(`/m/select/user?selectOne=1`);
    localStorage.setItem('CURRENT_FORM_CODE', JSON.stringify(formCodeInfo));
  };

  useEffect(() => {
    if (businessInfoId) {
      const storage = localStorage.getItem('CURRENT_FORM_CODE');
      if (storage) {
        const storageObj = JSON.parse(storage);
        setFormCodeInfo(storageObj);
        if (storageObj.formCode) {
          saveFormLast({ formCode: storageObj.formCode, recordType: 1 });
        }
        localStorage.removeItem('CURRENT_FORM_CODE');
      }
      const userStorage = localStorage.getItem('SELECT_USERS');
      if (userStorage) {
        const userStorageObj = JSON.parse(userStorage)[0];
        setUserInfo({
          name: userStorageObj.label,
          id: userStorageObj.memberId || userStorageObj.id,
        });
        onChangeUserInfo({
          name: userStorageObj.label,
          id: userStorageObj.id,
        });
        localStorage.removeItem('SELECT_USERS');
      }
      runFormCode({ recordType: 1 });
    }
  }, [runFormCode, businessInfoId]); // eslint-disable-line

  return (
    <div>
      <div className={styles.card}>
        <div className={styles.title}>{companyInfo.companyName}</div>
        <div className={styles.tip}>{t('channel_invite_become_partner')}</div>
        <div className={styles.qrBox}>
          <Spin spinning={showLoad}>
            {qrCodeImage ? (
              <img className={styles.qrImg} src={qrCodeImage} alt="" />
            ) : (
              <div className={styles.noCode}>
                <span>{t('channel_go_to_backend')}</span>
                <span>{t('channel_add_invite_form')}</span>
              </div>
            )}
          </Spin>
        </div>
        <div className={styles.codeTip}>
          <img
            className={styles.codeTipImg}
            src="https://img.huahuabiz.com/user_files/2024920/1726822484422901.png"
            alt=""
          />
          <div>{t('channel_scan_qr_tip')}</div>
        </div>
      </div>
      <div role="button" tabIndex={0} className={styles.card} onClick={onSelectForm}>
        <div className={styles.cell}>
          <span className={styles.label}>{t('channel_invite_form')}</span>
          <span className={classNames({ [styles.value]: !formCodeInfo.formCode })}>
            <span>
              {formCodeInfo.formCode ? formCodeInfo.name : t('channel_select_invite_form')}
            </span>
            <Icon name="right_arrow_line" />
          </span>
        </div>
      </div>
      <div className={styles.card}>
        <div role="button" tabIndex={0} className={styles.cell} onClick={onSelectUser}>
          <span className={styles.label}>{t('channel_responsible_person')}</span>
          <span>
            <span className={classNames({ [styles.value]: !userInfo.id })}>
              {userInfo.id ? userInfo.name : t('channel_select_company_principal')}
            </span>
            <Icon name="right_arrow_line" />
          </span>
        </div>
      </div>
      <div className={styles.footer}>
        <Checkbox checked onChange={() => {}}>
          <span className={styles.footerText}>{t('channel_require_sub_data_auth')}</span>
        </Checkbox>
        <div
          className={styles.dataAuthority}
          role="button"
          tabIndex={0}
          onClick={() => {
            setShowData(true);
          }}
        >
          {t('channel_data_perm_content')}
        </div>
      </div>
      <DataPermissionContent
        className={styles.drawer}
        visible={showData}
        onClose={() => {
          setShowData(false);
        }}
        onOk={() => {
          setShowData(false);
        }}
      />
    </div>
  );
}

export default Business;
