import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { Icon } from '@/components';
import { But<PERSON>, Spin } from 'antd';
import sendMessage from '@@/mobile/utils/utils';
import classNames from 'classnames';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useRequest } from 'ahooks';
import {
  channelFuncList,
  ChannelFuncListResult,
  channelSupplierNum,
  ChannelSupplierNumResult,
  PaveGoodsNumResult,
  getPaveGoodsNum,
} from '@/apis';
import { initPermission, testPermMobile, checkPerm } from '@/utils/permission';
import { getToken, setToken } from '@/utils/auth';
import { useTranslation } from 'react-i18next';
import Business from './business';
import styles from './index.module.less';

function Home() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [params] = useSearchParams({ token: '' });
  const [list, setList] = useState<ChannelFuncListResult[]>([]);
  const [showLoad, setShowLoad] = useState(false);
  const [supplierNum, setSupplierNum] = useState<ChannelSupplierNumResult>({
    channelApprovingCount: 0,
    channelTotalCount: 0,
  });
  const [paveGoodsNum, setPaveGoodsNum] = useState<PaveGoodsNumResult>({
    arrangeChannelCount: 0,
    arrangeProductCount: 0,
  });
  const userInfo = useRef({
    id: '',
    name: '',
  });
  const businessInfoId = useRef(0);

  // 去业务员
  const onNavBus = () => {
    navigate('/m/channel/business');
    localStorage.setItem('SELECT_USERS', JSON.stringify([userInfo.current]));
  };

  // 获取功能列表
  const { run } = useRequest(channelFuncList, {
    manual: true,
    onSuccess: (res) => {
      const arr = res.list.filter((item) => item.code !== 2);
      setList(arr);
      if (arr && arr.length) {
        const busInfo = arr.find((item) => item.code === 3);
        businessInfoId.current = busInfo ? busInfo.businessInfoId : 0;
        // 只有业务员权限
        if (arr.length === 1 && arr[0].code === 3) {
          // navigate('/m/channel/business', { replace: true });
        }
      } else {
        sendMessage('back', {
          isClose: true,
        });
      }
    },
  });

  // 获取渠道商数量
  const { run: getSupplierNum } = useRequest(channelSupplierNum, {
    manual: true,
    onSuccess: (result) => {
      setSupplierNum(result);
    },
  });

  // 获取铺货数量
  const { run: getPaveNum } = useRequest(getPaveGoodsNum, {
    manual: true,
    onSuccess: (result) => {
      setPaveGoodsNum(result);
    },
  });

  // 去渠道商列表
  const onNavApp = () => {
    if (showLoad) return;
    if (!testPermMobile('BH_001_001_001')) {
      return;
    }
    sendMessage('interfaceJump', {
      pageCode: 'channelSupplierList',
      param: {
        type: 0,
      },
    });
  };

  // 去AI数字大厅
  const onNavAIHall = async () => {
    // 在跳转前先设置document title
    document.title = 'AI数字大厅';
    window.location.href = `${import.meta.env.BIZ_FRONT_SITE}/ai-digital-hall/Mobile?token=${
      params.get('token') || getToken()
    }`;
  };

  // 去铺货
  const onNavPaveGoods = () => {
    if (showLoad) return;
    if (!testPermMobile('BH_001_001_005')) {
      return;
    }
    navigate('/m/channel/pave-goods');
    localStorage.setItem('SELECT_USERS', JSON.stringify([userInfo.current]));
  };

  // 去铺货新增
  const onNavPaveGoodsCreate = () => {
    if (showLoad) return;
    if (!testPermMobile('BH_001_001_005_002')) {
      return;
    }
    localStorage.removeItem('PAVE_GOODS_CREATE_INFO');
    localStorage.removeItem('SELECT_GOODS_MOBILE');
    navigate('/m/channel/pave-goods-create');
    localStorage.setItem('SELECT_USERS', JSON.stringify([userInfo.current]));
  };

  // // 去ai数据助手
  // const onNavDataHelp = () => {
  //   navigate('/m/channel/data-help');
  // };

  const init = useCallback(() => {
    setShowLoad(true);
    initPermission(true).finally(() => {
      setShowLoad(false);
    });
    run();
    getSupplierNum();
    getPaveNum();
  }, [getPaveNum, getSupplierNum, run]);

  const isBus = useMemo(() => list.some((item) => item.code === 3), [list]);

  const isChannel = useMemo(() => list.some((item) => item.code === 1), [list]);

  useEffect(() => {
    const token = params.get('token') || getToken();
    window.console.log(token);
    if (token) {
      setToken(token || '');
    } else {
      sendMessage('getUserInfo', (el) => {
        setToken(el.authorization);
        init();
      });
    }
    init();
  }, [init, params]);

  return (
    <div className={styles.page}>
      <Helmet>
        <title>{t('channel_management')}</title>
      </Helmet>
      <Spin spinning={showLoad}>
        <div className={classNames(styles.card, styles.channel)}>
          <div className={styles.title}>常用功能</div>
          <div className={styles.channelContent}>
            <div>
              <div role="button" tabIndex={0} className={styles.funcList} onClick={onNavAIHall}>
                <img
                  className={styles.aiLogo}
                  src="https://img.huahuabiz.com/user_files/2025217/1739773486031582.png"
                  alt=""
                />
              </div>
              <div role="button" tabIndex={0} className={styles.contentItemInfo}>
                <span className={styles.contentItemText}>AI数字大厅</span>
              </div>
            </div>
          </div>
        </div>
        {isBus && (
          <div
            role="button"
            tabIndex={0}
            className={classNames(styles.card, styles.bus)}
            onClick={onNavBus}
          >
            <img
              className={styles.busLogo}
              src="https://img.huahuabiz.com/user_files/20241114/1731565622427218.png"
              alt=""
            />
            <span className={styles.busText}>{t('channel_staff_center')}</span>
            <Icon name="right" className={styles.busIcon} />
          </div>
        )}
        {isChannel && (
          <>
            {checkPerm('BH_001_001_001') && (
              <div className={classNames(styles.card, styles.channel)}>
                <div className={styles.title}>{t('channel_partner')}</div>
                <div className={styles.channelContent}>
                  <div className={styles.channelContentItem}>
                    <div className={styles.contentItemNum}>{supplierNum.channelTotalCount}</div>
                    <div
                      role="button"
                      tabIndex={0}
                      className={styles.contentItemInfo}
                      onClick={onNavApp}
                    >
                      <span className={styles.contentItemText}>{t('channel_total')}</span>
                      <Icon name="right" color="#888B98" />
                    </div>
                  </div>
                  <div>
                    <div className={styles.contentItemNum}>{supplierNum.channelApprovingCount}</div>
                    <div
                      role="button"
                      tabIndex={0}
                      className={styles.contentItemInfo}
                      onClick={onNavApp}
                    >
                      <span className={styles.contentItemTip} />
                      <span className={styles.contentItemText}>{t('channel_pending_review')}</span>
                      <Icon name="right" color="#888B98" />
                    </div>
                  </div>
                </div>
              </div>
            )}
            {checkPerm('BH_001_001_005') && (
              <div className={classNames(styles.card, styles.channel)}>
                <div role="button" tabIndex={0} className={styles.title} onClick={onNavPaveGoods}>
                  <span>{t('channel_distribution_manage')}</span>
                  <Icon name="right" className={styles.titleRight} />
                </div>
                <div className={styles.paveGoods}>
                  <div className={styles.paveGoodsContent}>
                    <div className={styles.paveGoodsItem}>
                      <div className={styles.paveGoodsNum}>{paveGoodsNum.arrangeChannelCount}</div>
                      <div className={styles.paveGoodsText}>
                        {t('channel_distribution_channel_count')}
                      </div>
                    </div>
                    <div className={styles.paveGoodsItem}>
                      <div className={styles.paveGoodsNum}>{paveGoodsNum.arrangeProductCount}</div>
                      <div className={styles.paveGoodsText}>
                        {t('channel_distribution_product_count')}
                      </div>
                    </div>
                  </div>
                  <div className={styles.paveGoodsAdd}>
                    <Button type="primary" size="small" onClick={onNavPaveGoodsCreate}>
                      {t('channel_add_distribution')}
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        {(isBus || isChannel) && (
          <Business
            businessInfoId={businessInfoId.current}
            onChangeUserInfo={(val) => {
              userInfo.current = {
                ...val,
              };
            }}
          />
        )}
      </Spin>
    </div>
  );
}

export default Home;
