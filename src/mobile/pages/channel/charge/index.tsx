import React, { useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { Search, Empty, Modal } from '@/components';
import { useRequest } from 'ahooks';
import {
  BusinessChannelListResult,
  getChannelBusinessChannelList,
  removeBusinessChannel,
  removeChannelCompanyCheck,
} from '@/apis';
import { useSearchParams } from 'react-router-dom';
import Icon from '@echronos/echos-icon';
import InfiniteScroll from 'react-infinite-scroll-component';
import dayjs from 'dayjs';
import { debounce, cloneDeep } from 'lodash';
import { Divider, Spin, Dropdown, Menu, message } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

function Charge() {
  const { t } = useTranslation();
  const handleItems = [
    {
      key: '1',
      label: t('channel_remove'),
    },
  ];
  const [params] = useSearchParams({ channelId: '', businessInfoId: '', channelComName: '' });
  const requestParams = useRef({
    businessInfoId: params.get('businessInfoId') || '',
    channelId: params.get('channelId') || '',
    pageNo: 1,
    pageSize: 10,
    key: '',
  });
  const [list, setList] = useState<BusinessChannelListResult[]>([]);
  const totalPages = useRef(0);

  const { run } = useRequest(getChannelBusinessChannelList, {
    manual: true,
    defaultParams: [{ ...requestParams.current }],
    onSuccess: (res) => {
      totalPages.current = res.pagination.total;
      if (requestParams.current.pageNo === 1) {
        setList(res.list);
      } else {
        setList([...list, ...res.list]);
      }
      requestParams.current.pageNo += 1;
    },
  });

  const onLoadMore = () => {
    if (!(requestParams.current.pageNo <= totalPages.current)) return;
    run({ ...requestParams.current });
  };

  const onSearch = debounce((val: string) => {
    requestParams.current = {
      ...requestParams.current,
      pageNo: 1,
      key: val,
    };
    run({ ...requestParams.current });
  }, 500);

  const onHandle = (val: string, id: number) => {
    if (val === '1') {
      removeChannelCompanyCheck({
        id,
        status: 3,
        isBusinessClear: true,
        businessInfoId: Number(requestParams.current.businessInfoId) || '',
        toChannelId: Number(requestParams.current.channelId) || '',
      }).then((res) => {
        if (!res.ifBusinessCanClear) {
          Modal.confirm({
            title: t('channel_tip'),
            content: t('channel_remove_with_data_tip'),
            centered: true,
            width: '80%',
            wrapClassName: styles.modalBoxConfirm,
          });
        } else {
          Modal.confirm({
            title: t('channel_tip'),
            content: t('channel_confirm_remove'),
            centered: true,
            width: '80%',
            wrapClassName: styles.modalBox,
            onOk: () => {
              removeBusinessChannel({
                id,
                status: 3,
                isBusinessClear: true,
                businessInfoId: Number(requestParams.current.businessInfoId) || '',
              }).then(() => {
                message.success(t('channel_remove_success'));
                setList(cloneDeep(list.filter((item) => item.nextChannelId !== id)));
              });
            },
          });
        }
      });
    }
  };

  useEffect(() => {
    run({ ...requestParams.current });
  }, [run]);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>{t('channel_my_responsible')}</title>
      </Helmet>
      <div className={styles.search}>
        <Search placeholder={t('channel_search_channel_name')} onSearch={onSearch} />
      </div>
      <div className={styles.content} id="list">
        {list && !!list.length ? (
          <InfiniteScroll
            dataLength={list.length}
            hasMore={requestParams.current.pageNo < totalPages.current}
            loader={
              <div className="text-center">
                <Spin tip={t('channel_loading')} />
              </div>
            }
            next={onLoadMore}
            scrollableTarget="list"
            className={styles.scroll}
            endMessage={
              list.length > 10 && (
                <div className={styles.divider}>
                  <Divider plain>
                    <span className={styles.endMessage}>{t('channel_reached_bottom')}</span>
                  </Divider>
                </div>
              )
            }
          >
            <div className={styles.card}>
              {list.map((item, index) => (
                <div className={styles.item} key={`${index + 1}`}>
                  <div className={styles.title}>
                    <img
                      className={styles.titleImg}
                      src={
                        item.logo ||
                        'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
                      }
                      alt=""
                    />
                    <div className={styles.titleText}>{item.nextChannelComName || '--'}</div>
                    <Dropdown
                      overlay={
                        <Menu
                          onClick={({ key }) => onHandle(key, item.nextChannelId)}
                          className={styles.menu}
                          items={handleItems}
                        />
                      }
                      placement="bottom"
                      trigger={['click']}
                    >
                      <Icon className={styles.handle} name="more_line" />
                    </Dropdown>
                  </div>
                  <div className={styles.cell}>
                    <div className={styles.label}>{t('channel_join_time')}：</div>
                    <div>{item.bindTime ? dayjs(item.bindTime).format('YYYY-MM-DD') : '--'}</div>
                  </div>
                  <div className={styles.cell}>
                    <div className={styles.label}>{t('channel_distribution_quantity')}：</div>
                    <div>{item.shareNumber || '--'}</div>
                  </div>
                  <div className={styles.cell}>
                    <div className={styles.label}>
                      {t('channel_distribution_amount')}({t('channel_currency')})：
                    </div>
                    <div>{item.shareAmount || '--'}</div>
                  </div>
                  <div className={styles.cell}>
                    <div className={styles.label}>{t('channel_distribution_date')}：</div>
                    <div>{item.shareTime ? dayjs(item.shareTime).format('YYYY-MM-DD') : '--'}</div>
                  </div>
                </div>
              ))}
            </div>
          </InfiniteScroll>
        ) : (
          <div className={styles.noData}>
            <Empty />
          </div>
        )}
      </div>
    </div>
  );
}

export default Charge;
