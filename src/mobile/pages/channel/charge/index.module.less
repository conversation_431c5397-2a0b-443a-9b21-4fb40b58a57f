@import 'styles/mixins/mixins';

.container {
  color: #040919;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: #f5f6fa;
}

.search {
  padding: 2px 16px 16px;
}

.content {
  flex: 1;
  padding: 0 16px;
  overflow: auto;
}

.scroll {
  border-radius: 18px;
}

.card {
  padding: 0 20px;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
  overflow: auto;
}

.item:not(:last-child) {
  border-bottom: 1px solid #f3f3f3;
}

.title {
  display: flex;
  align-items: center;
  padding: 12px 0;
}

.titleImg {
  width: 44px;
  height: 44px;
  margin-right: 8px;
  border-radius: 50%;
}

.titleText {
  font-size: 16px;
  font-weight: 600;
  flex: 1;
  .text-overflow(1);
}

.cell {
  display: flex;
  margin-bottom: 8px;
  justify-content: space-between;
}

.label {
  color: #888b98;
}

.noData {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.handle {
  color: #999eb2;
  font-size: 24px !important;
}

.modalBox,
.modalBoxConfirm {
  :global {
    .ant-modal-confirm-title {
      text-align: center !important;
    }

    .ant-modal-confirm-content {
      margin-bottom: 20px;
      text-align: center !important;
    }

    .ant-modal-confirm-btns {
      display: flex;
      justify-content: space-between;
      float: none !important;
    }

    .ant-btn {
      width: 100%;
    }
  }
}

.modalBoxConfirm {
  :global {
    .ant-modal-confirm-btns {
      justify-content: center;
    }

    .ant-btn.ant-btn-default {
      display: none !important;
    }

    .ant-btn {
      width: 55% !important;
    }
  }
}
