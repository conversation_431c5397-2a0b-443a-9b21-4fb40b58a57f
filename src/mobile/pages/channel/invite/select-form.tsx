import React from 'react';
import { Popup } from 'antd-mobile';
import { Icon } from '@/components';
import { IformEnableListResult } from '@/apis';
import { useTranslation } from 'react-i18next';
import styles from './select-form.module.less';

interface SelectFormProps {
  visible: boolean;
  list: IformEnableListResult[];
  onClose: () => void;
  onConfirm: MultipleParamsFn<[val: IformEnableListResult]>;
}

function SelectForm({ visible, list, onClose, onConfirm }: SelectFormProps) {
  const { t } = useTranslation();

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      onMaskClick={onClose}
      className={styles.popup}
    >
      <div className={styles.title}>
        <span />
        <span>{t('channel_selectinvite_form')}</span>
        <Icon className={styles.titleClose} name="close" onClick={onClose} />
      </div>
      <div className={styles.content}>
        {list.map((item) => (
          <div
            role="button"
            tabIndex={0}
            key={item.id}
            className={styles.item}
            onClick={() => onConfirm(item)}
          >
            {item.name}
          </div>
        ))}
      </div>
    </Popup>
  );
}

export default SelectForm;
