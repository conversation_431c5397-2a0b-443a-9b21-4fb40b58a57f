.page {
  color: #040919;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: #f5f6fa;
}

.container {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 0 16px;
}

.content {
  flex: 1;
  overflow: auto;
}

.card {
  margin-bottom: 16px;
  padding: 20px 16px;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
}

.title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.tip {
  color: #888b98;
  margin-bottom: 16px;
}

.code {
  width: 100%;
  height: 180px;

  :global {
    .ant-spin-nested-loading {
      width: 100%;
      height: 100%;
    }

    .ant-spin-container {
      width: 100%;
      height: 100%;
      text-align: center;
    }
  }
}

.codeImg {
  width: 180px;
  height: 180px;
}

.codeTip {
  display: flex;
  margin-top: 12px;
  justify-content: center;
}

.codeTipImg {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}

.cell {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.value {
  display: flex;
  align-items: center;
}

.footer {
  padding: 20px 0;

  :global {
    .ant-btn {
      width: 100%;
    }
  }
}

.checkbox {
  margin-bottom: 20px;
}
