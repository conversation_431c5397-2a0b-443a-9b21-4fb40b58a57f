import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Icon } from '@/components';
import { Helmet } from 'react-helmet';
import { useRequest } from 'ahooks';
import {
  generateQrCode,
  getPaveFormLast,
  iformEnableList,
  IformEnableListResult,
  PaveFormLastResult,
  saveFormLast,
} from '@/apis';
import { getToken, setToken } from '@/utils/auth';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

function Invite() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [params] = useSearchParams({
    channelId: '',
    businessInfoId: '',
    channelComName: '',
    token: '',
  });
  const [currentFormInfo, setCurrentFormInfo] = useState<IformEnableListResult>({
    check: false,
    createTime: '',
    formCode: '',
    formVersion: 0,
    id: 0,
    isCreate: false,
    isDefault: 0,
    isEnable: 0,
    name: '',
    updateTime: '',
  });
  const [formCodeInfo, setFormCodeInfo] = useState<PaveFormLastResult>({
    formCode: '',
    name: '',
  });
  const [qrCodeImage, setQrCodeImage] = useState('');
  const [showLoad, setShowLoad] = useState(false);
  // const [showSelectPopup, setShowSelectPopup] = useState(false);

  const getCode = (code: string) => {
    setShowLoad(true);
    const url = `${window.location.protocol}//${
      window.location.host
    }/m/channel/join-state?channelId=${params.get('channelId') || ''}&businessInfoId=${
      params.get('businessInfoId') || ''
    }&formCode=${code}`;
    window.console.log(url);
    generateQrCode(11, url)
      .then((res) => {
        setQrCodeImage(`data:image/png;base64,${res.pictureBase}`);
      })
      .finally(() => {
        setShowLoad(false);
      });
  };

  const getFormCodeList = (code: string) => {
    iformEnableList({ type: 5 }).then((result) => {
      let defaultInfo = null;
      if (code) {
        defaultInfo = result.list.find((item) => item.formCode === code);
      } else {
        defaultInfo = result.list.find((item) => item.isDefault);
      }
      if (defaultInfo) {
        setCurrentFormInfo(defaultInfo);
        setFormCodeInfo({
          formCode: defaultInfo.formCode,
          name: defaultInfo.name,
        });
        getCode(defaultInfo.formCode);
      }
      if (!defaultInfo && result.list && result.list.length > 0) {
        const firstInfo = result.list[0];
        setCurrentFormInfo(firstInfo);
        setFormCodeInfo({
          formCode: firstInfo.formCode,
          name: firstInfo.name,
        });
        getCode(firstInfo.formCode);
      }
    });
  };

  const { run } = useRequest(getPaveFormLast, {
    manual: true,
    onSuccess: (res) => {
      getFormCodeList(res.formCode);
    },
  });

  const onSelectForm = () => {
    navigate(`/m/select/form?formCode=${formCodeInfo.formCode}&name=${formCodeInfo.name}`);
  };

  useEffect(() => {
    const token = params.get('token') || getToken();
    if (token) {
      setToken(token || '');
    }
    const storage = localStorage.getItem('CURRENT_FORM_CODE');
    if (storage) {
      const storageObj = JSON.parse(storage);
      setFormCodeInfo(storageObj);
      if (storageObj.formCode) {
        saveFormLast({ formCode: storageObj.formCode, recordType: 2 });
      }
      getFormCodeList(storageObj.formCode);
      localStorage.removeItem('CURRENT_FORM_CODE');
      return;
    }
    setShowLoad(true);
    run({ recordType: 2 });
  }, [params, run]); // eslint-disable-line

  return (
    <div className={styles.page}>
      <Helmet>
        <title>{t('channel_qr_invite')}</title>
      </Helmet>
      {/* <Header title={t('channel_qr_invite')} /> */}
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.card}>
            <div className={styles.title}>{params.get('channelComName')}</div>
            <div className={styles.tip}>{t('channel_invite_become_partner')}</div>
            <div className={styles.code}>
              <Spin spinning={showLoad}>
                {qrCodeImage && <img className={styles.codeImg} src={qrCodeImage} alt="" />}
              </Spin>
            </div>
            <div className={styles.codeTip}>
              <img
                className={styles.codeTipImg}
                src="https://img.huahuabiz.com/user_files/2024920/1726822484422901.png"
                alt=""
              />
              <span>{t('channel_scan_qr_tip')}</span>
            </div>
          </div>
          <div className={styles.card}>
            <div className={styles.cell}>
              <div>{t('channel_invite_form')}</div>
              <div role="button" tabIndex={0} className={styles.value} onClick={onSelectForm}>
                <span>{currentFormInfo.name || t('channel_create_invite_form_first')}</span>
                <Icon name="right" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Invite;
