import { useEffect, useRef, useState, MouseEvent } from 'react';
import dayjs from 'dayjs';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { Helmet } from 'react-helmet';
import { <PERSON><PERSON>, Divider, Spin, message } from 'antd';
import { Modal, Empty, Search } from '@/components';
import { PaveGoodsListResult, getPaveGoodsList, postPaveGoodsCancel } from '@/apis';
import { initPermission, testPermMobile } from '@/utils/permission';
import InfiniteScroll from 'react-infinite-scroll-component';
import { debounce } from 'lodash';
import selectPaveWarehouse from '@@/mobile/pages/channel/pave-goods-detail/select-pave-warehouse';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface PaveResult {
  list: PaveGoodsListResult[];
  pagination: {
    count: number;
    total: number;
  };
}

function PaveGoods() {
  const { t } = useTranslation();
  const statusList = [
    {
      key: 0,
      label: t('channel_status_pending'),
      color: 'rgb(114, 46, 209)',
      bgc: 'rgb(245, 232, 255)',
    },
    {
      key: 1,
      label: t('channel_status_distributed'),
      color: 'rgb(0, 180, 42)',
      bgc: 'rgb(232, 255, 234)',
    },
    {
      key: 2,
      label: t('channel_status_rejected'),
      color: 'rgb(245, 63, 63)',
      bgc: 'rgb(255, 236, 232)',
    },
    {
      key: 3,
      label: t('channel_status_cancelled'),
      color: 'rgb(245, 63, 63)',
      bgc: 'rgb(255, 236, 232)',
    },
    {
      key: 101,
      label: t('channel_status_recalled'),
      color: 'rgb(136, 139, 152)',
      bgc: 'rgb(243, 243, 243)',
    },
    {
      key: 102,
      label: t('channel_status_recall_pending'),
      color: 'rgb(104, 11, 196)',
      bgc: 'rgb(237, 229, 250)',
    },
  ];
  const navigate = useNavigate();
  const [paveResultList, setPaveResultList] = useState<PaveGoodsListResult[]>([]);
  const [paramsUrl] = useSearchParams();
  const isChannel = paramsUrl.get('isChannel') || '';
  const totalPage = useRef(0);
  const Count = useRef(0);
  const requestParams = useRef({
    queryType: Number(isChannel) ? 2 : 1,
    pageNo: 1,
    pageSize: 10,
    channelComId: paramsUrl.get('channelComId') || '',
    status: paramsUrl.get('channelComId') ? 1 : '',
    keyword: '',
  });

  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);

  // 获取铺货列表
  const { run } = useRequest(getPaveGoodsList, {
    manual: true,
    defaultParams: [{ ...requestParams.current }],
    onSuccess(res) {
      const { list = [], pagination } = res as unknown as PaveResult;
      totalPage.current = pagination.total;
      Count.current = pagination.count;

      if (requestParams.current.pageNo === 1) {
        setPaveResultList(list);
      } else {
        setPaveResultList(paveResultList?.concat(list));
      }
      requestParams.current.pageNo += 1;
    },
  });

  const onSearch = debounce((val: string) => {
    requestParams.current = {
      ...requestParams.current,
      pageNo: 1,
      keyword: val,
    };
    run({ ...requestParams.current });
  }, 300);

  // 进入列表页面后加载
  useEffect(() => {
    run({ ...requestParams.current });
  }, [run]);

  // 滑动加载;
  const loadMore = () => {
    if (!(requestParams.current.pageNo <= totalPage.current)) return;
    run({ ...requestParams.current });
  };

  // 取消
  const onCancel = (val: number) => {
    if (!testPermMobile('BH_001_001_005_001_002')) {
      return;
    }
    Modal.confirm({
      title: t('channel_tip'),
      content: t('channel_confirm_cancel'),
      centered: true,
      width: '80%',
      wrapClassName: styles.modalBox,
      onOk: () => {
        postPaveGoodsCancel({ arrangeInfoId: val }).then(() => {
          const index = paveResultList.findIndex((item) => item.arrangeInfoId === val);
          setPaveResultList((prevPaveResult) => {
            const newPaveResult = [...prevPaveResult];
            newPaveResult[index] = {
              ...newPaveResult[index],
              canCancel: false,
              canUpdate: false,
              status: 3,
            };
            message.success(t('channel_cancel_success'));
            return newPaveResult; // 返回新的数组
          });
        });
      },
    });
  };

  const onNavConfirm = (val: number) => {
    if (!testPermMobile('BH_001_001_005_001_001')) {
      return;
    }
    navigate(
      `/m/channel/pave-goods-detail?id=${val}&isChannel=${
        requestParams.current.queryType === 1 ? 0 : 1
      }&channelComId=${requestParams.current.channelComId}`
    );
  };

  const onNavEdit = (id: number) => {
    if (!testPermMobile('BH_001_001_005_001_001_001')) {
      return;
    }
    localStorage.removeItem('PAVE_GOODS_CREATE_INFO');
    localStorage.removeItem('SELECT_GOODS_MOBILE');
    navigate(
      `/m/channel/pave-goods-create?id=${id}&isChannel=${
        requestParams.current.queryType === 1 ? 0 : 1
      }&channelComId=${requestParams.current.channelComId}`
    );
  };

  const onRecycle = (val: number) => {
    selectPaveWarehouse({
      id: val,
      title: t('channel_select_recycle_warehouse'),
      onConfirm: () => {
        const index = paveResultList.findIndex((item) => item.arrangeInfoId === val);
        setPaveResultList((prevPaveResult) => {
          message.success(t('channel_submit_success'));
          const newPaveResult = [...prevPaveResult];
          newPaveResult[index] = {
            ...newPaveResult[index],
            canRecycle: false,
            status: 101,
          };
          return newPaveResult; // 返回新的数组
        });
      },
    });
  };

  // 操作
  const operation = (item: PaveGoodsListResult) => (
    <div className={styles.handle}>
      {item.canCancel && (
        <span
          role="button"
          tabIndex={0}
          className={styles.cancel}
          onClick={(e) => {
            e.stopPropagation();
            onCancel(item.arrangeInfoId);
          }}
        >
          {t('channel_cancel')}
        </span>
      )}
      {item.canUpdate && (
        <span
          role="button"
          tabIndex={0}
          className={styles.edit}
          onClick={(e) => {
            e.stopPropagation();
            onNavEdit(item.arrangeInfoId);
          }}
        >
          {t('channel_edit')}
        </span>
      )}
      {(item.canConfirm || item.canConfirmRecycle) && (
        <span
          role="button"
          tabIndex={0}
          className={styles.edit}
          onClick={(e) => {
            e.stopPropagation();
            onNavConfirm(item.arrangeInfoId);
          }}
        >
          {t('channel_go_confirm')}
        </span>
      )}
      {item.canRecycle && (
        <span
          role="button"
          tabIndex={0}
          className={styles.edit}
          onClick={(e) => {
            e.stopPropagation();
            onRecycle(item.arrangeInfoId);
          }}
        >
          {t('channel_distribution_recall')}
        </span>
      )}
      {item.status === 2 && (
        <span
          role="button"
          tabIndex={0}
          className={styles.edit}
          onClick={(e) => {
            e.stopPropagation();
            onNavConfirm(item.arrangeInfoId);
          }}
        >
          {t('channel_view_reason')}
        </span>
      )}
    </div>
  );

  // tag状态
  const getStatusInfo = (val: number) => statusList.find((item) => item.key === val);

  const NavToDetailPage = (event: MouseEvent, item: PaveGoodsListResult) => {
    event.preventDefault();
    event.stopPropagation();
    if (!testPermMobile('BH_001_001_005_001_001')) {
      return;
    }
    navigate(
      `/m/channel/pave-goods-detail?id=${item.arrangeInfoId}&isChannel=${
        requestParams.current.queryType === 1 ? 0 : 1
      }&channelComId=${requestParams.current.channelComId}`
    );
  };

  const onCreate = () => {
    if (!testPermMobile('BH_001_001_005_002')) {
      return;
    }
    localStorage.removeItem('PAVE_GOODS_CREATE_INFO');
    localStorage.removeItem('SELECT_GOODS_MOBILE');
    navigate('/m/channel/pave-goods-create');
  };

  useEffect(() => {
    initPermission();
  }, []);

  // useEffect(() => {
  //   keyboardStatusRun();
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);

  // 监听键盘弹起
  useEffect(() => {
    let originalHeight = window.innerHeight;
    let timer: ReturnType<typeof setTimeout>;

    const handleResize = () => {
      // 清除之前的定时器
      if (timer) clearTimeout(timer);

      // 设置一个短暂的延迟，等待高度变化稳定
      timer = setTimeout(() => {
        const currentHeight = window.innerHeight;

        // 如果当前高度明显小于原始高度，认为键盘弹出
        if (originalHeight - currentHeight > 100) {
          setIsKeyboardVisible(true);
        } else {
          setIsKeyboardVisible(false);
          // 更新原始高度（处理屏幕旋转等情况）
          originalHeight = currentHeight;
        }
      }, 100);
    };

    // 记录初始高度
    originalHeight = window.innerHeight;

    // 监听输入框焦点事件
    const handleFocusIn = () => {
      // 在Android上，focusin事件触发时键盘可能还没完全弹出
      // 设置一个短暂的延迟再检查高度变化
      setTimeout(handleResize, 300);
    };

    // 添加事件监听
    window.addEventListener('resize', handleResize);
    document.addEventListener('focusin', handleFocusIn);

    // 清理事件监听
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('focusin', handleFocusIn);
      if (timer) clearTimeout(timer);
    };
  }, []);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>{t('channel_distribution_manage')}</title>
      </Helmet>
      <div className={styles.search}>
        <Search placeholder="铺货渠道/铺货方名称" onSearch={onSearch} />
      </div>
      <div className={styles.wrapper} id="wrapper">
        {!!paveResultList && !!paveResultList.length ? (
          <InfiniteScroll
            dataLength={paveResultList.length}
            hasMore={requestParams.current.pageNo < totalPage.current}
            loader={
              <div className="text-center">
                <Spin tip={t('channel_loading')} />
              </div>
            }
            next={loadMore}
            scrollableTarget="wrapper"
            className={styles.scroll}
            endMessage={
              paveResultList.length === Count.current &&
              paveResultList.length > 10 && (
                <div className={styles.divider}>
                  <Divider plain>
                    <span className={styles.endMessage}>{t('channel_reached_bottom')}</span>
                  </Divider>
                </div>
              )
            }
          >
            {!!paveResultList &&
              !!paveResultList.length &&
              paveResultList.map((item) => (
                <div
                  key={item.arrangeNo || item.arrangeInfoId}
                  className={styles.card}
                  tabIndex={0}
                  role="button"
                  onClick={(e) => NavToDetailPage(e, item)}
                >
                  <div className={styles.itemLeft}>
                    <div className={styles.itemTitle}>
                      <div
                        className={styles.status}
                        style={{
                          color: getStatusInfo(item.status)?.color,
                          background: getStatusInfo(item.status)?.bgc,
                        }}
                      >
                        {getStatusInfo(item.status)?.label}
                      </div>
                      <span className={styles.name}>{item.channelComName}</span>
                    </div>
                    <div className={styles.number}>{item.arrangeNo}</div>
                    <div>
                      <span className={styles.count}>
                        {t('channel_quantity')}: <span>{item.arrangeSumNum}</span>
                      </span>
                      <span className={styles.time}>
                        {t('channel_time')}:
                        <span>
                          {item.arrangeTime ? dayjs(item.arrangeTime).format('YYYY-MM-DD') : '--'}
                        </span>
                      </span>
                    </div>
                  </div>
                  <div className={styles.handle}>{operation(item)}</div>
                </div>
              ))}
          </InfiniteScroll>
        ) : (
          <div className={styles.noData}>
            <Empty />
          </div>
        )}
      </div>
      {!isKeyboardVisible && (
        <div className={styles.footer}>
          <Button type="primary" onClick={onCreate}>
            {t('channel_add_distribution')}
          </Button>
        </div>
      )}
    </div>
  );
}

export default PaveGoods;
