@import 'styles/mixins/mixins';

.container {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: #f5f6fa;
  padding-top: 8px;

  .wrapper {
    padding: 0 16px;
    flex: 1;
    overflow: auto;

    .scroll {
      overflow: initial !important;
    }
  }
}

.search {
  padding: 0 16px 16px;
}

.card {
  display: flex;
  margin-bottom: 16px;
  padding: 12px;
  justify-content: space-between;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
}

.itemLeft {
  flex: 1;
  margin-right: 4px;
}

.itemTitle {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.status {
  font-size: 12px;
  display: inline-block;
  height: 20px;
  padding: 2px 4px;
  border-radius: 4px;
}

.name {
  flex: 1;
  color: #040919;
  font-weight: 600;
  margin-left: 12px;
  .text-overflow(1);
}

.number {
  color: #b1b3be;
  font-size: 12px;
  margin-bottom: 12px;
}

.count {
  color: #040919;
  font-size: 12px;
}

.time {
  color: #b1b3be;
  font-size: 12px;
  margin-left: 32px;
}

.handle {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.edit {
  color: #008cff;
  margin-left: 8px;
  cursor: pointer;
}

.cancel {
  color: #ea1c26;
  cursor: pointer;
}

.footer {
  padding: 16px;

  :global {
    .ant-btn {
      min-width: 100% !important;
    }
  }
}

.modalBox {
  :global {
    .ant-modal-confirm-title {
      text-align: center !important;
    }

    .ant-modal-confirm-content {
      margin-bottom: 20px;
      text-align: center !important;
    }

    .ant-modal-confirm-btns {
      display: flex;
      justify-content: space-between;
      float: none !important;
    }

    .ant-btn {
      width: 100%;
    }
  }
}

.noData {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}
