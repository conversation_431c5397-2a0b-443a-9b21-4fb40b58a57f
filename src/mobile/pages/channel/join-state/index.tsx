import React, { useEffect, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { channelJoinImgGet } from '@/apis';
import { Button } from 'antd';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getToken, setToken } from '@/utils/auth';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import styles from './indesx.module.less';

function ChannelHome() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [params] = useSearchParams({
    token: '',
    channelId: '',
    businessInfoId: '',
    formCode: '',
    formVersion: '',
    belongMemberId: '',
    settleType: '',
    qrcodeComId: '',
  });
  const [imageInfo, setImageInfo] = useState({
    companyId: 0,
    id: 0,
    invitePoster: '',
  });
  // const [items, setItems] = useState<any>([]);
  const paramsUrl = useRef({
    channelId: params.get('channelId') || '',
    businessInfoId: params.get('businessInfoId') || '',
    formCode: params.get('formCode') || '',
    formVersion: params.get('formVersion') || '',
    belongMemberId: params.get('belongMemberId') || '',
    settleType: params.get('settleType') || '',
    qrcodeComId: params.get('qrcodeComId') || '',
  });
  const [formCode, setFormCode] = useState(params.get('formCode') || '');

  // const [stateInfo, setStateInfo] = useState({
  //   id: 0,
  //   refuseReason: '',
  //   status: 1,
  // });

  const { run: getImage } = useRequest(channelJoinImgGet, {
    manual: true,
    // defaultParams: [{ appId: params.get('appId') || '' }],
    onSuccess: (result) => {
      setImageInfo({ ...result });
    },
  });

  useEffect(() => {
    const token = params.get('token') || getToken();
    if (token) {
      setToken(token || '');
    }
    setFormCode(params.get('formCode') || '');
    getImage();
    // getForm({ type: 5, appId: params.get('appId') || '' });
  }, [getImage, params]);

  return (
    <div className={styles.body}>
      <Helmet>
        <title>{t('channel_apply_become_partner')}</title>
      </Helmet>
      <div className={styles.image}>
        <img
          src={
            imageInfo.invitePoster
              ? imageInfo.invitePoster
              : 'https://img.huahuabiz.com/user_files/2023524/1684921395994590.png'
          }
          alt=""
        />
      </div>
      {/* {stateInfo.status !== 2 && stateInfo.status !== 3 && ( */}
      <div className={styles.footer}>
        <Button
          type="primary"
          disabled={!formCode}
          onClick={() => {
            // if (!items.length) {
            //   message.warning('该供货商暂无默认表单，不可申请');
            //   return;
            // }
            navigate(
              `/m/channel/join-form?token=${params.get('token')}&channelId=${
                paramsUrl.current.channelId
              }&businessInfoId=${paramsUrl.current.businessInfoId}&formCode=${
                paramsUrl.current.formCode
              }&formVersion=${paramsUrl.current.formVersion}&belongMemberId=${
                paramsUrl.current.belongMemberId
              }&settleType=${paramsUrl.current.settleType}&qrcodeComId=${
                paramsUrl.current.qrcodeComId
              }`
            );
          }}
        >
          {formCode ? t('channel_apply_become_partner') : t('channel_select_invite_form_first')}
          {/* {stateInfo.status === 0 ? '已提交，审核中' : '申请成为渠道商'} */}
        </Button>
      </div>
      {/* )} */}
    </div>
  );
}

export default ChannelHome;
