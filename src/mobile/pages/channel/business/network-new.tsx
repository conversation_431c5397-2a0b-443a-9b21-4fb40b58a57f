import { useEffect } from 'react';
import { useEcharts } from '@/hooks';
import { Echarts } from '@/components';
import classNames from 'classnames';
import { useRequest } from 'ahooks';
import { getChannelBusinessNew } from '@/apis';
import { useTranslation } from 'react-i18next';
import styles from './network-active.module.less';

interface NetworkNewProps {
  businessInfoId: number;
  channelId: number;
}

function NetworkNew({ businessInfoId, channelId }: NetworkNewProps) {
  const { t } = useTranslation();
  const echartsRef = useEcharts();

  const { run } = useRequest(getChannelBusinessNew, {
    manual: true,
    defaultParams: [{ businessInfoId, channelId }],
    onSuccess: (res) => {
      echartsRef.current?.setOption((echarts) => ({
        xAxis: {
          type: 'category',
          data: res.list.map((item) => item.monthValue),
        },
        yAxis: {
          type: 'value',
        },
        grid: {
          left: '3%', // 左边距
          right: '3%', // 右边距
          bottom: '5%', // 底部距离
          top: '5%', // 顶部距离
          containLabel: true, // 包含坐标轴的标签
        },
        series: [
          {
            data: res.list.map((item) => item.newCount),
            type: 'line',
            smooth: true,
            itemStyle: {
              width: 10,
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                { offset: 0, color: '#4DAFFF' },
                { offset: 1, color: '#5CE6DB' },
              ]),
            },
            areaStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                { offset: 0, color: 'rgba(0, 140, 255, .1)' },
                { offset: 1, color: 'rgba(25, 219, 20, .1)' },
              ]),
            },
          },
        ],
      }));
    },
  });

  useEffect(() => {
    if (businessInfoId && channelId) {
      run({ businessInfoId, channelId });
    }
  }, [businessInfoId, channelId, run]);

  return (
    <div className={classNames(styles.card, styles.cardNetwork)}>
      <div className={styles.header}>
        <div className={styles.headerText}>{t('channel_monthly_new_outlets')}</div>
      </div>
      <Echarts ref={echartsRef} style={{ width: '100%', height: '300px' }} />
    </div>
  );
}

export default NetworkNew;
