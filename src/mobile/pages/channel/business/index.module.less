.container {
  color: #040919;
  width: 100%;
  height: 100%;
  padding: 8px 16px 0;
  background-color: #f5f6fa;
  overflow: auto;
}

.card {
  margin-bottom: 16px;
  padding: 0 20px;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
}

.title {
  font-size: 18px;
  font-weight: 600;
  display: flex;
  padding: 20px 0;
  justify-content: space-between;
  align-items: center;
}

.titleTip {
  color: #888b98;
  font-size: 14px;
  font-weight: 400;
  margin-left: 8px;
}

.titleRight {
  display: flex;
  align-items: center;
}

.cell {
  display: flex;
  align-items: center;
  padding: 16px 0;
  justify-content: space-between;
}

.down {
  color: #999eb2;
  margin-left: 4px;
}

.code {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  padding: 16px 0;
  justify-content: space-between;
}

.codeIcon {
  color: #999eb2;
  font-size: 16px;
  font-weight: 400;
}

.filter {
  font-size: 14px;
  font-weight: 400;
}

.filterIcon {
  color: #999eb2;
  font-size: 16px;
  margin-left: 4px;
}

.noDataBusiness {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
}

.dropdown {
  :global {
    .ant-dropdown-menu-item {
      text-align: center;
    }
  }
}
