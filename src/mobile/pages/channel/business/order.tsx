import React, { useEffect, useRef, useState } from 'react';
import { Dropdown, Menu, Progress } from 'antd';
import { DatePicker } from 'antd-mobile';
import { useRequest } from 'ahooks';
import { ChannelBusinessSellResult, getChannelBusinessSell } from '@/apis';
import { Icon } from '@/components';
import dayjs from 'dayjs';
import { getMonthTimestamp } from '@/utils/utils';
import { useTranslation } from 'react-i18next';
import styles from './order.module.less';

interface OrderProps {
  businessInfoId: number;
  channelId: number;
}

function Order({ businessInfoId, channelId }: OrderProps) {
  const { t } = useTranslation();
  const items = [
    {
      key: 1,
      label: t('channel_current_month'),
    },
    {
      key: 2,
      label: t('channel_last_month'),
    },
    {
      key: 3,
      label: t('channel_specify_month'),
    },
  ];
  const [showPicker, setShowPicker] = useState(false);
  const [info, setInfo] = useState<ChannelBusinessSellResult>({
    doingOrderAmount: 0,
    doneOrderAmount: 0,
    totalOrderAmount: 0,
  });
  const paramsRequest = useRef({
    businessInfoId,
    channelId,
    statisticType: 1,
    queryType: 4,
    startDate: '',
    endDate: '',
  });
  const [filterInfo, setFilterInfo] = useState({
    key: 1,
    label: t('channel_current_month'),
  });

  const { run } = useRequest(getChannelBusinessSell, {
    manual: true,
    defaultParams: [{ ...paramsRequest.current }],
    onSuccess: (res) => {
      setInfo(res);
    },
  });

  const onFilter = (val: number) => {
    if (val === 1 || val === 2) {
      const currentInfo = items.find((item) => item.key === val);
      if (currentInfo) {
        setFilterInfo(currentInfo);
        paramsRequest.current = {
          ...paramsRequest.current,
          statisticType: val,
          startDate: '',
          endDate: '',
        };
        run({ ...paramsRequest.current });
      }
    }
    if (val === 3) {
      setShowPicker(true);
    }
  };

  const onSelectTime = (val: string) => {
    const year = dayjs(val).year();
    const month = dayjs(val).month();
    const time = getMonthTimestamp(year, month);
    setFilterInfo({
      key: 3,
      label: `${year}-${month + 1}`,
    });
    paramsRequest.current = {
      ...paramsRequest.current,
      statisticType: 4,
      startDate: `${time.startOfMonth}`,
      endDate: `${time.endOfMonth}`,
    };
    run({ ...paramsRequest.current });
  };

  useEffect(() => {
    if (channelId && businessInfoId) {
      paramsRequest.current = {
        ...paramsRequest.current,
        businessInfoId,
        channelId,
      };
      run({ ...paramsRequest.current });
    }
  }, [businessInfoId, channelId, run]);

  return (
    <>
      <div className={styles.card}>
        <div className={styles.title}>
          <div>{t('channel_sales_order')}</div>
          <Dropdown
            overlayClassName={styles.dropdown}
            trigger={['click']}
            placement="bottom"
            overlay={
              <Menu
                className={styles.menu}
                items={items}
                onClick={({ key }) => {
                  onFilter(Number(key));
                }}
              />
            }
          >
            <div className={styles.filter}>
              <span>{filterInfo.label}</span>
              <Icon name="down" className={styles.filterIcon} />
            </div>
          </Dropdown>
        </div>
        <div className={styles.progress}>
          <Progress
            type="dashboard"
            percent={(info.doingOrderAmount / (info.doingOrderAmount + info.doneOrderAmount)) * 100}
            gapDegree={140}
            strokeWidth={10}
            strokeColor="#16DBCC"
            trailColor="#008CFF"
            // eslint-disable-next-line react/no-unstable-nested-components
            format={() => (
              <div>
                <div className={styles.totalNum}>￥{info.totalOrderAmount}</div>
                <div className={styles.totalText}>{t('channel_total_amount')}</div>
              </div>
            )}
          />
          <div className={styles.stateList}>
            <div>
              <div>
                <span className={styles.line} style={{ background: '#16D9CD' }} />
                <span>{t('channel_in_progress')}</span>
              </div>
              <span className={styles.stateNum}>￥{info.doingOrderAmount}</span>
            </div>
            <div>
              <div>
                <span className={styles.line} style={{ background: '#008CFF' }} />
                <span>{t('channel_completed')}</span>
              </div>
              <span className={styles.stateNum}>￥{info.doneOrderAmount}</span>
            </div>
          </div>
        </div>
      </div>
      <DatePicker
        visible={showPicker}
        precision="month"
        onClose={() => {
          setShowPicker(false);
        }}
        onConfirm={(val: any) => {
          onSelectTime(val);
        }}
      />
    </>
  );
}

export default Order;
