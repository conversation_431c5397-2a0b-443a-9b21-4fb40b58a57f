import React, { useEffect, useMemo, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { Icon, Empty } from '@/components';
import { getChannelBusinessCompany, RelChannelInfoListResult } from '@/apis';
import { Dropdown, Menu } from 'antd';
import { getToken, setToken } from '@/utils/auth';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import Order from './order';
import Ranking from './ranking';
import NetworkActive from './network-active';
import NetworkNew from './network-new';
import Supplier from './supplier';
import styles from './index.module.less';

function Business() {
  const { t } = useTranslation();
  const [params] = useSearchParams({ token: '', formCode: '', formVersion: '' });
  const navigate = useNavigate();
  const [businessInfoId, setBusinessInfoId] = useState(0);
  const [channelList, setChannelList] = useState<RelChannelInfoListResult[]>([]);
  const [currentChannel, setCurrentChannel] = useState<RelChannelInfoListResult>({
    channelComId: 0,
    channelComName: '',
    channelId: 0,
  });
  const [tokenVal, setTokenVal] = useState('');

  const onSelectChannel = (val: number, listVal?: RelChannelInfoListResult[]) => {
    const selectInfo = (listVal || channelList).find((item) => item.channelId === val);
    if (selectInfo) {
      setCurrentChannel(selectInfo);
    }
  };

  const { run } = useRequest(getChannelBusinessCompany, {
    manual: true,
    onSuccess: (res) => {
      setBusinessInfoId(res.businessInfoId);
      setChannelList(res.relChannelInfoList);
      const channelIdVal = localStorage.getItem('CHANNEL_BUSINESS');
      if (res.relChannelInfoList && res.relChannelInfoList.length && !channelIdVal) {
        setCurrentChannel(res.relChannelInfoList[0]);
      }
      if (channelIdVal) {
        onSelectChannel(Number(channelIdVal), res.relChannelInfoList);
        localStorage.removeItem('CHANNEL_BUSINESS');
      }
    },
  });

  const onNavToInvite = () => {
    navigate(
      `/m/channel/invite?channelId=${currentChannel.channelId || ''}&channelComName=${
        currentChannel.channelComName || ''
      }&businessInfoId=${businessInfoId || ''}&token=${params.get('token') || getToken()}`
    );
  };

  useEffect(() => {
    const token = params.get('token') || getToken();
    if (token) {
      setToken(token || '');
      setTokenVal(token || '');
    }
    run();
  }, [params, run]);

  const items = useMemo(
    () =>
      (channelList &&
        channelList.length &&
        channelList.map((item) => ({
          key: item.channelId || 119,
          label: item.channelComName,
        }))) ||
      [],
    [channelList]
  );

  return (
    <div className={styles.container}>
      <Helmet>
        <title>{t('channel_business_manage')}</title>
      </Helmet>
      {!!businessInfoId && !!currentChannel.channelId ? (
        <>
          <div className={styles.card}>
            <div className={styles.cell}>
              <div>{t('channel_company_belong')}</div>
              <Dropdown
                trigger={['click']}
                placement="bottom"
                overlay={
                  <Menu
                    className={styles.menu}
                    items={items}
                    onClick={({ key }) => {
                      onSelectChannel(Number(key));
                    }}
                  />
                }
              >
                <div className={styles.titleRight}>
                  <span>{currentChannel.channelComName}</span>
                  <Icon className={styles.down} name="down" size={16} color="999EB2" />
                </div>
              </Dropdown>
            </div>
          </div>
          <Order businessInfoId={businessInfoId} channelId={currentChannel.channelId} />
          <Ranking
            businessInfoId={businessInfoId}
            channelId={currentChannel.channelId}
            token={tokenVal}
          />
          <NetworkActive businessInfoId={businessInfoId} channelId={currentChannel.channelId} />
          <NetworkNew businessInfoId={businessInfoId} channelId={currentChannel.channelId} />
          <Supplier
            businessInfoId={businessInfoId}
            channelId={currentChannel.channelId}
            token={tokenVal}
          />
          <div role="button" tabIndex={0} className={styles.card} onClick={onNavToInvite}>
            <div className={styles.code}>
              <span>{t('channel_my_invite_code')}</span>
              <Icon name="right" className={styles.codeIcon} />
            </div>
          </div>
        </>
      ) : (
        <div className={styles.noDataBusiness}>
          <Empty />
        </div>
      )}
    </div>
  );
}

export default Business;
