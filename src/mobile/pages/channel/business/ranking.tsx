import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useRequest } from 'ahooks';
import { getChannelBusinessRank, RankDetailListData } from '@/apis';
import { Dropdown, Menu } from 'antd';
import { Icon } from '@/components';
import { useTranslation } from 'react-i18next';
import type { FilterTimeConfirmProps } from './time-filter';
import TimeFilter from './time-filter';
import styles from './ranking.module.less';

interface RankingProps {
  businessInfoId: number;
  channelId: number;
  token: string;
}

const colorList = [
  {
    key: 1,
    color: '#246EFF',
    bgcColor: 'rgba(36, 110, 255, .7)',
  },
  {
    key: 2,
    color: '#16DBCC',
    bgcColor: 'rgba(22, 219, 204, .7)',
  },
  {
    key: 3,
    color: '#FFBB38',
    bgcColor: 'rgba(255, 187, 56, .7)',
  },
];

function Ranking({ businessInfoId, channelId, token }: RankingProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [data, setData] = useState<RankDetailListData[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const paramsRequest = useRef({
    businessInfoId,
    channelId,
    statisticType: 1,
    queryType: 4,
    ifReverse: true,
    startDate: '',
    endDate: '',
  });
  const [filterInfo, setFilterInfo] = useState({
    key: 1,
    label: t('channel_current_month'),
  });
  const [showPicker, setShowPicker] = useState(false);

  const { run } = useRequest(getChannelBusinessRank, {
    manual: true,
    defaultParams: [{ ...paramsRequest.current }],
    onSuccess: (res) => {
      setData(res.rankDetailList);
      setTotalAmount(res.totalAmount);
    },
  });

  const filterItems = [
    {
      key: 1,
      label: t('channel_current_month'),
    },
    {
      key: 2,
      label: t('channel_last_month'),
    },
    {
      key: 3,
      label: t('channel_current_year'),
    },
    {
      key: 4,
      label: t('channel_custom'),
    },
  ];

  const onFilter = (val: number) => {
    if ([1, 2, 3].includes(val)) {
      const currentInfo = filterItems.find((item) => item.key === val);
      if (currentInfo) {
        setFilterInfo(currentInfo);
        paramsRequest.current = {
          ...paramsRequest.current,
          statisticType: val,
          startDate: '',
          endDate: '',
        };
        run({ ...paramsRequest.current });
      }
    }
    if (val === 4) {
      setShowPicker(true);
    }
  };

  const onFilterTime = (val: FilterTimeConfirmProps) => {
    setShowPicker(false);
    setFilterInfo({
      key: 4,
      label: t('channel_custom'),
    });
    paramsRequest.current = {
      ...paramsRequest.current,
      statisticType: 4,
      startDate: val.startTime,
      endDate: val.endTime,
    };
    run({ ...paramsRequest.current });
  };

  const list = useMemo(() => {
    data.forEach((item, index) => {
      const items = item;
      colorList.forEach((each) => {
        if (index + 1 === each.key) {
          items.color = each.color;
          items.bgcColor = each.bgcColor;
        }
      });
      if (index === 0) {
        items.width = '100%';
      }
      if (index === 1) {
        items.width = `${(data[1].currentAmount / data[0].currentAmount) * 100}%`;
      }
      if (index === 2) {
        items.width = `${(data[2].currentAmount / data[0].currentAmount) * 100}%`;
      }
    });
    return data.filter((item, index) => index < 3);
  }, [data]);

  const onNavToRank = () => {
    localStorage.setItem('CHANNEL_BUSINESS', `${channelId}`);
    navigate(
      `/m/channel/sale-rank?businessInfoId=${businessInfoId}&channelId=${channelId}&token=${token}`
    );
  };

  useEffect(() => {
    if (channelId && businessInfoId) {
      paramsRequest.current = {
        ...paramsRequest.current,
        businessInfoId,
        channelId,
      };
      run({ ...paramsRequest.current });
    }
  }, [businessInfoId, channelId, run]);

  return (
    <>
      <div className={styles.card}>
        <div className={styles.title}>
          <div>
            {t('channel_sales_ranking')}
            <span className={styles.titleTip}>
              {t('channel_total_order_amount')}({t('channel_currency')})
            </span>
          </div>
          <Dropdown
            overlayClassName={styles.dropdown}
            trigger={['click']}
            placement="bottom"
            overlay={
              <Menu
                className={styles.menu}
                items={filterItems}
                onClick={({ key }) => {
                  onFilter(Number(key));
                }}
              />
            }
          >
            <div className={styles.filter}>
              <span>{filterInfo.label}</span>
              <Icon name="down" className={styles.filterIcon} />
            </div>
          </Dropdown>
        </div>
        <div role="button" tabIndex={0} onClick={onNavToRank}>
          {list.map((item, index) => (
            <div key={item.nextChannelComId}>
              <div className={styles.item}>
                <div className={styles.itemIndex} style={{ background: item.color }}>
                  {index + 1}
                </div>
                <div className={styles.name}>{item.nextChannelComName}</div>
              </div>
              <div className={styles.numBox}>
                <span
                  className={styles.num}
                  style={{ background: item.bgcColor, width: item.width }}
                >
                  ￥{item.currentAmount}
                </span>
              </div>
            </div>
          ))}
          <div className={styles.total}>
            <div className={styles.totalText}>
              {filterInfo.label}
              {t('channel_total_sales')}({t('channel_currency')})：
            </div>
            <div className={styles.totalNum}>￥{totalAmount}</div>
          </div>
        </div>
      </div>
      <TimeFilter
        visible={showPicker}
        onClose={() => setShowPicker(false)}
        onConfirm={(time) => onFilterTime(time)}
      />
    </>
  );
}

export default Ranking;
