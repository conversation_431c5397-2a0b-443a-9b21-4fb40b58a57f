@import './index.module.less';

.tab {
  display: flex;
  margin-bottom: 30px;
  justify-content: center;
}

.tabList {
  display: flex;
}

.tabItem {
  color: rgb(36 110 255 / 70%);
  display: flex;
  width: 88px;
  height: 36px;
  justify-content: center;
  align-items: center;
  background: #f5f6fa;
  cursor: pointer;

  &:first-child {
    border-radius: 10px 0 0 10px;
  }

  &:last-child {
    border-radius: 0 10px 10px 0;
  }
}

.tabItemActive {
  color: #008cff;
  font-weight: 600;
  background: #d9eeff;
}

.progress {
  display: flex;
  width: 100%;
  margin-top: 10px;
  justify-content: center;
  position: relative;

  :global {
    .ant-progress,
    .ant-progress-inner {
      width: 225px !important;
      height: 225px !important;
    }

    .ant-progress-circle .ant-progress-text {
      margin-top: -5px;
    }
  }
}

.totalNum {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
}

.totalText {
  color: #888b98;
  font-size: 14px;
  font-weight: 400;
  margin-top: 10px;
}

.stateList {
  color: #888b98;
  display: flex;
  width: 100%;
  padding: 0 20px;
  justify-content: space-between;
  position: absolute;
  bottom: 20px;
  left: 0;
}

.line {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.stateNum {
  color: #040919;
  font-size: 16px;
  font-weight: 600;
  display: inline-block;
  width: 60px;
  margin-top: 12px;
  text-align: center;
}
