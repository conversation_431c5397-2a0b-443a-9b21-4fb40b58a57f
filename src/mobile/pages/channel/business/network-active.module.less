@import './index.module.less';

.cardNetwork {
  padding: 0 10px;
}

.header {
  display: flex;
  padding: 16px 10px;
  justify-content: space-between;
}

.headerText {
  font-size: 18px;
  font-weight: 600;
}

.headerRight {
  display: flex;
}

.headerValue {
  color: #888b98;
  font-size: 12px;
  display: flex;
  align-items: center;

  &:first-child {
    margin-right: 20px;
  }
}

.headerColor {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 4px;
  border-radius: 50%;
}
