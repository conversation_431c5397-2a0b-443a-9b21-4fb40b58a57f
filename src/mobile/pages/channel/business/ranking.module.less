@import 'styles/mixins/mixins';
@import './index.module.less';

.item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.itemIndex {
  color: #fff;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  width: 18px;
  height: 18px;
  line-height: 18px;
  margin-right: 8px;
  justify-content: center;
  border-radius: 50%;
}

.filterLabel {
  font-size: 12px;
  display: flex;
  align-items: center;
}

.name {
  margin-top: 2px;
  flex: 1;
  .text-overflow(1);
}

.numBox {
  margin-bottom: 20px;
  padding-left: 26px;
}

.num {
  color: #fff;
  font-weight: 600;
  display: flex;
  height: 32px;
  padding-left: 12px;
  align-items: center;
  border-radius: 0 20px 0 0;
}

.total {
  display: flex;
  padding: 10px 0 20px;
  align-items: center;
  //justify-content: space-between;
}

.totalText {
  color: #888b98;
  //margin-bottom: 4px;
}

.totalNum {
  color: #000;
  font-size: 18px;
  font-weight: 600;
}
