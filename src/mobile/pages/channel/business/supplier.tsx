import { useNavigate } from 'react-router-dom';
import { Icon } from '@/components';
import { useRequest } from 'ahooks';
import { BusinessChannelListResult, getChannelBusinessChannelList } from '@/apis';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import styles from './supplier.module.less';

interface SupplierProps {
  businessInfoId: number;
  channelId: number;
  token: string;
}

function Supplier({ businessInfoId, channelId, token }: SupplierProps) {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const requestParams = useRef({
    businessInfoId,
    channelId,
    pageNo: 1,
    pageSize: 4,
  });
  const [list, setList] = useState<BusinessChannelListResult[]>([]);
  const [totalNum, setTotalNum] = useState(0);

  const { run } = useRequest(getChannelBusinessChannelList, {
    manual: true,
    defaultParams: [{ ...requestParams.current }],
    onSuccess: (res) => {
      setList(res.list);
      setTotalNum(res.pagination.count);
    },
  });

  const onNavToAll = () => {
    localStorage.setItem('CHANNEL_BUSINESS', `${channelId}`);
    navigate(
      `/m/channel/charge?businessInfoId=${businessInfoId}&channelId=${channelId}&token=${token}`
    );
  };

  useEffect(() => {
    if (businessInfoId > 0 && channelId > 0) {
      requestParams.current = {
        ...requestParams.current,
        businessInfoId,
        channelId,
      };
      run({ ...requestParams.current });
    }
  }, [run, businessInfoId, channelId]);

  return list && !!list.length ? (
    <div role="button" tabIndex={0} className={styles.card} onClick={onNavToAll}>
      <div className={styles.header}>
        <div className={styles.headerText}>{t('channel_my_responsible')}</div>
        <div className={styles.headerValue}>
          <span>
            {t('channel_all')}（{totalNum}）
          </span>
          <Icon name="right" />
        </div>
      </div>
      <div>
        {list.map((item) => (
          <div className={styles.item} key={item.nextChannelComName}>
            <img
              className={styles.itemImg}
              src={
                item.logo ||
                'https://img.huahuabiz.com/user_files/1648867805054491492/company%20logo.png'
              }
              alt=""
            />
            <div className={styles.itemText}>
              <div className={styles.itemName}>{item.nextChannelComName}</div>
              <div className={styles.itemTime}>
                {t('channel_join_time')}：{dayjs(item.bindTime).format('YYYY-MM-DD')}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  ) : (
    <div />
  );
}

export default Supplier;
