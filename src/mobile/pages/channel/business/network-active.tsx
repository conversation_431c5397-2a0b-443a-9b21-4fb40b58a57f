import { useEffect, useState } from 'react';
import { Echarts } from '@/components';
import { useEcharts } from '@/hooks';
import classNames from 'classnames';
import { useRequest } from 'ahooks';
import { getChannelBusinessActive } from '@/apis';
import { useTranslation } from 'react-i18next';
import styles from './network-active.module.less';

interface NetworkActiveProps {
  businessInfoId: number;
  channelId: number;
}

function NetworkActive({ businessInfoId, channelId }: NetworkActiveProps) {
  const { t } = useTranslation();
  const echartsRef = useEcharts();
  const [option, setOption] = useState(null);

  const { run } = useRequest(getChannelBusinessActive, {
    manual: true,
    defaultParams: [{ businessInfoId, channelId }],
    onSuccess: (res) => {
      setOption({
        xAxis: {
          type: 'category',
          data: res.list.map((item) => item.monthValue),
        },
        yAxis: {
          type: 'value',
        },
        grid: {
          left: '3%', // 左边距
          right: '3%', // 右边距
          bottom: '5%', // 底部距离
          top: '5%', // 顶部距离
          containLabel: true, // 包含坐标轴的标签
        },
        series: [
          {
            data: res.list.map((item) => item.totalCount),
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              borderRadius: [10, 10, 10, 10],
              color: '#FDAA0A',
            },
          },
          {
            data: res.list.map((item) => item.activeCount),
            type: 'bar',
            barWidth: '10px',
            itemStyle: {
              borderRadius: [10, 10, 10, 10],
              color: '#16DBCC',
            },
          },
        ],
      } as any);
    },
  });

  useEffect(() => {
    if (businessInfoId && channelId) {
      run({ businessInfoId, channelId });
    }
  }, [businessInfoId, channelId, run]);

  return (
    <div className={classNames(styles.card, styles.cardNetwork)}>
      <div className={styles.header}>
        <div className={styles.headerText}>{t('channel_active_outlets')}</div>
        <div className={styles.headerRight}>
          <span className={styles.headerValue}>
            <span className={styles.headerColor} style={{ background: '#FDAA0A' }} />
            <span>{t('channel_total_outlets')}</span>
          </span>
          <span className={styles.headerValue}>
            <span className={styles.headerColor} style={{ background: '#16DBCC' }} />
            <span>{t('channel_active_outlets')}</span>
          </span>
        </div>
      </div>
      <Echarts ref={echartsRef} option={option} style={{ width: '100%', height: '300px' }} />
    </div>
  );
}

export default NetworkActive;
