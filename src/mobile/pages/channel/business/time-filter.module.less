.popup {
  :global {
    .adm-popup-body {
      display: flex;
      border-radius: 12px 12px 0 0;
      flex-direction: column;
    }
  }
}

.title {
  font-size: 16px;
  font-weight: 600;
  display: flex;
  height: 54px;
  padding: 0 16px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f3f3;
}

.titleClose {
  color: #888b98;
  font-size: 18px;
  font-weight: 400;
}

.time {
  display: flex;
  align-items: center;
  padding: 16px;
}

.timeItem {
  display: flex;
  width: 100%;
  height: 32px;
  justify-content: center;
  flex: 1;
  align-items: center;
  border-radius: 8px;
  border: 1px solid #b1b3be;
}

.timeItemActive {
  border: 1px solid #008cff;
}

.timeText {
  padding: 0 10px;
}

.dataPickerView {
  height: 180px;
}

.footer {
  display: flex;
  padding: 12px 16px;
  border-top: 1px solid #f3f3f3;

  :global {
    .ant-btn {
      width: 100%;

      &:first-child {
        margin-right: 16px;
      }
    }
  }
}
