@import './index.module.less';

.header {
  display: flex;
  padding: 16px 0;
  justify-content: space-between;
  align-items: center;
}

.headerText {
  font-size: 18px;
  font-weight: 600;
}

.headerValue {
  color: #888b98;
}

.item {
  display: flex;
  padding: 12px 0;

  &:not(:last-child) {
    border-bottom: 1px solid #f3f3f3;
  }
}

.itemImg {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  margin-right: 8px;
}

.itemText {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.itemName {
  font-size: 16px;
  margin-bottom: 4px;
}

.itemTime {
  color: #888b98;
  font-size: 12px;
}
