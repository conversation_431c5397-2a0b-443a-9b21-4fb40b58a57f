import React, { useState } from 'react';
import { Button } from 'antd';
import { Popup, DatePickerView } from 'antd-mobile';
import { Icon } from '@/components';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { timestampToUTC } from '@/utils/utils';
import { useTranslation } from 'react-i18next';
import styles from './time-filter.module.less';

export interface FilterTimeConfirmProps {
  start: any;
  startTime: string;
  end: any;
  endTime: string;
}

interface SelectFormProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: MultipleParamsFn<[time: FilterTimeConfirmProps]>;
}

function TimeFilter({ visible, onClose, onConfirm }: SelectFormProps) {
  const { t } = useTranslation();
  const initTime = {
    start: timestampToUTC(new Date().getTime() - 2592000000),
    startTime: dayjs().startOf('day').valueOf() - 2592000000,
    end: new Date(),
    endTime: dayjs().endOf('day').valueOf(),
  };

  const [selectType, setSelectType] = useState('start');
  const [time, setTime] = useState<any>({ ...initTime });

  const onDataChange = (val: any) => {
    if (selectType === 'start') {
      setTime({ ...time, startTime: dayjs(val).startOf('day').valueOf() as any, start: val });
    } else {
      setTime({ ...time, endTime: dayjs(val).endOf('day').valueOf() as any, end: val });
    }
  };

  const onInitData = () => {
    setTime({ ...initTime });
  };

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      onMaskClick={onClose}
      className={styles.popup}
    >
      <div className={styles.title}>
        <span />
        <span>{t('channel_custom_time')}</span>
        <Icon name="close" className={styles.titleClose} onClick={onClose} />
      </div>
      <div className={styles.time}>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.timeItem, {
            [styles.timeItemActive]: selectType === 'start',
          })}
          onClick={() => setSelectType('start')}
        >
          {time.startTime ? dayjs(time.startTime).format('YYYY-MM-DD') : t('channel_start_time')}
        </div>
        <div className={styles.timeText}>{t('channel_to')}</div>
        <div
          role="button"
          tabIndex={0}
          className={classNames(styles.timeItem, {
            [styles.timeItemActive]: selectType === 'end',
          })}
          onClick={() => setSelectType('end')}
        >
          {time.endTime ? dayjs(time.endTime).format('YYYY-MM-DD') : t('channel_end_time')}
        </div>
      </div>
      {selectType === 'start' && (
        <DatePickerView
          className={styles.dataPickerView}
          defaultValue={new Date()}
          value={time.start}
          onChange={onDataChange}
        />
      )}
      {selectType === 'end' && (
        <DatePickerView
          className={styles.dataPickerView}
          defaultValue={new Date()}
          value={time.end}
          onChange={onDataChange}
        />
      )}
      <div className={styles.footer}>
        <Button onClick={onInitData}>{t('channel_reset_to_empty')}</Button>
        <Button type="primary" onClick={() => onConfirm(time)}>
          {t('channel_confirm')}
        </Button>
      </div>
    </Popup>
  );
}

export default TimeFilter;
