import { HTMLAttributes } from 'react';
// import { useMemoizedFn } from 'ahooks';
// import { addWebIframe } from '@echronos/core';
import styles from './index.module.less';

interface PropsType extends HTMLAttributes<HTMLIFrameElement> {
  name?: string;
  src: string;
}

function Iframe({ name, title, onLoad, ...props }: PropsType) {
  // const onLoadIframe: ReactEventHandler<HTMLIFrameElement> = useMemoizedFn((e) => {
  //   const ele = e.target as HTMLIFrameElement | null;
  //   if (ele) {
  //     addWebIframe(ele);
  //   }
  //   onLoad?.(e);
  // });

  return <iframe {...props} name={name} title={title} className={styles.iframe} />;
}

Iframe.defaultProps = {
  name: 'SystemSettingIframe',
  // eslint-disable-next-line react/default-props-match-prop-types
  title: 'SystemSettingIframe',
};

export default Iframe;
