import React, { useEffect, useRef, useState } from 'react';
import { Helmet } from 'react-helmet';
import { Icon, Search, Empty } from '@/components';
import { useRequest } from 'ahooks';
import { getChannelBusinessRank, RankDetailListData } from '@/apis';
import { useSearchParams } from 'react-router-dom';
import { debounce } from 'lodash';
import { Dropdown, Menu } from 'antd';
import TimeFilter, { FilterTimeConfirmProps } from '@@/mobile/pages/channel/business/time-filter';
import { getToken, setToken } from '@/utils/auth';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';
import styles from './index.module.less';

const filterItems = [
  {
    key: 1,
    label: i18next.t('channel_current_month'),
  },
  {
    key: 2,
    label: i18next.t('channel_last_month'),
  },
  {
    key: 3,
    label: i18next.t('channel_current_year'),
  },
  {
    key: 4,
    label: i18next.t('channel_custom'),
  },
];

const filterOrder = [
  {
    key: 1,
    label: i18next.t('channel_amount_high_to_low'),
  },
  {
    key: 2,
    label: i18next.t('channel_amount_low_to_high'),
  },
];

function SaleRank() {
  const { t } = useTranslation();
  const [params] = useSearchParams({ token: '', channelId: '', businessInfoId: '' });
  const paramsRequest = useRef({
    businessInfoId: params.get('businessInfoId') || '',
    channelId: params.get('channelId') || '',
    statisticType: 1,
    queryType: 4,
    ifReverse: true,
    startDate: '',
    endDate: '',
    key: '',
  });
  const [data, setData] = useState<RankDetailListData[]>([]);
  const [filterInfo, setFilterInfo] = useState({
    key: 1,
    label: t('channel_current_month'),
  });
  const [filterData, setFilterData] = useState({
    key: 1,
    label: t('channel_amount_high_to_low'),
  });
  const [showPicker, setShowPicker] = useState(false);

  const { run } = useRequest(getChannelBusinessRank, {
    manual: true,
    defaultParams: [{ ...paramsRequest.current }],
    onSuccess: (res) => {
      setData(res.rankDetailList);
    },
  });

  const onSearch = debounce((e) => {
    paramsRequest.current.key = e;
    run({ ...paramsRequest.current });
  }, 500);

  const onFilter = (val: number) => {
    if ([1, 2, 3].includes(val)) {
      const currentInfo = filterItems.find((item) => item.key === val);
      if (currentInfo) {
        setFilterInfo(currentInfo);
        paramsRequest.current = {
          ...paramsRequest.current,
          statisticType: val,
          startDate: '',
          endDate: '',
        };
        run({ ...paramsRequest.current });
      }
    }
    if (val === 4) {
      setShowPicker(true);
    }
  };

  const onFilterData = (val: number) => {
    const currentInfo = filterOrder.find((item) => item.key === val);
    if (currentInfo) {
      setFilterData(currentInfo);
    }
    paramsRequest.current = {
      ...paramsRequest.current,
      ifReverse: val === 1,
    };
    run({ ...paramsRequest.current });
  };

  const onFilterTime = (val: FilterTimeConfirmProps) => {
    setShowPicker(false);
    setFilterInfo({
      key: 4,
      label: t('channel_custom'),
    });
    paramsRequest.current = {
      ...paramsRequest.current,
      statisticType: 4,
      startDate: val.startTime,
      endDate: val.endTime,
    };
    run({ ...paramsRequest.current });
  };

  useEffect(() => {
    const token = params.get('token') || getToken();
    if (token) {
      setToken(token || '');
    }
    run({ ...paramsRequest.current });
  }, [params, run]);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>{t('channel_sales_ranking')}</title>
      </Helmet>
      <div className={styles.header}>
        <Search placeholder={t('channel_enter_supplier_name')} onSearch={onSearch} />
        <div className={styles.filter}>
          <Dropdown
            overlayClassName={styles.dropdown}
            trigger={['click']}
            placement="bottomRight"
            overlay={
              <Menu
                className={styles.menu}
                items={filterItems}
                onClick={({ key }) => {
                  onFilter(Number(key));
                }}
              />
            }
          >
            <span className={styles.filterTime}>
              <span>{filterInfo.label}</span>
              <Icon name="down" className={styles.filterIcon} />
            </span>
          </Dropdown>
          <Dropdown
            overlayClassName={styles.dropdown}
            placement="bottomRight"
            trigger={['click']}
            overlay={
              <Menu
                className={styles.menu}
                items={filterOrder}
                onClick={({ key }) => {
                  onFilterData(Number(key));
                }}
              />
            }
          >
            <span className={styles.filterTime}>
              <span>{filterData.label}</span>
              <Icon name="down" className={styles.filterIcon} />
            </span>
          </Dropdown>
        </div>
      </div>
      <div className={styles.content}>
        {data && !!data.length ? (
          data.map((item, index) => (
            <div className={styles.card} key={item.nextChannelComId}>
              <div className={styles.title}>
                {index < 3 && <div className={styles.titleNum}>{index + 1}</div>}
                <div className={styles.titleText}>{item.nextChannelComName}</div>
              </div>
              <div className={styles.cell}>
                <div className={styles.label}>
                  {t('channel_order_amount')}（{t('channel_currency')}）：
                </div>
                <div>{item.currentAmount}</div>
              </div>
            </div>
          ))
        ) : (
          <div className={styles.noData}>
            <Empty />
          </div>
        )}
      </div>
      <TimeFilter
        visible={showPicker}
        onClose={() => setShowPicker(false)}
        onConfirm={(time) => onFilterTime(time)}
      />
    </div>
  );
}

export default SaleRank;
