import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { getPaveGoodsDetail, PaveGoodsDetailResult, postPaveGoodsCancel } from '@/apis';
import { useRequest } from 'ahooks';
import { Button, message } from 'antd';
import classNames from 'classnames';
import { Helmet } from 'react-helmet';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import FileList from '@/src/mobile/components/file-list';
import { initPermission, testPermMobile } from '@/utils/permission';
import { Modal, FileListItem } from '@/components';
import selectPaveWarehouse from './select-pave-warehouse';
import styles from './index.module.less';

function PaveGoodsDetail() {
  const { t } = useTranslation();
  const [params] = useSearchParams();
  const navigate = useNavigate();
  const id = params.get('id') || '';
  const isChannel = params.get('isChannel') || ''; // 是否是渠道商过来的
  const [info, setInfo] = useState<PaveGoodsDetailResult>({} as PaveGoodsDetailResult);

  // 获取详情信息
  const { run } = useRequest(getPaveGoodsDetail, {
    manual: true,
    onSuccess: (res) => {
      setInfo(res);
    },
  });

  // 去编辑
  const onEdit = () => {
    if (!testPermMobile('BH_001_001_005_001_001_001')) {
      return;
    }
    localStorage.removeItem('PAVE_GOODS_CREATE_INFO');
    localStorage.removeItem('SELECT_GOODS_MOBILE');
    navigate(`/m/channel/pave-goods-create?id=${id}`);
  };

  // 去铺货回收
  const onRecycle = () => {
    selectPaveWarehouse({
      id: info.arrangeInfoId,
      title: t('channel_select_recycle_warehouse'),
      onConfirm: () => {
        run({ arrangeInfoId: Number(id) });
      },
    });
  };

  // 取消
  const onCancel = () => {
    if (!testPermMobile('BH_001_001_005_001_002')) {
      return;
    }
    Modal.confirm({
      title: t('channel_tip'),
      content: t('channel_confirm_cancel'),
      centered: true,
      width: '80%',
      wrapClassName: styles.modalBox,
      onOk: () => {
        postPaveGoodsCancel({ arrangeInfoId: info.arrangeInfoId }).then(() => {
          message.success(t('channel_cancel_success'));
          run({ arrangeInfoId: Number(id) });
        });
      },
    });
  };

  const onDefine = () => {};

  const onReject = () => {};

  useEffect(() => {
    run({ arrangeInfoId: Number(id) });
  }, [id, run]);

  useEffect(() => {
    initPermission();
  }, []);

  return (
    <div className={styles.page}>
      <Helmet>
        <title>{t('channel_distribution_detail')}</title>
      </Helmet>
      <div className={styles.container}>
        <div className={styles.card}>
          <div className={styles.title}>{t('channel_distribution_order_info')}</div>
          <div className={styles.cell}>
            <div className={styles.label}>
              {t('channel_distribution')}
              {Number(isChannel) ? t('channel_unit') : t('channel_channel')}：
            </div>
            <div className={styles.value}>
              {Number(isChannel) ? info.arrangeComName : info.receiveComName}
            </div>
          </div>
          <div className={styles.cell}>
            <span className={styles.label}>{t('channel_distribution_order_no')}：</span>
            <span>{info.arrangeNo}</span>
          </div>
          {!!Number(isChannel) && (
            <div className={styles.cell}>
              <span className={styles.label}>{t('channel_receiving_unit')}：</span>
              <span className={styles.value}>{info.receiveComName}</span>
            </div>
          )}
          {!!Number(isChannel) && [1, 2].includes(info.status) && (
            <div className={styles.cell}>
              <span className={styles.label}>
                {t('channel_confirm')}
                {info.status === 2 ? t('channel_reject') : t('channel_accept')}
                {t('channel_time')}：
              </span>
              <span className={styles.value}>
                {info.receiveTime ? dayjs(info.receiveTime).format('YYYY-MM-DD') : '--'}
              </span>
            </div>
          )}
          {!Number(isChannel) && (
            <div className={styles.cell}>
              <span className={styles.label}>{t('channel_distribution_warehouse')}：</span>
              <span>{info.arrangeWarehouseName}</span>
            </div>
          )}
          {!Number(isChannel) && (
            <div className={styles.cell}>
              <span className={styles.label}>{t('channel_operator')}：</span>
              <span>{info.operatorName}</span>
            </div>
          )}
          <div className={styles.cell}>
            <span className={styles.label}>{t('channel_distribution_time')}：</span>
            <span className={styles.value}>
              {info.arrangeTime ? dayjs(info.arrangeTime).format('YYYY-MM-DD') : '--'}
            </span>
          </div>
          {!!info.recycleTime && (
            <div className={styles.cell}>
              <span className={styles.label}>{t('channel_recall_time')}：</span>
              <span>{info.recycleTime ? dayjs(info.recycleTime).format('YYYY-MM-DD') : '--'}</span>
            </div>
          )}
        </div>
        {info.rejectReason && (
          <div className={classNames(styles.card, styles.cardReject)}>
            {t('channel_reject_reason')}：{info.rejectReason}
          </div>
        )}
        <div className={styles.card}>
          <div className={styles.title}>{t('channel_product_list')}</div>
          {info.arrangeProductList &&
            !!info.arrangeProductList.length &&
            info.arrangeProductList.map((item) => (
              <div className={styles.item}>
                <div className={styles.goodsItem}>
                  <img
                    className={styles.goodsImg}
                    src={
                      item.productMainImage ||
                      'https://img.huahuabiz.com/default/image/default_holder.png'
                    }
                    alt=""
                  />
                  <div className={styles.content}>
                    <div className={styles.goodsName}>{item.productName}</div>
                    <div className={styles.standard}>
                      {item.productSpecificationList.map((each) => (
                        <span key={each.name} className={styles.goodsStandardItem}>
                          {each.value}
                        </span>
                      ))}
                    </div>
                    {/* <GoodsItemCard.Standard */}
                    {/*  className={styles.standard} */}
                    {/*  goodsStandard={item.productSpecificationList || []} */}
                    {/* /> */}
                  </div>
                </div>
                <div className={classNames(styles.unitNum, styles.unitNumTitle)}>
                  <span className={styles.unitNumItem}>{t('channel_unit')}</span>
                  <span className={styles.unitNumItem}>{t('channel_quantity')}</span>
                </div>
                <div className={styles.unitNum}>
                  <span className={styles.unitNumItem}>{item.productUnit}</span>
                  <span className={styles.unitNumItem}>{item.productNumber}</span>
                </div>
              </div>
            ))}
          <div className={styles.goodsFooter}>
            {t('channel_total')}
            <span className={styles.goodsFooterNum}>
              {info.arrangeProductList && info.arrangeProductList.length}
            </span>
            {t('channel_types_of_products')}
          </div>
        </div>
        {info.attachmentList && info.attachmentList.length > 0 && (
          <div className={styles.card}>
            <div className={styles.title}>合同附件</div>
            <FileList
              fileList={info.attachmentList as FileListItem[]}
              download
              className={styles.fileList}
              onCopyDownloadUrl={(file) => {
                // 临时替代下载方案
                // 复制文件url到剪贴板
                if (file.url) {
                  // 创建临时输入框
                  const input = document.createElement('input');
                  input.setAttribute('readonly', 'readonly');
                  input.setAttribute('value', file.url);
                  document.body.appendChild(input);
                  input.select();
                  input.setSelectionRange(0, 9999);
                  try {
                    if (document.execCommand('copy')) {
                      message.success('复制成功，可在浏览器中打开链接下载');
                    } else {
                      message.error('复制失败，请使用电脑端操作');
                    }
                  } catch (err) {
                    message.error('复制失败，请使用电脑端操作');
                  }
                  document.body.removeChild(input);
                }
              }}
            />
          </div>
        )}
      </div>
      {(info.ifCanAudit || info.ifCanAuditRecycle || info.ifCanEdit || info.ifCanApplyRecycle) && (
        <div className={styles.footer}>
          {(info.ifCanAudit || info.ifCanAuditRecycle) && (
            <>
              <Button className="mr-4" onClick={onReject}>
                {t('channel_reject')}
              </Button>
              <Button type="primary" onClick={onDefine}>
                {info.ifCanAudit ? t('channel_confirm_receive') : t('channel_accept_recall')}
              </Button>
            </>
          )}
          {info.ifCanEdit && (
            <>
              <Button onClick={onCancel} className="mr-4">
                {t('channel_cancel')}
              </Button>
              <Button type="primary" onClick={onEdit}>
                {t('channel_edit')}
              </Button>
            </>
          )}
          {info.ifCanApplyRecycle && (
            <Button type="primary" onClick={onRecycle}>
              {t('channel_distribution_recall')}
            </Button>
          )}
        </div>
      )}
    </div>
  );
}

export default PaveGoodsDetail;
