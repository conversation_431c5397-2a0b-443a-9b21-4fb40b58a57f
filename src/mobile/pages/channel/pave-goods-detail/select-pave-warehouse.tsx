import { Modal } from '@/components';
import { message, ModalProps } from 'antd';
import { modalPopup } from '@/utils/popup';
import React, { useState } from 'react';
import { isFunction } from 'lodash';
import { postPaveGoodsRemove } from '@/apis';
import SelectWarehouse from '@@/mobile/pages/channel/pave-goods-create/select-warehouse';
import { useTranslation } from 'react-i18next';
import styles from './select-pave-warehouse.module.less';

interface SelectPaveWarehouseProps extends ModalProps {
  title: string;
  id: number;
  onConfirm: () => void;
}

function SelectPaveWarehouse({ title, id, onConfirm, ...props }: SelectPaveWarehouseProps) {
  const { t } = useTranslation();
  const [warehouseId, setWarehouseId] = useState(0);
  const [warehouseName, setWarehouseName] = useState('');

  const onOk = () => {
    if (!warehouseId) {
      message.warning(t('channel_select_warehouse_first'));
      return;
    }
    postPaveGoodsRemove({
      arrangeInfoId: id,
      inWarehouseId: warehouseId,
    }).then(() => {
      message.success(t('channel_submit_success'));
      if (isFunction(props.onCancel)) {
        props.onCancel();
      }
      onConfirm();
    });
  };

  return (
    <Modal
      {...props}
      width="80%"
      className={styles.modal}
      title={title}
      onOk={onOk}
      closable={false}
      okText={t('channel_confirm')}
      cancelText={t('channel_cancel')}
    >
      <SelectWarehouse
        title={t('channel_select_recycle_warehouse')}
        type="back"
        name={warehouseName}
        onChangeWarehouseNo={() => {}}
        onConfirm={(val, name) => {
          setWarehouseId(val);
          setWarehouseName(name);
        }}
      />
    </Modal>
  );
}

const selectPaveWarehouse = (config: SelectPaveWarehouseProps) => {
  modalPopup(SelectPaveWarehouse, { ...config });
};

export default selectPaveWarehouse;
