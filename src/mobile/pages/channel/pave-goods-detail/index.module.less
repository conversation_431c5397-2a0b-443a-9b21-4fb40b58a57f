@import 'styles/mixins/mixins';

.page {
  color: #040919;
  display: flex;
  height: 100%;
  flex-direction: column;
  background-color: #f5f6fa;
  padding-top: 8px;
}

.container {
  flex: 1;
  padding: 0 16px;
  overflow: auto;
}

.card {
  border-radius: 12px;
  background: #fff;
  box-shadow: 0 4px 20px 0 rgb(57 61 72 / 5.88%);
  margin-bottom: 16px;
  padding: 16px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

.cell {
  display: flex;

  &:not(:last-child) {
    margin-bottom: 8px;
  }
}

.label {
  color: #888b98;
  margin-right: 8px;
}

.value {
  flex: 1;
}

.item {
  margin-bottom: 16px;
  padding-bottom: 4px;
  border-bottom: 1px solid #f3f3f3;
}

.goodsItem {
  display: flex;
  margin-bottom: 16px;
}

.goodsImg {
  width: 88px;
  height: 88px;
  border-radius: 6px;
  margin-right: 12px;
  object-fit: contain;
}

.content {
  flex: 1;
  display: flex;
  justify-content: center;
  flex-direction: column;
}

.goodsName {
  font-size: 12px;
  //font-weight: 600;
  margin-bottom: 8px;
  word-break: break-all;
  .text-overflow(3);
}

.standard {
  color: #888b98;
  font-size: 12px;
  word-break: break-all;
  .text-overflow(2);
}

.goodsStandardItem {
  margin-right: 6px;

  &:last-child {
    margin-right: 0;
  }
}

.unitNum {
  display: flex;
  width: 100%;
  height: 28px;
  align-items: center;
  border-radius: 8px;
}

.unitNumTitle {
  background: #fafafc;
  margin-bottom: 4px;
}

.unitNumItem {
  flex: 1;
  text-align: center;
}

.goodsFooter {
  text-align: right;
}

.goodsFooterNum {
  font-size: 18px;
  font-weight: 600;
  margin: 0 2px;
}

.footer {
  display: flex;
  padding: 16px;

  :global {
    .ant-btn {
      width: 100%;
    }
  }
}

.modalBox {
  :global {
    .ant-modal-confirm-title {
      text-align: center !important;
    }

    .ant-modal-confirm-content {
      margin-bottom: 20px;
      text-align: center !important;
    }

    .ant-modal-confirm-btns {
      display: flex;
      justify-content: space-between;
      float: none !important;
    }

    .ant-btn {
      width: 100%;
    }
  }
}
