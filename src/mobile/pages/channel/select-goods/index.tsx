import {
  GetCategoryCustomeItemResult,
  getCategoryCustomeList,
  orderGoodsStandard<PERSON>ilter,
  stockGoodsList,
  StockGoodsListResult,
} from '@/apis';
import { OrderGoodsStandardFilterResult } from '@/apis/psi/order-goods-standard-filter';
import { Search } from '@/components';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import { Button, Checkbox } from '@echronos/antd';
import Icon from '@echronos/echos-icon';
import { useMemoizedFn } from 'ahooks';
import { InfiniteScroll, Space } from 'antd-mobile';
import classNames from 'classnames';
import { debounce } from 'lodash';
import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';
import SelectGoodsFilter from './select-goods-filter';

interface SelectCategoryProps {
  pageNo: number;
  pageSize: number;
  parentId?: number;
  keyword?: string;
  customizeCategorySet?: number[];
  clear?: boolean;
}
export interface SearchCateory {
  shopSkuNames: string[];
  standardList: { name: string; value: string[] }[];
}
function SelectGoodsPage() {
  const { t } = useTranslation();
  // 过滤选择
  const [filterSelect, setFilterSelect] = useState<SearchCateory>({
    shopSkuNames: [],
    standardList: [],
  });
  const [params] = useSearchParams();
  const warehouseNo = params.get('warehouseNo') || '';
  // 数据
  const [categorylist, setCategorylist] = useState<GetCategoryCustomeItemResult[]>([]);
  const [secondCategorylist, setSecondCategorylist] = useState<GetCategoryCustomeItemResult[]>([]);
  const [filter, setFilter] = useState<OrderGoodsStandardFilterResult>();
  const [goodslist, setGoodslist] = useState<StockGoodsListResult[]>([]);
  // 初始化
  const [goodsLoading, setGoodsLoading] = useState(true);
  const [loding, setLoding] = useState<boolean>(true);
  const [filterLoading, setFilterLoading] = useState<boolean>(true);
  // 无限滚动
  const [hasMore, setHasMore] = useState(true);
  const [hasMoreGoods, setHasMoreGoods] = useState(true);
  // 分类
  const [mainselect, setMainselect] = useState<number | null>(null);
  const [secondSelect, setSecondSelect] = useState<number | null>(null);
  const [selectGoods, setSelectGoods] = useState<StockGoodsListResult[]>([]);
  // 抽屉
  const [popup, setPopup] = useState(false);

  const navigator = useNavigate();
  // 分页获取分类
  const fetchCategory = useMemoizedFn(async (pramge: SelectCategoryProps) => {
    const categoryData = await getCategoryCustomeList(pramge);
    if (categorylist?.[0]?.categoryName !== t('channel_all'))
      categoryData.list.unshift({
        id: null,
        categoryName: t('channel_all'),
        code: '0',
      });
    if (pramge.pageNo * 20 >= categoryData.pagination.count) setHasMore(false);
    setCategorylist(categorylist.concat(categoryData.list));
  });

  // 二级分类
  const fetchSecondCategory = useMemoizedFn(async (parentId: number) => {
    const secondCategoryData = await getCategoryCustomeList({ pageNo: 1, pageSize: 999, parentId });
    setSecondCategorylist(secondCategoryData.list);
  });

  // 获取商品
  const fetchGoods = useMemoizedFn(async (pramge: SelectCategoryProps) => {
    let customizeCategorySet: number[] = [];
    if (mainselect) customizeCategorySet = [mainselect];
    if (secondSelect) customizeCategorySet = [secondSelect];
    // 添加搜索选择
    const goodData = await stockGoodsList({
      ...pramge,
      customizeCategorySet,
      shopSkuNames: filterSelect.shopSkuNames,
      standardFilterParams: filterSelect.standardList,
      warehouseNo,
      businessSourceType: 0,
      isInventoryType: 0,
      isQuerySelfSku: 1,
      isWarehouseFilter: 1,
      sort: 'desc',
    });
    if (pramge.pageNo * 10 >= goodData.pagination.count) setHasMoreGoods(false);
    setGoodsLoading(false);
    if (pramge.clear) setGoodslist(goodData.list);
    else setGoodslist(goodslist.concat(goodData.list));
  });
  // 获取分类
  const fetchFilter = useMemoizedFn(async () => {
    setFilterLoading(true);
    const FilterData = await orderGoodsStandardFilter({
      sort: 'desc',
      businessSourceType: 1,
      customizeCategorySet: [],
    });
    setFilterLoading(false);
    setFilter(FilterData);
  });

  // 搜索防抖
  const fetchSearch = useMemoizedFn(
    debounce(async (value: string) => {
      setGoodsLoading(true);
      await fetchGoods({ pageNo: 1, pageSize: 10, keyword: value, clear: true });
    }, 500)
  );

  const hanleClick = useMemoizedFn(() => {
    if (selectGoods.length === 0) return;
    // navigator(`/m/channel/pave-goods-create?id=${id}`, { replace: true });
    navigator(-1);
    localStorage.setItem('SELECT_GOODS_MOBILE', JSON.stringify(selectGoods));
  });

  // 初始化
  useEffect(() => {
    (async () => {
      const Fetch = Promise.all([
        fetchCategory({ pageNo: 1, pageSize: 20 }),
        fetchGoods({ pageNo: 1, pageSize: 10 }),
      ]);
      await Fetch;
      setLoding(false);
      setGoodsLoading(false);
      const hasSelect = localStorage.getItem('SELECT_GOODS_MOBILE');
      if (hasSelect && hasSelect.length) {
        setSelectGoods(JSON.parse(hasSelect));
      }
    })();
  }, [fetchCategory, fetchGoods]);

  // 二级分类
  useEffect(() => {
    if (loding || goodsLoading) return;
    if (!mainselect) return;
    (async () => {
      await fetchSecondCategory(mainselect);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mainselect, fetchSecondCategory]);
  // 选择分类
  useEffect(() => {
    if (loding || goodsLoading) return;
    setGoodsLoading(true);
    (async () => {
      await fetchGoods({ pageNo: 1, pageSize: 10, clear: true });
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mainselect, secondSelect, fetchGoods]);

  // 获取过滤字段
  useEffect(() => {
    if (popup) fetchFilter();
  }, [popup, fetchFilter]);

  return (
    <div className={styles.select} style={{ scrollbarWidth: 'none' }}>
      <Helmet>
        <title>{t('channel_select_goods')}</title>
      </Helmet>
      {/* 头部 */}
      <header className={styles.selectHeader}>
        <Search
          placeholder={t('channel_enter_product_name')}
          type="text"
          className={styles.selectInput}
          onSearch={(value) => fetchSearch(value)}
          style={{ backgroundColor: '#F5F6FA' }}
        />
        {/* 过滤 */}
        <Space>
          <Icon name="filter_line" size={20} onClick={() => setPopup(true)} />
        </Space>
        {filter && (
          <SelectGoodsFilter
            goodsLoading={goodsLoading}
            loding={loding}
            filterSelect={filterSelect}
            setFilterSelect={setFilterSelect}
            fetchGoods={fetchGoods}
            filterLoading={filterLoading}
            popup={popup}
            setPopup={setPopup}
            setGoodsLoading={setGoodsLoading}
            filter={filter}
          />
        )}
      </header>
      {/* 主体 */}
      <main className={styles.selectMain}>
        {/* 一级菜单 */}
        <section className={styles.selectCategory}>
          {!loding && (
            <>
              {categorylist.map((item) => (
                <button
                  key={item.code}
                  type="button"
                  className={classNames(
                    styles.selectCategoryButton,
                    mainselect === item.id && styles.selectCategoryActive
                  )}
                  onClick={() => {
                    if (item.categoryName === t('channel_all')) {
                      setMainselect(null);
                      setSecondSelect(null);
                    } else setMainselect(item.id);
                  }}
                >
                  {item.categoryName}
                </button>
              ))}
              {!loding && (
                <InfiniteScroll
                  loadMore={() =>
                    fetchCategory({
                      pageSize: 20,
                      pageNo: Math.floor((categorylist?.length as number) / 20) + 1,
                    })
                  }
                  hasMore={hasMore}
                >
                  {hasMore && <LoadingOutlined />}
                </InfiniteScroll>
              )}
            </>
          )}
        </section>
        {/* 二级菜单和商品 */}
        <section className={styles.selectGoods}>
          {secondCategorylist && !!secondCategorylist.length && (
            <div className={styles.selectGoodsCategory}>
              {/* 二级请求 */}
              {secondCategorylist.map((item) => (
                <button
                  key={item.code}
                  type="button"
                  className={classNames(
                    styles.selectGoodsButton,
                    secondSelect === item.id && styles.selectGoodsActive
                  )}
                  onClick={() => {
                    if (item.id === secondSelect) setSecondSelect(null);
                    else setSecondSelect(item.id);
                  }}
                >
                  {item.categoryName}
                </button>
              ))}
            </div>
          )}
          {/* 商品 */}
          <div className={styles.selectGoodsList}>
            {goodsLoading ? (
              <LoadingOutlined className={styles.selectLoad} />
            ) : (
              <>
                {goodslist.map((item) => (
                  <section key={item.id} className={styles.selectGoodsItem}>
                    <img
                      className={styles.selectGoodsImage}
                      src={item.imagesList[0]}
                      alt={t('channel_product_image')}
                    />
                    <div className={styles.selectGoodsInfo}>
                      <div className={styles.selectGoodsHeader}>{item.name}</div>
                      <p className={styles.selectGoodsDescription}>
                        {item.standardList.map((each) => (
                          <span key={each.name} className={styles.selectGoodsDescriptionItem}>
                            {each.value}
                          </span>
                        ))}
                      </p>
                      {/* <p className={styles.selectGoodsDescription}>库存 {item.stock}</p> */}
                      {/* <p className={styles.selectGoodsDescription}>最小销售单元 {item.saleGroup}</p> */}
                    </div>
                    <div className={styles.selectGoodsCheckbox}>
                      <Checkbox
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectGoods([...selectGoods, item]);
                          } else {
                            setSelectGoods(
                              selectGoods.filter((itemSelect) => itemSelect.id !== item.id)
                            );
                          }
                        }}
                        // @ts-ignore
                        checked={selectGoods.find((itemSelect) => itemSelect.id === item.id)}
                      />
                    </div>
                  </section>
                ))}
                {!loding && !goodsLoading && (
                  <InfiniteScroll
                    loadMore={() =>
                      fetchGoods({
                        pageSize: 10,
                        pageNo: Math.floor(goodslist?.length as number) / 10 + 1,
                      })
                    }
                    hasMore={hasMoreGoods}
                  />
                )}
              </>
            )}
          </div>
        </section>
      </main>
      {/* 底部 */}
      <footer className={styles.selectFooter}>
        <img
          src="https://img.huahuabiz.com/user_files/20241113/1731461616778455.png"
          alt={t('channel_shopping_cart')}
          className={styles.selectFooterIcon}
        />
        <p className={styles.selectFooterText}>
          {t('channel_selected')}
          <span className={styles.selectFooterSum}>{selectGoods.length}</span>
        </p>
        <Button
          className={styles.selectFooterButton}
          disabled={selectGoods.length === 0}
          onClick={hanleClick}
        >
          {t('channel_done_selecting')}
        </Button>
      </footer>
    </div>
  );
}
export default SelectGoodsPage;
