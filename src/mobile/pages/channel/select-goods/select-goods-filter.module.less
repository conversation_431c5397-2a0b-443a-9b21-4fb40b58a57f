.popup {
  :global {
    .adm-popup-body {
      border-radius: 12px 12px 0 0;
    }
  }
}

.select {
  &Load {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  &Input {
    :global .ant-input {
      border-radius: 16px 16px 0 0;
      background-color: #f5f6fa;
    }

    &Icon {
      color: #999eb2;
      position: absolute;
      left: 28px;
    }
  }

  &Popup {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    overflow: scroll;
    scrollbar-width: none;

    &Show {
      color: #888b98;
      background: none;
      border: none;
      font-size: 12px;
      font-weight: 400;
      cursor: pointer;
    }

    &Value {
      display: grid;
      // max-height: 100px;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      overflow: scroll;
      scrollbar-width: none;

      &Item {
        display: block;
        width: 100%;
        border: #f3f3f3 solid 1px;
        max-width: 90px;
        border-radius: 8px;
        padding: 5px 12px;
        background: #f3f3f3;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
    }

    &Input {
      background-color: #f5f6fa;
    }

    &Item {
      width: 100%;
      padding: 0 8px 20px;
      background: #fff;
      box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
      border-radius: 18px;
    }

    &Title {
      color: #040919;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      line-height: 24px;
      margin-left: 8px;
      padding: 20px 0;
      justify-content: space-between;
    }

    &Header {
      flex-basis: 52px;
      padding: 12px;
    }

    &Main {
      display: flex;
      padding: 12px;
      flex: 1;
      overflow: scroll;
      scrollbar-width: none;
      flex-direction: column;
      gap: 12px;
      column-gap: 12px;
      background-color: #f5f6fa;
    }

    &Footer {
      display: flex;
      flex-basis: 54px;
      padding: 24px 20px;
      align-items: center;
      justify-content: center;
      gap: 20px;
      background: linear-gradient(
        180deg,
        #ffffff03 0%,
        #ffffff5c 12%,
        #ffffff94 25%,
        #ffffffb8 36%
      );
    }

    &Active {
      background: #d9eeff;
      color: #008cff;
      border: 1px solid #008cff;
    }
  }
}
