import { OrderGoodsStandardFilterResult } from '@/apis/psi/order-goods-standard-filter';
import { Search } from '@/components';
import { Button } from '@echronos/antd';
import Icon from '@echronos/echos-icon';
import { LoadingOutlined } from '@echronos/icons';
import { useMemoizedFn } from 'ahooks';
import { Popup } from 'antd-mobile';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { useEffect, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styles from './select-goods-filter.module.less';

interface SelectCategoryProps {
  pageNo: number;
  pageSize: number;
  parentId?: number;
  keyword?: string;
  customizeCategorySet?: number[];
  clear?: boolean;
}
export interface SearchCateory {
  shopSkuNames: string[];
  standardList: { name: string; value: string[] }[];
}

interface SelectGoodsFilterProps {
  loding: boolean;
  goodsLoading: boolean;
  popup: boolean;
  // eslint-disable-next-line no-unused-vars
  setPopup: (popup: boolean) => void;
  filter: OrderGoodsStandardFilterResult;
  // eslint-disable-next-line no-unused-vars
  fetchGoods: (params: SelectCategoryProps) => Promise<void>;
  filterLoading: boolean;
  // eslint-disable-next-line no-unused-vars
  setGoodsLoading: (load: boolean) => void;
  filterSelect: SearchCateory;
  // eslint-disable-next-line no-unused-vars
  setFilterSelect: (filterSelect: SearchCateory) => void;
}
/**
 * @param {boolean} popup 是否显示
 * @param {() => void} setPopup 设置是否显示
 * @param {OrderGoodsStandardFilterResult} filter 筛选数据
 * @param {OrderGoodsStandardFilterResult} filterCategory 分类数据
 * @param {SearchCateory} filterSelect 选中数据
 * @param {() => void} setFilterSelect 设置选中数据
 */
function SelectGoodsFilter({
  loding,
  popup,
  setPopup,
  filter,
  fetchGoods,
  goodsLoading,
  filterLoading,
  setGoodsLoading,
  filterSelect,
  setFilterSelect,
}: SelectGoodsFilterProps) {
  const { t } = useTranslation();
  // 过滤搜索
  const [filterSearch, setFilterSearch] = useState<string | null>(null);
  const [filterOpen, setFilterOpen] = useState<string[]>([]);
  // 过滤搜索防抖
  const filterSearchDebounce = useMemoizedFn(
    debounce(async (value: string) => {
      setFilterOpen([]);
      setFilterSearch(value);
    }, 500)
  );

  // 过滤分类
  const filterCategory = useMemo(() => {
    if (!filter)
      return {
        shopSkuNames: [],
        standardList: [],
      };
    if (!filterSearch) return filter;
    // setFilterComputed(false);
    return {
      shopSkuNames: filter.shopSkuNames.filter((item) => item.includes(filterSearch)),
      standardList: filter.standardList
        .map((item) => ({
          ...item,
          value: item.value.filter((value) => value.includes(filterSearch)),
        }))
        .filter((item) => item.value.length > 0),
    };
  }, [filterSearch, filter]);
  // 点击过滤
  const selectFilterFn = useMemoizedFn((items: string, name: string, type: 'delete' | 'add') => {
    const hasFilter = filterSelect.standardList.find((filterItem) => filterItem.name === name);
    if (type === 'delete') {
      // 有这个字段，并且长度大于1，删除
      if (hasFilter?.value?.includes(items) && hasFilter?.value?.length > 1) {
        return setFilterSelect({
          ...filterSelect,
          standardList: filterSelect.standardList.map((filterItem) =>
            filterItem.name === name
              ? {
                  ...filterItem,
                  value: filterItem.value.filter((value) => value !== items),
                }
              : filterItem
          ),
        });
      }
      // 有这个字段，并且长度为1，删除
      if (hasFilter?.value?.includes(items) && hasFilter?.value?.length === 1) {
        return setFilterSelect({
          ...filterSelect,
          standardList: filterSelect.standardList.filter((filterItem) => filterItem.name !== name),
        });
      }
    }
    if (type === 'add') {
      if (hasFilter) {
        // 有这个字段添加
        return setFilterSelect({
          ...filterSelect,
          standardList: filterSelect.standardList.map((item) =>
            item.name === name ? { ...item, value: [...item.value, items] } : item
          ),
        });
      }
      if (!hasFilter) {
        // 没有这个字段
        return setFilterSelect({
          ...filterSelect,
          standardList: [...filterSelect.standardList, { name, value: [items] }],
        });
      }
    }
    return null;
  });

  // 获取商品过滤数据
  useEffect(() => {
    if (loding || goodsLoading) return;
    if (!popup) {
      setGoodsLoading(true);
      (async () => {
        fetchGoods({ pageNo: 1, pageSize: 10, clear: true });
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterSelect, popup, fetchGoods, setGoodsLoading]);

  return (
    <Popup
      visible={popup}
      onClose={() => setPopup(false)}
      onMaskClick={() => {
        setFilterSelect({
          shopSkuNames: [],
          standardList: [],
        });
        setPopup(false);
      }}
      position="bottom"
      bodyStyle={{ height: '90%' }}
      className={styles.popup}
    >
      {filterLoading ? (
        <section className={styles.selectLoad}>
          <LoadingOutlined />
        </section>
      ) : (
        <section className={styles.selectPopup}>
          <header className={styles.selectPopupHeader}>
            <Search
              className={classNames(styles.selectPopupInput, styles.selectInput)}
              placeholder={t('channel_enter_keyword_search')}
              onSearch={filterSearchDebounce}
            />
          </header>
          {/* 列表 */}
          <main className={styles.selectPopupMain} style={{ scrollbarWidth: 'none' }}>
            {/* 名称 */}
            {filterCategory?.shopSkuNames && filterCategory.shopSkuNames.length > 0 && (
              <div
                className={styles.selectPopupItem}
                style={{
                  display: filterCategory?.shopSkuNames.length === 0 ? 'none' : '',
                }}
              >
                <h4 className={styles.selectPopupTitle}>
                  {t('channel_name')}
                  {filterCategory?.shopSkuNames && (
                    <button
                      type="button"
                      className={styles.selectPopupShow}
                      onClick={(e) => {
                        e.stopPropagation();
                        if (filterOpen.includes(t('channel_name')))
                          setFilterOpen(
                            filterOpen.filter((filterItem) => filterItem !== t('channel_name'))
                          );
                        else setFilterOpen([...filterOpen, t('channel_name')]);
                      }}
                    >
                      <span
                        style={{
                          display: filterCategory?.shopSkuNames.length <= 6 ? 'none' : '',
                        }}
                      >
                        {filterOpen.includes(t('channel_name')) ? (
                          <span>
                            {t('channel_collapse')}
                            <Icon name="up_arrow_line" />
                          </span>
                        ) : (
                          <span>
                            {t('channel_expand')}
                            <Icon name="down_arrow_line" />
                          </span>
                        )}
                      </span>
                    </button>
                  )}
                </h4>
                <div className={styles.selectPopupValue}>
                  {filterCategory?.shopSkuNames
                    .slice(0, filterOpen.includes(t('channel_name')) ? 999 : 6)
                    .map((itemed) => (
                      <div key={itemed} role="button" className={styles.selectPopupName}>
                        <button
                          type="button"
                          className={classNames(
                            styles.selectPopupValueItem,
                            filterSelect.shopSkuNames.includes(itemed) && styles.selectPopupActive
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (filterSelect.shopSkuNames.includes(itemed))
                              setFilterSelect({
                                ...filterSelect,
                                shopSkuNames: filterSelect.shopSkuNames.filter(
                                  (filterItem) => filterItem !== itemed
                                ),
                              });
                            else
                              setFilterSelect({
                                ...filterSelect,
                                shopSkuNames: [...filterSelect.shopSkuNames, itemed],
                              });
                          }}
                        >
                          {itemed}
                        </button>
                      </div>
                    ))}
                </div>
              </div>
            )}
            {filter && filterCategory?.standardList && (
              <>
                {filterCategory.standardList.map((item) => (
                  <div key={item.name} className={styles.selectPopupItem}>
                    <h4 className={styles.selectPopupTitle}>
                      {item.name}
                      {item.value.length > 6 && (
                        <button
                          type="button"
                          className={styles.selectPopupShow}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (filterOpen.includes(item.name))
                              setFilterOpen(
                                filterOpen.filter((filterItem) => filterItem !== item.name)
                              );
                            else setFilterOpen([...filterOpen, item.name]);
                          }}
                        >
                          {filterOpen.includes(item.name) ? (
                            <span>
                              {t('channel_collapse')}
                              <Icon name="up_arrow_line" />
                            </span>
                          ) : (
                            <span>
                              {t('channel_expand')}
                              <Icon name="down_arrow_line" />
                            </span>
                          )}
                        </button>
                      )}
                    </h4>
                    <div className={styles.selectPopupValue}>
                      {item.value
                        .slice(0, filterOpen.includes(item.name) ? 999 : 6)
                        .map((items) => (
                          <button
                            type="button"
                            key={items}
                            onClick={() =>
                              filterSelect.standardList
                                .find((filterItem) => filterItem.name === item.name)
                                ?.value?.includes(items)
                                ? selectFilterFn(items, item.name, 'delete')
                                : selectFilterFn(items, item.name, 'add')
                            }
                            className={classNames(
                              styles.selectPopupValueItem,
                              filterSelect.standardList
                                .find((filterItem) => filterItem.name === item.name)
                                ?.value?.includes(items) && styles.selectPopupActive
                            )}
                          >
                            {items}
                          </button>
                        ))}
                    </div>
                  </div>
                ))}
              </>
            )}
          </main>
          <footer className={styles.selectPopupFooter}>
            <Button
              style={{ flex: 1 }}
              onClick={() =>
                setFilterSelect({
                  shopSkuNames: [],
                  standardList: [],
                })
              }
            >
              {t('channel_reset')}
            </Button>
            <Button
              type="primary"
              style={{ flex: 1 }}
              className={styles.selectFooterButton}
              onClick={() => setPopup(false)}
            >
              {t('channel_confirm')}
            </Button>
          </footer>
        </section>
      )}
    </Popup>
  );
}

export default SelectGoodsFilter;
