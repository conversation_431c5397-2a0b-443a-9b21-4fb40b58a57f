@import 'styles/mixins/mixins';

.select {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  scrollbar-width: none;
  overflow: scroll;

  &Load {
    display: flex;
    width: 100%;
    height: 100%;
    justify-content: center;
    align-items: center;
  }

  &Srcoll {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &Header {
    display: flex;
    padding: 0 10px 0 16px;
    flex-basis: 52px;
    gap: 8px;
    align-items: center;
    justify-content: space-between;
  }

  &Main {
    flex: 1;
    display: flex;
    max-height: calc(100% - 108px);
    border: #f5f6fa;
  }

  &Category {
    flex-basis: 80px;
    height: 100%;
    scrollbar-width: none;
    overflow: scroll;

    &<PERSON><PERSON> {
      border: none;
      width: 100%;
      max-width: 80px;
      height: 66px;
      background: #f5f6fa;
      .text-overflow(2);
    }

    &Active {
      border: 1px solid #fff;
      background: #fff;
    }
  }

  &Goods {
    display: flex;
    flex-direction: column;
    max-width: calc(100% - 80px);
    overflow: scroll;
    scrollbar-width: none;
    flex: 1;

    &<PERSON><PERSON> {
      white-space: nowrap;
      border: none;
      border-radius: 10px;
      height: 27px;
      flex: 1;
      padding: 2px 16px;
      background-color: #f3f3f3;
    }

    &Active {
      background: #d9eeff;
      color: #008cff;
      border: 1px solid #008cff;
    }

    &Item {
      display: flex;
      margin-bottom: 20px;
    }

    &Category {
      display: flex;
      gap: 12px;
      padding: 16px 12px;
      overflow: scroll;
      scrollbar-width: none;
    }

    &Image {
      width: 90px;
      height: 90px;
      flex-basis: 90px;
      border-radius: 6px;
      object-fit: contain;
    }

    &Header {
      color: #040919;
      font-size: 12px;
      .text-overflow(3);

      margin-bottom: 6px;
      word-break: break-word;
    }

    &Info {
      display: flex;
      max-width: calc(100% - 108px);
      padding: 0 0 0 12px;
      flex: 1;
      flex-direction: column;
      justify-content: center;
    }

    &List {
      flex: 1;
      padding: 0 0 0 12px;
      overflow: scroll;
      scrollbar-width: none;
    }

    &Checkbox {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-basis: 28px;
    }

    &Description {
      .text-overflow(2) !important;

      color: #888b98;
      word-break: break-all;
      font-size: 12px;
    }

    &DescriptionItem {
      margin-right: 6px;

      :last-child {
        margin-right: 0;
      }
    }
  }

  &Input {
    :global .ant-input {
      background-color: #f5f6fa;
    }

    &Icon {
      color: #999eb2;
      position: absolute;
      left: 28px;
    }
  }

  &Footer {
    backdrop-filter: blur(20px);
    flex-basis: 54px;
    display: flex;
    align-items: center;
    padding: 0 20px;

    &Icon {
      width: 32px;
      height: 32px;
    }

    &Text {
      line-height: 25px;
      flex: 1;
      padding: 0 17px;
    }

    &Sum {
      color: #008cff;
      font-size: 18px;
      line-height: 25px;
      margin-left: 4px;
    }

    &Button {
      flex-basis: 80px;
      background: linear-gradient(250deg, #00c6ff 7%, #008cff 100%);
      border: none;

      & span {
        color: #fff;
      }
    }
  }
}
