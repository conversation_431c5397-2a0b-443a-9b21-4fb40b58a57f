import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { FormRender } from '@@/form-engine/containers';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

function SelectFormPreview() {
  const { t } = useTranslation();
  const [params] = useSearchParams({ formCode: '' });
  const formCode = params.get('formCode');

  return (
    <div className={styles.page}>
      <Helmet>
        <title>{t('channel_select_join_form')}</title>
      </Helmet>
      <div className={styles.content}>
        <FormRender formCode={formCode || null} />
      </div>
    </div>
  );
}

export default SelectFormPreview;
