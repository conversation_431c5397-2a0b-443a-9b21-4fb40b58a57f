.container {
  color: #040919;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  background-color: #f5f6fa;
}

.content {
  padding: 0 16px;
  overflow: auto;
  flex: 1;
}

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  overflow: hidden;
}

.cell {
  display: flex;
  height: 54px;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f3f3f3;

  &:last-child {
    border-bottom: none;
  }
}

.value {
  color: #888b98;
}

.formBox {
  padding-top: 20px;
}

.footer {
  color: #040919;
  display: flex;
  padding: 16px 20px;
  flex-direction: column;

  .checkbox {
    display: flex;
  }

  .label {
    margin-left: 8px;
  }

  :global {
    .ant-btn-primary {
      margin-top: 12px;
    }
  }
}

.agreement {
  color: #008cff;
  margin-left: 4px;
}

.dataAuthority {
  color: #008cff;
  margin: 4px 0 27px 25px;
  cursor: pointer;
}
