import { Button, Checkbox, message } from 'antd';
import {
  channelJoinInfo,
  channelJoinSubmit,
  ChannelJoinInfoResult,
  channelDetailInfo,
  ChannelDetailResult,
} from '@/apis';
import debounce from 'lodash/debounce';
import { useRequest } from 'ahooks';
import React, { useEffect, useRef, useState } from 'react';
import { FormRender, FormRenderInstance } from '@@/form-engine/containers';
import { user } from '@/store';
import { useSearchParams } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import sendMessage from '@@/mobile/utils/utils';
import { getToken, setToken } from '@/utils/auth';
import { useTranslation } from 'react-i18next';
import SelectDataPerm from './select-data-perm';
import styles from './index.module.less';

function ChannelInviteJoin() {
  const { t } = useTranslation();
  const [params] = useSearchParams({ id: '', isApply: '' });
  const id = Number(params.get('id') || '');
  const isApply = Number(params.get('isApply') || '');
  const ref = useRef<FormRenderInstance>(null);
  const [showSelectDataPerm, setShowSelectDataPerm] = useState(false);

  const [info, setInfo] = useState<ChannelJoinInfoResult>({
    belongMemberId: 0,
    belongMemberName: '',
    formCode: '',
    formVersion: 0,
    id: 0,
    invitedComId: 0,
    invitedComName: '',
    phone: '',
    supplierComId: 0,
    channelStatus: null,
    supplierComName: '',
  });

  const [detailInfo, setDetailInfo] = useState<ChannelDetailResult>({
    approveTime: '',
    belongMemberId: 0,
    belongMemberName: '',
    companyId: 0,
    companyLogo: '',
    companyName: '',
    extendVOS: [],
    formCode: '',
    formVersion: 0,
    id: 0,
    joinTime: '',
    status: 0,
    businessRelInfoList: [],
    nextChannelRelInfoList: [],
    supplierComName: '',
  });

  const [authorization, setAuthorization] = useState(true);

  const { run } = useRequest(channelJoinInfo, {
    manual: true,
    defaultParams: [{ id, actionType: 1 }],
    onSuccess: (result) => {
      if (result.channelStatus !== null) {
        message.warning(t('channel_already_submitted'));
      }
      setInfo(result);
    },
  });

  const { run: runInfo } = useRequest(channelDetailInfo, {
    manual: true,
    defaultParams: [{ id, actionType: 1 }],
    onSuccess: (result) => {
      if (result.status !== 2) {
        message.warning(t('channel_already_submitted'));
      }
      setDetailInfo(result);
    },
  });

  const onOk = debounce(() => {
    // if (!authorization) {
    //   message.warn('请勾选数据权限授权');
    //   return;
    // }
    ref.current?.submit().then((res) => {
      const arr = Object.keys(res.values).map((item) => ({
        colName: item,
        colValue: res.values[item],
      })) as [];
      channelJoinSubmit({
        channelExtendList: arr.filter((item: any) => item.colValue),
        companyId: info.invitedComId || detailInfo.companyId,
        formCode: info.formCode || detailInfo.formCode,
        formVersion: ref.current?.getVersion() || 0,
        belongMemberId: info.belongMemberId || detailInfo.belongMemberId,
        settleType: 2,
        inviteId: isApply ? null : id,
        id: isApply ? id : null,
      }).then(() => {
        message.success(t('channel_submit_success'));
        setTimeout(() => {
          sendMessage('back', {
            isClose: true,
          });
        }, 1500);
      });
    });
  }, 300);

  useEffect(() => {
    const token = params.get('token') || getToken();
    if (token) {
      setToken(token || '');
    }
    if (isApply) {
      runInfo({ id, actionType: 1 });
    } else {
      run({ id, actionType: 1 });
    }
  }, [id, isApply, params, run, runInfo]);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>{t('channel_fill_join_form')}</title>
      </Helmet>
      <div className={styles.content}>
        <div className={styles.card}>
          <div className={styles.cell}>
            <div className={styles.label}>{t('channel_invited_company')}</div>
            <div className={styles.value}>{info.invitedComName || detailInfo.companyName}</div>
          </div>
          {(info.belongMemberName || detailInfo.belongMemberName) && (
            <div className={styles.cell}>
              <div className={styles.label}>{t('channel_responsible_person')}</div>
              <div className={styles.value}>
                {info.belongMemberName || detailInfo.belongMemberName}
              </div>
            </div>
          )}
          {info.phone && (
            <div className={styles.cell}>
              <div className={styles.label}>{t('channel_contact_method')}</div>
              <div className={styles.value}>{info.phone}</div>
            </div>
          )}
        </div>
        <div className={styles.card}>
          <div className={styles.cell}>
            <div className={styles.label}>{t('channel_select_org')}</div>
            <div className={styles.value}>
              {info.invitedComName || detailInfo.companyName || user.companyName}
            </div>
          </div>
          <div className={styles.formBox}>
            <FormRender ref={ref} formCode={info.formCode || detailInfo.formCode || null} />
          </div>
        </div>
      </div>
      <div className={styles.footer}>
        <div className={styles.checkbox}>
          <Checkbox
            // checked={authorization}
            checked
            onChange={(e) => {
              setAuthorization(e.target.checked);
            }}
          />
          <div
            className={styles.label}
            role="button"
            tabIndex={0}
            onClick={() => {
              setAuthorization(!authorization);
            }}
          >
            {t('channel_agree_data_auth')}“{info.supplierComName || detailInfo.supplierComName}”
            {t('channel_view_agree')}
            <span
              className={styles.agreement}
              role="button"
              tabIndex={0}
              onClick={(e) => {
                e.stopPropagation();
                setShowSelectDataPerm(true);
              }}
            >
              {t('channel_data_perm_content')}
            </span>
          </div>
        </div>
        <Button type="primary" onClick={onOk}>
          {t('channel_confirm')}
        </Button>
      </div>
      <SelectDataPerm visible={showSelectDataPerm} onClose={() => setShowSelectDataPerm(false)} />
    </div>
  );
}

export default ChannelInviteJoin;
