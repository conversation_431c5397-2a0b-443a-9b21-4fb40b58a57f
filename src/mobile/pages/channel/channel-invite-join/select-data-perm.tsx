import { Popup } from 'antd-mobile';
import { Button, Checkbox } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './select-data-perm.module.less';

interface GroupAgreementOtherProps {
  visible: boolean;
  onClose: () => void;
}

function SelectDataPerm({ visible, onClose }: GroupAgreementOtherProps) {
  const { t } = useTranslation();

  const options = [
    {
      value: '0',
      label: t('channel_sales_orders'),
    },
    // {
    //   value: '1',
    //   label: t('客户信息'),
    // },
    {
      value: '2',
      label: t('channel_sub_channel_info'),
    },
    {
      value: '3',
      label: t('channel_sub_channel_orders'),
    },
    {
      value: '4',
      label: t('channel_sub_channel_customer'),
    },
  ];

  return (
    <Popup
      visible={visible}
      position="bottom"
      onClose={onClose}
      onMaskClick={onClose}
      className={styles.popup}
    >
      <div className={styles.title}>{t('channel_data_permission')}</div>
      <div className={styles.content}>
        <Checkbox.Group options={options} value={['0', '1', '2', '3', '4']} />
      </div>
      <div className={styles.footer}>
        <Button type="primary" onClick={onClose}>
          {t('channel_confirm')}
        </Button>
      </div>
    </Popup>
  );
}

export default SelectDataPerm;
