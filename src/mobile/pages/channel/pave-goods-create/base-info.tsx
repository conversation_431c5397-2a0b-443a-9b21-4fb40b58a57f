import { DatePicker } from '@/components';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import SelectChannel from './select-channel';
import SelectWarehouse from './select-warehouse';
import styles from './base-info.module.less';

interface BaseInfoProps {
  channelName: string;
  arrangeTime: any;
  warehouseName: string;
  onConfirm: MultipleParamsFn<[key: string, val: any, name?: string]>;
  onChangeWarehouseNo: MultipleParamsFn<[val: string]>;
}

function BaseInfo({
  channelName,
  arrangeTime,
  warehouseName,
  onConfirm,
  onChangeWarehouseNo,
}: BaseInfoProps) {
  const { t } = useTranslation();

  return (
    <div className={styles.card}>
      <div className={styles.formItem}>
        <div>
          <span className={styles.formItemRequired}>*</span>
          <span className={styles.formItemLabel}>{t('channel_distribution_channel')}</span>
        </div>
        <SelectChannel
          name={channelName}
          onConfirm={(name, id) => onConfirm('channelId', id, name)}
        />
      </div>
      <div className={styles.formItem}>
        <div>
          <span className={styles.formItemRequired}>*</span>
          <span className={styles.formItemLabel}>{t('channel_distribution_time')}</span>
        </div>
        {/* @ts-ignore */}
        <DatePicker
          disabled
          disabledDate={(current) => current && current > dayjs().endOf('day')}
          allowClear={false}
          inputReadOnly={false}
          value={dayjs(arrangeTime)}
          onChange={(e) => {
            onConfirm('arrangeTime', e);
          }}
        />
      </div>
      <div className={styles.formItem}>
        <div>
          <span className={styles.formItemRequired}>*</span>
          <span className={styles.formItemLabel}>{t('channel_outbound_warehouse')}</span>
        </div>
        <SelectWarehouse
          name={warehouseName}
          onConfirm={(val, name) => onConfirm('warehouseId', val, name)}
          onChangeWarehouseNo={onChangeWarehouseNo}
        />
      </div>
    </div>
  );
}

export default BaseInfo;
