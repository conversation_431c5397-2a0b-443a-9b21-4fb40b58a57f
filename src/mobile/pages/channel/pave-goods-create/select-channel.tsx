import { Popup } from 'antd-mobile';
import { Empty, Icon, Search } from '@/components';
import { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { channelList, ChannelListResult } from '@/apis';
import { useRequest } from 'ahooks';
import { debounce } from 'lodash';
import { Divider, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './select.module.less';

interface SelectChannelProps {
  name: string;
  onConfirm: MultipleParamsFn<[name: string, id: number]>;
}

function SelectChannel({ name, onConfirm }: SelectChannelProps) {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState<ChannelListResult[]>([]);
  const params = useRef({
    pageNo: 1,
    pageSize: 10,
    status: 1,
    key: '',
  });
  const totalPage = useRef(0);

  const { run } = useRequest(channelList, {
    manual: true,
    onSuccess: (res) => {
      totalPage.current = res.pagination.total;
      if (params.current.pageNo === 1) {
        setList(res.list);
      } else {
        setList([...list, ...res.list]);
      }
      params.current.pageNo += 1;
    },
  });

  const onPopupScroll = debounce(() => {
    if (params.current.pageNo > totalPage.current) return;
    run({ ...params.current });
  }, 100);

  const onSearch = debounce((val: string) => {
    params.current.pageNo = 1;
    params.current.key = val;
    run({ ...params.current });
  }, 300);

  useEffect(() => {
    if (visible) {
      params.current.pageNo = 1;
      run({ ...params.current });
    }
  }, [run, visible]);

  const onSelect = () => {
    setVisible(true);
  };

  const onClose = () => {
    setVisible(false);
  };

  return (
    <>
      <div role="button" tabIndex={0} className={styles.select} onClick={onSelect}>
        <span className={styles.selectName}>
          {name || (
            <span className={styles.placeholder}>{t('channel_select_distribution_channel')}</span>
          )}
        </span>
        <Icon name="down" />
      </div>
      <Popup
        visible={visible}
        position="bottom"
        onClose={onClose}
        onMaskClick={onClose}
        className={styles.popup}
      >
        <div className={styles.title}>{t('channel_select_distribution_channel')}</div>
        <div className={styles.search}>
          <Search onSearch={onSearch} placeholder={t('channel_enter_channel_name')} />
        </div>
        <div className={styles.list} id="selectChannelList">
          {list && !!list.length ? (
            <InfiniteScroll
              dataLength={list.length}
              hasMore={(params.current.pageNo as number) < totalPage.current}
              loader={
                <div className="text-center">
                  <Spin tip={t('channel_loading')} />
                </div>
              }
              next={onPopupScroll}
              scrollableTarget="selectChannelList"
              endMessage={
                list &&
                list.length > 10 && (
                  <div className={styles.divider}>
                    <Divider plain>
                      <span className={styles.endMessage}>{t('channel_reached_bottom')}</span>
                    </Divider>
                  </div>
                )
              }
            >
              {list.map((item) => (
                <div
                  role="button"
                  tabIndex={0}
                  key={item.id}
                  className={styles.item}
                  onClick={() => {
                    onConfirm(item.companyName, item.id);
                    onClose();
                  }}
                >
                  {item.companyName}
                </div>
              ))}
            </InfiniteScroll>
          ) : (
            <div className={styles.noData}>
              <Empty />
            </div>
          )}
        </div>
      </Popup>
    </>
  );
}

export default SelectChannel;
