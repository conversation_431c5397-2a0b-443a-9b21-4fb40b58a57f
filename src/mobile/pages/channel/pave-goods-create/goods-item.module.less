@import 'styles/mixins/mixins';

.goodsItem {
  display: flex;
  margin-bottom: 16px;
  padding: 8px;
  position: relative;
  border-radius: 12px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
}

.img {
  display: flex;
  align-items: center;
}

.goodsImg {
  width: 108px;
  height: 108px;
  border-radius: 10px;
  margin-right: 12px;
  object-fit: contain;
}

.content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.goodsName {
  font-size: 12px;
  //font-weight: 600;
  margin-bottom: 6px;
  word-break: break-all;
  .text-overflow(3);
}

.goodsAttr {
  flex: 1;
  display: flex;
  align-items: center;
}

.goodsStandard {
  .text-overflow(2) !important;

  color: #888b98;
  word-break: break-all;
  font-size: 12px;
  margin-bottom: 6px;
}

.goodsStandardItem {
  margin-right: 6px;

  &:last-child {
    margin-right: 0;
  }
}

.goodsNum {
  display: flex;
  align-items: center;
  margin-top: 6px;
}

.unit {
  flex: 1;
}

.goodsNumInput {
  width: 102px;
  margin-right: 12px;

  :global {
    .ant-input-number-input {
      font-size: 14px !important;
      width: 102px !important;
      height: 26px;
      text-align: left;
    }
  }
}

.goodsDelete {
  width: 20px;
  height: 20px;
  position: absolute;
  top: -10px;
  right: -10px;
}
