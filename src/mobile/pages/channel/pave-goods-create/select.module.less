@import 'styles/mixins/mixins';

.popup {
  :global {
    .adm-popup-body {
      border-radius: 12px 12px 0 0;
      background-color: #f5f6fa;
      display: flex;
      height: 80%;
      flex-direction: column;
    }
  }
}

.select {
  display: flex;
  height: 32px;
  padding: 0 12px;
  align-items: center;
  flex: 1;
  background-color: #fff;
  border: 1px solid #b1b3be;
  border-radius: 6px;
}

.selectName {
  font-size: 15px;
  flex: 1;
  margin-right: 4px;
  .text-overflow(1);
}

.placeholder {
  color: #b1b3be;
}

.title {
  font-size: 16px;
  font-weight: 600;
  padding: 16px;
  text-align: center;
}

.search {
  margin-bottom: 16px;
  padding: 0 16px;
}

.list {
  padding: 0 16px;
  overflow: auto;
  flex: 1;
}

.item {
  display: flex;
  width: 100%;
  margin-bottom: 12px;
  padding: 14px 16px;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  border-radius: 6px;
  word-break: break-word;
}

.noData {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
