import { Icon, NewSimpleUpload } from '@/components';
import FileList from '@/src/mobile/components/file-list';
import { FileListItem } from '@echronos/echos-ui/dist/file-list';
import { Toast } from 'antd-mobile';
import { useState } from 'react';
import styles from './goods-info.module.less';

interface UploadAttachmentProp {
  fileList: FileListItem[];
  // eslint-disable-next-line no-unused-vars
  onChangeFile: (val: FileListItem) => void;
  // eslint-disable-next-line no-unused-vars
  onChangeFileList: (val: FileListItem[]) => void;
  required?: boolean; // 添加必选属性
}

function UploadAttachment({
  fileList,
  onChangeFile,
  onChangeFileList,
  required,
}: UploadAttachmentProp) {
  const [uploading, setUploading] = useState(false);

  return (
    <div className={styles.card}>
      {fileList.length < 5 && (
        <NewSimpleUpload
          maxCount={5}
          multiple
          className={styles.uploadContanier}
          onChange={(e) => {
            setUploading(false);
            onChangeFile(e.file);
          }}
          beforeUpload={(file) => {
            const input = document.querySelector('input[type="file"]') as HTMLInputElement;
            if (input && input.files && input.files.length + fileList.length > 5) {
              Toast.show({
                content: `最多只能上传5个附件`,
              });
              return false;
            }
            if (file.size > 20 * 1024 * 1024) {
              Toast.show({
                content: `单个文件大小不能大于20M`,
              });
              return false;
            }
            setUploading(true);
            return true;
          }}
        >
          <div className={styles.uploadContanier}>
            <div role="button" tabIndex={0} className={styles.addBtn}>
              {uploading ? (
                <div className={styles.loadingIcon}>
                  <Icon name="loading" className={styles.addBtnIcon} spin />
                </div>
              ) : (
                <Icon name="plus" className={styles.addBtnIcon} />
              )}
            </div>
            <div className={styles.addBtnText}>
              {uploading ? '上传中...' : '添加附件'}
              {required && !uploading && <span style={{ color: 'red' }}>*</span>}
            </div>
          </div>
        </NewSimpleUpload>
      )}
      {required && fileList.length === 0 && (
        <div style={{ color: 'red', fontSize: '12px', marginTop: '4px' }}>请上传附件</div>
      )}
      <FileList
        fileList={fileList}
        onChange={(e) => onChangeFileList(e)}
        className={styles.fileList}
      />
    </div>
  );
}

UploadAttachment.defaultProps = {
  required: false,
};
export default UploadAttachment;
