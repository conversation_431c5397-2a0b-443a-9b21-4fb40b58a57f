import { Icon } from '@/components';
import { useNavigate } from 'react-router-dom';
import { GoodsListQueryData, unitConvertApi } from '@@/order-rewrite/apis';
import { useCallback } from 'react';
import BigNumber from 'big.js';
import { message } from 'antd';
import { cloneDeep } from 'lodash';
import { useTranslation } from 'react-i18next';
import styles from './goods-info.module.less';
import GoodsItem from './goods-item';

interface GoodsInfoProps {
  warehouseNo: string;
  goodsList: GoodsListQueryData[];
  onConfirm: MultipleParamsFn<[val: GoodsListQueryData[]]>;
  onGoCreate: () => void;
}

function GoodsInfo({ warehouseNo, goodsList, onConfirm, onGoCreate }: GoodsInfoProps) {
  const { t } = useTranslation();
  const navigator = useNavigate();

  // 数量改变
  const onChangeNum = useCallback(
    (val: any, key: number, type = '') => {
      goodsList.forEach((item) => {
        const items = item;
        if (items.id === key) {
          if (
            val &&
            (item.saleGroupAfter || item.saleGroup) &&
            BigNumber(val)
              .div(item.saleGroupAfter || item.saleGroup || 1)
              .toNumber() %
              1 !==
              0
          ) {
            if (type === 'blur') {
              message.warning(t('channel_enter_integer_multiple'));
              items.number = BigNumber(
                Math.ceil(val / Number(item.saleGroupAfter || item.saleGroup || 1))
              )
                .times(Number(item.saleGroupAfter || item.saleGroup || 1))
                .toNumber();

              return;
            }
          }
          items.number = val;
        }
      });
      onConfirm(cloneDeep(goodsList));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [goodsList, onConfirm]
  );

  // 切换单位
  const onSelectUnit = useCallback(
    (val: string, currentItem: GoodsListQueryData) => {
      if (val !== (currentItem.unitAfter || currentItem.unit)) {
        unitConvertApi({
          skuId: currentItem.skuId,
          productMarketPrice: currentItem.marketPrice,
          productMinimumQuantity: currentItem.minimum,
          productMinimumSalesUnit: currentItem.saleGroup,
          productUnit: val,
          productUnitBefore: currentItem.unitAfter || currentItem.unit,
          stockNumber: currentItem.stock,
          number: currentItem.number,
          floorPrice: currentItem.floorPrice,
        }).then((res) => {
          goodsList.forEach((item) => {
            const items = item;
            if (items.id === currentItem.id) {
              items.unitAfter = res.productUnit;
              items.marketPriceAfter = res.productMarketPrice;
              items.saleGroupAfter = Number(res.productMinimumSalesUnit);
              items.minimumAfter = res.productMinimumQuantity;
              const saleUnit = Number(res.productMinimumSalesUnit || items.saleGroup || 1);
              items.number = saleUnit ? Math.ceil(Number(res.number) / saleUnit) * saleUnit : 0;
              items.floorPriceAfter = Number(res.floorPrice);
              items.stock = res.stockNumber;
              items.priceValue = '';
              items.unit = res.productUnit;
              items.saleGroup = Number(res.productMinimumSalesUnit);
            }
          });
          onConfirm(cloneDeep(goodsList));
        });
      }
    },
    [goodsList, onConfirm]
  );

  const onDelete = useCallback(
    (val: number) => {
      const arr = goodsList.filter((item) => item.id !== val);
      onConfirm([...arr]);
    },
    [goodsList, onConfirm]
  );

  const onNavCreate = () => {
    if (!warehouseNo) {
      message.warning(t('channel_select_warehouse_first'));
      return;
    }
    onGoCreate();
    navigator(`/m/select/goods?warehouseNo=${warehouseNo}`);
  };

  return (
    <div className={styles.goodsInfo}>
      <div className={styles.card}>
        <div role="button" tabIndex={0} className={styles.addBtn} onClick={onNavCreate}>
          <Icon name="plus" className={styles.addBtnIcon} />
        </div>
        <div className={styles.addBtnText}>{t('channel_select_goods')}</div>
      </div>
      {goodsList.map((item) => (
        <GoodsItem
          key={item.id}
          imagesList={item.imagesList}
          name={item.name}
          mainUnit={item.unit}
          unit={item.unitAfter || item.unit}
          saleGroup={item.saleGroupAfter || item.saleGroup}
          number={item.number}
          unitTemplateId={item.unitTemplateId}
          standardList={item.standardList}
          onDeleteGoods={() => onDelete(item.id)}
          onChangeNum={(val, type) => onChangeNum(val, item.id, type)}
          onSelectUnit={(val) => onSelectUnit(val, item)}
        />
      ))}
    </div>
  );
}

export default GoodsInfo;
