import React, { useState } from 'react';
import { Stepper } from '@/src/order-rewrite/components';
import { Dropdown, Menu } from 'antd';
import { Icon } from '@/components';
import { unitListApi, UnitListData } from '@@/order-rewrite/apis';
import { useRequest } from 'ahooks';
import { useTranslation } from 'react-i18next';
import styles from './goods-item.module.less';

interface GoodsItemProps {
  imagesList: string[];
  name: string;
  standardList: { name: string; value: string }[];
  unitTemplateId: number;
  mainUnit: string;
  unit: string;
  saleGroup: number;
  number: number;
  onDeleteGoods: () => void;
  onChangeNum: MultipleParamsFn<[val: string, type: string]>;
  onSelectUnit: MultipleParamsFn<[val: string]>;
}

function GoodsItem({
  imagesList,
  standardList,
  name,
  unitTemplateId,
  mainUnit,
  unit,
  number,
  saleGroup,
  onDeleteGoods,
  onChangeNum,
  onSelectUnit,
}: GoodsItemProps) {
  const { t } = useTranslation();
  const [unitList, setUnitList] = useState<UnitListData[]>([]);

  const { run: runUnit } = useRequest(unitListApi, {
    manual: true,
    onSuccess: (res) => {
      setUnitList(res.list);
    },
  });

  return (
    <div className={styles.goodsItem}>
      {/* eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions */}
      <img
        className={styles.goodsDelete}
        src="https://img.huahuabiz.com/user_files/20241115/1731634905998218.png"
        alt=""
        onClick={onDeleteGoods}
      />
      <div className={styles.img}>
        <img
          className={styles.goodsImg}
          src={
            (imagesList && imagesList.length && imagesList[0]) ||
            'https://img.huahuabiz.com/default/image/default_holder.png'
          }
          alt=""
        />
      </div>
      <div className={styles.content}>
        <div className={styles.goodsName}>{name}</div>
        <div className={styles.goodsStandard}>
          {standardList.map((item) => (
            <span key={item.name} className={styles.goodsStandardItem}>
              {item.value}
            </span>
          ))}
        </div>
        <div className={styles.goodsNum}>
          <Stepper
            className={styles.goodsNumInput}
            placeholder={t('channel_enter_quantity')}
            step={saleGroup || 1}
            width={102}
            min={0}
            value={number}
            onChange={(e) => onChangeNum(e as string, 'change')}
            onBlur={(e) => onChangeNum(e.target.value, 'blur')}
          />
          <div className={styles.unit}>
            {!unitTemplateId && <span>{unit}</span>}
            {!!unitTemplateId && (
              <Dropdown
                trigger={['click']}
                overlayClassName={styles.dropdown}
                overlay={
                  <Menu
                    onClick={({ key }) => onSelectUnit(key)}
                    className={styles.menu}
                    items={unitList.map((each) => ({
                      key: each.unitName,
                      label: each.unitName,
                    }))}
                  />
                }
                placement="bottom"
                onVisibleChange={(e) => {
                  if (e) {
                    runUnit({ mainUnitName: mainUnit, templateId: unitTemplateId });
                  }
                }}
              >
                <div className={styles.goodsUnit}>
                  <span>{unit}</span>
                  <Icon name="down" className={styles.unitIcon} />
                </div>
              </Dropdown>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default GoodsItem;
