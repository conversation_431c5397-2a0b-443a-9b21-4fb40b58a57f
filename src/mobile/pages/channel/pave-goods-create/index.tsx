import { getPaveGoodsDetail, postPaveGoodsCreate, postPaveGoodsEdit } from '@/apis';
import { FileListItem } from '@/components';
import { GoodsListQueryData } from '@@/order-rewrite/apis';
import { useRequest } from 'ahooks';
import { Button, message } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import { useNavigate, useSearchParams } from 'react-router-dom';
import BaseInfo from './base-info';
import GoodsInfo from './goods-info';
import styles from './index.module.less';
import UploadAttachment from './UploadAttachment';

function PaveGoodsCreate() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [paramsUrl] = useSearchParams();
  const id = paramsUrl.get('id') || '';

  const [info, setInfo] = useState({
    channelId: 0,
    arrangeTime: dayjs(),
    warehouseId: 0,
    arrangeProductList: [],
  });
  const [warehouseNo, setWarehouseNo] = useState('');
  const [channelName, setChannelName] = useState('');
  const [fileList, setFileList] = useState<FileListItem[]>([]);
  const [warehouseName, setWarehouseName] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const { run } = useRequest(getPaveGoodsDetail, {
    manual: true,
    onSuccess: (res) => {
      setWarehouseNo(res.arrangeWarehouseNo);
      setChannelName(res.receiveComName);
      setWarehouseName(res.arrangeWarehouseName);
      setTimeout(() => {
        setInfo({
          channelId: res.channelId,
          arrangeTime: dayjs(res.arrangeTime),
          warehouseId: res.arrangeWarehouseId,
          arrangeProductList: res.arrangeProductList.map((item) => ({
            skuId: item.skuId,
            id: item.shopSkuId,
            imagesList: [item.productMainImage],
            name: item.productName,
            standardList: item.productSpecificationList,
            unit: item.productUnit,
            number: item.productNumber,
            unitTemplateId: item.unitTemplateId,
          })) as [],
        });
        // TODO:編輯時已有的附件
        // @ts-ignore
        setFileList(Array.isArray(res.attachmentList) ? res.attachmentList : []);
      }, 50);
    },
  });

  const onChangeData = (key: string, val: any, name?: string) => {
    if (key === 'arrangeTime') {
      setInfo({ ...info, arrangeTime: dayjs(val).valueOf() as any });
      return;
    }
    if (key === 'channelId') {
      setChannelName(name || '');
    }
    if (key === 'warehouseId') {
      setWarehouseName(name || '');
    }
    setInfo({
      ...info,
      [key]: val,
    });
  };

  const onChangeWarehouseNo = (val: string) => {
    setWarehouseNo(val);
  };

  const onGoCreate = () => {
    localStorage.setItem(
      'PAVE_GOODS_CREATE_INFO',
      JSON.stringify({ ...info, warehouseNo, channelName, warehouseName, fileList })
    );
    localStorage.setItem('SELECT_GOODS_MOBILE', JSON.stringify(info.arrangeProductList));
  };

  const onSubmit = () => {
    if (submitting) return;

    setSubmitting(true);
    const fn = id ? postPaveGoodsEdit : postPaveGoodsCreate;
    fn({
      channelId: info.channelId,
      warehouseId: info.warehouseId,
      arrangeTime: dayjs(info.arrangeTime).valueOf(),
      arrangeProductList: info.arrangeProductList.map((item: GoodsListQueryData) => ({
        shopSkuId: item.id,
        skuId: item.skuId,
        unitName: item.unitAfter || item.unit,
        productNumer: item.number,
      })),
      arrangeInfoId: id,
      // 添加附件
      attachmentList: fileList.map((item) => ({
        name: item.name,
        size: item.size,
        url: item.url,
        type: item.type,
      })),
    } as any)
      .then(() => {
        message.success(id ? t('channel_edit_success') : t('channel_submit_success'));
        navigate('/m/channel/pave-goods', { replace: true });
      })
      .finally(() => {
        setSubmitting(false);
      });
  };

  useEffect(() => {
    const hasSelectGoods = localStorage.getItem('SELECT_GOODS_MOBILE');
    const hasInfo = localStorage.getItem('PAVE_GOODS_CREATE_INFO');
    if (hasSelectGoods && hasSelectGoods.length) {
      setInfo({
        ...(hasInfo ? JSON.parse(hasInfo as any) : info),
        arrangeProductList: JSON.parse(hasSelectGoods),
      });
    }
    if (hasInfo) {
      const storeInfo = JSON.parse(hasInfo);
      setWarehouseNo(storeInfo?.warehouseNo);
      setChannelName(storeInfo.channelName);
      setWarehouseName(storeInfo.warehouseName);
      setFileList(storeInfo?.fileList || []);
    }
  }, []); // eslint-disable-line

  useEffect(() => {
    const hasInfo = localStorage.getItem('PAVE_GOODS_CREATE_INFO');
    if (id && !hasInfo) {
      run({ arrangeInfoId: Number(id) });
    }
  }, [id, run]);

  const isDisabled = useMemo(
    () =>
      !info.channelId ||
      !info.arrangeTime ||
      !info.warehouseId ||
      !info.arrangeProductList.length ||
      !fileList.length || // 添加附件必选验证
      info.arrangeProductList.some((item: GoodsListQueryData) => !Number(item.number)),
    [info.arrangeProductList, info.arrangeTime, info.channelId, info.warehouseId, fileList]
  );

  const onChangeFile = (e: FileListItem) => {
    fileList.push(e);
    setFileList([...fileList]);
  };

  const onChangeFileList = (e: FileListItem[]) => {
    setFileList([...e]);
  };

  return (
    <div className={styles.page}>
      <Helmet>
        <title>{id ? t('channel_edit_distribution') : t('channel_add_distribution')}</title>
      </Helmet>
      <div className={classNames(styles.content)}>
        <BaseInfo
          channelName={channelName}
          arrangeTime={info.arrangeTime}
          warehouseName={warehouseName}
          onConfirm={onChangeData}
          onChangeWarehouseNo={onChangeWarehouseNo}
        />
        <GoodsInfo
          warehouseNo={warehouseNo}
          goodsList={info.arrangeProductList}
          onGoCreate={onGoCreate}
          onConfirm={(val) => onChangeData('arrangeProductList', val)}
        />
        {/* 附件 */}
        <UploadAttachment
          onChangeFileList={onChangeFileList}
          fileList={fileList}
          onChangeFile={onChangeFile}
          required
        />
      </div>
      <div className={styles.footer}>
        <Button
          type="primary"
          onClick={onSubmit}
          disabled={isDisabled || submitting}
          loading={submitting}
        >
          {t('channel_confirm')}
        </Button>
      </div>
    </div>
  );
}

export default PaveGoodsCreate;
