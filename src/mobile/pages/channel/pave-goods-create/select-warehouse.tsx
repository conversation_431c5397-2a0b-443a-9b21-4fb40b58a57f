import { Popup } from 'antd-mobile';
import { Icon, Search, Empty } from '@/components';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useRequest } from 'ahooks';
import { debounce } from 'lodash';
import { warehouseListApi, WarehouseListData } from '@@/order-rewrite/apis';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';
import styles from './select.module.less';

interface SelectChannelProps {
  type?: string;
  title?: string;
  name: string;
  onConfirm: MultipleParamsFn<[id: number, name: string]>;
  onChangeWarehouseNo: MultipleParamsFn<[no: string]>;
}

function SelectWarehouse({
  type,
  title,
  name,
  onConfirm,
  onChangeWarehouseNo,
}: SelectChannelProps) {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [list, setList] = useState<Array<WarehouseListData>>([]);
  const params = useRef({
    pageNo: 1,
    pageSize: 10,
    warehouseStatus: 0,
    functionType: 3,
  });
  const totalPage = useRef(0);
  const [searchKey, setSearchKey] = useState('');

  const { run } = useRequest(warehouseListApi, {
    manual: true,
    onSuccess: (res) => {
      if (params.current.pageNo === 1) {
        setList(res.list);
      } else {
        setList([...list, ...res.list]);
      }
      params.current.pageNo += 1;
      totalPage.current = res.pagination.total;
    },
  });

  const onSearch = debounce((val: string) => {
    setSearchKey(val);
  }, 300);

  const warehouseList = useMemo(
    () => list.filter((item) => item.warehouseName.includes(searchKey)),
    [list, searchKey]
  );

  useEffect(() => {
    if (visible) {
      if (!type) {
        run({ ...params.current });
      } else {
        run({});
      }
    } else {
      params.current.pageNo = 1;
      setList([]);
    }
  }, [run, type, visible]);

  const onSelect = () => {
    setVisible(true);
  };

  const onClose = () => {
    setVisible(false);
  };

  return (
    <>
      <div role="button" tabIndex={0} className={styles.select} onClick={onSelect}>
        <span className={styles.selectName}>
          {name || <span className={styles.placeholder}>{title}</span>}
        </span>
        <Icon name="down" />
      </div>
      <Popup
        visible={visible}
        position="bottom"
        onClose={onClose}
        onMaskClick={onClose}
        className={styles.popup}
      >
        <div className={styles.title}>{title}</div>
        <div className={styles.search}>
          <Search onSearch={onSearch} placeholder={t('channel_enter_warehouse_name')} />
        </div>
        <div className={styles.list} id="selectChannelList">
          {warehouseList && !!warehouseList.length ? (
            warehouseList.map((item) => (
              <div
                role="button"
                tabIndex={0}
                key={item.id}
                className={styles.item}
                onClick={() => {
                  onChangeWarehouseNo(`${item.warehouseNo}`);
                  onConfirm(item.id, item.warehouseName);
                  onClose();
                }}
              >
                {item.warehouseName}
              </div>
            ))
          ) : (
            <div className={styles.noData}>
              <Empty />
            </div>
          )}
        </div>
      </Popup>
    </>
  );
}

SelectWarehouse.defaultProps = {
  type: '',
  title: i18next.t('channel_select_outbound_warehouse'),
};

export default SelectWarehouse;
