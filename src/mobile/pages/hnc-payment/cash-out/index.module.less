.container {
  font-size: 14px;
  color: #040919;
  min-height: 100%;
  padding: 16px 18px;
  background: rgb(245 246 250 / 70%);
  :global {
    .ant-form {
      .ant-form-item {
        justify-content: flex-start;
        .ant-form-item-label {
          min-width: 0;
          padding-right: 0;
        }
        .ant-form-item-control {
          text-align: left;
        }
      }
    }
    .ant-form-item-with-help {
      .ant-form-item-explain {
        text-align: left;
      }
    }
    .ant-input {
      width: 170px;
      text-align: left;
    }
  }
}

.accountInfo {
  .title {
    font-weight: bold;
  }
  .khAccount {
    padding-top: 5px;
    .item {
      margin-top: 7px;
    }
  }
}

.cardBoxHd {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .bd {
    display: flex;
    white-space: nowrap;
    align-items: center;
    .title {
      // font-size: 14px;
      font-weight: bold;
      margin-right: 4px;
    }
  }
  .showOpt {
    font-size: 12px;
    color: #FF2636;
    display: flex;
    align-items: center;
    img{
      margin-right: 2px;
      width: 16px;
      height: 16px;
    }
  }
}

.cardBoxBd {
  overflow: hidden;
  margin-top: 8px;
  .cashItemBd {
    display: flex;
    align-items: center;
    justify-content: space-between;
    :global {
      .ant-input {
        margin-left: 5px;
      }
    }
    .allOpt {
      color: #FF2636;
    }
  }
}

.cardBoxFt {
  border-top: 1px solid #F3F3F3;
  padding-top: 12px;
  color: #888B98;
}

.formAction {
  position: fixed;
  left: 18px;
  right: 18px;
  bottom: 20px;
  display: flex;
  padding-top: 24px;
  :global {
    .ant-btn {
      font-size: 16px;
    }
  }
  .submit {
    flex: 1;
  }
  .submit,.submit:focus {
    &, &:hover {
      background-color: #FF2636;
      color: #fff;
    }
  }
  // .ant-btn.ant-btn-default:focus
}

.modalMainBd {
  .bd {
    margin-bottom: 20px;
    font-size: 24px;
    text-align: center;
    .unit {
      font-size: 16px;
    }
  }
  .ft {
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
    padding: 0 5px 5px;
    .ftBd{
      display: flex;
      align-items: center;
      img {
        margin-left: 3px;
      }
    }
    .defprc{color: #aaa;text-decoration: line-through;font-size: 12px;margin-right: 5px;}
  }
  
}
.acitionsClass {
  .red {
    width: 88rpx;
  }
}
