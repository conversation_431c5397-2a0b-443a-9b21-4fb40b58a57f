import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { setToken } from '@/utils/auth';
import { getPaymentMch, getJzbBankInfo, getPaymentFund, jzbCashOut } from '@/apis';
import { GetPaymentMchRes } from '@/apis/payment/get-payment-mch';
import { GetJzbBankInfoRes } from '@/apis/payment/get-jzb-bank-info';
import { GetPaymentFundRes } from '@/apis/payment/get-payment-fund';
import { Helmet } from 'react-helmet';
import classNames from 'classnames';
import { Spin, Form, Input, Button, message } from 'antd';
import Card from '../components/card/index';
import ModalBox from '../components/modal-box/index';
import formStyles from '../form.module.less';
import styles from './index.module.less';

function CashOut() {
  const [form] = Form.useForm();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const companyId = Number(searchParams.get('companyId') || '0');
  const [mch, setMch] = useState({} as unknown as GetPaymentMchRes);
  const [bankInfo, setBankInfo] = useState({} as unknown as GetJzbBankInfoRes);
  const [moneyInfo, setMoneyInfo] = useState({} as unknown as GetPaymentFundRes);
  const [money, setMoney] = useState('');
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [showSm, setShowSm] = useState(false);
  // const [rate, setRate] = useState(0);
  // const appUserId = useRef('');

  const getInfo = async () => {
    const mchObj = await getPaymentMch({
      companyId,
      tenantId,
    });
    const bankInfoObj = await getJzbBankInfo({
      companyId,
    });
    const moneyObj = await getPaymentFund({
      companyId,
    });
    // const rateInfo = await getJzbRate({
    //   companyId,
    // });
    setMch(mchObj);
    setBankInfo(bankInfoObj);
    setMoneyInfo(moneyObj);
    // setRate(rateInfo.rate);
  };

  // 点击提现说明
  const showTips = () => {
    setShowSm(true);
  };

  // 点击全部提现
  const allCashOut = () => {
    const balance = moneyInfo.balance.toString();
    form.setFieldsValue({
      money: balance,
    });
    setMoney(balance);
  };

  // 点击提交的时候执行
  const onFinish = async () => {
    setVisible(true);
  };

  // 点击确定提现
  const smtCashOut = () => {
    setLoading(true);
    jzbCashOut({
      appUserId: companyId, // 公司Id
      amount: money, // 提现金额
    })
      .then(() => {
        message.success('提交成功，预计1-3个工作日到账');
        setTimeout(() => {
          window.wx.miniProgram.navigateTo({
            url: '/subpackages/pay/pages/hnc-wallet/index',
            success() {
              setVisible(false);
              setMoney('');
            },
          });
        }, 3000);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const DialogContent = (
    <div className={styles.modalMainBd}>
      <div className={styles.bd}>
        <span className={styles.unit}>￥</span>
        <span>{parseFloat(money).toFixed(2)}</span>
      </div>
      {/* <div className={styles.ft}>
        <div className={styles.ftBd}>
          <span>服务费</span>
          <div role="button" tabIndex={0} onClick={showTips}>
            <img
              src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023725/****************.png"
              alt=""
            />
          </div>
        </div>
        <div>￥{(parseFloat(money) * rate).toFixed(2)}</div>
      </div> */}
      <div className={styles.ft}>
        <div className={styles.ftBd}>
          <span>提现手续费</span>
          <div role="button" tabIndex={0} onClick={showTips}>
            <img
              src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023725/****************.png"
              alt=""
            />
          </div>
        </div>
        <div>
          <span className={styles.defprc}>￥2.50</span>
          <span>￥{parseFloat(money) <= 5000 ? '2.00' : '0.00'}</span>
        </div>
      </div>
    </div>
  );

  const ShowSmContent = (
    <div className={styles.smModal}>
      <div>1. 提现方式</div>
      <div>支持提现到个人银行卡或企业账户</div>
      <div>2. 提现费率</div>
      <div>
        在提现环节，银行会收取2.5元/笔的提现手续费，华商贸平台对单笔提现金额超过5000元的提现，给与全额补贴，单笔提现金额不超过5000元的提现，给与0.5元/笔的补贴，需收取2元/笔提现手续费。
      </div>
      <div>3. 到账时间</div>
      <div>提现申请后，一般会在1-3个工作日内到账</div>
    </div>
  );

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, [searchParams]);

  useEffect(() => {
    setLoading(true);
    getInfo();
    setLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={classNames(styles.container, formStyles.formHorizontal)}>
      <Helmet>
        <title>提现</title>
      </Helmet>
      <Spin spinning={loading}>
        <Form form={form} validateTrigger="onSubmit" onFinish={onFinish}>
          <Card extClass={styles.accountInfo}>
            <div className={styles.title}>提现账户</div>
            <div className={styles.khAccount}>
              <div className={styles.item}>开户名称：{mch.companyName}</div>
              <div className={styles.item}>
                开户行：{bankInfo.bankName}（
                {bankInfo.memberAcctNo ? bankInfo.memberAcctNo.slice(-4) : ''}）
              </div>
            </div>
          </Card>
          <Card>
            <div className={styles.cardBoxHd}>
              <div className={styles.bd}>
                <div className={styles.title}>提现金额</div>
              </div>
              <div className={styles.showOpt} role="button" tabIndex={0} onClick={showTips}>
                <img
                  src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023725/****************.png"
                  alt=""
                />
                <span>查看说明</span>
              </div>
            </div>
            <div className={styles.cardBoxBd}>
              <Form.Item
                name="money"
                label=""
                rules={[
                  () => ({
                    validator(_, value) {
                      if (/^\d+(?:\.\d{1,2})?$/.test(value)) {
                        return Promise.resolve();
                      }
                      if (!value) {
                        return Promise.reject(new Error('请输入提现金额'));
                      }
                      return Promise.reject(new Error('金额只能输入数字，且最多保留两位小数'));
                    },
                  }),
                ]}
              >
                <div className={styles.cashItemBd}>
                  <div>
                    ￥
                    <Input
                      value={money}
                      onChange={(e) => setMoney(e.target.value)}
                      placeholder="请输入提现金额"
                    />
                  </div>
                  <div className={styles.allOpt} role="button" tabIndex={0} onClick={allCashOut}>
                    全部提现
                  </div>
                </div>
              </Form.Item>
            </div>
            <div className={styles.cardBoxFt}>可提现金额：{moneyInfo.balance}元</div>
          </Card>
          <div className={styles.formAction}>
            <Button className={styles.submit} htmlType="submit">
              确定提现
            </Button>
          </div>
        </Form>
        <ModalBox
          title="提示"
          visible={visible}
          showCloseButton
          onClose={() => setVisible(false)}
          content={DialogContent}
          actions={[
            {
              bgColor: 'gray',
              key: 'cancel',
              text: '取消',
              onClick: () => setVisible(false),
            },
            {
              bgColor: 'red',
              key: 'ok',
              text: '确定提现',
              onClick: () => smtCashOut(),
            },
          ]}
        />
        <ModalBox
          title="提示"
          visible={showSm}
          showCloseButton
          onClose={() => setShowSm(false)}
          content={ShowSmContent}
          actions={[
            {
              bgColor: 'red',
              key: 'ok',
              text: '确定',
              onClick: () => setShowSm(false),
            },
          ]}
        />
      </Spin>
    </div>
  );
}

export default CashOut;
