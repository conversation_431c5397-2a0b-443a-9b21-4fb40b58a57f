import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { setToken } from '@/utils/auth';
import { getPaymentMch, getJzbTransferStatus, getJzbBankInfo } from '@/apis';
import { GetPaymentMchRes } from '@/apis/payment/get-payment-mch';
import { GetJzbTransferStatusRes } from '@/apis/payment/get-jzb-transfer-status';
import { GetJzbBankInfoRes } from '@/apis/payment/get-jzb-bank-info';
import { Helmet } from 'react-helmet';
import BindSuccess from '../components/bind-success';
import BindFail from '../components/bind-fail';
import BindConfirm from '../components/bind-confirm';
import styles from './index.module.less';

interface OptionsProps {
  label: string;
  value: number;
}

function BindResult() {
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const companyId = Number(searchParams.get('companyId') || '0');
  const [dkStatus, setDkStatus] = useState(0); // 2成功 3失败 4待确认
  const [mch, setMch] = useState({} as unknown as GetPaymentMchRes);
  const [dkInfo, setDkInfo] = useState({} as unknown as GetJzbTransferStatusRes);
  const [bankType, setBankType] = useState(0);
  const [bankTypeOptions, setBankTypeOptions] = useState<OptionsProps[]>([]);
  const [bankInfo, setBankInfo] = useState({} as unknown as GetJzbBankInfoRes);
  const [loading, setLoading] = useState(false);

  const getDkStatus = async () => {
    const mchObj = await getPaymentMch({
      companyId,
      tenantId,
    });
    setMch(mchObj);
    const dkObj = await getJzbTransferStatus({
      tenantId,
      companyId,
      jzbCode: mchObj.jzbCode,
    });
    setDkStatus(dkObj.authStatus);
    setDkInfo(dkObj);
  };

  // 获取绑卡详细信息
  const getBankInfo = async () => {
    let opitonsArry = [];
    const bankInfoObj = await getJzbBankInfo({
      companyId,
    });
    if (bankInfoObj.bindBankType === 1) {
      opitonsArry = [
        {
          label: '个人卡',
          value: 1,
        },
      ];
    } else {
      opitonsArry = [
        {
          label: '企业账户',
          value: 2,
        },
      ];
    }
    setBankInfo(bankInfoObj);
    setBankType(bankInfoObj.bindBankType);
    setBankTypeOptions(opitonsArry);
  };

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, [searchParams]);

  useEffect(() => {
    setLoading(true);
    getDkStatus();
    getBankInfo();
    setLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>绑卡验证</title>
      </Helmet>
      {dkStatus === 2 ? (
        <BindSuccess
          loading={loading}
          setLoading={(value: boolean) => setLoading(value)}
          mch={mch}
          bankInfo={bankInfo}
          dkInfo={dkInfo}
          getDkStatus={getDkStatus}
        />
      ) : (
        ''
      )}
      {dkStatus === 3 ? (
        <BindFail
          loading={loading}
          setLoading={(value: boolean) => setLoading(value)}
          bankInfo={bankInfo}
          dkInfo={dkInfo}
          bankType={bankType}
          bankTypeOptions={bankTypeOptions}
        />
      ) : (
        ''
      )}
      {dkStatus === 4 ? (
        <BindConfirm
          loading={loading}
          bankInfo={bankInfo}
          bankType={bankType}
          bankTypeOptions={bankTypeOptions}
        />
      ) : (
        ''
      )}
    </div>
  );
}

export default BindResult;
