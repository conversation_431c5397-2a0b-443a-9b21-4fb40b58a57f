.formHorizontal {
  :global {
    .ant-form {
      .ant-form-item {
        margin-top: -2px;
        margin-bottom: 0;
        padding-top: 10px;
        padding-bottom: 10px;
        justify-content: space-between;
        flex-wrap: nowrap;
        border-top: 1px solid #f3f3f3;

        .ant-form-item-label,
        .ant-form-item-control {
          flex: none;
        }

        .ant-form-item-label {
          min-width: 90px;
          padding-right: 15px;
          padding-bottom: 0;
        }

        .ant-form-item-control {
          text-align: right;
        }
      }
    }

    .ant-form-item-with-help {
      .ant-form-item-explain {
        text-align: right;
      }
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-input,
    .ant-select {
      font-size: 16px;
      width: 170px;
    }

    .ant-input,
    .ant-select-selector {
      border: none !important;
      box-shadow: none !important;
      text-align: right;
    }

    .ant-input {
      padding: 6px 0;
    }

    .ant-select-arrow {
      right: 0;
    }

    .ant-select-single.ant-select-show-arrow .ant-select-selection-item,
    .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
      line-height: 32px;
      padding-right: 6px;
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: #FF2636;
    }
    .ant-radio-inner::after {
      background-color: #FF2636;
    }
  }
}
:global {
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    color: #FF2636 !important;
  }
}