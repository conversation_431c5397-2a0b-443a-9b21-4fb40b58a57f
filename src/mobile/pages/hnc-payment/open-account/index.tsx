import { useState, useMemo, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { setToken } from '@/utils/auth';
import { savePaymentMch, getPaymentCardType, getPaymentFrCardType } from '@/apis';
import { GetPaymentCardTypeRes } from '@/apis/payment/get-payment-card-type';
import { GetPaymentFrCardTypeRes } from '@/apis/payment/get-payment-fr-card-type';
import classNames from 'classnames';
import { Helmet } from 'react-helmet';
import { Form, Input, Select, Radio, Button, Spin, Tooltip, message } from 'antd';
import Card from '../components/card/index';
import styles from './index.module.less';

const ztTypeOptions = [
  {
    label: '企业',
    value: 2,
  },
  {
    label: '个体工商户',
    value: 1,
  },
];

function OpenAccount() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [form] = Form.useForm();
  const [cardTypeOptions, setCardTypeOptions] = useState<GetPaymentCardTypeRes[]>([]);
  const [frCardTypeOptions, setFrCardTypeOptions] = useState<GetPaymentFrCardTypeRes[]>([]);
  const [ztType, setZtType] = useState(0);
  const [loading, setLoading] = useState(false);
  const [tooltipVisible, setTooltipVisible] = useState(false);
  const [agree, setAgree] = useState(false);
  const companyNameStr = decodeURIComponent(searchParams.get('companyName') || '');

  const defaultData = {
    companyName: companyNameStr?.substring(0, 60) || '',
    cardType: 73,
    cardNo: searchParams.get('companyCreditCode') || '',
    shopName: decodeURIComponent(searchParams.get('shopName') || ''),
    frName: decodeURIComponent(searchParams.get('legalPerson') || ''),
  };

  // 主体类型
  const ztTypeHtml = useMemo(
    () =>
      ztTypeOptions.map((ele, index) => (
        <div
          key={ele.value}
          className={classNames(styles.item, {
            [styles.cur]: ele.value === ztType,
            [styles.item0]: index === 0,
          })}
          role="button"
          tabIndex={0}
          onClick={() => setZtType(ele.value)}
        >
          {ele.label}
        </div>
      )),
    [ztType]
  );

  // 点击提交的时候执行
  const onFinish = (values: any) => {
    const { companyName, cardType, cardNo, shopName, frName, frCardType, frCardNo } = values;
    const phone = searchParams.get('phone');
    const shopId = searchParams.get('shopId') || 0;
    const tenantId = searchParams.get('tenantId') || '';
    if (!ztType) {
      message.error('请选择企业主体信息');
      return;
    }
    if (!agree) {
      setTooltipVisible(true);
      return;
    }
    setLoading(true);
    savePaymentMch({
      telephone: phone || '', // 开发和测试时需先写死,上线需改回传phone
      tenantId,
      bizFlag: ztType, // 体工商户标志
      companyName, // 公司名称
      certificateType: cardType, // 公司证件类型
      certificateCode: cardNo, // 公司证件号码
      storeId: Number(shopId), // 店铺id
      storeName: shopName, // 店铺名称
      reprName: frName, // 法人名称
      reprGlobalType: frCardType, // 法人证件类型
      reprGlobalId: frCardNo, // 法人证件号码
    })
      .then(() => {
        message.success('开户成功');
        setTimeout(() => {
          window.wx.miniProgram.navigateBack();
        }, 3000);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 点击取消
  const onCancel = () => {
    window.wx.miniProgram.navigateBack();
  };

  // 点击查看协议
  const onShowAgree = () => {
    const formObj = form.getFieldsValue();
    sessionStorage.setItem(
      'openAccountForm',
      JSON.stringify({
        formObj,
        ztType,
        agree,
      })
    );
    navigate('/m/hnc-payment/agreement');
  };

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, [searchParams]);

  useEffect(() => {
    const openAccountForm = sessionStorage.getItem('openAccountForm');

    // 获取企业证件类型
    getPaymentCardType().then((res) => {
      setCardTypeOptions(res.list);
    });

    // 获取法人证件类型
    getPaymentFrCardType().then((res) => {
      setFrCardTypeOptions(res.list);
    });

    // 若缓存中有openAccountForm，则用openAccountForm里的值
    if (openAccountForm) {
      const formObj = JSON.parse(openAccountForm);
      form.setFieldsValue(formObj.formObj);
      setZtType(formObj.ztType);
      setAgree(formObj.agree);
    } else {
      form.setFieldsValue(defaultData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 返回组件HTML
  return (
    <div className={classNames(styles.container, styles.formHorizontal)}>
      <Helmet>
        <title>开通账户</title>
      </Helmet>
      {/* 以下是表单主体 */}
      <Spin spinning={loading}>
        <Form
          className={styles.mainForm}
          form={form}
          validateTrigger="onSubmit"
          onFinish={onFinish}
        >
          <Card extClass={classNames(styles.cardBox, styles.ztTypeBox)}>
            <div className={styles.title}>企业主体信息</div>
            <div className={styles.subTitle}>主体类型</div>
            <div className={styles.ztType}>{ztTypeHtml}</div>
          </Card>
          <Card extClass={styles.cardBox}>
            <div className={styles.cardBoxBd}>
              <Form.Item
                name="companyName"
                label="企业名称"
                rules={[{ required: true, message: '请填写企业名称' }]}
              >
                <Input maxLength={60} placeholder="请填写企业名称" />
              </Form.Item>
              <Form.Item
                name="cardType"
                label="企业证件类型"
                rules={[{ required: true, message: '请选择企业证件类型' }]}
              >
                <Select options={cardTypeOptions} placeholder="请选择企业证件类型" />
              </Form.Item>
              <Form.Item
                name="cardNo"
                label="企业证件号"
                rules={[{ required: true, message: '请填写企业证件号' }]}
              >
                <Input maxLength={20} placeholder="请填写企业证件号" />
              </Form.Item>
              <Form.Item
                name="shopName"
                label="店铺名称"
                rules={[{ required: true, message: '请填写开通的站点名称' }]}
              >
                <Input placeholder="请填写开通的站点名称" />
              </Form.Item>
            </div>
          </Card>
          <Card extClass={styles.cardBox}>
            <div className={styles.title}>法人信息</div>
            <div className={styles.cardBoxBd}>
              <Form.Item
                name="frName"
                label="法人名称"
                rules={[{ required: true, message: '请填写法人名称' }]}
              >
                <Input maxLength={20} placeholder="请填写法人名称" />
              </Form.Item>
              <Form.Item
                name="frCardType"
                label="法人证件类型"
                rules={[{ required: true, message: '请选择法人证件类型' }]}
              >
                <Select options={frCardTypeOptions} placeholder="请选择法人证件类型" />
              </Form.Item>
              <Form.Item
                name="frCardNo"
                label="法人证件号码"
                rules={[{ required: true, message: '请填写法人证件号码' }]}
              >
                <Input maxLength={20} placeholder="请填写法人证件号码" />
              </Form.Item>
            </div>
          </Card>
          <div className={styles.agreement}>
            <Tooltip
              visible={tooltipVisible}
              placement="topLeft"
              title="请先阅读并同意开户协议"
              overlayClassName={styles.tooltip}
            >
              <Radio
                onChange={(e) => {
                  setAgree(e.target.checked);
                  if (e.target.checked) {
                    setTooltipVisible(false);
                  }
                }}
              >
                我已阅读并同意
                <span className={styles.link} onClick={onShowAgree} role="button" tabIndex={0}>
                  《开户协议》
                </span>
              </Radio>
            </Tooltip>
          </div>
          <div className={styles.action}>
            <Button className={styles.cancel} htmlType="button" onClick={onCancel}>
              取消
            </Button>
            <Button className={styles.submit} htmlType="submit">
              确定
            </Button>
          </div>
        </Form>
      </Spin>
    </div>
  );
}

export default OpenAccount;
