.container {
  min-height: 100%;
  padding-right: 18px;
  padding-left: 18px;
  background: rgb(245 246 250 / 70%);
}

:global {
  // antD官网给了下拉popupClassName属性（ts报不存在，只是一个h5放外面不影响）
  .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
    color: #FF2636 !important;
  }
}

.formHorizontal {
  :global {
    .ant-form {
      padding-top: 16px;
      .ant-form-item {
        margin-top: -2px;
        margin-bottom: 0;
        padding-top: 10px;
        padding-bottom: 10px;
        justify-content: space-between;
        flex-wrap: nowrap;
        border-top: 1px solid #f3f3f3;

        .ant-form-item-label,
        .ant-form-item-control {
          flex: none;
        }

        .ant-form-item-label {
          min-width: 90px;
          padding-right: 15px;
          padding-bottom: 0;
        }

        .ant-form-item-control {
          text-align: right;
        }
      }
    }

    .ant-form-item-with-help {
      .ant-form-item-explain {
        text-align: right;
      }
    }

    .ant-radio-checked .ant-radio-inner {
      border-color: #ff2636;
    }

    .ant-radio-inner::after {
      background-color: #ff2636;
    }

    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      display: none;
    }

    .ant-input,
    .ant-select {
      font-size: 16px;
      width: 180px;
    }

    .ant-input,
    .ant-select-selector {
      border: none !important;
      box-shadow: none !important;
      text-align: right;
    }

    .ant-input {
      padding: 6px 0;
    }

    .ant-select-arrow {
      right: 0;
    }

    .ant-select-single.ant-select-show-arrow .ant-select-selection-item,
    .ant-select-single.ant-select-show-arrow .ant-select-selection-placeholder {
      line-height: 32px;
      padding-right: 6px;
    }
  }
}

.cardBox {
  margin-bottom: 16px;
  padding-right: 18px;
  padding-left: 18px;

  .title {
    font-weight: 600;
    padding-top: 16px;
    padding-bottom: 6px;
  }

  .subTitle {
    color: #888b98;
  }
}

.cardBoxBd {
  overflow: hidden;
}

.ztTypeBox {
  padding-bottom: 16px;
}

.ztType {
  display: flex;
  margin-top: 16px;

  .item {
    display: flex;
    height: 28px;
    justify-content: center;
    box-sizing: border-box;
    flex: 1;
    align-items: center;
    background: #f5f6fa;
    border-radius: 10px;
    border: 1px solid #f5f6fa;
  }

  .item0 {
    margin-right: 12px;
  }

  .cur {
    background: #FFE8EA;
    border-color: #FF2636;
    color: #FF2636;
  }
}

.agreement {
  overflow: hidden;
  :global {
    .ant-radio-wrapper {
      color: #888B98;
    }
  }
  .link {
    color: #FF2636;
  }
}

.action {
  display: flex;
  padding-bottom: 24px;
  padding-top: 24px;
  .cancel, .submit {
    flex: 1;
  }
  .cancel {
    &, &:hover {
      background-color: #F3F3F3;
    }
    margin-right: 15px;
  }
  .submit,.submit:focus {
    &, &:hover {
      background-color: #FF2636;
      color: #fff;
    }
  }
  // .ant-btn.ant-btn-default:focus
}