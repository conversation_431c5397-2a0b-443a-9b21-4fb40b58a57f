import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { setToken } from '@/utils/auth';
import { getPaymentMch } from '@/apis';
import { Helmet } from 'react-helmet';
import { Spin } from 'antd';
import styles from './index.module.less';

function Agreement() {
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const companyId = Number(searchParams.get('companyId') || '0');
  const [url, setUrl] = useState('');
  const [loading, setLoading] = useState(false);

  const getInfo = async () => {
    const mchObj = await getPaymentMch({
      companyId,
      tenantId,
    });
    setUrl(
      `https://my.orangebank.com.cn/orgLogin/hd/act/jianzb/jzbxy.html?name=${mchObj.companyName}`
    );
  };

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, [searchParams]);

  useEffect(() => {
    setLoading(true);
    getInfo();
    setLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>商户服务协议</title>
      </Helmet>
      <Spin spinning={loading}>
        <iframe className={styles.iframe} src={url} title="external-page" width="100%" />
      </Spin>
    </div>
  );
}

export default Agreement;
