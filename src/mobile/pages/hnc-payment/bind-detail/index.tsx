import { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { setToken } from '@/utils/auth';
import { getPaymentMch, getJzbBankInfo, unbindJzbBank } from '@/apis';
import { GetPaymentMchRes } from '@/apis/payment/get-payment-mch';
import { GetJzbBankInfoRes } from '@/apis/payment/get-jzb-bank-info';
import { Spin, Button, message } from 'antd';
import { Helmet } from 'react-helmet';
import Members from '../components/members';
import BankType from '../components/bank-type';
import BankInfo from '../components/bank-info';
import ModalBox from '../components/modal-box/index';
import styles from './index.module.less';

interface OptionsProps {
  label: string;
  value: number;
}

function BindDetail() {
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const companyId = Number(searchParams.get('companyId') || '0');
  const [mch, setMch] = useState({} as unknown as GetPaymentMchRes);
  const [bankType, setBankType] = useState(0);
  const [bankTypeOptions, setBankTypeOptions] = useState<OptionsProps[]>([]);
  const [bankInfo, setBankInfo] = useState({} as unknown as GetJzbBankInfoRes);
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);

  // 获取商户信息
  const getMchInfo = async () => {
    const mchObj = await getPaymentMch({
      companyId,
      tenantId,
    });
    setMch(mchObj);
  };

  // 获取绑卡详细信息
  const getBankInfo = async () => {
    let opitonsArry = [];
    const bankInfoObj = await getJzbBankInfo({
      companyId,
    });
    if (bankInfoObj.bindBankType === 1) {
      opitonsArry = [
        {
          label: '个人卡',
          value: 1,
        },
      ];
    } else {
      opitonsArry = [
        {
          label: '企业账户',
          value: 2,
        },
      ];
    }
    setBankInfo(bankInfoObj);
    setBankType(bankInfoObj.bindBankType);
    setBankTypeOptions(opitonsArry);
  };

  // 点击解绑
  const unbindBank = async () => {
    setLoading(true);
    unbindJzbBank({
      id: bankInfo.id,
      companyId,
      tenantId,
      memberAcctNo: bankInfo.memberAcctNo,
    })
      .then(() => {
        message.success('解绑成功');
        setTimeout(() => {
          window.wx.miniProgram.navigateTo({ url: '/subpackages/pay/pages/hnc-wallet/index' });
        }, 3000);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, [searchParams]);

  useEffect(() => {
    setLoading(true);
    getMchInfo();
    getBankInfo();
    setLoading(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>银行卡</title>
      </Helmet>
      <Spin spinning={loading}>
        <Members
          companyId={Number(companyId)}
          companyName={mch.companyName}
          jzbCode={mch.jzbCode}
        />
        <BankType title="银行账户类型" options={bankTypeOptions} type={bankType} />
        <BankInfo bankInfo={bankInfo} pageType={2} />
        <div className={styles.action}>
          <Button className={styles.submit} htmlType="submit" onClick={() => setVisible(true)}>
            解绑
          </Button>
        </div>
        <ModalBox
          title="提示"
          visible={visible}
          showCloseButton
          onClose={() => setVisible(false)}
          content="解绑银行卡将无法提现，是否确定解绑？"
          actions={[
            {
              bgColor: 'gray',
              key: 'cancel',
              text: '取消',
              onClick: () => setVisible(false),
            },
            {
              bgColor: 'red',
              key: 'ok',
              text: '确定',
              onClick: () => unbindBank(),
            },
          ]}
        />
      </Spin>
    </div>
  );
}

export default BindDetail;
