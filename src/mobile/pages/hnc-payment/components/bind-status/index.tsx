import classNames from 'classnames';
import Card from '../card';
import styles from './index.module.less';

interface BindStatusProps {
  item: {
    imgSrc: string;
    title: string;
    msg: string;
    optType?: number;
    optTxt?: string;
  };
  onClick?: () => void;
}

function BindStatus({ item, onClick }: BindStatusProps) {
  return (
    <Card extClass={styles.statusBox}>
      <div className={styles.pic}>
        <img src={item.imgSrc} alt="" />
      </div>
      <div className={styles.title}>{item.title}</div>
      <div className={styles.msg}>{item.msg}</div>
      {item.optTxt ? (
        <div
          className={classNames(styles.opt, {
            [styles.opt1]: item.optType === 1,
            [styles.opt2]: item.optType === 2,
          })}
          tabIndex={0}
          role="button"
          onClick={onClick}
        >
          {item.optTxt}
        </div>
      ) : (
        ''
      )}
    </Card>
  );
}

BindStatus.defaultProps = {
  onClick: () => {},
};
export default BindStatus;
