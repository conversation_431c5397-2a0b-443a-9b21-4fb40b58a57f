import { PropsWithChildren } from 'react';
import { jzbBindCompanyAgain } from '@/apis';
import { GetJzbTransferStatusRes } from '@/apis/payment/get-jzb-transfer-status';
import { GetJzbBankInfoRes } from '@/apis/payment/get-jzb-bank-info';
import { Spin, message } from 'antd';
import BankType from '../bank-type';
import BankInfo from '../bank-info';
import BindStatus from '../bind-status';

const bindStatusProps = {
  imgSrc:
    'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023721/****************.png',
  title: '打款失败',
  msg: '点击下方按钮重新发起鉴权',
  optType: 2,
  optTxt: '重新发起',
};

interface BindFailProps {
  loading: boolean;
  // eslint-disable-next-line no-unused-vars
  setLoading: (value: boolean) => void;
  bankInfo: GetJzbBankInfoRes;
  dkInfo: GetJzbTransferStatusRes;
  bankType: number;
  bankTypeOptions: {
    value: number;
    label: string;
  }[];
}

function BindFail({
  loading,
  setLoading,
  bankInfo,
  dkInfo,
  bankType,
  bankTypeOptions,
}: PropsWithChildren<BindFailProps>) {
  const checkAgain = () => {
    setLoading(true);
    jzbBindCompanyAgain({
      companyId: dkInfo.companyId,
      tenantId: dkInfo.tenantId,
    })
      .then(() => {
        message.success('发起成功');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Spin spinning={loading}>
      <BindStatus item={bindStatusProps} onClick={checkAgain} />
      <BankType title="银行账户类型" options={bankTypeOptions} type={bankType} />
      <BankInfo bankInfo={bankInfo} pageType={1} />
    </Spin>
  );
}

export default BindFail;
