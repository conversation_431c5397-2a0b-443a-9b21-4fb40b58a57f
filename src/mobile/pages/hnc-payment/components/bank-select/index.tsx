import { useState } from 'react';
import debounce from 'lodash/debounce';
import { searchJzbBankList } from '@/apis';
import { Select, Spin } from 'antd';

interface BankSelectProps {
  placeholder?: string;
  // eslint-disable-next-line no-unused-vars
  onChange: (value: string) => void;
}

interface bankListProps {
  label: string;
  value: string;
}

function BankSelect({ placeholder, onChange }: BankSelectProps) {
  const [fetching, setFetching] = useState(false);
  const [bankList, setBankList] = useState<bankListProps[]>([]);

  const onSearch = debounce((keyword: string) => {
    setBankList([]);
    setFetching(true);
    searchJzbBankList({
      aprCode: 'BANK_TYPE_ALL',
      aprShowmsg: keyword,
    })
      .then((res) => {
        const list = res?.list || [];
        const newList = list.map((item) => ({
          label: item.aprShowmsg,
          value: `${item.aprShowmsg},${item.aprValue}`,
        }));
        setBankList(newList);
      })
      .finally(() => {
        setFetching(false);
      });
  }, 1000);

  return (
    <Select
      dropdownMatchSelectWidth={false}
      placeholder={placeholder}
      showSearch
      filterOption={false}
      onSearch={onSearch}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      onChange={(value: string) => onChange(value)}
      options={bankList}
    >
      {/* {bankList.map((ele) => (
        <Select.Option key={ele.value} className={optionClass}>
          {ele.label}
        </Select.Option>
      ))} */}
    </Select>
  );
}

BankSelect.defaultProps = {
  placeholder: '',
};

export default BankSelect;
