.cardBoxBd {
  overflow: hidden;
  :global {
    .ant-select-selection-search-input {
      text-align: right;
    }
    .ant-radio-wrapper {
      margin-right: 0;
      margin-left: 16px;
    }
    span.ant-radio + * {
      padding-right: 0;
    }
  }
}
.bankOptions {
  width: 260px;
  :global {
    .ant-select-item-option-content {
      white-space: normal;
    }
  }
}
.validCode{
  margin-top: 2px;
  display: flex;
  width: 170px;
  :global {
    .ant-input {
      width: auto;
    }
  }
  .opt {
    white-space: nowrap;
    margin-left: 8px;
    margin-top: 6px;
    color: #FF2636;
  }
}

.action {
  display: flex;
  padding-bottom: 24px;
  padding-top: 24px;
  .cancel, .submit {
    flex: 1;
  }
  .cancel {
    &, &:hover {
      background-color: #F3F3F3;
    }
    margin-right: 15px;
  }
  .submit,.submit:focus {
    &, &:hover {
      background-color: #FF2636;
      color: #fff;
    }
  }
  // .ant-btn.ant-btn-default:focus
}