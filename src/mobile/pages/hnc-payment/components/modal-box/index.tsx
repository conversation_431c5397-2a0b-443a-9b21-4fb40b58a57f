import { HTMLAttributes, PropsWithChildren } from 'react';
import { Modal } from 'antd-mobile';
import classNames from 'classnames';
import styles from './index.module.less';

interface actionsProps {
  key: string;
  text: string;
  bgColor?: string;
  onClick?: () => void;
}

export interface ModalBoxPorps extends HTMLAttributes<HTMLDivElement> {
  title?: string;
  visible: boolean;
  showCloseButton: boolean;
  onClose?: () => void;
  content?: any;
  actions?: actionsProps[];
}

function ModalBox({
  title,
  visible,
  showCloseButton,
  onClose,
  content,
  actions,
}: PropsWithChildren<ModalBoxPorps>) {
  const buttonEle = actions?.map((item) => (
    <div
      key={item.key}
      className={classNames(
        styles.item,
        item.bgColor === 'gray' ? styles.gray : '',
        item.bgColor === 'red' ? styles.red : ''
      )}
      role="button"
      tabIndex={0}
      onClick={item.onClick}
    >
      {item.text}
    </div>
  ));
  const DialogContent = (
    <div className={styles.modalMain}>
      {content}
      {actions && actions.length > 0 ? <div className={styles.modalAction}>{buttonEle}</div> : ''}
    </div>
  );

  return (
    <Modal
      title={title}
      content={DialogContent}
      visible={visible}
      showCloseButton={showCloseButton}
      onClose={onClose}
    />
  );
}

ModalBox.defaultProps = {
  title: '提示',
  onClose() {},
  content: '',
  actions: [],
};

export default ModalBox;
