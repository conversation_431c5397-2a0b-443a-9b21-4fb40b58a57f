import { useEffect, useState } from 'react';
import { getCompanyPersonalization } from '@/apis';
// import { GetPaymentMchRes } from '@/apis/payment/get-payment-mch';
import Card from '../card/index';
import styles from './index.module.less';

interface MembersProps {
  companyId: number;
  companyName: string;
  jzbCode: string;
}

function Members({ companyId, companyName, jzbCode }: MembersProps) {
  // const [mchData, setMchData] = useState({} as unknown as GetPaymentMchRes);
  const [logoUrl, setLogoUrl] = useState('');
  const getLogo = async () => {
    const info = await getCompanyPersonalization(companyId);
    const logo =
      info.logoUrl ||
      'https://img.huahuabiz.com/PC/static/img/default_avatar/24.png?x-image-process=image/resize,m_pad,w_48,h_48,limit_1/sharpen,100';
    setLogoUrl(logo);
  };

  useEffect(() => {
    getLogo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Card extClass={styles.memberCard}>
      <div className={styles.members}>
        <div className={styles.pic}>
          <img src={logoUrl} alt="" />
        </div>
        <div className={styles.info}>
          <div className={styles.hd}>
            <div className={styles.name}>{companyName}</div>
            <div className={styles.tip}>华正支付</div>
          </div>
          <div className={styles.bd}>{jzbCode}</div>
        </div>
      </div>
    </Card>
  );
}

export default Members;
