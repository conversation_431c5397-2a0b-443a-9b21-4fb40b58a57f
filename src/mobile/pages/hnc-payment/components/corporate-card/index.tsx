import { useState, useEffect, useRef, PropsWithChildren } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  getJzbProvince,
  getJzbCity,
  searchJzbHasSuperCard,
  getJzbSuperCard,
  getJzbSuperCard2,
  jzbBindCompany,
} from '@/apis';
import { GetPaymentMchRes } from '@/apis/payment/get-payment-mch';
import { getToken } from '@/utils/auth';
import { Spin, Form, Radio, Input, Select, Button, message } from 'antd';
import Card from '../card';
import BankSelect from '../bank-select';
import ZhBankSelect from '../zh-bank-select';
import formStyles from '../../form.module.less';
import styles from './index.module.less';

interface provinceCityProps {
  label: string;
  value: string;
}

interface CorporateCardProps {
  tenantId: string;
  bankType: number;
  mch: GetPaymentMchRes;
}

function CorporateCard({ tenantId, bankType, mch }: PropsWithChildren<CorporateCardProps>) {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isPa, setIspa] = useState(true); // false表示不是平安账户 true表示是平安账户
  const [provinceList, setProvinceList] = useState<provinceCityProps[]>([]); // 省份列表
  const [cityList, setCityList] = useState<provinceCityProps[]>([]); // 城市列表
  const [city, setCity] = useState(undefined as unknown as string); // 城市编码
  const [isFr, setIsFr] = useState('');
  const bankBnkcode = useRef(''); // 大小额联行号
  const bankDreccode = useRef(''); // 清算行号
  const bankName = useRef(''); // 开户行名称
  const superCard = useRef(''); // 超级网银号
  const companyId = mch.companyId || 0;

  // 获取省份
  const getProvince = () => {
    getJzbProvince().then((res) => {
      const list = res?.list || [];
      const newList = list.map((item) => ({
        label: item.nodeNodename,
        value: item.nodeNodecode,
      }));
      setProvinceList(newList);
    });
  };

  // 切换银行
  const onChangeBank = (value: string) => {
    const arry = value.split(',');

    // eslint-disable-next-line prefer-destructuring
    bankName.current = arry[0];

    form.setFieldsValue({
      bankCode: arry[1],
    });
    setIspa(arry[1] === '307');
  };

  // 切换省份
  const changeProvince = (value: string) => {
    const bankCode = form.getFieldValue('bankCode');
    form.setFieldsValue({
      city: undefined,
    });
    setCityList([]);
    getJzbCity({
      bankClscode: bankCode,
      cityNodecode: value,
    }).then((res) => {
      const list = res?.list || [];
      const newList = list.map((item) => ({
        label: item.cityAreaname,
        value: item.cityAreacode,
      }));
      setCityList(newList);
    });
  };

  // 切换城市
  const changeCity = (value: string) => {
    setCity(value);
  };

  // 切换支行
  const onChangeZh = async (value: string) => {
    const arry = value.split(',');
    form.setFieldsValue({
      zhName: arry[0],
    });

    // eslint-disable-next-line prefer-destructuring
    bankBnkcode.current = arry[1];
    // eslint-disable-next-line prefer-destructuring
    bankDreccode.current = arry[2];

    // 查看是否有超级网银号
    const hasSuperCard = await searchJzbHasSuperCard({
      bankDreccode: arry[2],
    });
    if (hasSuperCard.codeCount === 1) {
      const superObj = await getJzbSuperCard({
        bankDreccode: arry[2],
      });
      superCard.current = superObj.superBankCode;
    }
    if (hasSuperCard.codeCount > 1) {
      const superObj = await getJzbSuperCard2({
        bankDreccode: arry[2],
        bankName: arry[0],
      });
      superCard.current = superObj.bankNo;
    }
  };

  // 切换是否是法人
  const onChangeFr = (e: any) => {
    setIsFr(e.target.value);
  };

  // 点击提交的时候执行
  const onFinish = (values: any) => {
    const { dgAccount, zhName, province, yName, yCardNo, yPhone } = values;
    setLoading(true);
    jzbBindCompany({
      tenantId, // 租户id
      companyId, // 公司Id
      memberAcctNo: dgAccount, // 会员银行卡号
      bankType: isPa ? 1 : 2, // 银行类型:1：本行 2：他行
      bindBankType: bankType, // 1：个人卡 2：企业账户
      bankName: bankName.current, // 开户名称
      province, // 开户省
      city, // 开户市
      acctOpenBranchName: isPa ? bankName.current : zhName, // 开户支行名称
      cnapsBranchId: bankBnkcode.current, // 大小额行号：大小额行号和超级网银行号两者二选一必填。
      eiconBankBranchId: superCard.current, // 超级网银行号
      repFlag: isFr, // 会员名称是否是法人：1-是  2-否（个体工商户必输）
      memberName: yName, // 会员名称
      memberGlobalId: yCardNo, // 会员证件号
      mobile: yPhone, // 会员手机号
    })
      .then(() => {
        message.success('提交成功');
        setTimeout(() => {
          navigate(
            `/m/hnc-payment/bindResult?token=${getToken()}&tenantId=${tenantId}&companyId=${companyId}`
          );
        }, 3000);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 点击取消
  const onCancel = () => {
    window.wx.miniProgram.navigateBack();
  };

  useEffect(() => {
    getProvince();
  }, []); // eslint-disable-line

  return (
    <Spin spinning={loading}>
      <div className={formStyles.formHorizontal}>
        <Form form={form} validateTrigger="onSubmit" onFinish={onFinish}>
          <Card>
            <div className={styles.cardBoxBd}>
              <Form.Item
                name="dgAccount"
                label="企业对公账号"
                rules={[{ required: true, message: '请输入企业对公账户账号' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
              <Form.Item
                name="bankCode"
                label="开户行名称"
                rules={[{ required: true, message: '请选择开户行' }]}
              >
                <BankSelect placeholder="请搜索关键字" onChange={onChangeBank} />
              </Form.Item>
              {!isPa ? (
                <>
                  <Form.Item
                    label="开户支行所在省"
                    name="province"
                    rules={[{ required: true, message: '请选择开户支行所在省' }]}
                  >
                    <Select
                      options={provinceList}
                      placeholder="请选择所在省"
                      onChange={changeProvince}
                    />
                  </Form.Item>
                  <Form.Item
                    label="开户支行所在市"
                    name="city"
                    rules={[{ required: true, message: '请选择开户支行所在市' }]}
                  >
                    <Select options={cityList} placeholder="请选择所在市" onChange={changeCity} />
                  </Form.Item>
                </>
              ) : (
                ''
              )}
              {!isPa && city ? (
                <Form.Item
                  name="zhName"
                  label="开户支行名称"
                  rules={[{ required: true, message: '请选择开户支行' }]}
                >
                  <ZhBankSelect
                    optionClass={styles.bankOptions}
                    apiData={{
                      bankClscode: form.getFieldValue('bankCode'),
                      bankCityCode: form.getFieldValue('city'),
                    }}
                    placeholder="请搜索关键字"
                    onChange={onChangeZh}
                  />
                </Form.Item>
              ) : (
                ''
              )}
              <Form.Item
                name="isFr"
                label="您是否为法人"
                rules={[{ required: true, message: '请选择' }]}
              >
                <Radio.Group onChange={onChangeFr}>
                  <Radio value="1">是</Radio>
                  <Radio value="2">否</Radio>
                </Radio.Group>
              </Form.Item>
              {isFr === '2' ? (
                <>
                  <Form.Item
                    name="yName"
                    label="您的姓名"
                    rules={[{ required: true, message: '请填写姓名' }]}
                  >
                    <Input maxLength={20} placeholder="请填写姓名" />
                  </Form.Item>
                  <Form.Item
                    name="yPhone"
                    label="您的手机号"
                    rules={[{ required: true, message: '请填写手机号' }]}
                  >
                    <Input type="number" maxLength={11} placeholder="请填写手机号" />
                  </Form.Item>
                  <Form.Item
                    name="yCardNo"
                    label="您的身份证号码"
                    rules={[{ required: true, message: '请填写身份证号码' }]}
                  >
                    <Input maxLength={20} placeholder="请填写身份证号码" />
                  </Form.Item>
                </>
              ) : (
                ''
              )}
            </div>
          </Card>
          <div className={styles.action}>
            <Button className={styles.cancel} htmlType="button" onClick={onCancel}>
              取消
            </Button>
            <Button className={styles.submit} htmlType="submit">
              发起鉴权
            </Button>
          </div>
        </Form>
      </div>
    </Spin>
  );
}

export default CorporateCard;
