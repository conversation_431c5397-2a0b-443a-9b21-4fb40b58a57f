import { useMemo } from 'react';
import { GetJzbBankInfoRes } from '@/apis/payment/get-jzb-bank-info';
import Card from '../card/index';
import styles from './index.module.less';

interface BankInfoProps {
  bankInfo: GetJzbBankInfoRes;
  pageType: number; // 页面类型，根据不同类型显示不同字段
}

function BankInfo({ bankInfo, pageType }: BankInfoProps) {
  const infoEle = useMemo(() => {
    const {
      bindBankType,
      memberAcctNo,
      bankName,
      indivBusinessFlag,
      acctOpenBranchName,
      memberName,
      agencyClientName,
      agencyClientMobile,
      agencyClientGlobalId,
      repFlag,
      companyName,
      bankType,
      province,
      city,
    } = bankInfo;
    let dtlHtml = null;
    if (memberAcctNo) {
      let arry = [];
      if (bindBankType === 1) {
        arry = [
          {
            field: 'memberAcctNo',
            text: '卡号',
            value: `${memberAcctNo.slice(0, 4)}****${memberAcctNo.slice(-4)}`,
          },
          {
            field: 'bankName',
            text: '开户行名称',
            value: bankName,
          },
          {
            field: 'memberName',
            text: '姓名',
            value: `${memberName.slice(0, 1)}**`,
          },
        ];
      } else {
        arry = [
          {
            field: 'memberAcctNo',
            text: '企业对公账号',
            value: `${memberAcctNo.slice(0, 4)}****${memberAcctNo.slice(-4)}`,
          },
          {
            field: 'bankName',
            text: '开户行名称',
            value: bankName,
          },
        ];
        if (pageType === 2) {
          arry.push({
            field: 'companyName',
            text: '企业名称',
            value: indivBusinessFlag === 1 ? companyName : memberName,
          });
        }
        if (pageType === 1 && repFlag === '2') {
          const jbrArry = [
            {
              field: 'memberName',
              text: '经办人姓名',
              value: agencyClientName ? `${agencyClientName.slice(0, 1)}**` : '',
            },
            {
              field: 'mobile',
              text: '经办人手机',
              value: agencyClientMobile
                ? `${agencyClientMobile.slice(0, 3)}****${agencyClientMobile.slice(-4)}`
                : '',
            },
            {
              field: 'memberGlobalId',
              text: '经办人证件号码',
              value: agencyClientGlobalId
                ? `${agencyClientGlobalId.slice(0, 4)}****${agencyClientGlobalId.slice(-4)}`
                : '',
            },
          ];
          arry.push(...jbrArry);
        }
      }

      if (bankType !== 1) {
        const bankArry = [
          {
            field: 'area',
            text: '开户支行省市',
            value: `${province}${city}`,
          },
          {
            field: 'acctOpenBranchName',
            text: '开户支行名称',
            value: acctOpenBranchName,
          },
        ];
        arry.splice(2, 0, ...bankArry);
      }

      dtlHtml = arry.map((ele) => (
        <div key={ele.field} className={styles.row}>
          <div className={styles.th}>{ele.text}</div>
          <div className={styles.td}>{ele.value}</div>
        </div>
      ));
    }
    return dtlHtml;
  }, [bankInfo, pageType]);

  return (
    <Card extClass={styles.dtlCard}>
      <div className={styles.cardBoxBd}>{infoEle}</div>
    </Card>
  );
}

export default BankInfo;
