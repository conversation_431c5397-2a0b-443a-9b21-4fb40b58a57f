import { useState, PropsWithChildren } from 'react';
import { jzbBindCompanyAgain, jzbCheckAmount } from '@/apis';
import { GetPaymentMchRes } from '@/apis/payment/get-payment-mch';
import { GetJzbTransferStatusRes } from '@/apis/payment/get-jzb-transfer-status';
import { GetJzbBankInfoRes } from '@/apis/payment/get-jzb-bank-info';
import classNames from 'classnames';
import { Spin, Form, Input, Button, message } from 'antd';
import Card from '../card';
import BindStatus from '../bind-status';
import ModalBox from '../modal-box/index';
import formStyles from '../../form.module.less';
import styles from './index.module.less';

const bindStatusProps = {
  imgSrc:
    'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023721/****************.png',
  title: '打款成功',
  msg: '已向您的对公账户打入一笔随机数目的确认金额，请收到短信后正确填写！',
  optType: 1,
  optTxt: '未收到打款短信？',
};

interface BindSuccessProps {
  loading: boolean;
  // eslint-disable-next-line no-unused-vars
  setLoading: (value: boolean) => void;
  mch: GetPaymentMchRes;
  bankInfo: GetJzbBankInfoRes;
  dkInfo: GetJzbTransferStatusRes;
  getDkStatus: () => void;
}

function BindSuccess({
  loading,
  setLoading,
  mch,
  bankInfo,
  dkInfo,
  getDkStatus,
}: PropsWithChildren<BindSuccessProps>) {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);
  const [showReJq, setShowReJq] = useState(false);

  const handleNoMsg = () => {
    setVisible(true);
  };

  const checkAgain = (type: number) => {
    setLoading(true);
    jzbBindCompanyAgain({
      companyId: dkInfo.companyId,
      tenantId: dkInfo.tenantId,
    })
      .then(() => {
        message.success('获取成功');
        if (type === 1) {
          setVisible(false);
        } else {
          setShowReJq(false);
        }
        getDkStatus(); // 重新获取打款状态等信息
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const DialogContent = (
    <div className={styles.checkAgain}>
      <div>未收到打款结果短信，可能有以下原因，请进行检查:</div>
      <div>1、短信接收可能有延迟，请稍后查看；</div>
      <div>2、请确认接收手机号是否为填写的手机号；</div>
      <div>
        接收手机号确认无误，并一直未收到短信，请点击
        <span className={styles.opt} onClick={() => checkAgain(1)} role="button" tabIndex={0}>
          重新获取
        </span>
        （*打款结果短信每日只能获取5次）
      </div>
    </div>
  );

  // 点击提交的时候执行
  const onFinish = (values: any) => {
    setLoading(true);
    jzbCheckAmount({
      jzbCode: dkInfo.jzbCode,
      authAmt: values.money,
      orderNo: values.command,
      companyId: dkInfo.companyId,
      tenantId: dkInfo.tenantId,
    })
      .then(() => {
        message.success('绑卡成功');
        setTimeout(() => {
          window.wx.miniProgram.navigateTo({ url: '/subpackages/pay/pages/hnc-wallet/index' });
        }, 3000);
      })
      .catch((err: { code: number; message: string }) => {
        if (err.code === 3) {
          setShowReJq(true);
        } else {
          message.error(err.message);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 点击取消
  const onCancel = () => {
    window.wx.miniProgram.navigateBack();
  };

  return (
    <Spin spinning={loading}>
      <BindStatus item={bindStatusProps} onClick={handleNoMsg} />
      <div className={classNames(formStyles.formHorizontal, styles.bindValidForm)}>
        <Form form={form} validateTrigger="onSubmit" onFinish={onFinish}>
          <Card extClass={styles.validCard}>
            <div className={styles.title}>提现账户</div>
            <div className={styles.khAccount}>
              <div className={styles.item}>
                <div className={styles.t}>开户名称：</div>
                <div>{mch.companyName}</div>
              </div>
              <div className={styles.item}>
                <div className={styles.t}>开户行：</div>
                <div>
                  {bankInfo.bankName}（{bankInfo.memberAcctNo.slice(-4)}）
                </div>
              </div>
            </div>
            <div className={styles.cardBoxBd}>
              <Form.Item
                name="money"
                label="鉴权金额"
                rules={[
                  () => ({
                    validator(_, value) {
                      if (/^\d+(?:\.\d{1,2})?$/.test(value)) {
                        return Promise.resolve();
                      }
                      if (!value) {
                        return Promise.reject(new Error('请输人账户收到的验证金额'));
                      }
                      return Promise.reject(new Error('只能数字，最多保留两位小数'));
                    },
                  }),
                ]}
              >
                <Input placeholder="请输入账户收到的验证金额" />
              </Form.Item>
              <Form.Item
                name="command"
                label="指令号"
                rules={[{ required: true, message: '请输入短信收到的指令号' }]}
              >
                <Input placeholder="请输入短信收到的指令号" />
              </Form.Item>
            </div>
          </Card>
          <div className={styles.action}>
            <Button className={styles.cancel} htmlType="button" onClick={onCancel}>
              取消
            </Button>
            <Button className={styles.submit} htmlType="submit">
              提交
            </Button>
          </div>
        </Form>
      </div>
      <ModalBox
        title="提示"
        visible={visible}
        showCloseButton
        onClose={() => setVisible(false)}
        content={DialogContent}
      />
      <ModalBox
        title="提示"
        visible={showReJq}
        showCloseButton={false}
        content="提交鉴权申请已超过48小时，请重新发起鉴权"
        actions={[
          {
            bgColor: 'red',
            key: 'ok',
            text: '重新发起鉴权',
            onClick: () => checkAgain(2),
          },
        ]}
      />
    </Spin>
  );
}

export default BindSuccess;
