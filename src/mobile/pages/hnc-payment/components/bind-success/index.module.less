.cardBoxBd {
  overflow: hidden;
}

.bindValidForm {
  :global {
    .ant-form {
      .ant-form-item {
        .ant-form-item-label {
          min-width: 65px;
        }
      }
    }
    .ant-input {
      width: 195px;
    }
  }
}

.validCard {
  .title {
    font-weight: bold;
  }
  .khAccount {
    padding-top: 5px;
    padding-bottom: 12px;
    .item {
      margin-top: 7px;
      display: flex;
      .t{
        width: 80px;
      }
    }
  }
}

.action {
  display: flex;
  padding-bottom: 24px;
  padding-top: 24px;
  .cancel, .submit {
    flex: 1;
  }
  .cancel {
    &, &:hover {
      background-color: #F3F3F3;
    }
    margin-right: 15px;
  }
  .submit,.submit:focus {
    &, &:hover {
      background-color: #FF2636;
      color: #fff;
    }
  }
  // .ant-btn.ant-btn-default:focus
}

.checkAgain {
  line-height: 25px;
  .opt {
    color: #FF2636;
  }
}