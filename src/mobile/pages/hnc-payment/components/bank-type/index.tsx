import { PropsWithChildren } from 'react';
import classNames from 'classnames';
import Card from '../card/index';
import styles from './index.module.less';

interface BankTypeProps {
  title?: string;
  options: {
    value: number;
    label: string;
  }[];
  type: number;
  // eslint-disable-next-line react/require-default-props, no-unused-vars
  changeType?: (value: number) => void;
}

function BankType({ title, options, type, changeType }: PropsWithChildren<BankTypeProps>) {
  return (
    <Card extClass={styles.bankTypeCard}>
      <div className={styles.title}>{title}</div>
      <div className={styles.list}>
        {options.map((ele, index) => (
          <div
            key={ele.value}
            className={classNames(styles.item, {
              [styles.cur]: ele.value === type,
              [styles.item0]: index === 0,
            })}
            role="button"
            tabIndex={0}
            onClick={() => {
              if (changeType) {
                changeType(ele.value);
              }
            }}
          >
            {ele.label}
          </div>
        ))}
      </div>
    </Card>
  );
}

BankType.defaultProps = {
  title: '选择银行账户类型',
};

export default BankType;
