import { useState } from 'react';
import debounce from 'lodash/debounce';
import { getJzbZhBankList } from '@/apis';
import { Select, Spin } from 'antd';

interface ZhBankSelectProps {
  optionClass?: string;
  apiData: {
    bankClscode: string;
    bankCityCode: string;
  };
  placeholder?: string;
  // eslint-disable-next-line no-unused-vars
  onChange: (value: string) => void;
}

interface bankListProps {
  label: string;
  value: string;
}

function ZhBankSelect({ optionClass, apiData, placeholder, onChange }: ZhBankSelectProps) {
  const [fetching, setFetching] = useState(false);
  const [bankList, setBankList] = useState<bankListProps[]>([]);

  const onSearch = debounce((keyword: string) => {
    setBankList([]);
    setFetching(true);
    getJzbZhBankList({
      ...apiData,
      bankLname: keyword,
    })
      .then((res) => {
        const list = res?.list || [];
        const newList = list.map((item) => ({
          label: item.bankLname,
          value: `${item.bankLname},${item.bankBnkcode},${item.bankDreccode}`,
        }));
        setBankList(newList);
      })
      .finally(() => {
        setFetching(false);
      });
  }, 1000);

  return (
    <Select
      dropdownMatchSelectWidth={false}
      placeholder={placeholder}
      showSearch
      filterOption={false}
      onSearch={onSearch}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      onChange={(value: string) => onChange(value)}
    >
      {bankList.map((ele) => (
        <Select.Option key={ele.value} className={optionClass}>
          {ele.label}
        </Select.Option>
      ))}
    </Select>
  );
}

ZhBankSelect.defaultProps = {
  optionClass: '',
  placeholder: '',
};

export default ZhBankSelect;
