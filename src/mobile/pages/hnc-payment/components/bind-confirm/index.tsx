import { PropsWithChildren } from 'react';
import { GetJzbBankInfoRes } from '@/apis/payment/get-jzb-bank-info';
import { Spin } from 'antd';
import BankType from '../bank-type';
import BankInfo from '../bank-info';
import BindStatus from '../bind-status';

const bindStatusProps = {
  imgSrc:
    'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023721/****************.png',
  title: '待确认',
  msg: '已向您的对公账户打入一笔随机数目的确认金额，暂未进账，请稍后查看！',
};

interface BindConfirmProps {
  loading: boolean;
  bankInfo: GetJzbBankInfoRes;
  bankType: number;
  bankTypeOptions: {
    value: number;
    label: string;
  }[];
}

function BindConfirm({
  loading,
  bankInfo,
  bankType,
  bankTypeOptions,
}: PropsWithChildren<BindConfirmProps>) {
  return (
    <Spin spinning={loading}>
      <BindStatus item={bindStatusProps} />
      <BankType title="银行账户类型" options={bankTypeOptions} type={bankType} />
      <BankInfo bankInfo={bankInfo} pageType={1} />
    </Spin>
  );
}

export default BindConfirm;
