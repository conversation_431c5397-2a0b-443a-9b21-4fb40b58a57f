import { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import { setToken } from '@/utils/auth';
import { getPaymentMch } from '@/apis';
import { GetPaymentMchRes } from '@/apis/payment/get-payment-mch';
import { Helmet } from 'react-helmet';
import Members from '../components/members';
import BankType from '../components/bank-type';
import PersonalCard from '../components/personal-card';
import CorporateCard from '../components/corporate-card';
import styles from './index.module.less';

interface optionsProps {
  label: string;
  value: number;
}

function BindCard() {
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const companyId = searchParams.get('companyId') || '0';
  const [mch, setMch] = useState({} as unknown as GetPaymentMchRes);
  const [bankType, setBankType] = useState(2);
  const [bankTypeOptions, setBankTypeOptions] = useState<optionsProps[]>([]);

  // 获取商户信息
  const getMchInfo = async () => {
    const mchObj = await getPaymentMch({
      companyId: Number(companyId),
      tenantId,
    });
    let options = [
      {
        label: '企业账户',
        value: 2,
      },
    ];

    // 前提是个体工商户：当法人证件类型不是身份证时只能选个人卡，否则可以选个人卡和企业账户
    if (mchObj.bizFlag === 1) {
      if (mchObj.reprGlobalType !== '1') {
        options = [
          {
            label: '个人卡',
            value: 1,
          },
        ];
      } else {
        options = [
          {
            label: '个人卡',
            value: 1,
          },
          {
            label: '企业账户',
            value: 2,
          },
        ];
      }
      setBankType(1);
    }
    setBankTypeOptions(options);
    setMch(mchObj);
  };

  // 切换银行账户类型
  const changeBankType = useCallback((value: number) => {
    setBankType(value);
  }, []);

  useEffect(() => {
    const token = searchParams.get('token');
    if (token) {
      setToken(token);
    }
  }, [searchParams]);

  useEffect(() => {
    getMchInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.container}>
      <Helmet>
        <title>绑卡</title>
      </Helmet>
      <Members companyId={Number(companyId)} companyName={mch.companyName} jzbCode={mch.jzbCode} />
      <BankType options={bankTypeOptions} type={bankType} changeType={changeBankType} />
      {bankType === 1 ? <PersonalCard tenantId={tenantId} bankType={bankType} mch={mch} /> : ''}
      {bankType === 2 ? <CorporateCard tenantId={tenantId} bankType={bankType} mch={mch} /> : ''}
    </div>
  );
}

export default BindCard;
