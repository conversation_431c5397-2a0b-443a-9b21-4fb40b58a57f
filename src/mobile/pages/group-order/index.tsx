import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet';
import styles from './index.module.less';

function GroupOrder() {
  const wxMini = () => {
    // @ts-ignore
    // eslint-disable-next-line no-undef
    wx.config({
      debug: true, // 开启调试模式,调用的所有api的返回值会在客户端console.log出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印
      appId: 'wx4e040c79fee61f76',
      timestamp: 0, // 必填，生成签名的时间戳
      nonceStr: 'nonceStr', // 必填，生成签名的随机串
      signature: 'signature', // 必填，签名
      jsApiList: ['chooseImage'], // 必填，需要使用的JS接口列表
      openTagList: ['wx-open-launch-weapp'], // 可选，需要使用的开放标签列表，例如['wx-open-launch-app']
    });
    // @ts-ignore
    // eslint-disable-next-line no-undef
    wx.ready(() => {});
  };

  useEffect(() => {
    const script = document.createElement('script'); // 创建一个script标签
    script.type = 'text/javascript';
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'; // 微信的js文件
    script.async = true;
    document.body.appendChild(script); // 将这个script标签拼接进HTML的body里
    script.onload = wxMini;
  });

  return (
    <div className={styles.box}>
      <Helmet>
        <title>悦安居团购单</title>
      </Helmet>
      <div className={styles.content}>
        <img src="https://img.huahuabiz.com/user_files/20231123/1700703623947508.png" alt="" />
      </div>
      {/* @ts-ignore */}
      <wx-open-launch-weapp
        appid="wx4e040c79fee61f76"
        path="/subpackages/other/pages/h5/index"
        env-version="develop"
        extra-data={JSON.stringify({ id: 21 })}
      >
        <div className={styles.footer}>
          <div className={styles.button}>查看单据</div>
        </div>
        {/* @ts-ignore */}
      </wx-open-launch-weapp>
    </div>
  );
}

export default GroupOrder;
