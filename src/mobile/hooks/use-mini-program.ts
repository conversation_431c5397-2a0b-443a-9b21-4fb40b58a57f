import { useSearchParams } from 'react-router-dom';
import { useMount } from 'ahooks';
import { isWX, isEchMini } from '@/utils/js-bridge';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';

function useMiniProgram(satype?: string): ReturnType<typeof useSearchParams> {
  const [searchParams, navigateSearchParams] = useSearchParams();

  useMount(() => {
    if (isWX || isEchMini) {
      const token = searchParams.get('token');
      const appId = searchParams.get('appId');

      if (token) {
        setToken(token);
      }
      if (appId) {
        setHeader('appId', appId);
      }
      if (satype) {
        setHeader('satype', satype);
      }
    }
  });

  return [searchParams, navigateSearchParams];
}

export default useMiniProgram;
