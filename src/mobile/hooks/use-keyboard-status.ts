import { useState } from 'react';
// import { isIOS, isWeb } from '@/utils/js-bridge';

export default function useKeyboardStatus() {
  const [keyboardStatus, setKeyboardStatus] = useState(false);
  const run = () => {
    const bodyHeight = document.documentElement.clientHeight || document.body.clientHeight;
    window.addEventListener('resize', () => {
      const nowHeight = document.documentElement.clientHeight || document.body.clientHeight;
      const diff = bodyHeight - nowHeight;
      setKeyboardStatus(nowHeight < bodyHeight && diff > 100);
    });
  };

  return { keyboardStatus, run };
}
