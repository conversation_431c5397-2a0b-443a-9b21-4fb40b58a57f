import { useSearchParams } from 'react-router-dom';
import { useState } from 'react';
import { isIOS, isWeb } from '@/utils/js-bridge';
import { setHeader } from '@/utils/http';
import { setToken } from '@/utils/auth';
import sendMessage from '../utils/utils';

export default function useMobileHeader() {
  const [searchParams] = useSearchParams({ headHeight: '44' });
  const [isWx, setIsWx] = useState(true);
  const headHeight = Number(searchParams.get('headHeight'));
  // 返回
  const back = (type: string) => {
    if (type === 'sendMessage') {
      const org = '*';
      window.parent.postMessage({ type: 'back' }, org);
      sendMessage('back', {
        isClose: true,
      });
    } else {
      window.history.back();
    }
  };

  const sendLoadedWebMessage = () => {
    const org = '*';
    window.parent.postMessage({ type: 'load' }, org);
  };

  const watchWebMessage = () => {
    window.addEventListener(
      'message',
      (e) => {
        const { data } = e;
        if (data.type === 'token') {
          setHeader('satype', 'WEB');
          setToken(data.data);
        }
      },
      false
    );
  };

  const run = (isCheckStart = false) => {
    let token = searchParams.get('token');
    if (isCheckStart) {
      if (!token?.startsWith('bearer')) {
        token = `bearer ${token}`;
      }
    }
    if (token) {
      // setHeader('satype', 'ech-aop');
      setToken(token);
      setIsWx(true);
      const appId = searchParams.get('appId');
      const tenantId = searchParams.get('tenantId');
      if (appId) {
        setHeader('appId', appId);
      }
      if (tenantId) {
        setHeader('tenantId', tenantId);
      }
    } else {
      setIsWx(false);
      if (!isWeb) {
        setHeader('satype', isIOS ? 'IOS' : 'ANDROID');
      }
      sendLoadedWebMessage();
      watchWebMessage();
    }
  };

  return { isWx, headHeight, back, run };
}
