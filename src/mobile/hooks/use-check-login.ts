import { getUserCurrentUserInfo } from '@/apis';

export default function useCheckLogin() {
  const redirectToLogin = () => {
    const { pathname } = window.location;
    const url = `/subpackages/auth/pages/login/index?h5PageForwardingUrl=${pathname.slice(1)}`;
    window.wx.miniProgram.redirectTo({
      url,
    });
  };

  const checkLogin = (redirect = true) =>
    new Promise((resolve, reject) => {
      getUserCurrentUserInfo()
        .then(() => {
          resolve(true);
        })
        .catch((err) => {
          if (err.code === 4 && redirect) {
            redirectToLogin();
            // eslint-disable-next-line
            reject(false);
          }
        });
    });

  return { redirectToLogin, checkLogin };
}
