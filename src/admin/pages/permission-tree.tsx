import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Tree, Button, Form, Input, message } from 'antd';
import classNames from 'classnames';
import { Context, Modal } from '@/components';
import {
  getSystemAllPermissionTreeList,
  addSystemPermission,
  removeSystemPermission,
} from '@/apis';
import type { GetSystemAllPermissionTreeListRes, addSystemPermissionParams } from '@/apis';
import { testPerm } from '@/utils/permission';

// import recursiveNext from '../utils/recursive';
import styles from './permission-tree.module.less';

type NewGetSystemAllPermissionTreeListRes = GetSystemAllPermissionTreeListRes & {
  key: number;
  children?: NewGetSystemAllPermissionTreeListRes[];
  isSearch?: boolean;
};

type CurrentItemState = addSystemPermissionParams & {
  funcName: string;
};

const titles = ['权限字典'];

const getParentKey = (key: React.Key, tree: NewGetSystemAllPermissionTreeListRes[]): React.Key => {
  let parentKey: React.Key;
  for (let i = 0; i < tree.length; i += 1) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some((item) => item.id === key)) {
        parentKey = node.key;
      } else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey!;
};

function PermissionTree() {
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [operateStatus, setOperateStatus] = useState('add');
  const [form] = Form.useForm();
  const initialValues = { parentId: 0, permCode: '', permName: '', id: 0, funcId: 0 };
  const currentItem = useRef<addSystemPermissionParams>({
    id: 0,
    parentId: 0,
    permCode: '',
    permName: '',
    funcId: 0,
  });

  const [currentItemState, setCurrentItemState] = useState<CurrentItemState>({
    id: 0,
    parentId: 0,
    permCode: '',
    permName: '',
    funcId: 0,
    funcName: '',
  });

  // 搜索
  const permData = useRef<GetSystemAllPermissionTreeListRes[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const searchValue = useRef('');
  // const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [treeData, setTreeData] = useState<NewGetSystemAllPermissionTreeListRes[]>([]);

  const getTreeData = useCallback(() => {
    setLoading(true);
    getSystemAllPermissionTreeList()
      .then((res) => {
        // @ts-ignore
        const list: NewGetSystemAllPermissionTreeListRes[] = res?.list || [];
        const allArr: NewGetSystemAllPermissionTreeListRes[] = [];
        const recursive = (arr: NewGetSystemAllPermissionTreeListRes[]) => {
          arr.forEach((item) => {
            allArr.push(item);
            const newItem = item;
            newItem.key = item.id;
            if (searchValue.current) {
              newItem.isSearch = searchValue.current
                ? newItem.permName.includes(searchValue.current) ||
                  newItem.permCode.includes(searchValue.current)
                : false;
            }
            if (newItem.children?.length) {
              recursive(newItem.children);
            }
          });
        };

        recursive(list);
        setTreeData(list);
        permData.current = allArr;
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const onSearch = (val: string) => {
    searchValue.current = val;
    const newExpandedKeys = val
      ? permData.current
          .map((item) => {
            if (item.permCode.includes(val) || item.permName.includes(val)) {
              return getParentKey(item.id, treeData);
            }
            return null;
          })
          .filter((item) => item)
      : [];
    // .filter((item, i, self) => item && self.indexOf(item) === i);

    const recursive = (arr: NewGetSystemAllPermissionTreeListRes[]) => {
      arr.forEach((item) => {
        const newItem = item;
        newItem.isSearch = val
          ? newItem.permName.includes(val) || newItem.permCode.includes(val)
          : false;
        if (newItem.children?.length) {
          recursive(newItem.children);
        }
      });
    };

    recursive(treeData);
    setTreeData([...treeData]);
    setExpandedKeys(newExpandedKeys as React.Key[]);
    setAutoExpandParent(true);
  };

  const onAddPerm = (nodeData: NewGetSystemAllPermissionTreeListRes) => {
    if (!testPerm('AR_001_004_002')) {
      return;
    }

    setOperateStatus('add');
    setIsModalOpen(true);
    // currentItem.current = nodeData.parentId
    //   ? { ...initialValues, parentId: nodeData.parentId }
    //   : { ...initialValues };
    currentItem.current = { ...initialValues, parentId: nodeData.id, funcId: nodeData.funcId || 0 };
    setCurrentItemState({
      permName: nodeData.permName,
      permCode: nodeData.permCode,
      parentId: nodeData.id,
      funcId: nodeData.funcId || 0,
      funcName: nodeData.funcName || '',
    });

    form.setFieldsValue(initialValues);
  };

  const onEditPerm = (nodeData: NewGetSystemAllPermissionTreeListRes) => {
    if (!testPerm('AR_001_004_002')) {
      return;
    }
    setOperateStatus('edit');
    currentItem.current.id = nodeData.id;
    currentItem.current.parentId = nodeData.parentId;
    currentItem.current.permCode = nodeData.permCode;
    currentItem.current.permName = nodeData.permName;
    currentItem.current.funcId = nodeData.funcId;
    setCurrentItemState({
      permName: nodeData.permName,
      permCode: nodeData.permCode,
      parentId: nodeData.id,
      funcId: nodeData.funcId || 0,
      funcName: nodeData.funcName || '',
    });
    form.setFieldsValue(currentItem.current);
    setIsModalOpen(true);
  };

  const onRemovePerm = (nodeData: NewGetSystemAllPermissionTreeListRes) => {
    if (!testPerm('AR_001_004_003')) {
      return;
    }
    const ids: number[] = [];
    ids.push(nodeData.id);
    if (nodeData?.children) {
      const recursive = (arr: NewGetSystemAllPermissionTreeListRes[]) => {
        arr.forEach((item) => {
          ids.push(item.id);
          if (item.children?.length) {
            recursive(item.children);
          }
        });
      };
      recursive(nodeData?.children);
    }

    const ModalContent = (
      <div>
        是否删除权限
        <span style={{ color: 'red', margin: '0px 6px' }}>
          {nodeData.permName}（编码：{nodeData.permCode}）
        </span>
        ? {nodeData?.children?.length ? '(当前权限存在子级，删除当前权限会把子级权限都删除掉)' : ''}
      </div>
    );
    Modal.confirm({
      title: '提示',
      // content: `是否删除权限   ${nodeData.permName}（编码：${nodeData.permCode}）   ?${
      //   nodeData?.children?.length ? '(当前权限存在子级，删除当前权限会把子级权限都删除掉)' : ''
      // }`,
      content: ModalContent,
      okText: '确认',
      cancelText: '取消',
      onOk() {
        removeSystemPermission({ ids }).then(() => {
          message.success('删除成功');
          getTreeData();
        });
      },
    });
  };

  const onModalOk = () => {
    form
      .validateFields()
      .then(() => {
        const { permName, permCode } = form.getFieldsValue();
        const { id, funcId } = currentItem.current;
        let postData: addSystemPermissionParams = {
          permName,
          permCode,
          parentId: currentItem.current.parentId || 0,
          funcId: funcId || 0,
        };
        if (id && operateStatus === 'edit') {
          postData = { ...postData, id };
        }
        addSystemPermission(postData).then(() => {
          message.success(`${operateStatus === 'add' ? '添加' : '修改'}成功`);
          setIsModalOpen(false);
          getTreeData();
        });
      })
      .catch(() => {
        message.error('请填写或选择带星号的内容');
      });
  };

  const onModalCancel = () => {
    setIsModalOpen(false);
  };

  useEffect(() => {
    getTreeData();
  }, [getTreeData]);

  const createTreeTitle = (nodeData: NewGetSystemAllPermissionTreeListRes) => (
    <div className={classNames(styles.treeTitle, nodeData.isSearch ? styles.isSearchTitle : '')}>
      <div className={styles.treeTitleText}>
        <span>{nodeData.permName}</span>
        <span className={styles.treeTitleTextCode}>（编码：{nodeData.permCode}）</span>
      </div>
      <div className={styles.treeTitleOperate}>
        <Button
          type="primary"
          size="small"
          className={styles.addBtn}
          onClick={() => {
            onAddPerm(nodeData);
          }}
        >
          添加子级
        </Button>

        <Button
          type="primary"
          size="small"
          className={styles.addBtn}
          onClick={() => {
            onEditPerm(nodeData);
          }}
        >
          编辑
        </Button>
        <Button
          type="default"
          size="small"
          onClick={() => {
            onRemovePerm(nodeData);
          }}
        >
          删除
        </Button>
      </div>
    </div>
  );

  return (
    <Context
      permission="AR_001_004_001"
      head={
        <Context.Head
          title={titles}
          onSearch={onSearch}
          realTimeSearch={false}
          // extra={
          //   <div className={styles.addBtn}>
          //     <Button
          //       type="primary"
          //       size="small"
          //       style={{ marginLeft: 10 }}
          //       onClick={() => {
          //         if (!testPerm('AR_001_004_002')) {
          //           return;
          //         }
          //         setOperateStatus('add');
          //         setIsModalOpen(true);
          //         currentItem.current = { ...initialValues };
          //         setCurrentItemState({ ...initialValues, funcName: '' });
          //         form.setFieldsValue(initialValues);
          //       }}
          //     >
          //       添加权限
          //     </Button>
          //   </div>
          // }
        />
      }
      loading={loading}
    >
      <div className={styles.permissionTree}>
        {treeData.length ? (
          <Tree
            // checkable
            // defaultExpandedKeys={['0-0-0', '0-0-1']}
            // defaultSelectedKeys={['0-0-0', '0-0-1']}
            // defaultCheckedKeys={['0-0-0', '0-0-1']}
            // onSelect={onSelect}
            // onCheck={onCheck}
            treeData={treeData}
            // @ts-ignore
            titleRender={(nodeData) => createTreeTitle(nodeData)}
            blockNode
            selectable={false}
            onExpand={onExpand}
            expandedKeys={expandedKeys}
            autoExpandParent={autoExpandParent}
          />
        ) : (
          ''
        )}
      </div>
      <Modal
        title={`${operateStatus === 'add' ? '添加' : '修改'}${
          currentItemState.permName && operateStatus === 'add' ? '子' : ''
        }权限`}
        visible={isModalOpen}
        onOk={onModalOk}
        onCancel={onModalCancel}
      >
        {currentItemState.permName ? (
          <div style={{ marginBottom: 10 }}>
            <div>
              当前权限： {currentItemState.permName}（编码：{currentItemState.permCode}）
            </div>
            {currentItemState.funcName ? <div>当前应用：{currentItemState.funcName}</div> : ''}
          </div>
        ) : (
          ''
        )}

        <Form
          form={form}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          initialValues={initialValues}
        >
          <Form.Item
            label="权限名称"
            name="permName"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input maxLength={10} />
          </Form.Item>
          <Form.Item
            label="权限编码"
            name="permCode"
            rules={[{ required: true, message: '请输入权限名称' }]}
          >
            <Input maxLength={50} />
          </Form.Item>
        </Form>
      </Modal>
    </Context>
  );
}

export default PermissionTree;
