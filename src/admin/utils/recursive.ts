import { isArray } from 'lodash';

interface callbackObjProps<W> {
  childCallback?: MultipleParamsFn<[item: W]>;
  nextStepCallback?: MultipleParamsFn<[item: W]>;
  lastStepCallback?: MultipleParamsFn<[item: W]>;
}
// 递归树结构数据
export default function recursiveNext<T>(
  value: Array<T> | T,
  callbackObj: callbackObjProps<T>,
  childKey = 'childs'
) {
  if (isArray(value)) {
    value.forEach((childItem) => {
      if (callbackObj?.childCallback) {
        callbackObj.childCallback(childItem);
      }
      // @ts-ignore
      const childArr = childItem[childKey];
      if (childArr?.length) {
        if (callbackObj?.nextStepCallback) {
          callbackObj.nextStepCallback(childItem);
        }
        recursiveNext(childArr, callbackObj);
      } else if (callbackObj?.lastStepCallback) {
        callbackObj.lastStepCallback(childItem);
      }
    });
  } else {
    // @ts-ignore
    const valueArr = value[childKey];
    if (valueArr?.length) {
      valueArr.forEach((childItem: T) => {
        if (callbackObj.childCallback) {
          callbackObj.childCallback(childItem);
        }
        // @ts-ignore
        const childArr = childItem[childKey];
        if (childArr?.length) {
          if (callbackObj?.nextStepCallback) {
            callbackObj.nextStepCallback(childItem);
            recursiveNext(childArr, callbackObj);
          }
        } else if (callbackObj?.lastStepCallback) {
          callbackObj.lastStepCallback(childItem);
        }
      });
    }
  }
}

// export function removeTreeListItem<T>(
//   treeList: Array<T>,
//   key: string,
//   value: number | number,
//   childKey = 'children'
// ) {
//   // 根据id属性从数组（树结构）中移除元素
//   if (!treeList || !treeList.length) {
//     return;
//   }
//   for (let i = 0; i < treeList.length; i += 1) {
//     // @ts-ignore
//     if (treeList[i][key] === value) {
//       treeList.splice(i, 1);
//       break;
//     }
//     // @ts-ignore
//     removeTreeListItem(treeList[i][childKey], key, value);
//   }
//   return treeList;
// }
