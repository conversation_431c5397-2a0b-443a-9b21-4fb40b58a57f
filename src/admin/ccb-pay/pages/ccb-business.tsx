import { useRef, useMemo, useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, message, Table } from 'antd';
import type { ColumnsType } from 'antd/es/table';

import { getPaymentCcbCustomerList, importPaymentCustomer, exportPaymentCcbCustomer } from '@/apis';

import type { GetPaymentCcbCustomerListRes } from '@/apis';
import { Context } from '@/components';
import { usePermission } from '@/hooks';
// import { checkCharge, checkPermission } from '@/utils/permission';

import { CcbBusinessFilterDrawer, ImportExcelDrawer } from '../container/index';
import styles from './order-ledger.module.less';

// const companyCertificateTypeObject: { [key: string]: string } = {
//   '01': '组织机构代码证',
//   '02': '营业执照',
//   '03': '其他',
//   '04': '统一社会信用代码',
// };

// const contactCertificateTypeObject: { [key: string]: string } = {
//   '01': '居民身份证',
//   '02': '其他',
// };

type DataType = GetPaymentCcbCustomerListRes & {
  index: number;
};

export default function CcbBusiness() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  // const mallId = searchParams.get('mallId') || '';
  const tableRef = useRef<null | HTMLTableElement>(null);
  const contentRef = useRef<null | HTMLDivElement>(null);
  const [scrollHeight, setScrollHeight] = useState(500);
  const [customerData, setCustomerData] = useState<DataType[]>([]);
  const [filterData, setFilterData] = useState({
    keywords: '',
  });

  const [params, setParams] = useState({ pageNo: 1, pageSize: 10, total: 0, keywords: '' });
  const paramsRef = useRef({ pageNo: 1, pageSize: 10, total: 0, keywords: '' });

  const isFilterActive = useMemo(
    () => Boolean(Object.values(filterData).filter((val) => val).length),
    [filterData]
  );
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [showImportDrawer, setShowImportDrawer] = useState(false);

  const quickOptions = [
    {
      label: '商家订单分账',
      value: `/ccb-pay/order/ledger?tenantId=${tenantId}`,
    },
    {
      label: '建行商家',
      value: `/ccb-pay/ccb/business?tenantId=${tenantId}`,
    },
    {
      label: '建行分账规则',
      value: `/ccb-pay/ccb/rule?tenantId=${tenantId}`,
    },
  ];
  const quickValue = `/ccb-pay/ccb/business?tenantId=${tenantId}`;

  const columnOptions: ColumnsType<DataType> = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
    },
    {
      title: '建行商家编号',
      dataIndex: 'mktMrchId',
      align: 'center',
    },
    {
      title: '建行商家名称',
      dataIndex: 'mktMrchNm',
      align: 'center',
    },
    {
      title: '商家证件类型',
      dataIndex: 'mrchCrdtTp',
      align: 'center',
      // render: (_, { mrchCrdtTp }) => <div>{companyCertificateTypeObject[mrchCrdtTp]}</div>,
    },
    {
      title: '商家证件号码',
      dataIndex: 'mrchCrdtNo',
      align: 'center',
    },
    {
      title: 'POS编号',
      dataIndex: 'posNo',
      align: 'center',
    },
    {
      title: '柜台代码',
      dataIndex: 'mrchCnterCd',
      align: 'center',
    },
    {
      title: '联系人',
      dataIndex: 'ctcpsnNm',
      align: 'center',
    },
    {
      title: '联系人手机号',
      dataIndex: 'mblphNo',
      align: 'center',
    },
    {
      title: '联系人证件类型',
      dataIndex: 'crdtTp',
      align: 'center',
      // render: (_, { crdtTp }) => <div>{contactCertificateTypeObject[crdtTp]}</div>,
    },
    {
      title: '联系人证件号码',
      dataIndex: 'crdtNo',
      align: 'center',
    },
  ];

  const getCustomerList = () => {
    const { pageNo, pageSize, keywords } = paramsRef.current;
    getPaymentCcbCustomerList({
      pageNo,
      pageSize,
      keywords,
      tenantId,
      // mallId: Number(mallId),
    }).then((res) => {
      const data = res.list.map((item, index) => ({
        ...item,
        index: index + 1 + (pageNo - 1) * pageSize,
      }));
      const pageParams = {
        pageNo,
        pageSize,
        keywords,
        total: res.pagination.count || 0,
      };
      setParams({ ...pageParams });
      paramsRef.current = pageParams;
      setCustomerData(data);
    });
  };

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const onImport = usePermission('', (file: File | null) => {
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('tenantId', tenantId);
      // formData.append('mallId', mallId);
      importPaymentCustomer(formData).then((res) => {
        if (!res.successNum) {
          message.error('导入失败');
          return;
        }
        paramsRef.current = { pageNo: 1, pageSize: 10, total: 0, keywords: '' };
        getCustomerList();
        if (!res.errorNum) {
          message.success('导入成功');
          return;
        }
        message.info(`导入成功${res.successNum}条，导入失败${res.errorNum}条`);
      });
    }
  });

  const onExport = () => {
    setExportLoading(true);
    exportPaymentCcbCustomer({
      // mallId: Number(mallId),
      tenantId,
    })
      .then((res) => {
        if (res.type !== 'application/json') {
          const URL = window.webkitURL || window.URL;
          const url = URL.createObjectURL(res);
          urlDownload(url, `建行商家模板.xls`);
        } else {
          message.error(`系统开小差，请稍后再试`);
        }
      })
      .finally(() => {
        setExportLoading(false);
      });
  };

  const onFilter = usePermission('M_001_010_001_003', () => {
    setShowFilterDrawer(true);
  });

  useEffect(() => {
    setTimeout(() => {
      if (contentRef.current?.offsetHeight) {
        setScrollHeight(contentRef.current.offsetHeight - 162);
        if (tableRef.current?.style) {
          const tableBody = document.querySelector(
            `.${styles.ccbPayContentBody} .ant-table-body`
          ) as HTMLDivElement;
          if (tableBody) {
            tableBody.style.height = `${contentRef.current.offsetHeight - 162}px`;
          }
        }
      }
    }, 100);
    getCustomerList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Context
      className={styles.ccbPay}
      // permission={{ code: 'M_001_005_001', newLogic: true }}
      head={
        <Context.Head
          title={[{ title: '商家分账' }]}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              defaultValue={quickValue}
              dropdownMatchSelectWidth={120}
              options={quickOptions}
              onChange={navigate}
            />
          }
          isFilterActive={isFilterActive}
          onFilter={() => {
            onFilter();
          }}
        />
      }
    >
      <div className={styles.ccbPayContent}>
        <div className={styles.ccbPayContentHead}>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setShowImportDrawer(true);
            }}
          >
            导入
          </Button>
          {/* <div role="presentation" className={styles.downloadA} onClick={onExport}>
            下载模板
          </div> */}
        </div>
        <div className={styles.ccbPayContentBody} ref={contentRef}>
          <Table
            ref={tableRef}
            columns={columnOptions}
            dataSource={customerData}
            scroll={{ y: scrollHeight, x: 700 }}
            pagination={{
              current: params.pageNo || 1,
              defaultCurrent: params.pageNo || 1,
              defaultPageSize: params.pageSize || 10,
              total: params.total || 1,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number) => `共 ${total} 条`,
              onChange: (pageNo, pageSize) => {
                const pageParams = { ...params, pageNo, pageSize };
                paramsRef.current = pageParams;
                setParams(pageParams);
                getCustomerList();
              },
            }}
          />
        </div>
      </div>

      <CcbBusinessFilterDrawer
        visible={showFilterDrawer}
        formData={filterData}
        onOk={(val) => {
          setFilterData(val);
          paramsRef.current.pageNo = 1;
          paramsRef.current.keywords = val.keywords;
          paramsRef.current.pageNo = 1;
          getCustomerList();
          setShowFilterDrawer(false);
        }}
        onClose={() => {
          setShowFilterDrawer(false);
        }}
      />

      <ImportExcelDrawer
        visible={showImportDrawer}
        exportLoading={exportLoading}
        onOk={(file) => {
          setShowImportDrawer(false);
          onImport(file);
        }}
        onExport={() => {
          onExport();
        }}
        onClose={() => {
          setShowImportDrawer(false);
        }}
        onCancel={() => {
          setShowImportDrawer(false);
        }}
      />
    </Context>
  );
}
