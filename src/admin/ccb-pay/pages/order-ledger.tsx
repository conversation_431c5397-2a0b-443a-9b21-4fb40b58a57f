import { useRef, useMemo, useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Table, Dropdown, Menu, message, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import {
  getPaymentCcbOrderLedgerList,
  exportPaymentCcbOrderLedger,
  importPaymentOrderLedger,
} from '@/apis';
import type { GetPaymentCcbOrderLedgerListRes } from '@/apis';
import { Context, Icon } from '@/components';
import { usePermission } from '@/hooks';
// import { checkCharge, checkPermission } from '@/utils/permission';

import {
  OrderLedgerFilterDrawer,
  SetCustomerDrawer,
  SetRuleDrawer,
  SplitRuleDetailDrawer,
  ImportExcelDrawer,
} from '../container/index';
import type { OrderLedgerFormValueOption } from '../container/index';
import styles from './order-ledger.module.less';

// const typeOptions = [
//   { value: 1, label: '企业商户' },
//   { value: 2, label: '工长 ' },
//   { value: 3, label: '设计公司' },
//   { value: 4, label: '装修公司' },
//   { value: 4, label: '城市运营商' },
// ];

export type DataType = GetPaymentCcbOrderLedgerListRes & {
  index: number;
  key: number;
};

export default function OrderLedger() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  // const mallId = Number(searchParams.get('mallId') || 0);
  const tableRef = useRef<null | HTMLTableElement>(null);
  const contentRef = useRef<null | HTMLDivElement>(null);
  const [scrollHeight, setScrollHeight] = useState(500);
  const [loading, setLoading] = useState(false);
  const [orderLedgerData, setOrderLedgerData] = useState<DataType[]>([]);
  const [filterData, setFilterData] = useState<OrderLedgerFormValueOption>({
    keywords: '',
    type: 0,
  });
  const [params, setParams] = useState({
    pageNo: 1,
    pageSize: 10,
    total: 0,
    keywords: '',
    type: 0,
  });
  const paramsRef = useRef({ pageNo: 1, pageSize: 10, total: 0, keywords: '', type: 0 });

  const isFilterActive = useMemo(
    () => Boolean(Object.values(filterData).filter((val) => val).length),
    [filterData]
  );
  const [exportLoading, setExportLoading] = useState(false);
  const [showImportDrawer, setShowImportDrawer] = useState(false);
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [showSetCustomerDrawer, setShowSetCustomerDrawer] = useState(false);
  const [showSetRuleDrawer, setShowSetRuleDrawer] = useState(false);
  const [showSplitRuleDetailDrawer, setShowSplitRuleDetailDrawer] = useState(false);
  const [currentItem, setCurrentItem] = useState({} as DataType);
  const [selectedRowKeys, setSelectedRowKeys] = useState<Record<number, number[]>>({ 1: [] });

  // const quickOptions = [
  //   {
  //     label: '商家订单分账',
  //     value: `/ccb-pay/order/ledger?tenantId=${tenantId}&mallId=${mallId}`,
  //   },
  //   {
  //     label: '建行商家',
  //     value: `/ccb-pay/ccb/business?tenantId=${tenantId}&mallId=${mallId}`,
  //   },
  //   {
  //     label: '建行分账规则',
  //     value: `/ccb-pay/ccb/rule?tenantId=${tenantId}&mallId=${mallId}`,
  //   },
  // ];
  // const quickValue = `/ccb-pay/order/ledger?tenantId=${tenantId}&mallId=${mallId}`;
  const quickOptions = [
    {
      label: '商家订单分账',
      value: `/ccb-pay/order/ledger?tenantId=${tenantId}`,
    },
    {
      label: '建行商家',
      value: `/ccb-pay/ccb/business?tenantId=${tenantId}`,
    },
    {
      label: '建行分账规则',
      value: `/ccb-pay/ccb/rule?tenantId=${tenantId}`,
    },
  ];
  const quickValue = `/ccb-pay/order/ledger?tenantId=${tenantId}`;

  const columnOptions: ColumnsType<DataType> = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
    },
    {
      title: '企业名称',
      dataIndex: 'enterCompanyName',
      align: 'center',
      render: (_val, { enterCompanyName, isAuth }) => (
        <div className={styles.companyName}>
          <div>{enterCompanyName}</div>
          <Tooltip placement="top" title={isAuth ? '已认证' : '未认证'}>
            <img
              className={styles.img}
              src={
                isAuth
                  ? 'https://img.huahuabiz.com/user_files/20221117/1668679048574569.png'
                  : 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/20221219/1671412658040161.png'
              }
              alt=""
            />
          </Tooltip>
        </div>
      ),
    },
    {
      title: '店铺名称',
      dataIndex: 'shopName',
      align: 'center',
    },
    {
      title: '入驻身份',
      dataIndex: 'typeName',
      align: 'center',
      // render: (_val, { type }) => {
      //   const typeArr = typeOptions.filter((val) => type === val.value);
      //   let text = '--';
      //   if (typeArr.length) {
      //     text = typeArr[0].label;
      //   }
      //   return <div>{text}</div>;
      // },
    },
    {
      title: '建行商家编号',
      dataIndex: 'mktMrchId',
      align: 'center',
    },
    {
      title: '建行商家名称',
      dataIndex: 'mktMrchNm',
      align: 'center',
    },
    {
      title: '分账规则',
      dataIndex: 'quantity',
      align: 'center',
      render: (_val, record) =>
        record.quantity ? (
          <div
            className={styles.ruleNum}
            role="presentation"
            onClick={() => {
              setCurrentItem(record);
              setShowSplitRuleDetailDrawer(true);
            }}
          >
            {record.quantity}条
          </div>
        ) : (
          <div>{record.quantity}条</div>
        ),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Dropdown
          overlay={
            <Menu
              items={
                !record.isOperator
                  ? [
                      { label: `${record.subMchId ? '修改' : '配置'}建行商家`, key: 'setCustomer' },
                      { label: `${record.quantity ? '修改' : '配置'}分账规则`, key: 'setRule' },
                    ]
                  : [{ label: `${record.subMchId ? '修改' : '配置'}建行商家`, key: 'setCustomer' }]
              }
              onClick={({ key, domEvent }) => {
                domEvent.stopPropagation();
                setCurrentItem(record);
                if (key === 'setCustomer') {
                  setShowSetCustomerDrawer(true);
                } else {
                  setShowSetRuleDrawer(true);
                }
              }}
            />
          }
          placement="bottom"
        >
          <Icon
            name="zu13366"
            size={24}
            color="#999EB2"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          />
        </Dropdown>
      ),
    },
  ];

  const getOrderLedgerList = () => {
    setLoading(true);
    const { pageNo, pageSize, keywords, type } = paramsRef.current;
    const paramData = {
      ...{
        // mallId,
        tenantId,
        keywords,
        pageNo,
        pageSize,
      },
      ...(type ? { type } : {}),
    };
    getPaymentCcbOrderLedgerList(paramData)
      .then((res) => {
        const data = res.list.map((item, index) => ({
          ...item,
          index: (pageNo - 1) * pageSize + index + 1,
          key: item.enterCompanyId,
        }));
        const pageParams = {
          pageNo,
          pageSize,
          keywords,
          total: res.pagination.count || 0,
          type,
        };
        setParams({ ...pageParams });
        paramsRef.current = pageParams;
        setOrderLedgerData([...data]);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const onFilter = usePermission('', () => {
    setShowFilterDrawer(true);
  });

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const onExport = usePermission('', (subCompanyIds: number[] = []) => {
    setExportLoading(true);
    exportPaymentCcbOrderLedger({
      // mallId,
      tenantId,
      ...(subCompanyIds.length ? { subCompanyIds } : {}),
    })
      .then((res) => {
        if (res.type !== 'application/json') {
          const URL = window.webkitURL || window.URL;
          const url = URL.createObjectURL(res);
          urlDownload(url, `商家订单分账模板.xls`);
        } else {
          message.error(`系统开小差，请稍后再试`);
        }
      })
      .finally(() => {
        setExportLoading(false);
      });
  });

  const onImport = usePermission('', (file: File | null) => {
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('tenantId', tenantId);
      // formData.append('mallId', String(mallId));

      importPaymentOrderLedger(formData).then((res) => {
        if (!res.successNum) {
          message.error('导入失败');
          return;
        }
        paramsRef.current = { pageNo: 1, pageSize: 10, total: 0, keywords: '', type: 0 };
        getOrderLedgerList();
        if (!res.errorNum) {
          message.success('导入成功');
          return;
        }
        message.info(`导入成功${res.successNum}条，导入失败${res.errorNum}条`);
      });
    }
  });

  useEffect(() => {
    setTimeout(() => {
      if (contentRef.current?.offsetHeight) {
        setScrollHeight(contentRef.current.offsetHeight - 132);
        if (tableRef.current?.style) {
          const tableBody = document.querySelector(
            `.${styles.ccbPayContentBody} .ant-table-body`
          ) as HTMLDivElement;
          if (tableBody) {
            tableBody.style.height = `${contentRef.current.offsetHeight - 132}px`;
          }
        }
      }
    }, 60);
    getOrderLedgerList();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Context
      className={styles.ccbPay}
      // permission={{ code: 'M_001_005_001', newLogic: true }}
      head={
        <Context.Head
          title={[{ title: '商家分账' }]}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              defaultValue={quickValue}
              dropdownMatchSelectWidth={120}
              options={quickOptions}
              onChange={navigate}
            />
          }
          isFilterActive={isFilterActive}
          onFilter={() => {
            onFilter();
          }}
        />
      }
    >
      <div className={styles.ccbPayContent}>
        <div className={styles.ccbPayContentHead}>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setShowImportDrawer(true);
            }}
          >
            导入
          </Button>
          <Button
            type="primary"
            size="small"
            style={{ marginLeft: 10 }}
            onClick={() => {
              const arr = Object.values(selectedRowKeys);
              const subCompanyIds = arr.flat();
              onExport(subCompanyIds);
            }}
          >
            下载模板
          </Button>
        </div>
        <div className={styles.ccbPayContentBody} ref={contentRef}>
          <Table
            ref={tableRef}
            rowSelection={{
              type: 'checkbox',
              selectedRowKeys: selectedRowKeys[params.pageNo],
              defaultSelectedRowKeys: selectedRowKeys[params.pageNo],
              // @ts-ignore
              onChange: (keys: number[]) => {
                selectedRowKeys[params.pageNo] = keys;
                setSelectedRowKeys({ ...selectedRowKeys });
              },
            }}
            columns={columnOptions}
            dataSource={orderLedgerData}
            scroll={{ y: scrollHeight }}
            loading={loading}
            pagination={{
              current: params.pageNo || 1,
              defaultCurrent: params.pageNo || 1,
              defaultPageSize: params.pageSize || 10,
              total: params.total || 1,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number) => `共 ${total} 条`,
              onChange: (pageNo, pageSize) => {
                const pageParams = { ...params, pageNo, pageSize };
                setParams({ ...pageParams });
                paramsRef.current = pageParams;
                getOrderLedgerList();
              },
            }}
          />
        </div>
      </div>

      <OrderLedgerFilterDrawer
        visible={showFilterDrawer}
        tenantId={tenantId}
        formData={filterData}
        onOk={(val) => {
          setFilterData(val);
          paramsRef.current.keywords = val.keywords;
          paramsRef.current.type = val.type;
          paramsRef.current.pageNo = 1;
          getOrderLedgerList();
          setShowFilterDrawer(false);
        }}
        onClose={() => {
          setShowFilterDrawer(false);
        }}
      />

      <SetCustomerDrawer
        visible={showSetCustomerDrawer}
        subId={currentItem.subMchId}
        tenantId={tenantId}
        // mallId={mallId}
        info={currentItem}
        onSuccess={() => {
          setShowSetCustomerDrawer(false);
          getOrderLedgerList();
        }}
        onClose={() => {
          setShowSetCustomerDrawer(false);
        }}
      />

      <SetRuleDrawer
        visible={showSetRuleDrawer}
        tenantId={tenantId}
        // mallId={mallId}
        info={currentItem}
        onSuccess={() => {
          setShowSetRuleDrawer(false);
          getOrderLedgerList();
        }}
        onClose={() => {
          setShowSetRuleDrawer(false);
        }}
      />

      <SplitRuleDetailDrawer
        visible={showSplitRuleDetailDrawer}
        id={currentItem.ruleId}
        tenantId={tenantId}
        // mallId={mallId}
        onClose={() => {
          setShowSplitRuleDetailDrawer(false);
        }}
      />

      <ImportExcelDrawer
        visible={showImportDrawer}
        exportLoading={exportLoading}
        onOk={(file) => {
          setShowImportDrawer(false);
          onImport(file);
        }}
        onExport={() => {
          onExport();
        }}
        onClose={() => {
          setShowImportDrawer(false);
        }}
        onCancel={() => {
          setShowImportDrawer(false);
        }}
      />
    </Context>
  );
}
