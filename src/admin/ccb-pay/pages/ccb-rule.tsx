import { useRef, useMemo, useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Table, Dropdown, Menu, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
// import dayjs from 'dayjs';

import { getPaymentCcbAccountList, importPaymentAccount, exportPaymentCcbRule } from '@/apis';
import type { GetPaymentCcbAccountListRes } from '@/apis';
import { Context, Icon } from '@/components';
import { usePermission } from '@/hooks';
// import { checkCharge, checkPermission } from '@/utils/permission';
import { CcbRuleFilterDrawer, SplitRuleDetailDrawer, ImportExcelDrawer } from '../container/index';
import styles from './order-ledger.module.less';

// const clrgMtdcdObject: { [key: string]: string } = {
//   '1': '按金额分账',
//   '2': '按比例分账',
//   '3': '先固定金额再比例',
//   // 4: '其他',
// };

type DataType = GetPaymentCcbAccountListRes & {
  index: number;
};

export default function CcbRule() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  // const mallId = Number(searchParams.get('mallId') || 0);
  const tableRef = useRef<null | HTMLTableElement>(null);
  const contentRef = useRef<null | HTMLDivElement>(null);
  const [scrollHeight, setScrollHeight] = useState(500);
  const [ruleData, setRuleData] = useState<DataType[]>([]);
  const [filterData, setFilterData] = useState({
    keywords: '',
    type: 0,
  });

  const [params, setParams] = useState({
    pageNo: 1,
    pageSize: 10,
    total: 0,
    keywords: '',
    ruleMode: 0,
  });
  const paramsRef = useRef({ pageNo: 1, pageSize: 10, total: 0, keywords: '', ruleMode: 0 });

  const isFilterActive = useMemo(
    () => Boolean(Object.values(filterData).filter((val) => val).length),
    [filterData]
  );
  const [exportLoading, setExportLoading] = useState(false);
  const [showImportDrawer, setShowImportDrawer] = useState(false);
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [showSetCustomerDrawer, setShowSetCustomerDrawer] = useState(false);
  const [currentItem, setCurrentItem] = useState<DataType>({
    index: 0,
    id: 0,
    companyId: 0,
    mchId: 0,
    marketNo: '',
    ruleId: '',
    ruleName: '',
    ruleDes: '',
    subAccCyc: '',
    clrgDlayDys: '',
    clrgMode: '',
    clrgMtdcd: '',
    isTotal: false,
    efdt: '',
    expdt: '',
  });

  const quickOptions = [
    {
      label: '商家订单分账',
      value: `/ccb-pay/order/ledger?tenantId=${tenantId}`,
    },
    {
      label: '建行商家',
      value: `/ccb-pay/ccb/business?tenantId=${tenantId}`,
    },
    {
      label: '建行分账规则',
      value: `/ccb-pay/ccb/rule?tenantId=${tenantId}`,
    },
  ];
  const quickValue = `/ccb-pay/ccb/rule?tenantId=${tenantId}`;

  const menuItems = [{ label: '查看详情', key: 'detail' }];

  const columnOptions: ColumnsType<DataType> = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
    },
    {
      title: '分账规则编号',
      dataIndex: 'ruleId',
      align: 'center',
    },
    {
      title: '分账规则名称',
      dataIndex: 'ruleName',
      align: 'center',
    },
    {
      title: '分账模式',
      dataIndex: 'clrgMtdcdStr',
      align: 'center',
      // render: (_val, { clrgMtdcd }) => <div>{clrgMtdcd ? clrgMtdcdObject[clrgMtdcd] : '--'}</div>,
    },
    {
      title: '生效日期',
      dataIndex: 'efdt',
      align: 'center',
      // render: (_val, { effectiveTime }) => (
      //   <div>{effectiveTime ? dayjs(effectiveTime).format('YYYY-MM-DD HH:mm:ss') : '--'}</div>
      // ),
    },
    {
      title: '失效日期',
      dataIndex: 'expdt',
      align: 'center',
      // render: (_val, { invalidTime }) => (
      //   <div>{invalidTime ? dayjs(invalidTime).format('YYYY-MM-DD HH:mm:ss') : '--'}</div>
      // ),
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Dropdown
          overlay={
            <Menu
              items={menuItems}
              onClick={({ key, domEvent }) => {
                domEvent.stopPropagation();
                if (key === 'detail') {
                  setShowSetCustomerDrawer(true);
                  setCurrentItem(record);
                }
              }}
            />
          }
          placement="bottom"
        >
          <Icon
            name="zu13366"
            size={24}
            color="#999EB2"
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          />
        </Dropdown>
      ),
    },
  ];

  const getRuleList = () => {
    const { pageNo, pageSize, keywords, ruleMode } = paramsRef.current;
    const paramData = {
      ...{
        // mallId,
        tenantId,
        keywords,
        pageNo,
        pageSize,
      },
      ...(ruleMode ? { ruleMode } : {}),
    };
    getPaymentCcbAccountList(paramData).then((res) => {
      const data = res.list.map((item, index) => ({
        ...item,
        index: index + 1 + (pageNo - 1) * pageSize,
      }));
      const pageParams = {
        pageNo,
        pageSize,
        keywords,
        total: res.pagination.count || 0,
        ruleMode,
      };
      setParams({ ...pageParams });
      paramsRef.current = pageParams;
      setRuleData(data);
    });
  };

  const onFilter = usePermission('M_001_010_001_003', () => {
    setShowFilterDrawer(true);
  });

  const urlDownload = (url: string, fileName: string) => {
    if (!url) return;
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
  };

  const onExport = () => {
    setExportLoading(true);
    exportPaymentCcbRule({
      // mallId,
      tenantId,
    })
      .then((res) => {
        if (res.type !== 'application/json') {
          const URL = window.webkitURL || window.URL;
          const url = URL.createObjectURL(res);
          urlDownload(url, `建行规则模板.xls`);
        } else {
          message.error(`系统开小差，请稍后再试`);
        }
      })
      .finally(() => {
        setExportLoading(false);
      });
  };

  const onImport = usePermission('', (file: File | null) => {
    if (file) {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('tenantId', tenantId);
      // formData.append('mallId', String(mallId));
      importPaymentAccount(formData).then((res) => {
        if (!res.successNum) {
          message.error('导入失败');
          return;
        }
        paramsRef.current = { pageNo: 1, pageSize: 10, total: 0, keywords: '', ruleMode: 0 };
        getRuleList();
        if (!res.errorNum) {
          message.success('导入成功');
          return;
        }

        message.info(`导入成功${res.successNum}条，导入失败${res.errorNum}条`);
      });
    }
  });

  useEffect(() => {
    setTimeout(() => {
      if (contentRef.current?.offsetHeight) {
        setScrollHeight(contentRef.current.offsetHeight - 132);
        if (tableRef.current?.style) {
          const tableBody = document.querySelector(
            `.${styles.ccbPayContentBody} .ant-table-body`
          ) as HTMLDivElement;
          if (tableBody) {
            tableBody.style.height = `${contentRef.current.offsetHeight - 132}px`;
          }
        }
      }
    }, 60);
    getRuleList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Context
      className={styles.packageContent}
      // permission={{ code: 'M_001_005_001', newLogic: true }}
      head={
        <Context.Head
          title={[{ title: '商家分账' }]}
          quickFilter={
            <Context.QuickFilter
              label="显示"
              defaultValue={quickValue}
              dropdownMatchSelectWidth={120}
              options={quickOptions}
              onChange={navigate}
            />
          }
          isFilterActive={isFilterActive}
          onFilter={() => {
            onFilter();
          }}
        />
      }
    >
      <div className={styles.ccbPayContent}>
        <div className={styles.ccbPayContentHead}>
          <Button
            type="primary"
            size="small"
            onClick={() => {
              setShowImportDrawer(true);
            }}
          >
            导入
          </Button>
          {/* <div role="presentation" className={styles.downloadA} onClick={onExport}>
            下载模板
          </div> */}
        </div>
        <div className={styles.ccbPayContentBody} ref={contentRef}>
          <Table
            ref={tableRef}
            columns={columnOptions}
            dataSource={ruleData}
            scroll={{ y: scrollHeight }}
            pagination={{
              current: params.pageNo || 1,
              defaultCurrent: params.pageNo || 1,
              defaultPageSize: params.pageSize || 10,
              total: params.total || 1,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total: number) => `共 ${total} 条`,
              onChange: (pageNo, pageSize) => {
                const pageParams = { ...params, pageNo, pageSize };
                setParams({ ...pageParams });
                paramsRef.current = pageParams;
                getRuleList();
              },
            }}
          />
        </div>
      </div>
      <CcbRuleFilterDrawer
        visible={showFilterDrawer}
        formData={filterData}
        onOk={(val) => {
          setFilterData(val);
          paramsRef.current.pageNo = 1;
          paramsRef.current.keywords = val.keywords;
          paramsRef.current.ruleMode = val.type;
          paramsRef.current.pageNo = 1;
          getRuleList();
          setShowFilterDrawer(false);
        }}
        onClose={() => {
          setShowFilterDrawer(false);
        }}
      />
      <SplitRuleDetailDrawer
        visible={showSetCustomerDrawer}
        id={currentItem.id}
        tenantId={tenantId}
        // mallId={mallId}
        onClose={() => {
          setShowSetCustomerDrawer(false);
        }}
      />

      <ImportExcelDrawer
        visible={showImportDrawer}
        exportLoading={exportLoading}
        onOk={(file) => {
          setShowImportDrawer(false);
          onImport(file);
        }}
        onExport={() => {
          onExport();
        }}
        onClose={() => {
          setShowImportDrawer(false);
        }}
        onCancel={() => {
          setShowImportDrawer(false);
        }}
      />
    </Context>
  );
}
