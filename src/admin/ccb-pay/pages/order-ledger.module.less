.ccbPayContent {
  height: 100%;

  .ccbPayContentHead {
    display: flex;
    min-height: 40px;
    padding: 20px 20px 0;
    align-items: center;
    justify-content: flex-end;
  }

  .ccbPayContentBody {
    width: 100%;
    height: calc(100% - 60px);
    padding: 20px 20px 0;
    padding-top: 28px;

    // .titles {
    //   display: flex;
    //   align-items: center;
    //   flex-grow: 1;
    //   justify-content: space-between;
    //   background: #fafafc;
    //   border-radius: 8px;
    //   .titleItem {
    //     display: flex;
    //     justify-content: center;
    //     align-items: center;
    //     min-height: 38px;
    //   }
    // }

    :global {
      .ant-table-container table > thead > tr:first-child th:first-child {
        border-top-left-radius: 8px !important;
        border-bottom-left-radius: 8px !important;
      }

      .ant-table-container table > thead > tr:first-child th:last-child {
        border-top-right-radius: 8px !important;
        border-bottom-right-radius: 8px !important;
      }

      .ant-table-thead .ant-table-cell {
        background-color: #fafafc !important;
        padding: 10px !important;

        &::before {
          width: 0 !important;
        }
      }

      .ant-table-cell-row-hover {
        background-color: rgb(*********** / 30%) !important;
      }

      .ant-table-tbody > tr > td {
        border-bottom: 0;
      }
    }
  }

  .downloadA {
    color: #008cff;
    margin-left: 10px;
    cursor: pointer;
  }

  .ruleNum {
    color: #008cff;
    cursor: pointer;
  }

  .companyName {
    display: flex;
    align-items: center;

    .img {
      margin-left: 4px;
    }
  }
}
