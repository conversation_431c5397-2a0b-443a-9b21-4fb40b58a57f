.formCard {
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
  border-radius: 18px;
  background: white;
  padding: 0 20px;

  .formCardSelect {
    :global {
      .ant-select-selection-search {
        left: 0;
      }

      .ant-select-selector {
        border: none !important;
        padding: 0 !important;
        box-shadow: none !important;
      }

      .ant-select-focused {
        border: none !important;
      }

      .ant-select-selection-item {
        padding-right: 30px !important;
      }
    }
  }
}

.info {
  width: 100%;

  .infoItem {
    font-size: 14px;
    display: flex;
    min-height: 54px;

    .infoItemLabel {
      color: @text-colors[primary];
      display: flex;
      width: 70px;
      align-items: center;
    }

    .infoItemValue {
      color: @text-colors[secondary];
      display: flex;
      flex: 1;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

.customerItem {
  font-size: 14px;

  .customerItemInfo {
    display: flex;

    .customerItemInfoName {
      width: 166px;
      white-space: normal;
      word-break: break-all;
      // color: @text-colors[primary];
    }
  }

  .customerItemInfoPhone {
    width: 100px;
    margin: 4px 0;
  }

  &Text {
    width: 270px;
    white-space: normal;
    word-break: break-all;
  }
}

.cardBox {
  margin-bottom: 20px;
}

.cardTitle {
  color: #3d3d3d;
  font-size: 14px;
  font-weight: 500;
  margin-top: 12px;
  margin-bottom: 16px;
}

.formCardFooter {
  color: @text-colors[secondary];
  font-size: 16px;
  padding-bottom: 16px;
}

.orderSettingInfo {
  font-size: 14px;

  .orderSettingInfoItem {
    display: flex;
    min-height: 54px;
    border-bottom: 1px solid #f3f3f3;

    .orderSettingInfoItemLabel {
      color: @text-colors[primary];
      display: flex;
      flex: 1;
      align-items: center;
    }

    .orderSettingInfoItemValue {
      color: @text-colors[secondary];
      display: flex;
      width: 100px;
      justify-content: flex-end;
      align-items: center;
    }
  }

  .orderSettingInfoDesc {
    color: @text-colors[secondary];
    margin-top: 9px;
    padding-bottom: 16px;
  }
}
