import { useEffect, useRef, useState } from 'react';
import { useThrottleFn } from 'ahooks';
import { Spin, Table } from 'antd';
import type { DrawerProps } from 'antd';

import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import dayjs from 'dayjs';
import type { ColumnsType } from 'antd/es/table';
// import classNames from 'classnames';
// import { testPerm } from '@/utils/permission';
// import { getCustomCategoryList } from '@/apis';
// import type { GetCustomCategoryListResult } from '@/apis';
import styles from './set-customer-drawer.module.less';

interface PropsType extends DrawerProps {
  visible: boolean;
  id: number;
  onOk: MultipleParamsFn<[]>;
}

type DataType = {
  index: number;
  price: string;
  rate: number;
  accountType: number;
};

// const typeOptions = [
//   { value: 0, label: '全部' },
//   { value: 1, label: '普通商户' },
//   { value: 2, label: '工长 ' },
//   { value: 3, label: '设计师' },
//   { value: 4, label: '装修公司' },
// ];

const accountTypeObject: { [key: number]: string } = {
  1: '平台',
  2: '商户',
};

const typeObject: { [key: number]: string } = {
  1: '普通商户',
  2: '工长',
  3: '设计师',
  4: '装修公司',
};

type CustomerInfo = {
  companyName: string;
  shopName: string;
  type: number;
  time: number;
  relationId: number | null;
  splitAccount: {
    type: number;
    list: { index: number; price: string; rate: number; accountType: number }[];
  };
};

const baseCustomerInfo = {
  companyName: '万众城',
  shopName: '万众橙品',
  type: 1,
  time: *************,
  relationId: null,
  splitAccount: {
    type: 1,
    list: [{ index: 1, price: '88888', rate: 10, accountType: 1 }],
  },
};

function RuleDetailDrawer({ visible, onOk, ...props }: PropsType) {
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [loading, setLoading] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>(baseCustomerInfo);
  const [relationId, setRelationId] = useState<number>(0);

  const columnOptions: ColumnsType<DataType> = [
    {
      title: '序号',
      dataIndex: 'index',
      align: 'center',
    },
    {
      title: '固定金额',
      dataIndex: 'price',
      align: 'center',
    },
    {
      title: '固定比例',
      dataIndex: 'rate',
      align: 'center',
      render: (_val, { rate }) => <div>{rate}%</div>,
    },
    {
      title: '分账方',
      dataIndex: 'accountType',
      align: 'center',
      render: (_val, { accountType }) => (
        <div>{accountType ? accountTypeObject[accountType] : '--'}</div>
      ),
    },
  ];

  useEffect(() => {
    setCustomerInfo(baseCustomerInfo);
    setLoading(false);
  }, []);

  useEffect(() => {
    if (!visible) {
      setCustomerInfo({
        companyName: '',
        shopName: '',
        type: 1,
        time: 0,
        relationId: null,
        splitAccount: {
          type: 1,
          list: [{ index: 1, price: '88888', rate: 10, accountType: 1 }],
        },
      });
      setRelationId(0);
    }
  }, [visible]);

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      window.console.log('relationId', relationId);
      onOk();
    },
    { wait: 3000 }
  ).run;

  return (
    <Drawer
      ref={drawerRef}
      title="配置建行商家"
      visible={visible}
      footer={
        <Drawer.Footer
          showCancel={false}
          okButtonProps={{
            disabled: Boolean(!relationId),
          }}
          okText="保存"
          onOk={() => {
            onConfirm();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <Spin spinning={loading}>
        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>基本信息</div>
          <div className={styles.formCard}>
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>企业名称</div>
                <div className={styles.infoItemValue}>{customerInfo.companyName}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>店铺名称</div>
                <div className={styles.infoItemValue}>{customerInfo.shopName}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>入驻身份</div>
                <div className={styles.infoItemValue}>{typeObject[customerInfo.type]}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>入驻时间</div>
                <div className={styles.infoItemValue}>
                  {dayjs(customerInfo.time).format('YYYY-MM-DD')}
                </div>
              </div>
              <div className={styles.formCardFooter}>
                次分帐信息只可内部查看,只有二级合伙人 可以使用.
              </div>
            </div>
          </div>
        </div>

        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>汇总订单设置</div>
          <div className={styles.formCard}>
            <div className={styles.orderSettingInfo}>
              <div className={styles.orderSettingInfoItem}>
                <div className={styles.orderSettingInfoItemLabel}>汇总计算订单金额后分账</div>
                <div className={styles.orderSettingInfoItemValue}>是</div>
              </div>
              <div className={styles.orderSettingInfoDesc}>
                小额高频类交易市场建议选择是,否则可能造成厘级差额
              </div>
            </div>
          </div>
        </div>

        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>分账周期设置</div>
          <div className={styles.formCard}>
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>企业名称</div>
                <div className={styles.infoItemValue}>{customerInfo.companyName}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>店铺名称</div>
                <div className={styles.infoItemValue}>{customerInfo.shopName}</div>
              </div>
            </div>
          </div>
        </div>

        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>分账模式设置</div>
          <div className={styles.formCard}>
            <div className={styles.orderSettingInfo}>
              <div className={styles.orderSettingInfoItem}>
                <div className={styles.orderSettingInfoItemLabel}>分账模式</div>
                <div className={styles.orderSettingInfoItemValue}>按比例</div>
              </div>
            </div>
            <Table
              className={styles.splitAccountTable}
              columns={columnOptions}
              dataSource={customerInfo.splitAccount.list}
              pagination={false}
            />
          </div>
        </div>
      </Spin>
    </Drawer>
  );
}

RuleDetailDrawer.displayName = 'RuleDetailDrawer';

RuleDetailDrawer.defaultProps = {};

export default RuleDetailDrawer;
