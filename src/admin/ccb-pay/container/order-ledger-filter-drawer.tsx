import { useEffect, useRef, useState } from 'react';
import { useThrottleFn } from 'ahooks';
import { Form, Input, Select } from 'antd';
import type { DrawerProps } from 'antd';
import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
// import { testPerm } from '@/utils/permission';
import { getGmallMchIdentityList } from '@/apis';
// import type { GetCustomCategoryListResult } from '@/apis';
import styles from './order-ledger-filter-drawer.module.less';

export type OrderLedgerFormValueOption = {
  keywords: string;
  type: number;
};

interface PropsType extends DrawerProps {
  formData: OrderLedgerFormValueOption;
  tenantId: string;
  onOk: MultipleParamsFn<[values: OrderLedgerFormValueOption]>;
}

// const typeOptions = [
//   { value: 0, label: '全部' },
//   { value: 1, label: '企业商户' },
//   { value: 2, label: '工长 ' },
//   { value: 3, label: '设计公司' },
//   { value: 4, label: '装修公司' },
//   // { value: 5, label: '城市运营商' },
// ];

function OrderLedgerFilterDrawer({ visible, formData, tenantId, onOk, ...props }: PropsType) {
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [form] = Form.useForm<OrderLedgerFormValueOption>();
  const [typeOptions, setTypeOptions] = useState<{ value: number; label: string }[]>([]);
  useEffect(() => {
    form.setFieldsValue(formData);
  }, [formData, form]);

  const getTypeOptions = () => {
    getGmallMchIdentityList({ tenantId, type: 1 }).then((res) => {
      const types = res.list?.map((item) => ({ label: item.identityName, value: item.id }));
      types.unshift({ value: 0, label: '全部' });
      setTypeOptions(types);
    });
  };

  // 重置筛选条件
  const onReset = () => {
    form.setFieldsValue({
      keywords: '',
      type: 0,
    });
  };

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      onOk(form.getFieldsValue());
    },
    { wait: 3000 }
  ).run;

  useEffect(() => {
    if (visible) {
      getTypeOptions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  return (
    <Drawer
      ref={drawerRef}
      title="筛选"
      visible={visible}
      footer={
        <Drawer.Footer
          cancelText="重置"
          onOk={() => {
            onConfirm();
          }}
          onCancel={() => {
            onReset();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <div className={styles.formCard}>
        <Form form={form} layout="vertical">
          <Form.Item name="keywords" label="企业名称/店铺名称/建行商家编号">
            <Input placeholder="请输入企业名称/店铺名称/建行商家编号" maxLength={50} />
          </Form.Item>
          {/* <Form.Item name="shopName" label="店铺名称">
            <Input placeholder="请输入店铺名称" maxLength={50} />
          </Form.Item>
          <Form.Item name="customerCode" label="商家号">
            <Input placeholder="请输入商家号" maxLength={50} />
          </Form.Item> */}
          <Form.Item name="type" label="入驻身份">
            <Select
              className={styles.formCardSelect}
              options={typeOptions}
              placeholder="请选择入驻身份"
            />
          </Form.Item>
        </Form>
      </div>
    </Drawer>
  );
}

OrderLedgerFilterDrawer.displayName = 'OrderLedgerFilterDrawer';

OrderLedgerFilterDrawer.defaultProps = {};

export default OrderLedgerFilterDrawer;
