import { useCallback, useEffect, useRef, useState } from 'react';
import { useThrottleFn } from 'ahooks';
import { Form, Select, Spin, message } from 'antd';
import type { DrawerProps } from 'antd';

import { Drawer, Icon } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
// import { testPerm } from '@/utils/permission';
import { getPaymentCcbCustomerList, updatePaymentCcbBindCustomer } from '@/apis';
import type { GetPaymentCcbCustomerListRes } from '@/apis';
import type { DataType } from '../pages/order-ledger';
import styles from './set-customer-drawer.module.less';

interface PropsType extends DrawerProps {
  visible: boolean;
  info: DataType;
  tenantId: string;
  // mallId: number;
  subId: number;
  onSuccess: MultipleParamsFn<[]>;
}

const typeObject: { [key: number]: string } = {
  1: '普通商户',
  2: '工长',
  3: '设计师',
  4: '装修公司',
};

function SetCustomerDrawer({
  visible,
  info,
  tenantId,
  // mallId,
  subId,
  onSuccess,
  ...props
}: PropsType) {
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [loading, setLoading] = useState(false);
  const [customerList, setCustomerList] = useState<GetPaymentCcbCustomerListRes[]>([]);
  const [relationId, setRelationId] = useState<string>('');

  const getCustomerList = useCallback(() => {
    setLoading(true);
    getPaymentCcbCustomerList({
      pageNo: 1,
      pageSize: 9999999,
      keywords: '',
      // mallId,
      tenantId,
    })
      .then((res) => {
        setCustomerList(res.list || []);
      })
      .finally(() => {
        setLoading(false);
      });
  }, [tenantId]);

  useEffect(() => {
    if (!visible) {
      setRelationId('');
    } else {
      setRelationId(subId ? String(subId) : '');
      getCustomerList();
    }
  }, [visible, getCustomerList, subId]);

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      // const ids = relationId.split('-');
      // const subMchId = Number(ids[0]);
      updatePaymentCcbBindCustomer({
        // mallId,
        tenantId,
        subMchId: Number(relationId || 0),
        subCompanyId: info.enterCompanyId,
        ruleId: info.ruleId,
      }).then(() => {
        message.success('保存成功');
        onSuccess();
      });
    },
    { wait: 3000 }
  ).run;

  return (
    <Drawer
      ref={drawerRef}
      title={`${info.subMchId ? '修改' : '配置'}建行商家`}
      visible={visible}
      footer={
        <Drawer.Footer
          showCancel={false}
          okButtonProps={{
            disabled: Boolean(!relationId),
          }}
          okText="保存"
          onOk={() => {
            onConfirm();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <Spin spinning={loading}>
        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>基本信息</div>
          <div className={styles.formCard}>
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>企业名称</div>
                <div className={styles.infoItemValue}>{info?.enterCompanyName}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>店铺名称</div>
                <div className={styles.infoItemValue}>{info?.shopName}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>入驻身份</div>
                <div className={styles.infoItemValue}>{typeObject[info?.type || 0]}</div>
              </div>
              {/* <div className={styles.formCardFooter}>
                次分帐信息只可内部查看,只有二级合伙人 可以使用.
              </div> */}
            </div>
          </div>
        </div>

        <div className={styles.cardBox}>
          <div className={styles.formCard}>
            <Form layout="vertical">
              <Form.Item
                name="customerId"
                label="建行商家"
                style={{ paddingTop: 16, paddingBottom: 16 }}
              >
                <Select
                  className={styles.formCardSelect}
                  defaultValue={relationId || ''}
                  value={relationId}
                  showSearch
                  filterOption={(input, option) => {
                    const title =
                      option?.props.children.props.children[0].props.children[0].props.children.toLowerCase();
                    return title.indexOf(input.toLowerCase()) >= 0;
                  }}
                  placeholder="选择商家"
                  onChange={(val) => {
                    setRelationId(val);
                  }}
                  optionLabelProp="label"
                >
                  {customerList.map((customerItem) => (
                    <Select.Option
                      value={String(customerItem.id)}
                      key={customerItem.id}
                      label={customerItem.mktMrchNm}
                    >
                      <div className={styles.customerItem}>
                        <div className={styles.customerItemInfo}>
                          <div className={styles.customerItemInfoName}>
                            {customerItem.mktMrchNm}
                          </div>
                          <div className={styles.customerItemInfoPhone}>
                            <Icon name="telephone" />
                            {customerItem.mblphNo}
                          </div>
                        </div>
                        {customerItem.mrchCrdtTp && customerItem.mrchCrdtNo ? (
                          <div className={styles.customerItemText}>
                            {customerItem.mrchCrdtTp}:{customerItem.mrchCrdtNo}
                          </div>
                        ) : null}
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
          </div>
        </div>

        {/* <div className={styles.cardBox}>
          <div className={styles.cardTitle}>汇总订单设置</div>
          <div className={styles.formCard}>
            <div className={styles.orderSettingInfo}>
              <div className={styles.orderSettingInfoItem}>
                <div className={styles.orderSettingInfoItemLabel}>汇总计算订单金额后分账</div>
                <div className={styles.orderSettingInfoItemValue}>是</div>
              </div>
              <div className={styles.orderSettingInfoDesc}>
                小额高频类交易市场建议选择是,否则可能造成厘级差额
              </div>
            </div>
          </div>
        </div> */}

        {/* <div className={styles.cardBox}>
          <div className={styles.cardTitle}>分账周期设置</div>
          <div className={styles.formCard}>
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>企业名称</div>
                <div className={styles.infoItemValue}>{customerInfo.companyName}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>店铺名称</div>
                <div className={styles.infoItemValue}>{customerInfo.shopName}</div>
              </div>
            </div>
          </div>
        </div> */}
      </Spin>
    </Drawer>
  );
}

SetCustomerDrawer.displayName = 'SetCustomerDrawer';

SetCustomerDrawer.defaultProps = {};

export default SetCustomerDrawer;
