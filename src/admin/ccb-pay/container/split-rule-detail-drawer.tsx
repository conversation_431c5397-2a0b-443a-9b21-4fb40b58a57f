import { useEffect, useRef, useState, useCallback, ReactNode } from 'react';
import { Spin, Table } from 'antd';
import type { DrawerProps } from 'antd';

import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import type { ColumnsType } from 'antd/es/table';
import { getPaymentCcbAccountDetail } from '@/apis';
import type { GetPaymentCcbAccountDetailRes } from '@/apis';
import styles from './split-rule-detail-drawer.module.less';

interface PropsType extends DrawerProps {
  visible: boolean;
  id: number;
  tenantId: string;
  // mallId: number;
}

type DataType = {
  id: number;
  accountingId: number;
  ruleId: string;
  seqNo: number;
  clrgMtdcd: number;
  clrgPctg: string;
  amt: number;
  memberType: number;
};

const subAccCycObject: { [key: number]: string } = { 1: '日', 2: '月', 3: '月末', 4: '强制分账' };

const memberTypeObject: { [key: string]: string } = {
  1: '平台',
  2: '商户',
  3: '运营方',
};

// const accountTypeObject: { [key: number]: string } = {
//   1: '平台',
//   2: '商户',
// };

// const clrgMtdcdObject: { [key: number]: string } = {
//   1: '翻盘式',
//   2: '滚动式',
//   3: '时间自定义式',
// };

function SplitRuleDetailDrawer({ visible, tenantId, id, ...props }: PropsType) {
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [loading, setLoading] = useState(false);
  const [customerInfo, setCustomerInfo] = useState<GetPaymentCcbAccountDetailRes>();
  const baseColumnOptions: ColumnsType<DataType> = [
    {
      title: '序号',
      dataIndex: 'seqNo',
      align: 'center',
    },
    {
      title: '固定金额',
      dataIndex: 'amt',
      align: 'center',
      className: '',
    },
    {
      title: '固定比例%',
      dataIndex: 'clrgPctg',
      align: 'center',
      className: '',
      // render: (_val, { rate }) => <div>{rate}%</div>,
    },
    {
      title: '分账方',
      dataIndex: 'memberType',
      align: 'center',
      render: (_val, { memberType }) => (
        // <Select
        //   value={memberType}
        //   placeholder="请选择分账方"
        //   onChange={(val) => {}}
        //   options={accountTypeOptions}
        //   style={{ width: 72 }}
        // />
        <div>{memberTypeObject[memberType]}</div>
      ),
    },
  ];
  const [columnOptions, setColumnOptions] = useState<ColumnsType<DataType>>(baseColumnOptions);

  const getRuleDetail = useCallback(() => {
    setLoading(true);
    getPaymentCcbAccountDetail({ tenantId, id })
      .then((res) => {
        setCustomerInfo({ ...res });
        const options = [...baseColumnOptions];
        if (res.clrgMtdcd === 1) {
          options[1].className = styles.hideCol;
          options[2].className = '';
        } else if (res.clrgMtdcd === 2) {
          options[1].className = '';
          options[2].className = styles.hideCol;
        }
        setColumnOptions([...options]);
      })
      .finally(() => {
        setLoading(false);
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tenantId, id]);

  useEffect(() => {
    if (visible) {
      getRuleDetail();
    } else {
      setCustomerInfo({} as GetPaymentCcbAccountDetailRes);
      setColumnOptions([...baseColumnOptions]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getRuleDetail, visible]);

  const createSplitCycleElement = () => {
    let element: string | ReactNode = '';
    if (customerInfo?.subAccCyc === 1 || customerInfo?.subAccCyc === 4) {
      element = `${customerInfo?.clrgDlayDys}天`;
    } else {
      element = `每月${customerInfo?.clrgDlayDys}日`;
    }
    return (
      <>
        {customerInfo?.subAccCyc ? subAccCycObject[customerInfo.subAccCyc] : ''}，{element}
      </>
    );
  };

  return (
    <Drawer
      ref={drawerRef}
      title="分账规则详情"
      visible={visible}
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <Spin spinning={loading}>
        {/* <div className={styles.cardBox}>
          <div className={styles.cardTitle}>平台订单</div>
          <div className={styles.formCard}>
            <Form layout="vertical">
              <Form.Item
                name="customerId"
                label="选择分账规则"
                style={{ paddingTop: 16, paddingBottom: 16 }}
              >
                <div>{customerInfo?.ruleName}</div>
              </Form.Item>
            </Form>
          </div>
        </div> */}

        <div className={styles.cardBox}>
          <div className={styles.formCard}>
            <div className={styles.ruleHead}>
              <div className={styles.ruleHeadName}>
                {customerInfo?.ruleName}
                {customerInfo?.clrgMtdcdStr && (
                  <span className={styles.ruleHeadTag}>{customerInfo?.clrgMtdcdStr}</span>
                )}
              </div>
              <div className={styles.ruleHeadCode}>{customerInfo?.ruleId}</div>
            </div>
            <div className={styles.ruleInfo}>
              <div className={styles.ruleInfoItem}>
                <div className={styles.ruleInfoItemLabel}>生效日期</div>
                <div className={styles.ruleInfoItemValue}>{customerInfo?.efdt || '--'}</div>
              </div>
              <div className={styles.ruleInfoItem}>
                <div className={styles.ruleInfoItemLabel}>失效日期</div>
                <div className={styles.ruleInfoItemValue}>{customerInfo?.expdt || '--'}</div>
              </div>
              <div className={styles.ruleDesc}>
                <div className={styles.ruleDescLabel}>分账描述</div>
                <div className={styles.ruleDescText}>{customerInfo?.ruleDes || '--'}</div>
              </div>
              {customerInfo?.detailList && (
                <Table
                  className={styles.splitAccountTable}
                  columns={columnOptions}
                  dataSource={customerInfo?.detailList || []}
                  pagination={false}
                />
              )}
            </div>
          </div>
        </div>

        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>分账周期设置</div>
          <div className={styles.formCard}>
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>分账周期模式</div>
                <div className={styles.infoItemValue}>{customerInfo?.clrgModeStr || '--'}</div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>分账周期</div>
                <div className={styles.infoItemValue}>
                  {/* {customerInfo?.subAccCyc ? subAccCycObject[customerInfo.subAccCyc] : ''} */}
                  {customerInfo?.subAccCyc === 3
                    ? subAccCycObject[customerInfo.subAccCyc]
                    : createSplitCycleElement()}
                </div>
              </div>
              <div className={styles.infoItem}>
                <div className={styles.infoItemLabel}>汇总计算订单金额后分账</div>
                <div className={styles.infoItemValue}>
                  {customerInfo?.isTotal === 1 ? '是' : '否'}
                </div>
              </div>
            </div>
          </div>
        </div>
      </Spin>
    </Drawer>
  );
}

SplitRuleDetailDrawer.displayName = 'SplitRuleDetailDrawer';

SplitRuleDetailDrawer.defaultProps = {};

export default SplitRuleDetailDrawer;
