import { useEffect, useRef, useState, ReactNode } from 'react';
import { useThrottleFn } from 'ahooks';
import { Form, Select, Spin, Table, message } from 'antd';
import type { DrawerProps } from 'antd';

import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import type { ColumnsType } from 'antd/es/table';
// import classNames from 'classnames';
// import { testPerm } from '@/utils/permission';
import {
  getPaymentCcbAccountList,
  getPaymentCcbAccountDetail,
  updatePaymentCcbBindRate,
} from '@/apis';
import type { GetPaymentCcbAccountListRes, GetPaymentCcbAccountDetailRes } from '@/apis';
import type { DataType } from '../pages/order-ledger';
import styles from './set-rule-drawer.module.less';

interface PropsType extends DrawerProps {
  visible: boolean;
  info: DataType;
  tenantId: string;
  // mallId: number;
  onSuccess: MultipleParamsFn<[]>;
}

// const accountTypeOptions = [
//   { label: '平台', value: 0 },
//   { label: '商户', value: 1 },
//   { label: '运营方', value: 2 },
// ];

// const typeObject: { [key: string]: string } = {
//   1: '翻盘式,',
//   2: '滚动式',
//   3: '时间自定义式',
// };

const memberTypeObject: { [key: string]: string } = {
  1: '平台',
  2: '商户',
  3: '运营方',
};

function SetRuleDrawer({ visible, info, tenantId, onSuccess, ...props }: PropsType) {
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [loading, setLoading] = useState(false);
  const [ruleList, setRuleList] = useState<GetPaymentCcbAccountListRes[]>([]);
  const [ruleId, setRuleId] = useState<string>('');
  const [ruleDetail, setRuleDetail] = useState({} as GetPaymentCcbAccountDetailRes);
  const [form] = Form.useForm();
  const baseColumnOptions: ColumnsType<GetPaymentCcbAccountDetailRes['detailList'][0]> = [
    {
      title: '序号',
      dataIndex: 'seqNo',
      align: 'center',
    },
    {
      title: '固定金额',
      dataIndex: 'amt',
      align: 'center',
      className: '',
    },
    {
      title: '固定比例%',
      dataIndex: 'clrgPctg',
      align: 'center',
      className: '',
      render: (_val, { clrgPctg }) => <div>{clrgPctg}</div>,
    },
    {
      title: '分账方',
      dataIndex: 'memberType',
      align: 'center',
      render: (_val, { memberType }) => (
        // <Select
        //   value={memberType}
        //   placeholder="请选择分账方"
        //   onChange={(val) => {}}
        //   options={accountTypeOptions}
        //   style={{ width: 72 }}
        // />
        <div>{memberTypeObject[memberType]}</div>
      ),
    },
  ];

  const [columnOptions, setColumnOptions] =
    useState<ColumnsType<GetPaymentCcbAccountDetailRes['detailList'][0]>>(baseColumnOptions);

  const subAccCycObject: { [key: number]: string } = { 1: '日', 2: '月', 3: '月末', 4: '强制分账' };

  const getRuleList = () => {
    setLoading(true);
    getPaymentCcbAccountList({
      pageNo: 1,
      pageSize: 100000,
      keywords: '',
      // mallId,
      tenantId,
    })
      .then((res) => {
        setRuleList(res.list?.length ? res.list : []);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const getRuleDetail = (id: number) => {
    if (!id) {
      message.error('请选择规则');
      return;
    }
    getPaymentCcbAccountDetail({
      // mallId,
      tenantId,
      id,
    }).then((res) => {
      // if (res.detailList?.length) {
      //   res.detailList = [res.detailList[0]];
      // }
      const options = [...baseColumnOptions];
      if (res.clrgMtdcd === 1) {
        options[1].className = styles.hideCol;
        options[2].className = '';
      } else if (res.clrgMtdcd === 2) {
        options[1].className = '';
        options[2].className = styles.hideCol;
      }
      setColumnOptions([...options]);
      setRuleDetail(res);
    });
  };

  useEffect(() => {
    if (!visible) {
      setRuleId('');
      form.setFieldsValue({ ruleId: null });
      setColumnOptions([...baseColumnOptions]);
    } else {
      getRuleList();
      if (info.ruleId) {
        form.setFieldsValue({ ruleId: String(info.ruleId) });
        setRuleId(String(info.ruleId));
        getRuleDetail(info.ruleId);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      if (!ruleId) {
        message.error('请选择规则');
        return;
      }
      updatePaymentCcbBindRate({
        // mallId,
        tenantId,
        subMchId: info.subMchId,
        subCompanyId: info.enterCompanyId,
        ruleId: Number(ruleId),
        // ruleId: Number(ruleDetail.ruleId),
      }).then(() => {
        message.success('保存成功');
        onSuccess();
      });
    },
    { wait: 3000 }
  ).run;

  const createSplitCycleElement = () => {
    let element: string | ReactNode = '';
    if (ruleDetail?.subAccCyc === 1 || ruleDetail?.subAccCyc === 4) {
      element = `${ruleDetail?.clrgDlayDys}天`;
    } else {
      element = `每月${ruleDetail?.clrgDlayDys}日`;
    }
    return (
      <>
        {ruleDetail?.subAccCyc ? subAccCycObject[ruleDetail.subAccCyc] : ''}，{element}
      </>
    );
  };

  return (
    <Drawer
      ref={drawerRef}
      title={`${info.quantity ? '修改' : '配置'}分账规则`}
      visible={visible}
      footer={
        <Drawer.Footer
          showCancel={false}
          okButtonProps={{
            disabled: Boolean(!ruleId),
          }}
          okText="保存"
          onOk={() => {
            onConfirm();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <Spin spinning={loading}>
        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>平台订单</div>
          <div className={styles.formCard}>
            <Form layout="vertical" form={form}>
              <Form.Item
                name="ruleId"
                label="选择分账规则"
                style={{ paddingTop: 16, paddingBottom: 16 }}
              >
                <Select
                  className={styles.formCardSelect}
                  showSearch
                  placeholder="请选择分账规则"
                  onChange={(val) => {
                    setRuleId(val);
                    form.setFieldsValue({ ruleId: val });
                    getRuleDetail(Number(val));
                  }}
                  filterOption={(input, option) => {
                    const name = option?.props.children[0].props.children.toLowerCase();
                    const code = option?.props.children[1].props.children[1].toLowerCase();
                    return (
                      name.indexOf(input.toLowerCase()) >= 0 ||
                      code.indexOf(input.toLowerCase()) >= 0
                    );
                  }}
                >
                  {ruleList.map((customerItem) => (
                    <Select.Option value={String(customerItem.id)} key={customerItem.id}>
                      <div className={styles.customerItem}>{customerItem.ruleName}</div>
                      <div className={styles.customerItem}>编号:{customerItem.ruleId}</div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Form>
          </div>
        </div>

        {ruleId ? (
          <>
            <div className={styles.cardBox}>
              <div className={styles.formCard}>
                <div className={styles.ruleHead}>
                  <div className={styles.ruleHeadName}>
                    {ruleDetail.ruleName}
                    {ruleDetail.clrgMtdcdStr && (
                      <span className={styles.ruleHeadTag}>{ruleDetail.clrgMtdcdStr}</span>
                    )}
                  </div>
                  <div className={styles.ruleHeadCode}>{ruleDetail.ruleId}</div>
                </div>
                <div className={styles.ruleInfo}>
                  <div className={styles.ruleInfoItem}>
                    <div className={styles.ruleInfoItemLabel}>生效日期</div>
                    <div className={styles.ruleInfoItemValue}>{ruleDetail.efdt}</div>
                  </div>
                  <div className={styles.ruleInfoItem}>
                    <div className={styles.ruleInfoItemLabel}>失效日期</div>
                    <div className={styles.ruleInfoItemValue}>{ruleDetail.expdt}</div>
                  </div>
                  <div className={styles.ruleDesc}>
                    <div className={styles.ruleDescLabel}>分账描述</div>
                    <div className={styles.ruleDescText}>{ruleDetail.ruleDes}</div>
                  </div>
                  <Table
                    className={styles.splitAccountTable}
                    columns={columnOptions}
                    dataSource={ruleDetail.detailList}
                    pagination={false}
                  />
                </div>
              </div>
            </div>
            <div className={styles.cardBox}>
              <div className={styles.cardTitle}>分账周期设置</div>
              <div className={styles.formCard}>
                <div className={styles.info}>
                  <div className={styles.infoItem}>
                    <div className={styles.infoItemLabel}>分账周期模式</div>
                    <div className={styles.infoItemValue}>{ruleDetail?.clrgModeStr || '--'}</div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoItemLabel}>分账周期</div>

                    <div className={styles.infoItemValue}>
                      {ruleDetail?.subAccCyc === 3
                        ? subAccCycObject[ruleDetail.subAccCyc]
                        : createSplitCycleElement()}
                    </div>
                  </div>
                  <div className={styles.infoItem}>
                    <div className={styles.infoItemLabel}>汇总计算订单金额后分账</div>
                    <div className={styles.infoItemValue}>
                      {ruleDetail?.isTotal === 1 ? '是' : '否'}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : null}
      </Spin>
    </Drawer>
  );
}

SetRuleDrawer.displayName = 'SetRuleDrawer';

SetRuleDrawer.defaultProps = {};

export default SetRuleDrawer;
