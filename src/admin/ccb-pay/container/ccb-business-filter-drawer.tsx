import { useEffect, useRef } from 'react';
import { useThrottleFn } from 'ahooks';
import { Form, Input } from 'antd';
import type { DrawerProps } from 'antd';
import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import styles from './order-ledger-filter-drawer.module.less';

export type CcbBusinessFormValueOption = {
  keywords: string;
};

interface PropsType extends DrawerProps {
  formData: CcbBusinessFormValueOption;
  onOk: MultipleParamsFn<[values: CcbBusinessFormValueOption]>;
}

function CcbBusinessFilterDrawer({ visible, formData, onOk, ...props }: PropsType) {
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [form] = Form.useForm<CcbBusinessFormValueOption>();

  useEffect(() => {
    form.setFieldsValue(formData);
  }, [formData, form]);

  // useEffect(() => {
  //   if (!visible) {
  //     form.setFieldsValue({
  //       keywords: '',
  //     });
  //   }
  // }, [visible, form]);

  // 重置筛选条件
  const onReset = () => {
    form.setFieldsValue({
      keywords: '',
    });
  };

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      onOk(form.getFieldsValue());
    },
    { wait: 3000 }
  ).run;

  return (
    <Drawer
      ref={drawerRef}
      title="筛选"
      visible={visible}
      footer={
        <Drawer.Footer
          cancelText="重置"
          onOk={() => {
            onConfirm();
          }}
          onCancel={() => {
            onReset();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <div className={styles.formCard}>
        <Form form={form} layout="vertical">
          <Form.Item name="keywords" label="建行商家编号/名称/商家证件号码/联系人手机号">
            <Input placeholder="建行商家编号/名称/商家证件号码/联系人手机号" maxLength={50} />
          </Form.Item>
          {/* <Form.Item name="mrchCrdtNo" label="证件编号">
            <Input placeholder="分账规则名称" maxLength={50} />
          </Form.Item>
          <Form.Item name="mblphNo" label="手机号">
            <Input placeholder="手机号" maxLength={11} />
          </Form.Item> */}
        </Form>
      </div>
    </Drawer>
  );
}

CcbBusinessFilterDrawer.displayName = 'CcbRuleFilterDrawer';

CcbBusinessFilterDrawer.defaultProps = {};

export default CcbBusinessFilterDrawer;
