import { useState, useRef, useEffect } from 'react';
import { useThrottleFn } from 'ahooks';
import { Spin } from 'antd';
import type { DrawerProps } from 'antd';
import { Drawer, Icon, FileList } from '@/components';
// import { testPerm } from '@/utils/permission';
// import { getCustomCategoryList } from '@/apis';
// import type { GetCustomCategoryListResult } from '@/apis';
import SelectFile from '../components/select-file';
import type { SelectFileHandle } from '../components/select-file';
import styles from './import-excel-drawer.module.less';

interface PropsType extends DrawerProps {
  exportLoading: boolean;
  onExport: MultipleParamsFn<[]>;
  onOk: MultipleParamsFn<[file: File | null]>;
  onCancel: MultipleParamsFn<[]>;
}

function ImportExcelDrawer({
  visible,
  exportLoading,
  onExport,
  onCancel,
  onOk,
  ...props
}: PropsType) {
  const selectFileRef = useRef<SelectFileHandle>(null);
  const [file, setFile] = useState<File | null>(null);

  useEffect(() => {
    if (!visible) {
      setFile(null);
      // @ts-ignore
      // selectFileRef.current?.fileInputRef.current?.value = '';
    }
  }, [visible]);

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      onOk(file);
    },
    { wait: 3000 }
  ).run;

  return (
    <Drawer
      title="导入"
      visible={visible}
      footer={
        <Drawer.Footer
          cancelText="取消"
          onOk={() => {
            onConfirm();
          }}
          onCancel={() => {
            onCancel();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      {file ? (
        <FileList
          listType="list"
          onChange={(values) => {
            if (!values.length) {
              setFile(null);
            }
          }}
          // download
          fileList={[
            {
              url: '',
              size: file?.size || 0,
              name: file?.name || '',
              type: file?.type,
              status: 'done',
              uid: `1`,
            },
          ]}
        />
      ) : (
        <div className={styles.uploadBox}>
          <div
            className={styles.uploadBtn}
            role="presentation"
            onClick={() => {
              selectFileRef.current?.fileInputClick();
            }}
          >
            <Icon name="plus" size={30} />
            上传文件
          </div>
          <div className={styles.uploadDesc}>支持扩展名为xls、xlsx,一次一个文件</div>

          <Spin tip="导出中..." spinning={exportLoading}>
            <div
              className={styles.downloadA}
              role="presentation"
              onClick={() => {
                onExport();
              }}
            >
              下载模板
            </div>
          </Spin>
        </div>
      )}

      <SelectFile
        ref={selectFileRef}
        onSuccess={(val) => {
          setFile(val);
        }}
      />
    </Drawer>
  );
}

ImportExcelDrawer.displayName = 'ImportExcelDrawer';

ImportExcelDrawer.defaultProps = {};

export default ImportExcelDrawer;
