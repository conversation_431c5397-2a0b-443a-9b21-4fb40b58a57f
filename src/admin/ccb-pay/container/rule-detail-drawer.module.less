.formCard {
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
  border-radius: 18px;
  background: white;
  padding: 0px 20px;
  .formCardSelect {
    :global {
      .ant-select-selection-search {
        left: 0;
      }
      .ant-select-selector {
        border: none !important;
        padding: 0 !important;
      }
      .ant-select-focused {
        border: none !important;
      }
    }
  }
}

.info {
  width: 100%;
  .infoItem {
    min-height: 54px;
    display: flex;
    font-size: 14px;
    .infoItemLabel {
      width: 70px;
      display: flex;
      align-items: center;
      color: @text-colors[primary];
    }
    .infoItemValue {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      color: @text-colors[secondary];
    }
  }
}

.customerItem {
  font-size: 14px;
  .customerItemInfo {
    display: flex;
    .customerItemInfoName {
      flex: 1;
      width: 100%;
      // color: @text-colors[primary];
    }
  }
  .customerItemInfoPhone {
    width: 100px;
    margin: 4px 0;
  }
}

.cardBox {
  margin-bottom: 20px;
}

.cardTitle {
  font-size: 14px;
  color: #3d3d3d;
  margin-bottom: 16px;
  margin-top: 12px;
  font-weight: 500;
}

.formCardFooter {
  font-size: 16px;
  color: @text-colors[secondary];
  padding-bottom: 16px;
}

.orderSettingInfo {
  font-size: 14px;
  .orderSettingInfoItem {
    display: flex;
    border-bottom: 1px solid #f3f3f3;
    min-height: 54px;
    .orderSettingInfoItemLabel {
      flex: 1;
      display: flex;
      align-items: center;
      color: @text-colors[primary];
    }
    .orderSettingInfoItemValue {
      width: 100px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      color: @text-colors[secondary];
    }
  }
  .orderSettingInfoDesc {
    color: @text-colors[secondary];
    margin-top: 9px;
    padding-bottom: 16px;
  }
}

.splitAccountTable {
  :global {
    .ant-table-cell {
      padding: 10px;
    }
  }
}
