import { useEffect, useRef } from 'react';
import { useThrottleFn } from 'ahooks';
import { Form, Input, Select } from 'antd';
import type { DrawerProps } from 'antd';
import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
// import { testPerm } from '@/utils/permission';
// import { getCustomCategoryList } from '@/apis';
// import type { GetCustomCategoryListResult } from '@/apis';
import styles from './order-ledger-filter-drawer.module.less';

export type CcbRuleFormValueOption = {
  keywords: string;
  type: number;
};

interface PropsType extends DrawerProps {
  formData: CcbRuleFormValueOption;
  onOk: MultipleParamsFn<[values: CcbRuleFormValueOption]>;
}

const typeOptions = [
  { value: 0, label: '全部' },
  { value: 2, label: '按金额分账 ' },
  { value: 1, label: '按比例分账' },
  { value: 3, label: '先扣减固定金额再按比例' },
  { value: 4, label: '自定义' },
];

function CcbRuleFilterDrawer({ visible, formData, onOk, ...props }: PropsType) {
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [form] = Form.useForm<CcbRuleFormValueOption>();
  useEffect(() => {
    form.setFieldsValue(formData);
  }, [formData, form]);

  // 重置筛选条件
  const onReset = () => {
    form.setFieldsValue({
      keywords: '',
      type: 0,
    });
  };

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      onOk(form.getFieldsValue());
    },
    { wait: 3000 }
  ).run;

  return (
    <Drawer
      ref={drawerRef}
      title="筛选"
      visible={visible}
      footer={
        <Drawer.Footer
          cancelText="重置"
          onOk={() => {
            onConfirm();
          }}
          onCancel={() => {
            onReset();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <div className={styles.formCard}>
        <Form form={form} layout="vertical">
          <Form.Item name="keywords" label="分账规则编号/名称">
            <Input placeholder="分账规则编号/名称" maxLength={100} />
          </Form.Item>
          {/* <Form.Item name="ruleName" label="分账规则名称">
            <Input placeholder="分账规则名称" maxLength={50} />
          </Form.Item> */}
          <Form.Item name="type" label="分账模式">
            <Select
              className={styles.formCardSelect}
              options={typeOptions}
              placeholder="请选择分账模式"
            />
          </Form.Item>
        </Form>
      </div>
    </Drawer>
  );
}

CcbRuleFilterDrawer.displayName = 'CcbRuleFilterDrawer';

CcbRuleFilterDrawer.defaultProps = {};

export default CcbRuleFilterDrawer;
