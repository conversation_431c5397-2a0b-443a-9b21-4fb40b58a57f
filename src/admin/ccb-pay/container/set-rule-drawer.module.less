.formCard {
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
  border-radius: 18px;
  background: white;
  padding: 0 20px;

  .formCardSelect {
    :global {
      .ant-select-selection-search {
        left: 0;
      }

      .ant-select-selector {
        border: none !important;
        padding: 0 !important;
        box-shadow: none !important;
      }

      .ant-select-focused {
        border: none !important;
      }

      .ant-select-selection-item {
        padding-right: 30px !important;
      }
    }
  }
}

.ruleHead {
  min-height: 82px;

  .ruleHeadName {
    color: @text-colors[primary];
    font-size: 16px;
    margin-bottom: 8px;
    padding-top: 16px;
    word-break: break-all;
  }

  .ruleHeadCode {
    color: @text-colors[secondary];
    border-bottom: 1px #f3f3f3 solid;
    padding-bottom: 16px;
  }

  .ruleHeadTag {
    color: #008cff;
    font-size: 12px;
    background: #d9eeff;
    margin-left: 8px;
    padding: 4px;
    border-radius: 2px;
  }
}

.ruleInfo {
  .ruleInfoItem {
    display: flex;
    height: 54px;

    .ruleInfoItemLabel {
      display: flex;
      width: 70px;
      align-items: center;
    }

    .ruleInfoItemValue {
      color: @text-colors[secondary];
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }

  .ruleDesc {
    .ruleDescLabel {
      color: @text-colors[primary];
    }

    .ruleDescText {
      color: @text-colors[secondary];
      margin-top: 16px;
      margin-bottom: 16px;
    }
  }
}

.info {
  width: 100%;

  .infoItem {
    font-size: 14px;
    display: flex;
    min-height: 54px;

    .infoItemLabel {
      color: @text-colors[primary];
      display: flex;
      width: 160px;
      align-items: center;
    }

    .infoItemValue {
      color: @text-colors[secondary];
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }
}

.customerItem {
  font-size: 14px;
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
  width: 250px;

  .customerItemInfo {
    display: flex;

    .customerItemInfoName {
      flex: 1;
      width: 100%;
      // color: @text-colors[primary];
    }
  }

  .customerItemInfoPhone {
    width: 100px;
    margin: 4px 0;
  }
}

.cardBox {
  margin-bottom: 20px;
}

.cardTitle {
  color: #3d3d3d;
  font-size: 14px;
  font-weight: 500;
  margin-top: 12px;
  margin-bottom: 16px;
}

.formCardFooter {
  color: @text-colors[secondary];
  font-size: 16px;
  padding-bottom: 16px;
}

.orderSettingInfo {
  font-size: 14px;

  .orderSettingInfoItem {
    display: flex;
    border-bottom: 1px solid #f3f3f3;
    min-height: 54px;

    .orderSettingInfoItemLabel {
      color: @text-colors[primary];
      flex: 1;
      display: flex;
      align-items: center;
    }

    .orderSettingInfoItemValue {
      color: @text-colors[secondary];
      display: flex;
      width: 100px;
      align-items: center;
      justify-content: flex-end;
    }
  }

  .orderSettingInfoDesc {
    color: @text-colors[secondary];
    margin-top: 9px;
    padding-bottom: 16px;
  }
}

.splitAccountTable {
  padding-bottom: 20px;

  :global {
    .ant-table-cell {
      padding: 10px;
    }
  }
}

.hideCol {
  display: none;
}
