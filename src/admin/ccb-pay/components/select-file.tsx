import {
  forwardRef,
  useImperativeHandle,
  ForwardRefExoticComponent,
  RefAttributes,
  useState,
  useRef,
  ChangeEvent,
  Dispatch,
  SetStateAction,
  MutableRefObject,
} from 'react';
import { message } from 'antd';
// import { generateUploadFile, UploadFile } from '@/utils/file';

export interface ImageCropProps {
  maxSize?: number;
  fileSuffixes?: string[];
  onSuccess: MultipleParamsFn<[file: File | null]>;
  onChange?: MultipleParamsFn<[e: ChangeEvent<HTMLInputElement>]>;
}

export type SelectFileHandle = {
  fileEvent: ChangeEvent<HTMLInputElement> | null;
  setFileEvent: Dispatch<SetStateAction<ChangeEvent<HTMLInputElement> | null>>;
  fileInputRef: MutableRefObject<HTMLInputElement | null>;
  fileInputClick: () => void;
};

const SelectFile: ForwardRefExoticComponent<ImageCropProps & RefAttributes<SelectFileHandle>> =
  forwardRef(({ maxSize, fileSuffixes, onSuccess, onChange }: ImageCropProps, ref) => {
    const fileInputRef = useRef<HTMLInputElement | null>(null);
    const [fileEvent, setFileEvent] = useState<ChangeEvent<HTMLInputElement> | null>(null);
    const onInputChange = (e: ChangeEvent<HTMLInputElement>) => {
      onChange?.(e);
      const file = e.target.files?.length ? e.target.files[0] : null;
      const suffixes = file?.name.substring(file?.name.lastIndexOf('.'));
      if (fileSuffixes?.length && !fileSuffixes?.includes(suffixes || '')) {
        message.error(`文件格式错误，只支持文件格式为${fileSuffixes?.join(',')}的文件`);
        return;
      }
      if (maxSize && file && maxSize < file?.size) {
        message.error('文件大小超过限制');
        return;
      }
      onSuccess(file);
      // if (file) {
      //   generateUploadFile(file, false)
      //     .upload()
      //     .then((res) => {
      //       onSuccess(res);
      //     });
      // }

      // setFileEvent(e);
    };

    useImperativeHandle(ref, () => ({
      fileEvent,
      setFileEvent,
      fileInputRef,
      fileInputClick: () => {
        if (fileInputRef?.current?.value) {
          fileInputRef.current.value = '';
        }
        fileInputRef?.current?.click();
      },
    }));

    return (
      <div>
        <input
          ref={(node) => {
            fileInputRef.current = node;
          }}
          accept={fileSuffixes?.join(',')}
          type="file"
          style={{ display: 'none' }}
          onChange={onInputChange}
        />
      </div>
    );
  });

SelectFile.displayName = 'SelectFile';
SelectFile.defaultProps = {
  maxSize: 0,
  fileSuffixes: ['.xls', '.xlsx'],
  onChange: () => {},
};

export default SelectFile;
