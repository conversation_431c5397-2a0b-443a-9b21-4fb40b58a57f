import { RouteObject } from 'react-router-dom';
import { lazy } from '@/components';

const routes: RouteObject[] = [
  {
    path: 'ccb-pay/order/ledger',
    element: lazy(() => import('./pages/order-ledger')),
  },
  {
    path: 'ccb-pay/ccb/business',
    element: lazy(() => import('./pages/ccb-business')),
  },
  {
    path: 'ccb-pay/ccb/rule',
    element: lazy(() => import('./pages/ccb-rule')),
  },
];

export default routes;
