import { useCallback, useRef, useState } from 'react';
import { Form, message, Modal, ModalProps } from 'antd';
import type { RangePickerProps } from 'antd/es/date-picker/generatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { useMount } from 'ahooks';
import { DatePicker } from '@/components';
import isArray from 'lodash/isArray';
import { exportCompanyAuthData } from '@/apis';
import { downloadObject } from '@/utils/file';

function ExportCompanyApprovalData(props: ModalProps) {
  const [form] = Form.useForm<{ date?: [Dayjs, Dayjs] }>();
  const dateRangeRef = useRef<[min: Dayjs | null, max: Dayjs | null]>([null, null]);
  const nowDateRef = useRef(null as unknown as Date);
  const [loading, setLoading] = useState(false);
  const onCalendarChange: RangePickerProps<Dayjs>['onCalendarChange'] = useCallback(
    (dates, _, info) => {
      const index: 0 | 1 = info.range === 'end' ? 1 : 0;
      const date: Dayjs | null | undefined = dates?.[index];
      const values: typeof dateRangeRef.current = [null, null];
      if (dayjs.isDayjs(date)) {
        values[index] = date.endOf('day').add((!index ? 1 : -1) * 6, 'months');
        if (!index) {
          values[index] = values[index]!.add(-1, 'day');
        }
      }
      dateRangeRef.current = values;
    },
    []
  );
  const disableDate: RangePickerProps<Dayjs>['disabledDate'] = useCallback((date) => {
    const endDateRange = dateRangeRef.current;
    if (
      date.isAfter(nowDateRef.current) ||
      (endDateRange[0] && date.isAfter(endDateRange[0])) ||
      (endDateRange[1] && date.isBefore(endDateRange[1]))
    ) {
      return true;
    }
    return false;
  }, []);
  const onOk: ModalProps['onOk'] = useCallback(
    (e) => {
      const dates = form.getFieldValue('date');
      if (isArray(dates) && dayjs.isDayjs(dates[0]) && dayjs.isDayjs(dates[1])) {
        const { onCancel } = props;
        setLoading(true);
        // 发起请求并下载文件
        exportCompanyAuthData({ startDate: dates[0], endDate: dates[1] })
          .then((res) => {
            if (res instanceof Blob) {
              downloadObject(res, '优盟平台采购型会员数据.xlsx');
              onCancel?.(e);
            } else {
              message.error('下载失败');
            }
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        message.error('请先选择日期');
      }
      e.preventDefault();
    },
    [form, props.onCancel] // eslint-disable-line
  );

  useMount(() => {
    const now = dayjs().endOf('day');
    nowDateRef.current = now.toDate();
    form.setFieldsValue({ date: [now.add(-6, 'months').startOf('day'), now] });
  });

  return (
    <Modal {...props} confirmLoading={loading} centered width={410} title="导出数据" onOk={onOk}>
      <Form form={form}>
        <Form.Item name="date" label="选择日期：" className="mb-0">
          <DatePicker.RangePicker disabledDate={disableDate} onCalendarChange={onCalendarChange} />
        </Form.Item>
      </Form>
    </Modal>
  );
}

export default ExportCompanyApprovalData;
