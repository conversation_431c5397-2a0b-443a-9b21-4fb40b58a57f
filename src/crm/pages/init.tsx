import { useRequest, useMount } from 'ahooks';
import { Button, message } from 'antd';
import { useBack } from '@/hooks';
import { Context } from '@/components';
import { user } from '@/store';
import { crmInitData } from '@/apis';

const { Head } = Context;
function CrmInitPage() {
  const [onBack] = useBack();
  const { run, loading } = useRequest(crmInitData, { manual: true, debounceWait: 300 });

  useMount(() => {
    if (!user.member.master) {
      message.warning('暂无权限，请联系公司管理员开通');
      onBack();
    }
  });

  return (
    <Context head={<Head title="CRM" />} loading={loading}>
      <Button
        type="primary"
        onClick={run}
        style={{ position: 'absolute', margin: 'auto', inset: 0, width: '88px', height: '38px' }}
      >
        初始化
      </Button>
    </Context>
  );
}

CrmInitPage.displayName = 'CrmInitPage';

CrmInitPage.defaultProps = {};

export default CrmInitPage;
