@import '../../mobile/pages/merchant-settlement/compoents/form-card/form-card.module.less';

.filter {
  :global {
    .ant-form-item {
      border-bottom: 1px solid #f3f3f3;
      padding-bottom: 10px !important;
    }
    // .ant-form-item:last-child {
    //   border-bottom: none;
    //   margin-bottom: 0px !important;
    // }
    .ant-form-item-label {
      width: 66px;
      text-align: left;
    }

    .ant-form-item-label > label::after {
      content: '';
    }

    .ant-form-item-label > label {
      color: #040919;
    }

    .ant-form-item-label {
      font-weight: 400 !important;
    }

    .ant-image-mask {
      width: 86px;
      height: 86px;
    }

    .ant-form-item-explain {
      opacity: 1 !important;
    }
  }

  .man {
    position: relative;
  }

  .mandatory::before {
    content: '*';
    color: #ff4d4f;
    font-size: 14px;
    position: absolute;
    top: 8px;
    left: -7px;
    font-family: SimSun, sans-serif;
  }

  .formCard {
    margin-bottom: 94px;
  }

  .text {
    text-align: right;
  }

  .fixForm {
    margin-bottom: 12px;
    padding: 10px 20px 0;
    border-radius: 18px;
    background: #fff;

    /* 常规卡片样式 */
    box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  }

  .submitBox {
    display: flex;
    justify-content: space-between;
    position: fixed;
    align-items: center;

    .submitBtn {
      width: 48%;
    }

    .submitExit {
      color: #888b98;
      background: #f3f3f3;
    }

    .submitAdd {
      color: #fff;
      background: linear-gradient(257deg, #00c6ff 0%, #008cff 100%);
    }

    .submitBtnS {
      cursor: pointer;
      width: 100%;
      background-color: #008cff;
    }
  }

  .edit {
    color: #008cff;
    cursor: pointer;
  }

  .tempMessage {
    color: #040919;
    font-size: 16px;
    margin-top: 16px;
  }

  .customImg {
    width: 86px;
    height: 86px;
    border-radius: 10px;
    margin-right: 19px;
    margin-bottom: 19px;
  }

  .customImg:nth-child(3n) {
    margin-right: 0;
  }

  .notCheck {
    color: #c6ccd8;
    cursor: not-allowed;
  }

  .formRender {
    :global .ant-form-item-control {
      margin-top: -7px;
    }

    :global {
      .ant-form-item {
        flex-direction: column;
        align-items: flex-start;
      }

      .ant-col.ant-form-item-control {
        width: 100%;
      }

      .ant-input-textarea {
        padding-bottom: 12px;
      }
    }

    :global [role='button'] {
      width: 90px;
      height: 90px;
      margin-left: 16px;

      div {
        width: 100%;
        height: 100%;
        line-height: 90px;
      }

      .anticon-plus {
        font-size: 30px;
        vertical-align: middle;
      }
    }

    :global .ant-image {
      margin-left: 10px;
    }
  }
}
