import { MutableRefObject, PropsWithChildren, useEffect, useRef, useState, useMemo } from 'react';
import { Drawer, Modal, SimpleUploadInstance } from '@/components';
import { Form, FormInstance, Input, Select, Spin, message } from 'antd';
import {
  ApplyMallParam,
  getFormTemplateComponents,
  getMallMchDetail,
  getProvinceCityCounty,
  getMallCategoryType,
  mallResult,
} from '@/apis';
import classNames from 'classnames';
import { FormRender, FormRenderInstance, FormViewer } from '@/src/form-engine/containers';
import { mallDetailResult } from '@/apis/gmall/get-mall-mch-detail';
import { useSearchParams } from 'react-router-dom';
import { isPhone } from '@/utils/utils';
import putMallMchUpdate, {
  mallUpdateParams,
  mchExtendParamsItem,
} from '@/apis/gmall/put-mall-mch-update';
import useFormEngineData from '@@/form-engine/hooks/use-form-engine-data';
import dayjs from 'dayjs';
import isString from 'lodash/isString';
import { useTranslation } from 'react-i18next';
import Merchant from '../components/merchant';
import style from './entry-details.module.less';

interface Details {
  formCode: null | string;
  visible: boolean;
  isApply: boolean;
  btnItem: mallResult;
  onClose: () => void;
  refusalToPass: SimpleFn<number>;
}

interface cityResult {
  label: string;
  pinyin: string;
  value: number;
  children: cityResult[];
}

interface StoresOrHncRef {
  formRef: MutableRefObject<FormInstance>;
  uploadRef: MutableRefObject<SimpleUploadInstance>;
}

interface unKnowResult {
  [key: string]: string;
}

const { Option } = Select;

function EntryDetails({
  visible,
  isApply,
  formCode,
  btnItem,
  onClose,
  refusalToPass,
  ...props
}: PropsWithChildren<Details>) {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const tenantId = searchParams.get('tenantId') || '';
  const formRef = useRef(null as unknown as FormInstance);
  const CustomContentRef = useRef(null as unknown as FormRenderInstance);
  const storesOrHncRef = useRef(null as unknown as StoresOrHncRef);
  const [formParams, setFormParams] = useState({
    level: [1],
  } as unknown as ApplyMallParam);
  const [categoryList, setCategoryList] = useState<{ id: number; name: string }[]>([]);
  const [detailItem, setDetailItem] = useState({} as mallDetailResult);
  const [component, setComponent] = useState<any[]>([]);
  const [, setCityList] = useState<cityResult[]>([]);
  const [initialValues, setInitialValues] = useState<Record<string, any>>({});
  const [isEdit, setIsEdit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [formObj, setFormObj] = useState<Record<string, unknown> | null>(null);
  const formEngine = useMemo(
    () => ({
      formCode: detailItem.formCode,
      versionNumber: detailItem.versionNumber,
    }),
    [detailItem]
  );
  const [formData, components] = useFormEngineData(
    formObj,
    detailItem.formCode ? formEngine : null
  );
  // 通过拒绝入驻申请
  const editDrawer = () => {
    if (isEdit) {
      Modal.confirm({
        title: t('entryDetails_modal_title'),
        content: t('entryDetails_modal_content'),
        okText: t('entryDetails_modal_continue'),
        icon: '',
        cancelText: t('entryDetails_modal_leave'),
        centered: true,
        keyboard: false,
        maskClosable: false,
        onCancel: () => {
          if (formRef.current) {
            formRef.current.resetFields();
            setIsEdit(false);
          }
        },
        onOk: () => {},
      });
    } else {
      onClose();
    }
  };

  // 编辑赋值
  const editClick = () => {
    setLoading(true);
    const isImg: unKnowResult = {};
    const isData: unKnowResult = {};
    detailItem.extList.forEach((res) => {
      const colValue = res.colValue.match(/^\[.*?\]$/g) ? JSON.parse(res.colValue) : res.colValue;
      if (res.colValue.match('test' || 'user_files')) {
        isImg[res.colName] = colValue;
      } else {
        isData[res.colName] = colValue;
      }
    });
    setInitialValues(isImg);
    formRef.current.setFieldsValue({
      linkman: detailItem.contact,
      phone: detailItem.phone,
      level: detailItem.categories.map((m) => m.id),
      shopId: detailItem.shopId || null,
    });
    setIsEdit(true);
    setTimeout(() => {
      setLoading(false);
      if (formCode) CustomContentRef.current.form.setFieldsValue({ ...isData, ...isImg });
    }, 1000);
  };

  // 初始化数据
  const initData = () => {
    setLoading(true);
    Promise.all([
      getMallCategoryType({ type: 1, tenantId }).then((res) => {
        setCategoryList(res.list);
      }),
      getMallMchDetail({ id: btnItem.id, tenantId }).then((res) => {
        setDetailItem(res);
        const obj: Record<string, unknown> = {};
        res.extList.forEach((item) => {
          obj[item.colName] = item.colValue;
        });
        setFormObj(obj);
      }),
      getProvinceCityCounty().then((res) => {
        setCityList(res.list);
      }),
    ]).finally(() => {
      setLoading(false);
    });
  };

  // 表单提交
  const submit = () => {
    formRef.current.validateFields().then((res) => {
      const updateData: mallUpdateParams = {
        mallId: detailItem.mallId,
        formCode: detailItem.formCode,
        versionNumber: detailItem.versionNumber,
        mchExtendParams: [] as mchExtendParamsItem[],
        phone: res.phone,
        contact: res.linkman,
        category: res.level,
        id: detailItem.id,
        tenantId,
      };
      if (detailItem.type === 6 || detailItem.type === 7) {
        const storesOrHncRefData = storesOrHncRef.current.formRef.current.getFieldsValue();
        const isNull = Object.values(storesOrHncRefData).every((f: unknown) => {
          const content = f as string;
          if (!content.length) {
            message.error(t('entryDetails_message_empty'));
            return false;
          }
          return true;
        });
        if (!isNull) return;
        const businessScope = storesOrHncRefData.businessScope.map(
          (m: number[]) => m[m.length - 1]
        );
        updateData.physicalStoreInfoParam = {
          ...detailItem.physicalStore,
          ...storesOrHncRefData,
          businessScope,
        };
        if (detailItem.type === 6) {
          // @ts-ignore
          updateData.physicalStoreInfoParam.storeProvince =
            storesOrHncRefData.storeProvince.join('、');
        }
      }
      if (formCode) {
        CustomContentRef.current.form.validateFields().then((formItem) => {
          const itemArray = Object.keys(formItem);
          updateData.mchExtendParams = itemArray.map((column) => {
            const objFoo = detailItem.extList.find((f) => f.colName === column);
            const objBaz = component.find((f) => f.colName === column);
            return {
              label: objFoo?.label || objBaz.attr.label,
              colName: objFoo?.colName || column,
              colValue: isString(formItem[column])
                ? formItem[column]
                : JSON.stringify(formItem[column]),
            };
          });
          putMallMchUpdate(updateData).then(() => {
            setIsEdit(false);
            initData();
          });
        });
      } else {
        putMallMchUpdate(updateData).then(() => {
          setIsEdit(false);
          initData();
        });
      }
    });
  };

  useEffect(() => {
    if (visible) {
      initData();
      if (formCode) {
        getFormTemplateComponents({ formCode }).then((res) => {
          setComponent(res.components);
        });
      }
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      title={t('entryDetails_title')}
      visible={visible}
      {...props}
      className={style.filter}
      extra={
        !isApply &&
        !isEdit && (
          <div
            className={style.edit}
            tabIndex={0}
            role="button"
            onClick={() => {
              editClick();
            }}
          >
            {t('entryDetails_btn_edit')}
          </div>
        )
      }
      onClose={() => {
        editDrawer();
      }}
    >
      <Spin spinning={loading}>
        <div className={style.formCard}>
          <div className={style.fixForm}>
            <Form layout="horizontal" ref={formRef}>
              <Form.Item label={t('entryDetails_label_company')} name="enterCompanyName">
                <div className={classNames(style.text, isEdit && style.notCheck)}>
                  {detailItem.enterCompanyName || t('entryDetails_text_empty')}
                </div>
              </Form.Item>
              <Form.Item label={t('entryDetails_label_shop')} name="shopId">
                <div className={classNames(style.text, isEdit && style.notCheck)}>
                  {detailItem.shopName || t('entryDetails_text_empty')}
                </div>
              </Form.Item>

              <Form.Item
                label={t('entryDetails_label_contact')}
                name="linkman"
                className={classNames(style.man, isEdit && style.mandatory)}
                rules={[
                  {
                    validator(_rule, value) {
                      if (value.length > 0) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('entryDetails_form_contactRequired')));
                    },
                  },
                ]}
              >
                {isEdit ? (
                  <Input
                    maxLength={10}
                    placeholder={t('entryDetails_form_contactPlaceholder')}
                    defaultValue={detailItem.contact}
                    bordered={false}
                  />
                ) : (
                  <div className={style.text}>{detailItem.contact}</div>
                )}
              </Form.Item>

              <Form.Item
                label={t('entryDetails_label_phone')}
                name="phone"
                className={classNames(style.man, isEdit && style.mandatory)}
                rules={[
                  {
                    validator(_rule, value) {
                      if (/^\d+$/.test(value) && value.length === 11 && isPhone(value)) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error(t('entryDetails_form_phoneError')));
                    },
                  },
                ]}
              >
                {isEdit ? (
                  <Input
                    placeholder={t('entryDetails_form_phonePlaceholder')}
                    maxLength={11}
                    defaultValue={detailItem.phone}
                    bordered={false}
                  />
                ) : (
                  <div className={style.text}>{detailItem.phone}</div>
                )}
              </Form.Item>

              <Form.Item
                label={t('entryDetails_label_entryType')}
                name="typeName"
                className={classNames(style.man, isEdit && style.mandatory)}
              >
                <div className={classNames(style.text, isEdit && style.notCheck)}>
                  {detailItem.typeName}
                </div>
              </Form.Item>

              <Form.Item
                label={t('entryDetails_label_identityType')}
                name="identityType"
                className={classNames(style.man, isEdit && style.mandatory)}
              >
                <div className={classNames(style.text, isEdit && style.notCheck)}>
                  {detailItem.identityType === 1
                    ? t('entryDetails_text_enterprise')
                    : t('entryDetails_text_personal')}
                </div>
              </Form.Item>

              {categoryList.length > 0 && (
                <Form.Item
                  label={isEdit && t('entryDetails_label_merchantType')}
                  name="level"
                  className={classNames(style.man, isEdit && style.mandatory)}
                >
                  {isEdit ? (
                    <Select
                      placeholder={t('entryDetails_form_merchantTypePlaceholder')}
                      value={formParams.level}
                      bordered={false}
                      mode="multiple"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      defaultValue={detailItem.categories.map((m) => m.id)}
                      onChange={(val) => {
                        setFormParams((par) => ({ ...par, level: val }));
                      }}
                    >
                      {categoryList.map((res) => (
                        <Option value={res.id} key={res.id} label={res.name}>
                          {res.name}
                        </Option>
                      ))}
                    </Select>
                  ) : (
                    <>
                      <div>{t('entryDetails_label_merchantType')}</div>
                      <div className={style.tempMessage}>
                        {detailItem.categories?.map((m) => m.name).join('、')}
                      </div>
                    </>
                  )}
                </Form.Item>
              )}

              <Form.Item label={t('entryDetails_label_applyTime')} name="enterCompanyId">
                <div className={classNames(style.text, isEdit && style.notCheck)}>
                  {detailItem.applyTime
                    ? dayjs(detailItem.applyTime).format('YYYY-MM-DD')
                    : t('entryDetails_text_empty')}
                </div>
              </Form.Item>

              <Form.Item label={t('entryDetails_label_settleTime')} name="enterCompanyId">
                <div className={classNames(style.text, isEdit && style.notCheck)}>
                  {detailItem.verifyTime
                    ? dayjs(detailItem.verifyTime).format('YYYY-MM-DD')
                    : t('entryDetails_text_empty')}
                </div>
              </Form.Item>
            </Form>
          </div>

          {detailItem.physicalStore && (detailItem.type === 6 || detailItem.type === 7) && (
            <div className={style.fixForm}>
              <div className={style.formRender}>
                <Merchant
                  type={String(detailItem.type)}
                  tenantId={tenantId}
                  isEdit={isEdit}
                  ref={storesOrHncRef}
                  physicalStore={detailItem.physicalStore}
                />
              </div>
            </div>
          )}

          {!isEdit && detailItem?.extList?.length > 0 && formData && (
            // <div className={style.fixForm}>
            <FormViewer flat theme="cell" items={components} values={formData} />
            // </div>
          )}

          {formCode && isEdit && (
            <div className={style.fixForm}>
              <div className={style.formRender}>
                <FormRender
                  theme={false}
                  isMobile
                  initialValues={initialValues}
                  layout="horizontal"
                  formCode={formCode || null}
                  ref={CustomContentRef}
                />
              </div>
            </div>
          )}

          {isApply && (
            <div className={style.submitBox}>
              <div
                className={classNames(style.submitBtn, style.submitExit)}
                role="button"
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  refusalToPass(0);
                }}
              >
                {t('entryDetails_btn_reject')}
              </div>
              <div
                className={classNames(style.submitBtn, style.submitAdd)}
                role="button"
                tabIndex={0}
                onClick={(e) => {
                  e.stopPropagation();
                  refusalToPass(1);
                }}
              >
                {t('entryDetails_btn_pass')}
              </div>
            </div>
          )}

          {isEdit && (
            <div className={style.submitBox}>
              <div
                className={classNames(style.submitBtn, style.submitBtnS)}
                role="button"
                tabIndex={0}
                onClick={submit}
              >
                {t('entryDetails_btn_save')}
              </div>
            </div>
          )}
        </div>
      </Spin>
    </Drawer>
  );
}

export default EntryDetails;
