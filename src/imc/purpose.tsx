import { Form, Image } from 'antd';
import { isArray } from 'lodash';
import style from './containers/entry-details.module.less';
/**
 * 数组合并
 * @param ArrayOne 数组1
 * @param ArrayTwo 数组2
 * @param key {contrastOne：数组1需要对比的字段， contrastTwo： 数组2需要对比的对象}
 */

interface ArrType {
  [key: string]: any;
}

interface Ikea {
  contrastOne: keyof ArrType;
  contrastTwo: keyof ArrType;
}

// 传入两个不规则数组对象，把key值相同的对象进行合并返回新数组
export function mergeArray(ArrayOne: ArrType[], ArrayTwo: ArrType[], key: Ikea) {
  const newArray = ArrayOne.map((item) => {
    const obj = ArrayTwo.find((res) => item[key.contrastOne] === res[key.contrastTwo]);
    return {
      ...item,
      ...obj,
    };
  });

  return newArray;
}

/**
 * 自定义组件消息展示
 * @param customItem
 */

interface extListResult {
  id: number;
  // 主键ID
  gmallMchId: number;
  // 商户管理表ID
  label: string;
  // 标题
  colName: string;
  // 列名
  colValue: string;
  // 列值
}

interface cityResult {
  label: string;
  pinyin: string;
  value: number;
  children: cityResult[];
}

export function customTemplate(customItem: extListResult[], cityList?: cityResult[]) {
  const customTemp = (res: extListResult) => {
    const colValue: any = res.colValue.match(/^\[.*?\]$/g)
      ? JSON.parse(res.colValue)
      : res.colValue;
    const urlRef = /^(https?:)?\/\//;

    const area = cityList
      ?.find((f) => f.value === colValue[0])
      ?.children.find((f) => f.value === colValue[1]);
    if (isArray(colValue) && area) {
      return (
        <Form.Item label={res.label} name={res.colName}>
          <div className={style.text}>{area?.label}</div>
        </Form.Item>
      );
    }
    if (
      isArray(colValue) &&
      colValue.some((s) => s.includes && (s.includes('user_files') || s.includes('test')))
    ) {
      return (
        <Form.Item label="" name={res.colName}>
          <div>{res.label}</div>
          <div className={style.tempMessage}>
            {colValue.map((img: string, index: number) => (
              <Image
                className={style.customImg}
                key={img}
                style={!((index + 1) % 3) ? { marginRight: '0' } : { marginRight: '19px' }}
                src={urlRef.test(img) ? img : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${img}`}
                alt=""
              />
            ))}
          </div>
        </Form.Item>
      );
    }
    return (
      <Form.Item label="" name={res.colName}>
        <div>{res.label}</div>
        <div className={style.tempMessage}>
          {isArray(colValue) ? colValue.join('、') : colValue}
        </div>
      </Form.Item>
    );
  };

  const customHtml = customItem.map((item) => customTemp(item));

  return customHtml;
}

/**
 * 根据子集id获取父级id并重新组成一个新的数组
 */

interface Node {
  id: number;
  // 主键ID
  parentId: number;
  // 父级ID
  name: string;
  // 分类名称
  image?: string;
  // 分类图片
  level: number;
  // 分类等级
  childrenList: Node[];
  // 子级集合
}

export function findNodeById(id: number, nodes: Node[], returnName?: boolean) {
  const ids: unknown[] = [];
  const getId = (idFoo: number, nodesFoo: Node[]) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const node of nodesFoo) {
      if (node.id === idFoo) {
        ids.push(returnName ? node.name : node.id);
        if (node.level === 1) return;
        if (node.level !== 1) {
          if (node.parentId) getId(node.parentId, nodes);
        }
      }
      if (node.childrenList) {
        getId(idFoo, node.childrenList);
      }
    }
  };
  getId(id, nodes);
  return ids.reverse();
}
