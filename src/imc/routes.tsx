import { RouteObject } from 'react-router-dom';
import { lazy } from '@/components';

/**
 * 路由列表
 */
const routes: RouteObject[] = [
  {
    path: 'merchant',
    element: lazy(() => import('./pages/merchant-management')),
  },
  {
    path: 'merchant/merchant-identity',
    element: lazy(() => import('./pages/merchant-identity/merchant-identity')),
  },
  {
    path: 'merchant/merchant-identity/create-identity',
    element: lazy(() => import('./pages/merchant-identity/create-identity')),
  },
  {
    path: 'merchant/merchant-identity/merchant-entry-form',
    element: lazy(() => import('./pages/merchant-identity/merchant-entry-form')),
  },
  {
    path: 'merchant/merchant-identity/merchant-entry-form-preview/*',
    element: lazy(() => import('./pages/merchant-identity/merchant-entry-form-preview')),
  },
  {
    path: 'sheet/collection',
    element: lazy(() => import('../mobile/pages/work-sheet/collection')),
  },
  {
    path: 'guidance',
    element: lazy(() => import('./pages/beginner-guidance')),
  },
  {
    path: 'guidance/book',
    element: lazy(() => import('./pages/hand-book')),
  },
  // {
  //   path: '/join/group',
  //   element: lazy(() => import('./pages/join-group')),
  // },
  // {
  //   path: '*',
  //   element: MicroApp,
  // },
];

export default routes;

// 模块根目录
export const basePath = '/imc';
