import { Form, FormInstance, Input } from 'antd';
import { forwardRef, PropsWithChildren, useImperativeHandle } from 'react';
import { useTranslation } from 'react-i18next';
import style from './custom-content.module.less';

export interface customContentRef {
  form: FormInstance;
}

interface customContentProps {
  formCode: string | null;
}
const CustomContent = forwardRef<customContentRef, PropsWithChildren<customContentProps>>(
  ({ formCode }, ref) => {
    const { t } = useTranslation();
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
      form,
    }));

    return (
      <Form form={form} className={style.customContent} style={{ marginLeft: '-8px' }}>
        <Form.Item label={t('merchant_table_companyName')} name="enterCompanyId">
          <div className={style.text}>{formCode}</div>
          <Input />
        </Form.Item>
      </Form>
    );
  }
);

export default CustomContent;
