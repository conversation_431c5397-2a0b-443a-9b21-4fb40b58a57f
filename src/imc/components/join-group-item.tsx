import { Row, Col, But<PERSON>, Image, List, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './join-group-item.module.less';
import type { ItemInterface } from '../pages/join-group';

const { Title, Text } = Typography;
interface Props {
  item: ItemInterface;
}

function JoinGroupItem({ item }: Props) {
  const { t } = useTranslation();
  return (
    <List.Item className={styles.item}>
      <Row>
        <Col span={12}>
          <Image className={styles.round} width={43} height={43} src={item.avatar} />
          <div>
            <Title level={5}>{item.title}</Title>
            <Text>{item.message}</Text>
          </div>
        </Col>
        <Col span={12}>
          <div>
            <Button>{t('merchant_btn_pass')}</Button>
            <Button>{t('merchant_btn_reject')}</Button>
            <Text>{t('merchant_text_passed')}</Text>
            <Text>{t('merchant_text_rejected')}</Text>
          </div>
        </Col>
      </Row>
    </List.Item>
  );
}
export default JoinGroupItem;
