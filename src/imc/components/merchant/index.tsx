import { Form, FormInstance, Image, Input } from 'antd';
import {
  MutableRefObject,
  forwardRef,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { SimpleUploadInstance, Upload } from '@/components';
import { getProvinceCityCounty } from '@/apis';
import Cascader from 'antd/es/cascader';
import { ProvinceCityCounty } from '@/apis/get-province-city-county';
import classNames from 'classnames';
import { physicalStoreParams } from '@/apis/gmall/get-mall-mch-detail';
import getMallCategoryTreeList, {
  mallTreeListParams,
} from '@/apis/gmall/get-mall-category-tree-list';
import { useTranslation } from 'react-i18next';
import style from './index.module.less';
import { findNodeById } from '../../purpose';

interface StoresOrHncProps {
  type: string;
  tenantId: string | null;
  isEdit: boolean;
  physicalStore: physicalStoreParams;
}

interface StoresOrHncRef {
  formRef: MutableRefObject<FormInstance>;
  uploadRef: MutableRefObject<SimpleUploadInstance>;
}

const urlRef = /^(https?:)?\/\//;

const Merchant = forwardRef<StoresOrHncRef, StoresOrHncProps>(
  ({ type, isEdit, physicalStore, tenantId }, ref) => {
    const { t } = useTranslation();
    const formRef = useRef(null as unknown as FormInstance);
    const uploadRef = useRef(null as unknown as SimpleUploadInstance);
    // const [physicalStore] = useState(physicalStore as unknown as physicalStoreParams);
    const [cityList, setCityList] = useState<ProvinceCityCounty[]>([]);
    const [shopList, setShopList] = useState<mallTreeListParams[]>([]);

    const getCityCounty = () => {
      getProvinceCityCounty().then((res) => {
        setCityList(res.list);
      });
    };

    const getScopeBusiness = () => {
      if (tenantId) {
        getMallCategoryTreeList({ tenantId }).then((res) => {
          setShopList(res.list);
        });
      }
    };

    useImperativeHandle(
      ref,
      () => ({
        formRef,
        uploadRef,
      }),
      []
    );

    useEffect(() => {
      getCityCounty();
      getScopeBusiness();
    }, []); // eslint-disable-line

    useEffect(() => {
      if (isEdit) {
        setTimeout(() => {
          const businessScope = physicalStore.businessScope.map((id) => {
            const obj = findNodeById(id, shopList, false);
            return obj;
          });
          const obj = {
            ...physicalStore,
            businessScope,
          };
          if (type === '6') {
            // @ts-ignore
            obj.storeProvince = obj.storeProvince.split('、');
          }
          formRef.current?.setFieldsValue(obj);
        });
      }
    }, [isEdit]); // eslint-disable-line

    const hnc = () => {
      if (!isEdit) {
        return (
          <>
            <Form.Item label="" name={physicalStore.navigationAddress}>
              <div>{t('merchant_form_navAddress')}</div>
              <div className={style.tempMessage}>{physicalStore.navigationAddress}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeName}>
              <div>{t('merchant_form_storeName')}</div>
              <div className={style.tempMessage}>{physicalStore.storeName}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeProvince}>
              <div>{t('merchant_form_province')}</div>
              <div className={style.tempMessage}>{physicalStore.storeProvince}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeProjectArea}>
              <div>{t('merchant_form_projectArea')}</div>
              <div className={style.tempMessage}>{physicalStore.storeProjectArea}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeFloor}>
              <div>{t('merchant_form_floor')}</div>
              <div className={style.tempMessage}>{physicalStore.storeFloor}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeBuilding}>
              <div>{t('merchant_form_building')}</div>
              <div className={style.tempMessage}>{physicalStore.storeBuilding}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeNumber}>
              <div>{t('merchant_form_roomNumber')}</div>
              <div className={style.tempMessage}>{physicalStore.storeNumber}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.businessScope}>
              <div>{t('merchant_form_businessScope')}</div>
              <div className={style.tempMessage}>
                {physicalStore.businessScope
                  .map((id) => {
                    const obj = findNodeById(id, shopList, true);
                    return obj[obj.length - 1];
                  })
                  .join('、')}
              </div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeIntroduce}>
              <div>{t('merchant_form_storeDesc')}</div>
              <div className={style.tempMessage}>{physicalStore.storeIntroduce}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeImages}>
              <div>{t('merchant_form_storeFront')}</div>
              <div className={style.tempMessage}>
                <div className={style.tempMessage}>
                  {physicalStore.storeImages.map((img: string, index: number) => (
                    <Image
                      className={style.customImg}
                      key={img}
                      style={!((index + 1) % 3) ? { marginRight: '0' } : { marginRight: '19px' }}
                      src={
                        urlRef.test(img) ? img : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${img}`
                      }
                      alt=""
                    />
                  ))}
                </div>
              </div>
            </Form.Item>
          </>
        );
      }
      return (
        <>
          <Form.Item
            label={t('merchant_form_navAddress')}
            name="navigationAddress"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <div className={classNames(style.text, isEdit && style.notCheck)}>
              {physicalStore.navigationAddress}
            </div>
          </Form.Item>
          <Form.Item
            label={t('merchant_form_storeName')}
            name="storeName"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_storeNamePlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_province')}
            name="storeProvince"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Cascader
              style={{ width: '100%' }}
              options={cityList}
              allowClear={false}
              placeholder={t('merchant_form_provincePlaceholder')}
              fieldNames={{
                label: 'label',
                value: 'label',
                children: 'children',
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_projectArea')}
            name="storeProjectArea"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_projectAreaPlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_floor')}
            name="storeFloor"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_floorPlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_building')}
            name="storeBuilding"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_buildingPlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_roomNumber')}
            name="storeNumber"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_roomNumberPlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_businessScope')}
            name="businessScope"
            rules={[{ required: false, message: t('merchant_form_businessScopeRequired') }]}
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Cascader
              style={{ width: '100%' }}
              options={shopList}
              getPopupContainer={(getPopupContainer) => getPopupContainer.parentNode}
              allowClear={false}
              multiple
              showCheckedStrategy="SHOW_CHILD"
              maxTagCount="responsive"
              placeholder={t('merchant_form_businessScopePlaceholder')}
              fieldNames={{
                label: 'name',
                value: 'id',
                children: 'childrenList',
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_storeDesc')}
            name="storeIntroduce"
            rules={[{ required: false, message: t('merchant_form_businessScopeRequired') }]}
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_storeDescPlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_storeFront')}
            name="storeImages"
            rules={[{ required: false, message: t('merchant_form_storeFrontRequired') }]}
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Upload
              className={style.upload}
              ref={uploadRef}
              listType="card"
              maxCount={6}
              multiple
              listClassName={style.listClassName}
            />
          </Form.Item>
        </>
      );
    };

    const stores = () => {
      if (!isEdit) {
        return (
          <>
            <Form.Item label="" name={physicalStore.navigationAddress}>
              <div>{t('merchant_form_navAddress')}</div>
              <div className={style.tempMessage}>{physicalStore.navigationAddress}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeName}>
              <div>{t('merchant_form_storeName')}</div>
              <div className={style.tempMessage}>{physicalStore.storeName}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeAddress}>
              <div>{t('merchant_form_storeAddress')}</div>
              <div className={style.tempMessage}>{physicalStore.storeAddress}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.businessScope}>
              <div>{t('merchant_form_businessScope')}</div>
              <div className={style.tempMessage}>
                {physicalStore.businessScope
                  .map((id) => {
                    const obj = findNodeById(id, shopList, true);
                    return obj[obj.length - 1];
                  })
                  .join('、')}
              </div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeIntroduce}>
              <div>{t('merchant_form_storeDesc')}</div>
              <div className={style.tempMessage}>{physicalStore.storeIntroduce}</div>
            </Form.Item>
            <Form.Item label="" name={physicalStore.storeImages}>
              <div>{t('merchant_form_storeFront')}</div>
              <div className={style.tempMessage}>
                <div className={style.tempMessage}>
                  {physicalStore.storeImages.map((img: string, index: number) => (
                    <Image
                      className={style.customImg}
                      key={img}
                      style={!((index + 1) % 3) ? { marginRight: '0' } : { marginRight: '19px' }}
                      src={
                        urlRef.test(img) ? img : `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/${img}`
                      }
                      alt=""
                    />
                  ))}
                </div>
              </div>
            </Form.Item>
          </>
        );
      }
      return (
        <>
          <Form.Item
            label={t('merchant_form_navAddress')}
            name="navigationAddress"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <div className={classNames(style.text, isEdit && style.notCheck)}>
              {physicalStore.navigationAddress}
            </div>
          </Form.Item>
          <Form.Item
            label={t('merchant_form_storeName')}
            name="storeName"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_storeNamePlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_storeAddress')}
            name="storeAddress"
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_storeAddressPlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_businessScope')}
            name="businessScope"
            rules={[{ required: false, message: t('merchant_form_businessScopeRequired') }]}
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Cascader
              style={{ width: '100%' }}
              options={shopList}
              getPopupContainer={(getPopupContainer) => getPopupContainer.parentNode}
              allowClear={false}
              multiple
              showCheckedStrategy="SHOW_CHILD"
              maxTagCount="responsive"
              placeholder={t('merchant_form_businessScopePlaceholder')}
              fieldNames={{
                label: 'name',
                value: 'id',
                children: 'childrenList',
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_storeDesc')}
            name="storeIntroduce"
            rules={[{ required: false, message: t('merchant_form_businessScopeRequired') }]}
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Input.TextArea
              bordered={false}
              autoSize={{ minRows: 1, maxRows: 5 }}
              placeholder={t('merchant_form_storeDescPlaceholder')}
            />
          </Form.Item>
          <Form.Item
            label={t('merchant_form_storeFront')}
            name="storeImages"
            rules={[{ required: false, message: t('merchant_form_storeFrontRequired') }]}
            className={classNames(style.man, isEdit && style.mandatory)}
          >
            <Upload
              className={style.upload}
              ref={uploadRef}
              listType="card"
              maxCount={6}
              multiple
              listClassName={style.listClassName}
            />
          </Form.Item>
        </>
      );
    };
    return (
      <div className={style.hnc}>
        <Form layout="horizontal" ref={formRef}>
          {type === '6' ? hnc() : stores()}
        </Form>
      </div>
    );
  }
);

export default Merchant;
