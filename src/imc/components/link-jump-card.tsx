import { PropsWithChildren } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './link-jump-card.module.less';

interface dataParams {
  title: string;
  item: {
    title: string;
    body: string;
    btn: string;
    type: number;
    bgColor?: string;
  }[];
}

interface linkJumpCardParams {
  data: dataParams[];
}

const color = [
  'linear-gradient(90deg, #D6E2FE 0%, rgba(214,226,254,0.50) 99%)',
  'linear-gradient(90deg, #D5E9F9 0%, rgba(213,233,249,0.50) 97%)',
  'linear-gradient(90deg, #E1D2F9 0%, rgba(225,210,249,0.50) 98%)',
  'linear-gradient(90deg, #E0F6F1 0%, rgba(215,236,233,0.40) 100%)',
];

function LinkJumpCard({ data }: PropsWithChildren<linkJumpCardParams>) {
  const navigate = useNavigate();
  const jump = (type: number) => {
    navigate(`/guidance/book?type=${type}`);
  };

  return (
    <>
      {data.map((item) => (
        <div className={styles.LinkJumpCard}>
          <div className={styles.title}>{item.title}</div>
          <div className={styles.cardFlex}>
            {item.item.map((card, index) => (
              <div className={styles.card} style={{ background: card.bgColor || color[index] }}>
                <div className={styles.cardTitle}>{card.title}</div>
                <div className={styles.cardBody}>{card.body}</div>
                <div
                  className={styles.cardJump}
                  role="button"
                  tabIndex={card.type}
                  onClick={() => jump(card.type)}
                >
                  {card.btn}
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </>
  );
}

export default LinkJumpCard;
