@import '../components/link-jump-card.module.less';

.guidance {
  width: 100%;
  min-height: 100%;
  padding: 0 20px 14px;
  border-radius: 20px;
  opacity: 1;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .banner {
    display: flex;
    height: 240px;
    justify-content: center;
    position: relative;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(180deg, #fff 0%, #f5f6fa 97%);

    &::after {
      content: '';
      width: 100%;
      height: 240px;
      position: absolute;
      left: -22px;
      z-index: 3;
      background-image: linear-gradient(
          to right,
          rgb(103 155 253 / 0%) 0%,
          rgb(255 255 255 / 0%) 100%
        ),
        url('../assets/img/bg-left.png');
      // background-size: cover;
      background-repeat: no-repeat;
    }

    &::before {
      content: '';
      width: 465px;
      min-width: 465px;
      height: 240px;
      min-height: 240px;
      position: absolute;
      right: -20px;
      z-index: 2;
      background-image: url('https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/202348/1680917990193738.png');
      background-size: cover;
      background-repeat: no-repeat;
    }
  }

  .iconImg {
    width: 58px;
    height: 58px;
    z-index: 3;
  }

  .fingerPost {
    color: #040919;
    font-size: 30px;
    font-weight: 600;
    margin: 16px 0 8px;
    z-index: 3;
  }

  .master {
    color: #040919;
    font-size: 18px;
    font-weight: normal;
    z-index: 3;
  }
}
