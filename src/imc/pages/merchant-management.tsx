/* eslint-disable no-nested-ternary */
import {
  getMallPageList,
  getMerchantDataExport,
  postExportQrCodeBiz,
  putMallMchEnable,
  putMallVerify,
} from '@/apis';
import { mallParams, mallResult } from '@/apis/gmall/get-mall-page-list';
import { Context, List, ListInstance, Modal } from '@/components';
import { Button, message, Tooltip } from 'antd';
import dayjs from 'dayjs';
import { ChangeEvent, useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { ColumnsType } from 'antd/es/table';
import { ItemType } from '@/apis/company-admin/upload-members-excel';
import { useUnmount } from 'ahooks';
import { downloadObject } from '@/utils/file';
import TextArea from 'antd/lib/input/TextArea';
import { useTranslation } from 'react-i18next';
import styles from './merchant-management.module.less';

// import faith from '../assets/img/faith.png';
// import ordinary from '../assets/img/ordinary.png';
// import cityPartner from '../assets/img/city-partner.png';
import EntryDetails from '../containers/entry-details';

function MerchantManagement() {
  const { t } = useTranslation();
  const navigator = useNavigate();
  const refreshRef = useRef(null as unknown as ListInstance);
  const [searchType] = useSearchParams();
  const tenantId = searchType.get('tenantId') || '';
  const mallId = searchType.get('id');

  const [applyNum, setApplyNum] = useState(0);
  const [formCode, setFormCode] = useState<null | string>(null);
  const [timing, setTiming] = useState<null | any>(null);
  const [btnItem, setBtnItem] = useState({} as mallResult);
  const [entryDetailsShow, setEntryDetailsShow] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [rejectWhy, setRejectWhy] = useState('');
  const [inExport, setInExport] = useState(false);
  const [ids, setIds] = useState<number[]>([]);
  const [mallParam, setMallParam] = useState<mallParams>({
    tenantId,
    verifyStatus: 20,
    pageNo: 1,
    pageSize: 10,
  });

  const stateName = (state: number, isEnable: number) => {
    if (!isEnable) return t('merchant_status_disable');
    switch (state) {
      case 10:
        return t('merchant_status_pending');
      case 20:
        return t('merchant_status_settled');
      case 30:
        return t('merchant_status_reject');
      default:
        return '';
    }
  };

  // const stateType = (state: number) => {
  //   switch (state) {
  //     case 1:
  //       return t('merchant_type_enterprise');
  //     case 3:
  //       return t('merchant_type_design');
  //     case 4:
  //       return t('merchant_type_decoration');
  //     case 5:
  //       return t('merchant_type_operator');
  //     case 50:
  //       return t('merchant_type_foreman');
  //     case 51:
  //       return t('merchant_type_leader');
  //     case 52:
  //       return t('merchant_type_designer');
  //     default:
  //       return '';
  //   }
  // };

  const through = (id: number, verifyStatus: number) => {
    putMallVerify({ id, verifyStatus, tenantId }).then(() => {
      refreshRef.current.refresh();
    });
  };

  // 通过拒绝入驻申请
  const btnS = (item: mallResult, isState: number) => {
    if (!isState) {
      setBtnItem(item);
      setIsModalOpen(true);
      return;
    }
    Modal.confirm({
      centered: true,
      icon: '',
      content: isState ? t('merchant_modal_passConfirm') : t('merchant_modal_rejectConfirm'),
      onOk: () => {
        message.success(t('merchant_modal_operateSuccess'));
        if (entryDetailsShow) setEntryDetailsShow(false);
        through(item.id, isState ? 20 : 30);
      },
    });
  };

  const startForbidden = (item: mallResult) => {
    if (item.isOperator || item.verifyStatus !== 20) return;
    const title = item.isEnable ? t('merchant_modal_disableTitle') : '';
    const content = item.isEnable
      ? t('merchant_modal_disableContent')
      : t('merchant_modal_enableConfirm');
    Modal.confirm({
      centered: true,
      title,
      icon: '',
      content,
      onOk: () => {
        putMallMchEnable({ id: item.id, tenantId }).then(() => {
          message.success(t('merchant_modal_operateSuccess'));
          refreshRef.current.refresh();
        });
      },
    });
  };

  // 区分是商户管理 还是 新入驻申请
  const title = useMemo(() => {
    const state = mallParam.verifyStatus;

    if (searchType.get('state') === '1') {
      if (state !== 20) setMallParam({ ...mallParam, verifyStatus: 20 });
      return [t('merchant_title')];
    }

    if (state !== 10) setMallParam({ ...mallParam, verifyStatus: 10 });
    return [
      {
        title: t('merchant_title'),
        to: `/merchant?state=1&id=${mallId}&tenantId=${tenantId}`,
      },
      t('merchant_title_newApply'),
    ];
  }, [searchType, t]); // eslint-disable-line

  const getMall = () => {
    getMallPageList({ ...mallParam, verifyStatus: 10 })
      .then((res) => {
        setApplyNum(res.pagination.count);
      })
      .catch(() => {
        clearInterval(timing);
      });
  };

  // const urlDownload = (url: string, fileName: string) => {
  //   if (!url) return;
  //   const a = document.createElement('a');
  //   a.href = url;
  //   a.download = fileName;
  //   a.click();
  //   message.success('导出成功，请稍等');
  // };

  const enableDisable = () => {
    if (!tenantId || inExport) return;
    setInExport(true);
    getMerchantDataExport({ tenantId }).then((res) => {
      // const URL = window.webkitURL || window.URL;
      // const url = URL.createObjectURL(res);
      downloadObject(res, t('merchant_export_filename')).then(() => {
        message.success(t('merchant_export_success'));
        setInExport(false);
      });
    });
  };

  const getApply = () => {
    const int = setInterval(() => {
      getMall();
    }, 5000);
    setTiming(int);
  };

  // 列表图片回显
  // const imgShow = (id: number) => {
  //   if (id === 1) {
  //     return ordinary;
  //   }
  //   if (id === 3) {
  //     return cityPartner;
  //   }
  //   return faith;
  // };

  const onModalOk = () => {
    if (!btnItem.id) return;
    // if (!rejectWhy) {
    //   message.error(t('merchant_modal_rejectError'));
    //   return;
    // }
    putMallVerify({ id: btnItem.id, verifyStatus: 30, tenantId, reason: rejectWhy }).then(() => {
      message.success(t('merchant_modal_operateSuccess'));
      if (entryDetailsShow) setEntryDetailsShow(false);
      setIsModalOpen(false);
      setRejectWhy('');
      refreshRef.current.refresh();
    });
  };

  const onChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setRejectWhy(e.target.value);
  };

  const exportQr = () => {
    if (!ids.length) {
      message.error(t('merchant_modal_exportError'));
      return;
    }
    postExportQrCodeBiz({ ids, tenantId }).then(() => {
      navigator(`/website/download?webSiteId=${mallId}&tenantId=${tenantId}`);
    });
  };

  const rejectModal = (
    <Modal
      title={t('merchant_modal_rejectTitle')}
      width={500}
      visible={isModalOpen}
      onCancel={() => {
        setIsModalOpen(false);
        setRejectWhy('');
      }}
      onOk={onModalOk}
    >
      <TextArea
        showCount
        value={rejectWhy}
        maxLength={100}
        style={{ height: 120, resize: 'none' }}
        onChange={onChange}
        placeholder={t('merchant_modal_rejectPlaceholder')}
      />
    </Modal>
  );

  // 列表字段
  const columns: ColumnsType<ItemType> = useMemo(() => {
    if (searchType.get('state') === '1') {
      return [
        {
          title: t('merchant_table_index'),
          width: '6%',
          align: 'center',
          render: (item, unknown, index) => (
            <div>
              {refreshRef.current.getPagination().pageSize *
                (refreshRef.current.getPagination().current - 1) +
                (index + 1)}
            </div>
          ),
        },
        {
          title: t('merchant_table_shopName_title'),
          width: '14%',
          align: 'center',
          render: (item) => (
            <div className={styles.shopName}>
              <Tooltip placement="top" title={item.shopName}>
                <div className={styles.text}>{item.shopName || '-'}</div>
              </Tooltip>
            </div>
          ),
        },
        {
          title: t('merchant_table_companyName_title'),
          width: '16%',
          align: 'center',
          render: (item) => (
            <div className={styles.enterCompanyName}>
              {item.enterCompanyName ? (
                <>
                  <Tooltip placement="top" title={item.enterCompanyName}>
                    <div className={styles.text}>{item.enterCompanyName}</div>
                  </Tooltip>
                  <Tooltip
                    placement="top"
                    title={
                      item.isAuth
                        ? t('merchant_table_auth_certified')
                        : t('merchant_table_auth_uncertified')
                    }
                  >
                    <img
                      className={styles.img}
                      src={
                        item.isAuth
                          ? 'https://img.huahuabiz.com/user_files/20221117/1668679048574569.png'
                          : 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/20221219/1671412658040161.png'
                      }
                      alt=""
                    />
                  </Tooltip>
                </>
              ) : (
                t('merchant_table_empty')
              )}
            </div>
          ),
        },
        {
          title: t('merchant_table_applicant_title'),
          width: '10%',
          align: 'center',
          render: (item) => <div className={styles.fz12}> {item.applyUserName}</div>,
        },
        {
          title: t('merchant_table_applyTime_title'),
          width: '12%',
          align: 'center',
          render: (item) => (
            <div className={styles.fz12}>
              <div>{item.verifyTime ? dayjs(item.verifyTime).format('YYYY-MM-DD') : '-'}</div>
            </div>
          ),
        },
        {
          title: t('merchant_table_status_title'),
          width: '8%',
          align: 'center',
          render: (item) => <div>{stateName(item.verifyStatus, item.isEnable)}</div>,
        },
        {
          title: t('merchant_table_goodsCount_title'),
          width: '10%',
          align: 'center',
          render: (item) => (
            <div>
              {item.verifyStatus !== 20 ? t('merchant_table_goodsCount_empty') : item.goodsNum}
            </div>
          ),
        },
        // {
        //   title: '商户等级',
        //   width: '10%',
        //   align: 'center',
        //   render: (item) => (
        //     <div>
        //       {item.categories &&
        //         item.categories.map((res: { id: number; name: string }) => (
        //           <img src={imgShow(res.id)} key={res.name} className={styles.categories} alt="" />
        //         ))}
        //     </div>
        //   ),
        // },
        {
          title: t('merchant_table_type_title'),
          width: '10%',
          align: 'center',
          render: (item) => <div>{item.typeName}</div>,
        },
        {
          title: t('merchant_table_operation_title'),
          width: '6%',
          align: 'center',
          render: (item) => (
            <div
              role="button"
              tabIndex={0}
              className={item.isOperator ? styles.Disable : styles.enableDisable}
              onClick={(e) => {
                e.stopPropagation();
                startForbidden(item);
              }}
            >
              <span
                style={{
                  color: item.isOperator ? '#888B98' : item.isEnable ? '#EA1C26' : '#008CFF',
                }}
              >
                {/* // eslint-disable-next-line no-nested-ternary */}
                {item.verifyStatus === 20
                  ? item.isEnable
                    ? t('merchant_table_operation_disable')
                    : t('merchant_table_operation_enable')
                  : t('merchant_table_goodsCount_empty')}
              </span>
            </div>
          ),
        },
      ];
    }
    return [
      {
        title: t('merchant_table_index'),
        width: '6%',
        align: 'center',
        render: (item, unknown, index) => (
          <div>
            {refreshRef.current.getPagination().pageSize *
              (refreshRef.current.getPagination().current - 1) +
              (index + 1)}
          </div>
        ),
      },
      {
        title: t('merchant_table_shopName'),
        width: '21%',
        align: 'center',
        render: (item) => (
          <div className={styles.shopName}>
            <Tooltip placement="top" title={item.shopName}>
              <div className={styles.text}>{item.shopName || '-'}</div>
            </Tooltip>
          </div>
        ),
      },
      {
        title: t('merchant_table_companyName'),
        width: '23%',
        align: 'center',
        render: (item) => (
          <div className={styles.enterCompanyName}>
            {item.enterCompanyName ? (
              <>
                <Tooltip placement="top" title={item.enterCompanyName}>
                  <div className={styles.text}>{item.enterCompanyName}</div>
                </Tooltip>
                <Tooltip
                  placement="top"
                  title={
                    item.isAuth
                      ? t('merchant_table_auth_certified')
                      : t('merchant_table_auth_uncertified')
                  }
                >
                  <img
                    className={styles.img}
                    src={
                      item.isAuth
                        ? 'https://img.huahuabiz.com/user_files/20221117/1668679048574569.png'
                        : 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/20221219/1671412658040161.png'
                    }
                    alt=""
                  />
                </Tooltip>
              </>
            ) : (
              t('merchant_table_empty')
            )}
          </div>
        ),
      },
      {
        title: t('merchant_table_applicant'),
        width: '10%',
        align: 'center',
        render: (item) => <div className={styles.fz12}> {item.applyUserName}</div>,
      },
      {
        title: t('merchant_table_applyTime'),
        width: '12%',
        align: 'center',
        render: (item) => (
          <div className={styles.fz12}>
            <div>
              {item.applyTime
                ? dayjs(item.applyTime).format(t('merchant_table_date_format'))
                : t('merchant_table_empty')}
            </div>
          </div>
        ),
      },
      {
        title: t('merchant_table_status'),
        width: '8%',
        align: 'center',
        render: (item) => <div>{stateName(item.verifyStatus, item.isEnable)}</div>,
      },
      // {
      //   title: '商户等级',
      //   width: '10%',
      //   align: 'center',
      //   render: (item) => (
      //     <div>
      //       {item.categories &&
      //         item.categories.map((res: { id: number; name: string }) => (
      //           <img src={imgShow(res.id)} key={res.name} className={styles.categories} alt="" />
      //         ))}
      //     </div>
      //   ),
      // },
      {
        title: t('merchant_table_type'),
        width: '10%',
        align: 'center',
        render: (item) => <div>{item.typeName}</div>,
      },
      {
        title: t('merchant_table_operation'),
        width: '6%',
        align: 'center',
        render: (item) =>
          item.verifyStatus === 10 ? (
            <div className={styles.btn}>
              <span
                className={styles.blue008CFF}
                tabIndex={0}
                role="button"
                onClick={(e) => {
                  e.stopPropagation();
                  btnS(item, 1);
                }}
              >
                {t('merchant_table_btn_pass')}
              </span>
              <span
                className={styles.redEA1C26}
                tabIndex={0}
                role="button"
                onClick={(e) => {
                  e.stopPropagation();
                  btnS(item, 0);
                }}
              >
                {t('merchant_table_btn_reject')}
              </span>
            </div>
          ) : (
            <div>-</div>
          ),
      },
    ];
  }, [searchType, t]); // eslint-disable-line

  const footer = useCallback(() => {
    if (searchType.get('state') === '1') {
      return (
        <span role="button" tabIndex={0} className={styles.batchItem} onClick={() => exportQr()}>
          {t('merchant_btn_exportShopCode')}
        </span>
      );
    }
    return '';
  }, [searchType, ids.length, t]); // eslint-disable-line

  const defaultPageSize = useMemo(() => {
    if (searchType.get('state') === '1') {
      return 9999;
    }
    return 10;
  }, [searchType]);

  useUnmount(() => {
    clearInterval(timing);
    refreshRef.current = null as unknown as ListInstance;
  });

  useEffect(() => {
    getMall();
    getApply();
  }, []); // eslint-disable-line

  return (
    <Context
      head={
        <Context.Head
          placeholder={t('merchant_search_placeholder')}
          title={title}
          extra={
            searchType.get('state') === '1' && (
              <Context.HeadTool className={styles.HeadTool}>
                <div className={styles.export} role="button" tabIndex={0} onClick={enableDisable}>
                  <img
                    src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023417/1681713687482972.png"
                    alt=""
                    className={styles.upload}
                  />
                  {inExport ? t('merchant_btn_exporting') : t('merchant_btn_export')}
                </div>
                <Button
                  size="small"
                  type="primary"
                  className={styles.apply}
                  onClick={() => {
                    navigator(`/merchant?state=2&id=${mallId}&tenantId=${tenantId}`);
                  }}
                >
                  {t('merchant_btn_newApply')}
                  {applyNum > 0 && (
                    <div className={styles.circular}>{applyNum > 98 ? '99+' : applyNum}</div>
                  )}
                </Button>
                <Button
                  size="small"
                  type="primary"
                  className={styles.ml12}
                  onClick={() => {
                    navigator(`/merchant/merchant-identity?id=${mallId}&tenantId=${tenantId}`);
                  }}
                >
                  {t('merchant_btn_backend')}
                </Button>
              </Context.HeadTool>
            )
          }
        />
      }
    >
      <List
        // @ts-ignore
        ref={refreshRef}
        key={defaultPageSize}
        footer={footer}
        onRow={(res) => ({
          onClick: () => {
            setFormCode(res.formCode && res.formCode.length ? res.formCode : null);
            setBtnItem(res);
            setEntryDetailsShow(true);
          },
        })}
        request={getMallPageList}
        params={mallParam}
        defaultPageSize={100}
        rowKey="id"
        columns={columns}
        rowSelection={{
          type: 'checkbox',
          columnWidth: 40,
          selectedRowKeys: ids,
          onChange(e) {
            // @ts-ignore
            setIds(e);
          },
        }}
      />
      {/* {entryDetailsShow && ( */}
      <EntryDetails
        visible={entryDetailsShow}
        formCode={formCode}
        btnItem={btnItem}
        isApply={searchType.get('state') === '2'}
        onClose={() => {
          refreshRef.current.refresh();
          setEntryDetailsShow(false);
        }}
        refusalToPass={(is: number) => {
          btnS(btnItem, is);
        }}
      />
      {/* )} */}
      {rejectModal}
    </Context>
  );
}

export default MerchantManagement;
