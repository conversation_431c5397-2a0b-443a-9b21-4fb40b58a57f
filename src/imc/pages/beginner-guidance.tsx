import { Context } from '@/components';
// import LinkJumpCard from '../components/link-jump-card';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import styles from './beginner-guidance.module.less';

interface dataParams {
  title: string;
  item: {
    title: string;
    body: string;
    btn: string;
    type: number;
    bgColor?: string;
  }[];
}

const color = [
  'linear-gradient(90deg, #D6E2FE 0%, rgba(214,226,254,0.50) 99%)',
  'linear-gradient(90deg, #D5E9F9 0%, rgba(213,233,249,0.50) 97%)',
  'linear-gradient(90deg, #E1D2F9 0%, rgba(225,210,249,0.50) 98%)',
  'linear-gradient(90deg, #E0F6F1 0%, rgba(215,236,233,0.40) 100%)',
];

function BeginnerGuidance() {
  const { t } = useTranslation();
  const jumpData = [
    {
      title: t('guidance_section_quick'),
      item: [
        {
          title: t('guidance_card_org_title'),
          body: t('guidance_card_org_desc'),
          btn: t('guidance_card_org_btn'),
          type: 1,
        },
        {
          title: t('guidance_card_goods_title'),
          body: t('guidance_card_goods_desc'),
          btn: t('guidance_card_goods_btn'),
          type: 2,
        },
        {
          title: t('guidance_card_shop_title'),
          body: t('guidance_card_shop_desc'),
          btn: t('guidance_card_shop_btn'),
          type: 3,
        },
      ],
    },
    {
      title: t('guidance_section_advanced'),
      item: [
        {
          title: t('guidance_card_sales_title'),
          body: t('guidance_card_sales_desc'),
          btn: t('guidance_card_sales_btn'),
          type: 4,
        },
        {
          title: t('guidance_card_purchase_title'),
          body: t('guidance_card_purchase_desc'),
          btn: t('guidance_card_purchase_btn'),
          type: 5,
        },
        {
          title: t('guidance_card_operation_title'),
          body: t('guidance_card_operation_desc'),
          btn: t('guidance_card_operation_btn'),
          type: 6,
        },
        {
          title: t('handbook_managementRule'),
          body: t('guidance_card_manage_desc'),
          btn: t('guidance_card_org_btn'),
          type: 7,
        },
      ],
    },
  ] as dataParams[];
  return (
    <Context
      theme={null}
      head={<Context.Head placeholder={t('guidance_placeholder')} title={t('guidance_title')} />}
    >
      <div className={styles.guidance}>
        <div className={styles.banner}>
          <div className={styles.iconImg}>
            <img
              className={styles.iconImg}
              src="https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023321/1679365298184875.png"
              alt=""
            />
          </div>
          <div className={styles.fingerPost}>{t('guidance_banner_title')}</div>
          <div className={styles.master}>{t('guidance_banner_subtitle')}</div>
        </div>
        {jumpData.map((item) => (
          <div className={styles.LinkJumpCard} key={item.title}>
            <div className={styles.title}>{item.title}</div>
            <div className={styles.cardFlex}>
              {item.item.map((card, index) => (
                <div
                  className={styles.card}
                  key={card.type}
                  style={{ background: card.bgColor || color[index] }}
                >
                  <div className={styles.cardTitle}>{card.title}</div>
                  <div className={styles.cardBody}>{card.body}</div>
                  <Link className={styles.cardJump} to={`/guidance/book?type=${card.type}`}>
                    {card.btn}
                  </Link>
                  {/* <div
                    className={styles.cardJump}
                    role="button"
                    tabIndex={card.type}
                    onClick={() => jump(card.type)}
                  >
                    {card.btn}
                  </div> */}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </Context>
  );
}

export default BeginnerGuidance;
