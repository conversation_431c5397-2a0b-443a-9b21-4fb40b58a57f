@import 'styles/mixins/mixins';

.handBook {
  display: flex;
  flex-direction: column;
  height: 100%;

  .title {
    color: #040919;
    font-size: 22px;
    font-weight: 600;
    padding: 28px;
  }

  .body {
    display: flex;
    .flex-row();

    white-space: nowrap;
  }

  .bodyLeft {
    display: inline-block;
    width: 27%;
    min-width: 248px;
    // overflow: auto;
    height: 100%;
    border-right: 1px solid #f5f6fa;
  }

  .bodyRight {
    display: inline-block;
    width: 73%;
    padding: 0 25px;
    // height: 100%;
  }

  .link {
    display: flex;
    padding: 25px 28px;
    border-bottom: 1px solid #f5f6fa;
    cursor: pointer;
  }

  .linkCopy {
    background: #fbfbfe;
  }

  .link:first-child {
    border-top: 1px solid #f5f6fa;
  }

  .serial {
    color: #fff;
    width: 20px;
    height: 20px;
    line-height: 20px;
    margin-right: 8px;
    text-align: center;
    background: #008cff;
    border-radius: 50%;
  }

  .serialCopy {
    background: #c6ccd8;
  }

  .cut {
    color: #008cff;
    font-size: 18px;
    font-weight: normal;
  }

  .cutCopy {
    color: #040919;
  }

  .establish {
    color: #040919;
    font-size: 32px;
    font-weight: 600;
    margin-top: 32px;
  }

  .speediness {
    color: #040919;
    font-size: 18px;
    margin-top: 12px;
    margin-bottom: 30px;
  }

  .btn {
    color: #fff;
    width: fit-content;
    height: 38px;
    line-height: 38px;
    padding: 0 41px;
    text-align: center;
    border-radius: 10px;
    background: linear-gradient(255deg, #00c6ff 0%, #008cff 100%);
    cursor: pointer;
  }
}
