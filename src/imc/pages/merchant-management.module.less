.enterCompanyName {
  display: flex;
  align-items: center;
  justify-content: center;

  .text {
    // max-width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .img {
    margin-left: 4px;
  }
}

.timeText {
  color: #888b98;
  display: inline-block;
  width: 30px;
}

.fz12 {
  font-size: 14px;
}

.categories {
  margin-top: 4px;
}

.btn {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;

  .blue008CFF {
    color: #008cff;
  }

  .redEA1C26 {
    color: #ea1c26;
  }
}

.apply {
  position: relative;

  .circular {
    color: #fff;
    font-size: 12px;
    width: 24px;
    height: 24px;
    line-height: 24px;
    position: absolute;
    top: -7px;
    right: -3px;
    transform: scale(0.7);
    text-align: center;
    border-radius: 50%;
    background-color: #ff5923;
  }
}

.ml12 {
  margin-left: 12px;
}

.shopName {
  .text {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.enableDisable {
  cursor: pointer;
}

.Disable {
  cursor: not-allowed;
  color: #888b98;
}

.batchItem {
  color: #178fff;
  cursor: pointer;
}

.HeadTool {
  display: flex;

  .export {
    color: #888b98;
    font-size: 12px;
    display: flex;
    height: 28px;
    line-height: 28px;
    margin-right: 12px;
    padding: 0 20px;
    justify-content: center;
    align-items: center;
    border-radius: 10px;
    background: #f3f3f3;
    cursor: pointer;
  }

  .upload {
    width: 16px;
    height: 16px;
    margin-right: 2px;
  }
}
