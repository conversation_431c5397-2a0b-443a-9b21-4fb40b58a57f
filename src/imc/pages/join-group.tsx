import { Row, Col, Empty } from 'antd';
import { useState, useEffect } from 'react';
import { Modal, Context, Icon } from '@/components';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';
import styles from './join-group.module.less';
import JoinGroupList from '../components/join-group-list';

export interface ItemInterface {
  avatar: string;
  title: string;
  message: string;
  state: number;
}

// 模拟接口
function getData(): Promise<ItemInterface[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      const list: ItemInterface[] = [
        {
          avatar: 'https://img.huahuabiz.com/static/default-avatar/10.png',
          title: i18next.t('joinGroup_mock_title'),
          message: i18next.t('joinGroup_mock_message'),
          state: 1,
        },
      ];
      resolve(list);
    }, 200);
  });
}

function JoinGroup() {
  const { t } = useTranslation();
  const [list, setList] = useState([] as ItemInterface[]);

  useEffect(() => {
    (async () => {
      const result = await getData();
      setList(result);
    })();
  }, []);

  const onEmpty = () => {
    Modal.confirm({
      title: t('joinGroup_modal_title'),
      content: t('joinGroup_modal_confirm'),
      closable: false,
      onOk: () => {
        setList([]);
      },
    });
  };

  return (
    <Context>
      <Row justify="space-between" align="middle" className={styles.header}>
        <Col span={12}>
          <span className={styles.title}>{t('joinGroup_title')}</span>
        </Col>
        <Col span={12} className="text-right">
          <span aria-hidden className="pointer" onClick={onEmpty}>
            <Icon name="trash" size="18px" color="#888B98" />
            <span className={styles.emptyBtnText}>{t('joinGroup_btn_clear')}</span>
          </span>
        </Col>
      </Row>
      {list.length ? <JoinGroupList list={list} /> : <Empty />}
    </Context>
  );
}

export default JoinGroup;
