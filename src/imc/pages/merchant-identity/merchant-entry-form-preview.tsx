import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';
import PreviewView from '@@/form-engine/components/preview-view';
import { useTranslation } from 'react-i18next';
import SelectCompanyAndShop from './compoennts/select-company-and-shop/select-company-and-shop';
import Contacts from './compoennts/contacts/contacts';

function AccessPreview() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams({ tenantId: '', id: '' });
  const tenantId = searchParams.get('tenantId') || '';
  const mallId = searchParams.get('id');
  const selectCompanyAndShop = useMemo(() => <SelectCompanyAndShop />, []);
  const contacts = useMemo(() => <Contacts />, []);
  return (
    <PreviewView
      getTitle={(res) => [
        {
          title: res.title || t('preview_title_default'),
          to: `/merchant/merchant-identity/merchant-entry-form?id=${mallId}&tenantId=${tenantId}`,
        },
        t('preview_title_page'),
      ]}
      components={{
        companyAndShop: () => selectCompanyAndShop,
        contacts: () => contacts,
      }}
    />
  );
}

AccessPreview.displayName = 'AccessPreview';

export default AccessPreview;
