import React from 'react';
import { Form, Input } from 'antd';
import { useTranslation } from 'react-i18next';

function Contacts() {
  const { t } = useTranslation();

  return (
    <div>
      <Form.Item
        label={t('contacts_label_name')}
        required
        labelCol={{ span: 24 }}
        style={{ marginBottom: '24px' }}
      >
        <Input placeholder={t('contacts_placeholder_name')} bordered={false} />
      </Form.Item>
      <Form.Item label={t('contacts_label_phone')} required labelCol={{ span: 24 }}>
        <Input placeholder={t('contacts_placeholder_phone')} bordered={false} />
      </Form.Item>
    </div>
  );
}

export default Contacts;
