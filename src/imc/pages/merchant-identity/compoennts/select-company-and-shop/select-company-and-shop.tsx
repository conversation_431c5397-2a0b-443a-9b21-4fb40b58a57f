import React from 'react';
import { Form, Select } from 'antd';
import { useTranslation } from 'react-i18next';

function SelectCompanyAndShop() {
  const { t } = useTranslation();

  return (
    <div>
      <Form.Item
        label={t('companyShop_label_company')}
        required
        labelCol={{ span: 24 }}
        style={{ marginBottom: '24px' }}
      >
        <Select placeholder={t('companyShop_placeholder_company')} bordered={false} />
      </Form.Item>
      <Form.Item label={t('companyShop_label_shop')} required labelCol={{ span: 24 }}>
        <Select placeholder={t('companyShop_placeholder_shop')} bordered={false} />
      </Form.Item>
    </div>
  );
}

export default SelectCompanyAndShop;
