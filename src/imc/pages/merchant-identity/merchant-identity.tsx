import { Context, List, DropdownSelect, Icon } from '@/components';
import { identityListPage, Identity } from '@/apis';
import { ColumnsType } from 'antd/es/table';
import { ItemType } from '@/apis/company-admin/upload-members-excel';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useMemo, useRef } from 'react';
import { Button } from 'antd';
import { useTranslation } from 'react-i18next';
import { setFormData } from './form-stroe';

import styles from './merchant-identity.module.less';

type StatusMapType = Record<string, string>;

function MerchantManagement() {
  const { t } = useTranslation();
  const [searchType] = useSearchParams();
  const navigator = useNavigate();
  const tenantId = searchType.get('tenantId') || '';
  const mallId = searchType.get('id');
  const getIdentityListParams = useRef({
    pageNo: 1,
    pageSize: 20,
    tenantId,
  });
  const statusMap: StatusMapType = {
    10: t('identity_list_status_normal'),
  };
  const title = [
    {
      title: t('identity_list_title_manage'),
      to: `/merchant?state=1&id=${mallId}&tenantId=${tenantId}`,
    },
    t('identity_list_title_backend'),
  ];

  // 编辑
  const editIdentity = (item: Identity) => {
    setFormData({
      id: item.id,
      type: item.type,
      identityName: item.identityName,
      description: item.description,
      formCode: item.formCode,
    });
    navigator(
      `/merchant/merchant-identity/create-identity?state=1&id=${mallId}&tenantId=${tenantId}`
    );
  };

  // 选择操作
  const selectOpt = (key: string, item: Identity) => {
    if (key === '1') {
      editIdentity(item);
    }
    if (key === '2') {
      setFormData({
        id: item.id,
        type: item.type,
        identityName: item.identityName,
        description: item.description,
        formCode: item.formCode,
      });
      navigator(
        `/merchant/merchant-identity/merchant-entry-form?id=${mallId}&tenantId=${tenantId}&formCode=${item.formCode}`
      );
    }
  };

  // 列表字段
  const columns: ColumnsType<ItemType> = useMemo(
    () => [
      {
        title: t('identity_list_column_name'),
        width: '20%',
        align: 'center',
        render: (item) => <div>{item.identityName}</div>,
      },
      {
        title: t('identity_list_column_formType'),
        width: '20%',
        align: 'center',
        render: () => <div>{t('identity_list_type_custom')}</div>,
      },
      {
        title: t('identity_list_column_type'),
        width: '20%',
        align: 'center',
        render: (item) => (
          <div>
            {item.type === 1 ? t('identity_list_type_company') : t('identity_list_type_personal')}
          </div>
        ),
      },
      {
        title: t('identity_list_column_status'),
        width: '20%',
        align: 'center',
        render: (item) => <div className={styles.fz12}> {statusMap[item.status]}</div>,
      },
      {
        title: t('identity_list_column_operation'),
        width: '20%',
        align: 'center',
        render: (item) => (
          <div>
            <DropdownSelect
              options={[
                {
                  label: t('identity_list_dropdown_editIdentity'),
                  key: '1',
                },
                {
                  label: t('identity_list_dropdown_editForm'),
                  key: '2',
                },
              ]}
              dropdownClass={styles.optBox}
              select={(key) => {
                selectOpt(`${key}`, item);
              }}
            >
              <Icon className={styles.icon} name="zu13366" size={20} />
            </DropdownSelect>
          </div>
        ),
      },
    ],
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [t]
  );

  const getIdentityList = () => identityListPage(getIdentityListParams.current);

  return (
    <Context
      head={
        <Context.Head
          title={title}
          extra={
            <Context.HeadTool className={styles.HeadTool}>
              <Button
                size="small"
                type="primary"
                className={styles.ml12}
                onClick={() => {
                  setFormData(null);
                  navigator(
                    `/merchant/merchant-identity/create-identity?state=1&id=${mallId}&tenantId=${tenantId}`
                  );
                }}
              >
                {t('identity_list_btn_create')}
              </Button>
            </Context.HeadTool>
          }
        />
      }
    >
      <List
        onRow={() => ({
          onClick: () => {},
        })}
        request={getIdentityList}
        params={getIdentityListParams}
        rowKey="id"
        columns={columns}
      />
    </Context>
  );
}

export default MerchantManagement;
