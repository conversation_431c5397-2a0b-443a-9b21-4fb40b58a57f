import { Context } from '@/components';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button, Form, Input, Radio, FormInstance, message } from 'antd';
import { updateIdentity, checkIdentityName } from '@/apis';
import { useState, useRef } from 'react';
import { useMount } from 'ahooks';
import { useTranslation } from 'react-i18next';
import { setFormData, getFormData } from './form-stroe';
import styles from './create-identity.module.less';

function CreateIdentity() {
  const { t } = useTranslation();
  const navigator = useNavigate();
  const [searchType] = useSearchParams();
  const tenantId = searchType.get('tenantId') || '';
  const mallId = searchType.get('id');
  const [params, setParams] = useState({
    type: 1,
    identityName: '',
    description: '',
  });
  const form = useRef(null as unknown as FormInstance);
  const isEdit = !!getFormData()?.formCode;

  const title = [
    {
      title: t('identity_title_manage'),
      to: `/merchant?state=1&id=${mallId}&tenantId=${tenantId}`,
    },
    {
      title: t('identity_title_backend'),
      to: `/merchant/merchant-identity?state=1&id=${mallId}&tenantId=${tenantId}`,
    },
    t('identity_title_setting'),
  ];

  const submitEdit = () => {
    updateIdentity({
      id: getFormData()?.id as number,
      identityName: params.identityName,
      description: params.description,
      tenantId,
    }).then(() => {
      message.success(t('identity_message_success'));
      navigator(`/merchant/merchant-identity?state=1&id=${mallId}&tenantId=${tenantId}`);
    });
  };

  useMount(() => {
    const data = getFormData();
    if (data) {
      setParams(data);
      form.current.setFieldsValue(data);
    }
  });

  return (
    <Context head={<Context.Head title={title} />}>
      <div className={styles.container}>
        <div className={styles.formBox}>
          <Form layout="vertical" autoComplete="off" ref={form} initialValues={params}>
            <Form.Item
              name="type"
              label={t('identity_form_type')}
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Radio.Group
                disabled={!!getFormData()?.id}
                onChange={(e) => {
                  setParams({ ...params, type: e.target.value });
                }}
                defaultValue={params.type}
              >
                <Radio value={1}>{t('identity_form_type_company')}</Radio>
                <Radio value={2}>{t('identity_form_type_personal')}</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name="identityName"
              label={t('identity_form_name')}
              rules={[
                {
                  required: true,
                  max: 10,
                },
              ]}
            >
              <Input
                placeholder={t('identity_form_name_placeholder')}
                defaultValue={params.identityName}
                onChange={(e) => {
                  setParams({ ...params, identityName: e.target.value });
                }}
              />
            </Form.Item>
            <Form.Item
              name="description"
              label={t('identity_form_desc')}
              rules={[
                {
                  max: 30,
                },
              ]}
            >
              <Input
                placeholder={t('identity_form_desc_placeholder')}
                defaultValue={params.description}
                onChange={(e) => {
                  setParams({ ...params, description: e.target.value });
                }}
              />
            </Form.Item>
          </Form>
        </div>
        <div className={styles.handelBox}>
          <Button
            className={styles.btn}
            onClick={() => {
              navigator(-1);
            }}
          >
            {t('identity_btn_cancel')}
          </Button>
          {isEdit ? (
            <Button
              className={styles.btn}
              type="primary"
              htmlType="submit"
              onClick={() => {
                form.current.validateFields().then(() => {
                  submitEdit();
                });
              }}
            >
              {t('identity_btn_save')}
            </Button>
          ) : (
            <Button
              className={styles.btn}
              type="primary"
              htmlType="submit"
              onClick={() => {
                form.current.validateFields().then((res) => {
                  checkIdentityName({
                    identityName: params.identityName,
                    tenantId,
                  }).then(() => {
                    setFormData(res);
                    navigator(
                      `/merchant/merchant-identity/merchant-entry-form?id=${mallId}&tenantId=${tenantId}`
                    );
                  });
                });
              }}
            >
              {t('identity_btn_next')}
            </Button>
          )}
        </div>
      </div>
    </Context>
  );
}

export default CreateIdentity;
