import React, { useMemo, useRef, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Context, Modal } from '@/components';
import { Button, message } from 'antd';
import {
  addIdentity,
  AddIdentityParams,
  getFormTemplateComponents,
  updateFormTemplateComponents,
} from '@/apis';
import { FormGenerator, FormGeneratorInstance } from '@@/form-engine/containers';
import { Component } from '@@/form-engine/containers/form-generator/interface';
import { useMount } from 'ahooks';
import { useTranslation } from 'react-i18next';
import { setFormData, getFormData } from './form-stroe';
import SelectCompanyAndShop from './compoennts/select-company-and-shop/select-company-and-shop';
import Contacts from './compoennts/contacts/contacts';
import styles from './merchant-entry-form.module.less';

function MerchantEntryForm() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams({ formCode: '', name: '', tenantId: '', id: '' });
  const tenantId = searchParams.get('tenantId') || '';
  const mallId = searchParams.get('id');
  const ref = useRef<FormGeneratorInstance>(null);
  const [formCode] = useState(searchParams.get('formCode') || '');
  const [loading, setLoading] = useState(false);
  const [components, setComponents] = useState(null as unknown as Component[]);
  const formType = getFormData()?.type;

  // 取消
  const onCancel = () => {
    Modal.confirm({
      icon: null,
      okText: t('entryForm_modal_continue'),
      cancelText: t('entryForm_modal_leave'),
      title: t('entryForm_modal_title'),
      content: t('entryForm_modal_content'),
      centered: true,
      onCancel: () => {
        navigate(-1);
      },
    });
  };

  const head = (
    <Context.Head
      title={[
        {
          title: t('entryForm_title_manage'),
          to: `/merchant?state=1&id=${mallId}&tenantId=${tenantId}`,
        },
        {
          title: t('entryForm_title_backend'),
          to: `/merchant/merchant-identity?state=1&id=${mallId}&tenantId=${tenantId}`,
        },
        t('entryForm_title_design'),
      ]}
    />
  );

  const selectCompanyAndShop = useMemo(() => <SelectCompanyAndShop />, []);
  const contacts = useMemo(() => <Contacts />, []);

  const filterCustomComponents = (cps: Omit<Component, 'tempId'>[]) =>
    cps.filter(
      (item) => item.type !== 'companyAndShop' && item.type !== 'contacts' && item.type !== 'empty'
    );

  const add = () => {
    const list = ref.current?.getList();
    const data = getFormData();
    if (data) {
      const params = {
        ...data,
        components: filterCustomComponents(list as Omit<Component, 'tempId'>[]),
        tenantId,
      };
      setLoading(true);
      addIdentity(params as AddIdentityParams)
        .then(() => {
          message.success(t('entryForm_message_success'));
          setFormData(null);
          navigate(`/merchant/merchant-identity?state=1&id=${mallId}&tenantId=${tenantId}`);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const edit = () => {
    const list = ref.current?.getList();
    if (formCode && list && list?.length > 0) {
      setLoading(true);
      updateFormTemplateComponents({
        formCode,
        components: filterCustomComponents(list as Omit<Component, 'tempId'>[]),
        tenantId,
      })
        .then(() => {
          message.success(t('entryForm_message_success'));
          setFormData(null);
          navigate(`/merchant/merchant-identity?state=1&id=${mallId}&tenantId=${tenantId}`);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  };

  const submit = () => {
    if (formCode) {
      edit();
    } else {
      add();
    }
  };

  useMount(() => {
    const data = getFormData();
    if (!data) {
      navigate(
        `/merchant/merchant-identity/create-identity?state=1&id=${mallId}&tenantId=${tenantId}`
      );
    }
    if (formCode) {
      getFormTemplateComponents(formCode).then((res) => {
        const list = res.components.map((item) => ({
          tempId: item.id as number,
          attr: item.attr,
          type: item.type,
        }));

        if (formType === 1) {
          setComponents([
            {
              tempId: 1,
              attr: {},
              hideHandle: true,
              disable: true,
              type: 'companyAndShop',
            },
            {
              tempId: 2,
              attr: {},
              hideHandle: true,
              disable: true,
              type: 'contacts',
            },
            ...list,
          ]);
        } else {
          setComponents([
            {
              tempId: 2,
              attr: {},
              hideHandle: true,
              disable: true,
              type: 'contacts',
            },
            ...list,
          ]);
        }
      });
    } else if (formType === 1) {
      setComponents([
        {
          tempId: 1,
          attr: {},
          hideHandle: true,
          disable: true,
          type: 'companyAndShop',
        },
        {
          tempId: 2,
          attr: {},
          hideHandle: true,
          disable: true,
          type: 'contacts',
        },
      ]);
    } else {
      setComponents([
        {
          tempId: 2,
          attr: {},
          hideHandle: true,
          disable: true,
          type: 'contacts',
        },
      ]);
    }
  });
  return (
    <Context head={head} theme={null}>
      <div className={styles.container}>
        <div className={styles.body}>
          {components && (
            <FormGenerator
              ref={ref}
              defaultComponents={components}
              customComponents={
                formType === 1
                  ? {
                      companyAndShop: () => selectCompanyAndShop,
                      contacts: () => contacts,
                    }
                  : {
                      contacts: () => contacts,
                    }
              }
              // preview={{
              //   getURL: () =>
              //     `/merchant/merchant-identity/merchant-entry-form-preview?id=${mallId}&tenantId=${tenantId}`,
              // }}
            />
          )}
        </div>
        <div className={styles.footer}>
          <Button type="default" size="middle" className="mr-3" onClick={onCancel}>
            {t('entryForm_btn_cancel')}
          </Button>
          <Button
            type="primary"
            size="middle"
            disabled={loading}
            onClick={submit}
            loading={loading}
          >
            {t('entryForm_btn_save')}
          </Button>
        </div>
      </div>
    </Context>
  );
}

export default MerchantEntryForm;
