import { getDistributionCompanyId } from '@/apis';
import { Context } from '@/components';
import { createTeam } from '@/src/home/<USER>';
import { user } from '@/store';
import { message } from 'antd';
import classNames from 'classnames';
import localforage from 'localforage';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styles from './hand-book.module.less';

interface constructionResult {
  companyId: number;
  id: number;
  pagination: { count: number; total: number };
  tenantId: string;
  tenantName: string;
}

function HandBook() {
  const [routePar] = useSearchParams();
  const [cutType, setCutType] = useState(0);
  const navigate = useNavigate();
  const type = routePar.get('type');
  const { companyId, companyName } = user;
  const memberId = user.member.id;
  const { t } = useTranslation();

  const [constructionList, setConstructionList] = useState<constructionResult | null>();

  const establishmentCheck = (url: string) => {
    if (constructionList) {
      const { tenantId, id } = constructionList;
      return `${url}&tenantId=${tenantId}&webSiteId=${id}`;
    }
    return '/build/manage?action=createSite';
  };

  const title = useMemo(() => {
    const getLink = () => {
      switch (routePar.get('type')) {
        case '1':
          return t('handbook_digitalOrg');
        case '2':
          return t('handbook_uploadGoods');
        case '3':
          return t('handbook_createShop');
        case '4':
          return t('handbook_salesRule');
        case '5':
          return t('handbook_purchaseRule');
        case '6':
          return t('handbook_operationRule');
        case '7':
          return t('handbook_managementRule');
        default:
          return t('handbook_unset');
      }
    };
    return [{ title: t('handbook_title'), to: '/guidance' }, getLink()];
  }, [routePar, t]);

  const getTitle = () => {
    switch (type) {
      case '1':
        return t('handbook_tip_team');
      case '2':
        return t('handbook_tip_goods');
      case '3':
        return t('handbook_tip_team');
      case '4':
        return t('handbook_tip_sales');
      case '5':
        return t('handbook_tip_purchase');
      case '6':
        return t('handbook_tip_operation');
      case '7':
        return t('handbook_tip_management');
      default:
        return t('handbook_unset');
    }
  };

  const cutData = useMemo(() => {
    switch (routePar.get('type')) {
      case '1':
        return [
          t('handbook_step_createOrg'),
          t('handbook_step_companyAuth'),
          t('handbook_step_inviteMember'),
          t('handbook_step_assignPermission'),
        ];
      case '2':
        return [t('handbook_step_customCategory'), t('handbook_step_uploadGoods')];
      case '3':
        return [
          t('handbook_step_createSite'),
          t('handbook_step_applyMiniapp'),
          t('handbook_step_authMiniapp'),
          t('handbook_step_connectPay'),
          t('handbook_step_miniappInfo'),
        ];
      case '4':
        return [
          t('handbook_step_customerRule'),
          t('handbook_step_serviceRule'),
          t('handbook_step_priceRule'),
          t('handbook_step_creditRule'),
          t('handbook_step_distributionRule'),
        ];
      case '5':
        return [
          t('handbook_step_supplierRule'),
          t('handbook_step_quoteRule'),
          t('handbook_step_tenderRule'),
        ];
      case '6':
        return [
          t('handbook_step_goodsRule'),
          t('handbook_step_orderRule'),
          t('handbook_step_warehouseRule'),
          t('handbook_step_financeRule'),
        ];
      case '7':
        return [
          t('handbook_step_orgStructure'),
          t('handbook_step_rolePermission'),
          t('handbook_step_workflow'),
          t('handbook_step_attendance'),
          t('handbook_step_hr'),
          t('handbook_step_community'),
        ];
      default:
        return [];
    }
  }, [routePar, t]);

  const videoPhotoData = useMemo(() => {
    /**
     * type 区分是图片还是视频
     * url 图片视频链接
     * title 标题
     * body 内容text
     * btnText 按钮文字
     * go 跳转url 或 特殊处理
     * personalIdentityIsDisable 个人组织下是否提示切换组织
     */
    switch (routePar.get('type')) {
      case '1':
        return [
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356439295991.png',
            title: t('handbook_step_createOrg'),
            body: t('handbook_text_createTeam'),
            btnText: t('handbook_btn_create'),
            go: 'teamAndAdvisors',
            personalIdentityIsDisable: false,
          },
          {
            type: 'video',
            url: 'https://img.huahuabiz.com/user_files/2023413/1681372571330196.mp4',
            title: t('handbook_step_companyAuth'),
            body: t('handbook_text_companyAuth'),
            btnText: t('handbook_btn_auth'),
            go: '/user/company/auth?addTeam=1',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356482151154.png',
            title: t('handbook_step_inviteMember'),
            body: t('handbook_text_inviteMember'),
            btnText: t('handbook_btn_invite'),
            go: 'addressBook',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://img.huahuabiz.com/user_files/2023423/1682240321652197.png',
            title: t('handbook_step_assignPermission'),
            body: t('handbook_text_permission'),
            btnText: t('handbook_btn_set'),
            go: '/user/permission/manage',
            personalIdentityIsDisable: true,
          },
          // {
          //   type: 'video',
          //   url: 'https://img.huahuabiz.com/test/2023320/1679300031658703.mp4',
          //   title: '探索更多',
          //   body: '来帮助中心查看更多',
          //   btnText: '立即前往',
          //   go: 'exploreMore',
          //   personalIdentityIsDisable: false,
          // },
        ];
      case '2':
        return [
          {
            type: 'video',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/test/1681366307636102632/1681120997322602.mp4',
            title: t('handbook_step_customCategory'),
            body: t('handbook_text_category'),
            btnText: t('handbook_btn_go'),
            go: '/goods/custom/category',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356615502995.png',
            title: t('handbook_step_uploadGoods'),
            body: t('handbook_text_goods'),
            btnText: t('handbook_btn_go'),
            go: '/shop/goods/list',
            personalIdentityIsDisable: true,
          },
        ];
      case '3':
        return [
          {
            type: 'video',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/test/202348/1680923219165918.mp4',
            title: t('handbook_step_createSite'),
            body: t('handbook_text_site'),
            btnText: t('handbook_btn_create'),
            go: '/build/manage?action=createSite',
            personalIdentityIsDisable: true,
          },
          {
            type: 'video',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/test/1680923516008408427/%E5%88%9B%E5%BB%BA%E5%B9%B6%E5%8F%91%E5%B8%83%E4%B8%B4%E6%97%B6%E5%B0%8F%E7%A8%8B%E5%BA%8F.mp4',
            title: t('handbook_step_applyMiniapp'),
            body: t('handbook_text_miniapp'),
            btnText: t('handbook_btn_apply'),
            go: establishmentCheck(`/wx-applets/mp?type=2`),
            personalIdentityIsDisable: true,
          },
          {
            type: 'video',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/test/1680923656119401161/%E5%A6%82%E4%BD%95%E8%BF%9B%E8%A1%8C%E5%B0%8F%E7%A8%8B%E5%BA%8F%E8%BD%AC%E6%AD%A3.mp4',
            title: t('handbook_step_authMiniapp'),
            body: t('handbook_text_miniappAuth'),
            btnText: t('handbook_btn_auth'),
            go: establishmentCheck(`/wx-applets/mp?type=2`),
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://img.huahuabiz.com/user_files/2023413/1681365695400706.jpg',
            title: t('handbook_step_connectPay'),
            body: t('handbook_text_pay'),
            btnText: t('handbook_btn_set'),
            go: establishmentCheck(`/wx-applets/mp?type=2`),
            personalIdentityIsDisable: true,
          },
          {
            type: 'video',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/test/1680923682795974154/%E6%8F%90%E4%BA%A4%E5%AE%A1%E6%A0%B8%EF%BC%8C%E5%8F%91%E5%B8%83%E5%BE%AE%E4%BF%A1%E5%B0%8F%E7%A8%8B%E5%BA%8F.mp4',
            title: t('handbook_step_miniappInfo'),
            body: t('handbook_text_miniappInfo'),
            btnText: t('handbook_btn_set'),
            go: establishmentCheck(`/wx-applets/mp/base?`),
            personalIdentityIsDisable: true,
          },
        ];
      case '4':
        return [
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356683769726.png',
            title: t('handbook_step_customerRule'),
            body: t('handbook_text_crm'),
            btnText: t('handbook_btn_set'),
            go: '/admin/crm/settings/role',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356701875113.png',
            title: t('handbook_step_serviceRule'),
            body: t('handbook_text_service'),
            btnText: t('handbook_btn_set'),
            go: '/user/service/list',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/168135671810214.png',
            title: t('handbook_step_priceRule'),
            body: t('handbook_text_price'),
            btnText: t('handbook_btn_set'),
            go: '/shop/circle/list',
            personalIdentityIsDisable: true,
          },
          {
            type: 'video',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/test/1681366178952576481/1681267715392.mp4',
            title: t('handbook_step_creditRule'),
            body: t('handbook_text_credit'),
            btnText: t('handbook_btn_set'),
            go: '/finance/credit/control/list',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356751455507.png',
            title: t('handbook_step_distribution'),
            body: t('handbook_text_distribution'),
            btnText: t('handbook_btn_enable'),
            go: '/distribution/home',
            personalIdentityIsDisable: true,
          },
        ];
      case '5':
        return [
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356810953120.png',
            title: t('handbook_step_supplierRule'),
            body: t('handbook_text_supplier'),
            btnText: t('handbook_btn_config'),
            go: '/admin/srm/settings/role',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356827825441.png',
            title: t('handbook_step_quoteRule'),
            body: t('handbook_text_quote'),
            btnText: t('handbook_btn_config'),
            go: 'priceQuotation',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356851761216.png',
            title: t('handbook_step_tenderRule'),
            body: t('handbook_text_tender'),
            btnText: t('handbook_btn_auth'),
            go: '/bidding/admin/bidding/type',
            personalIdentityIsDisable: true,
          },
        ];
      case '6':
        return [
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681356894789776.png',
            title: t('handbook_step_goodsRule'),
            body: t('handbook_text_goodsManage'),
            btnText: t('handbook_btn_go'),
            go: '/shop/goods/list',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://img.huahuabiz.com/user_files/2023413/1681366812414334.png',
            title: t('handbook_step_orderRule'),
            body: t('handbook_text_order'),
            btnText: t('handbook_btn_go'),
            go: '/indent',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://img.huahuabiz.com/user_files/2023413/168136690037237.png',
            title: t('handbook_step_warehouseRule'),
            body: t('handbook_text_warehouse'),
            btnText: t('handbook_btn_go'),
            go: '/stock',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://img.huahuabiz.com/user_files/2023413/1681366937845410.png',
            title: t('handbook_step_financeRule'),
            body: t('handbook_text_finance'),
            btnText: t('handbook_btn_go'),
            go: '/finance/gathering/orderPaymen',
            personalIdentityIsDisable: true,
          },
        ];
      case '7':
        return [
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681365724759588.png',
            title: t('handbook_step_orgStructure'),
            body: t('handbook_text_org'),
            btnText: t('handbook_btn_config'),
            go: `/company-admin/worker-manage/${companyId}/${memberId}`,
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://img.huahuabiz.com/user_files/2023423/1682240321652197.png',
            title: t('handbook_step_rolePermission'),
            body: t('handbook_text_role'),
            btnText: t('handbook_btn_config'),
            go: '/user/permission/manage',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681365759878565.png',
            title: t('handbook_step_workflow'),
            body: t('handbook_text_workflow'),
            btnText: t('handbook_btn_config'),
            go: '/admin/workflow',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://img.huahuabiz.com/user_files/2023413/1681372624223453.jpg',
            title: t('handbook_step_attendance'),
            body: t('handbook_text_attendance'),
            btnText: t('handbook_btn_config'),
            go: '/user/attendance/group/index',
            personalIdentityIsDisable: true,
          },
          {
            type: 'photo',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/user_files/2023413/1681357067223972.png',
            title: t('handbook_step_hr'),
            body: t('handbook_text_hr'),
            btnText: t('handbook_btn_config'),
            go: '/user/hrm/roster',
            personalIdentityIsDisable: true,
          },
          // {
          //   type: 'photo',
          //   url: 'https://t7.baidu.com/it/u=1819248061,230866778&fm=193&f=GIF',
          //   title: '会议室',
          //   body: '会议室线上化，预约状态清晰可见',
          //   btnText: '立即配置',
          //   go: '/user/calendar/meetingroom?type=2',
          //   personalIdentityIsDisable: true,
          // },
          {
            type: 'video',
            url: 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/test/1681807930413781456/%E7%A4%BE%E5%8C%BA0411~1.mp4',
            title: t('handbook_step_community'),
            body: t('handbook_text_community'),
            btnText: t('handbook_btn_config'),
            go: constructionList
              ? `/admin/subject/topic/topic_list?tenantId=${constructionList.tenantId}`
              : '/build/manage',
            personalIdentityIsDisable: true,
          },
        ];
      default:
        return [];
    }
  }, [routePar, constructionList, t]); // eslint-disable-line

  const gotoUrl = (url: string, disable: boolean) => {
    if (companyName === t('handbook_account_personal') && disable) {
      message.info(t('handbook_message_switchTeam'));
      return;
    }
    switch (url) {
      case 'teamAndAdvisors':
        createTeam({
          onSuccess: () => {
            navigate(import.meta.env.BIZ_APP_HOME_URL);
          },
          goCompanyAuth: () => {
            navigate('/user/company/auth?addTeam=1');
          },
        });
        break;
      case 'exploreMore':
        window.open('http://help.huahuabiz.com/');
        break;
      case 'addressBook':
        localforage.setItem('workerManageOpen', 'addMemberDrawer');
        navigate(`/company-admin/worker-manage/${companyId}/${memberId}`);
        break;
      case 'priceQuotation':
        message.info(t('handbook_message_miniProgram'));
        break;
      default:
        navigate(url);
    }
  };

  useEffect(() => {
    if (type === '3' || type === '7') {
      getDistributionCompanyId().then((res) => {
        setConstructionList(res.tenantId ? res : null);
      });
    }
  }, [type]);

  return (
    <Context head={<Context.Head placeholder={t('handbook_placeholder')} title={title} />}>
      <div className={styles.handBook}>
        <div className={styles.title}>{getTitle()}</div>
        <div className={styles.body}>
          <div className={styles.bodyLeft}>
            {cutData.map((item, cutIndex) => (
              <div
                className={classNames(styles.link, cutIndex !== cutType ? styles.linkCopy : '')}
                tabIndex={cutIndex}
                role="button"
                onClick={() => {
                  setCutType(cutIndex);
                }}
              >
                <div
                  className={classNames(
                    styles.serial,
                    cutIndex !== cutType ? styles.serialCopy : ''
                  )}
                >
                  {cutIndex + 1}
                </div>
                <div className={classNames(styles.cut, cutIndex !== cutType ? styles.cutCopy : '')}>
                  {item}
                </div>
              </div>
            ))}
          </div>
          <div className={styles.bodyRight}>
            {videoPhotoData
              .filter((f, index) => index === cutType)
              .map((item) => (
                <>
                  <div>
                    {item.type === 'video' ? (
                      <video width="100%" src={item.url} controls>
                        <track default kind="captions" srcLang="en" src="/video/php/friday.vtt" />
                      </video>
                    ) : (
                      <img src={item.url} alt="" />
                    )}
                  </div>
                  <div className={styles.establish}>{item.title}</div>
                  <div className={styles.speediness}>{item.body}</div>
                  <div
                    className={styles.btn}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      gotoUrl(item.go, item.personalIdentityIsDisable);
                    }}
                  >
                    {item.btnText}
                  </div>
                </>
              ))}
          </div>
        </div>
      </div>
    </Context>
  );
}

export default HandBook;
