.item {
  cursor: pointer;
  border-radius: 18px;
  padding: 8px 10px;
  position: relative;
}

.item:hover {
  background-color: rgb(217 238 255 / 30%);
}

.item:active {
  background-color: rgb(217 238 255 / 50%);
}

.mb12 {
  margin-bottom: 16px;
}

.mr16 {
  margin-right: 16px;
}

.ml8 {
  margin-left: 8px;
}

.itemContent {
  display: flex;
  margin-bottom: 12px;
}

.avatar {
  border-radius: 12px;
  min-width: 64px;
}

.product {
  display: flex;
  margin-left: 8px;
  justify-content: space-between;
  flex-direction: column;
}

.title {
  color: #040919;
  font-size: 14px;
  line-height: 20px;
}

.size {
  color: #888b98;
  font-size: 12px;
  line-height: 17px;
}
@media screen and (min-width: @screen-sm-min) {
  .title,
  .size {
    width: 180px;
  }
}
@media screen and (min-width: @screen-md-min) {
  .title,
  .size {
    width: 480px;
  }
}
@media screen and (min-width: @screen-lg-min) {
  .title,
  .size {
    width: 900px;
  }
}

.price {
  color: #040919;
  font-size: 14px;
  line-height: 20px;
}

.count {
  color: #888b98;
  font-size: 12px;
  margin-left: 8px;
}

.extra {
  color: #000;
  display: none;
  position: absolute;
  top: 4px;
  right: 10px;
  z-index: 1;
}

.item:hover .extra {
  display: block;
}

.btn {
  position: absolute !important;
  right: 16px;
  bottom: 8px;
  z-index: 1;
}

.itemFoot {
  color: #888b98;
  display: flex;
  height: 20px;
  line-height: 20px;
  align-items: center;
}

.subTitle {
  color: #b1b3be;
  font-size: 12px;
  line-height: 17px;
}

.content {
  display: flex;
  margin-left: 8px;
  padding: 10px 0;
  justify-content: space-between;
  flex-direction: column;
}

.removeText {
  color: #888b98;
  font-size: 14px;
  line-height: 20px;
}
