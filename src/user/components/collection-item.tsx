import { BaseSyntheticEvent, useState } from 'react';
import { Link } from 'react-router-dom';
import { Image, Button, Dropdown, Menu, Typography, message } from 'antd';
import type { MenuProps } from 'antd';
import { Icon, Price } from '@/components';
import { addCart, GetUserCollectionItem } from '@/apis';
import { shareMessage } from '@@/renovation/containers/frame/util';
// import dayjs from 'dayjs';
import styles from './collection-item.module.less';

const { Text, Paragraph } = Typography;
// const fileTypeImages = {
//   link: `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/website/images/file-type/link.png`,
//   excel: `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/website/images/file-type/excel.png`,
//   pdf: `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/website/images/file-type/pdf.png`,
//   ppt: `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/website/images/file-type/ppt.png`,
//   radio: `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/website/images/file-type/radio.png`,
//   word: `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/website/images/file-type/word.png`,
// };

export interface CollectionItemProps {
  itemData: GetUserCollectionItem;
  itemIndex: number;
  // eslint-disable-next-line no-unused-vars
  onClick: (data: GetUserCollectionItem, i: number) => void;
  // eslint-disable-next-line no-unused-vars
  onRemove: (data: GetUserCollectionItem, i: number) => void;
}

// eslint-disable-next-line no-unused-vars
function CollectionItem({ itemData, itemIndex, onClick, onRemove }: CollectionItemProps) {
  const [btnLoading, setBtnLoading] = useState(false);
  const productData = itemData.shopSkuFollowResponse;

  // 转发
  const onShare = () => {
    shareMessage({
      sharetype: 10,
      param: {
        shopId: productData.shopId,
        shopName: productData.shopName,
        shopLogo: productData.avatar,
        image: productData.imageList[0],
        title: productData.name,
        shopSkuId: productData.shopSkuId,
        price: productData.price,
      },
    }).then(() => {
      message.success('转发成功！');
    });
  };

  // 加入购物车
  const onBuy = (event: BaseSyntheticEvent) => {
    event.stopPropagation();
    event.preventDefault();
    setBtnLoading(true);
    addCart({
      shopSkuId: productData.shopSkuId,
      payWayNo: productData.payWayNo,
      shopId: productData.shopId,
      skuId: productData.skuId,
      productNumber: productData.minimum || 1,
      productUnit: productData.unit,
    })
      .then(() => {
        message.success('加入购物车成功！');
      })
      .finally(() => {
        setBtnLoading(false);
      });
  };

  // 更多操作
  const onMenuClick: MenuProps['onClick'] = ({ key, domEvent }) => {
    domEvent.stopPropagation();
    switch (key) {
      case 'share':
        onShare();
        break;
      case 'remove':
        onRemove(itemData, itemIndex);
        break;
      default:
    }
  };

  // 更多操作 Element
  const menu = (
    <Menu onClick={onMenuClick}>
      <Menu.Item key="share" className="text-center">
        转发
      </Menu.Item>
      <Menu.Item key="remove" className="text-center">
        删除
      </Menu.Item>
    </Menu>
  );

  // 商品
  const productItem = (
    <Link to={`/shop/goods/detail?shopSkuId=${productData.shopSkuId}`}>
      <div
        className={styles.item}
        role="button"
        tabIndex={0}
        onClick={() => onClick(itemData, itemIndex)}
      >
        <div className={styles.itemContent}>
          <Image
            src={productData.imageList[0]}
            preview={false}
            width="64px"
            height="64px"
            className={styles.avatar}
          />
          <div className={styles.product}>
            <Paragraph ellipsis className={styles.title}>
              {productData.name}
            </Paragraph>
            <Paragraph ellipsis className={styles.size}>
              {productData.standardListStr}
            </Paragraph>
            <div className={styles.price}>
              <Price className={styles.price} value={productData.price} />
              <Text className={styles.count}>月售：{productData.sales}</Text>
            </div>
          </div>
        </div>
        <Link
          to={`/shop-home?bid=${productData.shopId}&code=deco_shop`}
          onClick={(e) => e.stopPropagation()}
          className={styles.itemFoot}
        >
          <Icon name="shop" size={20} />
          <span className={styles.ml8}>{productData.shopName}</span>
          <Icon name="right" size={16} />
        </Link>
        <Dropdown overlay={menu} placement="bottomRight">
          <Icon
            name="zu13366"
            size={20}
            className={styles.extra}
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
          />
        </Dropdown>
        <Button
          size="small"
          type="primary"
          className={styles.btn}
          onClick={onBuy}
          disabled={btnLoading}
        >
          加入购物车
        </Button>
      </div>
    </Link>
  );

  // 文本、图片、链接、文件
  // const messageItem = (
  //   <div className={styles.item}>
  //     {itemData.type === 2 && (
  //       <Paragraph ellipsis={{ rows: 3 }} style={{ width: '90%' }} className={styles.title}>
  //         {itemData.content}
  //       </Paragraph>
  //     )}
  //     {itemData.type === 3 && (
  //       <Image
  //         src={itemData.image}
  //         preview={false}
  //         width="150px"
  //         height="150px"
  //         className={styles.avatar}
  //       />
  //     )}
  //     {itemData.type === 4 && (
  //       <div className={styles.itemContent}>
  //         <Image
  //           src={itemData.siteLogo}
  //           preview={false}
  //           width="64px"
  //           height="64px"
  //           className={styles.avatar}
  //         />
  //         <div className={styles.content}>
  //           <Paragraph className={styles.title}>{itemData.siteTitle}</Paragraph>
  //           <Paragraph className={styles.subTitle}>{itemData.siteUrl}</Paragraph>
  //         </div>
  //       </div>
  //     )}
  //     {itemData.type === 5 && (
  //       <div className={styles.itemContent}>
  //         <Image
  //           src={itemData.fileIcon}
  //           preview={false}
  //           width="64px"
  //           height="64px"
  //           className={styles.avatar}
  //         />
  //         <div className={styles.content}>
  //           <Paragraph className={styles.title}>{itemData.fileName}</Paragraph>
  //           <Paragraph className={styles.subTitle}>{itemData.fileSize}</Paragraph>
  //         </div>
  //       </div>
  //     )}
  //     <div className={styles.itemFoot} style={{ marginTop: '12px' }}>
  //       <Text className={styles.siez}>{itemData.name}</Text>
  //       <Text className={styles.siez} style={{ marginLeft: '4px' }}>
  //         {dayjs(itemData.date).format('YYYY/MM/DD ')}
  //       </Text>
  //     </div>
  //     <Dropdown overlay={menu} placement="bottomRight">
  //       <Icon name="zu13366" size={20} className={styles.extra} />
  //     </Dropdown>
  //   </div>
  // );

  // 失效内容
  // const removedItem = (
  //   <div className={styles.item}>
  //     <Text className={styles.removeText}>该内容已被删除</Text>
  //     <Dropdown overlay={menu} placement="bottomRight">
  //       <Icon
  //         name="zu13366"
  //         size={20}
  //         className={styles.extra}
  //         onClick={(e) => e.stopPropagation()}
  //       />
  //     </Dropdown>
  //   </div>
  // );

  return itemData.msgType === 3 ? productItem : null;
}

CollectionItem.displayName = 'CollectionItem';

export default CollectionItem;
