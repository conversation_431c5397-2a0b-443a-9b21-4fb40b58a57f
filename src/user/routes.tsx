import { RouteObject } from 'react-router-dom';
import { lazy } from '@/components';

const routes: RouteObject[] = [
  {
    path: 'my/collection',
    element: lazy(() => import('./pages/collection')),
  },
  {
    path: 'my/hsj/ai',
    element: lazy(() => import('./pages/hsj-ai')),
  },
  {
    path: 'my/base-info',
    element: lazy(() => import('./pages/base-info')),
  },
];

if (import.meta.env.DEV) {
  routes.push({
    path: 'my/cdn/update',
    element: lazy(() => import('./pages/cdn-update')),
  });
}

export default routes;
