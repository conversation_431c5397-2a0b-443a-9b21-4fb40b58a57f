import { useState, useEffect, useCallback, useRef, useReducer } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { List, Empty, Spin, Divider, message } from 'antd';
import { Context, Modal } from '@/components';
import { getUserCollectionList, GetUserCollectionItem, toggleCollectionProduct } from '@/apis';
import CollectionItem from '../components/collection-item';
import styles from './collection.module.less';

const { Head, QuickFilter } = Context;
const pageSize = 10;
const options = [
  { label: '全部', value: '-1' },
  { label: '商品', value: '3' },
  { label: '文本', value: '1' },
  { label: '图片', value: '2' },
  { label: '链接', value: '21' },
  { label: '文件', value: '20' },
];

interface InitialState {
  pageNo: number;
  totalPage: number;
  list: GetUserCollectionItem[];
}

type CollectionAction =
  | {
      type: 'setPage';
      payload: { pageNo?: number; totalPage?: number };
    }
  | {
      type: 'setList';
      payload: GetUserCollectionItem[];
    }
  | {
      type: 'setAll';
      payload: InitialState;
    };

const initialState = {
  pageNo: 1,
  totalPage: 1,
  list: [],
};

const reducer = (state: InitialState, action: CollectionAction): InitialState => {
  switch (action.type) {
    case 'setPage':
      return { ...state, ...action.payload };
    case 'setList':
      return { ...state, list: action.payload };
    case 'setAll':
      return action.payload;
    default:
      return state;
  }
};

function Collection() {
  const type = useRef('-1');
  const scrollEl = useRef(null as unknown as HTMLDivElement);
  const [loading, setLoading] = useState(false);
  const [state, dispatch] = useReducer(reducer, initialState);

  // 获取数据
  const getList = useCallback((argument: InitialState) => {
    getUserCollectionList({
      pageSize,
      msgType: +type.current,
      pageNo: argument.pageNo,
    })
      .then((res) => {
        dispatch({
          type: 'setAll',
          payload: {
            pageNo: argument.pageNo + 1,
            list: [...argument.list, ...res.list],
            totalPage: Math.ceil(res.totalCount / pageSize),
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // 加载更多数据
  const loadMoreData = () => {
    if (!(state.pageNo <= state.totalPage) || loading) return;
    getList(state);
  };

  // 删除收藏
  const onRemoveItem = (item: GetUserCollectionItem) => {
    Modal.confirm({
      title: '提示',
      content: '是否删除该内容？',
      onOk: () => {
        toggleCollectionProduct({
          productId: [item.shopSkuFollowResponse.shopSkuId],
          bool: false,
        }).then(() => {
          dispatch({
            type: 'setList',
            payload: state.list.filter((tmpItem) => tmpItem.collectionId !== item.collectionId),
          });
          message.success('删除成功!');
        });
      },
    });
  };

  useEffect(() => {
    setLoading(true);
    getList(initialState);
  }, [getList]);

  const header = (
    <Head
      title="收藏"
      quickFilter={
        <QuickFilter
          label="显示"
          defaultValue={type.current}
          options={options}
          onChange={(value) => {
            setLoading(true);
            type.current = value;
            getList(initialState);
            if (scrollEl.current) scrollEl.current.scrollTop = 0;
          }}
        />
      }
    />
  );

  const content = state.list.length ? (
    <div id="scrollableDiv" ref={scrollEl} className={styles.collectionList}>
      <InfiniteScroll
        dataLength={state.list.length}
        next={loadMoreData}
        hasMore={state.pageNo <= state.totalPage}
        loader={
          <div className="text-center">
            <Spin tip="加载中..." />
          </div>
        }
        endMessage={
          <div className={styles.divider}>
            <Divider plain>
              <span className={styles.endMessage}>没有更多了</span>
            </Divider>
          </div>
        }
        scrollableTarget="scrollableDiv"
      >
        <List
          dataSource={state.list}
          itemLayout="vertical"
          renderItem={(item, i) => (
            <List.Item key={item.collectionId} className={styles.collectionItem}>
              <CollectionItem
                itemData={item}
                itemIndex={i}
                onClick={() => {}}
                onRemove={onRemoveItem}
              />
            </List.Item>
          )}
        />
      </InfiniteScroll>
    </div>
  ) : (
    <Empty
      image={`${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/PC/static/img/empty.png`}
      description="暂⽆相关内容"
      className={styles.empty}
    />
  );

  return (
    <Context head={header} loading={loading}>
      {!loading && content}
    </Context>
  );
}

Collection.displayName = 'Collection';

export default Collection;
