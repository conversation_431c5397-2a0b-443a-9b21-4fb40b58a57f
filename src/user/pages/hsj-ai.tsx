import { Context } from '@/components';
import { useEffect, useState } from 'react';

function HsjAi() {
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    setLoading(true);
  }, []);

  return (
    <Context loading={loading}>
      <iframe
        title="华世界AI"
        src="http://192.168.166.21/"
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          verticalAlign: 'middle',
        }}
        onLoad={() => {
          setLoading(false);
        }}
      />
    </Context>
  );
}

export default HsjAi;
