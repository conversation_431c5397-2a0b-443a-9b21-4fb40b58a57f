import { useEffect, useRef, useState } from 'react';
import { useMemoizedFn } from 'ahooks';
import { Input, Cascader, Select, message, Avatar, Checkbox } from 'antd';
import classNames from 'classnames';
import {
  Context,
  GaoDeMap,
  Icon,
  Modal,
  SelectedMapInfo,
  SimpleUpload,
  Upload,
} from '@/components';
import EchIcon from '@echronos/echos-icon';
import ImageCropper from '@echronos/echos-ui/dist/image-cropper';
import type { ImageCropperHandle } from '@echronos/echos-ui/dist/image-cropper';
import { EditOutlined } from '@echronos/icons';
import { user } from '@/store';
import { getCompanyBaseInfo, getCompanyCategories, updateCompanyBaseInfo } from '@/apis';
import type { GetCompanyBaseInfoResultType, GetCategoryResult } from '@/apis';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

const { TextArea } = Input;

function DasicInfoSettings() {
  const { t } = useTranslation();
  const checkboxOptions = [
    { label: t('base_info_contactPhone'), value: '3' },
    { label: t('base_info_storeNavgation'), value: '5' },
    { label: t('base_info_cart'), value: '1' },
    { label: t('base_info_share'), value: '2' },
  ];
  const [pageLoading, setPageLoading] = useState(true);
  const [categories, setCategories] = useState<GetCategoryResult[]>([]);
  const [baseInfo, setBaseInfo] = useState({
    id: null,
    logoUrl: '',
    status: 0,
    companyName: '',
    abbreviation: '',
    categoryId: null,
    categoryList: [],
    staff: null,
    contactSite: '',
    contactPhone: '',
    contactEmail: '',
    contact: '',
    imageList: [],
    homepageCards: '',
    profile: '',
    background: '',
    longitude: null,
    latitude: null,
  } as GetCompanyBaseInfoResultType); // 企业基本信息
  // 企业基本信息 取消编辑时 的备份
  const [baseInfoBackup, setBaseInfoBackup] = useState<GetCompanyBaseInfoResultType>(
    {} as GetCompanyBaseInfoResultType
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const imageCropperRef = useRef<ImageCropperHandle>(null); // 主页背景图 裁剪
  const companyLogoRef = useRef<ImageCropperHandle>(null); // 企业形象照 裁剪
  // 当前操作的企业形象照 索引
  const [currentEditCompanyLogoIndex, setCurrentEditCompanyLogoIndex] = useState(0);
  // 主页简介 编辑弹窗
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  // 当前编辑类型
  const [currentEditType, setCurrentEditType] = useState<string>('');

  const baseInfoRef: any = useRef({
    id: null,
    logoUrl: '',
    status: 0,
    companyName: '',
    abbreviation: '',
    categoryId: null,
    categoryList: [],
    staff: null,
    contactSite: '',
    contactPhone: '',
    contactEmail: '',
    contact: '',
    imageList: [],
  });
  const selectMapInfo = useRef({} as SelectedMapInfo);

  const getMaxLength = useMemoizedFn((type: string) => {
    if (type === 'simpleName') {
      return 30;
    }
    if (type === 'address') {
      return 200;
    }
    if (type === 'phone') {
      return 13;
    }
    if (type === 'mailbox') {
      return 30;
    }
    if (type === 'contacts') {
      return 20;
    }
    if (type === 'introduction') {
      return 100;
    }
    return 200;
  });

  const getEditValue = useMemoizedFn((type: string) => {
    if (type === 'simpleName') {
      return baseInfo.abbreviation;
    }
    if (type === 'address') {
      return baseInfo.contactSite;
    }
    if (type === 'phone') {
      return baseInfo.contactPhone;
    }
    if (type === 'mailbox') {
      return baseInfo.contactEmail;
    }
    if (type === 'contact') {
      return baseInfo.contact;
    }
    if (type === 'introduction') {
      return baseInfo.profile;
    }
    return undefined;
  });

  // 点击 编辑icon 弹出编辑框
  const handleEdit = useMemoizedFn((type: string) => {
    setCurrentEditType(type);
    setIsProfileModalOpen(true);
    setBaseInfoBackup({ ...baseInfo });
  });

  const queryGetCompanyBaseInfo = () => {
    setPageLoading(true);
    getCompanyBaseInfo()
      .then((res) => {
        setBaseInfo({
          ...res,
          categoryList: res.categoryList ? JSON.parse(res.categoryList) : [],
          abbreviation: res.abbreviation || null,
        });
      })
      .finally(() => {
        setPageLoading(false);
      });
  };

  useEffect(() => {
    baseInfoRef.current = baseInfo;
  }, [baseInfo]);

  useEffect(() => {
    getCompanyCategories().then(({ list }) => {
      const categoriesList = list.map((item) => {
        if (item.children && item.children.length > 0) {
          item.children.forEach((child) => {
            // eslint-disable-next-line no-param-reassign
            child.children = undefined;
          });
        }
        return item;
      });
      setCategories(categoriesList);
      queryGetCompanyBaseInfo();
    });
  }, []);

  return (
    <Context container theme={null} loading={pageLoading}>
      <div className={styles.dasicInfoSetPage}>
        <div className={styles.pageTitle}>{t('base_info_basic')}</div>
        <div className={styles.scrollBox}>
          <div>
            <div className={styles.centerBox}>
              <div className={styles.logoBox}>
                <Upload
                  maxCount={1}
                  accept="image/*"
                  value={baseInfo.logoUrl}
                  onUploadChange={(e) => {
                    if (e.status === 'success') {
                      setBaseInfo({ ...baseInfo, logoUrl: e.file.url });
                      updateCompanyBaseInfo({ ...baseInfoRef.current, logoUrl: e.file.url })
                        .then(() => {
                          // 编辑成功
                          message.success(t('base_info_success'));
                        })
                        .finally(() => {
                          queryGetCompanyBaseInfo();
                        });
                    }
                  }}
                >
                  {baseInfo.logoUrl ? (
                    <Avatar shape="circle" className={styles.avatarIcon} src={baseInfo.logoUrl} />
                  ) : (
                    <Avatar
                      shape="circle"
                      className={styles.avatarIcon}
                      src="https://img.huahuabiz.com/user_files/202329/1675912078549519.png"
                    />
                  )}
                  <img
                    className={styles.editAvatarIcon}
                    src="https://img.huahuabiz.com/user_files/20231211/1702282766054601.png"
                    alt=""
                  />
                </Upload>
              </div>
            </div>
            <div className={styles.companyName}>{baseInfo.companyName || user.companyName}</div>
            <div className={styles.cardBox}>
              <div className={styles.flexRow}>
                <div className={styles.flexLine}>
                  <img
                    className={styles.lineIcon}
                    src="https://img.huahuabiz.com/user_files/20231213/1702448377845868.png"
                    alt=""
                  />
                  <div>{t('base_info_orgCertStatus')}</div>
                </div>
                <div className={styles.flexLine}>
                  {baseInfo.status ? null : <Icon name="warn-sold" size={20} color="#f9ae08" />}
                  <div className={styles.authStatus}>
                    {baseInfo.status ? t('base_info_OrgCertified') : t('base_info_OrgUncertified')}
                  </div>
                  <Icon
                    name="right"
                    size={20}
                    onClick={() => {
                      const a = document.createElement('a');
                      a.target = '_top';
                      a.href = '/user/company/auth';
                      a.click();
                    }}
                  />
                </div>
              </div>
            </div>

            <div className={styles.cardBox}>
              <div className={classNames(styles.flexRow)}>
                <div>
                  <div className={styles.flexLine}>
                    <img
                      className={styles.lineIcon}
                      src="https://img.huahuabiz.com/user_files/20231213/1702448782214569.png"
                      alt=""
                    />
                    <div>{t('base_info_ent/org')}</div>
                  </div>
                </div>
                <div>{baseInfo.companyName || user.companyName}</div>
              </div>
              <div className={styles.line} />
              <div className={styles.flexRow}>
                <div>
                  <div className={styles.flexLine}>
                    <img
                      className={styles.lineIcon}
                      src="https://img.huahuabiz.com/user_files/20231213/1702448784886371.png"
                      alt=""
                    />
                    <div>{t('base_info_ent/orgAbbreviation')}</div>
                  </div>
                </div>
                <div className={styles.flexLine}>
                  <Input
                    style={{ width: 300, textAlign: 'right' }}
                    className={styles.inputValue}
                    value={baseInfo.abbreviation || ''}
                    placeholder={t('base_info_delabbreviationPlaceholder')}
                    bordered={false}
                    disabled
                  />
                  <EditOutlined
                    size={22}
                    color="#999eb2"
                    className={styles.editIcon}
                    onClick={() => {
                      if (baseInfo.status) {
                        handleEdit('simpleName');
                      } else {
                        message.warning(t('base_info_delAbbreviationWarning'));
                      }
                    }}
                  />
                </div>
              </div>
              <div className={styles.line} />
              <div className={styles.flexRow}>
                <div>
                  <div className={styles.flexLine}>
                    <img
                      className={styles.lineIcon}
                      src="https://img.huahuabiz.com/user_files/20231213/1702449359455880.png"
                      alt=""
                    />
                    <div>{t('base_info_industryType')}</div>
                  </div>
                </div>
                <Cascader
                  options={categories || []}
                  placeholder={t('base_info_delSelectIndType')}
                  loading={!categories}
                  style={{ width: 240, textAlign: 'right' }}
                  showSearch
                  allowClear={false}
                  bordered={false}
                  value={baseInfo.categoryList || []}
                  onChange={(value) => {
                    setBaseInfo({
                      ...baseInfo,
                      categoryId: Number(value?.[value.length - 1]) || null,
                      categoryList: value || [],
                    });
                    updateCompanyBaseInfo({
                      ...baseInfoRef.current,
                      categoryId: Number(value?.[value.length - 1]) || null,
                      categoryList: value || [],
                    })
                      .then(() => {
                        message.success(t('base_info_success'));
                      })
                      .finally(() => {
                        queryGetCompanyBaseInfo();
                      });
                  }}
                />
              </div>
              <div className={styles.line} />
              <div className={styles.flexRow}>
                <div>
                  <div className={styles.flexLine}>
                    <img
                      className={styles.lineIcon}
                      src="https://img.huahuabiz.com/user_files/20231213/1702449361461729.png"
                      alt=""
                    />
                    <div>{t('base_info_employeeSize')}</div>
                  </div>
                </div>
                <Select
                  bordered={false}
                  style={{ width: 150, textAlign: 'right' }}
                  placeholder={t('base_info_delemployeePlaceholder')}
                  value={baseInfo.staff || null}
                  options={[
                    {
                      value: 1,
                      label: '1~50',
                    },
                    {
                      value: 2,
                      label: '51~100',
                    },
                    {
                      value: 3,
                      label: '101~300',
                    },
                    {
                      value: 4,
                      label: '301~500',
                    },
                    {
                      value: 5,
                      label: '501~1000',
                    },
                    {
                      value: 6,
                      label: '≥1000',
                    },
                  ]}
                  onChange={(val) => {
                    setBaseInfo({ ...baseInfo, staff: val });
                    updateCompanyBaseInfo({
                      ...baseInfoRef.current,
                      staff: val,
                    })
                      .then(() => {
                        message.success(t('base_info_success'));
                      })
                      .finally(() => {
                        queryGetCompanyBaseInfo();
                      });
                  }}
                />
              </div>
            </div>

            <div className={styles.cardBox}>
              <div className={classNames(styles.flexRow)}>
                <div>
                  <div className={styles.flexLine}>
                    <img
                      className={styles.lineIcon}
                      src="https://img.huahuabiz.com/user_files/20231213/1702449363981688.png"
                      alt=""
                    />
                    <div>{t('base_info_contactAddress')}</div>
                  </div>
                </div>
                <div className={styles.flexLine}>
                  <Input
                    title={baseInfo.contactSite}
                    style={{ width: 400, textAlign: 'right' }}
                    className={styles.addressInput}
                    value={baseInfo.contactSite}
                    placeholder={t('base_info_delAddressPlaceholder')}
                    bordered={false}
                    disabled
                  />
                  <EchIcon
                    size={20}
                    // color="#999eb2"
                    name="address_line"
                    onClick={() => {
                      setIsModalOpen(true);
                    }}
                  />
                </div>
              </div>
              <div className={styles.line} />
              <div className={styles.flexRow}>
                <div className={styles.flexLine}>
                  <img
                    className={styles.lineIcon}
                    src="https://img.huahuabiz.com/user_files/20231213/1702449366054540.png"
                    alt=""
                  />
                  <div>{t('base_info_contactPhone')}</div>
                </div>
                <div className={styles.flexLine}>
                  <Input
                    style={{ width: 300, textAlign: 'right' }}
                    className={styles.inputValue}
                    value={baseInfo.contactPhone}
                    placeholder={t('base_info_delPhonePlaceholder')}
                    maxLength={13}
                    bordered={false}
                    disabled
                  />
                  <EditOutlined
                    size={22}
                    color="#999eb2"
                    className={styles.editIcon}
                    onClick={() => {
                      handleEdit('phone');
                    }}
                  />
                </div>
              </div>
              <div className={styles.line} />
              <div className={styles.flexRow}>
                <div className={styles.flexLine}>
                  <img
                    className={styles.lineIcon}
                    src="https://img.huahuabiz.com/user_files/20231213/1702449368373815.png"
                    alt=""
                  />
                  <div>{t('base_info_contactEmail')}</div>
                </div>
                <div className={styles.flexLine}>
                  <Input
                    style={{ width: 300, textAlign: 'right' }}
                    className={styles.inputValue}
                    value={baseInfo.contactEmail}
                    placeholder={t('base_info_delEmailPlaceholder')}
                    bordered={false}
                    disabled
                  />
                  <EditOutlined
                    size={22}
                    color="#999eb2"
                    className={styles.editIcon}
                    onClick={() => {
                      handleEdit('mailbox');
                    }}
                  />
                </div>
              </div>
              <div className={styles.line} />
              <div className={styles.flexRow}>
                <div className={styles.flexLine}>
                  <img
                    className={styles.lineIcon}
                    src="https://img.huahuabiz.com/user_files/20231213/1702449370689769.png"
                    alt=""
                  />
                  <div>{t('base_info_contact')}</div>
                </div>
                <div className={styles.flexLine}>
                  <Input
                    style={{ width: 300, textAlign: 'right' }}
                    className={styles.inputValue}
                    value={baseInfo.contact}
                    placeholder={t('base_info_delcontactPlaceholder')}
                    bordered={false}
                    disabled
                  />
                  <EditOutlined
                    size={22}
                    color="#999eb2"
                    className={styles.editIcon}
                    onClick={() => {
                      handleEdit('contact');
                    }}
                  />
                </div>
              </div>
            </div>

            <div className={styles.cardBox}>
              <div className={classNames(styles.flexRow, styles.backImgRow)}>
                <div className={styles.imgLabel}>
                  <div className={styles.flexLine}>
                    <img
                      className={styles.lineIcon}
                      src="https://img.huahuabiz.com/user_files/20241018/172922123895195.png"
                      alt=""
                    />
                    <div>{t('base_info_corporateImage')}</div>
                  </div>
                  <div className={styles.imgTips}>{t('base_info_delImageFormat')}</div>
                  <div className={styles.imgTips}>{t('base_info_delImageRatio')}</div>
                </div>
                <div className={styles.flexLine}>
                  {baseInfo?.imageList && baseInfo?.imageList.length
                    ? baseInfo.imageList.map((item: string, index: number) => (
                        <div className={styles.imageBox}>
                          <img className={styles.imgBox} src={item} alt="" />
                          <div className={styles.editImgBox}>
                            <div className={styles.imgDelBtnRow}>
                              <div className={styles.imgDelBtnBox}>
                                <EchIcon
                                  color="#fff"
                                  name="close_line"
                                  onClick={() => {
                                    window.console.log('删除 企业形象照');

                                    updateCompanyBaseInfo({
                                      ...baseInfoRef.current,
                                      imageList: [
                                        ...baseInfoRef.current.imageList.filter(
                                          (_: any, i: number) => index !== i
                                        ),
                                      ],
                                    })
                                      .then(() => {
                                        message.success(t('base_info_success'));
                                      })
                                      .finally(() => {
                                        queryGetCompanyBaseInfo();
                                      });
                                  }}
                                />
                              </div>
                            </div>
                            <div className={styles.footerBtnBox}>
                              <div
                                tabIndex={0}
                                role="button"
                                className={styles.footerBtn}
                                onClick={() => {
                                  window.console.log('编辑 企业形象照');
                                  companyLogoRef.current?.onVisible();
                                  setCurrentEditCompanyLogoIndex(index);
                                }}
                              >
                                {t('edit')}
                              </div>

                              <SimpleUpload
                                className={styles.footerBtn}
                                maxCount={1}
                                accept="image/*"
                                onChange={(e: { file: { url: string; filePath: string } }) => {
                                  window.console.log('替换 企业形象照', e);
                                  baseInfoRef.current.imageList[index] = e.file.url;
                                  updateCompanyBaseInfo({
                                    ...baseInfoRef.current,
                                    imageList: [...baseInfoRef.current.imageList],
                                  })
                                    .then(() => {
                                      message.success(t('base_info_success'));
                                    })
                                    .finally(() => {
                                      queryGetCompanyBaseInfo();
                                    });
                                }}
                              >
                                {t('base_info_replace')}
                              </SimpleUpload>
                            </div>
                          </div>
                        </div>
                      ))
                    : null}
                  {baseInfo?.imageList?.length < 4 ? (
                    <SimpleUpload
                      maxCount={1}
                      accept="image/*"
                      onChange={(e: { file: { url: string; filePath: string } }) => {
                        window.console.log('上传 企业形象照', e);
                        updateCompanyBaseInfo({
                          ...baseInfoRef.current,
                          imageList: [...baseInfoRef.current.imageList, e.file.url],
                        })
                          .then(() => {
                            message.success(t('base_info_success'));
                          })
                          .finally(() => {
                            queryGetCompanyBaseInfo();
                          });
                      }}
                    >
                      <div className={styles.uploadBtn}>
                        <EchIcon size={24} name="add_line" />
                        <span>{t('base_info_uploadImg')}</span>
                      </div>
                    </SimpleUpload>
                  ) : null}
                  <ImageCropper
                    onSuccess={(value) => {
                      window.console.log(value);
                      // 当前替换图片的索引 currentEditCompanyLogoIndex
                      baseInfoRef.current.imageList[currentEditCompanyLogoIndex] = value.url;
                      updateCompanyBaseInfo({
                        ...baseInfoRef.current,
                        imageList: [...baseInfoRef.current.imageList],
                      })
                        .then(() => {
                          message.success(t('base_info_success'));
                        })
                        .finally(() => {
                          queryGetCompanyBaseInfo();
                        });
                    }}
                    ref={companyLogoRef}
                    aspectType={0}
                    obsFolder={(import.meta.env?.BIZ_OBS_FOLDER as string) || 'test'}
                    obsToken={localStorage.getItem('CURRENT_SESSION_TOKEN') as string}
                    imgUrl={baseInfo.imageList[currentEditCompanyLogoIndex]}
                  />
                </div>
              </div>
            </div>

            <div className={styles.cardBox}>
              <div className={styles.flexRow}>
                <div className={styles.flexLine}>
                  <img
                    className={styles.lineIcon}
                    src="https://img.huahuabiz.com/user_files/20241018/1729220119032740.png"
                    alt=""
                  />
                  <div>{t('base_info_homeCard')}</div>
                </div>
                <div className={styles.flexLine}>
                  <Checkbox.Group
                    onChange={(e) => {
                      updateCompanyBaseInfo({ ...baseInfoRef.current, homepageCards: e.join(',') })
                        .then(() => {
                          message.success(t('base_info_success'));
                        })
                        .finally(() => {
                          queryGetCompanyBaseInfo();
                        });
                    }}
                    value={baseInfo.homepageCards?.split(',') || ['']}
                    options={checkboxOptions}
                  />
                </div>
              </div>
              <div className={styles.line} />
              <div>
                <div className={classNames(styles.flexRow, styles.textAreaRow)}>
                  <div className={styles.flexLine}>
                    <img
                      className={styles.lineIcon}
                      src="https://img.huahuabiz.com/user_files/20241018/1729220120800480.png"
                      alt=""
                    />
                    <div>{t('base_info_intro')}</div>
                  </div>
                  <div className={styles.flexLine}>
                    <TextArea
                      // className={styles.introductionTextArea}
                      style={{ width: 300, height: 120, resize: 'none' }}
                      value={baseInfo.profile}
                      placeholder={t('base_info_delIntroPlaceholder')}
                    />
                    <EditOutlined
                      size={22}
                      color="#999eb2"
                      className={styles.editIcon}
                      onClick={() => {
                        handleEdit('introduction');
                      }}
                    />
                  </div>
                </div>
              </div>
              <div className={styles.line} />
              <div>
                <div className={classNames(styles.flexRow, styles.backImgRow)}>
                  <div className={styles.imgLabel}>
                    <div className={styles.flexLine}>
                      <img
                        className={styles.lineIcon}
                        src="https://img.huahuabiz.com/user_files/20241018/1729220122826250.png"
                        alt=""
                      />
                      <div>{t('base_info_homeBackgroundImage')}</div>
                    </div>
                    <div className={styles.imgTips}>{t('base_info_delBackgroundImgFormat')}</div>
                    <div className={styles.imgTips}>{t('base_info_delBackgroundImgRatio')}</div>
                  </div>

                  <div className={styles.flexLine}>
                    <div className={styles.editpageHomeBack}>
                      {baseInfo?.background ? (
                        <img src={baseInfo.background} alt="" />
                      ) : (
                        <img
                          src="https://img.huahuabiz.com/user_files/202492/1725248731341629.png"
                          alt=""
                        />
                      )}
                      <div>
                        <div className={styles.editpageHomeBackBtn}>
                          <div>
                            <EchIcon size={20} color="#fff" name="picture_line" />
                            <SimpleUpload
                              maxCount={1}
                              accept="image/*"
                              onChange={(e: { file: { url: string; filePath: string } }) => {
                                window.console.log(e);
                                updateCompanyBaseInfo({
                                  ...baseInfoRef.current,
                                  background: e.file.url,
                                })
                                  .then(() => {
                                    message.success(t('base_info_success'));
                                  })
                                  .finally(() => {
                                    queryGetCompanyBaseInfo();
                                  });
                              }}
                              // obsFolder={(import.meta.env?.BIZ_OBS_FOLDER as string) || 'test'}
                              // obsToken={localStorage.getItem('CURRENT_SESSION_TOKEN') as string}
                            >
                              <span>{t('base_info_changeImage')}</span>
                            </SimpleUpload>
                          </div>
                          <div>
                            <EchIcon size={20} color="#fff" name="tailor_line" />
                            <ImageCropper
                              onSuccess={(value) => {
                                window.console.log(value.url);
                                updateCompanyBaseInfo({
                                  ...baseInfoRef.current,
                                  background: value.url,
                                })
                                  .then(() => {
                                    message.success(t('base_info_success'));
                                  })
                                  .finally(() => {
                                    queryGetCompanyBaseInfo();
                                  });
                              }}
                              ref={imageCropperRef}
                              aspectType={0}
                              obsFolder={(import.meta.env?.BIZ_OBS_FOLDER as string) || 'test'}
                              obsToken={localStorage.getItem('CURRENT_SESSION_TOKEN') as string}
                              imgUrl={baseInfo.background}
                            >
                              <span
                                tabIndex={0}
                                role="button"
                                onClick={() => {
                                  window.console.log('baseInfo', baseInfo);
                                  imageCropperRef?.current?.onVisible();
                                }}
                              >
                                {t('base_info_recrop')}
                              </span>
                            </ImageCropper>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <Modal
        title={t('base_info_selectAddress')}
        visible={isModalOpen}
        onOk={() => {
          const { address, longitude, latitude } = selectMapInfo.current;
          window.console.log('地点信息', address, longitude, latitude);

          updateCompanyBaseInfo({
            ...baseInfoRef.current,
            contactSite: address || '',
            longitude,
            latitude,
          })
            .then(() => {
              message.success(t('base_info_success'));
              setIsModalOpen(false);
            })
            .finally(() => {
              queryGetCompanyBaseInfo();
            });
        }}
        onCancel={() => {
          setIsModalOpen(false);
        }}
      >
        <GaoDeMap
          width="660px"
          height="500px"
          showCircle={false}
          location={{
            longitude: Number(baseInfo.longitude) || null,
            latitude: Number(baseInfo.latitude) || null,
          }}
          address={baseInfo.contactSite}
          onSelectChanged={(val) => {
            selectMapInfo.current = val;
            window.console.log('地点信息 GaoDeMap', selectMapInfo.current);
          }}
        />
      </Modal>
      <Modal
        title={t('edit')}
        width={270}
        visible={isProfileModalOpen}
        onCancel={() => {
          setIsProfileModalOpen(false);
          setBaseInfo({ ...baseInfoBackup });
        }}
        onOk={() => {
          updateCompanyBaseInfo({ ...baseInfoRef.current })
            .then(() => {
              message.success(t('base_info_success'));
            })
            .finally(() => {
              setIsProfileModalOpen(false);
              queryGetCompanyBaseInfo();
            });
        }}
        centered
        keyboard={false}
        closable={false}
      >
        <div>
          <TextArea
            style={{ height: 120, resize: 'none' }}
            placeholder={t('base_info_pleaseEnter')}
            maxLength={getMaxLength(currentEditType)}
            value={getEditValue(currentEditType) || ''}
            onChange={(e) => {
              const { value } = e.target;
              if (currentEditType === 'simpleName') {
                setBaseInfo({ ...baseInfo, abbreviation: value || '' });
              }
              if (currentEditType === 'address') {
                setBaseInfo({ ...baseInfo, contactSite: value || '' });
              }
              if (currentEditType === 'phone') {
                setBaseInfo({ ...baseInfo, contactPhone: value || '' });
              }
              if (currentEditType === 'mailbox') {
                setBaseInfo({ ...baseInfo, contactEmail: value.trim() || '' });
              }
              if (currentEditType === 'contact') {
                setBaseInfo({ ...baseInfo, contact: value || '' });
              }
              if (currentEditType === 'introduction') {
                const inputValue = value;

                // 分割行并检查行数和字符数
                const lines = inputValue.split('\n');
                if (lines.length > 5) {
                  message.warning(t('base_info_delmaxLines'));
                  return;
                }
                setBaseInfo({ ...baseInfo, profile: inputValue || '' });
              }
            }}
          />
          {['introduction'].includes(currentEditType) ? (
            <div className={styles.inputTip}>{baseInfo.profile.length}/100</div>
          ) : null}
        </div>
      </Modal>
    </Context>
  );
}

export default DasicInfoSettings;
