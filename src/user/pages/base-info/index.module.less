.dasicInfoSetPage {
  font-size: 14px;
  height: 100%;
  padding: 27px 27px 0 32px;
  background-image: url('https://img.huahuabiz.com/user_files/20231219/1702952077480742.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  overflow-y: auto;
}

.scrollBox {
  min-height: 500px;
  overflow-y: auto;
  text-align: center;
}

.pageTitle {
  font-size: 28px;
  font-weight: 500;
}

.centerBox {
  display: flex;
  justify-content: center;
}

.logoBox {
  width: 95px;
  height: 95px;
  position: relative;
  align-items: center;
  border-radius: 50%;
  border: 3px solid #e0e0ea;
}

.logoImg {
  width: 89px;
  height: 89px;
  border-radius: 27px;
}

.avatarIcon {
  font-size: 50px;
  display: flex;
  width: 89px;
  height: 89px;
  align-items: center;
}

.editAvatarIcon {
  width: 25px;
  height: 25px;
  position: absolute;
  right: -5px;
  bottom: -5px;
}

.companyName {
  font-size: 26px;
  font-weight: 500;
  margin: 16px 0;
  text-align: center;
}

.flexRow {
  display: flex;
  height: 54px;
  justify-content: space-between;
  align-items: center;
  border-radius: 18px;
  background-color: #fff;
}

.lineIcon {
  width: 28px;
  height: 28px;
  margin-right: 8px;
}

.line {
  border-bottom: 1px solid #f3f3f3;
}

.cardBox {
  margin-top: 16px;
  padding: 0 20px;
  border-radius: 18px;
  background-color: #fff;
}

.authStatus {
  margin: 0 5px;
}

.inputValue {
  text-align: right;
  font-size: 14px;

  :global {
    cursor: default !important;
    color: #333 !important;
  }
}

.addressInput {
  text-align: right;
  font-size: 14px;

  :global {
    cursor: default !important;
    color: #333 !important;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}

.flexLine {
  display: flex;
  align-items: center;
}

.editIcon {
  cursor: pointer;
}

.imgLabel {
  display: flex;
  flex-direction: column;
}

.imgTips {
  color: #888b98;
  display: flex;
  margin-top: 3px;
  align-items: center;
}

.backImgRow {
  height: 140px;
}

.imageBox {
  margin-right: 5px;
  position: relative;

  &:hover {
    .editImgBox {
      display: flex;
    }
  }
}

.imgBox {
  width: 100px;
  height: 100px;
  border-radius: 5px;
  object-fit: cover;
}

.editImgBox {
  display: none;
  width: 100%;
  height: 100%;
  justify-content: space-between;
  position: absolute;
  top: 0;
  z-index: 999;
  flex-direction: column;
  // 删除按钮
  .imgDelBtnRow {
    display: flex;
    justify-content: flex-end;

    .imgDelBtnBox {
      display: flex;
      width: 20px;
      height: 20px;
      margin-top: 3px;
      margin-right: 3px;
      justify-content: center;
      box-sizing: border-box;
      align-items: center;
      background: rgb(0 0 0 / 30%);
      border-radius: 50%;
      cursor: pointer;
    }
  }

  // 编辑 替换按钮
  .footerBtnBox {
    display: flex;
    background: rgb(0 0 0 / 30%);
    padding: 3px 10px;

    & div:first-child {
      border-right: 1px solid #fff;
    }

    & div:last-child {
      border-left: 1px solid #fff;
    }

    .footerBtn {
      flex: 1;
      opacity: 1;
      text-align: center;
      color: #fff;
      cursor: pointer;
    }
  }
}

.uploadBtn {
  display: flex;
  width: 100px;
  height: 100px;
  justify-content: center;
  background: #f5f6fa;
  border-radius: 5px;
  border: 1px solid #dadbe0;
  flex-direction: column;
  cursor: pointer;
}

.textAreaRow {
  height: 140px;
}

.introductionTextArea {
  width: 300px;
  height: 120px;
  resize: none;
}

.editpageHomeBack {
  display: flex;
  width: 180px;
  height: 105px;
  justify-content: center;
  position: relative;
  border-radius: 4px;
  background: #caaaaa;
  cursor: pointer;
  align-items: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
  }
}

.editpageHomeBack > div:nth-child(2) {
  display: none;
  width: 180px;
  height: 105px;
  position: absolute;
  top: 50%;
  right: 0;
  z-index: 100;
  transform: translateY(-50%);
  border-radius: 4px;
  // background: #000000;
  // opacity: 0.5;
  background: rgb(0 0 0 / 50%);
}

.editpageHomeBack:hover > :nth-child(2) {
  display: block;
  width: 180px;
  height: 105px;
  position: absolute;
  top: 50%;
  right: 0;
  z-index: 100;
  transform: translateY(-50%);
  border-radius: 4px;
  background: rgb(0 0 0 / 50%);
}

.editpageHomeBackBtn {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  flex-direction: column;
  align-items: center;
}

.editpageHomeBackBtn > div:nth-child(1) {
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  font-family: PingFangSC-Medium, sans-serif;
  gap: 5px;
}

.editpageHomeBackBtn > div:nth-child(2) {
  color: #fff;
  font-size: 14px;
  display: flex;
  align-items: center;
  font-family: PingFangSC-Medium, sans-serif;
  gap: 5px;
}

.inputTip {
  text-align: right;
  margin-top: 3px;
  margin-right: 5px;
}
