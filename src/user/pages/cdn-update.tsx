import { useState } from 'react';
import { Input, Button, message } from 'antd';
import { cdnUpdate } from '@/apis';
import { Context } from '@/components';

function CdnUpdate() {
  const [loading, setLoading] = useState(false);
  const [fileUrl, setFileUrl] = useState('');
  const [directoryUrl, setDirectoryUrl] = useState('');

  const BASE_URL = 'https://img.huahuabiz.com';

  const onUpdate = (type: string, path: string) => {
    setLoading(true);
    cdnUpdate({
      type,
      path: /^(https?:)\/\//.test(path) ? path : `${BASE_URL}${path}`,
    })
      .then(() => {
        message.success('更新成功');
        if (type === 'directory') {
          setDirectoryUrl('');
        }
        if (type === 'file') {
          setFileUrl('');
        }
      })
      .catch(() => {
        message.success('更新失败');
      })
      .finally(() => {
        setLoading(false);
      });
  };

  return (
    <Context>
      <div className="py-4 px-4">
        <h1 className="mb-4">更新CDN缓存</h1>
        <div className="mb-4">
          <Input
            value={fileUrl}
            placeholder="如：https://img.huahuabiz.com/test.jpg"
            onChange={(e) => {
              setFileUrl(e.target.value);
            }}
            className="mb-1"
          />
          <Button
            type="primary"
            disabled={!fileUrl}
            loading={loading}
            onClick={() => {
              onUpdate('file', fileUrl);
            }}
          >
            更新文件
          </Button>
        </div>
        <div className="mb-4">
          <Input
            value={directoryUrl}
            placeholder="如：https://img.huahuabiz.com/test/"
            onChange={(e) => {
              setDirectoryUrl(e.target.value);
            }}
            className="mb-1"
          />
          <Button
            type="primary"
            disabled={!directoryUrl}
            loading={loading}
            onClick={() => {
              onUpdate('directory', directoryUrl);
            }}
          >
            更新文件夹
          </Button>
        </div>
      </div>
    </Context>
  );
}

export default CdnUpdate;
