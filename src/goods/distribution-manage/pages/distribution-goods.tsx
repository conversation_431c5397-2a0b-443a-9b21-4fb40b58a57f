import { useState, useCallback, useReducer, useMemo, useRef, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  message,
  Pagination,
  Checkbox,
  Dropdown,
  Menu,
  Typography,
  Select,
  Button,
  Popover,
} from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';
import user from '@/store/user';
import { useMount, useDebounceFn } from 'ahooks';
import { usePermission } from '@/hooks';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';

import { Context, Empty, Icon, Modal, SecondaryTab } from '@/components';
import {
  getMcsFinishDistributionList,
  getMcsDistributionList,
  batchMcsAddDistributionGoods,
  batchMcsRemoveDistributionGoods,
  getMcsFinishDistributionPackageList,
  queryRejectCount,
  getPaymentMch,
} from '@/apis';
import type {
  GetMcsFinishDistributionLisParams,
  GetMcsFinishDistributionListResult,
  GetMcsFinishDistributionListPackageResult,
  GetMcsFinishDistributionListPackageParams,
} from '@/apis';
import { testPerm } from '@/utils/permission';
import { formatPrice, createUuid, getQueryString } from '@/utils/utils';
import { AddGoodsDrawer, AddPackageDrawer, FilterDrawer, RejectDrawer } from '../container/index';
import styles from './distribution-goods.module.less';

// import shareTag from '../../service-product/images/share-tag.png';

interface StateType {
  list: GetMcsFinishDistributionListResult[];
  packageList: GetMcsFinishDistributionListPackageResult[];
  total: number;
  pageSize: number;
  pageNo: number;
  status: number | string;
  searchKey: string;
  checkedList: CheckboxValueType[];
  customizeCategoryIdSet: number[];
  customizeCategoryCodeSet: string[];
  indeterminate: boolean;
  checkAll: boolean;
  tenantId: string;
  supplyType: number;
}
type ActionType = {
  type: 'setState';
  payload: Partial<StateType>;
};

const initialState: StateType = {
  list: [],
  packageList: [],
  total: 1,
  pageSize: 10,
  pageNo: 1,
  status: '',
  searchKey: '',
  checkedList: [],
  customizeCategoryIdSet: [],
  customizeCategoryCodeSet: [],
  indeterminate: false,
  checkAll: false,
  tenantId: '',
  supplyType: 1,
};
const reducer = (state: StateType, action: ActionType): StateType => {
  switch (action.type) {
    case 'setState':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};
const { Head } = Context;
const { Paragraph } = Typography;
const CheckboxGroup = Checkbox.Group;

const titles = [
  { title: i18n.t('paymentManage_goodsManage'), to: '/goods/manage/index' },
  i18n.t('paymentManage_distributionManage'),
];
const menuItems = [{ label: i18n.t('paymentManage_cancelDistribution'), key: 'cancel' }];
// const batchItems = [
//   { label: '批量新增关联', key: 'add' },
//   { label: '批量编辑关联', key: 'replace' },
// ];

const sessionKey = 'CURRENT_DISTRIBUTION_GOODS_CHANNEL';

type ChannelOption = { label: string; value: string; supplyType: number; key: string; id: number };

function DistributionGoods() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const imType = searchParams.get('imType');
  const imTenantId = searchParams.get('imTenantId');
  const [loading, setLoading] = useState(false);
  const tabOption = useMemo(
    () => [
      { label: t('paymentManage_goods'), value: 1 },
      { label: t('paymentManage_packageBundle'), value: 2 },
    ],
    [t]
  );
  const [tabActive, setTabActive] = useState<number>(1);
  const [state, dispatch] = useReducer(reducer, initialState);
  const [showAddGoodsVisible, setShowAddGoodsVisible] = useState(false);
  const [showAddPackageVisible, setShowAddPackageVisible] = useState(false);
  const [showFilterDrawer, setShowFilterDrawer] = useState(false);
  const [filterCategory, setFilterCategory] = useState<
    { label: string; value: string | number; code?: string }[]
  >([]);
  const [channelList, setChannelList] = useState<ChannelOption[]>([
    // {
    //   value: 1,
    //   label: '华世界店铺',
    // },
  ]);
  const isFilterActive = useMemo(() => Boolean(filterCategory.length), [filterCategory.length]);
  const customerCatId = useRef<number[]>([]);
  const customerCodes = useRef<string[]>([]);
  const addBtnStatus = useRef(true);

  const [showPopover, setShowPopover] = useState(false);
  const [addGoodsBtnLoading, setAddGoodsBtnLoading] = useState(false);
  const [addPackagesBtnLoading, setAddPackagesBtnLoading] = useState(false);
  const [showRejectVisible, setShowRejectVisible] = useState(false);
  const [showHistoryReject, setShowHistoryReject] = useState(false);
  const [rejectCount, setRejectCount] = useState(0);
  const currentChannel = useRef({
    websiteSize: 0,
    onlyWebsite: { name: '', tenantId: '' },
    supplyType: 1,
  });

  const getPackageList = useCallback((param: GetMcsFinishDistributionListPackageParams) => {
    setLoading(true);
    getMcsFinishDistributionPackageList({
      ...param,
    })
      .then((res) => {
        dispatch({
          type: 'setState',
          payload: {
            packageList: res?.list || [],
            // list: [],
            total: res.pagination.count,
            checkedList: [],
            indeterminate: false,
            checkAll: false,
            ...param,
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // 获取商品数据
  const getGoodsList = useCallback((param: GetMcsFinishDistributionLisParams) => {
    setLoading(true);

    getMcsFinishDistributionList({
      ...param,
    })
      .then((res) => {
        dispatch({
          type: 'setState',
          payload: {
            list: res.list,
            total: res.pagination.count,
            checkedList: [],
            indeterminate: false,
            checkAll: false,
            ...param,
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // 获取商城和店铺列表
  const getChannelList = useCallback(() => {
    const urlTenantId = getQueryString('tenantId') || '';
    let sessionVal = '';
    if (urlTenantId) {
      sessionVal = urlTenantId;
    } else {
      sessionVal = sessionStorage.getItem(sessionKey) || '';
    }
    getMcsDistributionList().then((res) => {
      let tenantId = '';
      let supplyType = 0;
      const list: ChannelOption[] = [];
      res?.list?.forEach((item, index) => {
        if (index === 0) {
          tenantId = item.tenantId;
          supplyType = item.supplyType;
        }
        if (item.tenantId === sessionVal) {
          tenantId = item.tenantId;
          supplyType = item.supplyType;
        }
        list.push({
          label: item.name,
          value: item.tenantId,
          key: createUuid(),
          supplyType: item.supplyType,
          id: item.id,
        });
      });

      setChannelList(list);
      currentChannel.current.supplyType = supplyType || 1;
      // const websiteList = list.filter((item) => item.supplyType === 2);
      // currentChannel.current.websiteSize = websiteList.length;
      // if (websiteList.length === 1) {
      //   currentChannel.current.onlyWebsite = {
      //     name: websiteList[0].label,
      //     tenantId: websiteList[0].value,
      //   };
      // }
      // const tenantId = list.length ? list[0].value : '';
      // const supplyType = list.length ? list[0].supplyType : 0;
      if (tenantId) {
        dispatch({
          type: 'setState',
          payload: {
            tenantId,
            supplyType,
          },
        });
        getGoodsList({
          tenantId,
          searchKey: '',
          pageNo: 1,
          pageSize: 10,
        });
      }
    });
  }, [getGoodsList]);

  const onSearch = useDebounceFn(
    (searchKey: string) => {
      const { tenantId } = state;
      if (tabActive === 1) {
        getGoodsList({
          tenantId,
          searchKey,
          pageNo: 1,
          pageSize: state.pageSize,
        });
      } else {
        getPackageList({
          tenantId,
          searchKey,
          pageNo: 1,
          pageSize: state.pageSize,
        });
      }
    },
    { wait: 500 }
  ).run;

  const cancelDistribution = useCallback(
    (ids: number[] | CheckboxValueType[]) => {
      batchMcsRemoveDistributionGoods({
        tenantId: state.tenantId,
        // @ts-ignore
        dataIds: ids,
        dataType: tabActive,
      }).then(() => {
        message.success(t('distribution_cancel_ougoods'));
        if (tabActive === 1) {
          getGoodsList({
            tenantId: state.tenantId,
            pageNo: state.pageNo,
            pageSize: state.pageSize,
            customerCatId: filterCategory.map((item) => Number(item.value)),
          });
        } else {
          getPackageList({
            tenantId: state.tenantId,
            pageNo: state.pageNo,
            pageSize: state.pageSize,
            customizeCategorySet: filterCategory.map((item) => item.code || ''),
          });
        }
      });
    },
    [
      filterCategory,
      state.tenantId,
      state.pageNo,
      state.pageSize,
      getGoodsList,
      tabActive,
      getPackageList,
      t,
    ]
  );

  // 选中
  const onCheckedList = useCallback(
    (ids: CheckboxValueType[]) => {
      const size = tabActive === 1 ? state.list.length : state.packageList.length;
      dispatch({
        type: 'setState',
        payload: {
          checkedList: ids,
          indeterminate: !!ids.length && ids.length < size,
          checkAll: ids.length === size,
        },
      });
    },
    [state.list.length, state.packageList.length, tabActive]
  );

  // 全选
  const onCheckAll = useCallback(
    (e: CheckboxChangeEvent) => {
      const { checked } = e.target;
      const list = tabActive === 1 ? state.list : state.packageList;
      dispatch({
        type: 'setState',
        payload: {
          checkedList: checked ? list.map((item) => item.id) : [],
          indeterminate: false,
          checkAll: checked,
        },
      });
    },
    [state.list, state.packageList, tabActive]
  );

  // 前往商品详情
  const toDetail = useCallback(({ id, skuSource }) => {
    window.open(`/shop/goods/v1/detail?skuId=${id}&type=${skuSource || 0}&shop=true`);
  }, []);

  // 前往商品详情
  const toPackageDetail = useCallback(({ id }) => {
    window.open(`/shop/goods/package/detail?packageId=${id}&shop=true`);
  }, []);

  // 规格
  const standardStr = useCallback((arr) => {
    let str = '';
    arr.forEach((item: any) => {
      str += `；${item.name}：${item.value}`;
    });
    return str.slice(1);
  }, []);

  // const onAutoDistributionByWebsite = (
  //   tenantId: string,
  //   dataType: number,
  //   shopSkuIds: number[]
  // ) => {
  //   const { onlyWebsite } = currentChannel.current;
  //   return new Promise((resolve, reject) => {
  //     batchMcsAddDistributionGoods({
  //       tenantId,
  //       dataIds: shopSkuIds,
  //       dataType,
  //     })
  //       .then(() => {
  //         resolve('');
  //         const content = (
  //           <div>
  //             已自动为您铺货到 “{onlyWebsite.name}” 站点
  //             {/* <div>不再提示</div> */}
  //           </div>
  //         );
  //         Modal.confirm({
  //           title: '铺货成功',
  //           icon: '',
  //           centered: true,
  //           okText: '确定',
  //           content,
  //           // onOk: () => {
  //           //   testPerm('AU_001');
  //           // },
  //         });
  //       })
  //       .catch(() => {
  //         reject();
  //       });
  //   });
  // };

  const onConfirmAddGoods = (tenantId: string, dataType: number, shopSkuIds: number[]) => {
    // const { supplyType, websiteSize, onlyWebsite } = currentChannel.current;
    // const { supplyType } = currentChannel.current;
    batchMcsAddDistributionGoods({
      tenantId,
      dataIds: shopSkuIds,
      dataType,
    }).then(() => {
      if (dataType === 1) {
        setShowAddGoodsVisible(false);
      } else {
        setShowAddPackageVisible(false);
      }
      message.success(t('add_success'));
      // if (!checkCharge('AU_001') && supplyType === 1) {
      //   const content = (
      //     <div>
      //       您已铺货成功，但是由于您未开通店铺，
      //       <span style={{ color: '#EA1C26' }}>商品将无法上架到商城中显示。</span>
      //       <br />
      //       是否去开通店铺？
      //     </div>
      //   );
      //   Modal.confirm({
      //     title: '提示',
      //     icon: '',
      //     centered: true,
      //     okText: '是',
      //     cancelText: '否',
      //     content,
      //     onOk: () => {
      //       testPerm('AU_001');
      //     },
      //   });
      // }
      // else if (supplyType === 1 && websiteSize === 1) {
      //   onAutoDistributionByWebsite(onlyWebsite.tenantId, dataType, shopSkuIds);
      // }
      // else {
      //   message.success('添加成功！');
      // }
      if (dataType === 1) {
        getGoodsList({
          tenantId: state.tenantId,
          searchKey: '',
          pageNo: 1,
          pageSize: 10,
        });
      } else {
        getPackageList({
          tenantId: state.tenantId,
          searchKey: '',
          pageNo: 1,
          pageSize: 10,
        });
      }
    });
  };

  const onQuickFilterSelectChange = (val: string) => {
    let supplyType = 0;
    channelList.forEach((item) => {
      if (item.value === val) {
        supplyType = item.supplyType;
      }
    });
    state.tenantId = val;
    state.supplyType = supplyType;
    currentChannel.current.supplyType = supplyType;
    sessionStorage.setItem(sessionKey, val);
    if (tabActive === 1) {
      getGoodsList({
        tenantId: val,
        searchKey: '',
        pageNo: 1,
        pageSize: 10,
      });
    } else {
      getPackageList({
        tenantId: val,
        searchKey: '',
        pageNo: 1,
        pageSize: 10,
      });
    }
  };

  const processEmptyText = () => {
    let text = '';
    if (channelList.length) {
      if (isFilterActive) {
        text = t('no_goods_found');
      } else {
        text = t('no_goods_distributed', { type: state.supplyType === 2 ? t('site') : t('mall') });
      }
    } else {
      text = t('no_mall_or_site');
    }
    return text;
  };

  // 批量操作
  // const onHandleBatch = useCallback(
  //   (key: string, domEvent: any) => {
  //     domEvent.stopPropagation();
  //     cancelDistribution(state.checkedList);
  //   },
  //   [state.checkedList]
  // );

  useMount(() => {
    getChannelList();
  });

  const onFilter = usePermission('M_001_010_001_003', () => {
    setShowFilterDrawer(true);
  });

  const onPackageFilter = usePermission('M_001_010_002_003', () => {
    setShowFilterDrawer(true);
  });

  const onClickAddGoods = usePermission('M_001_010_001_002', () => {
    setShowPopover(false);
    if (!state.tenantId) {
      message.warn(t('no_mall_or_site_found'));
      return;
    }
    setShowAddGoodsVisible(true);
    setAddGoodsBtnLoading(false);
  });

  const onClickPackageGoods = usePermission('M_001_010_002_002', () => {
    setShowPopover(false);
    if (!state.tenantId) {
      message.warn(t('no_mall_or_site_found'));
      return;
    }
    setShowAddPackageVisible(true);
    setAddPackagesBtnLoading(false);
  });

  const onCancel = usePermission('M_001_010_001_004', (item) => {
    Modal.confirm({
      title: t('prompt'),
      icon: '',
      centered: true,
      okText: t('yes'),
      cancelText: t('no'),
      content: t('cancel_distribution_warning', {
        type: state.supplyType === 2 ? t('site') : t('mall'),
      }),
      onOk: () => {
        cancelDistribution([item.id]);
      },
    });
  });

  const onBatchCancel = usePermission('M_001_010_001_005_001', () => {
    Modal.confirm({
      title: t('prompt'),
      icon: '',
      centered: true,
      okText: t('yes'),
      cancelText: t('no'),
      content: t('cancel_distribution_warning', {
        type: state.supplyType === 2 ? t('site') : t('mall'),
      }),
      onOk: () => {
        cancelDistribution(state.checkedList);
      },
    });
  });

  const onPackageCancel = usePermission('M_001_010_002_004', (item) => {
    Modal.confirm({
      title: t('prompt'),
      icon: '',
      centered: true,
      okText: t('yes'),
      cancelText: t('no'),
      content: t('cancel_distribution_warning', {
        type: state.supplyType === 2 ? t('site') : t('group_mall'),
      }),
      onOk: () => {
        cancelDistribution([item.id]);
      },
    });
  });

  const onPackageBatchCancel = usePermission('M_001_010_002_005_001', () => {
    Modal.confirm({
      title: t('prompt'),
      icon: '',
      centered: true,
      okText: t('yes'),
      cancelText: t('no'),
      content: t('cancel_distribution_warning', {
        type: state.supplyType === 2 ? t('site') : t('group_mall'),
      }),
      onOk: () => {
        cancelDistribution(state.checkedList);
      },
    });
  });

  const goCompanyAuth = () => {
    Modal.confirm({
      title: t('prompt'),
      icon: '',
      centered: true,
      okText: t('go_auth'),
      cancelText: t('paymentManage_cancel_btn'),
      content: t('complete_company_auth'),
      onOk: () => {
        navigate('/user/company/auth');
      },
    });
  };

  const onClickTabItem = (val: number) => {
    if (val === 1 && !testPerm('M_001_010_001')) {
      return;
    }
    if (val === 2 && !testPerm('M_001_010_002')) {
      return;
    }
    setTabActive(val);
    const { tenantId } = state;
    setFilterCategory([]);
    customerCatId.current = [];
    customerCodes.current = [];
    if (val === 1) {
      getGoodsList({
        tenantId,
        searchKey: '',
        pageNo: 1,
        pageSize: 10,
      });
    } else {
      getPackageList({
        tenantId,
        searchKey: '',
        pageNo: 1,
        pageSize: 10,
      });
    }
    document
      .querySelector('.distributionGoodsHead .ant-input-affix-wrapper .ant-input-clear-icon')
      // @ts-ignore
      ?.click();
  };

  const onLookReject = () => {
    setShowRejectVisible(true);
  };

  const jzbModal = () => {
    Modal.confirm({
      title: t('prompt'),
      icon: '',
      centered: true,
      okText: t('i_know'),
      className: 'jzbModal',
      cancelButtonProps: {
        className: 'displayNone',
      },
      content: t('jzb_modal_content'),
      onOk: () => {},
    });
  };

  const showJzbModal = async () => {
    if (state.tenantId === '5e4cd195-d776-4ea1-80c3-0a138865a7c0') {
      const { companyId } = user;

      // 阻止getPaymentMch重复请求
      if (!addBtnStatus.current) {
        return false;
      }

      addBtnStatus.current = false;
      const mch = await getPaymentMch({
        companyId,
        tenantId: state.tenantId,
      });
      addBtnStatus.current = true;

      // 0未开户,1成功,2失败,3异常
      if (mch.code === 100001 || mch.openStatus === 0) {
        jzbModal();
        return false;
      }
    }
    return true;
  };

  useEffect(() => {
    if (showPopover) {
      window.removeEventListener('click', () => {}, true);
    } else {
      window.addEventListener(
        'click',
        (e: any) => {
          if (!e.target.className.includes('addBtnPopover')) {
            setShowPopover(false);
            window.removeEventListener('click', () => {}, true);
          }
        },
        true
      );
    }
  }, [showPopover]);

  useEffect(() => {
    if (state.tenantId && tabActive) {
      queryRejectCount({ tenantId: state.tenantId, dataType: tabActive }).then((res) => {
        setRejectCount(res.total);
      });
    }
  }, [state.tenantId, tabActive, showRejectVisible, showHistoryReject]);

  useEffect(() => {
    if (imType && imTenantId) {
      setShowHistoryReject(true);
      setTabActive(Number(imType));
    }
  }, [imType, imTenantId]);

  const GoodsListElement = state.list.length ? (
    <div className={styles.body}>
      <div className={styles.header}>
        <Checkbox
          indeterminate={state.indeterminate}
          checked={state.checkAll}
          onChange={onCheckAll}
          className={styles.checkbox}
        />
        <div className={styles.titles}>
          <div className={styles.info}>{t('goods_info')}</div>
          <div className={styles.price}>{t('retail_price')}</div>
          <div className={styles.service}>{t('goods_category')}</div>
          <div className={styles.handle}>{t('operation')}</div>
        </div>
      </div>
      <CheckboxGroup value={state.checkedList} className={styles.list} onChange={onCheckedList}>
        {state.list.map((item) => (
          <div key={item.id} className={styles.item}>
            <div className={styles.itemList}>
              <Checkbox value={item.id} />
              <div
                role="button"
                tabIndex={0}
                className={styles.info}
                onClick={() => {
                  toDetail(item);
                }}
              >
                <div
                  className={classNames(
                    styles.imageWrap
                    // item.status === 10 ? styles.sale : styles.stay
                  )}
                >
                  <img
                    src={item.images?.length ? item.images[0] : ''}
                    alt=""
                    className={styles.image}
                  />
                </div>
                <div className={styles.title}>
                  <Paragraph ellipsis={{ rows: 2 }} className={styles.name}>
                    {/* {item.skuSource === 1 && (
                        <img src={shareTag} alt="" className={styles.tag} />
                      )} */}
                    {item.name}
                  </Paragraph>
                  <Paragraph ellipsis className={styles.standard}>
                    {standardStr(item?.standardList)}
                  </Paragraph>
                  <Paragraph ellipsis className={styles.standard}>
                    {`${t('min_sale_quantity')}: ${item.saleGroup}`}
                  </Paragraph>
                </div>
              </div>
            </div>
            <div className={styles.price}>￥{formatPrice(item.marketPrice)}</div>
            <div className={styles.service}>
              {(item.customizeCategoryList?.map((cateItem) => cateItem.categoryName) || []).join(
                '、'
              )}
            </div>
            <div className={styles.handle}>
              <Dropdown
                overlay={
                  <Menu
                    items={menuItems}
                    onClick={({ domEvent }) => {
                      domEvent.stopPropagation();
                      if (tabActive === 1) {
                        onCancel(item);
                      } else {
                        onPackageCancel(item);
                      }
                    }}
                  />
                }
                placement="bottom"
              >
                <Icon
                  name="zu13366"
                  size={24}
                  color="#999EB2"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                />
              </Dropdown>
            </div>
          </div>
        ))}
      </CheckboxGroup>
      <div className={styles.footer}>
        <div>
          <Checkbox checked={state.checkAll} onChange={onCheckAll}>
            {t('public_selectAll')}
          </Checkbox>
          {!!state.checkedList.length && (
            <>
              <span className={styles.checkedCount}>
                {t('selected_count', { count: state.checkedList.length })}
              </span>
              <span
                className={styles.linkBtn}
                role="presentation"
                onClick={() => {
                  if (tabActive === 1) {
                    onBatchCancel();
                  } else {
                    onPackageBatchCancel();
                  }
                }}
              >
                {t('batch_cancel_distribution')}
              </span>
            </>
          )}
        </div>
        <Pagination
          current={state.pageNo}
          total={state.total}
          pageSize={state.pageSize}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `${t('total_count', { total })}`}
          onChange={(pageNo, pageSize) => {
            getGoodsList({
              tenantId: state.tenantId,
              pageNo,
              pageSize,
              status: state.status,
              customizeCategorySet: customerCodes.current,
              // customerCatId: customerCatId.current,
              searchKey: state.searchKey,
            });
          }}
        />
      </div>
    </div>
  ) : (
    <Empty message="" description={processEmptyText()} className={styles.empty}>
      {isFilterActive || !channelList.length ? (
        ''
      ) : (
        <Button
          type="primary"
          onClick={() => {
            if (!showJzbModal()) {
              return;
            }
            if (user.company.status === 1) {
              onClickAddGoods();
            } else {
              goCompanyAuth();
            }
          }}
        >
          {t('add_goods')}
        </Button>
      )}
    </Empty>
  );

  const PackageListElement = state.packageList.length ? (
    <div className={styles.body}>
      <div className={styles.header}>
        <Checkbox
          indeterminate={state.indeterminate}
          checked={state.checkAll}
          onChange={onCheckAll}
          className={styles.checkbox}
        />
        <div className={styles.titles}>
          <div className={styles.info}>{t('paymentManage_goodsInfo')}</div>
          <div className={styles.price}>{t('specification_single_retail_price')}</div>
          <div className={styles.service}>{t('goods_category')}</div>
          <div className={styles.handle}>{t('operation')}</div>
        </div>
      </div>
      <CheckboxGroup value={state.checkedList} className={styles.list} onChange={onCheckedList}>
        {state.packageList.map((item) => (
          <div key={item.id} className={styles.item}>
            <div className={styles.itemList}>
              <Checkbox value={item.id} />
              <div
                role="button"
                tabIndex={0}
                className={styles.info}
                onClick={() => {
                  toPackageDetail(item);
                }}
              >
                <div
                  className={classNames(
                    styles.imageWrap
                    // item.status === 10 ? styles.sale : styles.stay
                  )}
                >
                  <img
                    src={item.imagesList?.length ? item.imagesList[0] : ''}
                    alt=""
                    className={styles.image}
                  />
                </div>
                <div className={styles.title}>
                  <Paragraph ellipsis={{ rows: 2 }} className={styles.name}>
                    {/* {item.skuSource === 1 && (
                      <img src={shareTag} alt="" className={styles.tag} />
                    )} */}
                    {item.name}
                  </Paragraph>

                  <Paragraph ellipsis className={styles.standard}>
                    {item.introduce}
                  </Paragraph>
                </div>
              </div>
            </div>
            <div className={styles.price}>￥{formatPrice(item.marketPrice)}</div>
            <div className={styles.service}>{item.catCustomizeName}</div>
            <div className={styles.handle}>
              <Dropdown
                overlay={
                  <Menu
                    items={menuItems}
                    onClick={({ domEvent }) => {
                      domEvent.stopPropagation();
                      if (tabActive === 1) {
                        onCancel(item);
                      } else {
                        onPackageCancel(item);
                      }
                    }}
                  />
                }
                placement="bottom"
              >
                <Icon
                  name="zu13366"
                  size={24}
                  color="#999EB2"
                  onClick={(e) => {
                    e.stopPropagation();
                    e.preventDefault();
                  }}
                />
              </Dropdown>
            </div>
          </div>
        ))}
      </CheckboxGroup>
      <div className={styles.footer}>
        <div>
          <Checkbox checked={state.checkAll} onChange={onCheckAll}>
            {t('public_selectAll')}
          </Checkbox>
          {!!state.checkedList.length && (
            <>
              <span className={styles.checkedCount}>
                {t('selected_count', { count: state.checkedList.length })}
              </span>
              <span
                className={styles.linkBtn}
                role="presentation"
                onClick={() => {
                  if (tabActive === 1) {
                    onBatchCancel();
                  } else {
                    onPackageBatchCancel();
                  }
                }}
              >
                {t('batch_cancel_distribution')}
              </span>
            </>
          )}
        </div>
        <Pagination
          current={state.pageNo}
          total={state.total}
          pageSize={state.pageSize}
          showSizeChanger
          showQuickJumper
          showTotal={(total) => `${t('total_count', { total })}`}
          onChange={(pageNo, pageSize) => {
            getPackageList({
              tenantId: state.tenantId,
              pageNo,
              pageSize,
              searchKey: state.searchKey,
              customizeCategorySet: customerCodes.current,
              status: state.status,
            });
          }}
        />
      </div>
    </div>
  ) : (
    <Empty message="" description={processEmptyText()} className={styles.empty}>
      {isFilterActive || !channelList.length ? (
        ''
      ) : (
        <Button
          type="primary"
          onClick={() => {
            if (user.company.status === 1) {
              onClickPackageGoods();
            } else {
              goCompanyAuth();
            }
          }}
        >
          {t('add_package')}
        </Button>
      )}
    </Empty>
  );

  return (
    <Context
      permission={{ code: 'M_001_010_001', newLogic: true }}
      className="distributionGoodsContent"
      head={
        <div className="distributionGoodsHead">
          <Head
            title={titles}
            isFilterActive={isFilterActive}
            // quickFilter={QuickFilter}
            onSearch={(val) => {
              onSearch(val);
            }}
            otherBtn={() => {
              setShowHistoryReject(true);
            }}
            onFilter={() => {
              if (tabActive === 1) {
                onFilter();
              } else {
                onPackageFilter();
              }
            }}
            placeholder={t('distributionSearchPlaceholder')}
            extra={
              <div className={styles.addBtn}>
                <Popover
                  placement="bottom"
                  overlayClassName={classNames(styles.addBtnPopover, 'addBtnPopover')}
                  visible={showPopover}
                  content={
                    <div className={styles.addBtnPopoverContent}>
                      <div
                        className={styles.addBtnPopoverItem}
                        role="presentation"
                        onClick={() => {
                          if (user.company.status === 1) {
                            onClickAddGoods();
                          } else {
                            goCompanyAuth();
                          }
                        }}
                      >
                        <div className={styles.addBtnPopoverItemText}>{t('add_goods')}</div>
                        <img
                          src="https://img.huahuabiz.com/user_files/1670998654917606077/gou.png"
                          className={styles.addBtnPopoverItemIcon}
                          alt=""
                        />
                      </div>
                      <div
                        className={styles.addBtnPopoverItem}
                        role="presentation"
                        onClick={() => {
                          if (user.company.status === 1) {
                            onClickPackageGoods();
                          } else {
                            goCompanyAuth();
                          }
                        }}
                      >
                        <div className={styles.addBtnPopoverItemText}>{t('add_package')}</div>
                        <img
                          src="https://img.huahuabiz.com/user_files/1670998654917606077/gou.png"
                          className={styles.addBtnPopoverItemIcon}
                          alt=""
                        />
                      </div>
                    </div>
                  }
                  trigger="click"
                >
                  <Button
                    type="primary"
                    size="small"
                    onClick={async () => {
                      if (!showJzbModal()) {
                        return;
                      }
                      setShowPopover(true);
                    }}
                  >
                    {t('add')}
                  </Button>
                </Popover>
              </div>
            }
          />
        </div>
      }
      loading={loading}
    >
      {channelList.length ? (
        <>
          <div className={styles.filterBar}>
            <div className={styles.filterBarLeft}>
              {t('my_distribution_channels')}
              <Select
                className={styles.filterBarLeftSelect}
                value={!state.tenantId ? null : state.tenantId}
                showSearch
                // optionFilterProp="children"
                // filterOption={(input, option) =>
                //   (option?.label ?? '')?.toLowerCase().includes(input.toLowerCase())
                // }
                // filterOption={(input, option) =>
                //   option?.props.children[0].toLowerCase().indexOf(input.toLowerCase()) >= 0
                // }
                filterOption={(input, option) => {
                  const title =
                    option?.props.children.props.children[1].props.children.toLowerCase();
                  return title.indexOf(input.toLowerCase()) >= 0;
                }}
                // options={channelList}
                onChange={onQuickFilterSelectChange}
              >
                {channelList.map((channelItem) => (
                  <Select.Option value={channelItem.value} key={channelItem.key}>
                    <div className={styles.channelItem}>
                      <div
                        className={classNames(
                          styles.channelItemTag,
                          channelItem.supplyType === 1 ? styles.storeTag : styles.websiteTag
                        )}
                      >
                        {channelItem.supplyType === 1 ? t('paymentManage_groupMall') : t('site')}
                      </div>
                      <div className={styles.channelItemText}>{channelItem.label}</div>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </div>
            <div className={styles.filterBarRight}>
              <SecondaryTab
                options={tabOption}
                value={tabActive}
                // size="small"
                onClickItem={(val) => {
                  onClickTabItem(Number(val.value));
                }}
              />
            </div>
          </div>
          {rejectCount > 0 ? (
            <div className={styles.rejectBox}>
              <Icon name="warn-sold" color="#f9ae08" size={18} />
              <div className={styles.rejectText}>
                {t('reject_count_message', { count: rejectCount })}
              </div>
              <div
                className={styles.lookDetails}
                tabIndex={-1}
                role="button"
                onClick={() => onLookReject()}
              >
                {t('view_details')}
                <Icon name="right" color="#008cff" size={18} />
              </div>
            </div>
          ) : null}
          {tabActive === 1 ? GoodsListElement : PackageListElement}
        </>
      ) : (
        <div>{tabActive === 1 ? GoodsListElement : PackageListElement}</div>
      )}

      <AddGoodsDrawer
        visible={showAddGoodsVisible}
        tenantId={state.tenantId}
        okBtnLoading={addGoodsBtnLoading}
        onClose={() => {
          setShowAddGoodsVisible(false);
        }}
        onOk={(ids) => {
          setAddGoodsBtnLoading(true);
          onConfirmAddGoods(state.tenantId, 1, ids);
        }}
      />

      <AddPackageDrawer
        visible={showAddPackageVisible}
        tenantId={state.tenantId}
        okBtnLoading={addPackagesBtnLoading}
        onClose={() => {
          setShowAddPackageVisible(false);
        }}
        onOk={(ids) => {
          setAddPackagesBtnLoading(true);
          onConfirmAddGoods(state.tenantId, 2, ids);
        }}
      />

      <FilterDrawer
        visible={showFilterDrawer}
        checked={filterCategory}
        onClose={() => {
          setShowFilterDrawer(false);
        }}
        onOk={(values) => {
          setFilterCategory(values);
          const ids = values.map((item) => Number(item.value));
          const codes = values.map((item) => item.code || '');
          customerCatId.current = ids;
          customerCodes.current = codes;
          if (tabActive === 1) {
            getGoodsList({
              tenantId: state.tenantId,
              searchKey: '',
              pageNo: 1,
              pageSize: 10,
              // customerCatId: ids,
              customizeCategorySet: codes,
            });
          } else {
            getPackageList({
              tenantId: state.tenantId,
              searchKey: '',
              pageNo: 1,
              pageSize: 10,
              customizeCategorySet: codes,
              // customerCatId: ids,
            });
          }
          setShowFilterDrawer(false);
        }}
      />

      <RejectDrawer
        visible={showRejectVisible}
        tenantId={imTenantId || state.tenantId}
        dataType={tabActive}
        setVisible={setShowRejectVisible}
        isAllhistory={false}
      />
      <RejectDrawer
        visible={showHistoryReject}
        tenantId={state.tenantId}
        dataType={tabActive}
        setVisible={setShowHistoryReject}
        isAllhistory
      />
    </Context>
  );
}

DistributionGoods.displayName = 'DistributionGoods';

export default DistributionGoods;
