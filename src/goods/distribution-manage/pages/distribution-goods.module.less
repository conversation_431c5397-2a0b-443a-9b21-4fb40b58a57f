@import 'styles/mixins/mixins';

.body {
  width: 100%;
  height: calc(100% - 60px);
  padding: 20px 20px 0;
  padding-top: 28px;
}

.quickFilter {
  color: #888b98;
  display: flex;
  align-items: center;

  :global {
    .ant-select-selector {
      background-color: transparent !important;
      border: 0 !important;
      color: #040919 !important;
      box-shadow: initial !important;
    }

    .ant-select-arrow {
      color: #888b98 !important;
    }
    // .ant-select-focused{
    //   box-shadow: initial !important;
    // }
  }
}

.filterBar {
  display: flex;
  align-items: center;
  min-height: 40px;
  padding: 20px 20px 0;

  .filterBarLeft {
    flex: 1;
    margin-right: 40px;

    .filterBarLeftSelect {
      width: 361px;
      margin-left: 12px;
    }
  }
}

.header {
  display: flex;
  height: 38px;
  padding-left: 20px;
  border-radius: 8px;
  background: #fafafc;
  align-items: center;
}

.titles {
  display: flex;
  align-items: center;
  flex-grow: 1;
  justify-content: space-between;
}

.list {
  width: 100%;
  height: calc(100% - 90px);
  overflow-y: auto;

  :global {
    .ant-checkbox-wrapper-checked {
      background-color: rgb(217 238 255 / 50%);
    }
  }
}

.item {
  display: flex;
  min-width: 800px;
  height: 96px;
  padding-left: 20px;
  justify-content: space-between;
  align-items: center;
  border-radius: 10px;

  &:hover {
    background-color: rgb(217 238 255 / 30%);
  }
}

.itemList {
  display: flex;
  align-items: center;
  height: 96px;
}

.info {
  display: flex;
  width: 300px;
  padding-left: 8px;
  cursor: pointer;
}

.price {
  width: 200px;
  text-align: center;
}

.service {
  width: 200px;
  text-align: center;
}

.serviceName {
  max-width: 130px !important;
}

.handle {
  width: 68px;
  text-align: center;
}

.title {
  display: flex;
  // justify-content: space-between;
  width: 220px;
  padding-left: 8px;
  flex-direction: column;
}

.standard {
  color: #888b98;
  font-size: 12px;
  margin-top: 4px;
}

.tag {
  width: 28px;
  height: 15px;
  vertical-align: text-top;
  margin-right: 4px;
}

.image {
  width: 64px;
  height: 64px;
  object-fit: contain;
  border-radius: 12px;
}

.serviceItem + .serviceItem {
  margin-top: 4px;
}

.noService {
  color: #888b98;
}

.serviceTotal {
  color: #888b98;
  margin-left: 4px;
}

.moreService {
  color: #888b98;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.imageWrap {
  overflow: hidden;
  position: relative;

  &::before {
    color: #fff;
    font-size: 12px;
    display: flex;
    width: 100%;
    height: 30%;
    padding: 6px 2px 2px;
    justify-content: center;
    position: absolute;
    top: 4%;
    left: -30%;
    transform: scale(0.8) rotate(-45deg);
    align-items: center;
  }
}

.sale::before {
  background: linear-gradient(119.47deg, rgb(50 209 108 / 85%), rgb(17 212 173 / 85%));
}

.stay::before {
  background: linear-gradient(118.96deg, rgb(242 143 44 / 85%), rgb(250 205 57 / 85%));
}

.footer {
  display: flex;
  height: 48px;
  padding: 10px 0 10px 20px;
  justify-content: space-between;
  align-items: center;
}

.checkedCount {
  font-size: 14px;
  margin: 0 12px;
}

.linkBtn {
  cursor: pointer;
  color: #008cff;
  font-size: 14px;
}

.empty {
  padding-top: 150px;
}

.addBtn {
  height: 28px;
  padding-left: 12px;
}

.addBtnPopover {
  :global {
    .ant-popover-inner-content {
      padding: 0 !important;
    }
  }
}

.addBtnPopoverContent {
  width: 200px;
  border-radius: 10px;
  padding: 8px 16px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .addBtnPopoverItem {
    display: flex;
    height: 32px;
    align-items: center;
    cursor: pointer;

    &Icon {
      display: none;
      width: 16px;
      height: 16px;
    }

    &:hover {
      color: @color1;

      .addBtnPopoverItemIcon {
        display: block;
      }
    }

    &Text {
      flex: 1;
    }
  }
}

.channelItemTag {
  font-size: 12px;
  height: 20px;
  line-height: 20px;
  margin-right: 4px;
  margin-left: 4px;
  border-radius: 2px;
  text-align: center;
}

.channelItemText {
  width: 277px;
  .text-overflow();
}

.storeTag {
  background: #d9eeff;
  color: #008cff;
  width: 44px;
}

.websiteTag {
  color: #05d380;
  background: #d0efe2;
  width: 32px;
}

.channelItem {
  display: flex;
  width: 100%;
  align-items: center;
}

.rejectBox {
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding-left: 20px;

  .warningIcon {
    width: 16px;
    height: 16px;
  }

  .rejectText {
    margin-left: 8px;
  }

  .rejectNumber {
    color: #ea1c26;
  }

  .lookDetails {
    color: #008cff;
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}

:global {
  .jzbModal {
    .displayNone {
      display: none;
    }
  }
}
