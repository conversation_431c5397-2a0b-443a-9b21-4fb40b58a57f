@import 'styles/mixins/mixins';

.addChildGoodsDrawer {
  z-index: 999;

  :global {
    .ant-drawer-mask {
      opacity: 0 !important;
      animation: initial !important;
    }

    .ant-drawer-header {
      background-color: #fff;
    }

    .ant-drawer-body {
      padding: 0 !important;
    }
  }
}

.packIcon {
  margin-right: 6px;
}

.spin {
  width: 100%;
  height: 100%;

  :global {
    .ant-spin-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
    }
  }
}

.drawerHead {
  padding: 8px 20px 12px;
  background-color: #fff;
  border-bottom: 1px solid #f3f3f3;

  .searchInput {
    background: #f5f6fa;
  }

  :global {
    .ant-input {
      background: #f5f6fa;
    }
  }
}

.drawerContent {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  flex: 1;

  &Left {
    width: 82px;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none !important;
    }

    &Scroll {
      width: 82px;
    }

    .itemActive {
      background-color: #fff;
    }

    .item,
    .itemActive {
      width: 82px;
      padding: 20px 10px;
      text-align: center;
      cursor: pointer;
      .text-overflow();

      &:hover {
        background-color: #fff;
      }
    }
  }

  &Right {
    display: flex;
    height: 100%;
    background: white;
    flex-direction: column;
    flex: 1;

    .secondaryCategory {
      display: flex;
      padding: 12px 20px 0 12px;
      justify-content: center;
      position: relative;
      background-color: #fff;

      &Content {
        flex: 1;
      }

      &Operate {
        color: #040919;
        display: flex;
        width: 20px;
        height: 20px;
        justify-content: center;
        border-radius: 50%;
        background: #f5f6fa;
        cursor: pointer;
        align-items: center;
      }

      &More {
        width: 100%;
        padding: 0 40px 10px 12px;
        position: absolute;
        top: 44px;
        left: 0;
        z-index: 9999;
        border-radius: 0 0 18px 18px;
        background: #fff;
        box-shadow: 0 8px 6px 0 rgb(0 0 0 / 5%);
      }

      &Item {
        display: inline-block;
        max-width: 110px;
        margin-right: 12px;
        padding: 5px 12px;
        border-radius: 10px;
        background: #f3f3f3;
        cursor: pointer;
        .text-overflow();

        &Active {
          color: #008cff;
          background: #d9eeff;
          border: 1px solid #008cff;
        }

        &:last-child {
          margin-right: 0;
        }
      }
    }

    &List {
      flex: 1;
      overflow: auto;
      position: relative;
    }
  }
}

.goodsItem {
  display: flex;
  width: 100%;
  height: 80px;
  margin-top: 20px;
  padding: 0 20px 0 12px;
  align-items: center;
  border-radius: 10px;
}

.imageBox {
  display: flex;
  width: 80px;
  height: 80px;
  overflow: hidden;
  justify-content: center;
  border-radius: 10px;
  align-items: center;
}

.goodsItemInfo {
  display: flex;
  width: 164px;
  height: 80px;
  padding-left: 12px;
  justify-content: space-between;
  flex-direction: column;
}

div.goodsItemInfoName {
  line-height: 22px;
}

.goodsItemInfoStandard {
  color: #888b98;
  font-size: 12px;
}

.drawerFooter {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 0 0 16px 16px;
  background: #fff;
}

.drawerFooterText {
  flex: 1;
  font-size: 14px;
}

.drawerFooterNumber {
  color: @primary-color;
  font-size: 18px;
  font-weight: 600;
  margin-left: 4px;
}

.drawerFooterCheck {
  height: 32px;
  flex: 1;
  margin-right: 17px;

  .drawerFooterCheckText {
    margin-left: 8px;
  }
}

.endMessage {
  color: #888b98;
  font-size: 12px;
}
