.rejectBody {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto;

  :global {
    .infinite-scroll-component {
      overflow-x: hidden !important;
    }
  }
}

.rejectBox {
  height: 100%;
  overflow: auto;
}

.goodsCard {
  min-height: 241px;
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .rejectTime {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .goodsBox {
    display: flex;
    margin-bottom: 16px;
    padding-bottom: 19px;
    border-bottom: 1px solid #f3f3f3;

    .photoBox {
      width: 90px;
      height: 90px;
      margin-right: 12px;
    }

    .infoBox {
      flex: 1;
      color: #888b98;

      .goodsName {
        color: #040919;
        width: 200px;
        height: 22px;
        line-height: 22px;
        margin-bottom: 4px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .tag {
        height: 18px;
        line-height: 18px;
        margin-bottom: 4px;
      }

      .price {
        color: #ea1c26;
        height: 22px;
        line-height: 22px;
      }
    }
  }
}

.photo {
  border-radius: 12px;
}

.rejectCause {
  .rejectLable {
    color: #888b98;
  }
}

.empty {
  margin-top: 50%;
}
