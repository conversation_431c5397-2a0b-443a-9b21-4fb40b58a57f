import { useCallback, useEffect, useRef, useState } from 'react';
import { useThrottleFn } from 'ahooks';
import { Spin } from 'antd';
import type { DrawerProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, FilterCard } from '@/components';
import type { FilterItemType, FilterCardRefType } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import CategoryFilter from '@/src/stock/pages/query/filter-category';
import { testPerm } from '@/utils/permission';
import { getCustomCategoryList } from '@/apis';
import type { GetCustomCategoryListResult } from '@/apis';
import styles from './filter-drawer.module.less';

interface OptionType {
  label: string;
  value: number | string;
  isHidden?: boolean;
  code?: string;
}

interface PropsType extends DrawerProps {
  checked?: OptionType[];
  onOk: MultipleParamsFn<[values: OptionType[]]>;
}

function FilterDrawer({ visible, checked, onOk, ...props }: PropsType) {
  const { t } = useTranslation();
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const filterRef1 = useRef(null as unknown as FilterCardRefType);
  const [loading, setLoading] = useState(false);
  const [allCategoryData, setAllCategoryData] = useState<GetCustomCategoryListResult[]>([]);
  const [showCategoryFilter, setShowCategoryFilter] = useState(false);
  const [categoryFilterWrap, setCategoryFilterWrap] = useState<HTMLElement | null>(null);
  const [checkedList, setCheckedList] = useState<OptionType[]>([]);
  const [filterOptions1, setFilterOptions1] = useState<FilterItemType[]>([
    {
      label: t('paymentManage_goodsCategory'),
      defaultValues: [''],
      key: 'customizeCategoryIdSet',
      type: 'checkbox',
      collapsible: 'disabled',
      showCheckedLabels: true,
      // hideChildren: true,
      // placeholder: '请选择分类',
      showMoreItem: true,
      moreItemText: t('paymentManage_more'),
      children: [],
    },
  ]);

  // 设置默认选中
  const setDefaulteValues = () => {};

  // 规格选项
  const getData = () => {
    setLoading(true);
    getCustomCategoryList({ pageNo: 1, pageSize: 1000000 })
      .then((res) => {
        const categoryData: OptionType[] = [];
        const showCategoryData: OptionType[] = [];

        res.list.forEach((item) => {
          if (item.grade === 1) {
            showCategoryData.push({
              label: item.categoryName,
              value: item.id,
            });
          }
          categoryData.push({
            label: item.categoryName,
            value: item.id,
            isHidden: item.grade !== 1 || showCategoryData.length > 5,
            code: item.code || '',
          });
        });
        // const categoryData: OptionType[] = res.list.map((item) => ({
        //   label: item.categoryName,
        //   value: item.id,
        //   isHidden: item.grade !== 1,
        // }));
        setAllCategoryData(res?.list || []);

        filterOptions1[0].children = categoryData;
        filterOptions1[0].defaultValues = checkedList.map((checkedItem) => checkedItem.value);
        setFilterOptions1([...filterOptions1]);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 重置筛选条件
  const onReset = useCallback(() => {
    filterRef1.current.onReset();
    filterOptions1[0].defaultValues = [];
    setFilterOptions1([...filterOptions1]);
    setCheckedList([]);
  }, [filterOptions1]);

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      onOk(checkedList);
    },
    { wait: 3000 }
  ).run;

  // 显示自定义分类
  const onShowCategoryFilter = useCallback(() => {
    setShowCategoryFilter(true);
  }, []);

  // 自定义分类
  const onSetCustomizeCategory = useCallback(
    (ids, arr) => {
      const list: GetCustomCategoryListResult[] = [];
      allCategoryData.forEach((cateItem) => {
        arr.forEach((checkedItem: any) => {
          if (cateItem.id === checkedItem.id) {
            list.push(cateItem);
          }
        });
      });
      filterOptions1[0].defaultValues = ids;
      // filterOptions1[0].children = list.map((item: any) => ({
      //   label: item.categoryName,
      //   value: item.id,
      //   isHidden: Boolean(item.parentId),
      // }));
      const newCheckedList = list.map(
        (item: { categoryName: string; id: number; code?: string }) => ({
          label: item.categoryName,
          value: item.id,
          code: item.code || '',
        })
      );
      setCheckedList([...newCheckedList]);
      setFilterOptions1([...filterOptions1]);
      setShowCategoryFilter(false);
    },
    [filterOptions1, allCategoryData]
  );

  const onPanelItemClick = (value: OptionType, active: boolean) => {
    if (active) {
      checkedList.push(value);
    } else {
      for (let i = 0; i < checkedList.length; i += 1) {
        const checkedItem = checkedList[i];
        if (checkedItem.value === value.value) {
          checkedList.splice(i, 1);
          i -= 1;
        }
      }
    }
    setCheckedList([...checkedList]);
  };

  useEffect(() => {
    if (visible) {
      if (!testPerm('M_001_012_001')) return;
      setDefaulteValues();
      getData();
      setTimeout(() => {
        setCategoryFilterWrap(drawerRef.current.getBodyElem());
      }, 0);
    } else setShowCategoryFilter(false);
  }, [visible]); // eslint-disable-line

  useEffect(() => {
    setCheckedList(checked || []);
  }, [checked]);

  return (
    <Drawer
      ref={drawerRef}
      title={t('filter')}
      visible={visible}
      footer={
        <Drawer.Footer
          cancelText={t('reset')}
          onOk={() => {
            onConfirm();
          }}
          okText={t('public_confirm')}
          onCancel={() => {
            onReset();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <FilterCard
          ref={filterRef1}
          list={filterOptions1}
          onSelectClick={onShowCategoryFilter}
          onMoreItemClick={onShowCategoryFilter}
          onPanelItemClick={onPanelItemClick}
        />
        {!!categoryFilterWrap && (
          <CategoryFilter
            visible={showCategoryFilter}
            defaultChecked={checkedList.map((item) => ({
              id: item.value,
              categoryName: item.label,
            }))}
            cateGoryId={filterOptions1[0].defaultValues || []}
            getContainer={categoryFilterWrap}
            onCloseCategory={() => {
              setShowCategoryFilter(false);
            }}
            setCateGoryId={onSetCustomizeCategory}
          />
        )}
      </Spin>
    </Drawer>
  );
}

FilterDrawer.displayName = 'FilterDrawer';

FilterDrawer.defaultProps = {
  checked: [],
};

export default FilterDrawer;
