import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Spin } from 'antd';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { Drawer, Empty } from '@/components';
import { formatPrice } from '@/utils/utils';
import { queryGoodsRejectList, queryPackageRejectList, queryHistoryRejectList } from '@/apis';
import { RejectResultType } from '@/apis/mcs/query-goods-reject-list';
import { HistoryRejectResultType } from '@/apis/mcs/query-history-reject-list';
import styles from './reject-drawer.module.less';

interface RejectType {
  visible: boolean;
  setVisible: Dispatch<SetStateAction<boolean>>;
  tenantId: string;
  dataType: number;
  isAllhistory: boolean;
}

function RejectDrawer(props: RejectType) {
  const { visible, setVisible, tenantId, dataType, isAllhistory } = props;
  const { t } = useTranslation();

  const [rejectList, setRejectList] = useState([] as RejectResultType[]);
  const [historyRejectList, setHistoryRejectList] = useState([] as HistoryRejectResultType[]);

  const [load, setLoad] = useState(false);
  const [pageCount, setPageCount] = useState(1);
  const [page, setPage] = useState(2);

  // 页面数据初始化
  const initialPageList = () => {
    // 所有历史驳回商品及商品包
    if (isAllhistory) {
      queryHistoryRejectList({ tenantId, pageNo: 1, pageSize: 10 }).then((res) => {
        setPageCount(Math.ceil(res.pagination.count / 10));
        setHistoryRejectList(res.list);
      });
      return;
    }
    // 商品或者商品包
    if (dataType === 1) {
      queryGoodsRejectList({
        tenantId,
        pageNo: 1,
        pageSize: 10,
      }).then((res) => {
        setPageCount(Math.ceil(res.pagination.count / 10));
        setRejectList(res.list);
      });
    } else {
      queryPackageRejectList({ tenantId, pageNo: 1, pageSize: 10 }).then((res) => {
        setPageCount(Math.ceil(res.pagination.count / 10));
        setRejectList(res.list);
      });
    }
  };

  const onGetRejectList = () => {
    setLoad(true);
    if (isAllhistory) {
      queryHistoryRejectList({ tenantId, pageNo: page, pageSize: 10 }).then((res) => {
        setLoad(false);
        setPageCount(Math.ceil(res.pagination.count / 10));
        setPage(page + 1);
        setHistoryRejectList((val) => val?.concat(res.list));
      });
      return;
    }

    if (dataType === 1) {
      queryGoodsRejectList({ tenantId, pageNo: 1, pageSize: 10 }).then((res) => {
        setLoad(false);
        setPageCount(Math.ceil(res.pagination.count / 10));
        setPage(2);
        setRejectList((val) => val?.concat(res.list));
      });
    } else {
      queryPackageRejectList({ tenantId, pageNo: 1, pageSize: 10 }).then((res) => {
        setLoad(false);
        setPageCount(Math.ceil(res.pagination.count / 10));
        setPage(2);
        setRejectList((val) => val?.concat(res.list));
      });
    }
  };

  const onClose = () => {
    setVisible(false);
    setPageCount(1);
    setPage(2);
  };

  const onLoadMore = () => {
    onGetRejectList();
  };

  useEffect(() => {
    if (tenantId && dataType && visible) {
      initialPageList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [tenantId, dataType, visible]);

  return (
    <Drawer title={t('reject_record')} visible={visible} onClose={onClose} placement="right">
      <div id="rejectBody" className={styles.rejectBox}>
        <InfiniteScroll
          dataLength={isAllhistory ? historyRejectList.length : rejectList.length}
          hasMore={page <= pageCount}
          loader={
            load ? (
              <div className="text-center">
                <Spin tip={t('loading')} />
              </div>
            ) : null
          }
          next={onLoadMore}
          scrollableTarget="rejectBody"
        >
          {isAllhistory ? (
            <div>
              {historyRejectList?.length > 0 ? (
                historyRejectList?.map((item) => (
                  <div className={styles.goodsCard}>
                    <div className={styles.rejectTime}>
                      {dayjs(
                        item.supplyType === 1 ? item.skuVO.createTime : item.packageVO.createTime
                      ).format(t('date_format'))}
                    </div>
                    <div className={styles.goodsBox}>
                      <div className={styles.photoBox}>
                        <img
                          className={styles.photo}
                          src={
                            item.supplyType === 1
                              ? item.skuVO.imageList?.[0]
                              : item.packageVO.imagesList?.[0]
                          }
                          alt=""
                        />
                      </div>
                      <div className={styles.infoBox}>
                        <div className={styles.goodsName}>
                          {item.supplyType === 1 ? item.skuVO.name : item.packageVO.name}
                        </div>
                        <div className={styles.tag}>
                          {item.supplyType === 1
                            ? item.skuVO.standardList?.map((every) => every.value).join(' ')
                            : item.packageVO.introduce}
                        </div>
                        {item.supplyType === 1 ? (
                          <div className={styles.tag}>
                            最小销售单元 {item.skuVO.saleGroup}
                            {item.skuVO.unit}
                          </div>
                        ) : null}
                        <div className={styles.price}>
                          ¥{' '}
                          {formatPrice(
                            item.supplyType === 1
                              ? item.skuVO.marketPrice
                              : item.packageVO.marketPrice
                          )}{' '}
                        </div>
                      </div>
                    </div>
                    <div className={styles.rejectCause}>
                      <span className={styles.rejectLable}>{t('supplier_rejection_reason')} </span>{' '}
                      {item.supplyType === 1 ? item.skuVO.remark : item.packageVO.remark}
                    </div>
                  </div>
                ))
              ) : (
                <Empty message={t('no_data')} className={styles.empty} />
              )}
            </div>
          ) : (
            <div>
              {rejectList?.length > 0 ? (
                rejectList?.map((item) => (
                  <div className={styles.goodsCard}>
                    <div className={styles.rejectTime}>
                      {dayjs(item.createTime).format(t('date_format'))}
                    </div>
                    <div className={styles.goodsBox}>
                      <div className={styles.photoBox}>
                        <img
                          className={styles.photo}
                          src={dataType === 1 ? item.imageList?.[0] : item.imagesList?.[0]}
                          alt=""
                        />
                      </div>
                      <div className={styles.infoBox}>
                        <div className={styles.goodsName}>{item.name}</div>
                        <div className={styles.tag}>
                          {dataType === 1
                            ? item.standardList?.map((every) => every.value).join(' ')
                            : item.introduce}
                        </div>
                        {dataType === 1 ? (
                          <div className={styles.tag}>
                            {t('paymentManage_minSaleUnit')} {item.saleGroup}
                            {item.unit}
                          </div>
                        ) : null}
                        <div className={styles.price}>¥ {formatPrice(item.marketPrice)} </div>
                      </div>
                    </div>
                    <div className={styles.rejectCause}>
                      <span className={styles.rejectLable}>{t('supplier_rejection_reason')}</span>{' '}
                      {item.remark}
                    </div>
                  </div>
                ))
              ) : (
                <Empty message={t('no_data')} className={styles.empty} />
              )}
            </div>
          )}
        </InfiniteScroll>
      </div>
    </Drawer>
  );
}

export default RejectDrawer;
