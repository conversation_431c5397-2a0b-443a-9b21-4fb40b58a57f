import { useEffect, useState, useRef, useCallback } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import classNames from 'classnames';
import flattenDeep from 'lodash/flattenDeep';
import findIndex from 'lodash/findIndex';
// import uniq from 'lodash/uniq';
import { Button, Checkbox, Spin, Divider, Typography } from 'antd';
import type { DrawerProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, Empty, Search, Icon, Price } from '@/components';
import { checkCharge, checkPermission } from '@/utils/permission';
import { getCategoryCustomeList, getMcsDistributionSkuByMall } from '@/apis';
import type { GetCategoryCustomeItemResult, GetMcsDistributionSkuByMallResult } from '@/apis';
import type { RelationCustomizeType } from '@/apis/sku-goods-list';
import styles from './add-goods-drawer.module.less';

interface PropsType extends DrawerProps {
  tenantId: string;
  okBtnLoading: boolean;
  // selectedGoods: RelationCustomizeType[];
  // eslint-disable-next-line no-unused-vars
  onOk: (shopSkuIdSet: number[], values: RelationCustomizeType[]) => void;
}

interface GoodsParams {
  pageNo: number;
  pageSize: number;
  customizeCategoryIdSet: number[];
  customizeCategoryCodeSet: string[];
  searchKey: string;
  totalPages: number;
}

const { Paragraph } = Typography;

function AddGoodsDrawer({ visible, tenantId, okBtnLoading, onOk, ...props }: PropsType) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [categoryList, setCategoryList] = useState<GetCategoryCustomeItemResult[]>([]);
  const [subCategoryList, setSubCategoryList] = useState<GetCategoryCustomeItemResult[]>([]);
  const [categoryId, setCategoryId] = useState(0);
  const [subCategoryId, setSubCategoryId] = useState(0);
  const [categoryHasMore, setCategoryHasMore] = useState(false);
  const [showSubCategory, setShowSubCategory] = useState(false);
  const [goodsList, setGoodsList] = useState<GetMcsDistributionSkuByMallResult[]>([]);
  const [goodsHasMore, setGoodsHasMore] = useState(false);
  const [checkedIds, setCheckedIds] = useState<number[]>([]);
  const [checkedItems, setCheckedItems] = useState<RelationCustomizeType[]>([]);
  const categoryParams = useRef({ grade: 1, pageNo: 1, pageSize: 20, totalPages: 1 });
  const goodsParams = useRef<GoodsParams>({
    pageNo: 1,
    pageSize: 10,
    totalPages: 1,
    customizeCategoryIdSet: [],
    customizeCategoryCodeSet: [],
    searchKey: '',
  });
  const [subIndex, setSubIndex] = useState(2);
  const [checkAll, setCheckAll] = useState(false);

  // 获取分类
  const getCategoryData = () => {
    getCategoryCustomeList({ ...categoryParams.current }).then((res) => {
      if (categoryParams.current.pageNo === 1) {
        res.list.unshift({
          id: 0,
          categoryName: '全部',
          code: '',
        });
      }
      categoryParams.current.totalPages = Math.ceil(
        res.pagination.count / categoryParams.current.pageSize
      );
      setCategoryHasMore(categoryParams.current.pageNo < categoryParams.current.totalPages);
      setCategoryList([...categoryList, ...res.list]);
    });
  };

  // 滚动加载分类
  const loadMoreCategory = () => {
    categoryParams.current.pageNo += 1;
    if (categoryParams.current.pageNo > categoryParams.current.totalPages) return;
    getCategoryData();
  };

  // 获取服务商品
  const getGoodsData = (data: GetMcsDistributionSkuByMallResult[] = []) => {
    const { pageNo, pageSize, searchKey, customizeCategoryCodeSet } = goodsParams.current;
    getMcsDistributionSkuByMall({
      pageNo,
      pageSize,
      searchKey,
      customizeCategoryCodeSet,
      tenantId,
    }).then((res) => {
      goodsParams.current.totalPages = res.pagination.total;
      setGoodsHasMore(goodsParams.current.pageNo < goodsParams.current.totalPages);
      const newGoodsList = [...data, ...res.list];
      if (newGoodsList.length && checkedIds.length) {
        const currentCheckedGoodsList = newGoodsList.filter((goodsItem) =>
          checkedIds.includes(goodsItem.id)
        );
        setCheckAll(currentCheckedGoodsList.length === newGoodsList.length);
      } else {
        setCheckAll(false);
      }

      setGoodsList(newGoodsList);
    });
  };

  // 滚动加载服务商品
  const loadMoreGoodsData = () => {
    goodsParams.current.pageNo += 1;
    if (goodsParams.current.pageNo > goodsParams.current.totalPages) return;
    getGoodsData(goodsList);
  };

  // 搜索
  const onSearch = (val: string) => {
    goodsParams.current.searchKey = val;
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryIdSet = [];
    goodsParams.current.customizeCategoryCodeSet = [];
    setShowSubCategory(false);
    setSubCategoryId(0);
    setCategoryId(0);
    setSubCategoryList([]);
    getGoodsData();
  };

  // 选择一级分类
  const onFirstCategory = (item: GetCategoryCustomeItemResult) => {
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryIdSet = item.id ? [item.id] : [];
    goodsParams.current.customizeCategoryCodeSet = item.code ? [item.code] : [];
    setShowSubCategory(false);
    setSubCategoryId(0);
    getGoodsData();
    if (!item.id) {
      setCategoryId(0);
      setSubCategoryList([]);
      return;
    }
    setCategoryId(item.id);
    getCategoryCustomeList({
      pageNo: 1,
      pageSize: 999,
      parentId: item.id,
    }).then((res) => {
      const reg = /[\u4e00-\u9fa5]/g;
      let width = 0;
      let index = 0;
      for (let i = 0; i < res.list.length; i += 1) {
        const cateItem = res.list[i];
        if (cateItem.categoryName) {
          const chineseNum = cateItem?.categoryName?.match(reg)?.length || 0;
          const englishNum = cateItem.categoryName.length - chineseNum;
          width += chineseNum * 18.5 + englishNum * 9.5 + 12;
          if (width > 241) {
            index = i;
            break;
          }
        }
      }
      setSubIndex(index || res.list.length);
      setSubCategoryList(res.list);
    });
  };

  // 选择二级分类
  const onSecondaryCategory = (item: GetCategoryCustomeItemResult) => {
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryIdSet = [item.id as number];
    goodsParams.current.customizeCategoryCodeSet = [item.code as string];
    setSubCategoryId(item.id as number);
    getGoodsData();
  };

  const onCheckAllChange = () => {
    const val = !checkAll;
    setCheckAll(val);
    if (val) {
      const shopSkuIdList: number[] = [];
      goodsList.forEach((item) => {
        if (!checkedIds.includes(item.id)) {
          checkedIds.push(item.id);
          shopSkuIdList.push(item.id);
        }
        const i = findIndex(checkedItems, ['customizeCatId', item.categoryId]);
        if (i === -1) {
          checkedItems.push({
            customizeCatId: item.categoryId,
            customizeCategoryName: item.categoryName,
            shopSkuIdList: [item.id],
          });
        } else {
          const checkedItem = checkedItems[i];
          if (!checkedItem.shopSkuIdList.includes(item.id)) {
            checkedItem.shopSkuIdList.push(item.id);
          }
        }
      });
    } else {
      goodsList.forEach((item) => {
        const index = checkedIds.indexOf(item.id);
        if (index !== -1) {
          checkedIds.splice(index, 1);
          for (let i = 0; i < checkedItems.length; i += 1) {
            const checkedItem = checkedItems[i];
            if (checkedItem.customizeCatId === item.categoryId) {
              checkedItems.splice(i, 1);
              i -= 1;
            }
          }
        }
      });
    }
    setCheckedIds([...checkedIds]);
    setCheckedItems([...checkedItems]);
  };

  // 选择
  const onChecked = (item: GetMcsDistributionSkuByMallResult) => {
    const { categoryName, id } = item;
    const i = findIndex(checkedItems, ['customizeCatId', item.categoryId]);
    if (i !== -1) {
      const checkedItem = checkedItems[i];
      const { shopSkuIdList } = checkedItem;
      const j = shopSkuIdList.indexOf(id);
      if (j !== -1) {
        shopSkuIdList.splice(j, 1);
        if (!shopSkuIdList.length) {
          checkedItems.splice(i, 1);
        }
      } else {
        shopSkuIdList.push(id);
      }
    } else {
      checkedItems.push({
        customizeCatId: item.categoryId,
        customizeCategoryName: categoryName,
        shopSkuIdList: [id],
      });
    }
    const ids = flattenDeep(checkedItems.map((checkItem) => checkItem.shopSkuIdList));

    // 当前商品列表已选中的项
    const currentCheckedGoodsList = goodsList.filter((goodsItem) => ids.includes(goodsItem.id));
    setCheckAll(currentCheckedGoodsList.length === goodsList.length);

    setCheckedIds(ids);
    setCheckedItems(checkedItems);
  };

  useEffect(() => {
    setCheckAll(false);
    setCheckedIds([]);
    setCheckedItems([]);
    if (visible) {
      if (checkCharge('M_001_012_001') && checkPermission('M_001_012_001')) {
        getCategoryData();
      }
      getGoodsData();
      // setCheckedIds(uniq(flattenDeep(selectedGoods.map((item) => item.shopSkuIdList))));
      // setCheckedItems(selectedGoods);
    } else {
      setLoading(false);
      setCategoryList([]);
      setSubCategoryList([]);
      setGoodsList([]);
      setCategoryId(0);
      setSubCategoryId(0);
      setShowSubCategory(false);
      categoryParams.current = { grade: 1, pageNo: 1, pageSize: 20, totalPages: 1 };
      goodsParams.current = {
        pageNo: 1,
        pageSize: 10,
        totalPages: 1,
        customizeCategoryIdSet: [],
        customizeCategoryCodeSet: [],
        searchKey: '',
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 规格
  const standardStr = useCallback((arr) => {
    let str = '';
    arr.forEach((item: any) => {
      str += ` ${item.value}`;
    });
    return str.slice(1);
  }, []);

  return (
    <Drawer
      visible={visible}
      title={t('add_goods')}
      className={styles.addChildGoodsDrawer}
      footer={
        <div className={styles.drawerFooter}>
          <div className={styles.drawerFooterCheck}>
            <Checkbox
              defaultChecked={false}
              onChange={() => {
                onCheckAllChange();
              }}
              checked={checkAll}
            >
              {t('select_all')}
            </Checkbox>
            {/* <span className={styles.drawerFooterCheckText}>全选</span> */}
          </div>
          {/* <div className={styles.drawerFooterText}>
            已选商品<span className={styles.drawerFooterNumber}>{checkedIds.length}</span>
          </div> */}
          <Button
            type="primary"
            disabled={!checkedIds.length}
            loading={okBtnLoading}
            onClick={() => {
              // useDebounce(onOk(checkedIds, checkedItems), { wait: 500 });
              onOk(checkedIds, checkedItems);
            }}
          >
            {t('add_comfirm_goods', { count: checkedIds.length })}
          </Button>
        </div>
      }
      {...props}
    >
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <div className={styles.drawerHead}>
          <Search
            realTimeSearch
            placeholder={t('search_goods')}
            className={styles.searchInput}
            onSearch={onSearch}
          />
        </div>
        <div className={styles.drawerContent}>
          <div className={styles.drawerContentLeft} id="drawerContentLeft">
            <InfiniteScroll
              dataLength={categoryList.length}
              className={styles.drawerContentLeftScroll}
              hasMore={categoryHasMore}
              loader={
                <div className="text-center">
                  <Spin tip={t('loading')} />
                </div>
              }
              next={loadMoreCategory}
              scrollableTarget="drawerContentLeft"
            >
              {categoryList.map((item) => (
                <div
                  role="button"
                  tabIndex={0}
                  key={item.id}
                  className={classNames(categoryId === item.id ? styles.itemActive : styles.item)}
                  onClick={() => {
                    onFirstCategory(item);
                  }}
                >
                  {item.categoryName}
                </div>
              ))}
            </InfiniteScroll>
          </div>
          <div className={styles.drawerContentRight}>
            {!!subCategoryList.length && (
              <div className={styles.secondaryCategory}>
                <div className={styles.secondaryCategoryContent}>
                  {subCategoryList.slice(0, subIndex).map((item) => (
                    <span
                      role="button"
                      tabIndex={item.id || 0}
                      key={item.id}
                      title={item.categoryName}
                      className={classNames(
                        styles.secondaryCategoryItem,
                        subCategoryId === item.id ? styles.secondaryCategoryItemActive : ''
                      )}
                      onClick={() => {
                        onSecondaryCategory(item);
                      }}
                    >
                      {item.categoryName}
                    </span>
                  ))}
                </div>
                {subCategoryList.length > subIndex ? (
                  <div
                    className={styles.secondaryCategoryOperate}
                    role="presentation"
                    onClick={() => {
                      setShowSubCategory(!showSubCategory);
                    }}
                  >
                    <Icon name={showSubCategory ? 'up' : 'down'} size={16} />
                  </div>
                ) : (
                  ''
                )}
                {showSubCategory ? (
                  <div className={styles.secondaryCategoryMore}>
                    <div className={styles.secondaryCategoryContent}>
                      {subCategoryList.slice(subIndex).map((item) => (
                        <span
                          role="button"
                          tabIndex={item.id || 0}
                          key={item.id}
                          title={item.categoryName}
                          className={classNames(
                            styles.secondaryCategoryItem,
                            subCategoryId === item.id ? styles.secondaryCategoryItemActive : '',
                            showSubCategory ? 'mt-2' : ''
                          )}
                          onClick={() => {
                            onSecondaryCategory(item);
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </div>
            )}
            <div className={styles.drawerContentRightList} id="drawerContentRightList">
              {goodsList.length ? (
                <InfiniteScroll
                  dataLength={goodsList.length}
                  className={styles.drawerContentRightScroll}
                  hasMore={goodsHasMore}
                  loader={
                    <div className="text-center">
                      <Spin tip={t('loading')} />
                    </div>
                  }
                  endMessage={
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>{t('channel_load_end')}</span>
                      </Divider>
                    </div>
                  }
                  next={loadMoreGoodsData}
                  scrollableTarget="drawerContentRightList"
                >
                  {goodsList.map((item) => (
                    <div className={styles.goodsItem} key={item.id}>
                      <div className={styles.imageBox}>
                        <img
                          src={item?.images?.length ? item.images[0] : ''}
                          className={styles.goodsItemImage}
                          alt=""
                        />
                      </div>
                      <div className={styles.goodsItemInfo}>
                        <div>
                          <Paragraph
                            ellipsis={{ rows: 2 }}
                            className={styles.goodsItemInfoName}
                            title={item.name}
                          >
                            {item.name}
                          </Paragraph>
                          <Paragraph ellipsis className={styles.goodsItemInfoStandard}>
                            {standardStr(item.standardList)}
                          </Paragraph>
                          <Paragraph ellipsis className={styles.goodsItemInfoStandard}>
                            {`${t('batchSaleUnitDrawer_minSaleUnit')} ${item.saleGroup}`}
                          </Paragraph>
                        </div>
                        <div className={styles.goodsItemInfoStandard}>
                          <Price value={item.marketPrice} />
                          <span> /{item.unit}</span>
                        </div>
                      </div>
                      <Checkbox
                        checked={checkedIds.includes(item.id)}
                        onChange={() => {
                          onChecked(item);
                        }}
                      />
                    </div>
                  ))}
                </InfiniteScroll>
              ) : (
                <Empty />
              )}
            </div>
          </div>
        </div>
      </Spin>
    </Drawer>
  );
}

AddGoodsDrawer.displayName = 'AddGoodsDrawer';

AddGoodsDrawer.defaultProps = {};

export default AddGoodsDrawer;
