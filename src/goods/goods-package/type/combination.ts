import type {
  GetMcsPackageSpuSkuListResult,
  GetMcsPackageSpuListResult,
  GetMcsPackageDetailResSku,
  AddMcsPackageParams,
} from '@/apis';

export type NewGetMcsPackageSpuSkuListResult = GetMcsPackageSpuSkuListResult & {
  productId?: number;
  quantity: number | string;
  unitList: { label: string; value: string }[];
  status?: number;
  groupDetailStatus?: number;
};

export interface NewGetMcsPackageDetailResSku extends GetMcsPackageDetailResSku {
  id?: number;
  showReplace?: boolean;
  imageList?: string[];
  active?: boolean;
  popoverVisible?: boolean;
  isError?: boolean;
  status?: number;
  groupDetailStatus?: number;
  invalidCount?: number;
  lowerShelfCount?: number;
  groupId?: number;
  isGift?: number;
  replaceList?: NewGetMcsPackageDetailResSku[];
  specificationGoodsList?: NewGetMcsPackageSpuSkuListResult[];
}

export type NewGetMcsPackageSpuListResult = GetMcsPackageSpuListResult & {
  productId?: number;
  specificationGoodsList?: NewGetMcsPackageSpuSkuListResult[];
  combinationData?: NewGetMcsPackageDetailResSku[];
};

export type NewAddMcsPackageParams = AddMcsPackageParams;

export type GoodsPackageDataStorage = AddMcsPackageParams & {
  combinationInfo: { goodsData: NewGetMcsPackageDetailResSku[] };
};
