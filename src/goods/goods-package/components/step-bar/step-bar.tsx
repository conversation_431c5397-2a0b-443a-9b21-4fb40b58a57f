import { forwardRef, useImperativeHandle, useRef, useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import styles from './step-bar.module.less';

interface StepBarPropsDataItem {
  status: number; // 0：未选中，1：选中，2:禁用
  text: string;
  url?: string;
}

export interface StepBarProps {
  data: StepBarPropsDataItem[];
  onClick?: MultipleParamsFn<[item: StepBarPropsDataItem, index: number]>;
}

const StepBar = forwardRef(({ data, onClick }: StepBarProps, ref) => {
  const navigate = useNavigate();
  const stepBarRef = useRef<HTMLDivElement | null>(null);
  const stepBarCanvasRef = useRef<HTMLCanvasElement | null>(null);
  const baseItemOffset = 17;
  const [ctx, setCtx] = useState<CanvasRenderingContext2D | null>(null);
  const canvasWidthRef = useRef(0);
  const [canvasWidth, stepBarCanvasWidth] = useState(700);

  const createCanvasStyle = (type: number, text: string) => {
    const styleObject = { strokeStyle: '#e3e2e2', fillStyle: '#000' };
    const fontObject = { text, x: 37, y: 25, fillStyle: '#fff' };
    switch (type) {
      case 1:
        styleObject.strokeStyle = '#008CFF';
        styleObject.fillStyle = '#008CFF';
        fontObject.fillStyle = '#fff';
        break;
      case 0:
        styleObject.strokeStyle = '#F5F6FA';
        styleObject.fillStyle = '#f5f6f7';
        fontObject.fillStyle = '#888B98';
        break;
      case 2:
        // styleObject.strokeStyle = 'rgba(0, 140, 255, 0.5)';
        styleObject.fillStyle = 'rgba(0, 140, 255, 0.5)';
        fontObject.fillStyle = '#fff';
        break;
      default:
        styleObject.strokeStyle = 'rgba(247,57,78,0.70)';
        styleObject.fillStyle = 'rgba(247,57,78,0.10)';
        fontObject.fillStyle = '#F7394E';
        break;
    }
    return { styleObject, fontObject };
  };

  // 第一个
  const drawFirstItem = useCallback(
    (
      styleObject = {
        strokeStyle: 'rgba(247,57,78,0.70)',
        fillStyle: 'rgba(247,57,78,0.10)',
      },
      fontObject = {
        fillStyle: '#F7394E',
        text: '',
        // x: 37,
        // y: 20,
      }
    ) => {
      if (ctx) {
        const baseItemWidth =
          (canvasWidthRef.current - (data.length - 1) * baseItemOffset) / data.length;
        ctx.beginPath();
        ctx.moveTo(12, 0);
        ctx.lineTo(baseItemWidth, 0);
        ctx.lineTo(baseItemWidth + baseItemOffset, 20);
        ctx.lineTo(baseItemWidth, 40);
        ctx.lineTo(12, 40);
        ctx.arcTo(0, 40, 0, 28, 12);
        ctx.lineTo(0, 28);
        ctx.lineTo(0, 12);
        ctx.arcTo(0, 0, 12, 0, 12);
        ctx.lineTo(12, 0);
        ctx.closePath();
        ctx.strokeStyle = styleObject.strokeStyle || 'rgba(247,57,78,0.70)';
        ctx.stroke();
        ctx.fillStyle = styleObject.fillStyle || 'rgba(247,57,78,0.10)';
        ctx.fill();
        ctx.font = fontObject.font || '14px bold Microsoft YaHei, Microsoft YaHei-Bold';
        ctx.fillStyle = fontObject.fillStyle || '#F7394E';
        ctx.textAlign = 'center';
        ctx.fillText(fontObject.text, baseItemWidth / 2, 25);
      }
    },
    [ctx, data]
  );

  // 第二个
  const drawArrow = useCallback(
    (
      offset = 150,
      styleObject = {
        strokeStyle: 'rgba(247,57,78,0.70)',
        fillStyle: 'rgba(247,57,78,0.10)',
      },
      fontObject = {
        fillStyle: '#F7394E',
        text: '',
        // x: 37,
        // y: 25,
      }
    ) => {
      if (ctx) {
        const baseItemWidth =
          (canvasWidthRef.current - (data.length - 1) * baseItemOffset) / data.length;
        ctx.beginPath();
        ctx.moveTo(0 + offset, 0);
        ctx.lineTo(baseItemWidth + offset, 0);
        ctx.lineTo(baseItemWidth + baseItemOffset + offset, 20);
        ctx.lineTo(baseItemWidth + offset, 40);
        ctx.lineTo(0 + offset, 40);
        ctx.lineTo(15 + offset, 20);
        ctx.lineTo(0 + offset, 0);
        ctx.closePath();
        ctx.strokeStyle = styleObject.strokeStyle || 'rgba(247,57,78,0.70)';
        ctx.stroke();
        ctx.fillStyle = styleObject.fillStyle || 'rgba(247,57,78,0.10)';
        ctx.fill();
        ctx.font = fontObject.font || '14px bold Microsoft YaHei, Microsoft YaHei-Bold';
        ctx.fillStyle = fontObject.fillStyle || '#F7394E';
        ctx.textAlign = 'center';
        ctx.fillText(fontObject.text, baseItemWidth / 2 + offset, 25);
        // ctx.fillText(fontObject.text, fontObject.x + offset, fontObject.y);
      }
    },
    [ctx, data]
  );

  // 最后一个
  const drawLastItem = useCallback(
    (
      offset,
      styleObject = {
        strokeStyle: 'rgba(247,57,78,0.70)',
        fillStyle: 'rgba(247,57,78,0.10)',
      },
      fontObject = {
        fillStyle: '#F7394E',
        text: '',
        x: 37,
        y: 25,
      }
    ) => {
      if (ctx) {
        const baseItemWidth =
          (canvasWidthRef.current - (data.length - 1) * baseItemOffset) / data.length + 17;
        ctx.beginPath();
        ctx.moveTo(0 + offset, 0);
        ctx.lineTo(baseItemWidth + offset - 12, 0);
        ctx.arcTo(baseItemWidth + offset, 0, baseItemWidth + offset, 12, 12);
        ctx.lineTo(baseItemWidth + offset, 12);
        ctx.lineTo(baseItemWidth + offset, 28);
        ctx.arcTo(baseItemWidth + offset, 40, baseItemWidth + offset - 12, 40, 12);
        ctx.lineTo(0 + offset, 40);
        ctx.lineTo(15 + offset, 20);
        ctx.lineTo(0 + offset, 0);
        ctx.closePath();
        ctx.strokeStyle = styleObject.strokeStyle || 'rgba(247,57,78,0.70)';
        ctx.stroke();
        ctx.fillStyle = styleObject.fillStyle || 'rgba(247,57,78,0.10)';
        ctx.fill();
        ctx.font = fontObject.font || '14px bold Microsoft YaHei, Microsoft YaHei-Bold';
        ctx.fillStyle = fontObject.fillStyle || '#F7394E';
        // ctx.fillText(fontObject.text, fontObject.x + offset, fontObject.y);
        ctx.textAlign = 'center';
        ctx.fillText(fontObject.text, baseItemWidth / 2 + offset, 25);
      }
    },
    [ctx, data]
  );

  const drawStepBar = useCallback(() => {
    const styleArr = data.map((item) => createCanvasStyle(item.status, item.text));
    const offset = (canvasWidthRef.current - (data.length - 1) * baseItemOffset) / data.length + 5;
    data.forEach((_item, i) => {
      if (i === 0) {
        drawFirstItem(styleArr[i].styleObject, styleArr[i].fontObject);
      } else if (i === data.length - 1) {
        drawLastItem(offset * i, styleArr[i].styleObject, styleArr[i].fontObject);
      } else {
        drawArrow(offset * i, styleArr[i].styleObject, styleArr[i].fontObject);
      }
    });
  }, [data, drawArrow, drawFirstItem, drawLastItem]);

  // 刷新画布
  const refreshCanvas = () => {
    const width = stepBarCanvasRef?.current?.offsetWidth || 0;
    const height = stepBarCanvasRef?.current?.offsetHeight || 0;
    ctx?.clearRect(0, 0, width, height);
  };

  const getCanvasWidth = () => {
    let width = 0;
    if (stepBarRef?.current?.offsetWidth) {
      width = (stepBarRef?.current?.offsetWidth || 50) - 50;
      canvasWidthRef.current = width;
    }
    return width;
  };

  window.onresize = () => {
    stepBarCanvasWidth(getCanvasWidth());
    refreshCanvas();
    drawStepBar();
  };

  useEffect(() => {
    if (stepBarCanvasRef.current) {
      setCtx(stepBarCanvasRef.current.getContext('2d'));
      stepBarCanvasWidth(getCanvasWidth());
    }
  }, []);

  useEffect(() => {
    if (ctx) {
      drawStepBar();
    }
  }, [ctx, drawStepBar]);

  useImperativeHandle(ref, () => ({
    refreshCanvas,
    drawStepBar,
  }));

  return (
    <div className={styles.stepBar} ref={stepBarRef}>
      <canvas
        id="stepBarCanvas"
        ref={stepBarCanvasRef}
        width={canvasWidth}
        height="40"
        onClick={(e) => {
          const baseItemWidth =
            (canvasWidthRef.current - (data.length - 1) * baseItemOffset) / data.length + 17;
          const { offsetX } = e.nativeEvent;
          const index = Math.ceil(offsetX / baseItemWidth) - 1;
          const item = data[index];
          if (onClick) {
            onClick!(data[index], index);
          } else if (item?.url) {
            navigate(item?.url);
          }
        }}
      />
    </div>
  );
});

StepBar.displayName = 'StepBar';
StepBar.defaultProps = {
  onClick: () => {},
};

export default StepBar;
