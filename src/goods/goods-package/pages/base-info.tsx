import { useRef, useMemo, useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Form, Input, message, Spin, Select } from 'antd';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useTranslation } from 'react-i18next';

import {
  getBaseGoodsCategoryList,
  customerCategoryList,
  getMcsPackageDetail,
  getMcsPackageGroupDetail,
} from '@/apis';
import type {
  // GetMcsPackageDetailRes,
  AddMcsPackageParamsCollocation,
  AddMcsPackageParamsCollocationSku,
} from '@/apis';
import { Context, Icon, ImageCropper, ImageCropperHandle } from '@/components';
import { UploadVideo } from '@/components/upload/index';
import { setStorage, getStorage, removeStorage } from '@/utils/storage';
import { usePermission } from '@/hooks';
import { checkCharge, checkPermission } from '@/utils/permission';

import { StepBar } from '../components/index';
import { ImageDragDrop, SearchSelect } from '../container/add/index';
import type { GoodsPackageDataStorage } from '../type/index';
import { goodsPackageStorageKey } from '../config/index';
import { NewGetMcsPackageDetailResSku } from '../type/combination';

// import type { StepBarProps } from '../components/index';
import styles from './base-info.module.less';

export interface FormItemLayout {
  labelCol: { span: number };
  wrapperCol: { span: number };
}

interface SelectOptionItem {
  label: string;
  value: number | string;
}

export default function GoodsPackageBaseInfo() {
  const { t } = useTranslation();
  const [urlParams] = useSearchParams();
  const goodsId = urlParams.getAll('goodsId')[0];
  const isBack = urlParams.getAll('isBack')[0];
  const navigate = useNavigate();
  const packageData = useRef<GoodsPackageDataStorage | null>(null);
  // const packageDetail = useRef<GetMcsPackageDetailRes | null>(null);
  const imageCropperRef = useRef<ImageCropperHandle>(null);
  const stepBarData = useMemo(
    () => [
      {
        text: t('base_basicInfo'),
        status: 1,
        url: goodsId ? `/goods-package/base-info?goodsId=${goodsId}` : '/goods-package/base-info',
      },
      {
        text: t('base_combinationDetail'),
        status: 0,
        url: goodsId
          ? `/goods-package/combination?goodsId=${goodsId}`
          : '/goods-package/combination',
      },
      {
        text: t('base_graphicDetail'),
        status: 0,
        url: goodsId
          ? `/goods-package/graphic-desc?goodsId=${goodsId}`
          : '/goods-package/graphic-desc',
      },
    ],
    [goodsId, t]
  );

  // const [visible, setVisible] = useState(false);
  const [currentImage, setCurrentImage] = useState('');
  // const [goodsPackageData, setGoodsPackageData] = useState({ imageList: [] });
  const [imageList, setImageList] = useState(['', '', '', '', '', '']);
  const currentImageIndex = useRef(0);
  const combinationInfo = useRef<{ goodsData: NewGetMcsPackageDetailResSku[] }>({ goodsData: [] });
  const detailList = useRef<{ imgUrl: string; text: string }[]>([]);
  const [allCategoryData, setAllCategoryData] = useState<SelectOptionItem[]>([]);
  // const [videoFiles, setVideoFiles] = useState<FileListItem[]>([]);
  const [videoList, setVideoList] = useState<{ image: string; url: string }[]>([]);
  const [uploadLoading, setUploadLoading] = useState<boolean>(false);
  const [visibleMarketPriceInput, setVisibleMarketPriceInput] = useState<boolean>(false);

  // form
  const formItemLayout: FormItemLayout = {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  };
  const [form] = Form.useForm();
  const formInitialValues = {
    name: '',
    introduce: '',
    category: '',
    calculationType: 1,
    marketPrice: '',
  };
  const calculationTypeOptions = [
    { label: t('base_sumOfGoodsPrices'), value: 1 },
    { label: t('base_fixedPrice'), value: 2 },
  ];

  const onCropper = () => {
    imageCropperRef?.current?.fileInputClick();
  };

  const changeImageListPosition = (dragIndex: number, hoverIndex: number) => {
    const data = [...imageList];
    const temp = data[dragIndex];
    // 交换位置
    data[dragIndex] = data[hoverIndex];
    data[hoverIndex] = temp;
    setTimeout(() => {
      setImageList(data);
    }, 0);
  };

  const categoryParams = useRef({ pageNo: 1, pageSize: 10, categoryName: '', totalPage: 1 });

  const onFake = (
    type: string,
    val: string,
    data: SelectOptionItem[],
    callback: MultipleParamsFn<[data: { label: string; value: string | number }[]]>
  ) => {
    let newData = data;
    if (type === 'search') {
      categoryParams.current = { pageNo: 1, pageSize: 10, categoryName: val, totalPage: 1 };
      newData = [];
    } else {
      categoryParams.current.pageNo += 1;
      categoryParams.current.categoryName = val;
    }
    const { pageNo, pageSize, categoryName, totalPage } = categoryParams.current;
    if (pageNo > totalPage) {
      return;
    }
    getBaseGoodsCategoryList({ pageNo, pageSize, categoryName }).then((res) => {
      categoryParams.current.totalPage = res.pagination.total;
      let categoryData = [...newData];
      const optionData = res?.list?.map((item) => ({
        label: item.categoryName || '',
        value: item.code || '',
      }));
      categoryData = categoryData.concat(optionData);
      callback(categoryData);
    });
  };

  const createPostDataStorage = (url: string) => {
    form.validateFields().then(() => {
      const formData = form.getFieldsValue();
      const images = imageList.filter((item) => item);
      if (!images.length) {
        message.error(t('package_image_required'));
        return;
      }
      const { goodsData } = combinationInfo.current;
      const collocationSku: AddMcsPackageParamsCollocation[] = [];
      goodsData.forEach((item, index) => {
        const collocationSkuList: AddMcsPackageParamsCollocationSku[] = [];
        item.specificationGoodsList?.forEach((skuItem) => {
          collocationSkuList.push({
            dataId: skuItem.id,
            isGift: skuItem.isGift || 0,
            quantity: Number(skuItem.quantity || 0),
            unit: skuItem.unit,
          });
        });
        (item.replaceList || []).forEach((replaceItem) => {
          replaceItem.specificationGoodsList?.forEach((skuItem) => {
            collocationSkuList.push({
              dataId: skuItem.id,
              isGift: skuItem.isGift || 0,
              quantity: Number(skuItem.quantity || 0),
              unit: skuItem.unit,
            });
          });
        });
        collocationSku.push({ level: index + 1, isGift: 0, skuList: collocationSkuList });
      });

      const postData = {
        ...formData,
        imageList: images,
        type: 1,
        operationType: 0,
        detailList: detailList.current || [],
        combinationInfo: combinationInfo.current,
        collocationSku,
        videoList,
      };
      setStorage(goodsPackageStorageKey, postData);
      navigate(url);
    });
  };

  const onCustomerCategory = usePermission('M_001_012_001');
  const onNext = usePermission('M_001_005_002', () => {
    createPostDataStorage(
      goodsId ? `/goods-package/combination?goodsId=${goodsId}` : '/goods-package/combination'
    );
  });

  const onClickUploadVideo = usePermission('M_001_005_004', () => {});

  const getPackageSpuSkuData = useCallback(() => {
    if (goodsId) {
      const id = Number(goodsId);
      getMcsPackageGroupDetail({ id }).then((res) => {
        const newGoodsData: NewGetMcsPackageDetailResSku[] = [];
        res.list?.forEach((item) => {
          if (item.replaceSpuList?.length) {
            const newAllGoodsArr: NewGetMcsPackageDetailResSku[] = item.replaceSpuList?.map(
              (spuItem) => ({
                productId: spuItem.productId,
                groupId: spuItem.groupId,
                images: spuItem.images,
                name: spuItem.name,
                isMainSpu: spuItem.isMainSpu || 1,
                replaceCount: spuItem.replaceCount,
                standardTotal: spuItem.standardTotal,
                checkStandardTotal: spuItem.checkStandardTotal,
                replaceList: [],
                active: false,
                popoverVisible: false,
                status: spuItem.status,
                invalidCount: spuItem.invalidCount,
                lowerShelfCount: spuItem.lowerShelfCount,
                isGift: item.isGift || 0,
                specificationGoodsList: (spuItem.skuList || []).map((skuItem) => ({
                  ...skuItem,
                  isGift: item.isGift || 0,
                  quantity: skuItem.quantity || skuItem.saleGroup,
                  unitList: [{ label: skuItem.unit, value: skuItem.unit }],
                })),
              })
            );
            // 第一个是spu，剩下的都是替换商品
            if (newAllGoodsArr.length > 1) {
              const arr = [...newAllGoodsArr];
              arr.splice(0, 1);
              newAllGoodsArr[0].replaceList = arr;
            }
            if (newAllGoodsArr.length) {
              newGoodsData.push(newAllGoodsArr[0]);
            }
          }
        });
        combinationInfo.current.goodsData = newGoodsData;
      });
    }
  }, [goodsId]);

  useEffect(() => {
    if (!goodsId || isBack) {
      return;
    }
    getMcsPackageDetail({ id: Number(goodsId) }).then((res) => {
      form.setFieldsValue({
        name: res.name,
        introduce: res.introduce,
        customizeCategoryCode: res.customizeCategoryCode,
        calculationType: res.calculationType || 1,
        marketPrice: res.marketPrice || '',
      });
      setVisibleMarketPriceInput(res.calculationType === 2);
      const imageArr = ['', '', '', '', '', ''];
      if (res.packageImage?.length) {
        res.packageImage.forEach((item, index) => {
          imageArr[index] = item;
        });
        setImageList(imageArr);
        setCurrentImage(imageArr[0]);
        setVideoList(res.videoList);
      }
      detailList.current = res.detailList || [];
    });
  }, [goodsId, isBack, form]);

  useEffect(() => {
    setTimeout(() => {
      getStorage<GoodsPackageDataStorage>(goodsPackageStorageKey).then((res) => {
        if (res) {
          packageData.current = res;
          if (res?.imageList?.length) {
            res?.imageList.forEach((item, index) => {
              imageList[index] = item;
            });
          }
          detailList.current = res.detailList || [];
          setImageList([...imageList]);
          setVideoList(res.videoList || []);
          if (res?.imageList?.length) {
            setCurrentImage(res.imageList[0]);
          }
          form.setFieldsValue({
            name: res.name || '',
            introduce: res.introduce || '',
            customizeCategoryCode: res.customizeCategoryCode || '',
            calculationType: res.calculationType || 1,
            marketPrice: res.marketPrice || '',
          });
          setVisibleMarketPriceInput(res.calculationType === 2);
          if (!packageData?.current?.combinationInfo?.goodsData?.length) {
            getPackageSpuSkuData();
          } else {
            combinationInfo.current = res.combinationInfo;
          }
        } else {
          getPackageSpuSkuData();
        }
      });
    }, 500);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (!checkCharge('M_001_012_001') || !checkPermission('M_001_012_001')) return;
    customerCategoryList().then((res) => {
      const categoryList = res.list || [];
      const categoryOptions: SelectOptionItem[] = [];
      categoryList.forEach((categoryItem) => {
        categoryOptions.push({ label: categoryItem.label || '', value: categoryItem.code || '' });
        if (categoryItem?.children?.length) {
          categoryItem.children.forEach((secondaryItem) => {
            categoryOptions.push({
              label: `${categoryItem.label || ''}/${secondaryItem.label || ''}`,
              value: secondaryItem.code || '',
            });
          });
        }
      });
      setAllCategoryData([...categoryOptions]);
    });
  }, []);

  return (
    <Context
      className={styles.packageContent}
      permission={{ code: 'M_001_005_001', newLogic: true }}
      head={
        <Context.Head
          title={[
            { title: t('goods_management'), to: '/goods/manage/index' },
            { title: goodsId ? t('edit_package') : t('new_package') },
          ]}
        />
      }
    >
      <StepBar
        data={stepBarData}
        // onClick={(item) => {
        //   createPostDataStorage(item?.url || '');
        // }}
      />
      <div className={styles.packageContentInfo}>
        <div className={styles.infoHead}>
          <div className={styles.infoHeadText}>{t('base_basicInfo')}</div>
        </div>
        <div className={styles.infoContent}>
          <div className={styles.infoContentLeft}>
            <div
              className={styles.bigImage}
              style={{ borderWidth: currentImage ? 0 : 1 }}
              role="presentation"
              onClick={() => {
                onCropper();
              }}
            >
              {currentImage ? (
                <img src={currentImage} alt={t('image_alt')} />
              ) : (
                <img
                  src="https://img.huahuabiz.com/user_files/1663149241582868457/image.png"
                  alt={t('image_alt')}
                />
              )}
            </div>

            <div className={styles.smallImage}>
              <DndProvider backend={HTML5Backend}>
                {imageList.map((item, index) => (
                  <div className={styles.smallImageItem} key={`${item + index}`}>
                    <ImageDragDrop
                      id={`${item + index}`}
                      index={index}
                      changePosition={changeImageListPosition}
                      // key={createUuid()}
                    >
                      <div
                        role="presentation"
                        className={styles.smallImageItemBox}
                        onClick={() => {
                          onCropper();
                        }}
                        onMouseEnter={() => {
                          currentImageIndex.current = index;
                          if (imageList[index]) {
                            setCurrentImage(imageList[index]);
                          }
                        }}
                        key={`${item + index}`}
                      >
                        {item ? (
                          <div className={styles.imageBox}>
                            <div className={styles.smallImageBox}>
                              <img src={item} alt={t('picture')} />
                            </div>
                            <Icon
                              name="close-solid"
                              className={styles.smallImageItemBoxIcon}
                              onClick={(e) => {
                                imageList[index] = '';
                                setImageList([...imageList]);
                                setCurrentImage('');
                                e.stopPropagation();
                              }}
                            />
                          </div>
                        ) : (
                          <Icon
                            name="plus"
                            onClick={() => {
                              currentImageIndex.current = index;
                            }}
                          />
                        )}
                      </div>
                    </ImageDragDrop>
                  </div>
                ))}
              </DndProvider>
            </div>
            <div className={styles.imageDesc}>{t('image_suggestion2')}</div>
          </div>
          <div className={styles.infoContentRight}>
            <Form {...formItemLayout} form={form} colon={false} initialValues={formInitialValues}>
              <Form.Item
                label={t('base_packageName')}
                required
                name="name"
                rules={[
                  {
                    required: true,
                    message: t('base_enterPackageName'),
                  },
                ]}
              >
                <Input placeholder={t('base_enterPackageName')} maxLength={50} />
              </Form.Item>
              <Form.Item label={t('base_packageSpec')} name="introduce">
                <Input placeholder={t('base_enterPackageSpec')} maxLength={50} />
              </Form.Item>
              <Form.Item label={t('base_customCategory')} name="customizeCategoryCode">
                <SearchSelect
                  defaultOptions={allCategoryData}
                  value={form.getFieldValue('customizeCategoryCode')}
                  onFake={onFake}
                  onClick={onCustomerCategory}
                  onChange={(val) => {
                    form.setFieldsValue({ customizeCategoryCode: val });
                  }}
                />
              </Form.Item>
              <Form.Item label={t('base_priceAlgorithm')} name="calculationType">
                <Select
                  options={calculationTypeOptions}
                  onChange={(value) => {
                    setVisibleMarketPriceInput(value === 2);
                  }}
                />
              </Form.Item>
              {visibleMarketPriceInput && (
                <Form.Item
                  label={t('base_retailPrice2')}
                  required
                  name="marketPrice"
                  rules={[
                    {
                      required: true,
                      message: t('base_enterRetailPrice'),
                    },
                  ]}
                >
                  <Input
                    placeholder={t('base_enterRetailPrice')}
                    maxLength={20}
                    suffix={t('paymentManage_yuan')}
                  />
                </Form.Item>
              )}
            </Form>
          </div>
        </div>
        <div className={styles.bottomContent}>
          <div className={styles.video}>
            <div className={styles.videoLabel}>{t('main_image_video')}</div>
            <div className={styles.videoContentWrap}>
              <div className={styles.videoContent}>
                {!videoList.length ? (
                  <Spin spinning={uploadLoading}>
                    {checkPermission('M_001_005_004') ? (
                      <UploadVideo
                        videoMaxSize={1024 * 1024 * 500}
                        duration={60}
                        beforeUpload={() => {
                          setUploadLoading(true);
                          return true;
                        }}
                        onSuccess={(val) => {
                          setVideoList([val]);
                          message.success(t('paymentManage_uploadSuccess'));
                        }}
                        onComplete={() => {
                          setUploadLoading(false);
                        }}
                      >
                        <div className={styles.uploadVideo}>
                          <div className={styles.uploadVideoTitle}>
                            <Icon name="plus" className={styles.uploadVideoPlus} />
                          </div>
                        </div>
                      </UploadVideo>
                    ) : (
                      <div
                        className={styles.uploadVideo}
                        role="presentation"
                        onClick={onClickUploadVideo}
                      >
                        <div className={styles.uploadVideoTitle}>
                          <Icon name="plus" className={styles.uploadVideoPlus} />
                        </div>
                      </div>
                    )}
                  </Spin>
                ) : (
                  <div className={styles.videoWrap}>
                    {videoList.length ? (
                      // eslint-disable-next-line jsx-a11y/media-has-caption
                      <video width={50} height={50} src={videoList[0].url} autoPlay muted />
                    ) : (
                      ''
                    )}
                    <img
                      src="https://img.huahuabiz.com/user_files/1671677372549965372/p-close.svg"
                      className={styles.closeImage}
                      alt=""
                      role="presentation"
                      onClick={(e) => {
                        e.stopPropagation();
                        setVideoList([]);
                      }}
                    />
                  </div>
                )}
              </div>
              <div className={styles.videoDesc}>
                <div style={{ marginBottom: 10 }}>{t('video_suggestion_ratio')}</div>
                <div>{t('video_suggestion_length')}</div>
              </div>
            </div>
          </div>
        </div>
        <ImageCropper
          onSuccess={(value) => {
            imageList[currentImageIndex.current] = value.url;
            setCurrentImage(value.url);
            setImageList([...imageList]);
          }}
          ref={imageCropperRef}
          aspectType={0}
        />
        <div className={styles.infoFooter}>
          <div
            className={styles.infoFooterLastBtn}
            role="presentation"
            onClick={() => {
              navigate('/shop/goods/list/?type=pack');
              removeStorage(goodsPackageStorageKey);
            }}
          >
            {t('base_cancel')}
          </div>
          <Button type="primary" onClick={onNext}>
            {t('base_nextStep')}
          </Button>
          {/* <div className={styles.infoFooterNextBtn} role="presentation" onClick={onNext}>
            下一步
          </div> */}
        </div>
      </div>
    </Context>
  );
}
