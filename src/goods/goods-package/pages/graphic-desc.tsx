/*
 * @Author: chenyuhao <EMAIL>
 * @Date: 2022-09-07 11:26:35
 * @LastEditors: chenyuhao <EMAIL>
 * @LastEditTime: 2022-10-19 16:06:27
 * @FilePath: \website\src\goods-package\pages\add.tsx
 */
import { useMemo, useState, useEffect, useRef, ReactNode } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, message } from 'antd';
import { addMcsPackage, updateMcsPackage } from '@/apis';
// import type { AddMcsPackageParamsCollocation, AddMcsPackageParamsCollocationSku } from '@/apis';
import { Context, GraphicDescDrawer, Icon } from '@/components';
import type { GraphicDescDrawerPropsDataItem } from '@/components';
import { removeStorage, getStorage, setStorage } from '@/utils/storage';
import { usePermission } from '@/hooks';
import { useTranslation } from 'react-i18next';
import { GoodsPackageDataStorage, NewAddMcsPackageParams } from '../type/index';
import { goodsPackageStorageKey } from '../config/index';
import { StepBar } from '../components/index';
import styles from './graphic-desc.module.less';

export default function GraphicDesc() {
  const { t } = useTranslation();
  const [urlParams] = useSearchParams();
  const goodsId = urlParams.getAll('goodsId')[0];
  const navigate = useNavigate();
  const stepBarData = useMemo(
    () => [
      {
        text: t('set_deptManage_baseInof'),
        status: 2,
        url: goodsId ? `/goods-package/base-info?goodsId=${goodsId}` : '/goods-package/base-info',
      },
      {
        text: t('set_deptManage_combination'),
        status: 2,
        url: goodsId
          ? `/goods-package/combination?goodsId=${goodsId}`
          : '/goods-package/combination',
      },
      {
        text: t('set_deptManage_graphicDesc'),
        status: 1,
        url: goodsId
          ? `/goods-package/graphic-desc?goodsId=${goodsId}`
          : '/goods-package/graphic-desc',
      },
    ],
    [goodsId, t]
  );

  const [visible, setVisible] = useState(false);
  const [preview, setPreview] = useState(false);
  const [graphicDescData, setGraphicDescData] = useState<GraphicDescDrawerPropsDataItem[]>([]);
  // const [graphicDescInfo, setGraphicDescInfo] = useState<{
  //   imageList: string[];
  //   textList: string[];
  // }>({
  //   imageList: [],
  //   textList: [],
  // });

  const packageData = useRef<GoodsPackageDataStorage | null>(null);

  // 生成提交数据
  const createPostData = (operationType = 0): NewAddMcsPackageParams => {
    const domain = 'https://img.huahuabiz.com/';
    // const detailImageList = graphicDescInfo.imageList.map((item) => {
    //   const index = item.indexOf(domain);
    //   return { imgUrl: item.substring(index), text: '' };
    // });
    // const detailTextList = graphicDescInfo.textList.map((item) => ({ text: item, imgUrl: '' }));
    // const detailList = [...detailImageList, ...detailTextList];
    const detailList = graphicDescData?.map((item) => {
      if (item.type === 'image') {
        const index = item.value.indexOf(domain);
        return { imgUrl: item.value.substring(index), text: '', videoUrl: '' };
      }
      if (item.type === 'video') {
        const index = item.value.indexOf(domain);
        return { videoUrl: item.value.substring(index), text: '', imgUrl: item?.image || '' };
      }
      return { text: item.value, imgUrl: '', videoUrl: '' };
    });
    // if (!packageData.current?.customizeCategoryCode) {
    //   delete packageData.current?.customizeCategoryCode;
    // }
    const postData = {
      ...packageData.current,
      name: packageData.current?.name || '',
      operationType,
      detailList,
      collocationSku: packageData.current?.collocationSku || [],
      type: 1,
    };
    if (goodsId) {
      postData.id = Number(goodsId);
    }
    delete postData.combinationInfo;
    return postData;
  };

  const createPackageDataToStorage = () => {
    const detailList = graphicDescData.map((item) => {
      let object = {};
      switch (item.type) {
        case 'image':
          object = { imgUrl: item.value || '' };
          break;
        case 'text':
          object = { text: item.value || '' };
          break;
        case 'video':
          object = { videoUrl: item.value || '', imgUrl: item.image || '' };
          break;

        default:
          break;
      }

      return object;
    });
    const newPackageData = { ...packageData.current, detailList };
    setStorage(goodsPackageStorageKey, newPackageData);
  };

  // 上一步
  const onLast = () => {
    createPackageDataToStorage();
    navigate(
      goodsId
        ? `/goods-package/combination?goodsId=${goodsId}&isBack=1`
        : '/goods-package/combination'
    );
  };

  // 保存
  const onSave = usePermission('M_001_005_003_001', () => {
    const postData = createPostData(0);
    const fn = goodsId ? updateMcsPackage : addMcsPackage;
    fn(postData).then(() => {
      message.success(t('save_success'));
      removeStorage(goodsPackageStorageKey);
      navigate('/shop/goods/list/?type=pack');
    });
  });

  // 上架
  const onPutAway = usePermission('M_001_005_003_002', () => {
    const postData = createPostData(1);
    const fn = goodsId ? updateMcsPackage : addMcsPackage;
    fn(postData).then(() => {
      message.success(t('put_away_success'));
      removeStorage(goodsPackageStorageKey);
      navigate('/shop/goods/list/?type=pack');
    });
  });

  useEffect(() => {
    setTimeout(() => {
      getStorage<GoodsPackageDataStorage>(goodsPackageStorageKey).then((res) => {
        if (res) {
          packageData.current = res;
          if (res?.detailList?.length) {
            const imageList: string[] = [];
            const textList: string[] = [];
            const graphicDescArr: GraphicDescDrawerPropsDataItem[] = [];
            res.detailList.forEach((item) => {
              if (item?.imgUrl && !item?.videoUrl) {
                imageList.push(item.imgUrl);
                graphicDescArr.push({ type: 'image', value: item.imgUrl });
              } else if (item.text) {
                textList.push(item.text);
                graphicDescArr.push({ type: 'text', value: item.text });
              } else {
                graphicDescArr.push({
                  type: 'video',
                  value: item.videoUrl,
                  image: item?.imgUrl || '',
                });
              }
            });
            // setGraphicDescInfo({ imageList, textList });
            setGraphicDescData(graphicDescArr);
          }
        }
      });
    }, 300);
  }, []);

  return (
    <Context
      className={styles.packageContent}
      permission={{ code: 'M_001_005_001', newLogic: true }}
      head={
        <Context.Head
          title={[
            { title: t('product_management'), to: '/shop/goods/list' },
            { title: `${goodsId ? t('edit_package') : t('new_package')}` },
          ]}
        />
      }
    >
      <StepBar
        data={stepBarData}
        // onClick={(item) => {
        //   navigate(item?.url || '');
        // }}
      />
      <div className={styles.packageContentInfo}>
        <div className={styles.infoHead}>
          <div className={styles.infoHeadText}>{t('set_deptManage_graphicDesc')}</div>
          <div
            className={styles.infoHeadOperate}
            role="presentation"
            onClick={() => {
              setVisible(true);
              setPreview(false);
            }}
          >
            {t('set_deptManage_edit')}
          </div>
        </div>
        <div className={styles.infoDesc}>
          {graphicDescData?.length ? (
            <div className={styles.infoDescContent}>
              <meta
                name="viewport"
                content="width=device-width,initial-scale=1,target-densitydpi=[dpi-value|device-dpi|high-dpi|medium-dpi|low-dpi],user-scalable=no"
              />
              {graphicDescData.map((item, index) => {
                let element: ReactNode | string = '';
                switch (item.type) {
                  case 'image':
                    element = (
                      <img
                        src={item.value}
                        alt={t('picture')}
                        className={styles.infoDescImageItem}
                        key={`${item.value + index}`}
                      />
                    );
                    break;
                  case 'video':
                    element = (
                      // eslint-disable-next-line jsx-a11y/media-has-caption
                      <video
                        src={item.value}
                        className={styles.infoDescVideo}
                        controls
                        key={`${item.value + index}`}
                      />
                    );
                    break;
                  case 'text':
                    element = (
                      <div className={styles.infoDescText} key={`${item.value + index}`}>
                        {item.value}
                      </div>
                    );
                    break;

                  default:
                    break;
                }
                return element;
              })}
              {/* {graphicDescInfo.textList.map((item, index) => (
                <div className={styles.infoDescText} key={`${item + index}`}>
                  {item}
                </div>
              ))}
              <div className={styles.infoDescImage}>
                {graphicDescInfo.imageList.map((item, index) => (
                  <img
                    src={item}
                    alt="图片"
                    className={styles.infoDescImageItem}
                    key={`${item + index}`}
                  />
                ))}
              </div> */}
            </div>
          ) : (
            <>
              <div style={{ height: 40 }} />
              <div
                className={styles.addGraphicDescBtn}
                role="presentation"
                onClick={() => {
                  setVisible(true);
                  setPreview(false);
                }}
              >
                <Icon name="plus" /> {t('graphic_addDescription')}
              </div>
            </>
          )}
        </div>
        <div className={styles.infoFooter}>
          <div
            className={styles.infoFooterLastBtn}
            role="presentation"
            onClick={() => {
              navigate('/shop/goods/list/?type=pack');
              removeStorage(goodsPackageStorageKey);
            }}
          >
            {t('graphic_cancel')}
          </div>
          <Button type="default" onClick={onLast}>
            {t('graphic_previousStep')}
          </Button>
          <Button type="primary" style={{ marginLeft: 12, marginRight: 12 }} onClick={onSave}>
            {t('graphic_save')}
          </Button>
          <Button type="primary" onClick={onPutAway}>
            {t('graphic_putAway')}
          </Button>
        </div>
      </div>

      <GraphicDescDrawer
        visible={visible}
        preview={preview}
        permissionList={[{ key: 'uploadVideo', code: 'M_001_005_005' }]}
        onClose={() => {
          setVisible(false);
        }}
        onPreviewChange={(val) => {
          setPreview(val);
        }}
        onConfirm={(values) => {
          // const imageList: string[] = [];
          // const textList: string[] = [];
          // values.forEach((item) => {
          //   switch (item.type) {
          //     case 'image':
          //       imageList.push(item.value);
          //       break;
          //     case 'text':
          //       textList.push(item.value);
          //       break;
          //     default:
          //       break;
          //   }
          // });
          setGraphicDescData(values);
          // setGraphicDescInfo({ ...{ imageList, textList } });
        }}
        data={graphicDescData}
      />
    </Context>
  );
}
