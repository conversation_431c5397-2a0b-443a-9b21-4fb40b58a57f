@import 'styles/mixins/mixins';

.defaultTextBtn {
  color: @primary-color;
  cursor: pointer;
}

.flexCenter {
  display: flex;
  justify-content: center;
  align-items: center;
}

.packageContent {
  background: transparent !important;

  &Info {
    width: 100%;
    height: calc(100% - 80px - 20px);
    margin-top: 20px;
    border-radius: 18px;
    background: white;
    box-shadow: @box-shadow-base;

    .infoHead {
      font-size: 16px;
      display: flex;
      height: 44px;
      margin-bottom: 10px;
      padding: 0 20px;
      justify-content: space-between;

      &Text {
        display: flex;
        height: 44px;
        padding-top: 5px;
        align-items: center;

        &Desc {
          color: @text-colors[secondary];
          font-size: 12px;
        }
      }

      &Operate {
        color: @primary-color;
        display: flex;
        height: 44px;
        padding-top: 5px;
        align-items: center;
        cursor: pointer;
      }
    }

    .infoContent {
      display: flex;
      flex-direction: row;
      height: calc(100% - 112px);

      .goodsContent {
        display: flex;
        width: 100%;

        &Left {
          // flex: 1;
          width: 50%;
          height: 100%;
          overflow: scroll;

          .mainGoodsItem {
            min-height: 193px;
            border-radius: 12px 0 0 12px;
            background-color: white;
            padding: 20px;
            padding-top: 10px;
            padding-right: 0;

            &Box {
              cursor: pointer;
              padding-right: 20px;
            }

            &Head {
              color: #3d3d3d;
              display: flex;
              align-items: center;

              &Left {
                flex: 1;
                color: #3d3d3d;
              }
            }

            &Content {
              display: flex;
              margin-top: 16px;
              margin-bottom: 20px;

              .mainGoodsImage {
                width: 80px;
                height: 80px;
                border-radius: 6px;
                position: relative;

                &Item {
                  display: flex;
                  width: 80px;
                  height: 80px;
                  overflow: hidden;
                  justify-content: center;
                  align-items: center;
                  border-radius: 6px;
                }
              }

              .mainGoodsInfo {
                width: calc(100% - 100px);
                padding-left: 12px;

                &Name {
                  margin-bottom: 8px;
                  .text-overflow(2);

                  word-break: break-all;
                }

                &Specification {
                  color: @text-colors[secondary];
                  font-size: 12px;

                  &Select {
                    color: #000;
                    margin-right: 4px;
                  }
                }
              }

              .mainGoodsOperate {
                width: 20px;
                cursor: pointer;
                visibility: hidden;
                padding-top: 20px;
              }

              &:hover {
                .mainGoodsOperate {
                  visibility: visible;
                }
              }
            }

            &Footer {
              display: flex;
              align-items: center;

              &Left {
                flex: 1;
                color: #888b98;
              }

              &Right {
                cursor: pointer;
                color: @primary-color;
                text-align: right;
              }
            }

            .replaceGoods {
              width: 100%;
              max-height: 250px;
              overflow: scroll;

              &Item {
                display: flex;
                width: 100%;
                min-height: 60px;
                margin-top: 20px;
                padding: 10px 12px;
                align-items: center;
                cursor: pointer;
                border-radius: 12px 0 0 12px;

                &Image {
                  width: 60px;
                  height: 60px;
                  border-radius: 6px;
                  position: relative;

                  &Item {
                    display: flex;
                    width: 60px;
                    height: 60px;
                    overflow: hidden;
                    justify-content: center;
                    align-items: center;
                    border-radius: 6px;
                  }
                }

                &Info {
                  width: calc(100% - 80px);
                  padding-left: 12px;

                  &Name {
                    margin-bottom: 8px;
                    .text-overflow();

                    word-break: break-all;
                  }

                  &Specification {
                    color: @text-colors[secondary];
                    font-size: 12px;

                    &Select {
                      color: #000;
                      margin-right: 4px;
                    }
                  }
                }

                &Operate {
                  width: 20px;
                  cursor: pointer;
                  visibility: hidden;
                  padding-top: 10px;
                }

                &:hover {
                  .replaceGoodsItemOperate {
                    visibility: visible;
                  }
                }
              }
            }

            &:hover {
              background: rgb(217 238 255 / 30%);
            }
          }
        }

        &Right {
          width: 50%;
          background: rgb(217 238 255 / 30%);
          padding: 0 20px;

          &Head {
            display: flex;
            height: 50px;
            border-bottom: 1px solid #f3f3f3;
            align-items: center;

            &Left {
              flex: 1;
              padding-right: 20px;
              .text-overflow();

              word-break: break-all;
            }

            &Right {
              .addGoodsSpecification {
                color: @primary-color;
                width: 86px;
                cursor: pointer;
              }
            }
          }

          &Content {
            height: calc(100% - 104px);
            overflow: scroll;

            .specificationGoodsItem {
              width: 100%;
              min-height: 120px;
              margin-top: 12px;
              padding: 0 20px;

              &Info {
                display: flex;
                width: 100%;
                min-height: 80px;

                &:hover {
                  .specificationGoodsItemInfoOperateIcon {
                    visibility: visible;
                  }
                }

                &Checkbox {
                  display: flex;
                  margin-right: 20px;
                  align-items: center;
                }

                &Image {
                  width: 80px;
                  height: 80px;
                  position: relative;
                  border-radius: 6px;

                  &Item {
                    width: 80px;
                    height: 80px;
                    border-radius: 6px;
                  }
                  // border: 1px solid #f5f6fa;
                }

                &Info {
                  display: flex;
                  margin-left: 12px;
                  position: relative;
                  flex: 1;
                  flex-direction: column;

                  &Name {
                    .text-overflow(1);

                    word-break: break-all;
                    margin-bottom: 8px;
                  }

                  &Text {
                    color: @text-colors[secondary];
                    font-size: 12px;
                    margin-bottom: 3px;
                  }
                }

                &Operate {
                  display: flex;
                  width: 30px;
                  justify-content: center;
                  cursor: pointer;
                  flex-direction: column;
                  align-items: center;

                  &Icon {
                    visibility: hidden;
                  }

                  &Refresh {
                    color: @primary-color;
                    font-size: 12px;
                    margin-top: 20px;
                    cursor: pointer;
                  }
                }
              }

              &Operate {
                width: 100%;

                &Item {
                  display: flex;
                  align-items: center;
                  margin-top: 16px;

                  &Value {
                    flex: 1;
                    text-align: right;
                  }
                }
              }
            }
          }

          &Footer {
            display: flex;
            width: 100%;
            height: 54px;
            background: rgb(245 246 250 / 50%);

            &Left {
              flex: 1;
              display: flex;
              align-items: center;

              &Text {
                color: #888b98;
                margin: 0 8px;
              }
            }

            &Right {
              .defaultTextBtn();

              display: flex;
              align-items: center;
            }
          }
        }
      }

      .noData {
        display: flex;
        width: 100%;
        height: 100%;
        justify-content: center;
        flex-direction: column;
        align-items: center;

        &Text {
          color: @text-colors[secondary];
          font-size: 16px;
          margin-bottom: 25px;
        }
      }
    }

    .infoFooter {
      display: flex;
      width: 100%;
      height: 58px;
      padding: 0 20px;
      justify-content: center;
      position: relative;
      text-align: center;
      align-items: center;

      &LastBtn {
        color: @primary-color;
        font-size: 14px;
        position: absolute;
        top: 18px;
        left: 20px;
        cursor: pointer;
      }

      &NextBtn {
        color: @primary-color;
        font-size: 14px;
        position: absolute;
        top: 18px;
        right: 0;
        cursor: pointer;
      }
    }
  }
}

.popoverContent {
  min-width: 88px;
  max-width: 120px;

  .popoverItem {
    display: flex;
    min-width: 88px;
    max-width: 120px;
    height: 32px;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    border-radius: 10px;

    &:hover {
      background: rgb(217 238 255 / 30%);
    }
  }
}

.curP {
  cursor: pointer;
}

.goodsImageBox {
  position: relative;
}

.goodsStatusTag {
  color: white;
  font-size: 10px;
  width: 32px;
  height: 16px;
  position: absolute;
  bottom: 0;
  left: 0;
  opacity: 0.8;
  border-radius: 0 12px;
  text-align: center;

  &Gift {
    /* 辅助色/F9AE08 */
    background: #f9ae08;
  }

  &Invalid {
    background-color: #888b98;
  }

  &SoldOut {
    background-color: #ea1c26;
  }
}

.goodsActive {
  background-color: rgb(217 238 255 / 30%) !important;
}

.isError {
  color: red;
}

.cancelBatchBtn {
  // color: @primary-color;
  display: flex;
  margin-left: 20px;
  justify-content: center;
  // width: 86px;
  cursor: pointer;
  align-items: center;
}

.shareTag {
  color: white;
  font-size: 10px;
  margin-right: 4px;
  padding: 2px;
  background: linear-gradient(248deg, #00c6ff 0%, #008cff 100%);
  border-radius: 4px;
}

// .replaceGoodsItemImageItem {
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   width: 60px;
//   height: 60px;
//   border-radius: 6px;
//   overflow: hidden;
// }

.rightImgBox {
  display: flex;
  width: 80px;
  height: 80px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
}
