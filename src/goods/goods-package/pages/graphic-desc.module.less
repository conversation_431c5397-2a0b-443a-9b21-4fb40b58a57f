.packageContent {
  background: transparent !important;

  &Info {
    width: 100%;
    height: calc(100% - 80px - 20px);
    margin-top: 20px;
    padding: 0 20px;
    border-radius: 18px;
    background: white;
    box-shadow: @box-shadow-base;

    .infoHead {
      font-size: 16px;
      display: flex;
      height: 44px;
      margin-bottom: 20px;
      justify-content: space-between;

      &Text {
        display: flex;
        height: 44px;
        padding-top: 5px;
        align-items: center;
      }

      &Operate {
        color: @primary-color;
        display: flex;
        height: 44px;
        padding-top: 5px;
        align-items: center;
        cursor: pointer;
      }
    }

    .infoDesc {
      width: 100%;
      height: calc(100% - 122px);
      overflow: scroll;

      .addGraphicDescBtn {
        color: @primary-color;
        display: flex;
        width: 100%;
        height: 80px;
        justify-content: center;
        align-items: center;
        border-radius: 10px;
        border: 1px dashed @primary-color;
        cursor: pointer;
        background: rgb(217 238 255 / 30%);
      }

      .infoDescText {
        // overflow: hidden;
        // text-overflow: ellipsis;
        // display: -webkit-box;
        // -webkit-line-clamp: 2;
        // -webkit-box-orient: vertical;
        color: #888b98;
        width: 100%;
        margin-bottom: 10px;
      }

      .infoDescImage {
        display: flex;
        width: 100%;
        height: 100%;
        overflow: scroll;
        flex-wrap: wrap;

        &Item {
          // width: 23%;
          // height: 23%;
          // max-height: 230px;
          // max-width: 230px;
          // width: 150px;
          // height: 150px;
          width: 100%;
          height: auto;
          margin-bottom: 10px;
          // margin: 10px;
          // box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
          // border-radius: 9px;
          // object-fit: cover;
        }
      }

      .infoDescVideo {
        width: 100%;
      }
    }

    .infoFooter {
      width: 100%;
      height: 58px;
      position: relative;
      text-align: center;

      &LastBtn {
        color: @primary-color;
        font-size: 14px;
        position: absolute;
        top: 18px;
        left: 0;
        cursor: pointer;
      }
    }
  }
}
