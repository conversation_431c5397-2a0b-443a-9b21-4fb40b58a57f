import { useMemo, useState, useEffect, ReactNode, useRef, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Button, Popover, Checkbox, Select, message } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';
import { usePermission } from '@/hooks';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';

import {
  formulaConvert,
  getMcsPackageSkuNumByUnit,
  // getMcsPackageDetailSpuSkuList,
  getMcsPackageGroupDetail,
  updateMcsPackageGroupSkuRefresh,
  getMcsPackageSkuQuantityByUnit,
} from '@/apis';

import type {
  // GetMcsPackageDetailRes,
  AddMcsPackageParamsCollocation,
  AddMcsPackageParamsCollocationSku,
  // GetMcsPackageDetailSpuSkuListParams,
  // GetMcsPackageDetailSpuSkuListResult,
} from '@/apis';

import { Context, Icon, StepperInput, Modal } from '@/components';
import { setStorage, getStorage, removeStorage } from '@/utils/storage';
import { checkPermission, testPerm } from '@/utils/permission';
import { StepBar } from '../components/index';
import type {
  NewGetMcsPackageSpuSkuListResult,
  NewGetMcsPackageSpuListResult,
} from '../type/combination';
import { GoodsPackageDataStorage } from '../type/index';
import { createSpecifications } from '../utils/index';
import { goodsPackageStorageKey } from '../config/index';

// import type { StepBarProps } from '../components/index';
import {
  AddChildGoodsDrawer,
  SelectSpecificationsDrawer,
  // BatchSettingsDrawer,
  BatchSettingsModal,
} from '../container/index';
import type { BatchInfo } from '../container/combination/batch-settings-modal';
import styles from './combination.module.less';
import { NewGetMcsPackageDetailResSku } from '../type/combination';

export default function Administrator() {
  const { t } = useTranslation();
  const [urlParams] = useSearchParams();
  const goodsId = urlParams.getAll('goodsId')[0];
  const packageData = useRef<GoodsPackageDataStorage | null>(null);
  // const productId = urlParams.getAll('productId')[0];
  const navigate = useNavigate();

  const stepBarData = useMemo(
    () => [
      {
        text: t('set_deptManage_baseInof'),
        status: 2,
        url: goodsId ? `/goods-package/base-info?goodsId=${goodsId}` : '/goods-package/base-info',
      },
      {
        text: t('set_deptManage_combination'),
        status: 1,
        url: goodsId
          ? `/goods-package/combination?goodsId=${goodsId}`
          : '/goods-package/combination',
      },
      {
        text: t('set_deptManage_graphicDesc'),
        status: 0,
        url: goodsId
          ? `/goods-package/graphic-desc?goodsId=${goodsId}`
          : '/goods-package/graphic-desc',
      },
    ],
    [goodsId, t]
  );

  const [childGoodsVisible, setChildGoodsVisible] = useState(false);
  const [selectSpecificationsVisible, setSelectSpecificationsVisible] = useState(false);
  const [replaceGoodsVisible, setReplaceGoodsVisible] = useState(false);
  const [goodsData, setGoodsData] = useState<NewGetMcsPackageDetailResSku[]>([]);

  // 当前点击的spu或者点击可替换商品
  const [currentGoods, setCurrentGoods] = useState({} as NewGetMcsPackageDetailResSku);
  const currentGoodsRef = useRef({
    index: -1, // 当前点击的spu的index
    type: 1, // 当前点击的类型，1是主spu，2是可替换商品
    replaceIndex: -1, // 当前可替换商品的index
  });

  const [selectSpecificationsMainItem, setSelectSpecificationsMainItem] = useState(
    {} as NewGetMcsPackageSpuListResult
  );

  // batch
  // const [currentMainGoods, setCurrentMainGoods] = useState();
  const [specificationGoods, setSpecificationGoods] = useState<NewGetMcsPackageSpuSkuListResult[]>(
    []
  );
  const [isBatch, setIsBatch] = useState(false);
  const [batchVisible, setBatchVisible] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);
  const [checkedList, setCheckedList] = useState<number[]>([]);
  const [checkedSkuIdList, setCheckedSkuIdList] = useState<number[]>([]);

  const [replaceFilterSpuIds, setReplaceFilterSpuIds] = useState<number[]>([]);
  // 全部已选择的sku的id
  const [allCheckedSkuIds, setAllCheckedSkuIds] = useState<number[]>([]);
  const [selectSpecificationsFilterSkuIds, setSelectSpecificationsFilterSkuIds] = useState<
    number[]
  >([]);

  // 获取当前的goodsItem
  const getCurrentGoodsItem = () => {
    const { index, replaceIndex, type } = currentGoodsRef.current;
    // @ts-ignore
    let item: NewGetMcsPackageDetailResSku = {};
    if (type === 1) {
      item = goodsData[index];
    } else {
      const replaceList = goodsData[index].replaceList || [];
      item = replaceList[replaceIndex];
    }
    return item;
  };

  // 生成newSpecificationGoodsList
  const createNewSpecificationGoodsList = () => {
    let newSpecificationGoodsList: NewGetMcsPackageDetailResSku['specificationGoodsList'] = [];
    const { index, replaceIndex, type } = currentGoodsRef.current;
    if (type === 1) {
      newSpecificationGoodsList = goodsData[index].specificationGoodsList || [];
    } else {
      const replaceList = goodsData[index].replaceList || [];

      newSpecificationGoodsList = replaceList[replaceIndex].specificationGoodsList || [];
    }
    return newSpecificationGoodsList;
  };

  // 设置item的 error
  const setItemError = (item: NewGetMcsPackageDetailResSku) => {
    const newItem = item;
    newItem.isError = false;
    item.specificationGoodsList?.forEach((skuItem) => {
      if (!skuItem.quantity || skuItem.groupDetailStatus) {
        newItem.isError = true;
      }
    });
    (item.replaceList || []).forEach((replaceItem) => {
      const newReplaceItem = replaceItem;
      newReplaceItem.showReplace = false;
      replaceItem.specificationGoodsList?.forEach((skuItem) => {
        if (!skuItem.quantity || skuItem.groupDetailStatus) {
          newReplaceItem.showReplace = true;
        }
      });
    });
  };

  // 检测是否可以下一步
  const checkNext = () => {
    let statusError = false;
    let numError = false;
    const noSpecificationGoodsList: NewGetMcsPackageDetailResSku[] = [];
    const replaceNoSpecificationGoodsList: NewGetMcsPackageDetailResSku[] = [];
    goodsData.forEach((item) => {
      const newItem = item;
      newItem.isError = false;
      if (item.specificationGoodsList?.length) {
        item.specificationGoodsList?.forEach((skuItem) => {
          if (!skuItem.quantity || skuItem.groupDetailStatus) {
            if (skuItem.groupDetailStatus) {
              statusError = true;
            } else {
              numError = true;
            }
            newItem.isError = true;
          }
        });
      } else {
        noSpecificationGoodsList.push(item);
      }
      (item.replaceList || []).forEach((replaceItem) => {
        const newReplaceItem = replaceItem;
        newReplaceItem.showReplace = false;
        if (replaceItem.specificationGoodsList?.length) {
          replaceItem.specificationGoodsList?.forEach((skuItem) => {
            if (!skuItem.quantity || skuItem.groupDetailStatus) {
              if (skuItem.groupDetailStatus) {
                statusError = true;
              } else {
                numError = true;
              }
              newItem.showReplace = true;
            }
          });
        } else {
          replaceNoSpecificationGoodsList.push(replaceItem);
        }
      });
    });
    setGoodsData([...goodsData]);

    return { statusError, numError, noSpecificationGoodsList, replaceNoSpecificationGoodsList };
  };

  // 生成PackageData
  const createPackageData = () => {
    const collocationSku: AddMcsPackageParamsCollocation[] = [];
    goodsData.forEach((item, index) => {
      const collocationSkuList: AddMcsPackageParamsCollocationSku[] = [];
      item.specificationGoodsList?.forEach((skuItem) => {
        collocationSkuList.push({
          dataId: skuItem.id,
          isGift: skuItem.isGift || 0,
          status: skuItem?.groupDetailStatus || 0,
          quantity: Number(skuItem.quantity || 0),
          unit: skuItem.unit,
        });
      });
      (item.replaceList || []).forEach((replaceItem) => {
        replaceItem.specificationGoodsList?.forEach((skuItem) => {
          collocationSkuList.push({
            dataId: skuItem.id,
            isGift: skuItem.isGift || 0,
            status: skuItem?.groupDetailStatus || 0,
            quantity: Number(skuItem.quantity || 0),
            unit: skuItem.unit,
          });
        });
      });
      const groupIdObject = item.groupId ? { id: item.groupId } : {};
      if (collocationSkuList.length) {
        collocationSku.push({
          level: index + 1,
          isGift: item.isGift || 0,
          skuList: collocationSkuList,
          ...groupIdObject,
        });
      }
    });

    const combinationInfo = { goodsData };
    return { ...packageData?.current, collocationSku, combinationInfo };
  };

  // 设置缓存数据
  const setPackageDataToStorage = () => {
    const newPackageData = createPackageData();
    setStorage(goodsPackageStorageKey, newPackageData);
  };

  const onCheckAllChange = (e: CheckboxChangeEvent) => {
    const ids = currentGoods.specificationGoodsList?.map((item) => item.id) || [];
    const skuIds = currentGoods.specificationGoodsList?.map((item) => item.skuId) || [];
    setCheckedSkuIdList(e.target.checked ? skuIds : []);
    setCheckedList(e.target.checked ? ids : []);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
  };

  const onCheckboxChange = (checkedValues: number[]) => {
    const specificationGoodsListSize = currentGoods?.specificationGoodsList?.length || 0;
    setCheckAll(checkedValues.length === specificationGoodsListSize);
    setIndeterminate(
      checkedValues?.length !== specificationGoodsListSize && checkedValues.length > 0
    );
    const skuIds: number[] = [];
    currentGoods.specificationGoodsList?.forEach((item) => {
      if (checkedValues.includes(item.id)) {
        skuIds.push(item.skuId);
      }
    });
    setCheckedSkuIdList(skuIds);
    setCheckedList(checkedValues);
  };

  // 获取spu的失效和下架的数量
  const getSpuInvalidLowerNum = (newSpecificationGoodsList: NewGetMcsPackageSpuSkuListResult[]) => {
    const invalidArr = newSpecificationGoodsList.filter(
      (specItem) => specItem.groupDetailStatus === 1
    );
    const lowerArr = newSpecificationGoodsList.filter(
      (specItem) => specItem.groupDetailStatus === 2
    );
    return { invalidArr, lowerArr };
  };

  // 处理spu的status
  const processSpuStatus = (newSpecificationGoodsList: NewGetMcsPackageSpuSkuListResult[]) => {
    let status = 0;
    const { invalidArr, lowerArr } = getSpuInvalidLowerNum(newSpecificationGoodsList);
    // const invalidArr = newSpecificationGoodsList.filter(
    //   (specItem) => specItem.groupDetailStatus === 1
    // );
    // const lowerArr = newSpecificationGoodsList.filter(
    //   (specItem) => specItem.groupDetailStatus === 2
    // );
    if (newSpecificationGoodsList.length) {
      const lower = invalidArr.length === newSpecificationGoodsList.length;
      const invalid = lowerArr.length === newSpecificationGoodsList.length;
      if (lower) {
        status = 1;
      } else if (
        invalid ||
        (newSpecificationGoodsList.length &&
          invalidArr.length + lowerArr.length === newSpecificationGoodsList.length)
      ) {
        status = 2;
      } else {
        status = 0;
      }
    }
    return status;
  };

  // 设置specification
  const setSpecificationGoodsDataFunc = (
    specificationGoodsList: NewGetMcsPackageSpuSkuListResult[]
  ) => {
    const { lowerArr, invalidArr } = getSpuInvalidLowerNum(specificationGoodsList);
    if (currentGoodsRef.current.type === 1) {
      goodsData[currentGoodsRef.current.index].specificationGoodsList = specificationGoodsList;
      goodsData[currentGoodsRef.current.index].status = processSpuStatus(specificationGoodsList);
      goodsData[currentGoodsRef.current.index].lowerShelfCount = lowerArr.length;
      goodsData[currentGoodsRef.current.index].invalidCount = invalidArr.length;
    } else {
      const replaceList = goodsData[currentGoodsRef.current.index]?.replaceList || [];
      replaceList[currentGoodsRef.current.replaceIndex].specificationGoodsList =
        specificationGoodsList;
      replaceList[currentGoodsRef.current.replaceIndex].status =
        processSpuStatus(specificationGoodsList);
      replaceList[currentGoodsRef.current.replaceIndex].lowerShelfCount = lowerArr.length;
      replaceList[currentGoodsRef.current.replaceIndex].invalidCount = invalidArr.length;
    }
    setCurrentGoods(getCurrentGoodsItem());
    setSpecificationGoods([...specificationGoodsList]);
    setGoodsData([...goodsData]);
  };

  // 批量删除
  const onBatchRemove = () => {
    if (!checkedList.length) {
      message.success(t('please_select_specifications_first'));
      return;
    }

    let invalidCount = 0;
    let lowerShelfCount = 0;

    for (let index = 0; index < specificationGoods.length; index += 1) {
      const specificationGoodsItem = specificationGoods[index];
      if (checkedList.includes(specificationGoodsItem.id)) {
        specificationGoods.splice(index, 1);
        index -= 1;
      } else if (specificationGoodsItem.groupDetailStatus === 1) {
        lowerShelfCount += 1;
      } else if (specificationGoodsItem.groupDetailStatus === 2) {
        invalidCount += 1;
      }
    }

    getCurrentGoodsItem().invalidCount = invalidCount;
    getCurrentGoodsItem().lowerShelfCount = lowerShelfCount;
    getCurrentGoodsItem().status = processSpuStatus(specificationGoods);
    setCheckedList([]);
    setCheckAll(false);
    setIndeterminate(false);
    setSpecificationGoodsDataFunc(specificationGoods);
  };

  // 刷新
  const onSpecificationGoodsRefresh = (index: number) => {
    const item = specificationGoods[index];
    updateMcsPackageGroupSkuRefresh({
      packageId: Number(goodsId),
      groupId: currentGoods.groupId || 0,
      invalidIds: [item.id],
    }).then((res) => {
      if (res.list.length) {
        const newSpecificationGoodsList = createNewSpecificationGoodsList();
        newSpecificationGoodsList[index].groupDetailStatus = 0;
        const refreshItem = res.list[0];
        newSpecificationGoodsList[index].unit = refreshItem.unit;
        if (refreshItem.saleGroup) {
          newSpecificationGoodsList[index].saleGroup = refreshItem.saleGroup;
        }
        newSpecificationGoodsList[index].quantity = refreshItem.quantity;

        const invalidCount = getCurrentGoodsItem()?.invalidCount || 0;
        if (invalidCount) {
          getCurrentGoodsItem().invalidCount = invalidCount - 1;
        }
        item.groupDetailStatus = 0;
        getCurrentGoodsItem().status = processSpuStatus(newSpecificationGoodsList);
        setItemError(getCurrentGoodsItem());
        setGoodsData([...goodsData]);
        setSpecificationGoods([...newSpecificationGoodsList]);
        message.success(t('refresh_success'));
      } else {
        message.success(t('refresh_failure'));
      }
    });
  };

  // 初始化左边选中
  const initGoodsDataActive = () => {
    goodsData.forEach((goodsItem) => {
      const newGoodsItem = goodsItem;
      newGoodsItem.active = false;
      if (goodsItem?.replaceList?.length) {
        goodsItem?.replaceList.forEach((replaceItem) => {
          const newReplaceItem = replaceItem;
          newReplaceItem.active = false;
        });
      }
    });
  };

  // 点击左侧商品
  const onClickGoodsItem = (item: NewGetMcsPackageDetailResSku, index: number) => {
    currentGoodsRef.current.index = index;
    currentGoodsRef.current.type = 1;
    setCurrentGoods({ ...item });
    const newItem = item;
    initGoodsDataActive();
    newItem.active = true;
    setIsBatch(false);
    setCheckedList([]);
    setCheckedSkuIdList([]);
    setGoodsData([...goodsData]);
    if (item?.replaceList) {
      const newReplaceFilterSpuIds = item.replaceList.map(
        (replaceItem) => replaceItem.productId || -1
      );
      newReplaceFilterSpuIds.push(item.productId || -1);
      setReplaceFilterSpuIds([...newReplaceFilterSpuIds]);
    }
    setSpecificationGoods([...(item?.specificationGoodsList || [])]);
  };

  // 删除操作
  const onClickRemoveGoodsFn = (index: number, isRemoveAllSpu = true) => {
    const removeItem = goodsData[index];
    const removeSkuIds = removeItem?.specificationGoodsList?.map((specItem) => specItem.id) || [];
    const removeReplaceSkuIds: number[] = [];
    if (isRemoveAllSpu) {
      removeItem.replaceList?.forEach((replaceItem) => {
        replaceItem.specificationGoodsList?.forEach((specItem) => {
          removeReplaceSkuIds.push(specItem.id);
        });
      });
    }
    const allRemoveSkuIds = removeSkuIds.concat(removeReplaceSkuIds);
    const spuIds = replaceFilterSpuIds.filter((item) => item !== removeItem.productId);
    const allSkuIds = allCheckedSkuIds.filter((id) => !allRemoveSkuIds.includes(id));
    if (index !== goodsData.length - 1) {
      goodsData[index + 1].active = true;
      setCurrentGoods(goodsData[index + 1]);
      currentGoodsRef.current = {
        index: index + 1,
        type: 1, // 当前点击的类型，1是主spu，2是可替换商品
        replaceIndex: -1, // 当前可替换商品的index
      };
      setSpecificationGoods(goodsData[index + 1].specificationGoodsList || []);
    } else if (goodsData.length === 1) {
      setCurrentGoods({
        name: '',
        standardTotal: 0,
        images: '',
        isMainSpu: 1,
        replaceCount: 0,
        checkStandardTotal: 0,
      });
      currentGoodsRef.current.index = -1;
      currentGoodsRef.current.type = 1;
      setSpecificationGoods([]);
    } else {
      goodsData[index - 1].active = true;
      setCurrentGoods(goodsData[index - 1]);
      currentGoodsRef.current = {
        index: index + 1,
        type: 1, // 当前点击的类型，1是主spu，2是可替换商品
        replaceIndex: -1, // 当前可替换商品的index
      };
      setSpecificationGoods(goodsData[index - 1].specificationGoodsList || []);
    }
    goodsData.splice(index, 1);
    setGoodsData([...goodsData]);
    setAllCheckedSkuIds(allSkuIds);
    setReplaceFilterSpuIds(spuIds);
  };

  // 点击叉号删除
  const onClickRemoveGoods = (item: NewGetMcsPackageDetailResSku, index: number) => {
    setTimeout(() => {
      if (!item?.replaceList?.length) {
        onClickRemoveGoodsFn(index);
      } else {
        const replaceItem = item?.replaceList[0];
        item?.replaceList.splice(0, 1);
        const newItem = {
          ...replaceItem,
          active: true,
          replaceList: item?.replaceList,
        };
        goodsData[index] = newItem;
        setCurrentGoods({ ...newItem });
        setSpecificationGoods([...(newItem.specificationGoodsList || [])]);
        const removeSkuIds = item?.specificationGoodsList?.map((specItem) => specItem.id) || [];
        const allSkuIds = allCheckedSkuIds.filter((id) => !removeSkuIds.includes(id));
        setAllCheckedSkuIds(allSkuIds);
      }
      setGoodsData([...goodsData]);
    }, 20);
  };

  // 点击可替换商品
  const onClickReplaceItem = (
    replaceItem: NewGetMcsPackageDetailResSku,
    replaceIndex: number,
    index: number
  ) => {
    initGoodsDataActive();
    currentGoodsRef.current.replaceIndex = replaceIndex;
    currentGoodsRef.current.type = 2;
    goodsData[index].active = false;
    if (goodsData[index]?.replaceList?.length) {
      // @ts-ignore
      goodsData[index].replaceList[replaceIndex].active = true;
    }
    setGoodsData([...goodsData]);
    setCurrentGoods({ ...replaceItem });
    setSpecificationGoods(replaceItem?.specificationGoodsList || []);
  };

  // 批量设置数量和单位
  const batchSettingUnitNum = (val: BatchInfo) => {
    if (val.unit) {
      getMcsPackageSkuNumByUnit({ unit: val.unit, skuIds: checkedSkuIdList }).then((res) => {
        const batchData = res?.list || [];
        specificationGoods.forEach((item) => {
          const newItem = item;
          batchData.forEach((batchItem) => {
            if (batchItem.skuId === item.skuId) {
              if (Number(val.quantity || 0) % batchItem.saleGroup === 0) {
                newItem.quantity = Number(val.quantity);
              } else {
                const offset = Math.ceil(Number(val.quantity || 0) / batchItem.saleGroup);
                newItem.quantity = offset * batchItem.saleGroup;
              }
              newItem.unit = batchItem.unit;
              newItem.saleGroup = batchItem.saleGroup;
            }
          });
        });
        setSpecificationGoodsDataFunc(specificationGoods);
      });
    } else {
      specificationGoods.forEach((item) => {
        if (checkedList.includes(item.id || item?.productId || 0)) {
          const newItem = item;
          if (Number(val.quantity || 0) % item.saleGroup === 0) {
            newItem.quantity = Number(val.quantity);
          } else {
            const offset = Math.ceil(Number(val.quantity || 0) / item.saleGroup);
            newItem.quantity = offset * item.saleGroup;
          }
        }
      });
      setSpecificationGoodsDataFunc(specificationGoods);
    }
  };

  // Specification 点击删除
  const onSpecificationGoodsRemove = (index: number) => {
    const newSpecificationGoodsList = createNewSpecificationGoodsList();
    const specificationItem = newSpecificationGoodsList[index];

    const lowerShelfCount = getCurrentGoodsItem()?.lowerShelfCount || 0;
    const invalidCount = getCurrentGoodsItem()?.invalidCount || 0;
    if (lowerShelfCount && specificationItem.groupDetailStatus === 1) {
      getCurrentGoodsItem().lowerShelfCount = lowerShelfCount - 1;
    }
    if (invalidCount && specificationItem.groupDetailStatus === 2) {
      getCurrentGoodsItem().invalidCount = invalidCount - 1;
    }
    newSpecificationGoodsList.splice(index, 1);
    const skuIdIndex = allCheckedSkuIds.indexOf(specificationItem.id);
    if (skuIdIndex !== -1) {
      allCheckedSkuIds.splice(skuIdIndex, 1);
      setAllCheckedSkuIds(allCheckedSkuIds);
    }
    getCurrentGoodsItem().status = processSpuStatus(newSpecificationGoodsList);
    setItemError(getCurrentGoodsItem());
    setGoodsData([...goodsData]);
    setSpecificationGoods([...newSpecificationGoodsList]);
  };

  // 切换单位
  const onUnitSelectChange = (
    val: NewGetMcsPackageSpuSkuListResult,
    index: number,
    unit: string
  ) => {
    const newVal = val;
    getMcsPackageSkuQuantityByUnit({
      skuId: val.skuId,
      quantity: Number(val.quantity || 0),
      beforeUnit: val.unit,
      afterUnit: unit,
    }).then((res) => {
      newVal.unit = res.unit;
      newVal.saleGroup = res.saleGroup;
      newVal.quantity = res.quantity;
      if (goodsData[currentGoodsRef.current.index]?.specificationGoodsList) {
        const newSpecificationGoodsList = createNewSpecificationGoodsList();
        newSpecificationGoodsList[index] = newVal;
      }
      setSpecificationGoods([...specificationGoods]);
      setGoodsData([...goodsData]);
    });
  };

  const initBatchData = () => {
    setIsBatch(false);
    setCheckAll(false);
    setCheckedList([]);
    setIndeterminate(false);
  };

  const onLast = () => {
    // const errObj = checkNext();
    // if (errObj.statusError) {
    //   message.error('存在下架或失效的商品，请刷新商品到最新的状态，移除下架状态的商品');
    //   return;
    // }
    // if (errObj.numError) {
    //   message.error('存在搭配数量不符合规范的项');
    //   return;
    // }
    setPackageDataToStorage();
    navigate(
      goodsId ? `/goods-package/base-info?goodsId=${goodsId}&isBack=1` : '/goods-package/base-info'
    );
  };

  // 处理checkedSkuIds
  const processCheckedSkuIds = (values: NewGetMcsPackageSpuListResult[]) => {
    const skuIds: number[] = [];
    values.forEach((item) => {
      if (item?.specificationGoodsList?.length) {
        item.specificationGoodsList.forEach((skuItem) => {
          skuIds.push(skuItem.id);
        });
      }
    });
    const checkedIds = Array.from(new Set(allCheckedSkuIds.concat(skuIds)));
    setAllCheckedSkuIds([...checkedIds]);
  };

  // 下一步
  const onNext = () => {
    if (goodsData.length < 2) {
      message.error(t('error_minimum_child_products'));
      return;
    }
    const errObj = checkNext();
    if (errObj.statusError) {
      message.error(t('error_invalid_product_status'));
      return;
    }
    if (errObj.numError) {
      message.error(t('error_invalid_quantity'));
      return;
    }
    if (errObj.noSpecificationGoodsList.length) {
      const nameArr = errObj.noSpecificationGoodsList.map((item) => item.name);
      const nameText = nameArr.join(',');
      const messageText = t('error_no_specification', { nameText });
      message.error(messageText);
      return;
    }
    if (errObj.replaceNoSpecificationGoodsList.length) {
      const nameArr = errObj.replaceNoSpecificationGoodsList.map((item) => item.name);
      const nameText = nameArr.join(',');
      const messageText = t('error_replace_no_specification', { nameText });
      message.error(messageText);
      return;
    }
    setPackageDataToStorage();
    navigate(
      goodsId ? `/goods-package/graphic-desc?goodsId=${goodsId}` : '/goods-package/graphic-desc'
    );
  };

  const getPackageSpuSkuData = useCallback(() => {
    if (goodsId) {
      const id = Number(goodsId);
      getMcsPackageGroupDetail({ id }).then((res) => {
        const newGoodsData: NewGetMcsPackageDetailResSku[] = [];
        res.list?.forEach((item) => {
          if (item.replaceSpuList?.length) {
            const newAllGoodsArr: NewGetMcsPackageDetailResSku[] = item.replaceSpuList?.map(
              (spuItem) => ({
                productId: spuItem.productId,
                groupId: spuItem.groupId,
                images: spuItem.images,
                name: spuItem.name,
                isMainSpu: spuItem.isMainSpu,
                replaceCount: spuItem.replaceCount,
                standardTotal: spuItem.standardTotal,
                checkStandardTotal: spuItem.checkStandardTotal,
                replaceList: [],
                active: false,
                popoverVisible: false,
                status: spuItem.status,
                invalidCount: spuItem.invalidCount,
                lowerShelfCount: spuItem.lowerShelfCount,
                isError: Boolean(
                  (spuItem.skuList || []).filter(
                    (skuItem) => !skuItem.quantity || skuItem.groupDetailStatus
                  ).length
                ),
                isGift: item.isGift || 0,
                source: spuItem.source || 1,
                specificationGoodsList: (spuItem.skuList || []).map((skuItem) => ({
                  ...skuItem,
                  isGift: item.isGift || 0,
                  quantity: skuItem.quantity || skuItem.saleGroup,
                  unitList: [{ label: skuItem.unit, value: skuItem.unit }],
                })),
              })
            );
            // 第一个是spu，剩下的都是替换商品
            if (newAllGoodsArr.length > 1) {
              const arr = [...newAllGoodsArr];
              arr.splice(0, 1);
              newAllGoodsArr[0].replaceList = arr;
            }
            if (newAllGoodsArr.length) {
              newGoodsData.push(newAllGoodsArr[0]);
            }
          }
        });
        setGoodsData(newGoodsData);
        if (newGoodsData.length) {
          setCurrentGoods(newGoodsData[0]);
          setSpecificationGoods(newGoodsData[0].specificationGoodsList || []);
          currentGoodsRef.current.index = 0;
        }
      });
    }
  }, [goodsId]);

  // 添加子商品
  const onAddBtn = usePermission('M_001_005_002_001_001', () => {
    setChildGoodsVisible(true);
  });

  // 删除商品
  const onDelGoods = usePermission(
    'M_001_005_002_001_005',
    (item: NewGetMcsPackageDetailResSku, index: number) => {
      onClickRemoveGoods(item, index);
    }
  );

  // 删除商品
  const onDeleteGoods = usePermission(
    'M_001_005_002_001_005',
    (item: NewGetMcsPackageDetailResSku, index: number) => {
      const newItem = item;
      newItem.popoverVisible = false;
      setGoodsData([...goodsData]);
      Modal.confirm({
        title: t('public_tips'),
        content: t('confirm_delete_goods'),
        okText: t('base_confirm'),
        cancelText: t('base_cancel'),
        centered: true,
        keyboard: false,
        onCancel: () => {},
        onOk: () => {
          onClickRemoveGoodsFn(index, true);
        },
      });
    }
  );

  // 设为赠品
  const onSetGift = usePermission(
    'M_001_005_002_001_006',
    (item: NewGetMcsPackageDetailResSku, index: number) => {
      const newItem = item;
      newItem.popoverVisible = false;
      Modal.confirm({
        title: t('public_tips'),
        content: t('confirm_set_as_gift'),
        okText: t('service_confirm'),
        cancelText: t('base_cancel'),
        centered: true,
        keyboard: false,
        onCancel: () => {},
        onOk: () => {
          const goodsItem = goodsData[index];
          goodsItem.isGift = 1;
          goodsItem.specificationGoodsList?.forEach((specificationItem) => {
            const newSpecificationItem = specificationItem;
            newSpecificationItem.isGift = 1;
          });
          goodsItem.replaceList?.forEach((skuItem) => {
            const newSkuItem = skuItem;
            newSkuItem.isGift = 1;
          });
          setGoodsData([...goodsData]);
        },
      });
    }
  );

  // 取消赠品
  const unSetGift = usePermission(
    'M_001_005_002_001_006',
    (item: NewGetMcsPackageDetailResSku, index: number) => {
      const newItem = item;
      newItem.popoverVisible = false;
      Modal.confirm({
        title: t('public_tips'),
        content: t('confirm_unset_gift'),
        okText: t('base_confirm'),
        cancelText: t('base_cancel'),
        centered: true,
        keyboard: false,
        onCancel: () => {},
        onOk: () => {
          const goodsItem = goodsData[index];
          goodsItem.isGift = 0;
          goodsItem.specificationGoodsList?.forEach((specificationItem) => {
            const newSpecificationItem = specificationItem;
            newSpecificationItem.isGift = 0;
          });
          goodsItem.replaceList?.forEach((skuItem) => {
            const newSkuItem = skuItem;
            newSkuItem.isGift = 0;
          });
          setGoodsData([...goodsData]);
        },
      });
    }
  );

  // 添加可替换商品
  const addReplaceGoods = usePermission('M_001_005_002_001_004', () => {
    setReplaceGoodsVisible(true);
  });

  // 删除可替换商品
  const delReplaceGoods = usePermission(
    'M_001_005_002_001_004',
    (
      item: NewGetMcsPackageDetailResSku,
      index: number,
      replaceItem: NewGetMcsPackageDetailResSku,
      replaceIndex: number
    ) => {
      const replaceList = item?.replaceList || [];
      const spuIds = replaceFilterSpuIds.filter((id) => id !== replaceList[replaceIndex].productId);
      if (replaceItem.active) {
        // const replaceList = goodsData[index]?.replaceList || [];
        // 只有一个替换
        if (replaceIndex === 0 && replaceIndex === replaceList.length - 1) {
          goodsData[index].active = true;
          const newSpecificationGoodsList = goodsData[index]?.specificationGoodsList || [];
          setSpecificationGoods([...newSpecificationGoodsList]);
          currentGoodsRef.current.type = 1;
          currentGoodsRef.current.index = index;
          setCurrentGoods({ ...goodsData[index] });
        } else if (replaceIndex === replaceList.length - 1) {
          // 移除的是最后一个
          const goodsItem = replaceList[replaceIndex - 1];
          goodsItem.active = true;
          setSpecificationGoods([...(goodsItem?.specificationGoodsList || [])]);
          currentGoodsRef.current.replaceIndex = replaceIndex - 1;
          setCurrentGoods({ ...goodsData[index] });
        } else if (replaceIndex < replaceList.length - 1) {
          // 移除的不是最后一个
          const goodsItem = replaceList[replaceIndex + 1];
          goodsItem.active = true;
          setSpecificationGoods([...(goodsItem?.specificationGoodsList || [])]);
          currentGoodsRef.current.replaceIndex = replaceIndex - 1;
          setCurrentGoods({ ...goodsData[index] });
        }
      }
      goodsData[index]?.replaceList?.splice(replaceIndex, 1);
      setGoodsData([...goodsData]);
      setReplaceFilterSpuIds([...spuIds]);
      if (replaceItem.id) {
        const i = allCheckedSkuIds.indexOf(replaceItem.id);
        allCheckedSkuIds.splice(i, 1);
        setAllCheckedSkuIds(allCheckedSkuIds);
      }
    }
  );

  // 删除商品规格
  const onDelSpec = usePermission('M_001_005_002_001_001', (index: number) => {
    onSpecificationGoodsRemove(index);
  });

  // 添加商品规格
  const onAddSpec = usePermission('M_001_005_002_001_001', () => {
    setSelectSpecificationsVisible(true);
    const goodsList =
      currentGoods.specificationGoodsList?.filter((item) => item.groupDetailStatus !== 1) || [];
    const newCurrentGoods = {
      ...currentGoods,
      id: currentGoods.productId || 0,
      specificationGoodsList: goodsList,
    };
    const goodsIds = goodsList?.map((item) => item.id) || [];
    const filterIds: number[] = allCheckedSkuIds.filter((item) => !goodsIds.includes(item));
    setSelectSpecificationsFilterSkuIds(filterIds);
    setSelectSpecificationsMainItem(newCurrentGoods);
  });

  // 搭配数量
  const numberPerm = usePermission('M_001_005_002_001_002_002');

  // 单位
  const unitPerm = usePermission('M_001_005_002_001_002_001');

  // 批量设置
  const onBatch = usePermission('M_001_005_002_001_003', () => {
    setIsBatch(true);
    setCheckAll(false);
    setCheckedList([]);
    setIndeterminate(false);
  });

  useEffect(() => {
    setSpecificationGoods([]);
    setTimeout(() => {
      getStorage<GoodsPackageDataStorage>(goodsPackageStorageKey).then((res) => {
        if (res) {
          packageData.current = res;
          const skuIds: number[] = [];
          if (packageData?.current?.combinationInfo?.goodsData?.length) {
            const combinationInfoGoodsData = packageData?.current?.combinationInfo?.goodsData;
            combinationInfoGoodsData.forEach((goodsItem, index) => {
              const newGoodsItem = goodsItem;
              newGoodsItem.popoverVisible = false;
              newGoodsItem.active = index === 0;
              let isError = false;
              goodsItem.specificationGoodsList?.forEach((skuItem) => {
                if (!skuItem.quantity || skuItem.groupDetailStatus) {
                  isError = true;
                }
                skuIds.push(skuItem.id);
              });
              newGoodsItem.isError = isError;
              goodsItem.replaceList?.forEach((replaceItem) => {
                const newReplaceItem = replaceItem;
                newReplaceItem.active = false;
                let isErr = false;
                replaceItem.specificationGoodsList?.forEach((skuItem) => {
                  if (!skuItem.quantity || skuItem.groupDetailStatus) {
                    isErr = true;
                  }
                  skuIds.push(skuItem.id);
                });
                newReplaceItem.isError = isErr;
              });
            });
            setAllCheckedSkuIds(skuIds);
            setGoodsData(combinationInfoGoodsData);
            setCurrentGoods(combinationInfoGoodsData[0]);
            setSpecificationGoods(combinationInfoGoodsData[0].specificationGoodsList || []);
            currentGoodsRef.current.index = 0;
          } else {
            getPackageSpuSkuData();
          }
        } else {
          getPackageSpuSkuData();
        }
      });
    }, 300);
  }, [getPackageSpuSkuData]);

  const createErrorElement = (item: NewGetMcsPackageDetailResSku) => {
    let element: ReactNode | string = '';
    if (item?.invalidCount || item?.lowerShelfCount) {
      if (
        item.invalidCount !== item?.specificationGoodsList?.length &&
        item.lowerShelfCount !== item?.specificationGoodsList?.length
      ) {
        let text = '';
        if (item?.invalidCount && item?.lowerShelfCount) {
          text = ',';
        }
        element = (
          <div className={styles.isError}>
            {item?.invalidCount ? (
              <span>{t('invalid_count', { count: item?.invalidCount })}</span>
            ) : (
              ''
            )}
            {item?.lowerShelfCount ? (
              <span>
                {text}
                {t('lower_shelf_count', { count: item?.lowerShelfCount })}
              </span>
            ) : (
              ''
            )}
          </div>
        );
      } else if (item.invalidCount === item?.specificationGoodsList?.length) {
        element = <div className={styles.isError}>{t('all_invalid')}</div>;
      } else if (item.lowerShelfCount === item?.specificationGoodsList?.length) {
        element = <div className={styles.isError}>{t('all_lower_shelf')}</div>;
      }
    }
    return element;
  };

  const createGoodsStatusElement = (item: { status: number; isGift: number }) => {
    let element: ReactNode | string = '';
    switch (item.status) {
      case 2:
        element = (
          <div className={classNames(styles.goodsStatusTag, styles.goodsStatusTagInvalid)}>
            {t('invalid')}
          </div>
        );
        break;
      case 1:
        element = (
          <div className={classNames(styles.goodsStatusTag, styles.goodsStatusTagSoldOut)}>
            {t('lower_shelf')}
          </div>
        );
        break;

      default:
        break;
    }
    if (item.isGift) {
      element = (
        <div className={classNames(styles.goodsStatusTag, styles.goodsStatusTagGift)}>
          {t('gift')}
        </div>
      );
    }

    return element;
  };

  return (
    <Context
      className={styles.packageContent}
      permission={{ code: 'M_001_005_002', newLogic: true }}
      head={
        <Context.Head
          title={[
            { title: t('goods_management'), to: '/shop/goods/list' },
            { title: goodsId ? t('edit_package') : t('new_package') },
          ]}
        />
      }
    >
      <StepBar
        data={stepBarData}
        // onClick={(item) => {
        //   setPackageDataToStorage();
        //   navigate(item?.url || '');
        // }}
      />
      <div className={styles.packageContentInfo}>
        <div className={styles.infoHead}>
          <div className={styles.infoHeadText}>
            {t('base_combinationDetail')}
            <div className={styles.infoHeadTextDesc}>
              （<span style={{ color: '#EA1C26' }}>*</span>
              <span>{t('required_field_note')}</span>）
            </div>
          </div>
          {goodsData.length ? (
            <div className={styles.infoHeadOperate}>
              <Button type="primary" size="small" onClick={onAddBtn}>
                {t('add_child_product')}
              </Button>
            </div>
          ) : (
            ''
          )}
        </div>
        <div className={styles.infoContent}>
          {goodsData.length ? (
            <div className={styles.goodsContent}>
              <div className={styles.goodsContentLeft}>
                {goodsData?.map((item, index) => (
                  <div
                    className={classNames(
                      styles.mainGoodsItem,
                      item.active ? styles.goodsActive : ''
                    )}
                    key={item.groupId || item.productId}
                  >
                    <div
                      className={styles.mainGoodsItemBox}
                      role="presentation"
                      onClick={() => {
                        onClickGoodsItem(item, index);
                      }}
                    >
                      <div className={styles.mainGoodsItemHead}>
                        <div className={styles.mainGoodsItemHeadLeft}>
                          {t('child_product_info', { index: index + 1 })}
                        </div>
                        <div className={styles.mainGoodsItemHeadRight}>
                          <Popover
                            placement="bottom"
                            visible={item.popoverVisible}
                            content={
                              <div className={styles.popoverContent}>
                                <div
                                  className={styles.popoverItem}
                                  role="presentation"
                                  onClick={() => {
                                    onDeleteGoods(item, index);
                                  }}
                                >
                                  {t('delete')}
                                </div>
                                {item.isGift ? (
                                  <div
                                    className={styles.popoverItem}
                                    role="presentation"
                                    onClick={() => {
                                      unSetGift(item, index);
                                    }}
                                  >
                                    {t('unset_gift')}
                                  </div>
                                ) : (
                                  <div
                                    className={styles.popoverItem}
                                    role="presentation"
                                    onClick={() => {
                                      onSetGift(item, index);
                                    }}
                                  >
                                    {t('set_as_gift')}
                                  </div>
                                )}
                              </div>
                            }
                            trigger="click"
                            onVisibleChange={(val) => {
                              const newItem = item;
                              newItem.popoverVisible = val;
                              setGoodsData([...goodsData]);
                            }}
                          >
                            <div className={styles.curP}>
                              <Icon
                                name="zu13366"
                                size={24}
                                onClick={() => {
                                  const newItem = item;
                                  newItem.popoverVisible = true;
                                }}
                              />
                            </div>
                          </Popover>
                        </div>
                      </div>
                      <div className={styles.mainGoodsItemContent}>
                        <div className={styles.mainGoodsImage}>
                          <div className={styles.mainGoodsImageItem}>
                            <img src={item.images} alt="" />
                          </div>
                          {createGoodsStatusElement({
                            status: item.status || 0,
                            isGift: item.isGift || 0,
                          })}
                        </div>

                        <div className={styles.mainGoodsInfo}>
                          <div
                            className={classNames(
                              styles.mainGoodsInfoName
                              // item.isError ? styles.isError : ''
                            )}
                          >
                            {item?.source === 2 ? (
                              <span className={styles.shareTag}>{t('specification_shared')}</span>
                            ) : (
                              ''
                            )}
                            {item.name}
                          </div>
                          <div className={styles.mainGoodsInfoSpecification}>
                            <span className={styles.mainGoodsInfoSpecificationSelect}>
                              {t('combination_selected_specifications', {
                                count: item?.specificationGoodsList?.length,
                              })}
                            </span>
                            <span>
                              {t('combination_total_specifications', {
                                count: item?.standardTotal,
                              })}
                            </span>
                          </div>
                          {createErrorElement(item)}
                        </div>
                        <div className={styles.mainGoodsOperate}>
                          <Icon
                            name="close-circle2"
                            color="rgba(0, 0, 0, 0.3)"
                            size={20}
                            onClick={() => {
                              onDelGoods(item, index);
                            }}
                          />
                        </div>
                      </div>
                      <div className={styles.mainGoodsItemFooter}>
                        <div className={styles.mainGoodsItemFooterLeft}>
                          {item?.replaceList?.length ? (
                            <div
                              className={styles.curP}
                              role="presentation"
                              onClick={() => {
                                goodsData[index].showReplace = !item.showReplace;
                                setGoodsData([...goodsData]);
                              }}
                            >
                              {t('combination_replaceable_products', {
                                count: item?.replaceList?.length,
                              })}
                              <Icon name={item.showReplace ? 'up' : 'down'} size={16} />
                            </div>
                          ) : (
                            <div>{t('combination_no_replaceable_products')}</div>
                          )}
                        </div>
                        <div
                          className={styles.mainGoodsItemFooterRight}
                          role="presentation"
                          onClick={addReplaceGoods}
                        >
                          {t('combination_add_replaceable')}
                        </div>
                      </div>
                    </div>

                    {item.showReplace ? (
                      <div className={styles.replaceGoods}>
                        {item?.replaceList?.map((replaceItem, replaceIndex) => (
                          <div
                            className={classNames(
                              styles.replaceGoodsItem,
                              replaceItem.active ? styles.goodsActive : ''
                            )}
                            key={replaceItem.productId}
                            role="presentation"
                            onClick={() => {
                              onClickReplaceItem(replaceItem, replaceIndex, index);
                            }}
                          >
                            <div className={styles.replaceGoodsItemImage}>
                              <div className={styles.replaceGoodsItemImageItem}>
                                <img src={replaceItem.images} alt="" />
                              </div>
                              {createGoodsStatusElement({
                                status: replaceItem.status || 0,
                                isGift: replaceItem.isGift || 0,
                              })}
                            </div>

                            <div className={styles.replaceGoodsItemInfo}>
                              <div
                                className={classNames(
                                  styles.replaceGoodsItemInfoName
                                  // replaceItem.isError ? styles.isError : ''
                                )}
                              >
                                {replaceItem.name}
                              </div>
                              <div className={styles.replaceGoodsItemInfoSpecification}>
                                <span className={styles.replaceGoodsItemInfoSpecificationSelect}>
                                  {t('combination_selected_specifications', {
                                    count: replaceItem.specificationGoodsList?.length,
                                  })}
                                </span>
                                <span>
                                  {t('combination_total_specifications', {
                                    count: replaceItem.standardTotal,
                                  })}
                                </span>
                              </div>
                              {createErrorElement(replaceItem)}
                            </div>
                            <div className={styles.replaceGoodsItemOperate}>
                              <Icon
                                name="close-circle2"
                                color="rgba(0, 0, 0, 0.3)"
                                size={20}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  delReplaceGoods(item, index, replaceItem, replaceIndex);
                                }}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      ''
                    )}
                  </div>
                ))}
              </div>
              <div className={styles.goodsContentRight}>
                <div className={styles.goodsContentRightHead}>
                  <div className={styles.goodsContentRightHeadLeft}>{currentGoods.name}</div>
                  <div className={styles.goodsContentRightHeadRight}>
                    {isBatch ? (
                      ''
                    ) : (
                      <div
                        className={styles.addGoodsSpecification}
                        role="presentation"
                        onClick={onAddSpec}
                      >
                        {t('add_goods_specification')}
                      </div>
                    )}
                  </div>
                </div>
                <div className={styles.goodsContentRightContent}>
                  <Checkbox.Group
                    style={{ width: '100%' }}
                    onChange={(values: CheckboxValueType[]) => {
                      const checkedValues = values.map((item) => Number(item));
                      onCheckboxChange(checkedValues);
                    }}
                    value={checkedList}
                  >
                    {specificationGoods?.map((item, index) => (
                      <div className={styles.specificationGoods} key={item.id}>
                        <div className={styles.specificationGoodsItem}>
                          <div className={styles.specificationGoodsItemInfo}>
                            {isBatch ? (
                              <div className={styles.specificationGoodsItemInfoCheckbox}>
                                <Checkbox value={item.id} />
                              </div>
                            ) : (
                              ''
                            )}
                            <div className={styles.specificationGoodsItemInfoImage}>
                              <div className={styles.rightImgBox}>
                                <img src={item.images} alt="" />
                              </div>

                              {createGoodsStatusElement({
                                status: item.groupDetailStatus || 0,
                                isGift: 0,
                              })}
                            </div>

                            <div className={styles.specificationGoodsItemInfoInfo}>
                              <div
                                className={styles.specificationGoodsItemInfoInfoName}
                                title={item.name}
                              >
                                {item?.source === 2 ? (
                                  <span className={styles.shareTag}>
                                    {t('specification_shared')}
                                  </span>
                                ) : (
                                  ''
                                )}
                                {item.name}
                              </div>
                              <div className={styles.specificationGoodsItemInfoInfoText}>
                                {createSpecifications(item.standardList)}
                              </div>
                              <div className={styles.specificationGoodsItemInfoInfoText}>
                                {t('base_minSaleUnit')} {item.saleGroup}
                              </div>
                            </div>
                            <div className={styles.specificationGoodsItemInfoOperate}>
                              <Icon
                                name="close-circle2"
                                color="rgba(0, 0, 0, 0.3)"
                                size={20}
                                onClick={() => {
                                  onDelSpec(index);
                                }}
                                className={styles.specificationGoodsItemInfoOperateIcon}
                              />
                              {[2].includes(item.groupDetailStatus || 0) ? (
                                <span
                                  className={styles.specificationGoodsItemInfoOperateRefresh}
                                  role="presentation"
                                  onClick={() => {
                                    onSpecificationGoodsRefresh(index);
                                  }}
                                >
                                  {t('refresh')}
                                </span>
                              ) : (
                                ''
                              )}
                            </div>
                          </div>
                          <div className={styles.specificationGoodsItemOperate}>
                            <div className={styles.specificationGoodsItemOperateItem}>
                              <div className={styles.specificationGoodsItemOperateItemLabel}>
                                {t('base_unit')}
                              </div>
                              <div className={styles.specificationGoodsItemOperateItemValue}>
                                <Select
                                  value={item.unit}
                                  style={{ width: 120 }}
                                  bordered={false}
                                  onFocus={() => {
                                    if (item.unitTemplateId) {
                                      formulaConvert(item.unitTemplateId).then((res) => {
                                        const newItem = item;
                                        const data = res.detailList || [];
                                        const unitList = data?.map((unitTemplateItem) => ({
                                          label: unitTemplateItem.unitName || '',
                                          value: unitTemplateItem.unitName || '',
                                        }));
                                        newItem.unitList = unitList;
                                        setGoodsData([...goodsData]);
                                      });
                                    }
                                  }}
                                  onChange={(val) => {
                                    if (testPerm('M_001_005_002_001_002_001')) {
                                      onUnitSelectChange(item, index, val);
                                    }
                                  }}
                                  onClick={unitPerm}
                                >
                                  {item.unitList.map(
                                    (unitItem: { label: string; value: string }) => (
                                      <Select.Option value={unitItem.value} key={unitItem.value}>
                                        {unitItem.label}
                                      </Select.Option>
                                    )
                                  )}
                                </Select>
                              </div>
                            </div>
                            <div className={styles.specificationGoodsItemOperateItem}>
                              <div className={styles.specificationGoodsItemOperateItemLabel}>
                                {t('base_quantity')}
                              </div>
                              <div className={styles.specificationGoodsItemOperateItemValue}>
                                <StepperInput
                                  value={item.quantity}
                                  min={item.saleGroup}
                                  readonly={!checkPermission('M_001_005_002_001_002_002')}
                                  onChange={(val) => {
                                    if (!checkPermission('M_001_005_002_001_002_002')) return;
                                    // @ts-ignore
                                    specificationGoods[index].quantity = val;
                                    if (
                                      goodsData[currentGoodsRef?.current?.index]
                                        ?.specificationGoodsList
                                    ) {
                                      const newSpecificationGoodsList =
                                        createNewSpecificationGoodsList();
                                      newSpecificationGoodsList[index].quantity = val;
                                    }
                                    setGoodsData([...goodsData]);
                                    setSpecificationGoods([...specificationGoods]);
                                  }}
                                  stepperNumber={item.saleGroup}
                                  onBlur={(value) => {
                                    if (!checkPermission('M_001_005_002_001_002_002')) return;
                                    let val = value;
                                    if (val) {
                                      const offset = Math.ceil(Number(val) / item.saleGroup);
                                      val = offset * item.saleGroup;
                                    } else {
                                      val = item.saleGroup;
                                    }
                                    // @ts-ignore
                                    specificationGoods[index].quantity = val;
                                    if (
                                      goodsData[currentGoodsRef?.current?.index]
                                        ?.specificationGoodsList
                                    ) {
                                      const newSpecificationGoodsList =
                                        createNewSpecificationGoodsList();
                                      newSpecificationGoodsList[index].quantity = val;
                                    }

                                    setGoodsData([...goodsData]);

                                    setSpecificationGoods([...specificationGoods]);
                                  }}
                                  onClick={numberPerm}
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </Checkbox.Group>
                </div>

                <div className={styles.goodsContentRightFooter}>
                  {isBatch ? (
                    <>
                      <div className={styles.goodsContentRightFooterLeft}>
                        <Checkbox
                          indeterminate={indeterminate}
                          onChange={onCheckAllChange}
                          checked={checkAll}
                        />
                        <span className={styles.goodsContentRightFooterLeftText}>
                          {t('selected_items', { count: checkedList.length })}
                        </span>
                        <Icon
                          className={styles.curP}
                          name="trash"
                          size={20}
                          color="#008CFF"
                          onClick={onBatchRemove}
                        />
                      </div>
                      <div
                        className={styles.goodsContentRightFooterRight}
                        role="presentation"
                        onClick={() => {
                          setIsBatch(true);
                          if (!checkedList.length) {
                            message.error(t('select_product_specifications_first'));
                            return;
                          }
                          setBatchVisible(true);
                        }}
                      >
                        {t('set_quantity')}
                      </div>
                      {isBatch ? (
                        <div
                          className={styles.cancelBatchBtn}
                          role="presentation"
                          onClick={() => {
                            initBatchData();
                          }}
                        >
                          {t('public_cancel')}
                        </div>
                      ) : (
                        ''
                      )}
                    </>
                  ) : (
                    <>
                      <div className={styles.goodsContentRightFooterLeft}>
                        {t('total_specifications', {
                          count: currentGoods.specificationGoodsList?.length,
                        })}
                      </div>
                      <div
                        className={styles.goodsContentRightFooterRight}
                        role="presentation"
                        onClick={onBatch}
                      >
                        {t('batch_settings')}
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          ) : (
            <div className={styles.noData}>
              <div className={styles.noDataText}>{t('no_products_in_package')}</div>
              <Button
                type="primary"
                icon={<Icon name="plus" style={{ marginRight: 6 }} />}
                onClick={onAddBtn}
              >
                {t('add_child_product')}
              </Button>
            </div>
          )}
        </div>

        {/* 添加子商品 */}
        <AddChildGoodsDrawer
          visible={childGoodsVisible}
          filterSkuIds={allCheckedSkuIds}
          onConfirm={(values) => {
            processCheckedSkuIds(values);
            initBatchData();
            const newGoodsData = values.map((item) => ({
              productId: item.id,
              images: item.images,
              name: item.name,
              isMainSpu: 1,
              replaceCount: 0,
              standardTotal: item.standardTotal,
              checkStandardTotal: item?.specificationGoodsList?.length || 0,
              replaceList: [],
              specificationGoodsList: item?.specificationGoodsList || [],
              active: false,
            }));
            const goodsDataArr = [...goodsData, ...newGoodsData];
            setGoodsData(goodsDataArr);
            if (newGoodsData.length) {
              initGoodsDataActive();
              newGoodsData[newGoodsData.length - 1].active = true;
              setCurrentGoods(newGoodsData[newGoodsData.length - 1]);
              setSpecificationGoods(newGoodsData[newGoodsData.length - 1].specificationGoodsList);
              currentGoodsRef.current.index = goodsDataArr.length - 1;
            }
          }}
          onClose={() => {
            setChildGoodsVisible(false);
          }}
        />

        {/* 添加替换商品 */}
        <AddChildGoodsDrawer
          title={t('add_replaceable_product')}
          visible={replaceGoodsVisible}
          filterSpuIds={replaceFilterSpuIds}
          filterSkuIds={allCheckedSkuIds}
          onConfirm={(values) => {
            processCheckedSkuIds(values);
            const valuesList = values.map((item) => ({
              ...item,
              isMainSpu: 1,
              replaceCount: 0,
              replaceList: [],
              isGift: getCurrentGoodsItem().isGift || 0,
              checkStandardTotal: item.specificationGoodsList?.length || 0,
              specificationGoodsList: (item?.specificationGoodsList || []).map(
                (specificationItem) => ({
                  ...specificationItem,
                  isGift: getCurrentGoodsItem().isGift || 0,
                })
              ),
            }));
            const replaceList = [
              ...(goodsData[currentGoodsRef.current.index]?.replaceList || []),
              ...valuesList,
            ];

            goodsData[currentGoodsRef.current.index].replaceList = replaceList;
            setCurrentGoods({ ...goodsData[currentGoodsRef.current.index] });
            setGoodsData([...goodsData]);
          }}
          onClose={() => {
            setReplaceGoodsVisible(false);
          }}
        />

        {/* 添加商品规格 */}
        <SelectSpecificationsDrawer
          mainItem={selectSpecificationsMainItem}
          filterSkuIds={selectSpecificationsFilterSkuIds}
          visible={selectSpecificationsVisible}
          onConfirm={(values) => {
            // processCheckedSkuIds(values);
            const skuIds = values.map((item) => item.id);
            const checkedIds = Array.from(new Set(allCheckedSkuIds.concat(skuIds)));
            setAllCheckedSkuIds([...checkedIds]);
            setSpecificationGoodsDataFunc(values);
            setSelectSpecificationsVisible(false);
          }}
          onClose={() => {
            setSelectSpecificationsVisible(false);
          }}
        />

        {/* 批量设置 */}
        {/* <BatchSettingsDrawer
          mainItem={{ ...currentGoods, id: currentGoods.productId || 0 }}
          visible={batchVisible}
          checkedList={checkedSkuIdList || []}
          onConfirm={(val) => {
            batchSettingUnitNum(val);
          }}
          onClose={() => {
            setBatchVisible(false);
          }}
        /> */}

        {/* 批量设置单位和数量 */}
        <BatchSettingsModal
          mainItem={{ ...currentGoods, id: currentGoods.productId || 0 }}
          visible={batchVisible}
          checkedList={checkedSkuIdList || []}
          onConfirm={(val) => {
            batchSettingUnitNum(val);
          }}
          onClose={() => {
            setBatchVisible(false);
          }}
        />

        <div className={styles.infoFooter}>
          <Button type="default" style={{ marginRight: 12 }} onClick={onLast}>
            {t('previous_step')}
          </Button>
          <Button type="primary" disabled={Boolean(goodsData.length === 0)} onClick={onNext}>
            {t('next_step')}
          </Button>
          <div
            className={styles.infoFooterLastBtn}
            role="presentation"
            onClick={() => {
              navigate('/shop/goods/list/?type=pack');
              removeStorage(goodsPackageStorageKey);
            }}
          >
            {t('public_cancel')}
          </div>
        </div>
      </div>
    </Context>
  );
}
