.packageContent {
  background: transparent !important;

  &Info {
    width: 100%;
    height: calc(100% - 80px - 20px);
    margin-top: 20px;
    border-radius: 18px;
    background: white;
    box-shadow: @box-shadow-base;

    .infoHead {
      font-size: 16px;
      display: flex;
      height: 44px;
      margin-bottom: 10px;
      padding: 0 20px;
      justify-content: space-between;

      &Text {
        height: 44px;
        line-height: 44px;
      }

      &Operate {
        color: @primary-color;
        height: 44px;
        line-height: 44px;
        cursor: pointer;
      }
    }

    .infoContent {
      display: flex;
      flex-direction: row;
      height: calc(100% - 342px);
      min-height: 351px;
      padding: 0 20px;

      &Left {
        width: 250px;

        .bigImage {
          display: flex;
          width: 230px;
          height: 230px;
          justify-content: center;
          border-radius: 10px;
          align-items: center;
          border: 1px dashed #b1b3be;
          cursor: pointer;

          &Image {
            display: flex;
            width: 230px;
            height: 230px;
            overflow: hidden;
            justify-content: center;
            align-items: center;
            border-radius: 10px;
          }
        }

        .smallImage {
          display: flex;
          width: 230px;
          margin-top: 16px;

          .imageBox {
            position: relative;
          }

          .smallImageBox {
            display: flex;
            width: 30px;
            height: 30px;
            overflow: hidden;
            justify-content: center;
            align-items: center;
            border-radius: 6px;
          }

          .smallImageItemBoxIcon {
            font-size: 12px;
            position: absolute;
            top: -5px;
            right: -5px;
            z-index: 9999;
          }

          &Item {
            color: #b1b3be;
            display: flex;
            width: 30px;
            height: 30px;
            margin-right: 10px;
            justify-content: center;
            border: 1px dashed #b1b3be;
            border-radius: 6px;
            align-items: center;
            cursor: pointer;
          }
        }

        .imageDesc {
          color: @text-colors[secondary];
          margin-top: 10px;
          margin-right: 20px;
        }
      }

      &Right {
        flex: 1;
      }
    }

    .bottomContent {
      height: 230px;
      padding: 0 20px;

      .video {
        // margin-top: 31px;

        .videoLabel {
          color: #040919;
          font-size: 16px;
          margin-bottom: 16px;
        }

        .videoContentWrap {
          display: flex;
          align-items: center;
        }

        .videoContent {
          width: 50px;
          height: 50px;
          // margin-left: 40px;
          :global {
            .ant-spin-nested-loading > div > .ant-spin {
              min-height: 50px;
            }
          }

          .coverVideo {
            visibility: hidden;
            position: fixed;
            top: 0;
          }

          .videoImageCanvas {
            display: none;
          }

          .videoWrap {
            width: 50px;
            height: 50px;
            position: relative;
            border: 1px dashed #b1b3be;
            border-radius: 6px;

            .closeImage {
              width: 16px;
              height: 16px;
              position: absolute;
              top: 0;
              right: 0;
              z-index: 99;
            }
          }

          .uploadVideo {
            color: #b1b3be;
            display: flex;
            width: 50px;
            height: 50px;
            justify-content: center;
            align-items: center;
            border: 1px dashed #b1b3be;
            border-radius: 12px;
            cursor: pointer;

            .uploadVideoPlus {
              font-size: 10px;
            }
          }
        }

        .videoDesc {
          color: #888b98;
          font-size: 12px;
          margin-left: 30px;
        }
      }
    }

    .infoFooter {
      width: 100%;
      height: 58px;
      position: relative;
      text-align: center;

      &LastBtn {
        color: @primary-color;
        font-size: 14px;
        position: absolute;
        top: 18px;
        left: 20px;
        cursor: pointer;
      }

      &NextBtn {
        color: @primary-color;
        font-size: 14px;
        position: absolute;
        top: 18px;
        right: 0;
        cursor: pointer;
      }
    }
  }
}

.marketPriceDesc {
  color: #b1b3be;
  font-size: 12px;
  margin-top: -12px;
  margin-bottom: 10px;
  margin-left: 25%;
}
