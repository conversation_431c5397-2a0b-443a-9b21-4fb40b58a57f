import { Select } from 'antd';

import { forwardRef, useImperativeHandle, useState, useEffect, CSSProperties, useRef } from 'react';
import i18n from 'i18next';

const { Option } = Select;
interface OptionItem {
  value: string | number;
  label: string;
}

let timeout: ReturnType<typeof setTimeout> | null;

interface SearchSelectProps {
  defaultOptions?: OptionItem[];
  value: string;
  placeholder?: string;
  style?: CSSProperties;
  onChange: MultipleParamsFn<[val: string]>;
  onFake: MultipleParamsFn<
    [
      type: string,
      val: string,
      data: OptionItem[],
      callback: MultipleParamsFn<[data: OptionItem[]]>
    ]
  >;
  onClick?: () => void;
}

const SearchSelect = forwardRef(
  (
    { defaultOptions, value, placeholder, style, onChange, onFake, onClick }: SearchSelectProps,
    ref
  ) => {
    useImperativeHandle(ref, () => ({}));
    const [data, setData] = useState<OptionItem[]>([]);
    const [selectValue, setSelectValue] = useState<string | null>('');
    const selectedItem = useRef<OptionItem>({ label: '', value: '' });
    const searchKey = useRef<string>('');
    const selectRef = useRef(null);

    const fetch = (type: string, val: string, callback: MultipleParamsFn<[data: OptionItem[]]>) => {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      const fake = async () => {
        await onFake(type, val, data, callback);
      };

      timeout = setTimeout(fake, 800);
    };

    const handleSearch = (newValue: string) => {
      searchKey.current = newValue;
      fetch('search', newValue, setData);
    };

    const handleChange = (newValue: string, option: any) => {
      // @ts-ignore
      selectRef.current?.blur();
      setSelectValue(newValue || null);
      if (newValue) {
        selectedItem.current = { label: option.children, value: option.value };
      } else {
        selectedItem.current = { label: '', value: '' };
      }
      onChange(newValue || '');
    };

    const handleFocus = () => {
      fetch('search', '', setData);
    };

    useEffect(() => {
      onFake('search', '', [], setData);
    }, [onFake]);

    useEffect(() => {
      setSelectValue(value || null);
    }, [value]);

    useEffect(() => {
      setData([...(defaultOptions || [])]);
    }, [defaultOptions]);

    useEffect(() => {
      if (defaultOptions?.length && value) {
        defaultOptions.forEach((item) => {
          if (item.value === value) {
            selectedItem.current = item;
          }
        });
      }
    }, [value, defaultOptions]);

    const options = data.map((d) => <Option key={d.value}>{d.label}</Option>);

    return (
      <Select
        ref={selectRef}
        allowClear
        showSearch
        value={selectValue}
        placeholder={placeholder}
        style={style}
        defaultActiveFirstOption={false}
        showArrow
        filterOption={false}
        onSearch={handleSearch}
        onChange={handleChange}
        onFocus={handleFocus}
        notFoundContent={null}
        getPopupContainer={(triggerNode) => triggerNode.parentNode}
        onPopupScroll={(e) => {
          const { target } = e;
          // @ts-ignore
          if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
            fetch('scrollBottom', searchKey.current || '', setData);
          }
        }}
        onClick={onClick}
      >
        {options}
      </Select>
    );
  }
);

SearchSelect.defaultProps = {
  defaultOptions: [],
  placeholder: i18n.t('please_enter'),
  style: {},
  onClick: () => {},
};

export default SearchSelect;
