@import 'styles/mixins/mixins';

.addChildGoodsDrawer {
  :global {
    .ant-drawer-mask {
      opacity: 0 !important;
      animation: initial !important;
    }

    .ant-drawer-header {
      background-color: #fff;
    }

    .ant-drawer-body {
      display: flex;
      padding: 0 !important;
      flex-direction: column;
    }
  }
}

.drawerHead {
  padding: 8px 20px 12px;
  background-color: #fff;
  border-bottom: 1px solid #f3f3f3;

  .goodsItem {
    display: flex;
    width: 100%;
    min-height: 80px;
    margin-top: 16px;
    padding: 0 12px;

    &Image {
      display: flex;
      width: 80px;
      height: 80px;
      overflow: hidden;
      justify-content: center;
      align-items: center;
      border-radius: 6px;
      border: 1px solid #f5f6fa;
    }

    &Info {
      display: flex;
      margin-left: 12px;
      position: relative;
      flex: 1;
      flex-direction: column;

      &Name {
        .text-overflow(2);

        word-break: break-all;
        margin-bottom: 8px;
      }

      &Num {
        display: flex;
        flex: 1;

        &Text {
          color: @text-colors[secondary];
          font-size: 12px;
          flex: 1;
        }
      }
    }
  }

  &Desc {
    color: @text-colors[secondary];
    font-size: 12px;
    height: 33px;
    padding: 8px 20px;
    background-color: #f5f6fa;
  }
}

.drawerContent {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  flex: 1;

  &Right {
    height: 100%;
    overflow: auto;
    flex: 1;
    background: white;

    &Scroll {
      flex: 1;

      .childGoodsItem {
        width: 100%;
        min-height: 120px;
        margin-top: 12px;
        padding: 0 20px;

        &Info {
          display: flex;
          width: 100%;
          min-height: 80px;

          &Checkbox {
            display: flex;
            margin-right: 20px;
            align-items: center;
          }

          &Image {
            display: flex;
            width: 80px;
            height: 80px;
            overflow: hidden;
            justify-content: center;
            align-items: center;
            border-radius: 6px;
            border: 1px solid #f5f6fa;
          }

          &Info {
            display: flex;
            width: 207px;
            margin-left: 12px;
            position: relative;
            flex: 1;
            flex-direction: column;

            &Name {
              .text-overflow(1);

              word-break: break-all;
              margin-bottom: 8px;
            }

            &Text {
              color: @text-colors[secondary];
              font-size: 12px;
              margin-bottom: 3px;
            }
            // &Text {
            //   // color: @text-colors[secondary];
            //   font-size: 12px;
            //   margin-bottom: 8px;
            //   display: flex;
            //   &Label {
            //     width: 100px;
            //   }
            //   &Value {
            //     flex: 1;
            //     text-align: right;
            //     .text-overflow(1);
            //   }
            // }
          }
        }

        &Operate {
          width: 100%;

          &Item {
            display: flex;
            align-items: center;
            margin-top: 16px;

            &Label {
              max-width: 160px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            &Value {
              flex: 1;
              text-align: right;
            }
          }
        }
      }
    }
  }
}

.drawerFooter {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 16px;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 45%) 12%,
    rgb(255 255 255 / 72%) 25%,
    rgb(255 255 255 / 90%) 36%
  );

  &Left {
    flex: 1;
    color: @primary-color;
  }

  &Right {
    color: @primary-color;
    display: flex;

    &Text {
      display: flex;
      margin-right: 12px;
      justify-content: center;
      align-items: center;
    }
  }
}

.batchBtn {
  color: @primary-color;
  cursor: pointer;
}

.shareTag {
  color: white;
  font-size: 10px;
  margin-right: 4px;
  padding: 2px;
  background: linear-gradient(248deg, #00c6ff 0%, #008cff 100%);
  border-radius: 4px;
}
