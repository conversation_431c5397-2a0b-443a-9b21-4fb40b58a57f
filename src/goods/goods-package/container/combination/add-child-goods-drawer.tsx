import { forwardRef, useImperativeHandle, useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON>, Spin, Badge, Divider } from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import classNames from 'classnames';
import i18n from 'i18next';
import { useTranslation } from 'react-i18next';
import { getCategoryCustomeList, getMcsPackageSpuList } from '@/apis';
import type { GetCategoryCustomeItemResult } from '@/apis';
import { Drawer, Search, Empty, Icon } from '@/components/index';
import { checkCharge, checkPermission } from '@/utils/permission';
// import { arrayFilter } from '@/utils/utils';

import SelectSpecificationsDrawer from './select-specifications-drawer';

import styles from './add-child-goods-drawer.module.less';

import { NewGetMcsPackageSpuListResult } from '../../type/combination';

interface AddChildGoodsDrawerProps {
  visible: boolean;
  title?: string;
  filterSpuIds?: number[];
  filterSkuIds?: number[];
  onConfirm: MultipleParamsFn<[values: NewGetMcsPackageSpuListResult[]]>;
  onClose: MultipleParamsFn;
}

const AddChildGoodsDrawer = forwardRef(
  (
    { visible, title, filterSpuIds, filterSkuIds, onClose, onConfirm }: AddChildGoodsDrawerProps,
    ref
  ) => {
    useImperativeHandle(ref, () => ({}));

    const [drawerVisible, setDrawerVisible] = useState(false);
    const { t } = useTranslation();

    const [categoryData, setCategoryData] = useState<GetCategoryCustomeItemResult[]>([]);
    const [isHasCategoryMore, setIsHasCategoryMore] = useState(false);
    const categoryParams = useRef({ grade: 1, pageNo: 1, pageSize: 20, totalPages: 0 });
    const [selectCategoryId, setSelectCategoryId] = useState<number | null>(null);

    const [secondaryCategoryData, setSecondaryCategoryData] = useState<
      GetCategoryCustomeItemResult[]
    >([]);

    const [selectSecondaryCategoryId, setSelectSecondaryCategoryId] = useState<number | null>(null);
    const [showSecondaryCategoryMore, setShowSecondaryCategoryMore] = useState(false);

    const [goodsData, setGoodsData] = useState<NewGetMcsPackageSpuListResult[]>([]);

    const goodsParams = useRef({
      pageNo: 1,
      pageSize: 10,
      totalPages: 0,
      customizeCategoryIdSet: [],
      customizeCategoryCodeSet: [],
      searchKey: '',
    } as { pageNo: number; pageSize: number; totalPages: number; customizeCategoryIdSet: number[]; customizeCategoryCodeSet: string[]; searchKey: string });

    const [isHasGoodsMore, setIsHasGoodsMore] = useState(false);

    const [selectSpecificationsDrawerVisible, setSelectSpecificationsDrawerVisible] =
      useState(false);

    const [currentGoods, setCurrentGoods] = useState<{
      item: NewGetMcsPackageSpuListResult;
      index: number;
    }>({
      item: {
        images: '',
        standardTotal: 0,
        name: '',
        id: 0,
        specificationGoodsList: [],
      },
      index: -1,
    });

    const [selectedSpecificationsData, setSelectedSpecificationsData] = useState<
      NewGetMcsPackageSpuListResult[]
    >([]);

    /* 分类  */
    const getCategoryData = () => {
      getCategoryCustomeList({ ...categoryParams.current }).then((res) => {
        if (categoryParams.current.pageNo === 1) {
          res.list.unshift({
            id: null,
            categoryName: t('all'),
            code: '',
          });
        }
        categoryParams.current.totalPages = Math.ceil(
          res.pagination.count / categoryParams.current.pageSize
        );

        setIsHasCategoryMore(categoryParams.current.pageNo < categoryParams.current.totalPages);
        setCategoryData([...categoryData, ...res.list]);
      });
    };

    const loadMoreCategory = () => {
      categoryParams.current.pageNo += 1;
      if (!(categoryParams.current.pageNo <= categoryParams.current.totalPages)) return;
      getCategoryData();
    };

    /* 商品 */
    const getGoodsData = useCallback(
      (
        data: NewGetMcsPackageSpuListResult[] = [],
        selectedData: NewGetMcsPackageSpuListResult[] = []
      ) => {
        getMcsPackageSpuList({
          ...goodsParams.current,
          checkSpuIdSet: filterSpuIds || [],
          // checkShopSkuIdSet: filterSkuIds || [],
        }).then((res) => {
          const goodsList: NewGetMcsPackageSpuListResult[] = [];
          res?.list?.forEach((item) => {
            if (!filterSpuIds?.includes(item.id)) {
              goodsList.push({ ...item, specificationGoodsList: [], productId: item.id });
            }
          });
          const newGoodsData: NewGetMcsPackageSpuListResult[] = data.concat(goodsList);
          newGoodsData.forEach((goodsItem) => {
            selectedData.forEach((selectedItem) => {
              if (goodsItem.id === selectedItem.id) {
                const newGoodsItem = goodsItem;
                newGoodsItem.specificationGoodsList = selectedItem.specificationGoodsList;
              }
            });
          });
          setGoodsData([...newGoodsData]);
          goodsParams.current.totalPages = res.pagination.total;
          setIsHasGoodsMore(goodsParams.current.pageNo < goodsParams.current.totalPages);
        });
      },
      [filterSpuIds]
    );

    const loadMoreGoodsData = () => {
      goodsParams.current.pageNo += 1;
      if (!(goodsParams.current.pageNo <= goodsParams.current.totalPages)) return;
      getGoodsData(goodsData, selectedSpecificationsData);
    };

    const onSearch = (val: string) => {
      goodsParams.current.searchKey = val;
      goodsParams.current.pageNo = 1;
      getGoodsData([], selectedSpecificationsData);
    };

    useEffect(() => {
      setDrawerVisible(visible);
      setSecondaryCategoryData([]);
      if (visible) {
        if (checkCharge('M_001_012_001') && checkPermission('M_001_012_001')) {
          getCategoryData();
        }
        getGoodsData();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible]);

    useEffect(() => {
      if (!drawerVisible) {
        setCategoryData([]);
        setGoodsData([]);
        setSelectCategoryId(null);
        setSecondaryCategoryData([]);
        setSelectSecondaryCategoryId(null);
        categoryParams.current = { grade: 1, pageNo: 1, pageSize: 20, totalPages: 0 };
        goodsParams.current = {
          pageNo: 1,
          pageSize: 10,
          totalPages: 0,
          customizeCategoryIdSet: [],
          customizeCategoryCodeSet: [],
          searchKey: '',
        };
        setSecondaryCategoryData([]);
        setSelectedSpecificationsData([]);
      }
    }, [drawerVisible]);

    const footerElement = (
      <div className={styles.drawerFooter}>
        <div className={styles.drawerFooterText}>
          {t('total_specifications2', { count: selectedSpecificationsData.length })}
        </div>
        <Button
          type="primary"
          disabled={!selectedSpecificationsData.length}
          onClick={() => {
            onConfirm(selectedSpecificationsData);
            setDrawerVisible(false);
            onClose();
          }}
        >
          {t('service_done')}
        </Button>
      </div>
    );

    return (
      <Drawer
        visible={drawerVisible}
        title={title || ''}
        className={styles.addChildGoodsDrawer}
        onClose={() => {
          setDrawerVisible(false);
          onClose();
        }}
        style={{ transform: 'translateX(0)' }}
        footer={footerElement}
      >
        <div className={styles.drawerHead}>
          <Search realTimeSearch className={styles.searchInput} onSearch={onSearch} />
        </div>
        <div className={styles.drawerContent}>
          <div className={styles.drawerContentLeft} id="drawerContentLeft">
            <InfiniteScroll
              dataLength={categoryData.length}
              className={styles.drawerContentLeftScroll}
              hasMore={isHasCategoryMore}
              loader={
                <div className="text-center">
                  <Spin tip={t('loading')} />
                </div>
              }
              next={loadMoreCategory}
              scrollableTarget="drawerContentLeft"
            >
              {categoryData?.map((item) => (
                <div
                  role="button"
                  tabIndex={item.id || 0}
                  key={item.id}
                  className={classNames(
                    selectCategoryId === item.id ? styles.itemActive : styles.item
                  )}
                  onClick={() => {
                    setGoodsData([]);
                    goodsParams.current.pageNo = 1;
                    goodsParams.current.customizeCategoryIdSet = item.id ? [item.id] : [];
                    goodsParams.current.customizeCategoryCodeSet = item.code ? [item.code] : [];
                    getGoodsData([], selectedSpecificationsData);
                    setShowSecondaryCategoryMore(false);
                    if (!item.id) {
                      setSelectCategoryId(null);
                      setSelectSecondaryCategoryId(null);
                      setSecondaryCategoryData([]);
                      return;
                    }
                    setSelectCategoryId(item.id);
                    getCategoryCustomeList({
                      pageNo: 1,
                      pageSize: 100,
                      parentId: item.id || null,
                    }).then((res) => {
                      setSecondaryCategoryData(res.list);
                    });
                  }}
                >
                  {item.categoryName}
                </div>
              ))}
            </InfiniteScroll>
          </div>
          <div className={styles.drawerContentRight}>
            {secondaryCategoryData.length ? (
              <div className={styles.secondaryCategory}>
                <div className={styles.secondaryCategoryContent}>
                  {secondaryCategoryData.slice(0, 2).map((item) => (
                    <span
                      role="button"
                      tabIndex={item.id || 0}
                      key={item.id}
                      title={item.categoryName}
                      className={classNames(
                        styles.secondaryCategoryItem,
                        selectSecondaryCategoryId === item.id
                          ? styles.secondaryCategoryItemActive
                          : ''
                        // showSecondaryCategoryMore ? 'mb-4' : ''
                      )}
                      onClick={() => {
                        setGoodsData([]);
                        setSelectSecondaryCategoryId(item.id);
                        goodsParams.current.pageNo = 1;
                        goodsParams.current.customizeCategoryIdSet = item.id ? [item.id] : [];
                        goodsParams.current.customizeCategoryCodeSet = item.code ? [item.code] : [];
                        getGoodsData([], selectedSpecificationsData);
                      }}
                    >
                      {item.categoryName}
                    </span>
                  ))}
                </div>
                {secondaryCategoryData.length > 2 ? (
                  <div
                    className={styles.secondaryCategoryOperate}
                    role="presentation"
                    onClick={() => {
                      setShowSecondaryCategoryMore(!showSecondaryCategoryMore);
                    }}
                  >
                    <Icon name={showSecondaryCategoryMore ? 'up' : 'down'} size={16} />
                  </div>
                ) : (
                  ''
                )}
                {showSecondaryCategoryMore ? (
                  <div className={styles.secondaryCategoryMore}>
                    <div className={styles.secondaryCategoryContent}>
                      {secondaryCategoryData.slice(2).map((item) => (
                        <span
                          role="button"
                          tabIndex={item.id || 0}
                          key={item.id}
                          title={item.categoryName}
                          className={classNames(
                            styles.secondaryCategoryItem,
                            selectSecondaryCategoryId === item.id
                              ? styles.secondaryCategoryItemActive
                              : '',
                            showSecondaryCategoryMore ? 'mt-2' : ''
                          )}
                          onClick={() => {
                            setGoodsData([]);
                            setSelectSecondaryCategoryId(item.id);
                            goodsParams.current.pageNo = 1;
                            goodsParams.current.customizeCategoryIdSet = item.id ? [item.id] : [];
                            goodsParams.current.customizeCategoryCodeSet = item.code
                              ? [item.code]
                              : [];
                            getGoodsData([], selectedSpecificationsData);
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  ''
                )}
                {/* <div className={styles.secondaryCategoryMore}>111</div> */}
              </div>
            ) : (
              ''
            )}
            <div className={styles.drawerContentRightList} id="drawerContentRightList">
              {goodsData?.length ? (
                <InfiniteScroll
                  dataLength={goodsData.length}
                  className={styles.drawerContentRightScroll}
                  hasMore={isHasGoodsMore}
                  loader={
                    <div className="text-center">
                      <Spin tip={t('paymentManage_loading')} />
                    </div>
                  }
                  endMessage={
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>{t('loaded_to_the_end')}</span>
                      </Divider>
                    </div>
                  }
                  next={loadMoreGoodsData}
                  scrollableTarget="drawerContentRightList"
                >
                  {goodsData?.map((item, index) => (
                    <div className={styles.goodsItem} key={item.id}>
                      <div className={styles.goodsItemImage}>
                        <img src={item.images} alt="" />
                      </div>
                      <div className={styles.goodsItemInfo}>
                        <div className={styles.goodsItemInfoName}>{item.name}</div>
                        <div className={styles.goodsItemInfoNum}>
                          <div className={styles.goodsItemInfoNumText}>
                            {t('total_specifications', { count: item.standardTotal })}
                          </div>
                          {/* {item.standardTotal ? ( */}
                          <div className={styles.goodsItemInfoNumBtn}>
                            {item?.specificationGoodsList?.length ? (
                              <Badge
                                count={item.specificationGoodsList.length}
                                size="small"
                                offset={[0, 0]}
                              >
                                <div
                                  className={classNames(
                                    styles.goodsItemInfoNumBtnBtn,
                                    i18n.language === 'zh' ? '' : styles.goodsItemInfoNumBtnBtnEn
                                  )}
                                  role="presentation"
                                  onClick={() => {
                                    setCurrentGoods({ item, index });
                                    setSelectSpecificationsDrawerVisible(true);
                                  }}
                                >
                                  {t('select_specifications')}
                                </div>
                              </Badge>
                            ) : (
                              <div
                                className={classNames(
                                  styles.goodsItemInfoNumBtnBtn,
                                  i18n.language === 'zh' ? '' : styles.goodsItemInfoNumBtnBtnEn
                                )}
                                role="presentation"
                                onClick={() => {
                                  setCurrentGoods({ item, index });
                                  setSelectSpecificationsDrawerVisible(true);
                                }}
                              >
                                {t('select_specifications')}
                              </div>
                            )}
                          </div>
                          {/* ) : (
                            ''
                          )} */}
                        </div>
                      </div>
                    </div>
                  ))}
                </InfiniteScroll>
              ) : (
                <Empty />
              )}
            </div>
          </div>
        </div>
        <SelectSpecificationsDrawer
          visible={selectSpecificationsDrawerVisible}
          mainItem={currentGoods.item}
          filterSkuIds={filterSkuIds}
          onClose={() => {
            setSelectSpecificationsDrawerVisible(false);
          }}
          onConfirm={(values) => {
            const newCurrentGoods = currentGoods;
            newCurrentGoods.item.specificationGoodsList = values;
            setCurrentGoods({ ...newCurrentGoods });
            goodsData[newCurrentGoods.index].specificationGoodsList = values;
            setSelectSpecificationsDrawerVisible(false);
            let isExit = false;
            selectedSpecificationsData.forEach((item, index) => {
              if (item.id === newCurrentGoods.item.id) {
                isExit = true;
                if (values.length) {
                  // spu图片，重置成选择的第一个图片
                  // if (selectedSpecificationsData[index].images !== values[0].images) {
                  //   selectedSpecificationsData[index].images = values[0].images;
                  // }
                  selectedSpecificationsData[index] = { ...newCurrentGoods.item };
                } else {
                  selectedSpecificationsData.splice(index, 1);
                }
              }
            });
            if (!isExit) {
              selectedSpecificationsData.push(newCurrentGoods.item);
            }
            setSelectedSpecificationsData([...selectedSpecificationsData]);
          }}
        />
      </Drawer>
    );
  }
);

AddChildGoodsDrawer.displayName = 'AddChildGoodsDrawer';

AddChildGoodsDrawer.defaultProps = {
  title: i18n.t('add_child_goods'),
  filterSpuIds: [],
  filterSkuIds: [],
};

export default AddChildGoodsDrawer;
