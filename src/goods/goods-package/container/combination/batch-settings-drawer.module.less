@import 'styles/mixins/mixins';

.batchSettingsDrawer {
  // width: 375px;
  :global {
    .ant-drawer-mask {
      // animation: initial !important;
      width: 375px;
      right: 0;
      left: initial;
      background-color: rgb(0 0 0 / 45%) !important;
    }

    .ant-drawer-header {
      // background-color: #fff;
    }

    .ant-drawer-body {
      display: flex;
      padding: 0 !important;
      flex-direction: column;
    }

    .ant-drawer-content-wrapper {
      width: 375px;
      right: 0;
      background-color: #f5f6fa;
    }
  }
}

.drawerHead {
  padding: 8px 20px 12px;
  background-color: #f5f6fa;
  border-bottom: 1px solid #f3f3f3;

  .warnMessage {
    width: 335px;
    border-radius: 18px;
    min-height: 102px;
    padding: 16px 12px;

    /* 背景色/FEF3DA */
    background: #fef3da;

    /* 辅助色/F9AE08 */
    border: 1px dashed #f9ae08;

    &Head {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &Text {
        font-weight: 600;
        flex: 1;
      }
    }
  }

  .goodsItem {
    display: flex;
    width: 100%;
    min-height: 80px;
    margin-top: 16px;
    padding: 0 12px;

    &Image {
      width: 80px;
      height: 80px;
      border-radius: 6px;
      border: 1px solid #f5f6fa;
    }

    &Info {
      display: flex;
      margin-left: 12px;
      position: relative;
      flex: 1;
      flex-direction: column;

      &Name {
        .text-overflow(2);

        word-break: break-all;
        margin-bottom: 8px;
      }

      &Num {
        display: flex;
        flex: 1;

        &Text {
          color: @text-colors[secondary];
          font-size: 12px;
          flex: 1;

          .selectedNum {
            color: black;
          }
        }
      }
    }
  }

  &Desc {
    color: @text-colors[secondary];
    font-size: 12px;
    height: 33px;
    padding: 8px 20px;
    background-color: #f5f6fa;
  }
}

.drawerContent {
  padding: 0 20px;

  .settingsItem {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    &Label {
      width: 78px;
    }

    &Value {
      flex: 1;
    }
  }
}

.batchBtn {
  color: @primary-color;
  cursor: pointer;
}

.curP {
  cursor: pointer;
}

.required {
  color: #ea1c26;
}

.settingsItemLabel {
  max-width: 160px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
