import { Modal, Icon } from '@/components';
import { useState, useEffect, useCallback } from 'react';
import { Image, Select, Input, message } from 'antd';
// import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import { getMcsPackageSkuByIds } from '@/apis';
import { NewGetMcsPackageSpuListResult } from '../../type/combination';
import styles from './batch-settings-drawer.module.less';

export interface BatchInfo {
  quantity: string | number;
  unit: string;
}

type BatchSettingsModalProps = {
  visible: boolean;
  mainItem: NewGetMcsPackageSpuListResult;
  checkedList: number[];
  max?: number;
  min?: number;
  onConfirm: MultipleParamsFn<[value: BatchInfo]>;
  onClose: MultipleParamsFn;
};

interface OptionItem {
  label: string;
  value: string;
}

function BatchSettingsModal({
  visible,
  mainItem,
  checkedList,
  max,
  min,
  onConfirm,
  onClose,
}: BatchSettingsModalProps) {
  const { t } = useTranslation();
  const [modalVisible, setModalVisible] = useState(false);
  const [showWarnMessage, setShowWarnMessage] = useState(false);

  const [batchInfo, setBatchInfo] = useState<BatchInfo>({
    quantity: '',
    unit: '',
  });

  const [mainGoods, setMainGoods] = useState<NewGetMcsPackageSpuListResult>({
    images: '',
    name: '',
    id: 0,
    standardTotal: 0,
    specificationGoodsList: [],
  });

  const [unitList, setUnitList] = useState<OptionItem[]>([]);

  const getMcsPackageSkuUnitList = useCallback(() => {
    if (!checkedList.length) {
      return;
    }
    getMcsPackageSkuByIds({ ids: checkedList }).then((res) => {
      const unitData: OptionItem[] = res.list?.map((item) => ({ label: item, value: item }));
      setShowWarnMessage(unitData.length > 1);
      setUnitList(unitData);
    });
  }, [checkedList]);

  const initData = () => {
    setBatchInfo({
      quantity: '',
      unit: '',
    });
    setMainGoods({
      images: '',
      name: '',
      id: 0,
      standardTotal: 0,
      specificationGoodsList: [],
    });
  };

  /* 商品 */
  useEffect(() => {
    setModalVisible(visible);
    if (visible) {
      getMcsPackageSkuUnitList();
    }
  }, [visible, getMcsPackageSkuUnitList]);

  useEffect(() => {
    if (mainItem?.id) {
      setMainGoods(mainItem);
    }
  }, [mainItem]);

  return (
    <Modal
      title={t('batch_settings')}
      visible={modalVisible}
      width={375}
      destroyOnClose
      onOk={() => {
        if (batchInfo.quantity) {
          if (!Number(batchInfo.quantity)) {
            message.error(t('quantity_cannot_be_zero'));
            return;
          }
          onConfirm(batchInfo);
          setModalVisible(false);
          initData();
          onClose();
        } else {
          message.error(t('enter_quantity'));
        }
      }}
      onCancel={() => {
        setModalVisible(false);
        initData();
        onClose();
      }}
    >
      <div className={styles.drawerHead} style={{ background: '#fff', borderBottom: 0 }}>
        {showWarnMessage ? (
          <div className={styles.warnMessage} style={{ width: '100%' }}>
            <div className={styles.warnMessageHead}>
              <Icon
                name="warn-sold"
                size={16}
                color="#F9AE08"
                className={styles.curP}
                style={{ marginRight: 5 }}
              />
              <div className={styles.warnMessageHeadText}>{t('please_note')}</div>
              <Icon
                name="close"
                size={16}
                color="#000000"
                className={styles.curP}
                onClick={() => {
                  setShowWarnMessage(false);
                }}
              />
            </div>
            {t('spec_warning')}
          </div>
        ) : (
          ''
        )}
        <div className={styles.goodsItem} style={{ padding: 0 }}>
          <div className={styles.goodsItemImage}>
            <Image src={mainGoods.images} preview={false} className={styles.goodsItemImage} />
          </div>
          <div className={styles.goodsItemInfo}>
            <div className={styles.goodsItemInfoName} title={mainGoods.name}>
              {mainGoods.name}
            </div>
            <div className={styles.goodsItemInfoNum}>
              <div className={styles.goodsItemInfoNumText}>
                <span className={styles.selectedNum}>
                  {t('selected_count2', { count: checkedList.length })}
                </span>
                <span>（{t('total_specifications', { count: mainGoods.standardTotal })}）</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.drawerContent}>
        <div className={styles.settingsItem}>
          <div className={styles.settingsItemLabel}>
            {/* <span className={styles.required}>*</span>单位 */}
            {t('unitMatrixingDrawer_unit')}
          </div>
          <div className={styles.settingsItemValue}>
            <Select
              style={{ width: '100%' }}
              onChange={(val) => {
                batchInfo.unit = val;
                setBatchInfo({ ...batchInfo });
              }}
              placeholder={t('select_unit_please')}
              value={batchInfo.unit}
            >
              {unitList.map((item) => (
                <Select.Option value={item.value} key={item.value}>
                  {item.label}
                </Select.Option>
              ))}
            </Select>
          </div>
        </div>
        <div className={styles.settingsItem}>
          <div className={styles.settingsItemLabel}>
            <span className={styles.required}>*</span>
            {t('quantity')}
          </div>
          <div className={styles.settingsItemValue}>
            <Input
              placeholder={t('enter_quantity')}
              value={batchInfo.quantity}
              onChange={(e) => {
                const inputVal = e.target.value;
                // const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,6}|[1-9][0-9]*\.\d{1,6})))$/;
                const reg = /^-?\d*(\.\d*)?$/;
                if (reg?.test(inputVal) || inputVal === '') {
                  batchInfo.quantity = inputVal;
                  setBatchInfo({ ...batchInfo });
                }
              }}
              onBlur={() => {
                let inputVal: number | string = String(batchInfo.quantity);
                if (inputVal.charAt(inputVal.length - 1) === '.') {
                  inputVal = inputVal.slice(0, -1);
                }
                inputVal = inputVal.replace(/0*(\d+)/, '$1');
                inputVal = inputVal ? Number(inputVal) : '';
                if (inputVal) {
                  if (inputVal > (max || 9999999999999)) {
                    inputVal = max || 9999999999999;
                  }
                  if (inputVal < (min || 0)) {
                    inputVal = min || 0;
                  }
                  batchInfo.quantity = inputVal;
                  setBatchInfo({ ...batchInfo });
                }
              }}
            />
          </div>
        </div>
      </div>
    </Modal>
  );
}

BatchSettingsModal.defaultProps = {
  max: 9999999999999,
  min: 0,
};

export default BatchSettingsModal;
