@import 'styles/mixins/mixins';

.addChildGoodsDrawer {
  .goodsItemInfoNumBtnBtnEn {
    border-top: 2px;
    width: 132px !important;
    position: relative;
    right: 70px;
  }

  .goodsItemInfoName {
    margin-bottom: 4px !important;
  }

  .goodsItemInfoNumText {
    margin-top: 1px;
    margin-bottom: 1px;
  }

  :global {
    .ant-drawer-mask {
      opacity: 0 !important;
      animation: initial !important;
    }

    .ant-drawer-header {
      background-color: #fff;
    }

    .ant-drawer-body {
      display: flex;
      padding: 0 !important;
      flex-direction: column;
    }
  }
}

.drawerHead {
  padding: 8px 20px 12px;
  background-color: #fff;
  border-bottom: 1px solid #f3f3f3;

  .searchInput {
    background: #f5f6fa;

    :global {
      .ant-input {
        background-color: #f5f6fa;
      }
    }
  }
}

.drawerContent {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  flex: 1;

  &Left {
    width: 82px;
    height: 100%;
    background: #f5f6fa;
    overflow: auto;

    &::-webkit-scrollbar {
      display: none !important;
    }

    &Scroll {
      width: 82px;
    }

    .item,
    .itemActive {
      width: 82px;
      padding: 20px 10px;
      word-break: break-all;
      text-align: center;
      cursor: pointer;
      .text-overflow();

      &:hover {
        background-color: #fff;
      }
    }

    .itemActive {
      background-color: #fff;
    }
  }

  &Right {
    display: flex;
    height: 100%;
    background: white;
    flex-direction: column;
    flex: 1;

    .secondaryCategory {
      display: flex;
      padding: 12px 20px 0 12px;
      justify-content: center;
      position: relative;
      background-color: #fff;

      &Content {
        flex: 1;
        // display: flex;
        // justify-content: center;
      }

      &Operate {
        color: #040919;
        display: flex;
        width: 20px;
        height: 20px;
        justify-content: center;
        border-radius: 50%;
        background: #f5f6fa;
        cursor: pointer;
        align-items: center;
      }

      &More {
        width: 100%;
        padding: 0 40px 10px 12px;
        position: absolute;
        top: 44px;
        left: 0;
        z-index: 9999;
        border-radius: 0 0 18px 18px;
        background: #fff;
        box-shadow: 0 8px 6px 0 rgb(0 0 0 / 5%);
      }

      &Item {
        display: inline-block;
        max-width: 92px;
        margin-right: 12px;
        padding: 5px 12px;
        border-radius: 10px;
        background: #f3f3f3;
        cursor: pointer;
        word-break: break-all;
        .text-overflow();

        &Active {
          color: #008cff;
          background: #d9eeff;
          border: 1px solid #008cff;
        }
      }
    }

    &List {
      flex: 1;
      overflow: auto;
      position: relative;
    }

    &Scroll {
      // flex: 1;
      .goodsItem {
        display: flex;
        width: 100%;
        min-height: 80px;
        margin-top: 16px;
        padding: 0 12px;

        &Image {
          display: flex;
          width: 80px;
          height: 80px;
          overflow: hidden;
          justify-content: center;
          border-radius: 6px;
          border: 1px solid #f5f6fa;
          align-items: center;

          &Item {
            max-width: 80px;
            max-height: 80px;
            border-radius: 6px;
          }
        }

        &Info {
          display: flex;
          margin-left: 12px;
          position: relative;
          flex: 1;
          flex-direction: column;

          &Name {
            .text-overflow(2);

            word-break: break-all;
            margin-bottom: 8px;
          }

          &Num {
            display: flex;
            flex: 1;

            &Text {
              color: @text-colors[secondary];
              font-size: 12px;
              flex: 1;
            }

            &Btn {
              display: flex;
              width: 54px;
              height: 22px;
              height: 100%;
              justify-content: flex-end;
              position: relative;
              flex-direction: column;

              &Btn {
                color: white;
                font-size: 12px;
                display: flex;
                width: 54px;
                height: 22px;
                margin-right: 8px;
                padding: 0 9px;
                justify-content: center;
                border-radius: 6px;
                background: linear-gradient(248deg, #00c6ff 0%, #008cff 100%);
                align-items: center;
                cursor: pointer;

                &:hover {
                  background: #00c6ff;
                }
              }
            }
          }
        }
      }
    }
  }
}

.drawerFooter {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 16px;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 45%) 12%,
    rgb(255 255 255 / 72%) 25%,
    rgb(255 255 255 / 90%) 36%
  );

  &Text {
    flex: 1;
    color: @primary-color;
  }
}

.endMessage {
  color: #888b98;
  font-size: 12px;
}
