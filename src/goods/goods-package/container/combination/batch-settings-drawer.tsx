import { forwardRef, useImperativeHandle, useState, useEffect, useCallback } from 'react';
import { Image, Select, Input, message } from 'antd';
// import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import { getMcsPackageSkuByIds } from '@/apis';
// import type { GetCategoryCustomeItemResult } from '@/apis';

import { Drawer, Icon } from '@/components/index';
import { NewGetMcsPackageSpuListResult } from '../../type/combination';
import styles from './batch-settings-drawer.module.less';

export interface BatchInfo {
  quantity: string | number;
  unit: string;
}

interface BatchSettingsDrawerProps {
  visible: boolean;
  mainItem: NewGetMcsPackageSpuListResult;
  checkedList: number[];
  max?: number;
  min?: number;
  onConfirm: MultipleParamsFn<[value: BatchInfo]>;
  onClose?: MultipleParamsFn;
}
interface OptionItem {
  label: string;
  value: string;
}

const BatchSettingsDrawer = forwardRef(
  (
    { visible, mainItem, checkedList, max, min, onClose, onConfirm }: BatchSettingsDrawerProps,
    ref
  ) => {
    const { t } = useTranslation();

    useImperativeHandle(ref, () => ({}));

    const [drawerVisible, setDrawerVisible] = useState(false);
    const [batchInfo, setBatchInfo] = useState<BatchInfo>({
      quantity: '',
      unit: '',
    });

    const [mainGoods, setMainGoods] = useState<NewGetMcsPackageSpuListResult>({
      images: '',
      name: '',
      id: 0,
      standardTotal: 0,
      specificationGoodsList: [],
    });

    const [unitList, setUnitList] = useState<OptionItem[]>([]);
    const [isConfirmDisable, setIsConfirmDisable] = useState(true);
    const [showWarnMessage, setShowWarnMessage] = useState(false);
    const [drawerHeight, setDrawerHeight] = useState(380);

    const getMcsPackageSkuUnitList = useCallback(() => {
      if (!checkedList.length) {
        return;
      }
      getMcsPackageSkuByIds({ ids: checkedList }).then((res) => {
        const unitData: OptionItem[] = res.list?.map((item) => ({ label: item, value: item }));
        setDrawerHeight(unitData.length > 1 ? 500 : 380);
        setShowWarnMessage(unitData.length > 1);
        setUnitList(unitData);
      });
    }, [checkedList]);

    /* 商品 */
    useEffect(() => {
      setDrawerVisible(visible);
      if (visible) {
        setBatchInfo({
          quantity: '',
          unit: '',
        });
        getMcsPackageSkuUnitList();
      }
    }, [visible, getMcsPackageSkuUnitList]);

    useEffect(() => {
      if (mainItem?.id) {
        setMainGoods(mainItem);
      }
    }, [mainItem]);

    return (
      <Drawer
        visible={drawerVisible}
        title={t('batch_settings')}
        placement="bottom"
        width={375}
        height={drawerHeight}
        closable={false}
        className={styles.batchSettingsDrawer}
        extra={
          <Icon
            name="close"
            size={20}
            className={styles.curP}
            onClick={() => {
              setDrawerVisible(false);
              onClose!();
            }}
          />
        }
        onClose={() => {
          setDrawerVisible(false);
          onClose!();
        }}
        // onConfirm={() => {
        //   onConfirm!();
        // }}
        footer={
          <Drawer.Footer
            okText={t('public_sure')}
            disabled={isConfirmDisable}
            onOk={() => {
              if (batchInfo.quantity) {
                onConfirm(batchInfo);
                setDrawerVisible(false);
                onClose!();
              } else {
                message.error(t('enter_quantity'));
              }
              // if (batchInfo.quantity && batchInfo.unit) {
              //   onConfirm(batchInfo);
              //   setDrawerVisible(false);
              //   onClose!();
              // } else {
              //   message.error('请选择单位和输入搭配数量');
              // }
            }}
            showCancel={false}
          />
        }
      >
        <div className={styles.drawerHead}>
          {showWarnMessage ? (
            <div className={styles.warnMessage}>
              <div className={styles.warnMessageHead}>
                <Icon
                  name="warn-sold"
                  size={16}
                  color="#F9AE08"
                  className={styles.curP}
                  style={{ marginRight: 5 }}
                />
                <div className={styles.warnMessageHeadText}>{t('please_note')}</div>
                <Icon
                  name="close"
                  size={16}
                  color="#000000"
                  className={styles.curP}
                  onClick={() => {
                    setShowWarnMessage(false);
                    setDrawerHeight(380);
                  }}
                />
              </div>
              {t('spec_warning')}
            </div>
          ) : (
            ''
          )}

          <div className={styles.goodsItem} style={{ padding: 0 }}>
            <div className={styles.goodsItemImage}>
              <Image src={mainGoods.images} preview={false} />
            </div>
            <div className={styles.goodsItemInfo}>
              <div className={styles.goodsItemInfoName} title={mainGoods.name}>
                {mainGoods.name}
              </div>
              <div className={styles.goodsItemInfoNum}>
                <div className={styles.goodsItemInfoNumText}>
                  <span className={styles.selectedNum}>
                    {t('selected_count2', { count: checkedList.length })}
                  </span>
                  <span>({t('total_specifications', { count: mainGoods.standardTotal })})</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className={styles.drawerContent}>
          <div className={styles.settingsItem}>
            <div className={styles.settingsItemLabel}>
              {/* <span className={styles.required}>*</span>单位 */}
              {t('paymentManage_unit')}
            </div>
            <div className={styles.settingsItemValue}>
              <Select
                style={{ width: '100%' }}
                onChange={(val) => {
                  batchInfo.unit = val;
                }}
                placeholder={t('select_unit_please')}
              >
                {unitList.map((item) => (
                  <Select.Option value={item.value} key={item.value}>
                    {item.label}
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
          <div className={styles.settingsItem}>
            <div className={styles.settingsItemLabel} title={t('quantity')}>
              <span className={styles.required}>*</span>
              {t('quantity')}
            </div>
            <div className={styles.settingsItemValue}>
              <Input
                placeholder={t('enter_quantity')}
                value={batchInfo.quantity}
                onChange={(e) => {
                  const inputVal = e.target.value;
                  // const reg = /^(([1-9][0-9]*)|(([0]\.\d{1,6}|[1-9][0-9]*\.\d{1,6})))$/;
                  const reg = /^-?\d*(\.\d*)?$/;
                  if (reg?.test(inputVal) || inputVal === '') {
                    batchInfo.quantity = inputVal;
                    setBatchInfo({ ...batchInfo });
                    if (batchInfo.quantity && Number(batchInfo.quantity)) {
                      setIsConfirmDisable(false);
                    } else {
                      setIsConfirmDisable(true);
                    }
                  }
                }}
                onBlur={() => {
                  let inputVal: number | string = String(batchInfo.quantity);
                  if (inputVal.charAt(inputVal.length - 1) === '.') {
                    inputVal = inputVal.slice(0, -1);
                  }
                  inputVal = inputVal.replace(/0*(\d+)/, '$1');
                  inputVal = inputVal ? Number(inputVal) : '';
                  if (inputVal) {
                    if (inputVal > (max || 9999999999999)) {
                      inputVal = max || 9999999999999;
                    }
                    if (inputVal < (min || 0)) {
                      inputVal = min || 0;
                    }
                    batchInfo.quantity = inputVal;
                    setBatchInfo({ ...batchInfo });
                    if (batchInfo.quantity && Number(batchInfo.quantity)) {
                      setIsConfirmDisable(false);
                    } else {
                      setIsConfirmDisable(true);
                    }
                  }
                }}
              />
            </div>
          </div>
        </div>
      </Drawer>
    );
  }
);

BatchSettingsDrawer.displayName = 'BatchSettingsDrawer';

BatchSettingsDrawer.defaultProps = {
  onClose: () => {},
  max: 9999999999999,
  min: 0,
};

export default BatchSettingsDrawer;
