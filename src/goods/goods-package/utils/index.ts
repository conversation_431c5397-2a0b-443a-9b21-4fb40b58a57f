// eslint-disable-next-line import/prefer-default-export
export function createSpecifications(standards: { name: string; value: string }[]) {
  const specifications: string[] = [];
  standards?.forEach((item) => {
    specifications.push(`${item.name}:${item.value}`);
  });
  return specifications.join(',');
}

export function limitDecimals(value: string | number | undefined) {
  // const reg = /^(\-)*(\d+)\.(\d).*$/; // 限制一位小数点
  // eslint-disable-next-line no-useless-escape
  const reg = /^(\-)*(\d+)\.(\d\d).*$/; // 限制两位位小数点
  let val: number | string = '';
  if (typeof value === 'string') {
    val = !Number.isNaN(Number(value)) ? value.replace(reg, '$1$2.$3') : '';
  } else if (typeof value === 'number') {
    val = !Number.isNaN(value) ? String(value).replace(reg, '$1$2.$3') : '';
  }

  return val;
}
