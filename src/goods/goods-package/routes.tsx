import { RouteObject } from 'react-router-dom';
import { lazy } from '@/components';

const routes: RouteObject[] = [
  {
    path: 'goods-package/base-info',
    element: lazy(() => import('./pages/base-info')),
  },
  {
    path: 'goods-package/combination',
    element: lazy(() => import('./pages/combination')),
  },
  {
    path: 'goods-package/graphic-desc',
    element: lazy(() => import('./pages/graphic-desc')),
  },
];

export default routes;
