@import 'styles/mixins/mixins';

.item {
  display: flex;
}

.itemLeft {
  overflow: hidden;
  position: relative;
}

.itemStatus,
.itemStatusNo {
  color: #fff;
  display: flex;
  width: 100%;
  height: 30%;
  padding: 6px 2px 2px;
  justify-content: center;
  position: absolute;
  top: 4%;
  left: -30%;
  transform: scale(0.8) rotateZ(-45deg);
  align-items: center;
  background: linear-gradient(119.47deg, rgb(50 209 108 / 85%) 0%, rgb(17 212 173 / 85%) 100%);
}

.itemStatusNo {
  background: linear-gradient(118.96deg, rgb(242 143 44 / 85%) 0%, rgb(250 205 57 / 85%) 100%);
}

.itemImg {
  width: 80px;
  height: 80px;
  margin-right: 12px;
  border-radius: @border-radius-xs;
}

.itemInfo {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
}

.itemName {
  width: 207px;
  margin-bottom: 6px;
  .text-overflow();
}

.itemNameTip {
  margin-top: -2px;
  margin-right: 4px;
}

.itemStandardList {
  width: 207px;
  .text-overflow();
}
