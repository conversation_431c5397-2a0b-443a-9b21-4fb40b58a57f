import { Price } from '@/components';
import { GoodsListInfo, StandardItem } from '@/apis';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import styles from './index.module.less';

interface RelateGoodsItemProps {
  info: GoodsListInfo;
}

function RelateGoodsItem({ info }: RelateGoodsItemProps) {
  const { t } = useTranslation();

  const standardListStr = (val: StandardItem[]) => {
    let str = '';
    val.forEach((item) => {
      str += `${item.name}: ${item.value}，`;
    });
    return str;
  };

  const statusStr = (status: number) => {
    if (status === 10) {
      return t('relateGoodsItem_selling');
    }
    if (status === 20) {
      return t('relateGoodsItem_pending');
    }
    return '';
  };

  return (
    <div className={styles.item}>
      <div className={styles.itemLeft}>
        <img
          className={styles.itemImg}
          src={
            info.images ||
            (info.imagesList && info.imagesList[0]) ||
            'https://img.huahuabiz.com/default/image/default_holder.png'
          }
          alt=""
        />
        <div
          className={classNames(info.mcsStatus === 10 ? styles.itemStatus : styles.itemStatusNo)}
        >
          {statusStr(info.mcsStatus)}
        </div>
      </div>
      <div className={styles.itemInfo}>
        <div>
          <div className={styles.itemName}>
            {info.link && info.link.length && (
              <img
                className={styles.itemNameTip}
                src="https://img.huahuabiz.com/user_files/20221217/1671244508981586.png"
                alt=""
              />
            )}
            <span>{info.name}</span>
          </div>
          <div className={styles.itemStandardList} title={standardListStr(info.standardList)}>
            {info.standardList.map((item) => (
              <span key={item.name}>
                {item.name}:{item.value}
              </span>
            ))}
          </div>
        </div>
        <Price value={info.marketPrice} />
      </div>
    </div>
  );
}

export default RelateGoodsItem;
