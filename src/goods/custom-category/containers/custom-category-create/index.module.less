@import 'styles/mixins/mixins';

.card,
.cardForm {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);

  :global {
    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-input {
      font-size: @font-size-lg;
    }

    .ant-form-item-label {
      label {
        padding: 0 12px;
      }

      .ant-form-item-required {
        padding: 0;
      }
    }
  }
}

.cardForm {
  padding: 16px 8px;
}

.line {
  margin: 8px 12px 16px;
  border-bottom: 1px solid #e5e5e5;
}

.title {
  margin-bottom: 16px;
}

.upload {
  display: flex;
  height: 140px;
  justify-content: center;
  align-items: center;
  border: 1px dashed #b1b3be;
  border-radius: 10px;
  cursor: pointer;
}
