import { useEffect, useRef, useState } from 'react';
import { Drawer, SimpleUpload } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import { Form, Input, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { customCategoryAdd } from '@/apis';
import classNames from 'classnames';
import styles from './index.module.less';

interface CustomCategoryCreatePops {
  visible: boolean;
  grade: number;
  parentId: number;
  onClose: () => void;
  onConfirm: () => void;
}

function CustomCategoryCreate({
  visible,
  grade,
  parentId,
  onClose,
  onConfirm,
  ...props
}: CustomCategoryCreatePops) {
  const { t } = useTranslation();
  const drawerRef = useRef<DrawerRefType>(null);
  const [form] = Form.useForm();
  const [imageUrl, setImageUrl] = useState('');
  const [isValueChange, setIsValueChange] = useState(true);

  const onValuesChange = () => {
    const isRequired = !form.getFieldsValue().categoryName;
    if (isRequired) {
      drawerRef.current?.setIsChange(false);
      setIsValueChange(true);
      return;
    }
    drawerRef.current?.setIsChange(true);
    setIsValueChange(false);
  };

  const onSubmit = () => {
    const { categoryName } = form.getFieldsValue();
    customCategoryAdd({
      categoryName,
      image: imageUrl,
      grade,
      parentId: parentId || '',
    })
      .then(() => {
        message.success(t('customCategoryCreate_addSuccess'));
        onConfirm();
      })
      .finally(() => {});
  };

  useEffect(() => {
    if (visible) {
      setIsValueChange(true);
    } else {
      form.resetFields();
      setImageUrl('');
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      title={t('customCategoryCreate_newCategory', {
        grade: grade === 1 ? t('custom_cateOne') : t('custom_catetTwo'),
      })}
      visible={visible}
      {...props}
      onClose={onClose}
      ref={drawerRef}
      footer={
        <Drawer.Footer
          okText={t('public_confirm')}
          showCancel={false}
          onOk={onSubmit}
          disabled={isValueChange}
        />
      }
    >
      <div className={classNames(styles.card, styles.cardForm)}>
        <Form layout="vertical" form={form} className={styles.form} onValuesChange={onValuesChange}>
          <Form.Item
            label={t('customCategoryCreate_categoryName')}
            required
            name="categoryName"
            extra={<div className={styles.line} />}
          >
            <Input
              className={styles.input}
              maxLength={20}
              bordered={false}
              placeholder={t('customCategoryCreate_enterName')}
            />
          </Form.Item>
        </Form>
      </div>
      <div className={styles.card}>
        <div className={styles.title}>{t('customCategoryCreate_addImage')}</div>
        <SimpleUpload
          className={styles.upload}
          accept={['.gif', '.pjp', '.jpg', '.png', '.jpeg', '.pjpeg', '.PNG'].join(',')}
          beforeUpload={(file) => {
            const fileSuffixes = ['.gif', '.pjp', '.jpg', '.png', '.jpeg', '.pjpeg', '.PNG'];
            const suffixesStatus = fileSuffixes.includes(
              file.name.substring(file.name.lastIndexOf('.'))
            );

            if (!suffixesStatus) {
              message.error(
                t('customCategoryCreate_invalidFileType', { types: fileSuffixes.join(',') })
              );
              return false;
            }

            return true;
          }}
          onChange={(e) => setImageUrl(e.file.url)}
        >
          {imageUrl ? (
            <img src={imageUrl} alt="" />
          ) : (
            <img
              src="https://img.huahuabiz.com/user_files/20221216/1671184511040177.png"
              alt={t('customCategoryCreate_addImage')}
            />
          )}
        </SimpleUpload>
      </div>
    </Drawer>
  );
}

export default CustomCategoryCreate;
