import { useEffect, useRef, useState } from 'react';
import { useInfiniteScroll } from 'ahooks';
import { Drawer, Empty, Modal } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import { useTranslation } from 'react-i18next';
import { Button, Checkbox, message, Spin } from 'antd';
import { goodsRelationRemove, GoodsListInfo, relationGoodsList } from '@/apis';
import { usePermission } from '@/hooks';
import RelateGoodsItem from '../../components/relate-goods-item';
import RelateGoodsSelete from '../relate-goods-select';
import styles from './index.module.less';

interface CustomCategoryCreatePops {
  visible: boolean;
  onClose: () => void;
  onRefresh: () => void;
  code: string;
  count: number;
}

interface Result {
  list: GoodsListInfo[];
  isNoMore: boolean;
}

function RelateGoods({
  visible,
  onClose,
  onRefresh,
  code,
  count,
  ...props
}: CustomCategoryCreatePops) {
  const { t } = useTranslation();
  const drawerRef = useRef<DrawerRefType>(null);
  const scroll = useRef<HTMLDivElement>(null);
  const params = useRef({
    pageNo: 1,
    pageSize: 10,
    customizeCategoryCodeSet: [],
  });
  const [ids, setIds] = useState<number[]>([]);
  const [isCheckedAll, setIsCheckedAll] = useState(false);
  const [showRelateGoodsSelete, setShowRelateGoodsSelete] = useState(false);

  const getLoadMoreList = (): Promise<Result> =>
    new Promise((resolve) => {
      // @ts-ignore
      relationGoodsList({ ...params.current, customizeCategoryCodeSet: [code] }).then((res) => {
        params.current.pageNo += 1;
        setIsCheckedAll(false);
        resolve({
          list: res.list,
          // @ts-ignore
          isNoMore: params.current.pageNo > res.pagination.total,
        });
      });
    });

  const { data, loading, reload, mutate } = useInfiniteScroll(getLoadMoreList, {
    target: scroll,
    isNoMore: (val) => Boolean(val?.isNoMore),
    manual: true,
  });

  const onSelectGoods = (id: number) => {
    data?.list.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.isChecked = !items.isChecked;
      }
    });
    setIsCheckedAll(data?.list.every((item) => item.isChecked) || false);
    const arr = data?.list.filter((item) => item.isChecked).map((item) => item.id) || [];
    setIds([...arr]);
  };

  const onChangeAll = (e: boolean) => {
    setIsCheckedAll(e);
    data?.list.forEach((item) => {
      const items = item;
      items.isChecked = e;
    });
    if (e) {
      const arr = data?.list.map((item) => item.id) || [];
      setIds([...arr]);
    } else {
      setIds([]);
    }
  };

  const onDelete = () => {
    Modal.confirm({
      title: t('public_tips'),
      content: t('relateGoods_confirmDelete'),
      okText: t('public_confirm'),
      cancelText: t('public_cancel'),
      centered: true,
      onOk: () => {
        goodsRelationRemove({
          customizeCategoryCode: code,
          shopSkuIds: ids,
        }).then(() => {
          message.success(t('relateGoods_deleteSuccess'));
          params.current.pageNo = 1;
          mutate({ list: [], isNoMore: false });
          reload();
          setIds([]);
          onRefresh();
          setIsCheckedAll(false);
        });
      },
    });
  };

  const onRelateGoods = usePermission('M_001_012_007', () => {
    setShowRelateGoodsSelete(true);
  });

  const initData = () => {
    params.current.pageNo = 1;
    mutate({ list: [], isNoMore: false });
    reload();
  };

  useEffect(() => {
    if (visible) {
      initData();
      if (count === 0) {
        setShowRelateGoodsSelete(true);
      }
    }
  }, [visible, count]); // eslint-disable-line

  const footer = (
    <div className={styles.footer}>
      <Checkbox
        checked={isCheckedAll}
        disabled={!data?.list?.length}
        onChange={(e) => onChangeAll(e.target.checked)}
      >
        {t('public_selectAll')}
      </Checkbox>
      <Button disabled={!ids.length} onClick={onDelete}>
        {t('public_delete')}
      </Button>
    </div>
  );

  return (
    <Drawer
      title={t('relateGoods_relatedGoods')}
      visible={visible}
      {...props}
      onClose={onClose}
      ref={drawerRef}
      footer={footer}
      push={false}
      extra={
        <div role="button" tabIndex={0} className={styles.titleAdd} onClick={onRelateGoods}>
          {t('relateGoods_addRelated')}
        </div>
      }
    >
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <div ref={scroll} className={styles.scroll}>
          {data?.list.length ? (
            data?.list.map((item) => (
              <div className={styles.item} key={item.id}>
                <Checkbox checked={ids.includes(item.id)} onChange={() => onSelectGoods(item.id)} />
                <RelateGoodsItem info={item} />
              </div>
            ))
          ) : (
            <div className={styles.noData}>
              <Empty />
            </div>
          )}
        </div>
      </Spin>
      <RelateGoodsSelete
        visible={showRelateGoodsSelete}
        code={code}
        onClose={() => {
          if (count === 0 && data?.list.length === 0) {
            onClose();
          }
          setShowRelateGoodsSelete(false);
        }}
        onConfirm={() => {
          setShowRelateGoodsSelete(false);
          initData();
          setIsCheckedAll(false);
          onRefresh();
        }}
      />
    </Drawer>
  );
}

export default RelateGoods;
