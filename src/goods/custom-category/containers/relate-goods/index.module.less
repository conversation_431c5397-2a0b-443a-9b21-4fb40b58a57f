@import 'styles/mixins/mixins';

.scroll {
  height: 100%;
  overflow: auto;
}

.spin {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.titleAdd {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.item {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  :global {
    .ant-checkbox-wrapper {
      margin-right: 20px;
    }
  }
}

.footer {
  display: flex;
  height: 86px;
  padding: 24px 20px;
  justify-content: space-between;
  align-items: center;

  :global {
    .ant-btn {
      width: 160px;
    }
  }
}
