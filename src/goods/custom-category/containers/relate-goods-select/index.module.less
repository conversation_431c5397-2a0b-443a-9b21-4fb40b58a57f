@import 'styles/mixins/mixins';

.content {
  display: flex;
  height: calc(100% - 52px);
  justify-content: space-between;
  flex-direction: column;
}

.search {
  margin-top: 8px;
  margin-bottom: 12px;
}

.spin {
  height: 100%;

  :global {
    .ant-spin-container {
      height: 100%;
    }
  }
}

.scroll {
  height: 100%;
  overflow: auto;
}

.item {
  display: flex;
  align-items: center;
  margin-bottom: 24px;

  :global {
    .ant-checkbox-wrapper {
      margin-right: 20px;
    }
  }
}

.noData {
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.footer {
  display: flex;
  height: 86px;
  padding: 24px 20px;
  justify-content: space-between;
  align-items: center;

  :global {
    .ant-btn {
      width: 160px;
    }
  }
}
