import { useEffect, useRef, useState } from 'react';
import { Drawer, Empty, Search } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import { Button, Checkbox, message, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { GoodsListInfo, goodsRelationAdd, skuGoodsList } from '@/apis';
import { useInfiniteScroll } from 'ahooks';
import RelateGoodsItem from '../../components/relate-goods-item';
import styles from './index.module.less';

interface CustomCategoryCreatePops {
  visible: boolean;
  code: string;
  onClose: () => void;
  onConfirm: () => void;
}

interface Result {
  list: GoodsListInfo[];
  isNoMore: boolean;
}

function RelateGoodsSelete({
  visible,
  code,
  onClose,
  onConfirm,
  ...props
}: CustomCategoryCreatePops) {
  const { t } = useTranslation();
  const drawerRef = useRef<DrawerRefType>(null);
  const scroll = useRef<HTMLDivElement>(null);
  const params = useRef({
    pageNo: 1,
    pageSize: 10,
    type: 5,
    soureType: 2,
    sort: 'desc',
    keyWord: '',
    customizeCategoryCode: [],
  });
  const [ids, setIds] = useState<number[]>([]);
  const [isCheckedAll, setIsCheckedAll] = useState(false);

  const getLoadMoreList = (): Promise<Result> =>
    new Promise((resolve) => {
      // @ts-ignore
      skuGoodsList({ ...params.current }).then((res) => {
        params.current.pageNo += 1;
        setIsCheckedAll(false);
        resolve({
          list: res.list,
          // @ts-ignore
          isNoMore: params.current.pageNo > res.pagination.total,
        });
      });
    });

  const { data, loading, reload, mutate } = useInfiniteScroll(getLoadMoreList, {
    target: scroll,
    isNoMore: (val) => Boolean(val?.isNoMore),
    manual: true,
  });

  const initData = () => {
    params.current.pageNo = 1;
    mutate({ list: [], isNoMore: false });
    reload();
  };

  const onSelectGoods = (id: number) => {
    data?.list.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.isChecked = !items.isChecked;
      }
    });
    setIsCheckedAll(data?.list.every((item) => item.isChecked) || false);
    const arr = data?.list.filter((item) => item.isChecked).map((item) => item.id) || [];
    setIds([...arr]);
  };

  const onChangeAll = (e: boolean) => {
    data?.list.forEach((item) => {
      const items = item;
      items.isChecked = e;
    });
    if (e) {
      const arr = data?.list.map((item) => item.id) || [];
      setIds([...arr]);
    } else {
      setIds([]);
    }
    setIsCheckedAll(e);
  };

  const onSubmit = () => {
    goodsRelationAdd({
      customizeCategoryCode: code,
      shopSkuIds: ids,
    }).then(() => {
      message.success(t('customCategoryCreate_addSuccess'));
      onConfirm();
    });
  };

  useEffect(() => {
    if (visible) {
      // @ts-ignore
      params.current.customizeCategoryCode = [code];
      params.current.pageNo = 1;
      setIsCheckedAll(false);
      reload();
    }
  }, [reload, visible, code]);
  const footer = (
    <div className={styles.footer}>
      <Checkbox
        checked={isCheckedAll}
        disabled={!data?.list?.length}
        onChange={(e) => onChangeAll(e.target.checked)}
      >
        {t('public_selectAll')}
      </Checkbox>
      <Button type="primary" disabled={!ids.length || !data?.list?.length} onClick={onSubmit}>
        {t('public_confirm')}
      </Button>
    </div>
  );

  return (
    <Drawer
      title={t('relateGoods_selectRelatedGoods')}
      visible={visible}
      {...props}
      onClose={onClose}
      ref={drawerRef}
      footer={footer}
    >
      <div className={styles.content}>
        <Search
          placeholder={t('relateGoods_enterKeyword')}
          className={styles.search}
          onSearch={(e) => {
            params.current.keyWord = e;
            initData();
          }}
        />
        <Spin spinning={loading} wrapperClassName={styles.spin}>
          <div ref={scroll} className={styles.scroll}>
            {data?.list.length ? (
              data?.list.map((item) => (
                <div className={styles.item} key={item.id}>
                  <Checkbox
                    checked={ids.includes(item.id)}
                    onChange={() => onSelectGoods(item.id)}
                  />
                  <RelateGoodsItem info={item} />
                </div>
              ))
            ) : (
              <div className={styles.noData}>
                <Empty />
              </div>
            )}
          </div>
        </Spin>
      </div>
    </Drawer>
  );
}

export default RelateGoodsSelete;
