@import 'styles/mixins/mixins';

.center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn {
  display: flex;
  align-items: center;
}

.content {
  height: 100%;

  :global {
    .ant-spin-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      padding: 0 20px;
    }
  }
}

.title {
  display: flex;
  align-items: center;
  height: 38px;
  margin: 20px 0 12px;
  padding: 0 20px;
  border-radius: 8px;
  background: #fafafc;
}

.name {
  flex: 1;
  text-align: left;
  position: relative;

  &::after {
    content: '*';
    color: #ea1c26;
    position: absolute;
    top: -2px;
    left: -7px;
  }
}

.img {
  .center();

  width: 20%;
}

.goods {
  .center();

  width: 25%;
}

.sort {
  width: 10%;
  text-align: center;
}

.handle {
  width: 10%;
  text-align: right;
}

.list {
  flex: 1;
  overflow: auto;
}

.item {
  display: flex;
  align-items: center;
  padding: 12px 20px;

  &:hover {
    background: rgb(217 238 255 / 30%);
    border-radius: 6px;
  }
}

.itemSelect {
  color: #008cff;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  align-items: center;
  border-radius: 4px;
  border: 1px solid #b1b3be;
  cursor: pointer;
}

.itemSelectNo {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

.itemName {
  position: relative;
  flex: 1;
}

.itemNameIcon {
  color: #999eb2;
  font-size: 18px;
  position: absolute;
  top: 4px;
  left: 73px;
}

.itemTwo {
  .item();
}

.itemNameTwo {
  padding-left: 56px;
  position: relative;

  &::after {
    content: '';
    width: 1px;
    height: 72px;
    position: absolute;
    top: -55px;
    left: 32px;
    background-color: #b1b3be;
  }

  &::before {
    content: '';
    width: 24px;
    height: 1px;
    position: absolute;
    top: 16px;
    left: 32px;
    background-color: #b1b3be;
  }
}

.listTwo .itemTwo:first-child .itemNameTwo::after {
  height: 56px;
  top: -40px;
}

.itemAdd {
  height: 72px;

  & .itemNameTwo {
    color: #008cff;
    padding-left: 60px;
    cursor: pointer;
  }

  & .itemNameTwo::after {
    height: 72px;
    top: -64px;
  }

  & .itemNameTwo::before {
    top: 8px;
  }
}

.itemNameAdd {
  &::after {
    top: -48px !important;
  }
}

.imgUpload {
  width: 48px;
  height: 48px;
  position: relative;
  border-radius: 6px;
  border: 1px dashed #b1b3be;
  cursor: pointer;
  .center();

  & .img {
    width: 48px;
    height: 48px;
    border-radius: 6px;
  }

  &:hover {
    .itemClose {
      display: block !important;
    }
  }
}

.itemClose {
  display: none;
  width: 14px;
  height: 14px;
  position: absolute;
  top: 3px;
  right: 3px;
  z-index: 999;
}

.itemGoods {
  .center();

  color: #008cff;
  width: 25%;
  cursor: pointer;
}

.itemGoodsDefault {
  color: #c6ccd8;
}

.itemSort {
  .center();

  width: 10%;
}

.itemSortHandle {
  cursor: pointer;
}

.itemSortGrey {
  color: #999eb2;
  cursor: not-allowed;
}

.itemDelte {
  color: #008cff;
  width: 10%;
  text-align: right;
  cursor: pointer;
}

.noDataContent {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
}
