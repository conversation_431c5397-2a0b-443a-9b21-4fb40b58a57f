import { useEffect, useState } from 'react';
import { Context, Icon, Modal, SimpleUpload, Empty } from '@/components';
import { Button, Input, message, Spin } from 'antd';
import { useRequest } from 'ahooks';
import {
  customCategoryDelete,
  customCategoryEdit,
  customCategorySort,
  customCategoryListCount,
  CustomCategoryListCountResult,
} from '@/apis';
import classNames from 'classnames';
import { usePermission } from '@/hooks';
import { useTranslation } from 'react-i18next';
import CustomCategoryCreate from '../containers/custom-category-create';
import RelateGoods from '../containers/relate-goods';
import styles from './index.module.less';

const fileSuffixes = ['.gif', '.pjp', '.jpg', '.png', '.jpeg', '.pjpeg', '.PNG'];

function CustomCategory() {
  const { t } = useTranslation();
  const [list, setList] = useState<CustomCategoryListCountResult[]>([]);
  const [showCeate, setShowCeate] = useState(false);
  const [showReleteGoods, setShowReleteGoods] = useState(false);
  const [grade, setGrade] = useState(0);
  const [parentId, setParentId] = useState(0);
  const [code, setCode] = useState('');
  const [count, setCount] = useState(0);

  const { run, loading } = useRequest(customCategoryListCount, {
    manual: true,
    onSuccess: (result) => {
      result.list.forEach((item) => {
        const items = item;
        items.isOpen = true;
        list.forEach((item1) => {
          if (items.id === item1.id) {
            items.isOpen = item1.isOpen;
          }
        });
      });
      setList([...result.list]);
    },
  });

  const onOpen = (id: number) => {
    list.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.isOpen = !items.isOpen;
      }
    });
    setList([...list]);
  };

  const onEdit = (
    id: number,
    categoryName: string,
    gradeVal: number,
    image: string,
    sort: number
  ) => {
    customCategoryEdit({
      id,
      categoryName,
      grade: gradeVal,
      image,
      sort: sort || 1,
    })
      .then(() => {
        message.success(t('set_deptManage_mem_editSuccessTip'));
        run();
      })
      .finally(() => {});
  };

  const onUploadImg = (
    categoryInfo: CustomCategoryListCountResult,
    url: string,
    gradeVal: number
  ) => {
    onEdit(categoryInfo.id, categoryInfo.name, gradeVal, url, categoryInfo.sort);
  };

  const onSort = (idVal: number, type: string) => {
    let sortId = 0;
    list.forEach((item, idx) => {
      if (item.id === idVal) {
        if (type === 'down') {
          sortId = list[idx + 1].id;
        } else if (type === 'up') {
          sortId = list[idx - 1].id;
        }
      }
      if (item.childrenList.length) {
        item.childrenList.forEach((each, idx1) => {
          if (each.id === idVal) {
            if (type === 'down') {
              sortId = list[idx].childrenList[idx1 + 1].id;
            } else if (type === 'up') {
              sortId = list[idx].childrenList[idx1 - 1].id;
            }
          }
        });
      }
    });
    const paramsVal = [
      {
        id: idVal,
      },
      {
        id: sortId,
      },
    ];
    customCategorySort([...paramsVal]).then(() => {
      message.success(t('set_deptManage_mem_editSuccessTip'));
      run();
    });
  };

  const onSortClick = usePermission('M_001_012_006', (idVal: number, type: string) => {
    onSort(idVal, type);
  });

  const onAdd = usePermission('M_001_012_002', (gradeVal: number, parentIdVal: number) => {
    setShowCeate(true);
    setGrade(gradeVal);
    setParentId(parentIdVal);
  });

  const onAddTwo = usePermission('M_001_012_003', (gradeVal: number, parentIdVal: number) => {
    setShowCeate(true);
    setGrade(gradeVal);
    setParentId(parentIdVal);
  });

  const onDelete = (id: number, gradeVal: number) => {
    Modal.confirm({
      title: t('common_prompt'),
      content: t('customCategory_confirmDelete'),
      okText: t('public_confirm'),
      cancelText: t('public_cancel'),
      centered: true,
      onOk: () => {
        customCategoryDelete({
          id,
          grade: gradeVal,
        }).then(() => {
          message.success(t('customCategory_deleteSuccess'));
          run();
        });
      },
    });
  };

  const onDeleteClick = usePermission('M_001_012_005', (id: number, gradeVal: number) => {
    onDelete(id, gradeVal);
  });

  const onRelateGoods = usePermission('M_001_012_007', (item: CustomCategoryListCountResult) => {
    setShowReleteGoods(true);
    setCode(item.code);
    setCount(Number(item.shopSkuCount));
  });

  useEffect(() => {
    run();
  }, [run]);

  const head = (
    <Context.Head
      title={[
        {
          to: '/goods/manage/index',
          title: t('goods_management'),
        },
        t('customCategory_customCategory'),
      ]}
      extra={
        <Context.HeadTool>
          <Button className={styles.btn} type="primary" size="small" onClick={() => onAdd(1, 0)}>
            <Icon name="plus" size={12} />
            {t('customCategory_createLevelOne')}
          </Button>
        </Context.HeadTool>
      }
    />
  );

  return (
    <Context head={head} permission={{ code: 'M_001_012_001' }}>
      <Spin wrapperClassName={styles.content} spinning={loading}>
        <div className={styles.title}>
          <span className={styles.name}>{t('classify_name')}</span>
          <span className={styles.img}>{t('customCategory_image')}</span>
          <span className={styles.goods}>{t('customCategory_relatedGoods')}</span>
          <span className={styles.sort}>{t('customCategory_sort')}</span>
          <span className={styles.handle}>{t('customCategory_operation')}</span>
        </div>
        <div className={styles.list}>
          {list.length ? (
            list.map((item, index) => (
              <>
                <div className={styles.item}>
                  {item.childrenList.length > 0 ? (
                    <div
                      role="button"
                      tabIndex={0}
                      className={styles.itemSelect}
                      onClick={() => onOpen(item.id)}
                    >
                      <Icon name={!item.isOpen ? 'plus' : 'minus'} />
                    </div>
                  ) : (
                    <div className={styles.itemSelectNo} />
                  )}

                  <div className={styles.itemName}>
                    <Input
                      onBlur={(e) => {
                        if (e.target.value) {
                          onEdit(item.id, e.target.value, 1, item.image, item.sort);
                        } else {
                          message.error(t('customCategoryEdit_nameEmptyError'));
                        }
                      }}
                      value={item.name}
                      status={item.name ? '' : 'error'}
                      placeholder={t('customCategoryEdit_levelOnePlaceholder')}
                      // disabled={item.name === '默认分类'}
                      onChange={(e) => {
                        list[index].name = e.target.value;
                        setList([...list]);
                      }}
                      maxLength={20}
                    />
                    {/* {item.name === '默认分类' && ( */}
                    {/*  <Tooltip placement="bottom" title="未进行分类的商品将自动挂在默认分类下"> */}
                    {/*    <Icon className={styles.itemNameIcon} name="info" /> */}
                    {/*  </Tooltip> */}
                    {/* )} */}
                  </div>
                  <div className={styles.img}>
                    <SimpleUpload
                      maxCount={1}
                      accept={fileSuffixes.join(',')}
                      beforeUpload={(file) => {
                        const suffixesStatus = fileSuffixes.includes(
                          file.name.substring(file.name.lastIndexOf('.'))
                        );

                        if (!suffixesStatus) {
                          message.error(
                            t('customCategoryEdit_invalidFileType', {
                              types: fileSuffixes.join(','),
                            })
                          );
                          return false;
                        }

                        return true;
                      }}
                      onChange={(e) => onUploadImg(item, e.file.url, 1)}
                    >
                      <div className={styles.imgUpload}>
                        {item.image ? (
                          <img className={styles.img} src={item.image} alt="" />
                        ) : (
                          <Icon name="plus" />
                        )}
                        {item.image && (
                          // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
                          <img
                            src="https://img.huahuabiz.com/user_files/202313/167272982779244.png"
                            className={styles.itemClose}
                            onClick={(e) => {
                              e.stopPropagation();
                              onEdit(item.id, item.name, 1, '', item.sort);
                            }}
                            alt=""
                          />
                        )}
                      </div>
                    </SimpleUpload>
                  </div>
                  <div
                    role="button"
                    tabIndex={0}
                    className={classNames(
                      styles.itemGoods
                      //   {
                      //   [styles.itemGoodsDefault]: item.name === '默认分类',
                      // }
                    )}
                    onClick={() => {
                      // if (item.name === '默认分类') {
                      //   return;
                      // }
                      onRelateGoods(item);
                    }}
                  >
                    <Icon name="plus" />
                    <span>
                      {t('relateGoods_selectRelatedGoods')}（{item.shopSkuCount}）
                    </span>
                  </div>
                  <div className={styles.itemSort}>
                    <Icon
                      className={classNames(styles.itemSortHandle, {
                        [styles.itemSortGrey]: index === 0,
                      })}
                      size={24}
                      name="up"
                      onClick={() => onSortClick(item.id, 'up')}
                    />
                    <Icon
                      className={classNames(styles.itemSortHandle, {
                        [styles.itemSortGrey]: index === list.length - 1,
                      })}
                      size={24}
                      name="down"
                      onClick={() => onSortClick(item.id, 'down')}
                    />
                  </div>
                  <div
                    role="button"
                    tabIndex={0}
                    className={styles.itemDelte}
                    onClick={() => onDeleteClick(item.id, item.grade)}
                  >
                    {t('public_delete')}
                  </div>
                </div>
                {item.isOpen && (
                  <div className={styles.listTwo}>
                    {item.childrenList.map((each, idx) => (
                      <div className={styles.itemTwo}>
                        <div className={classNames(styles.itemName, styles.itemNameTwo)}>
                          <Input
                            onBlur={(e) => {
                              if (e.target.value) {
                                onEdit(each.id, e.target.value, 2, each.image, each.sort);
                              } else {
                                message.error(t('customCategoryEdit_nameEmptyError'));
                              }
                            }}
                            status={each.name ? '' : 'error'}
                            value={each.name}
                            placeholder={t('customCategoryEdit_levelTwoPlaceholder')}
                            maxLength={20}
                            onChange={(e) => {
                              list[index].childrenList[idx].name = e.target.value;
                              setList([...list]);
                            }}
                          />
                        </div>
                        <div className={styles.img}>
                          <SimpleUpload
                            maxCount={1}
                            beforeUpload={(file) => {
                              const suffixesStatus = fileSuffixes.includes(
                                file.name.substring(file.name.lastIndexOf('.'))
                              );
                              if (!suffixesStatus) {
                                message.error(
                                  t('customCategoryEdit_invalidFileType', {
                                    types: fileSuffixes.join(','),
                                  })
                                );
                                return false;
                              }
                              return true;
                            }}
                            onChange={(e) => onUploadImg(each, e.file.url, 2)}
                          >
                            <div className={styles.imgUpload}>
                              {each.image ? (
                                <img className={styles.img} src={each.image} alt="" />
                              ) : (
                                <Icon name="plus" />
                              )}
                              {each.image && (
                                // eslint-disable-next-line jsx-a11y/no-noninteractive-element-interactions
                                <img
                                  src="https://img.huahuabiz.com/user_files/202313/167272982779244.png"
                                  className={styles.itemClose}
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onEdit(each.id, each.name, 2, '', each.sort);
                                  }}
                                  alt=""
                                />
                              )}
                            </div>
                          </SimpleUpload>
                        </div>
                        <div
                          role="button"
                          tabIndex={0}
                          className={styles.itemGoods}
                          onClick={() => {
                            onRelateGoods(each);
                          }}
                        >
                          <Icon name="plus" />
                          <span>
                            {t('relateGoods_selectRelatedGoods')}（{each.shopSkuCount}）
                          </span>
                        </div>
                        <div className={styles.itemSort}>
                          <Icon
                            className={classNames(styles.itemSortHandle, {
                              [styles.itemSortGrey]: idx === 0,
                            })}
                            size={24}
                            name="up"
                            onClick={() => onSortClick(each.id, 'up')}
                          />
                          <Icon
                            className={classNames(styles.itemSortHandle, {
                              [styles.itemSortGrey]: idx === item.childrenList.length - 1,
                            })}
                            size={24}
                            name="down"
                            onClick={() => onSortClick(each.id, 'down')}
                          />
                        </div>
                        <div
                          role="button"
                          tabIndex={0}
                          className={styles.itemDelte}
                          onClick={() => onDeleteClick(each.id, each.grade)}
                        >
                          {t('public_delete')}
                        </div>
                      </div>
                    ))}
                    <div className={classNames(styles.itemTwo, styles.itemAdd)}>
                      <div
                        role="button"
                        tabIndex={0}
                        className={classNames(styles.itemNameTwo, {
                          [styles.itemNameAdd]: !item.childrenList?.length,
                        })}
                        onClick={() => onAddTwo(2, item.id)}
                      >
                        <Icon name="plus" />
                        {t('customCategoryEdit_addLevelTwo')}
                      </div>
                    </div>
                  </div>
                )}
              </>
            ))
          ) : (
            <div className={styles.noDataContent}>
              <Empty />
            </div>
          )}
        </div>
      </Spin>
      <CustomCategoryCreate
        visible={showCeate}
        grade={grade}
        parentId={parentId}
        onClose={() => setShowCeate(false)}
        onConfirm={() => {
          run();
          setShowCeate(false);
        }}
      />
      <RelateGoods
        visible={showReleteGoods}
        onClose={() => setShowReleteGoods(false)}
        count={count}
        onRefresh={() => {
          run();
        }}
        code={code}
      />
    </Context>
  );
}

export default CustomCategory;
