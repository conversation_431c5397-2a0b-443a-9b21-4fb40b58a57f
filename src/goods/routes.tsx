import { RouteObject } from 'react-router-dom';
import goodsPackageRoutes from './goods-package/routes';
import serviceProduct from './service-product/routes';
import distributionManage from './distribution-manage/routes';
import customCategory from './custom-category/routes';
import createProduct from './create-product/routes';
import goodsManageRoutes from './goods-manage/routes';
import settingsProductShow from './settings-product-show/routes';
import goodsTag from './goods-tag/routes';

const routes: RouteObject[] = [
  ...goodsPackageRoutes,
  ...serviceProduct,
  ...distributionManage,
  ...customCategory,
  ...createProduct,
  ...goodsManageRoutes,
  ...settingsProductShow,
  ...goodsTag,
];

export default routes;
