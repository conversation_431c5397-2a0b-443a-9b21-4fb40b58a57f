@import 'styles/mixins/mixins';

.flexBox {
  color: #888b98;
  font-size: 12px;
  display: flex;
  margin-bottom: 8px;
  align-items: center;
}

.card {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);
}

.title {
  margin-bottom: 12px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;
}

.tipNotice {
  color: #888b98;
}

.enterText {
  color: #b1b3be;
  font-size: 12px;
  text-align: right;
}
