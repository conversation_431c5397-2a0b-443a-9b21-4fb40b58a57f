import { useEffect, useRef, useState } from 'react';
import { Form, Input, message } from 'antd';
import debounce from 'lodash/debounce';
import { useTranslation } from 'react-i18next';
import { Drawer, Icon } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import { addTag } from '@/apis';
import styles from './index.module.less';

interface CustomCategoryCreatePops {
  visible: boolean;
  parentId: number;
  onClose: () => void;
  onConfirm: () => void;
}

const { TextArea } = Input;

function CreateTagDrawer({
  visible,
  parentId,
  onClose,
  onConfirm,
  ...props
}: CustomCategoryCreatePops) {
  const { t } = useTranslation();
  const drawerRef = useRef<DrawerRefType>(null);
  const [form] = Form.useForm();
  const [text, setText] = useState('');
  const [tagList, setTagList] = useState<string[]>([]);

  // eslint-disable-next-line consistent-return
  const onWriteDetailName = (val: string) => {
    const tag = val.replace(/ /g, '');
    if (tag.indexOf('\n') !== -1) {
      const tagArr = tag.split('\n').filter((item) => item !== '');

      for (let i = 0; i < tagArr.length; i += 1) {
        const element = tagArr[i];
        if (element.length > 15) {
          message.warn(t('tag_exceed_length', { index: i + 1 }));
          break;
        }
      }
      setTagList(tagArr);
    } else if (tag.length > 15) {
      return message.warn(t('tag_exceed_length_single'));
    } else if (tag) {
      setTagList([tag]);
    } else if (!tag) {
      setTagList([]);
    }
    form.setFieldsValue({ tagValue: tag });
    setText(tag);
  };

  const onSubmit = debounce(() => {
    if (tagList.length) {
      addTag({ parentId, childNameSet: tagList }).then(() => {
        message.success(t('add_success2'));
        onConfirm();
      });
    } else {
      message.warning(t('grahic_enter_tag_name'));
    }
  }, 500);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]); // eslint-disable-line
  return (
    <Drawer
      title={t('add_tag_child')}
      visible={visible}
      {...props}
      onClose={onClose}
      ref={drawerRef}
      footer={
        <Drawer.Footer
          okText={t('public_save')}
          onOk={onSubmit}
          cancelText={t('paymentManage_cancel')}
          onCancel={onClose}
        />
      }
    >
      <div className={styles.flexBox}>
        <div>
          <Icon name="info-outline" color="#999eb2" size={16} style={{ marginRight: '5px' }} />
        </div>
        <div>{t('modify_delete_tag_warning')}</div>
      </div>
      <div className={styles.card}>
        <div className={styles.title}>
          {t('add_tag_child')}
          <span className={styles.tipNotice}>({t('value_length_limit')})</span>
        </div>
        <Form layout="vertical" form={form}>
          <Form.Item name="tagValue">
            <TextArea
              autoSize={{ minRows: 6, maxRows: 6 }}
              placeholder={t('enter_tag_name_placeholder')}
              bordered={false}
              style={{ height: 120, fontSize: '16px', padding: 0 }}
              value={text}
              onChange={(val) => onWriteDetailName(val.target.value)}
            />
            <div className={styles.enterText}>{t('enter_key_newline')}</div>
          </Form.Item>
        </Form>
      </div>
    </Drawer>
  );
}

export default CreateTagDrawer;
