import { useEffect, useState, useRef, useCallback } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import classNames from 'classnames';

import flattenDeep from 'lodash/flattenDeep';
import { useTranslation } from 'react-i18next';
import findIndex from 'lodash/findIndex';
import { Button, Checkbox, Spin, Divider, Typography } from 'antd';
import type { DrawerProps } from 'antd';
import { Drawer, Empty, Search, Icon, Price } from '@/components';
import { getCategoryCustomeList, skuGoodsList } from '@/apis';
import type { GetCategoryCustomeItemResult, GoodsListInfo } from '@/apis';
import type { RelationCustomizeType } from '@/apis/sku-goods-list';
import styles from './index.module.less';

interface PropsType extends DrawerProps {
  // eslint-disable-next-line no-unused-vars
  onOk: (shopSkuIdSet: number[], values: RelationCustomizeType[]) => void;
  tagCode: number;
}

interface GoodsParams {
  pageNo: number;
  pageSize: number;
  customizeCategoryCode: string;
  keyWord: string;
  totalPages: number;
}

const { Paragraph } = Typography;

function AddConTactGoods({ visible, onOk, tagCode, ...props }: PropsType) {
  const [t] = useTranslation();
  const [loading, setLoading] = useState(false);
  const [categoryList, setCategoryList] = useState<GetCategoryCustomeItemResult[]>([]);
  const [subCategoryList, setSubCategoryList] = useState<GetCategoryCustomeItemResult[]>([]);
  const [categoryId, setCategoryId] = useState(0);
  const [subCategoryId, setSubCategoryId] = useState(0);
  const [categoryHasMore, setCategoryHasMore] = useState(false);
  const [showSubCategory, setShowSubCategory] = useState(false);
  const [goodsList, setGoodsList] = useState<GoodsListInfo[]>([]);
  const [goodsHasMore, setGoodsHasMore] = useState(false);
  const [checkedIds, setCheckedIds] = useState<number[]>([]);
  const [checkedItems, setCheckedItems] = useState<RelationCustomizeType[]>([]);
  const categoryParams = useRef({ pageNo: 1, pageSize: 20, totalPages: 1 });
  const goodsParams = useRef<GoodsParams>({
    pageNo: 1,
    pageSize: 10,
    totalPages: 1,
    keyWord: '',
    customizeCategoryCode: '',
  });
  const [subIndex, setSubIndex] = useState(2);
  const [checkAll, setCheckAll] = useState(false);

  // 获取分类
  const getCategoryData = () => {
    getCategoryCustomeList({ ...categoryParams.current }).then((res) => {
      if (categoryParams.current.pageNo === 1) {
        res.list.unshift({
          id: 0,
          categoryName: t('contact_all'),
          code: '',
          grade: 1,
        });
      }
      categoryParams.current.totalPages = Math.ceil(
        res.pagination.count / categoryParams.current.pageSize
      );
      setCategoryHasMore(categoryParams.current.pageNo < categoryParams.current.totalPages);
      setCategoryList([...categoryList, ...res.list]?.filter((item) => item.grade !== 2));
    });
  };

  // 滚动加载分类
  const loadMoreCategory = () => {
    categoryParams.current.pageNo += 1;
    if (categoryParams.current.pageNo > categoryParams.current.totalPages) return;
    getCategoryData();
  };

  // 获取未关联商品
  const getGoodsData = (data: GoodsListInfo[] = []) => {
    const { pageNo, pageSize, keyWord, customizeCategoryCode } = goodsParams.current;
    skuGoodsList({
      pageNo,
      pageSize,
      type: 6,
      soureType: 2,
      sort: 'desc',
      keyWord,
      customizeCategoryCode,
      labelId: tagCode,
    }).then((res) => {
      goodsParams.current.totalPages = res.pagination.total;
      setGoodsHasMore(goodsParams.current.pageNo < goodsParams.current.totalPages);
      const newGoodsList = [...data, ...res.list];
      if (newGoodsList.length && checkedIds.length) {
        const currentCheckedGoodsList = newGoodsList.filter((goodsItem) =>
          checkedIds.includes(goodsItem.id)
        );
        setCheckAll(currentCheckedGoodsList.length === newGoodsList.length);
      } else {
        setCheckAll(false);
      }

      setGoodsList(newGoodsList);
    });
  };

  // 滚动加载未关联商品
  const loadMoreGoodsData = () => {
    goodsParams.current.pageNo += 1;
    if (goodsParams.current.pageNo > goodsParams.current.totalPages) return;
    getGoodsData(goodsList);
  };

  // 搜索
  const onSearch = (val: string) => {
    goodsParams.current.keyWord = val;
    goodsParams.current.pageNo = 1;
    setShowSubCategory(false);
    setSubCategoryId(0);
    setCategoryId(0);
    setSubCategoryList([]);
    getGoodsData();
  };

  // 选择一级分类
  const onFirstCategory = (item: GetCategoryCustomeItemResult) => {
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryCode = item.code ? item.code : '';
    setShowSubCategory(false);
    setSubCategoryId(0);
    getGoodsData();
    if (!item.id) {
      setCategoryId(0);
      setSubCategoryList([]);
      return;
    }
    setCategoryId(item.id);
    getCategoryCustomeList({
      pageNo: 1,
      pageSize: 999,
      parentId: item.id,
    }).then((res) => {
      const reg = /[\u4e00-\u9fa5]/g;
      let width = 0;
      let index = 0;
      for (let i = 0; i < res.list.length; i += 1) {
        const cateItem = res.list[i];
        if (cateItem.categoryName) {
          const chineseNum = cateItem?.categoryName?.match(reg)?.length || 0;
          const englishNum = cateItem.categoryName.length - chineseNum;
          width += chineseNum * 18.5 + englishNum * 9.5 + 12;
          if (width > 241) {
            index = i;
            break;
          }
        }
      }
      setSubIndex(index || res.list.length);
      setSubCategoryList(res.list);
    });
  };

  // 选择二级分类
  const onSecondaryCategory = (item: GetCategoryCustomeItemResult) => {
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryCode = item.code as string;
    setSubCategoryId(item.id as number);
    getGoodsData();
  };

  const onCheckAllChange = () => {
    const val = !checkAll;
    setCheckAll(val);
    if (val) {
      const shopSkuIdList: number[] = [];
      goodsList.forEach((item) => {
        if (!checkedIds.includes(item.id)) {
          checkedIds.push(item.id);
          shopSkuIdList.push(item.id);
        }
        const i = findIndex(checkedItems, ['customizeCatId', item.categoryId]);
        if (i === -1) {
          checkedItems.push({
            customizeCatId: item.categoryId,
            shopSkuIdList: [item.id],
            customizeCategoryName: item.categoryName,
          });
        } else {
          const checkedItem = checkedItems[i];
          if (!checkedItem.shopSkuIdList.includes(item.id)) {
            checkedItem.shopSkuIdList.push(item.id);
          }
        }
      });
    } else {
      goodsList.forEach((item) => {
        const index = checkedIds.indexOf(item.id);
        if (index !== -1) {
          checkedIds.splice(index, 1);
          for (let i = 0; i < checkedItems.length; i += 1) {
            const checkedItem = checkedItems[i];
            if (checkedItem.customizeCatId === item.categoryId) {
              checkedItems.splice(i, 1);
              i -= 1;
            }
          }
        }
      });
    }
    setCheckedIds([...checkedIds]);
    setCheckedItems([...checkedItems]);
  };

  // 选择
  const onChecked = (item: GoodsListInfo) => {
    const { id } = item;
    const i = findIndex(checkedItems, ['customizeCatId', item.categoryId]);
    if (i !== -1) {
      const checkedItem = checkedItems[i];
      const { shopSkuIdList } = checkedItem;
      const j = shopSkuIdList.indexOf(id);
      if (j !== -1) {
        shopSkuIdList.splice(j, 1);
        if (!shopSkuIdList.length) {
          checkedItems.splice(i, 1);
        }
      } else {
        shopSkuIdList.push(id);
      }
    } else {
      checkedItems.push({
        customizeCatId: item.categoryId,
        shopSkuIdList: [id],
        customizeCategoryName: item.categoryName,
      });
    }
    const ids = flattenDeep(checkedItems.map((checkItem) => checkItem.shopSkuIdList));

    // 当前商品列表已选中的项
    const currentCheckedGoodsList = goodsList.filter((goodsItem) => ids.includes(goodsItem.id));
    setCheckAll(currentCheckedGoodsList.length === goodsList.length);

    setCheckedIds(ids);
    setCheckedItems(checkedItems);
  };

  useEffect(() => {
    setCheckAll(false);
    setCheckedIds([]);
    setCheckedItems([]);
    if (visible) {
      getCategoryData();
      getGoodsData();
    } else {
      setLoading(false);
      setCategoryList([]);
      setSubCategoryList([]);
      setGoodsList([]);
      setCategoryId(0);
      setSubCategoryId(0);
      setShowSubCategory(false);
      categoryParams.current = { pageNo: 1, pageSize: 20, totalPages: 1 };
      goodsParams.current = {
        pageNo: 1,
        pageSize: 10,
        totalPages: 1,
        customizeCategoryCode: '',
        keyWord: '',
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 规格
  const standardStr = useCallback((arr) => {
    let str = '';
    arr.forEach((item: any) => {
      str += ` ${item.value}`;
    });
    return str.slice(1);
  }, []);

  return (
    <Drawer
      visible={visible}
      title={t('paymentManage_addProduct')}
      className={styles.addChildGoodsDrawer}
      footer={
        <div className={styles.drawerFooter}>
          <div className={styles.drawerFooterCheck}>
            <Checkbox
              defaultChecked={false}
              onChange={() => {
                onCheckAllChange();
              }}
              checked={checkAll}
            >
              {t('contact_select_all')}
            </Checkbox>
          </div>
          <Button
            type="primary"
            disabled={!checkedIds.length}
            onClick={() => {
              onOk(checkedIds, checkedItems);
            }}
          >
            {t('contact_confirm_goods', { count: checkedIds.length })}
          </Button>
        </div>
      }
      {...props}
    >
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <div className={styles.drawerHead}>
          <Search
            realTimeSearch
            placeholder={t('search_product_name')}
            className={styles.searchInput}
            onSearch={onSearch}
          />
        </div>
        <div className={styles.drawerContent}>
          <div className={styles.drawerContentLeft} id="drawerContentLeft">
            <InfiniteScroll
              dataLength={categoryList.length}
              className={styles.drawerContentLeftScroll}
              hasMore={categoryHasMore}
              loader={
                <div className="text-center">
                  <Spin tip={t('paymentManage_loading')} />
                </div>
              }
              next={loadMoreCategory}
              scrollableTarget="drawerContentLeft"
            >
              {categoryList.map((item) => (
                <div
                  role="button"
                  tabIndex={0}
                  key={item.id}
                  className={classNames(categoryId === item.id ? styles.itemActive : styles.item)}
                  onClick={() => {
                    onFirstCategory(item);
                  }}
                >
                  {item.categoryName}
                </div>
              ))}
            </InfiniteScroll>
          </div>
          <div className={styles.drawerContentRight}>
            {!!subCategoryList.length && (
              <div className={styles.secondaryCategory}>
                <div className={styles.secondaryCategoryContent}>
                  {subCategoryList.slice(0, subIndex).map((item) => (
                    <span
                      role="button"
                      tabIndex={item.id || 0}
                      key={item.id}
                      title={item.categoryName}
                      className={classNames(
                        styles.secondaryCategoryItem,
                        subCategoryId === item.id ? styles.secondaryCategoryItemActive : ''
                      )}
                      onClick={() => {
                        onSecondaryCategory(item);
                      }}
                    >
                      {item.categoryName}
                    </span>
                  ))}
                </div>
                {subCategoryList.length > subIndex ? (
                  <div
                    className={styles.secondaryCategoryOperate}
                    role="presentation"
                    onClick={() => {
                      setShowSubCategory(!showSubCategory);
                    }}
                  >
                    <Icon name={showSubCategory ? 'up' : 'down'} size={16} />
                  </div>
                ) : (
                  ''
                )}
                {showSubCategory ? (
                  <div className={styles.secondaryCategoryMore}>
                    <div className={styles.secondaryCategoryContent}>
                      {subCategoryList.slice(subIndex).map((item) => (
                        <span
                          role="button"
                          tabIndex={item.id || 0}
                          key={item.id}
                          title={item.categoryName}
                          className={classNames(
                            styles.secondaryCategoryItem,
                            subCategoryId === item.id ? styles.secondaryCategoryItemActive : '',
                            showSubCategory ? 'mt-2' : ''
                          )}
                          onClick={() => {
                            onSecondaryCategory(item);
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </div>
            )}
            <div className={styles.drawerContentRightList} id="drawerContentRightList">
              {goodsList.length ? (
                <InfiniteScroll
                  dataLength={goodsList.length}
                  className={styles.drawerContentRightScroll}
                  hasMore={goodsHasMore}
                  loader={
                    <div className="text-center">
                      <Spin tip={t('paymentManage_loading')} />
                    </div>
                  }
                  endMessage={
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>{t('channel_load_end')}</span>
                      </Divider>
                    </div>
                  }
                  next={loadMoreGoodsData}
                  scrollableTarget="drawerContentRightList"
                >
                  {goodsList.map((item) => (
                    <div className={styles.goodsItem} key={item.id}>
                      <div className={styles.imageBox}>
                        <img
                          src={item?.imagesList?.length ? item.imagesList[0] : ''}
                          className={styles.goodsItemImage}
                          alt=""
                        />
                      </div>
                      <div className={styles.goodsItemInfo}>
                        <div>
                          <Paragraph
                            ellipsis={{ rows: 2 }}
                            className={styles.goodsItemInfoName}
                            title={item.name}
                          >
                            {item.name}
                          </Paragraph>
                          <Paragraph ellipsis className={styles.goodsItemInfoStandard}>
                            {standardStr(item.standardList)}
                          </Paragraph>
                          <Paragraph ellipsis className={styles.goodsItemInfoStandard}>
                            {t('contact_min_sale_unit', { unit: item.saleGroup })}
                          </Paragraph>
                        </div>
                        <div className={styles.goodsItemInfoStandard}>
                          <Price value={item.marketPrice} />
                          <span> /{item.unit}</span>
                        </div>
                      </div>
                      <Checkbox
                        checked={checkedIds.includes(item.id)}
                        onChange={() => {
                          onChecked(item);
                        }}
                      />
                    </div>
                  ))}
                </InfiniteScroll>
              ) : (
                <Empty />
              )}
            </div>
          </div>
        </div>
      </Spin>
    </Drawer>
  );
}

export default AddConTactGoods;
