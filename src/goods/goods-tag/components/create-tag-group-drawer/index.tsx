import { useEffect, useRef, useState } from 'react';
import { Form, Input, message } from 'antd';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import debounce from 'lodash/debounce';
import { Drawer } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import { addTagGroup } from '@/apis';
import styles from './index.module.less';

interface CustomCategoryCreatePops {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const { TextArea } = Input;

function CreateTagGroupDrawer({ visible, onClose, onConfirm, ...props }: CustomCategoryCreatePops) {
  const drawerRef = useRef<DrawerRefType>(null);
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [text, setText] = useState('');
  const [tagList, setTagList] = useState<string[]>([]);

  // eslint-disable-next-line consistent-return
  const onWriteDetailName = (val: string) => {
    const tag = val.replace(/ /g, '');
    if (tag.indexOf('\n') !== -1) {
      const tagArr = tag.split('\n').filter((item) => item !== '');

      for (let i = 0; i < tagArr.length; i += 1) {
        const element = tagArr[i];
        if (element.length > 15) {
          message.warn(t('tag_exceed_length', { index: i + 1 }));
          break;
        }
      }
      setTagList(tagArr);
    } else if (tag.length > 15) {
      return message.warn(t('tag_exceed_length_single'));
    } else if (tag) {
      setTagList([tag]);
    } else if (!tag) {
      setTagList([]);
    }
    form.setFieldsValue({ tagValue: tag });
    setText(tag);
  };

  const onSubmit = debounce(() => {
    const { tagGroupName } = form.getFieldsValue();
    if (tagGroupName) {
      addTagGroup({ name: tagGroupName, childNameSet: tagList }).then(() => {
        message.success(t('add_success2'));
        onConfirm();
      });
    } else {
      message.warning(t('enter_tag_group_name'));
    }
  }, 500);

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]); // eslint-disable-line
  return (
    <Drawer
      title={t('create_tag_group')}
      visible={visible}
      {...props}
      onClose={onClose}
      ref={drawerRef}
      footer={
        <Drawer.Footer
          okText={t('public_save')}
          onOk={onSubmit}
          cancelText={t('paymentManage_cancel')}
          onCancel={onClose}
        />
      }
    >
      <Form layout="vertical" form={form}>
        <div className={classNames(styles.card, styles.cardForm)}>
          <Form.Item label={t('tag_group_name')} required name="tagGroupName">
            <Input
              maxLength={15}
              bordered={false}
              placeholder={t('enter_tag_group_name_placeholder')}
            />
          </Form.Item>
        </div>
        <div className={styles.card}>
          <div className={styles.title}>
            {t('add_tag_child')}
            <span className={styles.tipNotice}>({t('value_length_limit')})</span>
          </div>
          <Form.Item name="tagValue">
            <TextArea
              autoSize={{ minRows: 6, maxRows: 6 }}
              placeholder={t('enter_tag_name_placeholder')}
              bordered={false}
              style={{ height: 120, fontSize: '16px', padding: 0 }}
              value={text}
              onChange={(val) => onWriteDetailName(val.target.value)}
            />
            <div className={styles.enterText}>{t('enter_key_newline')}</div>
          </Form.Item>
        </div>
      </Form>
    </Drawer>
  );
}

export default CreateTagGroupDrawer;
