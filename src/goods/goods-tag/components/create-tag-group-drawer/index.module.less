@import 'styles/mixins/mixins';

.card,
.cardForm {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 10px;
  background: #fff;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);

  :global {
    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-input {
      font-size: @font-size-lg;
    }

    .ant-form-item-label {
      label {
        padding: 0 12px;
      }

      .ant-form-item-required {
        padding: 0;
      }
    }
  }
}

.cardForm {
  padding: 16px 8px;
}

.title {
  margin-bottom: 12px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f3f3f3;
}

.tipNotice {
  color: #888b98;
}

.enterText {
  color: #b1b3be;
  font-size: 12px;
  text-align: right;
}
