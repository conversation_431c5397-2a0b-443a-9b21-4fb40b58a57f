import { useEffect, useState } from 'react';
import { Button, Checkbox, message, Spin } from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import { useTranslation } from 'react-i18next';
import { Drawer, Empty, Modal } from '@/components';
import RelateGoodsItem from '@/src/goods/custom-category/components/relate-goods-item';
import { getContactGoodsList, addContantGoods, removeContantGoods } from '@/apis';
import type { GetContactGoodsListResult } from '@/apis';
import AddContactGoods from '../add-contact-goods';
import styles from './index.module.less';

interface CustomCategoryCreatePops {
  visible: boolean;
  onClose: () => void;
  onRefresh: () => void;
  tagCode: number;
  count: number;
}

const pageSize = 20;

function ContactGoods({
  visible,
  onClose,
  onRefresh,
  tagCode,
  count,
  ...props
}: CustomCategoryCreatePops) {
  const [ids, setIds] = useState<number[]>([]);
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [load, setLoad] = useState(false);
  const [pageCount, setPageCount] = useState(1);
  const [page, setPage] = useState(2);
  const [contactGoodsList, setContactGoodsList] = useState<GetContactGoodsListResult[]>([]);
  const [isCheckedAll, setIsCheckedAll] = useState(false);
  const [isAddContactGoods, setIsAddContactGoods] = useState(false);

  // 页面初始数据
  const initialPageList = () => {
    setLoading(true);
    getContactGoodsList({ pageNo: 1, pageSize, labelIds: [tagCode] }).then((res) => {
      setIsCheckedAll(false);
      setPageCount(res.pagination.total);
      setContactGoodsList(res.list);
      setLoading(false);
    });
  };

  const onMoreContactGoodsList = () => {
    setLoad(true);
    setLoading(true);
    getContactGoodsList({
      pageNo: page,
      pageSize,
      labelIds: [tagCode],
    })
      .then((res) => {
        setPageCount(res.pagination.total);
        setPage(page + 1);
        setContactGoodsList((val) => val.concat(res.list));
      })
      .finally(() => {
        setLoading(false);
        setLoad(false);
      });
  };

  const loadMore = () => {
    onMoreContactGoodsList();
  };

  const onSelectGoods = (id: number) => {
    contactGoodsList?.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.isChecked = !items.isChecked;
      }
    });
    setIsCheckedAll(contactGoodsList?.every((item) => item.isChecked) || false);
    const arr = contactGoodsList?.filter((item) => item.isChecked).map((item) => item.id) || [];
    setIds([...arr]);
  };

  const onChangeAll = (e: boolean) => {
    setIsCheckedAll(e);
    contactGoodsList?.forEach((item) => {
      const items = item;
      items.isChecked = e;
    });
    if (e) {
      const arr = contactGoodsList?.map((item) => item.id) || [];
      setIds([...arr]);
    } else {
      setIds([]);
    }
  };

  const onDelete = () => {
    Modal.confirm({
      title: t('prompt'),
      content: t('relateGoods_confirmDelete'),
      okText: t('public_sure'),
      cancelText: t('public_cancel'),
      centered: true,
      onOk: () => {
        removeContantGoods({
          labelId: tagCode,
          shopSkuIds: ids,
        }).then(() => {
          message.success(t('delete_success'));
          setIds([]);
          onRefresh();
          initialPageList();
          setIsCheckedAll(false);
        });
      },
    });
  };

  const openAddContactGoodsDrawer = () => {
    setIsAddContactGoods(true);
  };

  useEffect(() => {
    if (visible) {
      initialPageList();
      if (count === 0) {
        setIsAddContactGoods(true);
      }
    }
  }, [visible, count]); // eslint-disable-line

  const footer = (
    <div className={styles.footer}>
      <Checkbox
        checked={isCheckedAll}
        disabled={!contactGoodsList?.length}
        onChange={(e) => onChangeAll(e.target.checked)}
      >
        {t('select_all')}
      </Checkbox>
      <Button disabled={!ids.length} onClick={onDelete}>
        {t('specificationsView_delete')}
      </Button>
    </div>
  );

  return (
    <Drawer
      title={t('related_goods')}
      visible={visible}
      {...props}
      onClose={onClose}
      footer={footer}
      push={false}
      extra={
        <div
          role="button"
          tabIndex={0}
          className={styles.titleAdd}
          onClick={openAddContactGoodsDrawer}
        >
          {t('add_related')}
        </div>
      }
    >
      <div id="goodsList" className={styles.goodsListBox}>
        <InfiniteScroll
          dataLength={contactGoodsList?.length}
          hasMore={page <= pageCount}
          loader={
            load ? (
              <div className="text-center">
                <Spin tip={t('loading')} />
              </div>
            ) : null
          }
          next={loadMore}
          scrollableTarget="goodsList"
        >
          <Spin spinning={loading}>
            {contactGoodsList?.length ? (
              contactGoodsList?.map((item) => (
                <div className={styles.item} key={item.id}>
                  <Checkbox
                    checked={ids.includes(item.id)}
                    onChange={() => onSelectGoods(item.id)}
                  />
                  <RelateGoodsItem info={item} />
                </div>
              ))
            ) : (
              <div className={styles.noData}>
                <Empty />
              </div>
            )}
          </Spin>
        </InfiniteScroll>
      </div>
      <AddContactGoods
        visible={isAddContactGoods}
        tagCode={tagCode}
        onClose={() => {
          setIsAddContactGoods(false);
        }}
        onOk={(idList) => {
          addContantGoods({
            labelId: tagCode,
            shopSkuIds: idList,
          }).then(() => {
            message.success(t('channel_add_success'));
            setIds([]);
            onRefresh();
            initialPageList();
            setIsCheckedAll(false);
            setIsAddContactGoods(false);
          });
        }}
      />
    </Drawer>
  );
}

export default ContactGoods;
