/* stylelint-disable */
@import 'styles/mixins/mixins';

.card {
  height: 298px;
  padding: 20px 20px 24px 20px;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0px rgba(0, 0, 0, 0.08);
  background-color: white;
}

.cardTitle {
  display: flex;
  border-bottom: 1px solid #f3f3f3;
  padding-bottom: 16px;
  .cardTitleLeft {
    flex: 1;
  }

  .cardTitleText {
    font-size: 18px;
    font-weight: 500;
    color: #040919;
    line-height: 26px;
  }

  .cardTitleDesc {
    font-size: 14px;
    color: #888b98;
    line-height: 22px;
    margin-top: 4px;
  }
}

.websiteNum {
  margin-top: 12px;
  font-size: 12px;
  font-weight: 500;
  color: #040919;
  line-height: 25px;
}

.websiteBox {
  display: flex;
  margin-top: 8px;

  .websiteItem {
    height: 141px;
    width: 100%;
    background: rgba(217, 238, 255, 0.3);
    padding: 13px 0 16px 16px;
    border-radius: 8px;

    &:first-child {
      margin-right: 16px;
    }

    .websiteItemLabel {
      font-size: 12px;
      color: #888b98;
      margin-bottom: 12px;
    }

    .websiteItemValue {
      font-size: 14px;
      color: #040919;
    }
  }
}

.noData {
  display: flex;
  height: calc(100% - 45px);
  justify-content: center;
  flex-direction: column;
  align-items: center;

  &Image {
    margin-bottom: 10px;
  }

  .noDataText {
    line-height: 20px;
    font-size: 12px;
  }
  .noDataText2 {
    font-size: 12px;
    color: #888b98;
    line-height: 20px;
    margin-top: 4px;
    margin-bottom: 12px;
  }
}

.textEllipsis2 {
  word-break: break-all;
  .text-overflow(2);
}

.textEllipsis1 {
  .text-overflow(1);
}
