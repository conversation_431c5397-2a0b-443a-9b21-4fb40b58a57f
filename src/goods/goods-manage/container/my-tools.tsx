import { Image } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { testPerm } from '@/utils/permission';
import toolsList from './my-tools-data';
import styles from './my-tools.module.less';

function MyTools() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <div className={styles.card}>
      <div className={styles.cardTitle}>{t('my_tools')}</div>
      <div className={styles.tools}>
        {toolsList?.map((item) => (
          <div
            className={styles.toolItem}
            key={item.url}
            role="presentation"
            onClick={() => {
              if (item.code && !testPerm(item.code)) {
                return;
              }
              navigate(item.url);
            }}
          >
            <Image src={item.icon} className={styles.toolItemImage} preview={false} />
            <div className={styles.toolItemName}>{item.name}</div>
          </div>
        ))}
      </div>
    </div>
  );
}

export default MyTools;
