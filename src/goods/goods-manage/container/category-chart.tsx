import { useEffect, useMemo, useState, useCallback } from 'react';
import type {
  ComposeOption,
  LegendComponentOption,
  PieSeriesOption,
  TitleComponentOption,
  ToolboxComponentOption,
  TooltipComponentOption,
} from 'echarts';
import { Spin } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import { getMcsCategoryStatistics } from '@/apis';
import { Icon, Echarts, EchartsProps } from '@/components';
import { useEcharts } from '@/hooks';
import styles from './category-chart.module.less';

type EChartsOption = ComposeOption<
  | TitleComponentOption
  | ToolboxComponentOption
  | TooltipComponentOption
  | LegendComponentOption
  | PieSeriesOption
>;

interface OptionDataItem {
  value: number;
  itemValue: number;
  name: number | string;
}

function CategoryChart() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const echartsRef = useEcharts();
  const [showLoading, setShowLoading] = useState(false);
  const legendColors = useMemo(() => ['#008CFF', '#16DBCC', '#FFBB38', '#FF82AC', '#BC82FF'], []);
  const [inventoryData, setInventoryData] = useState([] as OptionDataItem[]);

  useEffect(() => {
    setShowLoading(true);
    getMcsCategoryStatistics()
      .then((res) => {
        const numberArr = [40, 33, 28, 22, 20];
        // const numberArr = [40, 34, 28, 22, 16];
        const data = res.list.map((item, index) => ({
          value: numberArr[index],
          itemValue: item.totalNum,
          name: item.customizeCategoryName,
          itemStyle: {
            color: legendColors[index],
          },
        }));
        setInventoryData(data);
        // setInventoryData([]);
      })
      .finally(() => {
        setShowLoading(false);
      });
  }, [legendColors]);

  const inventoryOption: EchartsProps['option'] = useCallback(
    () =>
      ({
        tooltip: {
          position: 'bottom',
          trigger: 'item',
          formatter(params) {
            // @ts-ignore
            return `<span style="color:#040919">${t('bidding_quantity')} ${
              params.data.itemValue
            }</span><br /> <span style="color:#888b98">${params.data.name}</span>`;
          },
        },
        // legend: {
        //   left: 'center',
        //   top: 'bottom',
        //   icon: 'circle',
        // },
        grid: {
          width: '100%',
          height: '304px',
          containLabel: true,
        },
        series: [
          {
            name: '分类前五',
            type: 'pie',
            radius: [29, 76],
            center: ['50%', '50%'],
            roseType: 'radius',
            itemStyle: {
              // borderRadius: 5,
            },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            data: inventoryData,
          },
        ],
      } as EChartsOption),
    [inventoryData, t]
  );

  return (
    <div className={styles.stock}>
      <Spin spinning={showLoading}>
        <div className={styles.card}>
          <div
            className={styles.title}
            role="presentation"
            onClick={() => {
              navigate(`/goods/custom/category`);
            }}
          >
            {t('category_management')}
            <Icon name="right" size={18} style={{ cursor: 'pointer' }} color="#999EB2" />
          </div>
          {inventoryData.length ? (
            <div className={styles.chartWrap}>
              <div className={styles.chartBox}>
                <Echarts
                  ref={echartsRef}
                  // id="psiCategoryChart"
                  option={inventoryOption}
                  style={{ height: '170px', width: '170px' }}
                />
                <div className={styles.chartBoxName}>{t('top_five_categories')}</div>
              </div>

              <div className={styles.chartLegend}>
                {inventoryData.map((item, index) => (
                  <div className={styles.chartLegendItem} key={`${item.name}${Math.random()}`}>
                    <div
                      className={styles.chartLegendItemDot}
                      style={{ background: legendColors[index] }}
                    />
                    <div className={styles.chartLegendItemText} title={`${item.name}`}>
                      <div className={styles.chartLegendItemTextText}>{item.name}</div>
                    </div>
                    <div className={styles.chartLegendItemNum} title={`${item.itemValue}`}>
                      <div className={styles.chartLegendItemNum}>{item.itemValue}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className={styles.noData}>
              <img
                className={styles.noDataImage}
                src="https://img.huahuabiz.com/user_files/1659422966009985620/no-data.c0069b13.png"
                alt=""
              />
              <div className={styles.noDataText}>{t('no_category_info')}</div>
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
}

export default CategoryChart;
