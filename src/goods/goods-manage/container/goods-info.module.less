/* stylelint-disable */
@import 'styles/mixins/mixins';

.navigation {
  color: #040919;
  font-size: 24px;
  font-weight: 500;
  width: 96px;
  height: 24px;
  line-height: 24px;
  margin: 12px 0;
  letter-spacing: 0;
}

.headTitle {
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.card {
  padding: 20px 24px 19px 24px;
  border-radius: 18px;
  background: linear-gradient(58deg, #1b38fa 2%, #1b38fa 2%, #1b87fa 97%, #1b87fa 97%);
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .rightBtnBox {
    display: flex;
    cursor: pointer;

    .rightBtn {
      color: white;
      font-size: 14px;
      font-weight: 100;
      margin-top: 2px;
    }
  }

  .countAll {
    margin: 19px 0;

    .number {
      color: #fff;
      font-size: 32px;
      font-weight: 500;
      margin-bottom: 4px;
    }

    .text {
      color: #fff;
      font-size: 14px;
      display: flex;
      align-items: center;
      cursor: pointer;
      opacity: 0.7;
    }
  }

  .countItem {
    white-space: nowrap;
  }

  .count {
    display: inline-block;
    margin-right: 74px;
    cursor: pointer;

    &:first-child {
      position: relative;
    }

    .flexBox {
      display: flex;
      align-items: end;
    }

    .number {
      color: #fff;
      font-size: 18px;
      font-weight: 500;
      margin-bottom: 4px;
      text-align: center;
    }

    .text {
      color: #fff;
      font-size: 14px;
      display: flex;
      align-items: center;
      opacity: 0.7;
    }

    .iconBox {
      position: relative;
      top: 2px;
    }
  }
}
