import { useEffect, useState } from 'react';
import { Progress, Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
// import classNames from 'classnames';

// import { Icon } from '@/components';
// import { testPerm } from '@/utils/permission';
import { getShopSKUCount } from '@/apis';
// import { formatPrice } from '@/utils/utils';
import styles from './goods-status.module.less';

function GoodsStatus() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showLoading, setShowLoading] = useState(false);
  const [statusInfo, setStatusInfo] = useState({
    totalNum: 0,
    sellCount: 0,
    notSoldCount: 0,
  });

  useEffect(() => {
    setShowLoading(true);
    getShopSKUCount()
      .then((res) => {
        setStatusInfo(res);
      })
      .finally(() => {
        setShowLoading(false);
      });
  }, []);

  return (
    <Spin spinning={showLoading}>
      <div className={styles.card}>
        <div className={styles.cardTitle}>{t('goods_status')}</div>
        <div className={styles.totalNum}>
          {t('total_quantity')} <span className={styles.num}>{statusInfo.totalNum}</span>
        </div>

        <Progress
          strokeLinecap="butt"
          percent={(statusInfo.sellCount / statusInfo.totalNum) * 100}
          trailColor="#F9AE08"
          showInfo={false}
          className={styles.goodsStatusProgress}
        />
        <div className={styles.upDownNum}>
          <div
            className={styles.upDownNumItem}
            role="presentation"
            onClick={() => {
              navigate(`/shop/goods/list/?status=50`);
            }}
          >
            <div className={styles.upDot} />
            {t('listed')} <span className={styles.num}>{statusInfo.sellCount}</span>
          </div>
          <div
            className={styles.upDownNumItem}
            role="presentation"
            onClick={() => {
              navigate(`/shop/goods/list/?status=90`);
            }}
          >
            <div className={styles.downDot} />
            {t('not_listed')} <span className={styles.num}>{statusInfo.notSoldCount}</span>
          </div>
        </div>
      </div>
    </Spin>
  );
}

export default GoodsStatus;
