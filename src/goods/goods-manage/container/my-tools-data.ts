// eslint-disable-next-line import/prefer-default-export
import i18n from 'i18next';

const toolsList = [
  {
    name: 'AI上架',
    icon: 'https://img.huahuabiz.com/user_files/2025527/1748334936453674.png',
    url: '/artifacts?type=createGoods',
    code: 'M_001_002_001',
  },
  {
    name: i18n.t('goodsManage_createGoods'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692688815824132.png',
    url: '/goods/create-product/single/0/',
    code: 'M_001_002_001',
  },
  {
    name: i18n.t('batch_new_product'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692688917847762.png',
    url: '/shop/goods/most',
    code: 'M_001_002_002',
  },
  {
    name: i18n.t('goodsManage_createPackage'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692689518770755.png',
    url: '/goods-package/base-info',
    code: 'M_001_005',
  },
  {
    name: i18n.t('goodsManage_createServiceGoods'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692689524726312.png',
    url: '/shop/goods/most?createType=2',
    code: 'M_001_007',
  },
  {
    name: i18n.t('goodsManage_importGoods'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692689562275962.png',
    url: '/shop/goods/list?operateType=importGoods',
    code: 'M_001_001_001',
  },
  {
    name: i18n.t('goodsManage_updateGoods'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692689579679406.png',
    url: '/shop/goods/list?operateType=updateGoods',
    code: 'M_001_001_001',
  },
  {
    name: i18n.t('goodsManage_distributionManage'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692689595579692.png',
    url: '/goods/distribution/index',
    code: 'M_001_010',
  },
  {
    name: i18n.t('goodsManage_customCategory'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/169268970747321.png',
    url: '/goods/custom/category',
    code: 'M_001_012',
  },
  {
    name: i18n.t('goodsManage_goodsRelationService'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/16926896143207.png',
    url: '/goods/relation/service',
    code: 'M_001_009',
  },
  {
    name: i18n.t('goodsManage_reportRecord'),
    icon: 'https://img.huahuabiz.com/user_files/20230822/1692689654262524.png',
    url: '/shop/goods/derive',
    code: 'M_001_011',
  },
  {
    name: i18n.t('goodsManage_goodsList'),
    icon: 'https://img.huahuabiz.com/user_files/20230821/1692584906580150.png',
    url: '/shop/goods/list',
    code: 'M_001_001_001',
  },
  {
    name: i18n.t('goodsManage_settings'),
    icon: 'https://img.huahuabiz.com/user_files/20231012/16970956814913.png',
    url: '/goods/settings-product-show',
  },
  {
    name: i18n.t('goodsManage_goodsTag'),
    icon: 'https://img.huahuabiz.com/user_files/2024226/1708938210311446.png',
    url: '/goods/goods-tag',
    code: 'M_001_014',
  },
];

export default toolsList;
