import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Spin } from 'antd';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/components';
import { getPmsGoodsNumData } from '@/apis';
import styles from './goods-info.module.less';

interface countItemParams {
  num: number;
  text: string;
  value: number;
  url: string;
}
function GoodsInfo() {
  const { t } = useTranslation();
  const [showLoading, setShowLoading] = useState(false);
  const [countItem, setCountItem] = useState<countItemParams[]>([]);
  const [editTotal, setEditTotal] = useState<number>(0);
  const navigate = useNavigate();

  useEffect(() => {
    setShowLoading(true);
    getPmsGoodsNumData()
      .then((res) => {
        setEditTotal(res.editTotal);
        setCountItem([
          {
            num: res.shareTotal,
            text: t('goodsManage_shareGoods'),
            value: 0,
            url: '/shop/goods/list/?type=share',
          },
          {
            num: res.serviceTotal,
            text: t('goodsManage_serviceGoods'),
            value: 2,
            url: '/shop/goods/list/?type=service',
          },
          {
            num: res.packageTotal,
            text: t('package'),
            value: 80,
            url: '/shop/goods/list/?type=pack',
          },
        ]);
      })
      .finally(() => {
        setShowLoading(false);
      });
  }, [t]);

  return (
    <Spin spinning={showLoading}>
      <div className={styles.card}>
        <div className={styles.head}>
          <div className={styles.headTitle}>{t('goods_statistics')}</div>
          <div
            className={styles.rightBtnBox}
            tabIndex={0}
            role="button"
            onClick={() => navigate('/shop/goods/list')}
          >
            <div className={styles.rightBtn}>{t('view')}</div>
            <Icon name="right" size={18} color="#FFFFFF" />
          </div>
        </div>
        <div className={styles.countAll}>
          <div className={styles.number}>{editTotal}</div>
          <div
            className={styles.text}
            role="button"
            tabIndex={0}
            onClick={() => {
              navigate(`/shop/goods/list/?type=sell`);
            }}
          >
            {t('self_built_goods')}
            <Icon name="right" size={16} style={{ cursor: 'pointer' }} color="#FFFFFF" />
          </div>
        </div>
        <div className={styles.countItem}>
          {countItem.map((m) => (
            <div className={styles.count} role="button" tabIndex={0} key={m.value}>
              <div className={styles.flexBox}>
                <div>
                  <div className={styles.number}>{m.num}</div>
                  <div
                    className={styles.text}
                    role="presentation"
                    onClick={() => {
                      navigate(m.url);
                    }}
                  >
                    {m.text}
                  </div>
                </div>
                <Icon name="right" size={16} color="#FFFFFF" className={styles.iconBox} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </Spin>
  );
}

export default GoodsInfo;
