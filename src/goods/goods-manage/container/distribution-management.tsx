import { useEffect, useState } from 'react';
import { Button, Spin } from 'antd';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { getMcsDistributionWebsiteStatistics } from '@/apis';
import styles from './distribution-management.module.less';

function DistributionManagement() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [showLoading, setShowLoading] = useState(false);
  const [websiteList, setWebsiteList] = useState<
    {
      tenantId: string;
      tenantName: string;
      totalNum: number;
    }[]
  >([]);

  useEffect(() => {
    setShowLoading(true);
    getMcsDistributionWebsiteStatistics()
      .then((res) => {
        setWebsiteList(res.list || []);
      })
      .finally(() => {
        setShowLoading(false);
      });
  }, []);

  return (
    <Spin spinning={showLoading}>
      <div className={styles.card}>
        <div className={styles.cardTitle}>
          <div className={styles.cardTitleLeft}>
            <div className={styles.cardTitleText}>{t('distribution_management')}</div>
            <div className={styles.cardTitleDesc}>{t('distribute_goods_to_sites')}</div>
          </div>
          {websiteList.length > 0 && (
            <Button
              type="primary"
              size="small"
              style={{ marginTop: 10, minWidth: '76px', height: '28px' }}
              disabled={!websiteList.length}
              onClick={() => {
                navigate(`/goods/distribution/index`);
              }}
            >
              {t('go_distribute')}
            </Button>
          )}
        </div>
        {websiteList.length ? (
          <div>
            <div className={styles.websiteNum}>
              {t('joined_sites_count', { count: websiteList.length })}
            </div>
            <div className={styles.websiteBox}>
              {websiteList.map((item, index) =>
                index < 2 ? (
                  <div
                    className={styles.websiteItem}
                    key={item.tenantId}
                    style={{ marginRight: websiteList.length === 1 ? '0' : '' }}
                  >
                    <div className={styles.websiteItemLabel}>{t('site_name')}</div>
                    <div
                      className={classNames(styles.websiteItemValue, styles.textEllipsis2)}
                      style={{ minHeight: 38 }}
                      title={item.tenantName}
                    >
                      {item.tenantName}
                    </div>
                    <div className={styles.websiteItemLabel}>{t('distribution_count')}</div>
                    <div
                      className={classNames(styles.websiteItemValue, styles.textEllipsis1)}
                      title={String(item.totalNum)}
                    >
                      {item.totalNum}
                    </div>
                  </div>
                ) : null
              )}
            </div>
          </div>
        ) : (
          <div className={styles.noData}>
            <img
              className={styles.noDataImage}
              src="https://img.huahuabiz.com/user_files/1689671736227969415/no-data-mini.svg"
              alt=""
            />
            <div className={styles.noDataText}>{t('no_sites_joined')}</div>
            <div className={styles.noDataText2}>{t('create_site_prompt')}</div>
            <Button
              type="primary"
              size="small"
              onClick={() => {
                navigate(`/build/manage?action=1`);
              }}
            >
              {t('create_site')}
            </Button>
          </div>
        )}
      </div>
    </Spin>
  );
}

export default DistributionManagement;
