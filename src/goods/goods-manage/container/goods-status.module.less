/* stylelint-disable */
@import 'styles/mixins/mixins';

.card {
  height: 213px;
  padding: 20px;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0px rgba(0, 0, 0, 0.08);
  background-color: white;
}

.cardTitle {
  font-size: 18px;
  line-height: 26px;
  font-weight: 500;
}

.upDownNum {
  display: flex;
  align-items: center;
  justify-content: center;
}

.upDot {
  background-color: #008cff;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 8px;
}

.downDot {
  background-color: #f9ae08;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 67px;
  margin-right: 8px;
}

.upDownNumItem {
  display: flex;
  justify-content: center;
  align-items: center;
  color: #888b98;
  cursor: pointer;
}

.totalNum {
  margin-top: 20px;
  color: #888b98;
}

.num {
  color: #040919;
  font-size: 18px;
  margin-left: 8px;
}

.goodsStatusProgress {
  :global {
    .ant-progress-inner {
      border-radius: 4px;
    }
    .ant-progress-bg {
      border-radius: 4px 0 0 4px;
      height: 26px !important;
    }

    .ant-progress-outer {
      height: 86px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
