import { Row, Col } from 'antd';
import { useMount } from 'ahooks';
import { Context } from '@/components';
import { useTranslation } from 'react-i18next';
import { clearPmsCategorySpec } from '@/apis';
import GoodsInfo from '../../container/goods-info';
import GoodsStatus from '../../container/goods-status';
import CategoryChart from '../../container/category-chart';
import DistributionManagement from '../../container/distribution-management';
import MyTools from '../../container/my-tools';

// import styles from './index.module.less';

export default function GraphicDesc() {
  const { t } = useTranslation();

  useMount(() => {
    clearPmsCategorySpec({});
  });

  return (
    <Context
      container
      permission={{ code: 'M_001', newLogic: true }}
      theme={null}
      head={<Context.Head title={t('goods_management')} />}
    >
      <Row gutter={[20, 16]}>
        <Col md={14} lg={12} xs={24}>
          <GoodsInfo />
        </Col>
        <Col md={10} lg={12} xs={24}>
          <GoodsStatus />
        </Col>
        <Col md={14} lg={12} xs={24}>
          <CategoryChart />
        </Col>
        <Col md={10} lg={12} xs={24}>
          <DistributionManagement />
        </Col>
        <Col span={24}>
          <MyTools />
        </Col>
      </Row>
    </Context>
  );
}
