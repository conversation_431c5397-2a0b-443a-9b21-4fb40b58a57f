import { Context } from '@/components';
import { useTranslation } from 'react-i18next';

const { Head } = Context;

function MultiplePage() {
  const { t } = useTranslation();

  return (
    <Context
      head={
        <Head
          title={[{ title: t('goods_management'), to: '/shop/goods/list' }, t('batch_add_goods')]}
        />
      }
      theme={null}
    />
  );
}

MultiplePage.displayName = 'MultiplePage';

MultiplePage.defaultProps = {};

export default MultiplePage;
