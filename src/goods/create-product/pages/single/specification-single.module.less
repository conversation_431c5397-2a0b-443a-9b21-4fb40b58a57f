.form {
  :global {
    .ant-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .ant-form-item-label {
      width: 130px;
    }

    .ant-form-item-explain-error {
      font-size: 12px;
    }
  }
}

.flexBox {
  color: #008cff;
  display: flex;
  margin-top: 4px;
  align-items: center;

  .flexItem {
    color: #008cff;
    font-size: 12px;
    cursor: pointer;
  }

  .rightIcon {
    cursor: pointer;
  }
}

.suffix {
  border-left: 1px solid #b1b3be;
  padding-left: 10px;
}

.labelIcon {
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

.setMutUnitVarErr {
  color: #ea1c26;
  margin-top: 8px;
  cursor: pointer;
}

.bigSelectAddBtn {
  color: #008cff;
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.templateBox {
  width: 100%;
}
