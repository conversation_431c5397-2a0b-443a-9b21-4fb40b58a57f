@import 'styles/mixins/mixins';

.attrBtn {
  display: flex;
}

.attrBtnItem {
  color: #008cff;
  font-size: 12px;
  cursor: pointer;
  margin-right: 18px;
}

.item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  border-radius: 6px;
  border: 1px solid #b1b3be;
  padding: 0 0 0 12px !important;
}

.name {
  max-width: 140px;
  padding-right: 4px;
  border-right: 1px solid #b1b3be;
  .text-overflow();
}

.select {
  flex: 1;
  text-align: right;
  width: calc(100% - 140px) !important;

  :global {
    .ant-select-selection-item {
      padding-right: 24px !important;
    }

    .ant-select-selector input {
      text-align: right;
    }
  }
}

.selectDrop {
  :global {
    .ant-select-item {
      text-align: right !important;
    }
  }
}

@media screen and (min-width: 1350px) {
  .list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    grid-gap: 16px;
  }
}
