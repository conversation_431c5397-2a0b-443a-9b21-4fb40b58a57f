import { useEffect, useState } from 'react';
import { Form, message, Select } from 'antd';
import { getBaseStandardList, GetBaseStandardListRes, AttributeListsType } from '@/apis';
import { useRequest } from 'ahooks';
import { cloneDeep } from 'lodash';
import classNames from 'classnames';
import { useTranslation } from 'react-i18next';
import goodsAttr from '../../containers/goods-attr';
import goodsAttrCreate from '../../containers/goods-attr-create';
import styles from './base-info-attr.module.less';

interface BaseInfoAttrProps {
  catId: number;
  attrOptions: GetBaseStandardListRes[];
  attributeList: any;
  onConfirm: MultipleParamsFn<[val: AttributeListsType[]]>;
}

function BaseInfoAttr({ attrOptions, attributeList, onConfirm, catId }: BaseInfoAttrProps) {
  const { t } = useTranslation();
  const [list, setList] = useState<GetBaseStandardListRes[]>([...attrOptions]);

  const { run } = useRequest(getBaseStandardList, {
    manual: true,
    onSuccess: (res) => {
      res.list.forEach((item) => {
        const items = item;
        list.forEach((each) => {
          if (items.id === each.id) {
            items.attributeId = each.attributeId;
          }
        });
      });
      setList(cloneDeep(res.list));
    },
  });

  const onCreate = () => {
    if (list.length >= 20) {
      message.warning(t('base_attrLimitWarning'));
      return;
    }
    goodsAttrCreate({
      catId,
      onConfirm: () => {
        run({ catId, useWay: 4, source: 1 });
      },
    });
  };

  const onEdit = () => {
    goodsAttr({
      catId,
      onConfirm: (val) => {
        val.forEach((item) => {
          const items = item;
          list.forEach((each) => {
            if (items.id === each.id) {
              items.attributeId = each.attributeId;
            }
          });
        });
        setList(cloneDeep(val));
      },
    });
  };

  const onChangeData = (val: number, id: number) => {
    list.forEach((item) => {
      const items = item;
      if (items.id === id) {
        items.attributeId = val;
      }
    });
    const arr = list
      .filter((item) => item.attributeId)
      .map((each) => ({
        standardId: each.id as number,
        attributeId: each.attributeId,
      }));
    onConfirm(arr);
    setList(cloneDeep(list));
  };

  useEffect(() => {
    attrOptions.forEach((item) => {
      const items = item;
      attributeList.forEach((each: any) => {
        if (items.id === each.standardId) {
          items.attributeId = each.attributeId;
        }
      });
    });
    const arr = attrOptions
      .filter((item) => item.attributeId)
      .map((each) => ({
        standardId: each.id as number,
        attributeId: each.attributeId,
      }));
    onConfirm(cloneDeep(arr));
    setList(cloneDeep(attrOptions));
  }, [attrOptions, attributeList]); // eslint-disable-line

  return (
    <Form.Item label={t('base_productAttributes')}>
      <div className={styles.form}>
        <div className={styles.list}>
          {list.map((item) => (
            <div
              key={item.id}
              className={classNames(styles.item, 'ant-input')}
              id={`item${item.id}`}
            >
              <div className={styles.name} title={item.attrbute}>
                {item.attrbute}
              </div>
              <Select
                bordered={false}
                value={item.attributeId}
                showSearch
                allowClear
                optionFilterProp="detailName"
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                placeholder={t('base_selectProductAttributeValue')}
                options={item.standardDetail}
                fieldNames={{ label: 'detailName', value: 'id' }}
                className={styles.select}
                dropdownClassName={styles.selectDrop}
                onChange={(e) => onChangeData(e, item.id as number)}
              />
            </div>
          ))}
        </div>
        <div className={styles.attrBtn}>
          <div role="button" tabIndex={0} className={styles.attrBtnItem} onClick={onCreate}>
            {t('base_createProductAttribute')}
          </div>
          <div role="button" tabIndex={0} className={styles.attrBtnItem} onClick={onEdit}>
            {t('base_manageProductAttribute')}
          </div>
        </div>
      </div>
    </Form.Item>
  );
}

export default BaseInfoAttr;
