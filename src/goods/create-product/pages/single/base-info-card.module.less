.card {
  padding: 20px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
}

.cardTitle {
  font-size: 16px;
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  margin-bottom: 16px;
}

.cardContent {
  display: flex;
}

.media {
  width: 210px;
}

.form {
  width: 0;
  flex: 1;

  :global {
    .ant-form-item {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .ant-form-item-label {
      width: auto;
    }

    .ant-cascader-menu-item-active {
      opacity: 0.8;
    }

    .ant-form-item-explain-error {
      font-size: 12px;
    }
  }
}

.formItemMessage {
  color: #888b98;
  font-size: 12px;
  min-height: 20px;
  line-height: 20px;
  margin-top: 4px;
}

.nameLineBtn {
  color: #008cff;
  font-size: 12px;
  padding-left: 10px;
  border-left: 1px solid #c6ccd8;
}

.prefixNameLineBtn {
  color: #008cff;
  font-size: 12px;
  padding-right: 10px;
  border-right: 1px solid #c6ccd8;
}

.attrInput {
  margin-bottom: 16px;

  :global {
    input {
      text-align: right;
    }
  }
}

.unitSelectWrap {
  display: flex;
  align-items: center;
  // padding: 5px 12px !important;
  padding: 0;
}

.unitSelect {
  height: 30px;

  :global {
    .ant-select-selector {
      // height: 20px !important;
      // padding: 0 !important;
      padding-left: 10px;

      &::after {
        line-height: 20px;
      }
    }

    // .ant-select-selection-search {
    //   left: 0 !important;
    // }

    // .ant-select-selection-item,
    // .ant-select-selection-placeholder {
    //   line-height: 20px !important;
    // }
    // .ant-select-selection-search-input {
    // height: 20px !important;
    // }
  }
}

.unitFormulaWrap {
  display: flex;
  width: 180px;
  height: 20px;
  padding-left: 12px;
  align-items: center;
  border-left: 1px solid #c6ccd8;
}

.unitFormulaText {
  color: #888b98;
  font-size: 12px;
  margin-right: 4px;
  margin-left: 8px;
}

.unitFormulaIcon {
  width: 12px;
  height: 12px;
  cursor: pointer;
}

.unitFormulaLink {
  color: #008cff;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.unitFormulaLinkErr {
  color: #ea1c26;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  cursor: pointer;
}

.priceFormulaIcon {
  width: 16px;
  height: 16px;
  margin-left: 4px;
}

.priceFormulaText {
  color: #888b98;
  font-size: 14px;
  margin-left: 8px;
  vertical-align: middle;
}

.priceFormulaName {
  color: #888b98;
  font-size: 14px;
  line-height: 20px;
  margin-top: 4px;

  &:first-child {
    margin-top: 0;
  }
}

.priceFormulaVar {
  color: #008cff;
  font-size: 14px;
  display: flex;
  align-items: center;
  line-height: 20px;
  margin-top: 4px;
  cursor: pointer;

  &:first-child {
    margin-top: 0;
  }
}

.miniFormItem {
  min-height: 20px;

  :global {
    .ant-form-item-label > label {
      height: 20px !important;
    }

    .ant-form-item-control-input {
      min-height: 20px !important;
    }
  }
}

.priceFormulaNameBox {
  color: #008cff;
  display: flex;
  width: 100%;
  align-items: center;
  min-height: 32px;
  padding: 0 12px;
  justify-content: space-between;
  border-radius: 6px;
  border: 1px solid #b1b3be;
  cursor: pointer;
}

.priceFormulaNameBoxErr {
  border-color: #ea1c26;
}

.priceFormulaNameBoxErrText {
  color: #ea1c26;
  margin-top: 4px;
}

.setMutUnitVarErr {
  color: #ea1c26;
  font-size: 14px;
  // margin-top: 8px;
  cursor: pointer;
}

.treeSelectStyles {
  :global {
    .ant-select-multiple .ant-select-selection-item {
      max-width: 112px;
      height: 24px;
      line-height: 23px;
      border-radius: 4px;
    }

    .ant-select-selector {
      padding: 2px 8px;
    }

    .ant-select-selection-item-remove {
      font-size: 12px;
      padding: 0 3px;
    }
  }
}

.categoryBox {
  display: flex;
  flex-direction: row;
  border: 1px solid #b1b3be;
  border-radius: 6px;

  .categorySelect {
    flex: 1;
    // height: 32px;
    // box-sizing: border-box;
    :global {
      .ant-select-selector {
        position: relative;
        left: -3px;
      }

      .ant-select-selection-item {
        background-color: #f5f6fa;
      }
    }
  }

  .newCategory {
    color: #008cff;
    font-size: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;

    .newCategoryText {
      width: 100%;
      border-left: 1px solid #b1b3be;
      padding: 0 12px;
    }
  }
}
