/* eslint-disable react/no-array-index-key */
import { forwardRef, useImperativeHandle, useEffect, useState, ReactNode } from 'react';
import {
  Form,
  Input,
  Select,
  // Switch,
  FormInstance,
  Divider,
} from 'antd';
import { useTranslation } from 'react-i18next';
import { checkPerm, testPerm } from '@/utils/permission';
import { NumericInput, Icon } from '@/components';
import { formulaConvert } from '@/apis';
import { ResultType as FreightOptionType } from '@/apis/base/get-freight-options';
import { ExpirationDateType, StateType } from '@/apis/pms/type';
import styles from './specification-single.module.less';
import SetPriceVariableDrawer from '../../components/set-price-variable-drawer';
import NewBuildTemplateDrawer from '../../components/new-build-template-drawer';

interface PropsType {
  freightOptions: FreightOptionType[];
  categoryId: number;
  priceNum: number;
  unitNum: number;
  // isAloneBuy: number;
  marketPrice: number | null | string;
  saleGroup: number | null | string;
  freightTemplateId: number | null;
  costPrice: number | null;
  skuCode: string;
  barsCode: string;
  expirationDate: ExpirationDateType;
  isPriceFormula: boolean;
  formulaId: number;
  formulaList: any[];
  formatFormula: string;
  formulaName: string;
  saleUnit: string;
  unit: string;
  unitTemplateId: number;
  onSetState: SimpleFn<Partial<StateType>>;
  onGetFreightOptions: MultipleParamsFn<[]>;
}

export type SpecificationSingleInstance = {
  form: FormInstance;
};

const SpecificationSingleComponent = forwardRef<SpecificationSingleInstance, PropsType>(
  (
    {
      freightOptions,
      priceNum,
      unitNum,
      categoryId,
      // isAloneBuy,
      marketPrice,
      saleGroup,
      freightTemplateId,
      costPrice,
      skuCode,
      barsCode,
      expirationDate,
      isPriceFormula,
      formulaId,
      formulaList,
      formatFormula,
      formulaName,
      saleUnit,
      unit,
      unitTemplateId,
      onSetState,
      onGetFreightOptions,
    },
    ref
  ) => {
    const [visible, setVisible] = useState(false);
    const [visibleTemplate, setVisibleTemplate] = useState(false);
    const [saleUnitVal, setSaleUnitVal] = useState('');
    const [saleUnitOptions, setSaleUnitOptions] = useState<{ label: string; value: string }[]>([]);
    const [form] = Form.useForm();
    const { t } = useTranslation();

    const onDefine = (price: number | null) => {
      onSetState({
        marketPrice: price as unknown as number,
      });
    };

    const getUnitOption = (unitTemplateIdVal: number) => {
      formulaConvert(unitTemplateIdVal).then((res) => {
        setSaleUnitOptions(
          res.detailList?.map((item) => ({
            label: item.unitName || '',
            value: item.unitName || '',
          })) || []
        );
      });
    };

    // useEffect(() => {
    //   if (!isPriceFormula || formulaId) {
    //     onSetState({
    //       // marketPrice: null,
    //       formulaList:
    //         formulaList?.map((item) => ({
    //           ...item,
    //           value: null,
    //         })) || [],
    //     });
    //   }
    //   // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [isPriceFormula, formulaId]);

    useEffect(() => {
      form.setFieldsValue({ marketPrice, saleGroup, freightTemplateId, costPrice });
    }, [marketPrice, saleGroup, freightTemplateId, costPrice, form]);

    useEffect(() => {
      setSaleUnitVal(saleUnit || unit);
      if (unitTemplateId) {
        getUnitOption(unitTemplateId);
      }
    }, [unit, saleUnit, unitTemplateId, form]);

    useImperativeHandle(ref, () => ({
      form,
    }));

    // const checkIsSetVar = () => {
    //   let isSetVar = false;
    //   if (formulaList?.length > 0 && isPriceFormula) {
    //     isSetVar = !formulaList?.filter((item) => item.value !== null && item.value !== '');
    //   }
    //   return isSetVar;
    // };

    const createVarBox = () => {
      const isSetVar = !formulaList?.filter((item) => item.value !== null && item.value !== '')
        .length;
      let varNode: ReactNode | string = '';
      if (formulaList?.length > 0 && isPriceFormula) {
        if (isSetVar) {
          varNode = (
            <div
              className={styles.setMutUnitVarErr}
              role="presentation"
              onClick={() => {
                setVisible(true);
              }}
            >
              请输入变量值
              <Icon name="right" size={14} />
            </div>
          );
        } else {
          varNode = (
            <div className={styles.flexBox} role="presentation" onClick={() => setVisible(true)}>
              {formulaList.map((item, index) => (
                <div className={styles.flexItem} key={index}>
                  {item.name}: {item.value}
                  {index === formulaList.length - 1 ? '' : ','}
                </div>
              ))}
              <Icon name="right" size={18} className={styles.rightIcon} />
            </div>
          );
        }
      }

      return varNode;
    };

    return (
      <div id="goodsSpecificationSingle">
        <Form className={styles.form} form={form} initialValues={{ freightTemplateId: null }}>
          <Form.Item label={t('specification_single_retail_price')} required>
            <Form.Item
              name="marketPrice"
              rules={
                isPriceFormula
                  ? [
                      {
                        validator: (_rule, value) => {
                          if (value === 0 || value === '0') {
                            return Promise.reject(
                              new Error(t('specification_single_retail_price_zero_error'))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]
                  : [
                      { required: true, message: t('specification_single_enter_retail_price') },
                      {
                        validator: (_rule, value) => {
                          if (value === 0 || value === '0') {
                            return Promise.reject(
                              new Error(t('specification_single_retail_price_zero_error'))
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]
              }
              noStyle
            >
              <div
                role="presentation"
                onClick={() => {
                  if (isPriceFormula && formulaList?.length) {
                    setVisible(true);
                  }
                }}
              >
                <NumericInput
                  placeholder={t('specification_single_enter_retail_price')}
                  precision={priceNum}
                  value={marketPrice ? `${marketPrice}` : ''}
                  prefix={marketPrice ? <span>￥</span> : <span />}
                  suffix={<span className={styles.suffix}>{unit}</span>}
                  disabled={isPriceFormula}
                  maxLength={(priceNum ? priceNum + 1 : 0) + 10}
                  readOnly={!checkPerm('M_001_002_001_002_001')}
                  onClick={() => {
                    testPerm('M_001_002_001_002_001');
                  }}
                  max={9999999999}
                  onChange={(value) => {
                    onSetState({
                      marketPrice: value as unknown as number,
                    });

                    form.setFieldsValue({ marketPrice: value });
                  }}
                />
              </div>
            </Form.Item>

            <div>{createVarBox()}</div>
          </Form.Item>
          <Form.Item
            className={styles.saleGroupFormItem}
            label={
              <span
                style={{
                  display: 'inline-block',
                  width: '84px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  verticalAlign: 'middle',
                }}
                title={t('specification_single_minimum_sales_unit')}
              >
                {t('specification_single_minimum_sales_unit')}
              </span>
            }
            tooltip={{
              title: t('specification_single_enter_retail_price2'),
              icon: (
                <img
                  src="https://img.huahuabiz.com/user_files/2023324/167964345796046.svg"
                  alt=""
                  className={styles.labelIcon}
                />
              ),
            }}
            rules={[
              { required: true, message: t('specification_single_enter_minimum_sales_unit') },
              {
                validator: (_rule, value) => {
                  if (value === 0 || value === '0') {
                    return Promise.reject(
                      new Error(t('specification_single_minimum_sales_unit_zero_error'))
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
            name="saleGroup"
          >
            <NumericInput
              placeholder={t('specification_single_enter_minimum_sales_unit')}
              precision={unitNum}
              value={saleGroup ? `${saleGroup}` : ''}
              suffix={unitTemplateId ? null : <span className={styles.suffix}>{unit}</span>}
              addonAfter={
                unitTemplateId ? (
                  <Select
                    defaultValue={unit}
                    value={saleUnitVal}
                    options={saleUnitOptions}
                    bordered={false}
                    onChange={(val) => {
                      setSaleUnitVal(val);
                      onSetState({ saleUnit: val });
                    }}
                  />
                ) : null
              }
              maxLength={(unitNum ? unitNum + 1 : 0) + 8}
              readOnly={!checkPerm('M_001_002_001_005')}
              onClick={() => {
                testPerm('M_001_002_001_005');
              }}
              onChange={(value) => {
                onSetState({
                  saleGroup: value as unknown as number,
                });
                form.setFieldsValue({ saleGroup: value });
              }}
            />
          </Form.Item>

          <Form.Item
            label={t('freight_template')}
            name="freightTemplateId"
            // rules={[{ required: true, message: '请选择运费模版' }]}
            // required
          >
            <Select
              placeholder={t('select_freight_template2')}
              value={freightTemplateId || null}
              options={freightOptions}
              showSearch
              allowClear
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
              fieldNames={{
                label: 'name',
                value: 'id',
              }}
              onFocus={() => {
                testPerm('L_001_002_001_001');
              }}
              onChange={(value) => {
                if (!testPerm('L_001_002_001_001')) {
                  onSetState({
                    freightTemplateId,
                  });
                  form.setFieldsValue({ freightTemplateId });
                  return;
                }
                onSetState({
                  freightTemplateId: value,
                });
                form.setFieldsValue({ freightTemplateId: value });
              }}
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
              // eslint-disable-next-line react/no-unstable-nested-components
              dropdownRender={(menu) => (
                <div className={styles.templateBox}>
                  {menu}
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ padding: '0 8px 4px', width: '100%' }}>
                    <div
                      className={styles.bigSelectAddBtn}
                      role="presentation"
                      onClick={() => {
                        if (testPerm('L_001_002_002')) {
                          setVisibleTemplate(true);
                        }
                        // onAddUnitConversion();
                      }}
                    >
                      <Icon name="plus" />
                      {t('add_freight_template')}
                    </div>
                  </div>
                </div>
              )}
            />
          </Form.Item>
          <Form.Item
            label={t('initial_cost')}
            // rules={[
            //   { required: true, message: '请输入成本价' },
            //   {
            //     validator: (_rule, value) => {
            //       if (value === 0 || value === '0') {
            //         return Promise.reject(new Error('成本价不能为0'));
            //       }
            //       return Promise.resolve();
            //     },
            //   },
            // ]}
            name="costPrice"
          >
            <NumericInput
              placeholder={t('enter_initial_cost')}
              precision={priceNum}
              value={costPrice ? `${costPrice}` : ''}
              prefix={costPrice ? <span>￥</span> : <span />}
              suffix={<span className={styles.suffix}>{unit}</span>}
              maxLength={(priceNum ? priceNum + 1 : 0) + 10}
              max={9999999999}
              readOnly={!checkPerm('M_001_002_001_002_001')}
              onClick={() => {
                testPerm('M_001_002_001_002_001');
              }}
              onChange={(value) => {
                onSetState({
                  costPrice: value as unknown as number,
                });
                form.setFieldsValue({ costPrice: value });
              }}
            />
          </Form.Item>
          <Form.Item label={t('product_code')}>
            <Input
              placeholder={t('enter_product_code')}
              value={skuCode}
              maxLength={20}
              // readOnly={!checkPerm('M_001_002_001_002_001')}
              // onClick={() => {
              //   testPerm('M_001_002_001_002_001');
              // }}
              onChange={(e) => {
                let val = '';
                const { value } = e.target;
                const reg = /^[^\u4e00-\u9fa5]*$/;
                if (reg.test(value)) {
                  val = value;
                } else if (value === '') {
                  val = '';
                }
                onSetState({
                  skuCode: val,
                });
              }}
            />
          </Form.Item>
          <Form.Item label={t('barcode')}>
            <Input
              placeholder={t('enter_barcode')}
              value={barsCode}
              maxLength={20}
              readOnly={!checkPerm('M_001_002_001_002_001')}
              onClick={() => {
                testPerm('M_001_002_001_002_001');
              }}
              onChange={(e) => {
                let val = '';
                const { value } = e.target;
                const reg = /^[0-9a-zA-Z\s]+$/;
                if (reg.test(value)) {
                  val = value;
                } else if (value === '') {
                  val = '';
                }
                onSetState({
                  barsCode: val,
                });
              }}
            />
          </Form.Item>
          <Form.Item label={t('shelf_life')}>
            <NumericInput
              placeholder={t('specification_shelf_shuru')}
              positiveInteger
              value={expirationDate.value ? `${expirationDate.value}` : ''}
              suffix={<span className={styles.suffix}>{t('specification_shelf_shuru2')}</span>}
              maxLength={9}
              onChange={(value) => {
                onSetState({
                  expirationDate: {
                    value: value as unknown as number,
                    type: 1,
                  },
                });
              }}
            />
          </Form.Item>
          {/* <Form.Item label="支持单独购买">
            <Switch
              checked={!!isAloneBuy}
              onChange={(checked) => {
                onSetState({
                  isAloneBuy: Number(checked) as 0 | 1,
                });
              }}
            />
          </Form.Item> */}
        </Form>

        <SetPriceVariableDrawer
          type="single"
          categoryId={categoryId}
          unit={unit}
          visible={visible}
          closeDrawer={setVisible}
          formulaId={formulaId}
          formulaName={formulaName}
          formatFormula={formatFormula}
          formulaList={formulaList}
          standardList={[]}
          updateState={(val) => {
            onSetState(val);
          }}
          onDefine={onDefine}
        />

        <NewBuildTemplateDrawer
          isVisible={visibleTemplate}
          onSuccess={() => {
            setVisibleTemplate(false);
            onGetFreightOptions();
          }}
          closeDrawer={setVisibleTemplate}
        />
      </div>
    );
  }
);

SpecificationSingleComponent.displayName = 'SpecificationSingleComponent';

SpecificationSingleComponent.defaultProps = {};

export default SpecificationSingleComponent;
