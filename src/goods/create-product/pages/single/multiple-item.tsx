/* eslint-disable no-unused-vars */
import {
  Key,
  useCallback,
  useEffect,
  useMemo,
  useReducer,
  useState,
  useRef,
  forwardRef,
  useImperativeHandle,
  ReactNode,
} from 'react';
import uniq from 'lodash/uniq';
import {
  Popover,
  Spin,
  Checkbox,
  Tag,
  Table,
  Pagination,
  Switch,
  Select,
  Input,
  InputNumber,
  message,
  Tooltip,
  Divider,
  ConfigProvider,
} from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { FilterValue } from 'antd/lib/table/interface';
import {
  updatePmsProductUnitManager,
  updatePmsProductPriceManager,
  updatePmsProductExtensionManager,
  updatePmsAllProductUnitManager,
  updatePmsAllProductPriceManager,
  updatePmsAllProductExtensionManager,
  formulaConvert,
  getProductStandardList,
} from '@/apis';
import { TableRowType, VarListType, MultipleDataType } from '@/apis/pms/type';
import { ResultType as FreightOptionType } from '@/apis/base/get-freight-options';
import { ResultType as UnitFormulaOptionType } from '@/apis/base/get-unit-formula-options';
import type { GraphicDescDrawerPropsDataItem } from '@/components';
import { GraphicDescDrawer, Icon, NumericInput } from '@/components';
import { arrayObjectDeduplication } from '@/utils/utils';
import { checkPerm, testPerm } from '@/utils/permission';
import { useTranslation } from 'react-i18next';

import BatchUnitStatusDrawer from '../../components/batch-unit-status-drawer';
import BatchUnitMatrDrawer from '../../components/batch-unit-matr-drawer';
import BatchSaleUnitDrawer from '../../components/batch-sale-unit-drawer';
import BatchRetailPriceDrawer from '../../components/batch-retail-price-drawer';
import BatchCostPriceDrawer from '../../components/batch-cost-price-drawer';
import BatchFreightTemplateDrawer from '../../components/batch-freight-template-drawer';
import BatchValidTimeDrawer from '../../components/batch-valid-time-drawer';
import BatchUnitManagerDrawer from '../../components/batch-unit-manager-drawer';
import BatchPriceManagerDrawer from '../../components/batch-price-manager-drawer';
import BatchExtendManagerDrawer from '../../components/batch-extend-manager-drawer';
import SetVariableDrawer from '../../components/set-variable-drawer';
import SetPriceVariableDrawer from '../../components/set-price-variable-drawer';
// import { limitDecimalsF, limitDecimalsF$, limitDecimalsP } from '../../utils/input';

import styles from './multiple-item.module.less';

interface PropsType {
  title: string;
  stepIndex: number;
  filterList: string[];
  titleId: string;
  formulaId: number;
  formulaName: string;
  formatFormula: string;
  formulaList: VarListType[];
  total: number;
  allTotal: number;
  pageSize: number;
  pageNo: number;
  unit: string;
  unitNum: number;
  categoryId: number;
  productType: number;
  childSheetId: number;
  isValidate: boolean;
  list: TableRowType[];
  condition: Record<string, string[]>;
  freightOptions: FreightOptionType[];
  unitFormulaSelect: UnitFormulaOptionType[];
  multipleStandard: MultipleDataType;
  onSpecRemove: MultipleParamsFn<[values: number[], type: number]>;
  getList: MultipleParamsFn<[page: number, pageSize: number, filter: string[]]>;
  updateState: MultipleParamsFn<[values: TableRowType[], index: number]>;
  onAddUnitConversion: MultipleParamsFn<[]>;
  onAddFreightTemplate: MultipleParamsFn<[]>;
  onPageChange: MultipleParamsFn<[val: number, pageSize: number, filter: string[]]>;
}
interface StateType {
  hideColumnKey: string[];
  filters: Record<string, FilterValue | null>;
  dataSource: TableRowType[];
  columns: ColumnsType<TableRowType>;
  selectedRowKeys: Key[];
  selectedRows: TableRowType[];
  selectedRowKeysPageData: Record<number, Key[]>;
  selectedRowsPageData: Record<number, TableRowType[]>;
  selectedAll: boolean;
  unit: string; // 基本单位
  pageNo: number;
  pageSize: number;
  total: number;
  filterList: string[];
}
type ActionType = {
  type: 'setState';
  payload: Partial<StateType>;
};

export type MultipleItemInstance = {
  validateFields: () => TableRowType[];
  updateBaseUnit: MultipleParamsFn<[val: string]>;
  updatePrice: MultipleParamsFn<[marketPrice: number | null, formulaIdVal: number]>;
  removeSelectedRowKeys: MultipleParamsFn<[values: number[]]>;
  checkSelectAll: MultipleParamsFn<[values: number[]]>;
  updateSelectAll: MultipleParamsFn<[val: boolean]>;
  getPageInfo: () => { pageNo: number; total: number };
};

const initialState: StateType = {
  hideColumnKey: [],
  filters: {},
  dataSource: [],
  columns: [],
  selectedRowKeys: [],
  selectedRows: [],
  selectedRowKeysPageData: {},
  selectedRowsPageData: {},
  selectedAll: false,
  unit: '',
  pageNo: 1,
  pageSize: 10,
  total: 1,
  filterList: [],
};
const reducer = (state: StateType, action: ActionType): StateType => {
  switch (action.type) {
    case 'setState':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

const MultipleItem = forwardRef<MultipleItemInstance, PropsType>(
  (
    {
      title,
      stepIndex,
      titleId,
      formulaId,
      formulaName,
      formatFormula,
      formulaList,
      filterList,
      total,
      allTotal,
      pageSize,
      categoryId,
      productType,
      childSheetId,
      isValidate,
      unit,
      unitNum,
      freightOptions,
      unitFormulaSelect,
      multipleStandard,
      onSpecRemove,
      getList,
      updateState,
      onAddUnitConversion,
      onAddFreightTemplate,
      onPageChange,
      ...props
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [loading, setLoading] = useState(false);
    const [hideColumnKeyOptions, setHideColumnKeyOptions] = useState<string[]>([]);
    const [state, dispatch] = useReducer(reducer, initialState);
    const selectedCount = useMemo<number>(() => {
      if (state.filterList?.length) {
        return state.selectedAll ? state.total : state.selectedRowKeys.length;
      }
      return state.selectedAll ? allTotal : state.selectedRowKeys.length;
    }, [state.selectedRowKeys, state.selectedAll, allTotal, state.filterList, state.total]);
    // console.log('state', state);

    const [showSetVar, setShowSetVar] = useState(false);
    const [isMoreUnit, setIsMoreUnit] = useState(false);
    const [isUnitMatr, setIsUnitMatr] = useState(false);
    const [isMinSaleUnit, setIsMinSaleUnit] = useState(false);
    const [isRetailPrice, setIsRetailPrice] = useState(false);
    const [isCostPrice, setIsCostPrice] = useState(false);
    const [isFreightTemplate, setIsFreightTemplate] = useState(false);
    const [isValidTime, setIsValidTime] = useState(false);
    const [isUnitManager, setIsUnitManager] = useState(false);
    const [isPriceManager, setIsPriceManager] = useState(false);
    const [isExtendManager, setIsExtendManager] = useState(false);
    const [graphicDescText, setGraphicDescText] = useState(t('graphicDescText'));
    const [visible, setVisible] = useState(false);
    const [preview, setPreview] = useState(false);
    const [visibleSetPriceVar, setVisibleSetPriceVar] = useState(false);
    const [recordInfo, setRecordInfo] = useState<TableRowType>();
    const [graphicDescData, setGraphicDescData] = useState<GraphicDescDrawerPropsDataItem[]>([]);
    const batchGraphicDescData = useRef<GraphicDescDrawerPropsDataItem[]>([]);
    const [currUnitInfo, setCurrUnitInfo] = useState({} as UnitFormulaOptionType);
    const tableBoxRef = useRef<null | HTMLDivElement>(null);
    const tableRef = useRef<null | HTMLDivElement>(null);
    const [filteredData, setFilteredData] = useState<{ label: string; values: string[] }[]>([]);
    // const filteredObject = useRef<Record<string, string[]>>({});
    // const [saleUnitOptions, setSaleUnitOptions] = useState<{ label: string; value: string }[]>([]);
    // const [saleUnitData, setSaleUnitData] =
    //   useState<Record<number, { label: string; value: string }[]>>();
    // const [variableDefaultValues, setVariableDefaultValues] = useState<
    //   { id: number; value: string }[]
    // >([]);
    const standardList = useMemo(() => {
      const list: { name: string; value: string }[][] = [];
      multipleStandard.standardList.forEach((item) => {
        list.push(item.values?.map((valueItem) => ({ name: item.name, value: valueItem.value })));
      });
      return list;
    }, [multipleStandard.standardList]);

    const currentState = useRef(initialState);
    const currentRecord = useRef<TableRowType>();
    const currIndex = useRef(-1);
    // const errorList = useRef<TableRowType[]>([]);

    const validateFields = () =>
      // eslint-disable-next-line array-callback-return, consistent-return
      state.dataSource.filter((item) => {
        // let isError = false;
        if (item.hasUnitTemplate && !item.unitTemplateId) {
          message.error(t('error_noUnitTemplate'));
          return true;
        }
        if (item.saleGroup === null || item.saleGroup === 0) {
          message.error(
            t(item.saleGroup === null ? 'error_noMinSaleUnit' : 'error_minSaleUnitZero')
          );
          return true;
        }
        if (item.marketPrice === null) {
          message.error(t('error_noRetailPrice'));
          return true;
        }
      }) || [];

    const createDetailList = (values: GraphicDescDrawerPropsDataItem[]) =>
      values.map((item) => {
        let imgUrl = '';
        let text = '';
        let videoUrl = '';
        switch (item.type) {
          case 'image':
            imgUrl = item.value;
            break;
          case 'video':
            videoUrl = item.value;

            break;
          default:
            text = item.value;
            break;
        }
        return { ...item, imgUrl, text, videoUrl };
      });

    const createGraphicDescData = (values: TableRowType['detailList']) =>
      values.map((item) => {
        let type = '';
        let value = '';
        if (item.imgUrl) {
          type = 'image';
          value = item.imgUrl;
        } else if (item.videoUrl) {
          type = 'video';
          value = item.videoUrl;
        } else {
          type = 'text';
          value = item.text;
        }
        return { type, value };
      });

    // 设置单位管理
    const onUpdateUnitManager = (data: TableRowType[], batchTemplateButton?: null) => {
      const params = {
        ...(categoryId ? { categoryId } : {}),
        insertType: 1,
        list: data.map((item) => ({
          id: item.id,
          unit: item.unit,
          templateButton: batchTemplateButton === null ? null : Number(!item.hasUnitTemplate),
          // hasUnitTemplate: item.hasUnitTemplate,
          unitTemplateId: item.unitTemplateId || null,
          saleUnit: item.saleUnit ?? null,
          saleGroup: item.saleGroup ?? null,
          // ...(item.unitTemplateId ? { unitTemplateId: item.unitTemplateId } : {}),
          // ...(item.saleUnit ? { saleUnit: item.saleUnit } : {}),
          templateFormulaArgsList: item.templateFormulaNameList,
          // templateFormulaArgsList: item.templateFormulaNameList.length
          //   ? item.templateFormulaNameList
          //   : null,
          // ...(item.templateFormulaNameList?.length
          //   ? { templateFormulaNameList: item.templateFormulaNameList }
          //   : {}),
        })),
      };
      return new Promise((resolve, reject) => {
        updatePmsProductUnitManager(params)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    };

    // 全部全部单位管理
    const onUpdateAllUnitManager = (
      saleUnit: number | null | string,
      multiUnit: boolean,
      unitConversion: number | null,
      templateFormulaArgsList: VarListType[],
      updateKeys = ['saleUnit', 'multiUnit', 'unitConversion', 'var']
    ) => {
      const params = {
        ...(categoryId ? { categoryId } : {}),
        insertType: 1,
        condition: state.filterList,
        unit,
        ...(updateKeys.includes('saleUnit') ? { saleGroup: saleUnit } : {}),
        ...(updateKeys.includes('multiUnit') ? { templateButton: Number(!multiUnit) } : {}),
        ...(updateKeys.includes('unitConversion') ? { unitTemplateId: unitConversion } : {}),
        ...(updateKeys.includes('var')
          ? {
              templateFormulaArgsList: templateFormulaArgsList?.map((item) => ({
                id: String(item.id),
                value: item.value || '',
              })),
            }
          : {}),
        // ...(multiUnit
        //   ? { hasUnitTemplate: multiUnit, unitTemplateId: unitConversion }
        //   : { hasUnitTemplate: multiUnit, unitTemplateId: null }),
      };
      return new Promise((resolve, reject) => {
        updatePmsAllProductUnitManager(params)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    };

    // 设置全部价格管理
    const onUpdatePriceManager = (data: TableRowType[]) => {
      const params = {
        ...(categoryId ? { categoryId } : {}),
        insertType: 1,
        list: data.map((item) => ({
          id: item.id,
          marketPrice: item.marketPrice ?? null,
          costPrice: item.costPrice ?? null,
          freightTemplateId: item.freightTemplateId ?? null,
          isCustomizeMade: item.isCustomizeMade || 0,
          ...(item.formulaId ? { formulaId: item.formulaId } : {}),
          ...(item.formulaList ? { formulaList: item.formulaList } : {}),
        })),
      };
      return new Promise((resolve, reject) => {
        updatePmsProductPriceManager(params)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    };

    // 设置价格管理
    const onUpdateAllPriceManager = (
      marketPrice: number | null | string,
      costPrice: number | null | string,
      freightTemplateId: number | null,
      formulaListVal: VarListType[],
      updateKeys = ['marketPrice', 'costPrice', 'freightTemplateId', 'var']
    ) => {
      const params = {
        ...(categoryId ? { categoryId } : {}),
        insertType: 1,
        condition: state.filterList,
        ...(updateKeys.includes('marketPrice') ? { marketPrice: marketPrice ?? null } : {}),
        ...(updateKeys.includes('costPrice') ? { costPrice: costPrice ?? null } : {}),
        ...(updateKeys.includes('var') ? { formulaList: formulaListVal } : {}),
        ...(updateKeys.includes('freightTemplateId')
          ? { freightTemplateId: freightTemplateId || null }
          : {}),
        // marketPrice: marketPrice ?? null
        // costPrice: costPrice ?? null,
        // freightTemplateId: freightTemplateId || null,
      };
      return new Promise((resolve, reject) => {
        updatePmsAllProductPriceManager(params)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    };

    // 设置扩展管理
    const onUpdateExtensionManager = (data: TableRowType[]) => {
      const params = {
        ...(categoryId ? { categoryId } : {}),
        insertType: 1,
        list: data.map((item) => ({
          id: item.id,
          skuCode: item.skuCode ?? null,
          barsCode: item.barsCode ?? null,
          expirationDate: item.expirationDate,
          detailParams: item.detailList,
        })),
      };
      return new Promise((resolve, reject) => {
        updatePmsProductExtensionManager(params)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    };

    // 设置全部扩展管理
    const onUpdateAllExtensionManager = (
      expirationDate: number | null,
      detailList: GraphicDescDrawerPropsDataItem[],
      updateKeys = ['expirationDate']
    ) => {
      const params = {
        ...(categoryId ? { categoryId } : {}),
        insertType: 1,
        condition: state.filterList,
        ...(updateKeys.includes('expirationDate')
          ? { expirationDate: { type: 1, value: expirationDate } }
          : {}),
        ...(detailList.length ? { detailParams: createDetailList(detailList) } : {}),
      };
      return new Promise((resolve, reject) => {
        updatePmsAllProductExtensionManager(params)
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            reject(err);
          });
      });
    };

    const onUpdateManagerFn = (
      type: string,
      params: TableRowType[],
      dataSource?: TableRowType[],
      onSuccess?: MultipleParamsFn<[]>
    ) => {
      let fn: (data: TableRowType[]) => Promise<unknown>;
      switch (type) {
        case 'unit':
          fn = onUpdateUnitManager;
          break;
        case 'price':
          fn = onUpdatePriceManager;
          break;
        case 'extension':
          fn = onUpdateExtensionManager;
          break;

        default:
          fn = onUpdateUnitManager;
          break;
      }
      if (fn) {
        fn(params)
          .then(() => {
            getList(state.pageNo, state.pageSize, state.filterList);
            onSuccess?.();
          })
          .catch(() => {
            getList(state.pageNo, state.pageSize, state.filterList);
          });
      }
    };

    const onBlurUpdateManager = (type: string, list?: TableRowType[]) => {
      // const data = currentState.current.dataSource;
      let data: TableRowType[] = [];
      if (list?.length) {
        data = list;
      } else if (currentRecord.current) {
        data = [currentRecord.current];
      }
      // const data = list ? list : currentRecord.current ? [currentRecord.current] : [];
      switch (type) {
        case 'unit':
          onUpdateUnitManager(data).catch(() => {
            getList(state.pageNo, state.pageSize, state.filterList);
          });
          break;
        case 'price':
          onUpdatePriceManager(data).catch(() => {
            getList(state.pageNo, state.pageSize, state.filterList);
          });
          break;
        case 'extension':
          onUpdateExtensionManager(data).catch(() => {
            getList(state.pageNo, state.pageSize, state.filterList);
          });
          break;

        default:
          break;
      }
    };

    // 显示已隐藏列
    const onShowColumn = useCallback(
      (key: string) => {
        dispatch({
          type: 'setState',
          payload: { hideColumnKey: state.hideColumnKey.filter((columnKey) => columnKey !== key) },
        });
      },
      [state.hideColumnKey]
    );

    const getAllGoodsList = (filter: string[] = []) => {
      const promise = new Promise((resolve: (values: TableRowType[]) => void, reject) => {
        getProductStandardList({
          type: 0,
          productSource: productType,
          categoryId,
          childSheetId: childSheetId || null,
          condition: filter,
          stepIndex: 1,
          pageSize: 999999,
          pageNo: 1,
          insertType: 1,
        })
          .then((res) => {
            const list = res.list?.map((item) => ({
              ...item,
              marketPrice: item.marketPriceStr,
              saleGroup: item.saleGroupStr,
            }));
            resolve(list);
          })
          .catch(() => {
            reject();
          });
      });
      return promise;
    };

    // 全选
    const selectAll = (value: boolean) => {
      let selectedRowKeys: Key[] = [];
      if (value) {
        // selectedRowKeys = uniq(
        //   state.selectedRowKeys.concat(state.dataSource.map((dataItem) => dataItem.id) as Key[])
        // );
        getAllGoodsList(filterList)
          .then((arr) => {
            const ids = arr.map((item) => item.id);
            selectedRowKeys = ids;
            dispatch({
              type: 'setState',
              payload: {
                selectedAll: value,
                selectedRowKeys,
              },
            });
          })
          .catch(() => {
            dispatch({
              type: 'setState',
              payload: {
                selectedAll: false,
                selectedRowKeys: [],
              },
            });
          });
      } else {
        dispatch({
          type: 'setState',
          payload: {
            selectedAll: value,
            selectedRowKeys,
          },
        });
      }
    };

    // 删除某一列
    const onRowDelete = useCallback(
      (record: TableRowType) => {
        onSpecRemove([record.id], 1);
      },
      [onSpecRemove]
    );

    const onHeaderBatchEdit = useCallback(
      (value: string) => {
        if (!testPerm('M_001_001_001_006')) {
          return value;
        }
        switch (value) {
          case 'moreUnit':
            return setIsMoreUnit(true);
          case 'unitMatr':
            return setIsUnitMatr(true);
          case 'minSaleUnit':
            return setIsMinSaleUnit(true);
          case 'retailPrice':
            return setIsRetailPrice(true);
          case 'costPrice':
            return setIsCostPrice(true);
          case 'freightTemplate':
            return setIsFreightTemplate(true);
          case 'validTime':
            return setIsValidTime(true);
          case 'graphicDesc':
            setGraphicDescText(t('batchSetGraphicDesc'));
            setGraphicDescData([]);
            // setGraphicDescData(batchGraphicDescData.current);
            setPreview(false);
            return setVisible(true);
          default:
            return value;
        }
      },
      [t]
    );

    const onBatchSet = (value: string) => {
      switch (value) {
        case '单位管理':
          return setIsUnitManager(true);
        case '价格管理':
          return setIsPriceManager(true);
        case '扩展信息':
          return setIsExtendManager(true);
        default:
          return value;
      }
    };

    // 多规格-多单位状态/单位换算/最小销售单元/零售价(元)/成本价(元)/运费模版/条形码/保质期(天)
    const onInputValue = (record: TableRowType, key: string, value: any) => {
      window.console.log(record, key, value);
      // const maxPrice = 99999998;
      const item = record;
      // const oldDataSource = [...state.dataSource];
      if (key === 'hasUnitTemplate') {
        item.hasUnitTemplate = value;
        item.unitTemplateId = 0;
        item.templateFormulaNameList = [];
      } else if (key === 'saleGroup') {
        // if (value !== null) {
        item.saleGroup = value;
        // }
      } else if (key === 'unitTemplateId') {
        item.unitTemplateId = value;
      } else if (key === 'marketPrice') {
        // if (value !== null) {
        // item.marketPrice = Number(value || 0) > maxPrice ? maxPrice : value;
        item.marketPrice = value;
        // }
        // item.marketPrice = value;
      } else if (key === 'costPrice') {
        // if (value !== null) {
        item.costPrice = value;
        // }
        // item.costPrice = value;
      } else if (key === 'freightTemplateId') {
        item.freightTemplateId = value;
      } else if (key === 'skuCode') {
        item.skuCode = value;
      } else if (key === 'barsCode') {
        const reg = /^[0-9a-zA-Z\s]+$/;
        if (reg.test(value)) {
          item.barsCode = value;
        } else {
          item.barsCode = '';
        }
      } else if (key === 'expirationDate') {
        item.expirationDate.type = 1;
        item.expirationDate.value = value;
      } else if (key === 'detailList') {
        item.detailList = value;
      }
      currentRecord.current = item;
      const unitManagerUpdateKeys = ['unitTemplateId'];
      const priceManagerUpdateKeys = ['freightTemplateId'];
      if (key === 'hasUnitTemplate') {
        if (item.hasUnitTemplate && item.unitTemplateId) {
          onUpdateUnitManager([item]).catch(() => {
            getList(state.pageNo, state.pageSize, state.filterList);
          });
        } else if (!item.hasUnitTemplate) {
          onUpdateUnitManager([item]).catch(() => {
            getList(state.pageNo, state.pageSize, state.filterList);
          });
        }
      }
      if (unitManagerUpdateKeys.includes(key)) {
        // onUpdateUnitManager(state.dataSource).catch(() => {

        onUpdateUnitManager([item]).catch(() => {
          getList(state.pageNo, state.pageSize, state.filterList);
        });
      }

      if (priceManagerUpdateKeys.includes(key)) {
        // onUpdatePriceManager(state.dataSource).catch(() => {
        onUpdatePriceManager([item]).catch(() => {
          // state.dataSource = oldDataSource;
        });
      }

      currentState.current = state;

      dispatch({
        type: 'setState',
        payload: { dataSource: [...state.dataSource] },
      });
      updateState(state.dataSource, stepIndex);
    };

    const addImageText = (record: TableRowType, key: string, value: any) => {
      const item = record;
      if (key === 'detailList') {
        item.detailList = value;
      }
      onUpdateManagerFn('extension', [item], state.dataSource);
    };

    const getUnitOption = (unitTemplateIdVal: number) =>
      new Promise((resolve: (values: { label: string; value: string }[]) => void, reject) => {
        formulaConvert(unitTemplateIdVal)
          .then((res) => {
            const list =
              res.detailList?.map((item) => ({
                label: item.unitName || '',
                value: item.unitName || '',
              })) || [];
            resolve(list);
          })
          .catch(() => {
            reject();
          });
      });

    // 批量删除
    const onBatchRemove = () => {
      onSpecRemove(state.selectedRowKeys as number[], state.selectedAll ? 3 : 2);
    };

    const columns = useMemo<ColumnsType<TableRowType>>(
      () =>
        state.columns
          .filter((column) => !state.hideColumnKey.includes(column.key as string))
          .map((column) => {
            const nowColumn = column;

            if (column.filters) {
              nowColumn.filterIcon = (
                <Icon
                  name="filter"
                  size={16}
                  color={column.key && column.filteredValue?.length ? '#008CFF' : '#999EB2'}
                />
              );
              // nowColumn.onFilter = (value, record) => {
              //   // getList(state.pageNo, state.pageSize);
              //   console.log('onFilter', value);
              //   // console.log('record', record);

              //   // @ts-ignore
              //   return record[column.dataIndex].indexOf(value as string) === 0;
              //   // return true;
              // };
            }

            switch (column.key) {
              case 'hasUnitTemplate':
                nowColumn.render = (_, record) => (
                  <Switch
                    checked={record.hasUnitTemplate}
                    onChange={(e) => {
                      onInputValue(
                        record,
                        'hasUnitTemplate',
                        testPerm('M_001_002_001_003') ? e : !e
                      );
                    }}
                  />
                );
                break;
              case 'unitTemplateId':
                nowColumn.render = (_, record, index) => {
                  if (record.hasUnitTemplate) {
                    const isHasVar = record?.templateFormulaNameList?.length || false;
                    const isSetVar =
                      record?.templateFormulaNameList?.filter(
                        (item) => item.value === null || item.value === ''
                      ).length === 0;
                    const varText = isSetVar
                      ? record?.templateFormulaNameList
                          ?.map((formItem) => `${formItem.name || ''}:${formItem.value}`)
                          .join(',')
                      : '';
                    let varElement: ReactNode | null = null;
                    const createVarContent = (
                      formItem: VarListType,
                      formIndex: number,
                      data: VarListType[]
                    ) => {
                      let node: ReactNode | null = null;
                      if (data.length < 3) {
                        node = (
                          <div>
                            {formItem.name}:{formItem.value}
                            {formIndex + 1 === data.length ? <Icon name="right" size={12} /> : ','}
                          </div>
                        );
                      } else {
                        node = (
                          <div>
                            {formItem.name}:{formItem.value}
                            {formIndex + 1 === 3 ? (
                              <span>
                                ... <Icon name="right" size={12} />
                              </span>
                            ) : (
                              ','
                            )}
                          </div>
                        );
                      }
                      return node;
                    };

                    if (isHasVar) {
                      if (varText) {
                        varElement = (
                          <div className={styles.setMutUnitVar}>
                            <Tooltip placement="top" title={varText}>
                              <div
                                style={{ textAlign: 'left', width: '82px', wordBreak: 'break-all' }}
                              >
                                {record?.templateFormulaNameList?.map((formItem, formIndex) =>
                                  formIndex < 3
                                    ? createVarContent(
                                        formItem,
                                        formIndex,
                                        record?.templateFormulaNameList
                                      )
                                    : null
                                )}
                              </div>
                            </Tooltip>
                          </div>
                        );
                      } else {
                        varElement = (
                          <div className={styles.setMutUnitVarErr}>
                            {t('enter_variable_value')}
                            <Icon name="right" size={12} />
                          </div>
                        );
                      }
                    }
                    return (
                      <div className={styles.bigSelect}>
                        <Select
                          value={record.unitTemplateId || null}
                          options={unitFormulaSelect?.map((item) => ({ ...item }))}
                          placeholder={t('select_conversion_relation')}
                          fieldNames={{
                            label: 'templateName',
                            value: 'id',
                          }}
                          style={{ width: '80%', textAlign: 'left' }}
                          onFocus={() => {
                            testPerm('L_001_004_001_001');
                            currentState.current = { ...state };
                          }}
                          onChange={(val, option: any) => {
                            currIndex.current = index;
                            const newRecord = record;
                            newRecord.saleUnit = record.unit;
                            if (option?.varList?.length) {
                              newRecord.hasUnitTemplateVar = true;
                              newRecord.templateFormulaNameList = option?.varList?.map(
                                (item: any) => ({
                                  id: item.id,
                                  name: item.name,
                                  value: '',
                                })
                              );
                              setCurrUnitInfo({ ...option });
                              setRecordInfo({ ...record });
                              setTimeout(() => {
                                setShowSetVar(true);
                              }, 300);
                            } else {
                              newRecord.hasUnitTemplateVar = false;
                              newRecord.templateFormulaNameList = [];
                            }
                            if (val) {
                              getUnitOption(val).then((list) => {
                                newRecord.saleUnitOptions = [...list];

                                dispatch({
                                  type: 'setState',
                                  payload: {
                                    dataSource: state.dataSource || [],
                                  },
                                });
                              });
                            }

                            onInputValue(record, 'unitTemplateId', val);
                          }}
                          status={!record.unitTemplateId ? 'error' : ''}
                          getPopupContainer={(triggerNode) => triggerNode.parentNode}
                          // eslint-disable-next-line react/no-unstable-nested-components
                          dropdownRender={(menu) => (
                            <div className={styles.unitTemplateBox}>
                              {menu}
                              <Divider style={{ margin: '8px 0' }} />
                              <div style={{ padding: '0 8px 4px', width: '100%' }}>
                                <div
                                  className={styles.bigSelectAddBtn}
                                  role="presentation"
                                  onClick={() => {
                                    if (!testPerm('L_001_004_002')) {
                                      return;
                                    }
                                    onAddUnitConversion();
                                  }}
                                >
                                  <Icon name="plus" />
                                  {t('add_conversion_relation')}
                                </div>
                              </div>
                            </div>
                          )}
                        />
                        {record?.templateFormulaNameList?.length ? (
                          <div
                            role="presentation"
                            onClick={() => {
                              if (unitFormulaSelect.length && record.unitTemplateId) {
                                const currUnitItemArr = unitFormulaSelect.filter(
                                  (item) => item.id === record.unitTemplateId
                                );
                                if (currUnitItemArr.length) {
                                  setCurrUnitInfo(currUnitItemArr[0]);
                                }
                              }
                              currIndex.current = index;
                              setRecordInfo({ ...record });
                              setTimeout(() => {
                                setShowSetVar(true);
                              }, 300);
                            }}
                          >
                            {/* 请设置换算变量值 */}
                            {varElement}
                          </div>
                        ) : null}
                      </div>
                    );
                  }

                  return `基本单位：${record.unit}`;
                };
                break;
              case 'saleGroup':
                nowColumn.render = (_, record) => (
                  // <div className={styles.costPriceInputBox}>
                  <div className={record.unitTemplateId ? '' : styles.costPriceInputBox}>
                    <div style={{ height: '92px', lineHeight: '92px' }}>
                      <NumericInput
                        placeholder={t('enter_min_sale_unit')}
                        precision={unitNum}
                        value={record.saleGroup ? `${record.saleGroup}` : ''}
                        // prefix={record.saleGroup ? <span>￥</span> : <span />}
                        // suffix={<span className={styles.suffix}>{unit}</span>}
                        max={9999999999}
                        status={!record.saleGroup ? 'error' : ''}
                        // disabled={Boolean(record.formulaId)}
                        style={{
                          width: record.unitTemplateId ? '40%' : '90%',
                          height: '32px',
                        }}
                        title={`${record.saleGroup ?? ''}`}
                        maxLength={10 + (unitNum ? unitNum + 1 : 0)}
                        onFocus={() => {
                          currentState.current = { ...state };
                        }}
                        onBlur={() => {
                          const newRecord = record;
                          newRecord.saleGroup = record.saleGroup
                            ? Number(record.saleGroup).toFixed(unitNum)
                            : '';
                          onBlurUpdateManager('unit');
                        }}
                        onChange={(val) => {
                          // const value = limitDecimalsP(val, record.priceNum, 8);
                          onInputValue(record, 'saleGroup', val);
                        }}
                        readOnly={!checkPerm('M_001_002_001_005')}
                        onClick={() => {
                          testPerm('M_001_002_001_005');
                        }}
                      />
                      {record.unitTemplateId ? (
                        <Select
                          value={record.saleUnit || record.unit}
                          options={record.saleUnitOptions}
                          style={{
                            width: '60%',
                            height: '32px',
                          }}
                          // bordered={tr}
                          // className={styles.saleUnitSelect}
                          // loading
                          onFocus={() => {
                            if (record?.unitTemplateId) {
                              getUnitOption(record?.unitTemplateId).then((list) => {
                                const newRecord = record;
                                newRecord.saleUnitOptions = [...list];

                                dispatch({
                                  type: 'setState',
                                  payload: {
                                    dataSource: state.dataSource || [],
                                  },
                                });
                              });
                            }
                          }}
                          onChange={(val) => {
                            const newRecord = record;
                            newRecord.saleUnit = val;
                            onUpdateUnitManager([newRecord])
                              .then(() => {
                                updateState(state.dataSource, stepIndex);
                              })
                              .catch(() => {
                                getList(state.pageNo, state.pageSize, state.filterList);
                              });
                            // onUpdateManagerFn('unit', [newRecord], state.dataSource);
                            // setSaleUnitVal(val);
                            // onSetState({ saleUnit: val });
                          }}
                        />
                      ) : null}
                    </div>
                    {!record.unitTemplateId && (
                      <div className={styles.costPriceUnit} title={record.unit}>
                        {record.unit}
                      </div>
                    )}
                  </div>
                );
                break;
              case 'marketPrice':
                nowColumn.render = (_, record, index) => {
                  const isHasVar = record?.formulaList?.length || false;
                  const isSetVar =
                    record?.formulaList?.filter((item) => item.value === null || item.value === '')
                      .length === 0;
                  const varText = isSetVar
                    ? record?.formulaList
                        ?.map((formItem) => `${formItem.name || ''}:${formItem.value || ''}`)
                        .join(',')
                    : '';
                  let varElement: ReactNode | null = null;
                  const createVarContent = (
                    formItem: VarListType,
                    formIndex: number,
                    data: VarListType[]
                  ) => {
                    let node: ReactNode | null = null;
                    if (data.length < 3) {
                      node = (
                        <div>
                          {formItem.name}:{formItem.value}
                          {formIndex + 1 === data.length ? <Icon name="right" size={12} /> : ','}
                        </div>
                      );
                    } else {
                      node = (
                        <div>
                          {formItem.name}:{formItem.value}
                          {formIndex + 1 === 3 ? (
                            <span>
                              ... <Icon name="right" size={12} />
                            </span>
                          ) : (
                            ','
                          )}
                        </div>
                      );
                    }
                    return node;
                  };
                  if (isHasVar && formulaId) {
                    if (varText) {
                      varElement = (
                        <div className={styles.setMutUnitVar}>
                          <Tooltip placement="top" title={varText}>
                            <div style={{ textAlign: 'left', width: '82px' }}>
                              {record?.formulaList?.map((formItem, formIndex) =>
                                formIndex < 3
                                  ? createVarContent(formItem, formIndex, record?.formulaList)
                                  : null
                              )}
                            </div>
                          </Tooltip>
                        </div>
                      );
                    } else {
                      varElement = (
                        <div className={styles.setMutUnitVarErr}>
                          {t('enter_variable_value')}
                          <Icon name="right" size={12} />
                        </div>
                      );
                    }
                  }
                  return (
                    <div style={{ marginTop: varElement ? 25 : 0 }}>
                      <NumericInput
                        placeholder={t('enter_retail_price')}
                        precision={record.priceNum}
                        value={record.marketPrice ? `${record.marketPrice}` : ''}
                        prefix={record.marketPrice ? <span>￥</span> : <span />}
                        // suffix={<span className={styles.suffix}>{unit}</span>}
                        max={9999999999}
                        className={
                          Number(record?.marketPrice || 0) <= 9999999999
                            ? styles.marketPrice
                            : styles.marketPriceError
                        }
                        status={
                          !record.marketPrice || Number(record.marketPrice) > 9999999999
                            ? 'error'
                            : ''
                        }
                        disabled={Boolean(record.formulaId)}
                        style={{
                          width: '80%',
                        }}
                        title={`${record.marketPrice ?? ''}`}
                        maxLength={10 + (record.priceNum ? record.priceNum + 1 : 0)}
                        onFocus={() => {
                          currentState.current = { ...state };
                        }}
                        onBlur={() => {
                          const newRecord = record;
                          newRecord.marketPrice = record.marketPrice
                            ? Number(record.marketPrice).toFixed(record.priceNum)
                            : '';
                          onBlurUpdateManager('price');
                        }}
                        onChange={(val) => {
                          // const value = limitDecimalsP(val, record.priceNum, 8);
                          onInputValue(record, 'marketPrice', val);
                        }}
                        readOnly={!checkPerm('M_001_002_001_002_002')}
                        onClick={() => {
                          testPerm('M_001_002_001_002_002');
                        }}
                      />
                      <div
                        role="presentation"
                        onClick={() => {
                          currIndex.current = index;
                          setVisibleSetPriceVar(true);
                          setRecordInfo({ ...record });
                        }}
                      >
                        {varElement}
                      </div>
                    </div>
                  );
                };
                break;
              case 'costPrice':
                nowColumn.render = (_, record) => (
                  <div className={styles.costPriceBox}>
                    <NumericInput
                      placeholder={t('enter_initial_cost')}
                      precision={record.priceNum}
                      value={record.costPrice ? `${record.costPrice}` : ''}
                      prefix={record.costPrice ? <span>{t('public_moneySymbol')}</span> : <span />}
                      // suffix={<span className={styles.suffix}>{unit}</span>}
                      max={9999999999}
                      className={
                        Number(record?.marketPrice || 0) <= 9999999999
                          ? styles.marketPrice
                          : styles.marketPriceError
                      }
                      style={{
                        width: '60%',
                        height: '32px',
                      }}
                      title={`${record.costPrice ?? ''}`}
                      maxLength={10 + (record.priceNum ? record.priceNum + 1 : 0)}
                      onFocus={() => {
                        currentState.current = { ...state };
                      }}
                      onBlur={() => {
                        const newRecord = record;
                        newRecord.costPrice = record.costPrice
                          ? Number(record.costPrice).toFixed(record.priceNum)
                          : '';
                        onBlurUpdateManager('price');
                      }}
                      onChange={(val) => {
                        // const value = limitDecimalsP(val, record.priceNum, 8);
                        onInputValue(record, 'costPrice', val);
                      }}
                      readOnly={!checkPerm('M_001_002_001_002_002')}
                      onClick={() => {
                        testPerm('M_001_002_001_002_002');
                      }}
                    />
                    <div
                      className={styles.costPriceUnit}
                      title={record.unit}
                      style={{ marginLeft: '5px' }}
                    >
                      {record.unit}
                    </div>
                  </div>
                );
                break;
              case 'freightTemplateId':
                nowColumn.render = (_, record) => (
                  <div className={styles.bigSelect}>
                    <Select
                      value={record.freightTemplateId || null}
                      style={{ width: '90%', textAlign: 'left' }}
                      placeholder={t('select_freight_template')}
                      options={freightOptions}
                      fieldNames={{
                        label: 'name',
                        value: 'id',
                      }}
                      onFocus={() => {
                        testPerm('L_001_002_001_001');
                      }}
                      onChange={(e) => {
                        if (!testPerm('L_001_002_001_001')) {
                          const newRecord = record;
                          newRecord.freightTemplateId = record.freightTemplateId;

                          dispatch({
                            type: 'setState',
                            payload: {
                              dataSource: [...state.dataSource],
                            },
                          });
                          return;
                        }
                        onInputValue(record, 'freightTemplateId', e);
                      }}
                      // status={!record.freightTemplateId ? 'error' : ''}
                      getPopupContainer={(triggerNode) => triggerNode.parentNode}
                      // eslint-disable-next-line react/no-unstable-nested-components
                      dropdownRender={(menu) => (
                        <div className={styles.templateBox}>
                          {menu}
                          <Divider style={{ margin: '8px 0' }} />
                          <div style={{ padding: '0 8px 4px', width: '100%' }}>
                            <div
                              className={styles.bigSelectAddBtn}
                              role="presentation"
                              onClick={() => {
                                if (testPerm('L_001_002_002')) {
                                  onAddFreightTemplate();
                                }
                              }}
                            >
                              <Icon name="plus" />
                              {t('add_freight_template')}
                            </div>
                          </div>
                        </div>
                      )}
                    />
                  </div>
                );
                break;
              case 'skuCode':
                nowColumn.render = (_, record) => (
                  <Input
                    value={record.skuCode ? record.skuCode : ''}
                    style={{ width: '90%' }}
                    placeholder={t('enter_product_code')}
                    maxLength={20}
                    onChange={(e) => {
                      let val = '';
                      const { value } = e.target;
                      const reg = /^[^\u4e00-\u9fa5]*$/;
                      if (reg.test(value)) {
                        val = value;
                      } else if (value === '') {
                        val = '';
                      }
                      onInputValue(record, 'skuCode', val);
                    }}
                    onFocus={() => {
                      currentState.current = { ...state };
                    }}
                    onBlur={() => {
                      onBlurUpdateManager('extension');
                    }}
                    // readOnly={!checkPerm('M_001_002_001_002_002')}
                    // onClick={() => {
                    //   testPerm('M_001_002_001_002_002');
                    // }}
                  />
                );
                break;
              case 'barsCode':
                nowColumn.render = (_, record) => (
                  <Input
                    value={record.barsCode}
                    style={{ width: '90%' }}
                    placeholder={t('enter_barcode')}
                    maxLength={20}
                    onChange={(e) => {
                      onInputValue(record, 'barsCode', e.target.value);
                    }}
                    onFocus={() => {
                      currentState.current = { ...state };
                    }}
                    onBlur={() => {
                      onBlurUpdateManager('extension');
                    }}
                    readOnly={!checkPerm('M_001_002_001_002_002')}
                    onClick={() => {
                      testPerm('M_001_002_001_002_002');
                    }}
                  />
                );
                break;
              case 'expirationDate':
                nowColumn.render = (_, record) => (
                  <InputNumber
                    value={record.expirationDate.value}
                    style={{ width: '90%' }}
                    precision={0}
                    placeholder={t('enter_days_placeholder')}
                    maxLength={9}
                    onChange={(val) => {
                      onInputValue(record, 'expirationDate', val);
                    }}
                    onFocus={() => {
                      currentState.current = { ...state };
                    }}
                    onBlur={() => {
                      onBlurUpdateManager('extension');
                    }}
                    min={1}
                    // status={}
                  />
                );
                break;
              case 'detailList':
                nowColumn.render = (_, record) => {
                  if (record.detailList.length) {
                    return (
                      <span
                        className={styles.lookImageText}
                        tabIndex={-1}
                        role="button"
                        onClick={() => {
                          setGraphicDescData(createGraphicDescData(record.detailList));
                          setRecordInfo({ ...record });
                          setGraphicDescText(t('graphicDescText'));
                          setVisible(true);
                          setPreview(true);
                        }}
                      >
                        {t('view_graphic')}
                      </span>
                    );
                  }
                  return (
                    <span
                      className={styles.addImageText}
                      tabIndex={-1}
                      role="button"
                      onClick={() => {
                        setGraphicDescData(createGraphicDescData(record.detailList));
                        setRecordInfo({ ...record });
                        setGraphicDescText(t('graphicDescText'));
                        setPreview(false);
                        setVisible(true);
                      }}
                    >
                      {t('add_graphic')}
                    </span>
                  );
                };
                break;
              case 'id':
                break;
              default:
                nowColumn.render = (text: string) => <div>{text || '--'}</div>;
            }
            return column;
          }),
      [state] // eslint-disable-line
    );

    const initColumns = useCallback(
      (data: Record<string, string[]>): ColumnsType<TableRowType> => {
        const filteredObj: Record<string, string[]> = {};
        filterList?.forEach((item) => {
          if (item) {
            const valueList = item.split('=');
            const key = valueList[0];
            const value = valueList[1];
            if (filteredObj?.[key]?.length) {
              filteredObj[key].push(value);
            } else {
              filteredObj[key] = [value];
            }
          }
        });
        const arr = Object.keys(data)
          .filter((name) => name !== 'name')
          .map((name) => ({
            title: name,
            dataIndex: name,
            key: name,
            align: 'center',
            ellipsis: true,
            filteredValue: Object.keys(filteredObj).length ? filteredObj[name] : [],
            filters: data[name].map((value) => ({ text: value, value })),
            width: '130px',
            // data[name].length > 1
            //   ?
            //   : undefined,
          }));

        const arr1 = [
          {
            title: (
              <>
                {t('base_multiUnitStatus2')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    window.console.log(data, 'data');
                    onHeaderBatchEdit('moreUnit');
                  }}
                />
              </>
            ),
            dataIndex: 'hasUnitTemplate',
            key: 'hasUnitTemplate',
            align: 'center',
            width: '90px',
          },
          {
            title: (
              <>
                {t('base_unitConversion')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    onHeaderBatchEdit('unitMatr');
                  }}
                />
              </>
            ),
            dataIndex: 'unitTemplateId',
            key: 'unitTemplateId',
            align: 'center',
          },
          {
            title: (
              <>
                <span className={styles.required}>*</span>
                {t('base_minSaleUnit2')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    onHeaderBatchEdit('minSaleUnit');
                  }}
                />
              </>
            ),
            dataIndex: 'saleGroup',
            key: 'saleGroup',
            align: 'center',
          },
        ];

        const arr2 = [
          {
            title: (
              <>
                <span className={styles.required}>*</span>
                {t('base_retailPrice')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    onHeaderBatchEdit('retailPrice');
                  }}
                />
              </>
            ),
            dataIndex: 'marketPrice',
            key: 'marketPrice',
            align: 'center',
          },
          {
            title: (
              <>
                {t('base_initialCost')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    onHeaderBatchEdit('costPrice');
                  }}
                />
              </>
            ),
            dataIndex: 'costPrice',
            key: 'costPrice',
            align: 'center',
          },
          {
            title: (
              <>
                {t('base_freightTemplate')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    onHeaderBatchEdit('freightTemplate');
                  }}
                />
              </>
            ),
            dataIndex: 'freightTemplateId',
            key: 'freightTemplateId',
            align: 'center',
          },
        ];

        const arr3 = [
          {
            title: t('base_productCode'),
            dataIndex: 'skuCode',
            key: 'skuCode',
            align: 'center',
          },
          {
            title: t('base_barcode'),
            dataIndex: 'barsCode',
            key: 'barsCode',
            align: 'center',
          },
          {
            title: (
              <>
                {t('base_shelfLife')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    onHeaderBatchEdit('validTime');
                  }}
                />
              </>
            ),
            dataIndex: 'expirationDate',
            key: 'expirationDate',
            align: 'center',
          },
          {
            title: (
              <>
                {t('base_graphicDesc')}
                <Icon
                  className={styles.editIcon}
                  name="edit1"
                  onClick={() => {
                    onHeaderBatchEdit('graphicDesc');
                  }}
                />
              </>
            ),
            dataIndex: 'detailList',
            key: 'detailList',
            align: 'center',
          },
        ];

        const handle = [
          {
            title: t('base_operation'),
            dataIndex: 'id',
            key: 'id',
            align: 'center',
            render: (_, record) => (
              <span
                className={styles.moveText}
                role="button"
                tabIndex={0}
                onClick={() => {
                  if (!testPerm('M_001_002_001_002_002_003')) {
                    return;
                  }
                  onRowDelete(record);
                }}
              >
                {t('base_remove')}
              </span>
            ),
            width: '80px',
          },
        ] as ColumnsType<TableRowType>;

        if (title === t('unit_management')) {
          return [...arr, ...arr1, ...handle] as ColumnsType<TableRowType>;
        }

        if (title === t('price_management')) {
          return [...arr, ...arr2, ...handle] as ColumnsType<TableRowType>;
        }

        if (title === t('extension_info')) {
          return [...arr, ...arr3, ...handle] as ColumnsType<TableRowType>;
        }

        return [];
      },
      [onRowDelete, onHeaderBatchEdit, title, filterList, t]
    );

    const initDataSource = useCallback(
      (data: TableRowType[]): TableRowType[] =>
        data.map((item) => {
          const nowItem = item;
          nowItem.hasUnitTemplateVar = false;
          nowItem.key = item.id;
          if (!nowItem.formulaId) nowItem.formulaId = null;
          item.standardList.forEach(({ name, value }) => {
            nowItem[name] = value;
          });
          return nowItem;
        }),
      []
    );

    const checkSelectedAll = (dataSource: TableRowType[]) => {
      if (state.selectedAll) {
        const ids = dataSource.map((item) => item.id);
        const { selectedRowKeys, selectedRows } = state;
        const newSelectedRowKeys = Array.from(new Set([...selectedRowKeys, ...ids]));
        const newSelectedRows = [
          ...dataSource,
          ...selectedRows.filter((item) => !ids.includes(item.id)),
        ];
        dispatch({
          type: 'setState',
          payload: {
            selectedRowKeys: newSelectedRowKeys,
            selectedRows: newSelectedRows,
          },
        });
      }
    };

    useEffect(() => {
      if (props?.list) {
        const dataSource = initDataSource(props.list);
        dispatch({
          type: 'setState',
          payload: {
            columns: initColumns(props.condition),
            dataSource,
            unit,
          },
        });
        if (dataSource.length) {
          checkSelectedAll(dataSource);
        }
        setTimeout(() => {
          const selectElementList = document.querySelectorAll(
            `.${styles.table} th.ant-table-selection-column .ant-checkbox-input`
          );
          if (selectElementList?.length) {
            selectElementList?.forEach((item) => {
              item.setAttribute('title', t('multipleItem_selectCurrentPage'));
            });
          }
        }, 500);
        // console.log('tableRef', tableRef);
      }
      if (props?.condition) {
        setHideColumnKeyOptions(
          Object.keys(props.condition)
            .filter((name) => name !== 'name')
            .map((name) => name)
        );
      }
      // console.log('useEffect', initColumns(props.condition));
      // console.log(' props.condition', props.condition);

      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props?.list, props?.condition, props.pageNo]);

    useEffect(() => {
      dispatch({
        type: 'setState',
        payload: {
          pageNo: props.pageNo,
          pageSize,
          total,
          filterList,
        },
      });
      const filteredObj: Record<string, string[]> = {};
      filterList?.forEach((item) => {
        if (item) {
          const valueList = item.split('=');
          const key = valueList[0];
          const value = valueList[1];
          if (filteredObj?.[key]?.length) {
            filteredObj[key].push(value);
          } else {
            filteredObj[key] = [value];
          }
        }
      });
      const filteredList: { label: string; values: string[] }[] = [];
      Object.keys(filteredObj).forEach((key) => {
        const values = filteredObj[key];
        filteredList.push({ label: key, values });
      });
      setFilteredData([...filteredList]);
    }, [props.pageNo, total, pageSize, filterList]);

    useEffect(() => {
      dispatch({
        type: 'setState',
        payload: {
          selectedRowKeys: [],
          selectedRows: [],
        },
      });
    }, [filterList]);

    // useEffect(() => {
    //   if (currentState.current.unit !== unit) {
    //     onBaseUnitChange();
    //   }
    //   // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [unit]);

    const updateBaseUnit = (unitVal: string) => {
      const data = state.dataSource?.map((item) => ({
        ...item,
        unit: unitVal,
        unitTemplateId: null,
        templateFormulaNameList: [],
        saleUnit: unitVal,
      }));
      // onUpdateAllUnitManager().then()
      onUpdateUnitManager(data).then(() => {
        currentState.current = { ...state, dataSource: data, unit: unitVal };
        getList(state.pageNo, state.pageSize, state.filterList);
      });
    };

    const updatePrice = (marketPrice: null | number = -1, formulaIdVal = -1) => {
      const data = state.dataSource?.map((item) => ({
        ...item,
        ...(marketPrice !== -1 ? { marketPrice } : {}),
        ...(formulaIdVal !== -1 ? { formulaId: formulaIdVal || null } : {}),
      }));

      onUpdatePriceManager(data).then(() => {
        currentState.current = { ...state, dataSource: data };
        // getList();
      });
    };

    useEffect(() => {
      dispatch({
        type: 'setState',
        payload: {
          total,
        },
      });
    }, [total, dispatch]);

    useImperativeHandle(ref, () => ({
      validateFields,
      updateBaseUnit,
      updatePrice,
      removeSelectedRowKeys: (values: number[]) => {
        const { selectedRowKeys, selectedRows } = state;
        const newSelectedRowKeys = selectedRowKeys?.filter(
          (item) => !values.includes(Number(item))
        );
        const newSelectedRows = selectedRows?.filter((item) => values.includes(Number(item.id)));
        // eslint-disable-next-line no-restricted-syntax
        // for (const key in selectedRowKeysPageData) {
        //   if (Object.prototype.hasOwnProperty.call(selectedRowKeysPageData, key)) {
        //     selectedRowKeysPageData[key] = selectedRowKeysPageData[key]?.filter(
        //       (item) => !values.includes(Number(item))
        //     );
        //   }
        // }
        // // eslint-disable-next-line no-restricted-syntax
        // for (const key in selectedRowsPageData) {
        //   if (Object.prototype.hasOwnProperty.call(selectedRowsPageData, key)) {
        //     selectedRowsPageData[key] = selectedRowsPageData[key]?.filter(
        //       (item) => !values.includes(Number(item.id))
        //     );
        //   }
        // }
        dispatch({
          type: 'setState',
          payload: {
            selectedRowKeys: newSelectedRowKeys,
            // selectedRowKeysPageData: { ...selectedRowKeysPageData },
            selectedRows: newSelectedRows,
            // selectedRowsPageData: { ...selectedRowsPageData },
          },
        });
      },
      checkSelectAll: (values: number[]) => {
        if (!state.selectedAll) {
          return;
        }
        const selectedRowKeys = uniq(state.selectedRowKeys.concat(values as Key[]));
        dispatch({
          type: 'setState',
          payload: {
            selectedAll: state.selectedAll,
            selectedRowKeys,
          },
        });
      },
      updateSelectAll: (val: boolean) => {
        if (!val) {
          dispatch({
            type: 'setState',
            payload: {
              selectedAll: val,
              selectedRowKeys: [],
            },
          });
        }
      },
      getPageInfo: () => ({ pageNo: state.pageNo, total: state.total }),
    }));

    const popoverContent = useCallback(
      () => (
        <Checkbox.Group
          options={hideColumnKeyOptions.map((hideColumnKeyOption) => ({
            label: hideColumnKeyOption,
            value: hideColumnKeyOption,
          }))}
          value={state.hideColumnKey}
          onChange={(checkedValues) => {
            dispatch({
              type: 'setState',
              payload: { hideColumnKey: checkedValues as string[] },
            });
          }}
          className={styles.hideColumnCheckboxGroup}
        />
      ),
      [hideColumnKeyOptions, state.hideColumnKey]
    );

    return (
      <div className={styles.wrap}>
        <Spin spinning={loading}>
          <div className={styles.tableTitle}>
            <span id={titleId}>{title}</span>
            <span className={styles.titleMsg}>
              {t('batch_setting_hint')}
              <Icon name="edit1" />
              {t('batch_setting_hint_end')}
            </span>
            <Popover
              content={popoverContent}
              placement="bottomRight"
              trigger="click"
              // @ts-ignore
              getPopupContainer={(triggerNode) => triggerNode.parentNode}
            >
              <Tooltip placement="top" title={t('list_setting')}>
                <Icon
                  name="todo-list"
                  title={t('hide_column_setting')}
                  className={styles.hideColumnBtn}
                  color={
                    state.hideColumnKey.length > 0 || filteredData.length > 0 ? '#008cff' : '#000'
                  }
                />
              </Tooltip>
            </Popover>
          </div>
          <div className={styles.hideColumnTags}>
            {state.hideColumnKey.map((columnKey) => (
              <Tag
                className={styles.tagBox}
                closable
                onClose={(e) => {
                  e.preventDefault();
                  onShowColumn(columnKey);
                }}
                key={columnKey}
              >
                {t('hidden_column', { columnKey })}
              </Tag>
            ))}
            {filteredData?.map((item) => (
              <Tag
                className={styles.tagBox}
                closable
                onClose={(e) => {
                  e.preventDefault();
                  const newFilterList: string[] = [];
                  const otherFilteredData = filteredData.filter(
                    (item2) => item2.label !== item.label
                  );
                  otherFilteredData?.forEach((otherItem) => {
                    otherItem.values?.forEach((valItem) => {
                      newFilterList.push(`${otherItem.label}=${valItem}`);
                    });
                  });
                  dispatch({
                    type: 'setState',
                    payload: {
                      selectedAll: false,
                      selectedRowKeys: [],
                    },
                  });
                  getList(1, state.pageSize, newFilterList);
                }}
                key={item.label}
              >
                {t('filtered_spec', { label: item.label, count: item.values.length })}
              </Tag>
            ))}
          </div>
          <ConfigProvider getPopupContainer={() => tableBoxRef.current as HTMLElement}>
            <div ref={tableBoxRef}>
              <Table
                ref={tableRef}
                columns={columns}
                dataSource={state.dataSource}
                rowKey="key"
                rowSelection={{
                  selectedRowKeys: state.selectedRowKeys,
                  // onChange: checkboxChange,
                  onSelect: (record, selected) => {
                    const { selectedRowKeys, selectedRows } = state;
                    let newSelectedRowKeys = selectedRowKeys;
                    let newSelectedRows = selectedRows;
                    if (selected) {
                      if (!newSelectedRowKeys.includes(record.key)) {
                        newSelectedRowKeys.push(record.key);
                        newSelectedRows.push(record);
                      }
                    } else {
                      newSelectedRowKeys = newSelectedRowKeys.filter((item) => item !== record.key);
                      newSelectedRows = newSelectedRows.filter((item) => item.key !== record.key);
                    }
                    dispatch({
                      type: 'setState',
                      payload: {
                        selectedRowKeys: [...newSelectedRowKeys],
                        selectedRows: [...newSelectedRows],
                        // selectedAll: !selected ? false : allTotal === newSelectedRowKeys.length,
                        selectedAll: false,
                      },
                    });
                    // console.log('newSelectedRowKeys', newSelectedRowKeys);
                    // console.log('newSelectedRows', newSelectedRows);
                  },
                  onSelectAll: (selected, selectedRowsValues, changeRows) => {
                    const { selectedRowKeys, selectedRows } = state;
                    let newSelectedRowKeys = selectedRowKeys;
                    let newSelectedRows = selectedRows;
                    const changeKeys = changeRows?.map((item) => item.key);
                    const rowsValues = selectedRowsValues?.filter((item) => item);
                    // console.log('selected', selected);
                    // console.log('selectedRowsValues', selectedRowsValues);
                    // console.log('changeRows', changeRows);
                    // console.log('newSelectedRows', newSelectedRows);
                    if (selected) {
                      newSelectedRowKeys = [...new Set([...newSelectedRowKeys, ...changeKeys])];
                      // newSelectedRows = [
                      //   ...rowsValues,
                      //   ...newSelectedRows.filter((item) => !changeKeys.includes(item.key)),
                      // ];
                      newSelectedRows = arrayObjectDeduplication([
                        ...newSelectedRows,
                        ...rowsValues,
                      ]);
                    } else {
                      newSelectedRowKeys = newSelectedRowKeys.filter(
                        (item) => !changeKeys.includes(item as number)
                      );
                      newSelectedRows = newSelectedRows.filter(
                        (item) => !changeKeys.includes(item.key)
                      );
                    }
                    // console.log('newSelectedRowKeys', newSelectedRowKeys);

                    dispatch({
                      type: 'setState',
                      payload: {
                        selectedRowKeys: [...newSelectedRowKeys],
                        selectedRows: [...newSelectedRows],
                        selectedAll: false,
                      },
                    });
                    // console.log('newSelectedRowKeys', newSelectedRowKeys);
                    // console.log('newSelectedRows', newSelectedRows);
                  },

                  // preserveSelectedRowKeys: true,
                }}
                onChange={(_pagination, tableFilters, _sorter, extra) => {
                  if (extra.action === 'filter') {
                    if (!testPerm('M_001_002_001_002_002_002')) {
                      return;
                    }
                    const newFilterList: string[] = [];
                    // eslint-disable-next-line no-restricted-syntax
                    for (const key in tableFilters) {
                      if (Object.prototype.hasOwnProperty.call(tableFilters, key)) {
                        const values = tableFilters[key];
                        if (values?.length) {
                          values?.forEach((item) => {
                            newFilterList.push(`${key}=${item}`);
                          });
                        }
                      }
                    }
                    dispatch({
                      type: 'setState',
                      payload: {
                        pageNo: 1,
                        selectedAll: false,
                        filters: tableFilters,
                        filterList: newFilterList,
                      },
                    });

                    getList(1, state.pageSize, newFilterList);
                  }
                }}
                pagination={false}
                className={styles.table}
              />
            </div>
          </ConfigProvider>

          <div className={styles.tableFooter}>
            <div>
              <Checkbox
                checked={state.selectedAll}
                className={styles.checkboxAll}
                onChange={(e) => {
                  selectAll(e.target.checked);
                }}
                disabled={!state.dataSource.length}
              >
                全选所有页
              </Checkbox>
              <span className={styles.selectedCount}>
                {t('selected_count', { count: selectedCount })}
              </span>
              {state.selectedRowKeys.length ? (
                <span
                  className={styles.batchDelBtn}
                  role="presentation"
                  onClick={() => {
                    if (!testPerm('M_001_002_001_002_002_003')) {
                      return;
                    }
                    onBatchRemove();
                  }}
                >
                  {t('public_del')}
                </span>
              ) : null}

              <span
                className={styles.batchBtns}
                tabIndex={-1}
                role="button"
                onClick={() => {
                  if (!testPerm('M_001_002_001_006')) {
                    return;
                  }
                  if (selectedCount < 1) {
                    message.error(t('select_product_first'));
                    return;
                  }
                  onBatchSet(title);
                }}
              >
                {t('batch_set')}
              </span>
            </div>
            <Pagination
              current={state.pageNo}
              pageSize={pageSize}
              total={state.total}
              showSizeChanger
              showQuickJumper
              showTotal={(total1) => t('multiple_item_total_count', { total: total1 })}
              onChange={(page, pageSizeVal) => {
                // if (pageSizeVal !== pageSize) {
                //   state.selectedRowKeysPageData = {};
                //   state.selectedRowsPageData = {};
                //   dispatch({
                //     type: 'setState',
                //     payload: {
                //       selectedRowKeysPageData: {},
                //       selectedRowsPageData: {},
                //     },
                //   });
                // }
                onPageChange(page, pageSizeVal, state.filterList);
              }}
            />
          </div>
        </Spin>

        <SetPriceVariableDrawer
          type="multiple"
          categoryId={categoryId}
          unit={unit}
          visible={visibleSetPriceVar}
          closeDrawer={setVisibleSetPriceVar}
          formulaId={recordInfo?.formulaId || 0}
          formulaList={recordInfo?.formulaList?.map((item) => ({ ...item })) || []}
          formatFormula={recordInfo?.formulaFormatName || ''}
          formulaName={recordInfo?.formulaName || ''}
          standardList={standardList}
          updateState={(val) => {
            state.dataSource[currIndex.current].formulaList = val.formulaList;
            state.dataSource[currIndex.current].marketPrice = val.marketPrice;
            const dataItem = state.dataSource[currIndex.current];
            onUpdateManagerFn('price', [dataItem], state.dataSource);
          }}
          onDefine={() => {
            // onPriceFormulaChange(val);
          }}
        />

        <SetVariableDrawer
          visible={showSetVar}
          drawerType="default"
          defaultValues={recordInfo?.templateFormulaNameList?.map((item) => ({ ...item }))}
          closeDrawer={setShowSetVar}
          dataInfo={{ ...currUnitInfo }}
          onConfirm={(val, values) => {
            state.dataSource[currIndex.current].unitTemplateId = val.id;
            state.dataSource[currIndex.current].templateFormulaNameList = values;
            const dataItem = state.dataSource[currIndex.current];
            // onUpdateManagerFn('unit', [dataItem], state.dataSource,getList);
            onUpdateManagerFn('unit', [dataItem], state.dataSource);
            // getList(state.pageNo, state.pageSize, state.filterList);
          }}
        />
        <BatchUnitStatusDrawer
          isShow={isMoreUnit}
          dataList={state.dataSource?.map((item) => ({ ...item }))}
          closeDrawer={setIsMoreUnit}
          onConfirm={(values) => {
            const dataArr: TableRowType[] = [];
            const dataSource = state.dataSource.map((item) => {
              const dataItem = {
                ...item,
                hasUnitTemplate: values.includes(item.id),
                ...(!values.includes(item.id)
                  ? { unitTemplateId: null, templateFormulaNameList: [] }
                  : {}),
              };
              // if (values.includes(item.id)) {
              dataArr.push(dataItem);
              // }
              return dataItem;
            });
            onUpdateUnitManager(dataArr).then(() => {
              dispatch({
                type: 'setState',
                payload: {
                  dataSource,
                },
              });
              updateState(dataSource, stepIndex);
            });
            // onUpdateManagerFn('unit', dataArr, dataSource, getList);
            // onUpdateManagerFn('unit', dataArr, dataSource);
          }}
        />
        <BatchUnitMatrDrawer
          isShow={isUnitMatr}
          unit={state.unit}
          // dataList={[...state.dataSource]}
          dataList={state.dataSource?.map((item) => ({ ...item }))}
          // closeDrawer={setIsUnitMatr}
          closeDrawer={(val) => {
            setIsUnitMatr(val);
          }}
          onConfirm={(values) => {
            const dataArr: TableRowType[] = [];
            const dataSource = state.dataSource.map((item, index) => {
              const dataItem = {
                ...item,
                // ...(values[index] !== null
                //   ? { unitTemplateId: values[index] || 0, hasUnitTemplate: true }
                //   : {}),
                unitTemplateId: values[index].unitTemplateId,
                hasUnitTemplate: Boolean(values[index].unitTemplateId),
                templateFormulaNameList: values[index].templateFormulaNameList || [],
                saleUnit: item.unit,
              };
              // if (values[index] !== null) {
              dataArr.push(dataItem);
              // }
              return dataItem;
            });

            onUpdateManagerFn('unit', dataArr, dataSource);
          }}
        />
        <BatchSaleUnitDrawer
          isShow={isMinSaleUnit}
          unitNum={unitNum}
          dataList={state.dataSource?.map((item) => ({ ...item }))}
          closeDrawer={setIsMinSaleUnit}
          onConfirm={(values) => {
            const dataArr: TableRowType[] = [];
            const dataSource = state.dataSource.map((item, index) => {
              const dataItem = {
                ...item,
                // ...(values[index] !== null ? { saleGroup: values[index] || 0 } : {}),
                saleGroup: values[index],
              };
              // if (values[index] !== null) {
              dataArr.push(dataItem);
              // }
              return dataItem;
            });
            onUpdateManagerFn('unit', dataArr, dataSource);
          }}
        />
        <BatchRetailPriceDrawer
          isShow={isRetailPrice}
          categoryId={categoryId}
          unit={unit}
          dataList={state.dataSource?.map((item) => ({ ...item }))}
          formulaId={formulaId}
          formulaName={formulaName}
          formatFormula={formatFormula}
          formulaList={formulaList?.map((item) => ({ ...item }))}
          standardList={standardList}
          closeDrawer={setIsRetailPrice}
          onConfirm={(values, formulaListVal) => {
            const dataArr: TableRowType[] = [];
            const dataSource = state.dataSource.map((item, index) => {
              const dataItem = {
                ...item,
                // ...(values[index] !== null ? { marketPrice: values[index] || 0 } : {}),
                marketPrice: values[index],
                formulaList: formulaListVal[index],
              };
              // if (values[index] !== null) {
              dataArr.push(dataItem);
              // }
              return dataItem;
            });
            onUpdateManagerFn('price', dataArr, dataSource);
          }}
        />
        <BatchCostPriceDrawer
          isShow={isCostPrice}
          dataList={state.dataSource?.map((item) => ({ ...item }))}
          closeDrawer={setIsCostPrice}
          onConfirm={(values) => {
            const dataArr: TableRowType[] = [];
            const dataSource = state.dataSource.map((item, index) => {
              const dataItem = {
                ...item,
                // ...(values[index] !== null ? { costPrice: values[index] || 0 } : {}),
                costPrice: values[index],
              };
              // if (values[index] !== null) {
              dataArr.push(dataItem);
              // }
              return dataItem;
            });
            onUpdateManagerFn('price', dataArr, dataSource);
          }}
        />
        <BatchFreightTemplateDrawer
          isShow={isFreightTemplate}
          dataList={state.dataSource?.map((item) => ({ ...item }))}
          closeDrawer={setIsFreightTemplate}
          freightOptions={freightOptions}
          onConfirm={(values) => {
            const dataArr: TableRowType[] = [];
            const dataSource = state.dataSource.map((item, index) => {
              const dataItem = {
                ...item,
                // ...(values[index] !== null ? { freightTemplateId: values[index] || 0 } : {}),
                freightTemplateId: values[index],
              };
              // if (values[index] !== null) {
              dataArr.push(dataItem);
              // }
              return dataItem;
            });
            onUpdateManagerFn('price', dataArr, dataSource);
          }}
        />
        <BatchValidTimeDrawer
          isShow={isValidTime}
          dataList={state.dataSource?.map((item) => ({ ...item }))}
          closeDrawer={setIsValidTime}
          onConfirm={(values) => {
            const dataArr: TableRowType[] = [];
            const dataSource = state.dataSource.map((item, index) => {
              const dataItem = {
                ...item,
                // ...(values[index] !== null
                //   ? { expirationDate: { type: 1, value: values[index] || 0 } }
                //   : {}),
                expirationDate: { type: 1, value: values[index] },
              };
              // if (values[index] !== null) {
              dataArr.push(dataItem);
              // }
              return dataItem;
            });
            onUpdateManagerFn('extension', dataArr, dataSource);
          }}
        />
        <BatchUnitManagerDrawer
          isShow={isUnitManager}
          unitNum={unitNum}
          baseUnit={state.unit}
          closeDrawer={setIsUnitManager}
          onConfirm={(saleUnit, multiUnit, unitConversion, varList, updateKeys) => {
            if (updateKeys.length) {
              if (state.selectedAll) {
                onUpdateAllUnitManager(
                  saleUnit,
                  multiUnit,
                  unitConversion,
                  varList,
                  updateKeys
                ).then(() => {
                  getList(state.pageNo, state.pageSize, state.filterList);
                });
              } else {
                const dataArr = state.dataSource
                  .filter((item) => state.selectedRowKeys.includes(item.key))
                  .map((item) => ({
                    ...item,
                    ...(updateKeys.includes('multiUnit') ? { hasUnitTemplate: multiUnit } : {}),
                    ...(updateKeys.includes('saleUnit') ? { saleGroup: saleUnit } : {}),
                    ...(updateKeys.includes('unitConversion')
                      ? { unitTemplateId: unitConversion }
                      : {}),
                    ...(updateKeys.includes('var') ? { templateFormulaNameList: varList } : {}),
                    saleUnit: item.unit,
                  }));
                if (updateKeys.includes('multiUnit')) {
                  onUpdateUnitManager(dataArr)
                    .then(() => {
                      getList(state.pageNo, state.pageSize, state.filterList);
                    })
                    .catch(() => {
                      getList(state.pageNo, state.pageSize, state.filterList);
                    });
                } else {
                  onUpdateUnitManager(dataArr, null)
                    .then(() => {
                      getList(state.pageNo, state.pageSize, state.filterList);
                    })
                    .catch(() => {
                      getList(state.pageNo, state.pageSize, state.filterList);
                    });
                }
              }
            }
          }}
        />
        <BatchPriceManagerDrawer
          isShow={isPriceManager}
          categoryId={categoryId}
          unit={unit}
          priceNum={state.dataSource?.[0]?.priceNum || 0}
          closeDrawer={setIsPriceManager}
          formulaId={formulaId}
          formulaName={formulaName}
          formatFormula={formatFormula}
          formulaList={formulaList}
          standardList={standardList}
          freightOptions={freightOptions}
          onConfirm={(marketPrice, costPrice, freightTemplateId, formulaListVal, updateKeys) => {
            // if (marketPrice !== null || costPrice !== null || freightTemplateId !== null) {
            // const dataArr: TableRowType[] = [];
            const dataArr = state.dataSource
              .filter((item) => state.selectedRowKeys.includes(item.key))
              .map((item) => ({
                ...item,
                ...(updateKeys.includes('marketPrice') ? { marketPrice } : {}),
                ...(updateKeys.includes('var') ? { formulaList: formulaListVal } : {}),
                ...(updateKeys.includes('costPrice') ? { costPrice } : {}),
                ...(updateKeys.includes('freightTemplateId') ? { freightTemplateId } : {}),
              }));
            if (state.selectedAll) {
              onUpdateAllPriceManager(
                marketPrice,
                costPrice,
                freightTemplateId,
                formulaListVal,
                updateKeys
              ).finally(() => {
                getList(state.pageNo, state.pageSize, state.filterList);
              });
            } else {
              onUpdateManagerFn('price', dataArr);
            }
            // }
          }}
        />
        <BatchExtendManagerDrawer
          isShow={isExtendManager}
          closeDrawer={setIsExtendManager}
          onConfirm={(expirationDate, detailList, updateKeys) => {
            const dataArr = state.dataSource
              .filter((item) => state.selectedRowKeys.includes(item.key))
              .map((item) => ({
                ...item,
                ...(updateKeys.includes('expirationDate')
                  ? { expirationDate: { type: 1, value: expirationDate } }
                  : {}),
                ...(detailList.length
                  ? { detailList: createDetailList(detailList) }
                  : { detailList: [] }),
              }));
            if (state.selectedAll) {
              onUpdateAllExtensionManager(expirationDate, detailList, updateKeys).then(() => {
                getList(state.pageNo, state.pageSize, state.filterList);
              });
            } else {
              onUpdateManagerFn('extension', dataArr);
            }
          }}
        />

        <GraphicDescDrawer
          visible={visible}
          preview={preview}
          title={graphicDescText}
          permissionList={[{ key: 'uploadVideo', code: 'M_001_005_005' }]}
          onClose={() => {
            setVisible(false);
          }}
          onPreviewChange={(val) => {
            setPreview(val);
          }}
          onConfirm={(values) => {
            setGraphicDescData(values);
            if (graphicDescText === t('graphicDescText')) {
              if (recordInfo) {
                addImageText(recordInfo, 'detailList', createDetailList(values));
              }
            } else {
              onUpdateAllExtensionManager(null, values)
                .then(() => {
                  const dataSource = state.dataSource?.map((item) => ({
                    ...item,
                    detailList: createDetailList(values),
                  }));
                  dispatch({
                    type: 'setState',
                    payload: {
                      dataSource,
                    },
                  });
                  updateState(dataSource, stepIndex);
                  batchGraphicDescData.current = values;
                })
                .catch(() => {
                  setGraphicDescData([]);
                });
            }
          }}
          data={graphicDescData}
        />
      </div>
    );
  }
);

MultipleItem.displayName = 'MultipleItem';

MultipleItem.defaultProps = {};

export default MultipleItem;
