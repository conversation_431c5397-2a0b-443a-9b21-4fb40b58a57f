import {
  useState,
  forwardRef,
  useImperative<PERSON>andle,
  useEffect,
  useMemo,
  ReactNode,
  useRef,
} from 'react';
import {
  Form,
  FormInstance,
  Input,
  Select,
  Cascader,
  Switch,
  Tooltip,
  TreeSelect,
  // message,
} from 'antd';
// import { useMount } from 'ahooks';
import flattenDeep from 'lodash/flattenDeep';
import classNames from 'classnames';
import { Icon, Modal } from '@/components';
import { testPerm } from '@/utils/permission';
import { GetCategoryResult as CategoryOptionType } from '@/apis/get-categories';
import { CustomerCategoryListResult as CustomizeOptionType } from '@/apis/base/customer-category-list';
import { ResultType as BrandOptionType } from '@/apis/system/get-brand-options';
import { ResultType as UnitOptionType } from '@/apis/base/get-unit-options';
import { ResultType as UnitFormulaOptionType } from '@/apis/base/get-unit-formula-options';
import { ResultType as PriceFormulaOptionType } from '@/apis/base/get-price-formula-options';
import { getGoodsPriceSimpleShelf, clearPmsCategorySpec, AttributeListsType } from '@/apis';
import type { GetBaseStandardListRes } from '@/apis';
import { StandardItemType, StateType, VarListType, MultipleDataType } from '@/apis/pms/type';
import { useTranslation } from 'react-i18next';
import MajorImagesComponent from '../../components/major-images';
import MajorVideoComponent from '../../components/major-video';
import SelectProductAttributeDrawer from '../../components/select-product-attribute-drawer';
import UnitFormulaDrawer from '../../components/unit-formula-drawer';
import PriceSettingDrawer from '../../components/price-setting-drawer';
import SetPriceVariableDrawer from '../../components/set-price-variable-drawer';
import SetVariableDrawer from '../../components/set-variable-drawer';
import CustomCategoryCreate from '../../components/custom-category-create';
import BaseInfoAttr from './base-info-attr';
import styles from './base-info-card.module.less';

interface PropsType {
  id: number;
  image: string;
  imageList: string[];
  videoList: any[];
  categoryId: number;
  categoryIds: number[];
  standardType: number;
  name: string;
  customerCategoryCodeSet: { label: string; value: string }[];
  // customizedIds: number[];
  taxTemplateId: number;
  brandId: number;
  unit: string;
  isUnitFormula: boolean;
  unitTemplateId: number;
  unitTemplateName: string;
  isPriceFormula: boolean;
  formulaId: number;
  formulaName: string;
  formatFormula: string;
  formulaList: VarListType[];
  attrOptions: GetBaseStandardListRes[];
  attributeList: StateType['attributeList'];

  categoryOptions: CategoryOptionType[];
  customizeOptions: CustomizeOptionType[];
  brandOptions: BrandOptionType[];
  taxRateOptions: { label: string; value: number }[];
  priceFormulaOptions: PriceFormulaOptionType[];
  unitFormulaOptions: UnitFormulaOptionType[];
  unitOptions: UnitOptionType[];
  condition: Record<string, string[]>;
  isValidate: boolean;
  multipleStandard: MultipleDataType;
  templateFormulaArgsList: {
    id: number;
    name: string;
    value: string;
  }[];

  onGetUnitOptions: SimpleFn<number>;
  onGetUnitFormulaOptions: MultipleParamsFn<[val: string, value: boolean]>;
  onGetPriceFormulaOptions: () => void;
  onGetCustomizeOptions: () => void;
  onUnitChange: MultipleParamsFn<[val: string]>;
  onPriceFormulaChange: MultipleParamsFn<[val: number | null]>;
  onGetStandardList: MultipleParamsFn<[val: number | null]>;

  // onUnitFormulaChange: MultipleParamsFn<[val: number]>;
  onSetState: SimpleFn<Partial<StateType>>;
}

export type BaseInfoCardInstance = {
  form: FormInstance;
  attributeLists: AttributeListsType[];
  standardList1?: any;
};

const BaseInfoCardComponent = forwardRef<BaseInfoCardInstance, PropsType>(
  (
    {
      id,
      image,
      imageList,
      videoList,
      categoryId,
      categoryIds,
      standardType,
      categoryOptions,
      name,
      customerCategoryCodeSet,
      // customizedIds,
      customizeOptions,
      // @ts-ignore
      standardList1,
      brandId,
      brandOptions,
      taxTemplateId,
      taxRateOptions,
      unit,
      isUnitFormula,
      unitOptions,
      unitTemplateId,
      unitTemplateName,
      unitFormulaOptions,
      condition,
      isValidate,
      multipleStandard,
      templateFormulaArgsList,

      isPriceFormula,
      formulaId,
      formulaName,
      formatFormula,
      formulaList,
      priceFormulaOptions,
      attrOptions,
      attributeList,

      onSetState,
      onGetUnitOptions,
      onGetUnitFormulaOptions,
      onGetPriceFormulaOptions,
      onGetCustomizeOptions,
      onUnitChange,
      onPriceFormulaChange,
      onGetStandardList,
      // onUnitFormulaChange,
    },
    ref
  ) => {
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const [showCreateCategory, setShowCreateCategory] = useState(false);
    const [visibleNameDrawer, setVisibleNameDrawer] = useState(false);
    const [visibleUnitFormulaDrawer, setVisibleUnitFormulaDrawer] = useState(false);
    const [visiblePriceSetting, setVisiblePriceSetting] = useState(false);
    const [visibleSetPriceVar, setVisibleSetPriceVar] = useState(false);
    const [visibleSetUnitVar, setVisibleSetUnitVar] = useState(false);
    const [unitInfo, setUnitInfo] = useState<UnitFormulaOptionType>({} as UnitFormulaOptionType);
    const singleUnitInfo = useRef<{
      isUnitFormula: boolean;
      unitTemplateId: number;
      unitTemplateName: string;
      templateFormulaArgsList: {
        id: number;
        name: string;
        value: string;
      }[];
    }>({
      isUnitFormula: false,
      unitTemplateId: 0,
      unitTemplateName: '',
      templateFormulaArgsList: [],
    });
    const standardList = useMemo(() => {
      const list: { name: string; value: string }[][] = [];
      multipleStandard.standardList.forEach((item) => {
        list.push(item.values?.map((valueItem) => ({ name: item.name, value: valueItem.value })));
      });

      return list;
    }, [multipleStandard.standardList]);
    const [attributeLists, setAttributeLists] = useState<AttributeListsType[]>([]);

    const onSelectTag = (data: Array<StandardItemType>) => {
      const goodsNameList = data.map((item) => ({
        name: item.name,
        value: item.values[0].value,
      }));
      const obj: Record<string, string[]> = {};
      data.forEach((item) => {
        obj[item.name] = item.values.map((item2) => item2.value);
      });
      const nameTag = flattenDeep(data.map((item) => item.values))
        .map((every) => every.value)
        .join(' ');
      onSetState({
        name: nameTag,
        nameStandardList: goodsNameList,
        condition: obj,
      });
      setVisibleNameDrawer(false);
    };

    // 补0函数, 0.1 -- 0.001
    const fillZeroFn = (n: number) => `0.${'0'.repeat(n - 1)}1`;

    // useMount(() => {
    //   onGetUnitFormulaOptions();
    // });

    useEffect(() => {
      if (unitFormulaOptions?.length && unitTemplateId) {
        const option = unitFormulaOptions.filter((item) => item.id === unitTemplateId)?.[0] || {};
        setUnitInfo(option);
      }
    }, [unitFormulaOptions, unitTemplateId]);

    useEffect(() => {
      form.setFieldsValue({ categoryIds, name, unit: unit || undefined });
    }, [categoryIds, name, unit, form]);

    useEffect(() => {
      if (standardType === 1) {
        singleUnitInfo.current = {
          isUnitFormula,
          unitTemplateId,
          unitTemplateName,
          templateFormulaArgsList,
        };
        onSetState({
          isUnitFormula: false,
          unitTemplateId: 0,
          unitTemplateName: '',
          templateFormulaArgsList: [],
        });
      } else if (Object.keys(singleUnitInfo.current).length) {
        onSetState(singleUnitInfo.current);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [standardType]);

    useImperativeHandle(ref, () => ({
      form,
      attributeLists,
    }));

    const createVarBox = () => {
      const isSetVar = !formulaList?.filter((item) => item.value !== null && item.value !== '')
        .length;
      let varNode: ReactNode | string = '';
      if (isSetVar) {
        varNode = (
          <div
            className={styles.setMutUnitVarErr}
            role="presentation"
            onClick={() => {
              setVisibleSetPriceVar(true);
            }}
          >
            {t('multiple_enterVariableValue')}
            <Icon name="right" size={14} />
          </div>
        );
      } else {
        varNode = formulaList.map((item) => (
          <div
            key={item.id}
            className={styles.priceFormulaVar}
            role="presentation"
            onClick={() => {
              setVisibleSetPriceVar(true);
            }}
          >
            {item.name}
            {item.value ? `:${item.value}` : ``}
            <Icon name="right" size={16} style={{ cursor: 'pointer' }} color="#008CFF" />
          </div>
        ));
      }
      return varNode;
    };

    const createUnitVar = () => {
      let varNode: ReactNode | string = '';
      if (!!unitTemplateId && templateFormulaArgsList?.length) {
        const isSetVar =
          templateFormulaArgsList?.filter((item) => item.value === '' || item.value === null)
            .length === 0;
        if (isSetVar) {
          varNode = (
            <div className={styles.formItemMessage}>
              {templateFormulaArgsList?.map((unitItem) => (
                <span
                  className={styles.unitFormulaLink}
                  role="button"
                  tabIndex={0}
                  onClick={() => {
                    setVisibleSetUnitVar(true);
                  }}
                >
                  {unitItem.name}：{unitItem.value} <Icon name="right" size={16} />
                </span>
              ))}
            </div>
          );
        } else {
          varNode = (
            <div className={styles.formItemMessage}>
              <span
                className={styles.unitFormulaLinkErr}
                role="button"
                tabIndex={0}
                onClick={() => {
                  setVisibleSetUnitVar(true);
                }}
              >
                {t('addVariableModal_enterVariableName2')} <Icon name="right" size={16} />
              </span>
            </div>
          );
        }
      }
      return varNode;
    };

    return (
      <div className={styles.card} id="goodsBaseInfoCard">
        <div className={styles.cardTitle}>{t('base_basicInfo')}</div>
        <div className={styles.cardContent}>
          <div className={styles.media}>
            <MajorImagesComponent image={image} imageList={imageList} onSetState={onSetState} />
            <MajorVideoComponent
              videoList={videoList}
              onSetState={onSetState}
              permCode="M_001_002_001_008"
            />
          </div>

          <Form className={styles.form} form={form} initialValues={{ unit: null }}>
            <Form.Item
              label={t('base_industryCategory')}
              rules={[{ required: true, message: t('base_selectIndustryCategory') }]}
              name="categoryIds"
              extra={
                <div className={styles.formItemMessage}>{t('base_mustSelectIndustryCategory')}</div>
              }
            >
              <Cascader
                disabled={Boolean(id)}
                allowClear={false}
                showSearch
                placeholder={t('base_selectIndustryCategory')}
                options={categoryOptions}
                value={categoryIds}
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
                onChange={(value) => {
                  if (!testPerm('M_001_002_001_001')) {
                    form.resetFields();
                    form.setFieldsValue({ categoryIds: [] });
                    return;
                  }
                  const updateCateFn = () => {
                    onSetState({
                      categoryId: value[value.length - 1] as number,
                      categoryIds: value as number[],
                      unit: '',
                      condition: {},
                      name: '',
                      customerCategoryCodeSet: [],
                      brandId: 0,
                      isUnitFormula: false,
                      unitTemplateId: 0,
                      unitTemplateName: '',
                      marketPrice: null,
                      saleGroup: null,
                      freightTemplateId: null,
                      costPrice: null,
                      isPriceFormula: false,
                      formulaId: 0,
                      formulaName: '',
                      formula: '',
                      formatFormula: '',
                      attributeList: [],
                    });
                    onGetUnitOptions(value[value.length - 1] as number);
                    form.resetFields();
                    form.setFieldsValue({ categoryIds: value });
                  };
                  if (categoryIds.length) {
                    Modal.confirm({
                      title: t('base_modalTitle'),
                      content: t('base_modalContent'),
                      okText: t('base_confirm'),
                      cancelText: t('base_cancel'),
                      centered: true,
                      keyboard: false,
                      maskClosable: false,
                      onCancel: () => {
                        form.setFieldsValue({ categoryIds });
                      },
                      onOk: () => {
                        updateCateFn();
                        onGetStandardList(value[value.length - 1] as number);
                        clearPmsCategorySpec({ categoryId });
                      },
                    });
                  } else {
                    onGetStandardList(value[value.length - 1] as number);
                    updateCateFn();
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label={t('base_productName')}
              rules={[{ required: true, message: t('base_productNameRequired') }]}
              name="name"
              extra={<div className={styles.formItemMessage}>{t('base_selectCategoryFirst')}</div>}
            >
              <Input
                disabled={!categoryId}
                placeholder={t('base_enterOrSelectName')}
                maxLength={50}
                value={name}
                suffix={
                  <span
                    className={styles.nameLineBtn}
                    style={{
                      cursor: categoryId ? 'pointer' : 'not-allowed',
                      color: categoryId ? '#008cff' : '#C6CCD8',
                    }}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      if (!categoryId) return;
                      setVisibleNameDrawer(true);
                    }}
                  >
                    {t('base_selectName')}
                  </span>
                }
                onChange={(e) => {
                  if (!testPerm('M_001_002_001_001')) {
                    return;
                  }
                  onSetState({
                    name: e.target.value,
                  });
                  form.setFieldsValue({ name: e.target.value });
                }}
              />
            </Form.Item>
            <Form.Item label={t('base_productCategory')} className={styles.treeSelectStyles}>
              <div className={styles.categoryBox}>
                <TreeSelect
                  className={styles.categorySelect}
                  showSearch
                  value={customerCategoryCodeSet}
                  placeholder={t('base_selectOrSearchCustomCategory')}
                  allowClear
                  multiple
                  treeDefaultExpandAll
                  // treeExpandedKeys={customerCategoryCodeSet.map((item) => item.value)}
                  treeNodeFilterProp="label"
                  showCheckedStrategy={TreeSelect.SHOW_ALL}
                  treeCheckStrictly
                  treeCheckable
                  onFocus={() => {
                    testPerm('M_001_012_001');
                  }}
                  onChange={(values, _label, extra) => {
                    if (!testPerm('M_001_002_001_001')) {
                      return;
                    }
                    const isClickFirst =
                      customizeOptions?.filter((item) => item.code === extra.triggerValue).length >
                      0;
                    let valueData: { label: string; value: string }[] = values;
                    if (extra.checked) {
                      if (isClickFirst) {
                        const spliceData = customizeOptions
                          ?.filter((item) => extra.triggerValue === item.code)
                          .map((item) => item.children?.map((child) => child.code))
                          .flat();
                        valueData = valueData?.filter((item) => !spliceData.includes(item.value));
                      } else {
                        let spliceItem = '';
                        customizeOptions?.forEach((cusItem) => {
                          cusItem.children?.forEach((childItem) => {
                            if (childItem.code === extra.triggerValue) {
                              spliceItem = cusItem.code || '';
                            }
                          });
                        });
                        valueData = valueData?.filter((item) => item.value !== spliceItem);
                      }
                    }

                    // const valueData = firstValues.concat(otherValues);
                    onSetState({
                      customerCategoryCodeSet: valueData,
                    });
                  }}
                  fieldNames={{ label: 'label', value: 'code', children: 'children' }}
                  treeData={customizeOptions}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                  bordered={false}
                />
                <div className={styles.newCategory}>
                  <div
                    className={styles.newCategoryText}
                    role="presentation"
                    onClick={() => {
                      if (testPerm('M_001_012_002')) {
                        setShowCreateCategory(true);
                      }
                    }}
                  >
                    {t('base_createCategory')}
                  </div>
                </div>
              </div>
            </Form.Item>

            <Form.Item label={t('base_productBrand')}>
              <Select
                allowClear
                showSearch
                placeholder={t('base_selectBrand')}
                value={brandId || null}
                options={brandOptions}
                fieldNames={{
                  label: 'name',
                  value: 'id',
                }}
                filterOption={(input, option) =>
                  (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onFocus={() => {
                  testPerm('M_001_012_001');
                }}
                onChange={(value) => {
                  if (!testPerm('M_001_002_001_001')) {
                    return;
                  }
                  onSetState({
                    brandId: value,
                  });
                }}
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              />
            </Form.Item>
            <Form.Item
              required
              name="unit"
              label={t('base_basicUnit')}
              rules={[{ required: true, message: t('base_selectBasicUnit') }]}
            >
              <div className={classNames('ant-input', styles.unitSelectWrap)}>
                <Select
                  showSearch
                  placeholder={t('base_selectBasicUnit')}
                  value={unit || null}
                  options={unitOptions}
                  bordered={false}
                  fieldNames={{
                    label: 'unitName',
                    value: 'unitName',
                  }}
                  filterOption={(input, option) =>
                    (option?.unitName ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                  className={styles.unitSelect}
                  onChange={(value, option: any) => {
                    if (!testPerm('M_001_002_001_001')) {
                      return;
                    }
                    onSetState({
                      isUnitFormula: false,
                      unit: value,
                      unitTemplateId: 0,
                      unitTemplateName: '',
                      unitNum: option.floatNum,
                      priceNum: option.priceFloatNum,
                      saleGroup: !option.floatNum ? 1 : parseFloat(fillZeroFn(option.floatNum)),
                    });
                    form.setFieldsValue({ unit: value });
                    onUnitChange(value);
                  }}
                  getPopupContainer={(triggerNode) => triggerNode.parentNode}
                />

                <div className={styles.unitFormulaWrap}>
                  <Switch
                    disabled={!unit || standardType === 1}
                    checked={isUnitFormula}
                    onClick={(checked) => {
                      if (!testPerm('M_001_002_001_003')) {
                        return;
                      }
                      if (checked) {
                        if (!unitFormulaOptions.length) {
                          onGetUnitFormulaOptions(unit, true);
                        }
                        setVisibleUnitFormulaDrawer(true);
                      } else {
                        onSetState({
                          isUnitFormula: false,
                          unitTemplateId: 0,
                          unitTemplateName: '',
                        });
                      }
                    }}
                  />
                  <span className={styles.unitFormulaText}>{t('base_enableMultiUnit')}</span>
                  <Tooltip placement="bottomRight" title={t('base_enableMultiUnitTooltip')}>
                    <img
                      src="https://img.huahuabiz.com/user_files/2023324/167964345796046.svg"
                      alt=""
                      className={styles.unitFormulaIcon}
                    />
                  </Tooltip>
                </div>
              </div>
              {!!unitTemplateId && (
                <div className={styles.formItemMessage}>
                  <span
                    className={styles.unitFormulaLink}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      setVisibleUnitFormulaDrawer(true);
                    }}
                  >
                    {t('base_Unit_change_relation')}：{unitTemplateName}{' '}
                    <Icon name="right" size={16} />
                  </span>
                </div>
              )}
              {createUnitVar()}
            </Form.Item>
            <Form.Item
              label={
                <div>
                  <span>{t('base_dynamicPrice')}</span>
                  <Tooltip placement="bottomLeft" title={t('base_dynamicPriceTooltip')}>
                    <img
                      src="https://img.huahuabiz.com/user_files/2023324/167964345796046.svg"
                      alt=""
                      className={styles.priceFormulaIcon}
                    />
                  </Tooltip>
                </div>
              }
              className={styles.miniFormItem}
            >
              <Switch
                checked={isPriceFormula}
                onChange={(checked) => {
                  if (!testPerm('M_001_002_001_004')) {
                    return;
                  }
                  if (checked && !priceFormulaOptions.length) {
                    onGetPriceFormulaOptions();
                  }
                  if (!checked) {
                    Modal.confirm({
                      title: t('base_modalTitle'),
                      content: t('base_closeDynamicPriceContent'),
                      okText: t('base_confirm'),
                      cancelText: t('base_cancel'),
                      centered: true,
                      keyboard: false,
                      maskClosable: false,
                      onCancel: () => {
                        onSetState({
                          isPriceFormula: true,
                        });
                      },
                      onOk: () => {
                        onSetState({
                          isPriceFormula: checked,
                          formulaId: 0,
                          formulaName: '',
                          formulaList: [],
                          formatFormula: '',
                          marketPrice: null,
                          // formula: '',
                        });
                      },
                    });
                  } else {
                    onSetState({
                      isPriceFormula: checked,
                    });
                    setVisiblePriceSetting(true);
                  }
                }}
              />
              <span className={styles.priceFormulaText}>{t('base_dynamicPriceCalculation')}</span>
            </Form.Item>
            {isPriceFormula && (
              <>
                <Form.Item label={t('base_selectDynamicPriceFormula')} required>
                  <div
                    className={classNames(
                      styles.priceFormulaNameBox,
                      isValidate && isPriceFormula && !formulaId
                        ? styles.priceFormulaNameBoxErr
                        : ''
                    )}
                    role="presentation"
                    onClick={() => {
                      // if (standardType === 1 && !multipleStandard.standardList.length) {
                      //   message.error('请先新增商品规格');
                      //   return;
                      // }
                      setVisiblePriceSetting(true);
                    }}
                  >
                    {formulaName ? (
                      <span>{formulaName}</span>
                    ) : (
                      <span style={{ color: '#888B98' }}>
                        {t('base_selectDynamicPriceFormula')}
                      </span>
                    )}
                    <Icon name="right" color="#C6CCD8" size={20} />
                  </div>
                  {isValidate && isPriceFormula && !formulaId ? (
                    <div className={styles.priceFormulaNameBoxErrText}>
                      {t('base_selectDynamicPriceFormula')}
                    </div>
                  ) : null}
                </Form.Item>
                {!!formatFormula && (
                  <Form.Item label={t('base_formula')} className={styles.miniFormItem}>
                    <div className={styles.priceFormulaName}>{formatFormula}</div>
                  </Form.Item>
                )}
                {!!formulaList.length && (
                  <Form.Item label={t('base_variable')} className={styles.miniFormItem}>
                    {!standardType ? (
                      createVarBox()
                    ) : (
                      <div>
                        <span style={{ color: '#888B98', fontSize: 14 }}>--</span>
                        <span style={{ color: '#EA1C26', fontSize: 12, marginLeft: 6 }}>
                          {t('base_multiSpecVariableTip')}
                        </span>
                      </div>
                    )}
                  </Form.Item>
                )}
              </>
            )}

            <Form.Item label={t('base_taxRate')}>
              <Select
                allowClear
                showSearch
                placeholder={t('base_selectTaxRate')}
                value={taxTemplateId || null}
                options={taxRateOptions}
                // fieldNames={{
                //   label: 'taxRate',
                //   value: 'taxRate',
                // }}
                filterOption={(input, option) =>
                  String(option?.value ?? '')
                    .toLowerCase()
                    .includes(input.toLowerCase())
                }
                // onFocus={() => {
                //   testPerm('M_001_012_001');
                // }}
                onChange={(value) => {
                  // if (!testPerm('M_001_002_001_001')) {
                  //   return;
                  // }
                  const taxRateInfo = taxRateOptions?.find((item) => item.value === value);
                  const taxRateValue = Number(taxRateInfo?.label?.split('%')?.[0]) || 0;
                  onSetState({
                    taxRate: taxRateValue,
                    taxTemplateId: value,
                  });
                }}
                getPopupContainer={(triggerNode) => triggerNode.parentNode}
              />
            </Form.Item>

            {/* {attrOptions?.length ? ( */}
            {/*  <Form.Item label="商品属性"> */}
            {/*    {attrOptions?.map((item) => */}
            {/*      item?.standardDetail?.map((standardItem, standardIndex) => ( */}
            {/*        <Input */}
            {/*          className={styles.attrInput} */}
            {/*          key={standardItem.id} */}
            {/*          placeholder={`请输入${standardItem.detailName}`} */}
            {/*          maxLength={35} */}
            {/*          value={attributeList?.[standardIndex]?.value || ''} */}
            {/*          prefix={ */}
            {/*            <span */}
            {/*              className={styles.prefixNameLineBtn} */}
            {/*              style={{ */}
            {/*                cursor: categoryId ? 'pointer' : 'not-allowed', */}
            {/*                color: categoryId ? '#008cff' : '#C6CCD8', */}
            {/*              }} */}
            {/*              role="button" */}
            {/*              tabIndex={0} */}
            {/*            > */}
            {/*              {standardItem.detailName} */}
            {/*            </span> */}
            {/*          } */}
            {/*          onChange={(e) => { */}
            {/*            const attrList = [...(attributeList || [])]; */}
            {/*            attrList[standardIndex].value = e.target.value; */}
            {/*            onSetState({ */}
            {/*              attributeList: attrList, */}
            {/*            }); */}
            {/*          }} */}
            {/*        /> */}
            {/*      )) */}
            {/*    )} */}
            {/*  </Form.Item> */}
            {/* ) : null} */}
            {!!categoryId && (
              <BaseInfoAttr
                catId={categoryId}
                attrOptions={attrOptions}
                attributeList={attributeList}
                onConfirm={(val) => setAttributeLists(val)}
              />
            )}
          </Form>
        </div>

        <PriceSettingDrawer
          isShow={visiblePriceSetting}
          categoryId={categoryId}
          closeDrawer={setVisiblePriceSetting}
          defaultId={formulaId}
          List={{ standardList1 }}
          onSelect={(val) => {
            const newFormulaList = val?.varList?.map((every: VarListType) => ({
              ...every,
              value: null,
            }));

            if (!val?.varList?.length) {
              if (standardType === 1) {
                onSetState({
                  formulaId: val.id,
                  formulaName: val.name,
                  formula: val.formula,
                  formatFormula: val.formatFormula,
                  formulaList: newFormulaList,
                });
                setVisiblePriceSetting(false);
              } else {
                getGoodsPriceSimpleShelf({
                  formulaId: val.id,
                  formulaList: newFormulaList,
                  standardList: !standardType
                    ? [[{ name: t('base_specification'), value: t('base_generalSpec') }]]
                    : standardList,
                  catId: categoryId,
                  unit,
                }).then((res) => {
                  onPriceFormulaChange(res.priceList[0]);
                  setVisiblePriceSetting(false);
                  // setRetailPrice(res.priceList[0]);
                  onSetState({
                    formulaId: val.id,
                    formulaName: val.name,
                    formula: val.formula,
                    formatFormula: val.formatFormula,
                    formulaList: newFormulaList,
                    marketPrice: res.priceList[0],
                  });
                });
              }
            } else {
              onSetState({
                formulaId: val.id,
                formulaName: val.name,
                formula: val.formula,
                formatFormula: val.formatFormula,
                formulaList: newFormulaList,
              });
              setVisiblePriceSetting(false);
              if (standardType !== 1) {
                setVisibleSetPriceVar(true);
              }
            }
          }}
          onClose={(idVal) => {
            if (!idVal) {
              onSetState({
                isPriceFormula: false,
                formulaId: 0,
                formulaName: '',
                formulaList: [],
                formatFormula: '',
                marketPrice: null,
                // formula: '',
              });
            }
          }}
        />
        {/* @ts-ignore */}
        <SetPriceVariableDrawer
          type={!standardType ? 'single' : 'multiple'}
          categoryId={categoryId}
          unit={unit}
          visible={visibleSetPriceVar}
          closeDrawer={setVisibleSetPriceVar}
          formulaId={formulaId}
          formulaList={formulaList?.map((item) => ({ ...item }))}
          formatFormula={formatFormula}
          formulaName={formulaName}
          // onSetState={onSetState}
          updateState={(val) => {
            onSetState(val);
          }}
          onDefine={(val) => {
            onPriceFormulaChange(val);
          }}
        />

        <SelectProductAttributeDrawer
          type="name"
          condition={condition}
          isShow={visibleNameDrawer}
          closeDrawer={setVisibleNameDrawer}
          categoryId={categoryId}
          onSelectTag={onSelectTag}
          onSelectedRemoveSuccess={() => {
            // getList();
          }}
        />

        <UnitFormulaDrawer
          title={t('base_selectUnitConversionRelation')}
          unit={unit as string}
          unitOptions={unitOptions}
          unitTemplateId={unitTemplateId}
          unitFormulaOptions={unitFormulaOptions}
          visible={visibleUnitFormulaDrawer}
          onClose={() => {
            setVisibleUnitFormulaDrawer(false);
          }}
          templateFormulaArgsList={templateFormulaArgsList}
          onGetUnitFormulaOptions={(val) => {
            onGetUnitFormulaOptions(val, true);
          }}
          onConfirm={(data, variableList, unitVal) => {
            onSetState({
              isUnitFormula: true,
              unit: unitVal,
              unitTemplateId: data.id,
              unitTemplateName: data.templateName,
              saleUnitOptions: data.childrenUnitList,
              templateFormulaArgsList: variableList || [],
            });
            setVisibleUnitFormulaDrawer(false);
            // onUnitFormulaChange(data.id);
          }}
          categoryId={categoryId}
        />

        <SetVariableDrawer
          drawerType="default"
          visible={visibleSetUnitVar}
          defaultValues={templateFormulaArgsList?.map((item) => ({ ...item }))}
          closeDrawer={setVisibleSetUnitVar}
          dataInfo={unitInfo}
          onConfirm={(_dataInfo, variableList) => {
            onSetState({
              templateFormulaArgsList: variableList || [],
            });
            setVisibleSetUnitVar(false);
          }}
        />

        <CustomCategoryCreate
          visible={showCreateCategory}
          onClose={() => setShowCreateCategory(false)}
          onConfirm={() => {
            // getCateList();
            onGetCustomizeOptions();
            setShowCreateCategory(false);
          }}
        />
      </div>
    );
  }
);

BaseInfoCardComponent.displayName = 'BaseInfoCardComponent';

BaseInfoCardComponent.defaultProps = {};

export default BaseInfoCardComponent;
