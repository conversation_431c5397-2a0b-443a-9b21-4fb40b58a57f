/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useRef, useState } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useMount, useUnmount } from 'ahooks';
import { Radio, Button, message } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { Context } from '@/components';
import { testPerm, checkPermission } from '@/utils/permission';
import {
  getCategories,
  customerCategoryList,
  getBrandOptions,
  getFreightOptionsCheckPerm,
  getUnitOptions,
  // getUnitFormulaOptions,
  getUnitFormulaOptionsByUnit,
  getPriceFormulaOptions,
  createSingleGoods,
  getEditGoodsInfo,
  // updateProduct,
  // clearProductCache,
  updatePmsGoodsSingleGoods,
  getGoodsPriceSimpleShelf,
  clearPmsCategorySpec,
  // getPmsCheckPriceManager,
  // getPmsCheckUnitManager,
  // getBaseTaxRateList,
  getBaseStandardList,
  getBaseTaxRateTemplateList,
} from '@/apis';
import type { GetBaseStandardListRes } from '@/apis';
import { GetCategoryResult as CategoryOptionType } from '@/apis/get-categories';
import { CustomerCategoryListResult as CustomizeOptionType } from '@/apis/base/customer-category-list';
import { ResultType as BrandOptionType } from '@/apis/system/get-brand-options';
import { ResultType as FreightOptionType } from '@/apis/base/get-freight-options';
import { ResultType as UnitOptionType } from '@/apis/base/get-unit-options';
import { ResultType as UnitFormulaOptionType } from '@/apis/base/get-unit-formula-options';
import { ResultType as PriceFormulaOptionType } from '@/apis/base/get-price-formula-options';
import { StateType, MultipleDataType } from '@/apis/pms/type';
import { useTranslation } from 'react-i18next';
import { initCategoryOptions } from '@/utils/utils';
import { getCategoryIds } from '../../utils';
// import CustomCodeSet from '../../utils/custom-code-set';
import BaseInfoCardComponent, { BaseInfoCardInstance } from './base-info-card';
import SpecificationSingle from './specification-single';
import SpecificationMultiple from './specification-multiple';
import type { SpecificationMultipleInstance } from './specification-multiple';
import type { SpecificationSingleInstance } from './specification-single';

import DescriptionCardComponent from '../../components/description-card';
import styles from './index.module.less';

export type TaxRaTeOption = { id: number; taxRate: number };

const { Head } = Context;

function SinglePage() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const params = useParams();
  const [searchParams] = useSearchParams();
  const cId = searchParams.get('cId');
  const tId = searchParams.get('tId');
  const id = Number(params.id) || 0;
  const headTitle = useRef([
    { title: t('goods_management'), to: '/goods/manage/index' },
    id ? t('edit_product') : t('add_single_product'),
  ]);
  const [loading, setLoading] = useState(false);
  const [categoryOptions, setCategoryOptions] = useState<CategoryOptionType[]>([]);
  const [customizeOptions, setCustomizeOptions] = useState<CustomizeOptionType[]>([]);
  const [brandOptions, setBrandOptions] = useState<BrandOptionType[]>([]);
  const [taxRateOptions, setTaxRateOptions] = useState<{ label: string; value: number }[]>([]);
  const [attrOptions, setAttrOptions] = useState<GetBaseStandardListRes[]>([]);
  const [freightOptions, setFreightOptions] = useState<FreightOptionType[]>([]);
  const [unitOptions, setUnitOptions] = useState<UnitOptionType[]>([]);
  const [unitFormulaOptions, setUnitFormulaOptions] = useState<UnitFormulaOptionType[]>([]);
  const [priceFormulaOptions, setPriceFormulaOptions] = useState<PriceFormulaOptionType[]>([]);
  // const [customCodeSet, setCustomCodeSet] = useState<CustomCodeSet | null>(null);
  const specificationSingleRef = useRef<SpecificationSingleInstance | null>(null);
  const baseInfoCardRef = useRef<BaseInfoCardInstance | null>(null);
  const specificationMultipleRef = useRef<SpecificationMultipleInstance | null>(null);
  const [isValidate, setIsValidate] = useState(false);
  const submitLoading = useRef(false);
  const singleMarketPrice = useRef<number | null | string>(0);
  const [state, setState] = useState<StateType & { standardList1: any[] }>({
    productType: 0, // 0商品 1服务
    childSheetId: 0,

    image: '',
    imageList: ['', '', '', '', '', ''],
    videoList: [],
    categoryId: 0,
    categoryIds: [],
    name: '',
    standardList1: [],
    customerCategoryCodeSet: [],
    customizedId: 0,
    customizedIds: [],
    brandId: 0,
    brandName: '',
    unit: '',
    unitNum: 6,
    priceNum: 6,
    isUnitFormula: false,
    unitTemplateId: 0,
    unitTemplateName: '',
    saleUnitOptions: [],
    isPriceFormula: false,
    formulaId: 0,
    formula: '',
    formulaName: '',
    formulaList: [],
    templateFormulaArgsList: [],

    standardType: 0, // 0单一规格 1多规格
    marketPrice: null,
    saleGroup: null,
    saleUnit: '',
    freightTemplateId: null,
    costPrice: null,
    skuCode: '',
    barsCode: '',
    expirationDate: {
      value: 0,
      type: 1,
    },
    isAloneBuy: 1,
    detailList: [],
    multipleStandard: {
      standardList: [],
      total: 0,
    },
    condition: {},
    status: 10,
    taxRate: 0,
    taxTemplateId: 0,
  });

  const onRadioChange = (e: RadioChangeEvent) => {
    const val = e.target.value;
    if (!val && !testPerm('M_001_002_001_002_001')) return;
    if (val && !testPerm('M_001_002_001_002_002')) return;

    // if (state.unit) {
    let { formulaList } = state;
    if (formulaList.length) {
      formulaList = formulaList?.map((item) => ({ ...item, value: null }));
    }
    if (!val) {
      const hasNullValue =
        formulaList?.filter((item) => item.value === null || item.value === '').length > 0;
      if ((!hasNullValue || !formulaList.length) && state.formulaId) {
        getGoodsPriceSimpleShelf({
          formulaId: state.formulaId,
          formulaList,
          standardList: [[{ name: t('base_specification'), value: t('base_generalSpec') }]],
          catId: state.categoryId,
          unit: state.unit,
        }).then((res) => {
          setState({ ...state, marketPrice: res.priceList[0], standardType: val, formulaList });
        });
      } else if (hasNullValue) {
        setState({
          ...state,
          marketPrice: null,
          standardType: val,
          formulaList,
        });
      } else {
        setState({
          ...state,
          marketPrice: singleMarketPrice.current,
          standardType: val,
          formulaList,
        });
      }
    } else {
      singleMarketPrice.current = state.marketPrice;
      setState({
        ...state,
        standardType: val,
        formulaList,
      });
    }
  };

  // 子组件改变父组件state
  const handleSetState = useCallback(
    (data: Partial<StateType>) => {
      const newState: any = state;
      Object.keys(data).forEach((key) => {
        if (key === 'multipleStandard') {
          newState[key] = { ...newState[key], ...data[key] };
        }
        // @ts-ignore
        newState[key] = data[key];
      });
      setState({ ...newState });
    },
    [state]
  );

  // 修改多规格state
  const handleSetMultipleStandard = useCallback(
    (data: Partial<MultipleDataType>) => {
      setState({
        ...state,
        multipleStandard: {
          ...state.multipleStandard,
          ...data,
        },
      });
    },
    [state]
  );

  // 获取单位选项（行业分类改变）
  const handleGetUnitOptions = useCallback((categoryId: number) => {
    getUnitOptions({ catId: categoryId }).then((res) => {
      setUnitOptions(res.list);
    });
  }, []);

  // 获取单位换算关系选项
  const handleGetUnitFormulaOptions = (unit: string, isUnitFormula: boolean) => {
    if (unit && isUnitFormula) {
      getUnitFormulaOptionsByUnit({ unitName: unit }).then((res) => {
        setUnitFormulaOptions(res.list || []);
      });
    }
  };

  // 获取动态价格公示选项
  const handleGetPriceFormulaOptions = useCallback(() => {
    if (priceFormulaOptions.length) return;
    getPriceFormulaOptions({ pageNo: 1, pageSize: 999 }).then((res) => {
      setPriceFormulaOptions(res.list);
    });
  }, [priceFormulaOptions]);

  // 点击将跳转对应的位置（锚点功能）
  const onAnchor = (anchorName: string) => {
    if (anchorName) {
      const anchorElement = document.getElementById(anchorName);
      if (anchorElement) {
        // block:表示滚动到锚点的顶部或者底部，start/end
        // behavior:表示滚动的效果，auto/smooth(滚动效果)
        anchorElement.scrollIntoView({ block: 'start', behavior: 'smooth' });
      }
    }
  };

  // 单个规格新增检验
  const singleValidateFields = () =>
    new Promise((resolve, reject) => {
      setIsValidate(true);
      let isErr = false;
      const images = state.imageList?.filter((item) => item) || [];
      if (!images.length) {
        isErr = true;
        message.error(t('base_addImage'));
        onAnchor('goodsBaseInfoCard');
        reject(state.imageList);
      }
      if (
        state.isPriceFormula &&
        state.formulaList?.filter((item) => item.value === '' || item.value === null).length > 0
      ) {
        isErr = true;
        onAnchor('goodsBaseInfoCard');
        message.error(t('base_setDynamicPriceFormula'));
        reject(state.formulaId);
      }
      if (
        state.isUnitFormula &&
        state.templateFormulaArgsList.filter((item) => item.value === '' || item.value === null)
          .length > 0
      ) {
        isErr = true;
        onAnchor('goodsBaseInfoCard');
        message.error(t('base_setUnitConversionTemplate'));
        reject(state.unit);
      }
      if (isErr) {
        baseInfoCardRef.current?.form.validateFields();
        specificationSingleRef.current?.form.validateFields();
      } else {
        const baseKeys = ['categoryIds', 'name', 'unit'];
        const singleKeys = ['marketPrice', 'saleGroup'];
        Promise.all([
          baseInfoCardRef.current?.form.validateFields(),
          specificationSingleRef.current?.form.validateFields(),
        ])
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            if (err.errorFields.length && err.errorFields[0].name?.[0]) {
              const errKey = err.errorFields[0].name?.[0] || '';
              if (baseKeys.includes(errKey)) {
                onAnchor('goodsBaseInfoCard');
              }
              if (singleKeys.includes(errKey)) {
                onAnchor('goodsSpecificationSingle');
              }
            }
            reject(err);
          });
      }
    });

  // 多个规格新增检验
  const multipleValidateFields = () =>
    new Promise((resolve, reject) => {
      setIsValidate(true);
      let isErr = false;
      const images = state.imageList?.filter((item) => item) || [];
      if (!images.length) {
        isErr = true;
        onAnchor('goodsBaseInfoCard');
        message.error(t('base_addImage'));
        reject(state.imageList);
      }
      if (isErr) {
        baseInfoCardRef.current?.form.validateFields();
      } else {
        const baseKeys = ['categoryIds', 'name', 'unit'];
        baseInfoCardRef.current?.form
          .validateFields()
          .then((res) => {
            resolve(res);
          })
          .catch((err) => {
            if (err.errorFields.length && err.errorFields[0].name?.[0]) {
              const errKey = err.errorFields[0].name?.[0] || '';
              if (baseKeys.includes(errKey)) {
                onAnchor('goodsBaseInfoCard');
              }
            }
            reject(err);
          });
      }
    });

  // 新增单个商品
  const createGoodsFn = (subType: 0 | 1) => {
    submitLoading.current = true;
    createSingleGoods({
      categoryId: state.categoryId,
      name: state.name,
      nameStandardList: state.nameStandardList || [],
      customerCategoryCodeSet: state.customerCategoryCodeSet.map((item) => item.value),
      standardType: state.standardType,
      productType: state.productType,
      type: subType,
      imageList: state.imageList.filter((item: string) => item),
      isAloneBuy: state.isAloneBuy,
      videoList: state.videoList,
      attributeList: state.attributeList?.filter(
        (item) => item.value !== '' && item.value !== undefined
      ),
      attributeLists: baseInfoCardRef.current?.attributeLists,
      ...(state.taxTemplateId
        ? { taxTemplateId: state.taxTemplateId, taxRate: state.taxRate }
        : {}),
      ...(state.brandId ? { brandId: state.brandId } : {}),
      ...(state.formulaId ? { formulaId: state.formulaId } : {}),
      ...(!state.standardType
        ? {
            unityStandardParam: {
              marketPrice: state.marketPrice,
              saleGroup: state.saleGroup,
              freightTemplateId: state.freightTemplateId || null,
              costPrice: state.costPrice,
              skuCode: state.skuCode,
              barsCode: state.barsCode,
              detailList: state.detailList,
              formulaList: state.formulaList,
              expirationDate: state.expirationDate,
              unit: state.unit,
              saleUnit: state.saleUnit,
              templateFormulaArgsList: state.templateFormulaArgsList,
              ...(state.unitTemplateId ? { unitTemplateId: state.unitTemplateId } : {}),
            },
          }
        : {}),
    })
      .then(() => {
        message.success(`${subType ? t('base_publishSuccess') : t('base_saveSuccess')}`);
        if (!checkPermission('M_001_001_001')) {
          message.error(t('base_noPermissionGoodsList'));
          navigate('/goods/manage/index');
        } else {
          navigate('/shop/goods/list');
        }
      })
      .finally(() => {
        submitLoading.current = false;
      });
  };

  // 修改单个商品
  const updateSingleGoodsFn = (subType: 0 | 1) => {
    submitLoading.current = true;
    const {
      name,
      nameStandardList,
      unit,
      brandId,
      formulaId,
      marketPrice,
      saleGroup,
      freightTemplateId,
      unitTemplateId,
      customizedId,
      barsCode,
      skuCode,
      costPrice,
      imageList,
      detailList,
      formulaList,
      templateFormulaArgsList,
      categoryId,
      isAloneBuy,
      videoList,
      customerCategoryCodeSet,
      expirationDate,
      childSheetId,
      taxRate,
      taxTemplateId,
    } = state;

    updatePmsGoodsSingleGoods({
      id,
      name,
      nameStandardList,
      unit,
      marketPrice,
      saleGroup: saleGroup || 0,
      costPrice: costPrice ?? null,
      skuCode: skuCode ?? null,
      barsCode,
      images: imageList.filter((item: string) => item),
      detailParams: detailList,
      categoryId,
      isAloneBuy,
      videoList,
      customizeCategoryCodeSet: customerCategoryCodeSet.map((item) => item.value),
      expirationDate,
      operationType: subType,
      saleUnit: state.saleUnit,
      attributeList: state.attributeList?.filter(
        (item) => item.value !== '' && item.value !== undefined
      ),
      attributeLists: baseInfoCardRef.current?.attributeLists || [],
      ...(taxTemplateId ? { taxTemplateId, taxRate } : {}),
      ...(formulaList && formulaId ? { formulaNameList: formulaList } : {}),
      ...(customizedId ? { customizeId: customizedId } : {}),
      ...(templateFormulaArgsList && unitTemplateId ? { templateFormulaArgsList } : {}),
      ...(brandId ? { brandId } : {}),
      ...(formulaId ? { formulaId } : {}),
      ...(freightTemplateId ? { freightTemplateId } : {}),
      ...(unitTemplateId ? { unitTemplateId } : {}),
      ...(childSheetId ? { childSheetId } : {}),
    })
      .then(() => {
        message.success(`${subType ? t('base_publishSuccess') : t('base_saveSuccess')}`);
        if (!checkPermission('M_001_001_001')) {
          message.error(t('base_noPermissionGoodsList'));
          navigate('/goods/manage/index');
        } else {
          navigate('/shop/goods/list');
        }
      })
      .finally(() => {
        submitLoading.current = false;
      });
  };

  const checkIsSetVar = () => {
    let isSetVar = false;
    if (state.formulaList?.length > 0 && state.isPriceFormula) {
      isSetVar =
        state.formulaList?.filter((item) => item.value !== null && item.value !== '').length === 0;
    }
    return isSetVar;
  };

  const onSubmit = (subType: 0 | 1) => {
    if (subType) {
      if (!testPerm('M_001_002_001_007_002')) {
        return;
      }
    } else if (subType === 0 && !testPerm('M_001_002_001_007_001')) {
      return;
    }
    if (!submitLoading.current) {
      // customCodeSet?.getCustomCodeSet(state.customerCategoryCodeSet.flat(), customizeOptions);
      if (id) {
        if (state.standardType === 1) {
          multipleValidateFields().then(() => {
            updateSingleGoodsFn(subType);
          });
        } else {
          singleValidateFields().then(() => {
            if (!checkIsSetVar()) {
              updateSingleGoodsFn(subType);
            } else {
              message.error(t('base_enterPriceFormulaVariable'));
            }
          });
        }
      } else if (state.standardType === 1) {
        multipleValidateFields().then(() => {
          // if (!specificationMultipleRef.current?.validateFields().length) {
          createGoodsFn(subType);
          // }
        });
      } else {
        singleValidateFields().then(() => {
          if (!checkIsSetVar()) {
            createGoodsFn(subType);
          } else {
            message.error(t('base_enterPriceFormulaVariable'));
          }
        });
      }
    }
  };

  const getCustomizeOptions = () => {
    customerCategoryList().then((res) => {
      setCustomizeOptions(res.list || []);
    });
  };

  const onGetFreightOptions = () => {
    getFreightOptionsCheckPerm().then((res) => {
      setFreightOptions(res.list);
    });
  };

  const onGetCategories = () =>
    new Promise((resolve, reject) => {
      getCategories()
        .then((res) => {
          const cateOptions = initCategoryOptions(res.list || []);
          setCategoryOptions(cateOptions);
          resolve(cateOptions);
        })
        .catch((err) => {
          reject(err);
        });
    });

  const onGetCustomerCategoryList = () =>
    new Promise((resolve, reject) => {
      customerCategoryList()
        .then((res) => {
          const customOptions = res.list || [];
          setCustomizeOptions(customOptions);
          resolve(customOptions);
        })
        .catch((err) => {
          reject(err);
        });
    });

  const onGetBrandOptions = () =>
    new Promise((resolve, reject) => {
      getBrandOptions()
        .then((res) => {
          setBrandOptions(res.list || []);
          resolve(res.list || []);
        })
        .catch((err) => {
          reject(err);
        });
    });

  const onGetTaxRateList = () =>
    new Promise((resolve, reject) => {
      getBaseTaxRateTemplateList()
        .then((res) => {
          const list = (res.list || [])?.map((item) => ({
            label: `${item.taxRate}%`,
            value: item.id,
          }));
          setTaxRateOptions(list);
          resolve(list);
        })
        .catch((err) => {
          reject(err);
        });
    });

  const onGetStandardList = (
    catId: number | null,
    attributeList = state.attributeList,
    stateVal = state
  ) => {
    if (catId) {
      getBaseStandardList({ catId, useWay: 4, source: 1 }).then((res) => {
        setAttrOptions(res.list || []);
        const attributeData = attributeList || [];
        const nameList = attributeData?.map((item) => item.name) || [];
        res.list?.forEach((item) => {
          item?.standardDetail?.forEach((standardItem) => {
            const index = nameList.indexOf(standardItem?.detailName || '');
            if (index === -1) {
              attributeData.push({ name: standardItem.detailName || '', value: '' });
            }
          });
        });
        setState({ ...stateVal, attributeList: attributeData });
      });
    } else {
      setAttrOptions([]);
    }
  };

  useMount(async () => {
    clearPmsCategorySpec({});
    if (!checkPermission('M_001_002_001')) {
      message.error(t('base_noPermissionSingleProduct'));
      return;
    }
    if (!checkPermission('M_001_002_001_002_001')) {
      message.error(t('base_noPermissionSingleSpec'));
    }
    let cateOptions: CategoryOptionType[] = [];
    // let customOptions: CustomizeOptionType[] = [];
    let priceOptions: PriceFormulaOptionType[] = [];
    try {
      if (id) {
        await getPriceFormulaOptions({ pageNo: 1, pageSize: 999 }).then((res) => {
          priceOptions = res.list;
          setPriceFormulaOptions(priceOptions);
        });
      }
    } catch (err) {
      window.console.warn(err);
    }

    Promise.allSettled([
      onGetCategories(),
      onGetCustomerCategoryList(),
      onGetBrandOptions(),
      onGetFreightOptions(),
      onGetTaxRateList(),
    ])
      .then((arr) => {
        arr.forEach((item, index) => {
          if (item.status === 'fulfilled' && index === 0) {
            cateOptions = item.value as CategoryOptionType[];
          }
          // if (item.status === 'fulfilled' && index === 1) {
          //   customOptions = item.value as CustomizeOptionType[];
          // }
        });
      })
      .finally(() => {
        if (id) {
          setLoading(true);
          getEditGoodsInfo({ id })
            .then((res) => {
              console.warn(res);

              const imgList = ['', '', '', '', '', ''];
              res?.imagesList?.forEach((item, index) => {
                imgList[index] = item;
              });
              let formatFormula = '';
              const priceFormulas = priceOptions.filter((item) => item.id === res.formulaId);
              if (priceFormulas.length) {
                // formula = priceFormulas[0].formula;
                formatFormula = priceFormulas[0].formatFormula;
              }
              const stateValue = {
                image: res?.imagesList?.[0] || '',
                imageList: imgList,
                productType: res.type, // 0商品 1服务
                childSheetId: 0,
                videoList: res.videoList,
                standardList1: res.standardList || [],
                categoryId: res.categoryId,
                categoryIds: res.categoryId ? getCategoryIds(res.categoryId, cateOptions) : [],
                name: res.name,
                customerCategoryCodeSet: res.customizeCategoryList?.map((item) => ({
                  label: item.name,
                  value: item.code,
                })),
                customizedId: res.customizedId,
                // customizedIds: res.customizedId
                //   ? getCustomizedIds(res.customizedId, customizeOptions)
                //   : [],
                customizedIds: [res.customizedId],
                brandId: res.brandId,
                brandName: res.brandName,
                unit: res.unit,
                unitNum: res.unitNum,
                priceNum: res.priceNum,
                unitTemplateId: res.unitTemplateId,
                unitTemplateName: res.unitTemplateName,
                saleUnitOptions: [],
                formulaId: res.formulaId || 0,
                formulaName: res.formulaName,
                // formula,
                formatFormula,
                formulaList: res.formulaNameList || [],
                templateFormulaArgsList: res.templateFormulaNameList || [],

                standardType: 0 as 0 | 1, // 0单一规格 1多规格
                marketPrice: res.marketPrice,
                saleGroup: res.saleGroup,
                saleUnit: res.saleUnit,
                freightTemplateId: res.freightTemplateId,
                costPrice: res.costPrice,
                skuCode: res.skuCode,
                barsCode: res.barsCode,
                expirationDate: res.expirationDate || {
                  value: null,
                  type: 1,
                },
                isAloneBuy: res.isAloneBuy,
                detailList: res?.detailList || [],
                isUnitFormula: Boolean(res.unitTemplateId),
                isPriceFormula: Boolean(res.formulaId),
                multipleStandard: {
                  standardList: [],
                  total: 0,
                },
                condition: {},
                status: res.status,
                taxRate: res.taxRate,
                taxTemplateId: res.taxTemplateId,
                attributeList: res?.attributeList || [],
              };
              setState(stateValue);

              handleGetUnitOptions(res.categoryId);
              handleGetUnitFormulaOptions(res.unit, Boolean(res.unitTemplateId));
              onGetStandardList(res.categoryId, stateValue.attributeList, stateValue);
            })
            .finally(() => {
              setLoading(false);
            });
        }
      });
    if (!id && (cId || tId)) {
      setState({ ...state, categoryId: Number(cId || 0), childSheetId: Number(tId || 0) });
    }
  });

  useUnmount(() => {
    // clearProductCache({
    //   type: state.childSheetId ? 1 : 0,
    //   childSheetId: state.childSheetId || null,
    //   categoryId: state.categoryId,
    //   insertType: 1,
    // });
  });

  return (
    <Context
      head={<Head title={headTitle.current} />}
      foot={
        <>
          <Button
            onClick={() => {
              navigate(-1);
            }}
          >
            {t('public_cancel')}
          </Button>
          <Button type="primary" onClick={() => onSubmit(0)}>
            {t('public_save')}
          </Button>
          {state.status !== 50 ? (
            <Button type="primary" onClick={() => onSubmit(1)}>
              {t('public_putOn')}
            </Button>
          ) : null}
        </>
      }
      theme={null}
      loading={loading}
      permission={{ code: 'M_001_002_001', newLogic: true }}
    >
      <BaseInfoCardComponent
        id={id}
        ref={baseInfoCardRef}
        categoryOptions={categoryOptions}
        customizeOptions={customizeOptions}
        brandOptions={brandOptions}
        taxRateOptions={taxRateOptions}
        unitOptions={unitOptions}
        unitFormulaOptions={unitFormulaOptions}
        priceFormulaOptions={priceFormulaOptions}
        attrOptions={attrOptions}
        isValidate={isValidate}
        // @ts-ignore
        standardList1={state.standardList1}
        {...state}
        attributeList={state.attributeList}
        formatFormula={state.formatFormula || ''}
        formulaName={state.formulaName || ''}
        onSetState={handleSetState}
        onGetUnitOptions={handleGetUnitOptions}
        onGetUnitFormulaOptions={(unit: string, isUnitFormula: boolean) => {
          if (unit) {
            handleGetUnitFormulaOptions(unit, isUnitFormula);
          }
        }}
        onGetPriceFormulaOptions={handleGetPriceFormulaOptions}
        onGetCustomizeOptions={getCustomizeOptions}
        onUnitChange={(val) => {
          setState({ ...state, unit: val });
          setTimeout(() => {
            specificationMultipleRef.current?.updateBaseUnit(val);
          }, 100);
        }}
        onPriceFormulaChange={(val) => {
          if (state.standardType === 1) {
            specificationMultipleRef.current?.updatePrice(val, state.formulaId);
          }
          // setState({ ...state, marketPrice: val });
        }}
        onGetStandardList={onGetStandardList}
      />
      <div className={styles.card}>
        <div className={styles.cardTitle}>{t('base_productSpecifications')}</div>
        {!id && (
          <Radio.Group
            onChange={onRadioChange}
            value={state.standardType}
            className={styles.radioGroup}
          >
            <Radio value={0}>{t('base_singleSpecification')}</Radio>
            <Radio value={1}>{t('base_multipleSpecifications')}</Radio>
          </Radio.Group>
        )}
        {state.standardType ? (
          <SpecificationMultiple
            ref={specificationMultipleRef}
            freightOptions={freightOptions}
            unitOptions={unitOptions}
            isAddSpecifications={Boolean(state.categoryId && state.unit)}
            isValidate={isValidate}
            {...state}
            attributeList={state.attributeList || []}
            unit={state.unit}
            formatFormula={state.formatFormula || ''}
            customerCategoryCodeSet={state.customerCategoryCodeSet || []}
            nameStandardList={state.nameStandardList || []}
            onSetMultipleStandard={handleSetMultipleStandard}
            onGetUnitFormulaOptions={(unit: string) => {
              if (unit) {
                handleGetUnitFormulaOptions(unit, true);
              }
            }}
            onGetFreightOptions={onGetFreightOptions}
          />
        ) : (
          <SpecificationSingle
            ref={specificationSingleRef}
            categoryId={state.categoryId}
            freightOptions={freightOptions}
            priceNum={state.priceNum}
            unitNum={state.unitNum}
            // isAloneBuy={state.isAloneBuy}
            marketPrice={state.marketPrice}
            saleGroup={state.saleGroup}
            freightTemplateId={state.freightTemplateId}
            costPrice={state.costPrice}
            skuCode={state.skuCode}
            barsCode={state.barsCode}
            expirationDate={state.expirationDate}
            isPriceFormula={state.isPriceFormula}
            formulaId={state.formulaId}
            formulaList={state.formulaList}
            formatFormula={state.formatFormula || ''}
            formulaName={state.formulaName}
            saleUnit={state.saleUnit}
            unit={state.unit}
            unitTemplateId={state.unitTemplateId}
            onSetState={handleSetState}
            onGetFreightOptions={onGetFreightOptions}
          />
        )}
      </div>
      {!state.standardType && (
        <DescriptionCardComponent detailList={state.detailList} onSetState={handleSetState} />
      )}
      <div style={{ height: '58px' }} />
    </Context>
  );
}

SinglePage.displayName = 'SinglePage';

SinglePage.defaultProps = {};

export default SinglePage;
