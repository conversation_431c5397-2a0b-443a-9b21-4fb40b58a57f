@import 'styles/mixins/mixins';

.wrap {
  & + & {
    &::before {
      content: '';
      display: block;
      height: 1px;
      margin: 24px 0;
      background-color: #f3f3f3;
    }
  }
}

.tableTitle {
  font-weight: bold;
  height: 22px;
  line-height: 22px;
}

.titleMsg {
  color: #888b98;
  font-size: 12px;
  font-weight: normal;
}

.hideColumnBtn {
  font-size: 20px;
  font-weight: normal;
  float: right;
  cursor: pointer;
}

.hideColumnTags {
  margin-top: 8px;
  cursor: pointer;
}

.hideColumnCheckboxGroup {
  width: 184px;

  :global {
    .ant-checkbox-wrapper {
      display: flex;
      height: 32px;
      margin-right: 0 !important;
      padding: 8px 0 8px 8px;
      align-items: center;
      border-radius: 10px;

      &:hover {
        background: rgb(217 238 255 / 30%);
      }

      & + & {
        margin-top: 4px;
      }
    }

    .ant-checkbox-wrapper-checked {
      background: rgb(217 238 255 / 50%);
    }
  }
}

.table {
  margin: 12px 0;

  :global {
    & .ant-table {
      font-size: 12px;
    }

    & .ant-table-cell {
      padding: 9px 6px;
      border-bottom: none;
    }

    & td.ant-table-cell {
      padding-top: 10px;
      padding-bottom: 10px;
      border-bottom: none !important;
    }

    & .ant-table-cell:first-child {
      padding-left: 20px;
    }

    & .ant-table-cell:last-child {
      padding-right: 20px;
    }

    & th.ant-table-cell::before {
      display: none;
    }

    & .ant-input-number {
      font-size: 12px;
      width: 80px;
    }

    // & .ant-input-number input {
    //   padding: 0 5px;
    //   text-align: center;
    // }

    // & .ant-input-number + span {
    //   margin-left: 4px;
    // }

    & .ant-table-filter-column {
      justify-content: center;
    }

    & .ant-table-filter-column .ant-table-column-title {
      flex: none;
      max-width: calc(100% - 24px);
    }

    & .ant-table-filter-trigger {
      margin-left: 0;
    }

    & .ant-table-selection-col {
      width: 41px;
      min-width: 41px;
      max-width: 41px;
    }

    & td.ant-table-cell-ellipsis > div {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      white-space: normal;
      word-break: normal;
    }

    & th.ant-table-cell {
      font-size: 600;
    }

    & th.ant-table-cell:last-child {
      border-bottom-right-radius: 10px;
    }

    & th.ant-table-cell:first-child {
      border-bottom-left-radius: 10px;
    }

    & td.ant-table-cell:first-child {
      border-bottom-left-radius: 10px;
      border-top-left-radius: 10px;
    }

    & td.ant-table-cell:last-child {
      border-bottom-right-radius: 10px;
      border-top-right-radius: 10px;
    }

    & .ant-table-row:hover td.ant-table-cell {
      background: rgb(217 238 255 / 30%);
    }

    & .ant-table-row {
      height: 72px;
    }

    .ant-input-number-handler-wrap {
      display: none !important;
    }
  }
}

// :global{
//   .ant-dropdown {
//     .ant-table-filter-dropdown {
//       min-width: 200px;
//       .ant-table-filter-dropdown-btns {
//         border-bottom-left-radius: 10px;
//         border-bottom-right-radius: 10px;
//       }
//       .ant-btn-link {
//         background: #f3f3f3;
//         color: #040919;
//       }
//     }
//   }
// }

.tagBox {
  border: none;

  :global {
    .ant-tag-close-icon {
      color: #040919;
      margin-left: 8px;
    }
  }
}

.tableFooter {
  display: flex;
  height: 48px;
  justify-content: space-between;
  align-items: center;
}

.checkboxAll {
  margin-left: 20px;
}

.selectedCount {
  margin-left: 12px;
}

.batchDelBtn {
  color: #ea1c26;
  margin-left: 12px;
  cursor: pointer;
}

.batchBtns {
  color: #008cff;
  margin-left: 16px;
  cursor: pointer;
}

.editIcon {
  color: #999eb2;
  margin-left: 4px;
  cursor: pointer;
}

.moveText {
  color: #008cff;
  cursor: pointer;
}

.lookImageText {
  cursor: pointer;
}

.addImageText {
  color: #008cff;
  cursor: pointer;
}

.setMutUnitVar {
  color: #008cff;
  display: flex;
  margin-top: 8px;
  justify-content: center;
  cursor: pointer;
}

.setMutUnitVarErr {
  color: #ea1c26;
  margin-top: 8px;
  cursor: pointer;
}

.required {
  color: #ea1c26;
}

.costPriceSuffix {
  max-width: 30px;
  word-break: break-all;
  cursor: pointer;
  .text-overflow();
}

.costPriceInputBox {
  display: flex;
  justify-content: center;

  .costPriceInput {
    min-width: 80px;
  }

  .costPriceUnit {
    max-width: 60px;
    height: 92px;
    line-height: 92px;
    margin-left: 4px;
    cursor: pointer;
    .text-overflow(2);
  }
}

.saleUnitSelect {
  min-width: 60px;
  max-width: 80px;

  :global {
    .ant-select-selector {
      padding: 0;
    }
  }
}

.bigSelect {
  :global {
    .ant-select-dropdown {
      min-width: 160px !important;
    }
  }
}

.bigSelectAddBtn {
  color: #008cff;
  display: flex;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.unitTemplateBox {
  width: 100%;
}

.templateBox {
  width: 100%;
}

.costPriceBox {
  display: flex;
  align-items: center;
}

.marketPrice {
  color: #040919;

  :global {
    .ant-input {
      color: #040919;
    }
  }
}

.marketPriceError {
  color: #ea1c26;

  :global {
    .ant-input {
      color: #ea1c26;
    }
  }
}
