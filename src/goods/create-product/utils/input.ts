const createPrecisionRegExp = (precision = 0) => {
  const regVar = '\\d';
  // let reg = new RegExp('^(-)*(\\d+).*$');
  let reg = /^(-)*(\d+).*$/;
  if (precision) {
    let precisionVar = '';
    for (let index = 0; index < precision; index += 1) {
      precisionVar += regVar;
    }
    reg = new RegExp(`^(-)*(\\d+)\\.(${precisionVar}).*$`);
  }
  return reg;
};

export const limitDecimalsF = (
  value: number | null | string | undefined,
  precision = 0
  // integerLength = 8
) => {
  const reg = createPrecisionRegExp(precision);
  // const maxLength = precision ? integerLength + integerLength + 1 : integerLength;
  if (value === null || value === undefined) {
    return '';
  }
  const val = `${value}`
    // replace(/\B(?=(\d{3})+(?!\d))/g, ',').
    .replace(reg, precision ? '$1$2.$3' : '$1$2');

  return val;
};

export const limitDecimalsF$ = (
  value: number | null | string | undefined,
  precision = 0
  // integerLength = 8
) => {
  const reg = createPrecisionRegExp(precision);
  return value === null || value === undefined
    ? null
    : `￥ ${value}`
        // .replace(/\B(?=(\d{3})+(?!\d))/g, ',')
        .replace(reg, precision ? '$1$2.$3' : '$1$2');
};

export const limitDecimalsP = (
  value: number | null | string | undefined,
  precision = 0,
  integerLength = 8,
  valueType = 'string',
  max = 99999999
) => {
  const reg = createPrecisionRegExp(precision);
  // const maxLength = precision ? integerLength + integerLength + 1 : integerLength;

  if (value === null || value === undefined) {
    return null;
  }
  let val: number | string = String(value)
    .replace(/￥\s?|(,*)/g, '')
    .replace(reg, precision ? '$1$2.$3' : '$1$2');
  if (val) {
    const valNum = Number(val);
    if (Number(val) > max) {
      return max;
    }
    if (val.includes('.') && val[val.length - 1] === '.') {
      val = val.slice(0, -1);
    }
    if (!val.includes('.')) {
      if (String(valNum).length > integerLength) {
        val = String(valNum).slice(0, integerLength);
      }
    } else {
      // if (String(valNum).length > maxLength) {
      //   val = String(valNum).slice(0, maxLength);
      // }

      const splitArr = String(val).split('.');
      let integer = splitArr[0];
      let decimal = splitArr[1];

      if (integer.length > integerLength) {
        integer = integer.slice(0, integerLength);
      }
      if (decimal.length > precision) {
        decimal = decimal.slice(0, precision);
      }
      val = `${integer}.${decimal}`;
    }

    if (valueType === 'number') {
      val = valueType === 'number' ? Number(val) : String(val);
    }
  }

  return val ?? null;
};
