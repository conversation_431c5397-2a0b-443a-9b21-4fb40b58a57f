import { CustomerCategoryListResult as CustomizeOptionType } from '@/apis/base/customer-category-list';
import i18n from 'i18next';

const fingerboard = [
  { id: 1, label: '0', value: '0' },
  { id: 2, label: '1', value: '1' },
  { id: 3, label: '2', value: '2' },
  { id: 4, label: '3', value: '3' },
  { id: 5, label: '4', value: '4' },
  { id: 6, label: '5', value: '5' },
  { id: 7, label: '6', value: '6' },
  { id: 8, label: '7', value: '7' },
  { id: 9, label: '8', value: '8' },
  { id: 10, label: '9', value: '9' },
  { id: 11, label: '10', value: '10' },
  { id: 12, label: i18n.t('public_WanNum'), value: 'Int' },
  { id: 13, label: '+', value: '+' },
  { id: 14, label: '-', value: '-' },
  { id: 15, label: '*', value: '*' },
  { id: 16, label: '/', value: '/' },
  { id: 17, label: '(', value: '(' },
  { id: 18, label: ')', value: ')' },
  { id: 19, label: '.', value: '.' },
];

// 查找分类ids
const getCategoryIds = (id: number, options: any[]): number[] => {
  let categoryIds = [] as number[];
  if (id) {
    const fn = (arr: any[], ids: number[]) => {
      for (let i = 0; i < arr.length; i += 1) {
        const { value, children } = arr[i];
        const nowIds = [...ids, value];
        if (value === id) {
          categoryIds = [...nowIds];
          return true;
        }
        if (children && children.length) {
          const result = fn(children, nowIds);
          if (result) return true;
        }
      }
      return false;
    };
    fn(options, []);
  }
  return categoryIds;
};

const getCustomCodeSet = (list: string[], options: CustomizeOptionType[]) => {
  const codeObject: { [key: string]: string[] } = {};
  const recursiveFn = (
    codes: string[],
    optionArr: CustomizeOptionType[],
    preItem: CustomizeOptionType | null
  ) => {
    optionArr.forEach((item) => {
      const code = item.code || '';
      if (codes.includes(code)) {
        if (preItem) {
          const preCode = preItem?.code || '';
          if (codeObject[preCode]?.length) {
            if (!codeObject[preCode].includes(preCode)) {
              codeObject[preCode].push(preCode);
            }
            if (preItem?.children?.length === codeObject[preCode].length) {
              codeObject[preCode] = codeObject[preCode].slice(0, 1);
            } else {
              codeObject[preCode].push(code);
            }
          } else {
            codeObject[preCode] = [preCode, code];
          }
        } else {
          codeObject[code] = [code];
        }
      }
      if (item?.children?.length) {
        recursiveFn(codes, item?.children, item);
      }
    });
  };
  recursiveFn(list, options, null);
  return Object.values(codeObject);
};

export function getTreeDepth(data: any[]) {
  if (!Array.isArray(data)) {
    return 0;
  }

  let maxDepth = 0;
  data.forEach((item) => {
    const depth = getTreeDepth(item);
    if (depth > maxDepth) {
      maxDepth = depth;
    }
  });

  return maxDepth + 1;
}

export { fingerboard, getCategoryIds, getCustomCodeSet };
