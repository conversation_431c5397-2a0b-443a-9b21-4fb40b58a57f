import { CustomerCategoryListResult as CustomizeOptionType } from '@/apis/base/customer-category-list';

export default class CustomCodeSet {
  selected: string[];

  options: CustomizeOptionType[];

  codeObject: { [key: string]: string[] } = {};

  accessLevelList: CustomizeOptionType[] = [];

  accessLevelCodeList: string[] = [];

  constructor(selected: string[] = [], options: CustomizeOptionType[] = []) {
    this.selected = selected;
    this.options = options;
  }

  private recursiveFn = (codes: string[], optionArr: CustomizeOptionType[], groupKey = '') => {
    const { codeObject } = this;
    optionArr.forEach((item) => {
      const code = item.code || '';
      if (codes.includes(code) && codeObject[groupKey] && code.indexOf(groupKey) === 0) {
        if (!codeObject[groupKey].length) {
          codeObject[groupKey].push(groupKey);
        }
        if (!codeObject[groupKey].includes(code)) {
          codeObject[groupKey].push(code);
        }
      }
      if (item?.children?.length) {
        this.recursiveFn(codes, item?.children, groupKey);
      } else if (item.code && !this.accessLevelCodeList.includes(item.code)) {
        this.accessLevelList.push(item);
        this.accessLevelCodeList.push(item.code);
      }
    });
    this.codeObject = codeObject;
  };

  private recursiveForDepthFirst = (
    codes: string[],
    optionArr: CustomizeOptionType[],
    groupKey = ''
  ) => {
    optionArr.forEach((item) => {
      if (!item.children || !item.children.length || item.code?.indexOf(groupKey) !== 0) return;
      this.recursiveForDepthFirst(codes, item.children);
      // 判断是否下级都选中
      const isAllSelected = item.children?.every((item2) => codes.includes(item2.code || ''));
      if (isAllSelected) {
        const childCodes = item.children.map((childItem) => childItem.code || '');
        // 删掉下级只保留上级
        childCodes.forEach((codeItem) => {
          if (item) {
            const index = codes.indexOf(codeItem);
            if (index !== -1) {
              codes.splice(index, 1);
            }
          }
        });
      }
    });
    return codes;
  };

  getCustomCodeSet(selected = this.selected, options = this.options) {
    options?.forEach((item) => {
      this.codeObject[item.code || ''] = [];
      this.recursiveFn(selected, options, item.code || '');
    });
    const { codeObject } = this;
    const codeObj: { [key: string]: string[] } = {};
    // 清空value为空数组的数据
    // eslint-disable-next-line no-restricted-syntax
    for (const key in codeObject) {
      if (Object.prototype.hasOwnProperty.call(codeObject, key)) {
        const value = codeObject[key];
        if (value.length) {
          codeObj[key] = value;
        }
      }
    }
    this.codeObject = codeObj;
    const codeSetList: string[][] = [];
    const codeArr = Object.values(this.codeObject);
    //
    codeArr.forEach((codeItem) => {
      const codes = this.recursiveForDepthFirst(codeItem, options, codeItem[0]);
      codeSetList.push(codes);
    });

    // return Object.values(this.codeObject);
    return codeSetList;
  }

  // eslint-disable-next-line class-methods-use-this
  getAccessLevelValueList(codes: string[][]) {
    const codeList: string[] = [];
    codes.forEach((item) => {
      codeList.push(item[item.length - 1]);
    });
    return codeList;
  }

  // getAccessLevelValueList(codes: string[]) {
  //   const accessLevelCodes = this.accessLevelList.map((item) => item.code || '');

  //   return codes.filter((item) => accessLevelCodes.includes(item));
  // }

  private recursiveFn2(
    codes: string[],
    optionArr: CustomizeOptionType[],
    preItem: CustomizeOptionType | null
  ) {
    const codeObject: { [key: string]: string[] } = {};
    optionArr.forEach((item) => {
      const code = item.code || '';
      if (codes.includes(code)) {
        if (preItem) {
          const preCode = preItem?.code || '';
          if (codeObject[preCode]?.length) {
            if (!codeObject[preCode].includes(preCode)) {
              codeObject[preCode].push(preCode);
            }
            if (preItem?.children?.length === codeObject[preCode].length) {
              codeObject[preCode] = codeObject[preCode].slice(0, 1);
            } else {
              codeObject[preCode].push(code);
            }
          } else {
            codeObject[preCode] = [preCode, code];
          }
        } else {
          codeObject[code] = [code];
        }
      }
      if (item?.children?.length) {
        this.recursiveFn2(codes, item?.children, item);
      } else {
        this.accessLevelList.push(item);
      }
    });
    this.codeObject = codeObject;
  }

  getCustomCodeSet2(selected = this.selected, options = this.options) {
    this.recursiveFn2(selected, options, null);
    return Object.values(this.codeObject);
  }
}
