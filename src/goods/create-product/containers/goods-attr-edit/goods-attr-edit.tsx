import { Drawer } from '@/components';
import { DrawerProps, Input, message } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { updateStandardDetailName } from '@/apis';
import { DrawerRefType } from '@/components/drawer/drawer';
import { isFunction } from 'lodash';
import styles from '../goods-attr-create/goods-attr-create.module.less';

export interface GoodsAttrEditProps extends DrawerProps {
  id: number;
  standardId: number;
  detailName: string;
  onConfirm: () => void;
}

function GoodsAttrEdit({ id, standardId, detailName, onConfirm, ...props }: GoodsAttrEditProps) {
  const { t } = useTranslation();
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [name, setName] = useState(detailName);

  const onClose = () => {
    if (isFunction(props.onClose)) {
      props.onClose();
    }
  };

  const onSubmit = () => {
    if (name === detailName) {
      message.success(t('goodsAttrCreate_updateSuccess'));
      onClose();
      return;
    }
    if (!name) {
      message.warning(t('goodsAttrCreate_enterAttrValue'));
      return;
    }
    updateStandardDetailName({
      id,
      standardId,
      detailName: name,
    }).then(() => {
      message.success(t('goodsAttrCreate_updateSuccess'));
      onConfirm();
      onClose();
    });
  };

  useEffect(() => {
    drawerRef.current.setIsChange(name !== detailName);
  }, [detailName, name]);

  useEffect(() => {
    if (props.visible) {
      setName(detailName);
    }
  }, [detailName, props.visible]);

  const footer = <Drawer.Footer onOk={onSubmit} onCancel={onClose} />;

  return (
    <Drawer
      title={t('goodsAttrEdit_editAttrValue')}
      {...props}
      className={styles.drawer}
      placement="bottom"
      footer={footer}
      ref={drawerRef}
      onClose={onClose}
    >
      <div className={styles.card}>
        <div className={styles.title}>{t('goodsAttrCreate_attrValue')}</div>
        <Input.TextArea
          value={name}
          placeholder={t('goodsAttrCreate_attrValue3')}
          maxLength={30}
          className={styles.input}
          onChange={(e) => setName(e.target.value)}
          onKeyDown={(e) => {
            // @ts-ignore
            if (e.key === ' ' && !e.target.value) {
              e.preventDefault();
            }
          }}
        />
      </div>
    </Drawer>
  );
}

export default GoodsAttrEdit;
