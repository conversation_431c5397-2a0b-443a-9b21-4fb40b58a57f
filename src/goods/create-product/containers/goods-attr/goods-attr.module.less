@import 'styles/mixins/mixins';

.card {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
}

.title {
  display: flex;
  margin-bottom: 20px;
  justify-content: space-between;
  align-items: center;
}

.titleName {
  display: flex;
}

.titleText {
  font-weight: 600;
  max-width: 120px;
  margin-right: 12px;
  .text-overflow(1);
}

.titleEdit {
  cursor: pointer;
}

.addBtn {
  color: #008cff;
  font-size: 12px;
  cursor: pointer;
}

.detail {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 8px;
}

.detailItem {
  color: #888b98;
  width: 94px;
  height: 30px;
  padding: 5px 16px;
  border: 1px solid #bfbfbf;
  border-radius: 8px;
  cursor: pointer;
}

.detailItemText {
  .text-overflow(1);
}

.noData {
  display: flex;
  height: 100%;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}
