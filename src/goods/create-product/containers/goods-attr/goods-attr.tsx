import { useEffect, useState } from 'react';
import { Drawer, Icon, Empty } from '@/components';
import { DrawerProps, message } from 'antd';
import { useRequest } from 'ahooks';
import { getBaseStandardList, GetBaseStandardListRes } from '@/apis';
import { isFunction } from 'lodash';
import { useTranslation } from 'react-i18next';
import goodsAttrCreate from '../goods-attr-create';
import GoodsAttrCreate from '../goods-attr-create/goods-attr-create';
import GoodsAttrEdit from '../goods-attr-edit/goods-attr-edit';
import styles from './goods-attr.module.less';

export interface GoodsAttrProps extends DrawerProps {
  catId: number;
  onConfirm: MultipleParamsFn<[val: GetBaseStandardListRes[]]>;
}

function GoodsAttr({ catId, onConfirm, ...props }: GoodsAttrProps) {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [visibleEdit, setVisibleEdit] = useState(false);
  const [list, setList] = useState<GetBaseStandardListRes[]>([]);
  const [currentInfo, setCurrentInfo] = useState({
    type: '',
    standardId: 0,
    name: '',
  });
  const [currentEditInfo, setCurrentEditInfo] = useState({
    id: 0,
    standardId: 0,
    detailName: '',
  });

  const { run } = useRequest(getBaseStandardList, {
    manual: true,
    onSuccess: (res) => {
      setList(res.list);
      onConfirm(res.list);
    },
  });

  const onEdit = (val: string, id: number, attrbute = '') => {
    setVisible(true);
    setCurrentInfo({
      type: val,
      standardId: id,
      name: attrbute,
    });
  };

  const onCreate = () => {
    if (list.length >= 20) {
      message.warning(t('goodsAttr_attrLimitWarning'));
      return;
    }
    goodsAttrCreate({
      catId,
      onConfirm: () => {
        run({ catId, useWay: 4, source: 1 });
      },
    });
  };

  const onEditAttr = (standardId: number, id: number, detailName: string) => {
    setCurrentEditInfo({
      id,
      standardId,
      detailName,
    });
    setVisibleEdit(true);
  };

  const onClose = () => {
    if (visible) {
      setVisible(false);
      return;
    }
    if (visibleEdit) {
      setVisibleEdit(false);
      return;
    }
    if (isFunction(props.onClose)) {
      props.onClose();
      setVisible(false);
    }
  };

  useEffect(() => {
    if (props.visible) {
      run({ catId, useWay: 4, source: 1 });
    }
  }, [run, props.visible, catId]);

  const extra = (
    <div
      className={styles.addBtn}
      role="button"
      tabIndex={0}
      onClick={onCreate}
      style={{
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
        width: '63px',
      }}
      title={t('goodsAttr_createNewAttr')}
    >
      {t('goodsAttr_createNewAttr')}
    </div>
  );

  return (
    <Drawer
      {...props}
      title={<div style={{ fontSize: '16px' }}>{t('goodsAttr_title', { count: list.length })}</div>}
      onClose={onClose}
      push={false}
      extra={extra}
    >
      {list &&
        !!list.length &&
        list.map((item) => (
          <div className={styles.card} key={item.id}>
            <div className={styles.title}>
              <div className={styles.titleName}>
                <span className={styles.titleText} title={item.attrbute}>
                  {item.attrbute}
                </span>
                <Icon
                  name="edit1"
                  color="#999EB2"
                  className={styles.titleEdit}
                  onClick={() => onEdit('name', item.id as number, item.attrbute)}
                />
              </div>
              <div
                role="button"
                tabIndex={0}
                className={styles.addBtn}
                onClick={() => onEdit('attr', item.id as number)}
              >
                {t('goodsAttr_addAttrValue')}
              </div>
            </div>
            <div className={styles.detail}>
              {item.standardDetail?.map((each) => (
                <div
                  key={each.id}
                  role="button"
                  tabIndex={0}
                  className={styles.detailItem}
                  title={each.detailName}
                  onDoubleClick={() =>
                    onEditAttr(item.id as number, each.id as number, each.detailName as string)
                  }
                >
                  <span className={styles.detailItemText}>{each.detailName}</span>
                </div>
              ))}
            </div>
          </div>
        ))}
      {(!list || !list.length) && (
        <div className={styles.noData}>
          <Empty />
        </div>
      )}
      <GoodsAttrCreate
        visible={visible}
        catId={catId}
        type={currentInfo.type}
        standardId={currentInfo.standardId}
        name={currentInfo.name}
        onConfirm={() => {
          run({ catId, useWay: 4, source: 1 });
        }}
        onClose={() => {
          setVisible(false);
        }}
      />
      <GoodsAttrEdit
        visible={visibleEdit}
        id={currentEditInfo.id}
        standardId={currentEditInfo.standardId}
        detailName={currentEditInfo.detailName}
        onConfirm={() => {
          run({ catId, useWay: 4, source: 1 });
        }}
        onClose={() => {
          setVisibleEdit(false);
        }}
      />
    </Drawer>
  );
}

export default GoodsAttr;
