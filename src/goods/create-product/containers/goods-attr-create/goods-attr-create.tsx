import { useEffect, useMemo, useState, ChangeEvent, useRef } from 'react';
import { DrawerProps, Input, message } from 'antd';
import { Drawer } from '@/components';
import { addStandardBase, addStandardDetail, updateStandardBase } from '@/apis';
import { isFunction } from 'lodash';
import { useTranslation } from 'react-i18next';
import { DrawerRefType } from '@/components/drawer/drawer';
import classNames from 'classnames';
import styles from './goods-attr-create.module.less';

export interface GoodsAttrCreateProps extends DrawerProps {
  type?: string;
  standardId?: number;
  name?: string;
  catId: number;
  onConfirm: () => void;
}

function GoodsAttrCreate({
  catId,
  type,
  standardId,
  name,
  onConfirm,
  ...props
}: GoodsAttrCreateProps) {
  const { t } = useTranslation();
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const [attrbute, setAttrbute] = useState(name || '');
  const [groupType] = useState(1);
  const [detailNameList, setDetailNameList] = useState<string[]>([]);
  const [isAttrLong, setIsAttrLong] = useState(false);

  // eslint-disable-next-line consistent-return
  const onChangeAttr = (val: string) => {
    const tag = val.replace(/ /g, '');
    // eslint-disable-next-line no-useless-escape
    const regIsNum = /^[0-9][0-9]*([\.][0-9]{1,6})?$/;
    if (tag.indexOf('\n') !== -1) {
      const attrbuteArr = tag.split('\n').filter((item) => item !== '');

      for (let i = 0; i < attrbuteArr.length; i += 1) {
        const element = attrbuteArr[i];
        if (element.length > 30 && groupType === 1) {
          message.warn(t('goodsAttrCreate_attrValueTooLong', { index: i + 1 }));
          setIsAttrLong(true);
          break;
        }
        if (!regIsNum.test(String(Number(element))) && groupType === 2) {
          message.warn(t('goodsAttrCreate_invalidNumber', { index: i + 1 }));
          break;
        }
      }
      setDetailNameList(attrbuteArr);
    } else if (tag.length > 30) {
      setIsAttrLong(true);
      return message.warn(t('goodsAttrCreate_attrValueTooLongSingle'));
    } else if (!regIsNum.test(String(Number(tag))) && groupType === 2) {
      if (tag) {
        message.warn(t('goodsAttrCreate_invalidNumberSingle'));
      }
      setDetailNameList([tag]);
      setIsAttrLong(false);
    } else if (tag) {
      setDetailNameList([tag]);
      setIsAttrLong(false);
    } else if (!tag) {
      setDetailNameList([]);
    }
  };

  const onHandleChangeName = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const { value: inputValue } = e.target;
    // 使用正则表达式只允许数字、汉字和字母
    const filteredValue = inputValue.replace(/[^'a-zA-Z0-9\u4E00-\u9FA5.]/g, '');
    setAttrbute(filteredValue);
  };

  const onClose = () => {
    if (isFunction(props.onClose)) {
      props.onClose();
    }
  };

  const checkNum = () => {
    if (isAttrLong) {
      if (detailNameList.length > 1) {
        const arrNum: number[] = [];
        detailNameList.forEach((item, index) => {
          if (item.length > 30) {
            arrNum.push(index + 1);
          }
        });
        message.warn(
          <span>{t('goodsAttrCreate_attrValueTooLong', { index: arrNum.join(', ') })}</span>
        );
      } else {
        message.warn(t('goodsAttrCreate_attrValueTooLongSingle'));
      }
      return false;
    }
    return true;
  };

  const onSubmit = () => {
    // 新建商品属性
    if (!attrbute && (type === 'name' || !type)) {
      message.warning(t('goodsAttrCreate_enterAttrName'));
      return;
    }
    if ((!detailNameList || !detailNameList.length) && (type === 'attr' || !type)) {
      message.warning(t('goodsAttrCreate_enterAttrValue'));
      return;
    }
    // 修改属性明
    if (type === 'name') {
      updateStandardBase({ attrbute, id: standardId || 0, useWay: 4, valueType: 1 }).then(() => {
        message.success(t('goodsAttrCreate_updateSuccess'));
        onClose();
        onConfirm();
      });
    } else if (type === 'attr') {
      if (!checkNum()) {
        return;
      }
      addStandardDetail({ detailName: detailNameList, source: 1, standardId, type: 0 }).then(() => {
        message.success(t('goodsAttrCreate_addSuccess'));
        onClose();
        onConfirm();
      });
    } else {
      if (!checkNum()) {
        return;
      }
      addStandardBase({
        attrbute,
        catId,
        useWay: 4,
        source: 1,
        valueType: 1,
        standardDetailParam: {
          detailName: detailNameList,
          type: 0,
        },
      }).then(() => {
        message.success(t('goodsAttrCreate_addSuccess'));
        onClose();
        onConfirm();
      });
    }
  };

  useEffect(() => {
    if (props.visible) {
      setAttrbute(name || '');
    }
  }, [name, props.visible]);

  useEffect(() => {
    if (!type) {
      drawerRef.current.setIsChange(detailNameList.length > 0 || attrbute.length > 0);
    }
    if (type === 'name') {
      drawerRef.current.setIsChange(name !== attrbute);
    }
    if (type === 'attr') {
      drawerRef.current.setIsChange(detailNameList.length > 0);
    }
  }, [attrbute, attrbute.length, detailNameList.length, name, type]);

  const title = useMemo(() => {
    switch (type) {
      case 'name':
        return t('goodsAttrCreate_editAttrName');
      case 'attr':
        return t('goodsAttrCreate_addAttrValue');
      default:
        return t('goodsAttrCreate_createAttr');
    }
  }, [type, t]);

  const footer = (
    <Drawer.Footer
      okText={t('public_confirm')}
      cancelText={t('public_cancel')}
      onOk={onSubmit}
      onCancel={onClose}
    />
  );

  return (
    <Drawer
      title={title}
      {...props}
      footer={footer}
      ref={drawerRef}
      placement={type && type.length ? 'bottom' : 'right'}
      className={classNames({ [styles.drawer]: type && type.length, [styles.drawerRight]: !type })}
    >
      {(type === 'name' || !type) && (
        <div className={styles.card}>
          <div className={styles.title}>{t('goodsAttrCreate_attrName')}</div>
          <Input.TextArea
            value={attrbute}
            placeholder={t('goodsAttrCreate_enterAttrName2')}
            maxLength={15}
            className={styles.input}
            onChange={onHandleChangeName}
          />
        </div>
      )}
      {(type === 'attr' || !type) && (
        <div className={styles.card}>
          <div className={styles.title}>{t('goodsAttrCreate_attrValue')}</div>
          <Input.TextArea
            placeholder={t('goodsAttrCreate_enterAttrValue2')}
            className={styles.input}
            onChange={(e) => onChangeAttr(e.target.value)}
            onKeyDown={(e) => {
              // @ts-ignore
              if (e.key === ' ' && !e.target.value) {
                e.preventDefault();
              }
            }}
          />
        </div>
      )}
    </Drawer>
  );
}

GoodsAttrCreate.defaultProps = {
  type: '',
  standardId: 0,
  name: '',
};

export default GoodsAttrCreate;
