.drawer {
  :global {
    .ant-drawer-content-wrapper {
      width: 375px;
      height: 60% !important;
      right: 0 !important;
      left: auto !important;
    }

    .ant-drawer-wrapper-body {
      height: 100% !important;
    }
  }
}

.drawerRight {
  :global {
    .ant-drawer-wrapper-body {
      height: 100% !important;
    }
  }
}

.card {
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(2 9 58 / 8%);
}

.title {
  font-weight: 600;
  margin-bottom: 12px;
}

.input {
  height: 109px !important;
  resize: none;
}

.arrNumItem:last-child .arrNumItemTip {
  display: none;
}
