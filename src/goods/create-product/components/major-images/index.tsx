import { useRef } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useTranslation } from 'react-i18next';
import { Icon, ImageCropper, ImageCropperHandle } from '@/components';
import { StateType } from '@/apis/pms/type';
import { ImageDragDrop } from '../../../goods-package/container/add/index';
import styles from './index.module.less';

interface PropsType {
  image: string;
  imageList: string[];
  onSetState: SimpleFn<Partial<StateType>>;
}

function MajorImagesComponent({ image, imageList, onSetState }: PropsType) {
  const { t } = useTranslation();
  const imageCropperRef = useRef<ImageCropperHandle>(null);
  const currentIndex = useRef(0);

  const onCropper = () => {
    imageCropperRef?.current?.fileInputClick();
  };

  const changeImageListPosition = (dragIndex: number, hoverIndex: number) => {
    const data = [...imageList];
    const temp = data[dragIndex];
    data[dragIndex] = data[hoverIndex];
    data[hoverIndex] = temp;
    setTimeout(() => {
      onSetState({ imageList: data });
    }, 0);
  };

  return (
    <div>
      {image ? (
        <div className={styles.bigImage}>
          <img
            src={image}
            alt=""
            role="presentation"
            onClick={() => {
              onCropper();
            }}
          />
        </div>
      ) : (
        <div
          className={styles.uploadBtn}
          role="button"
          tabIndex={0}
          onClick={() => {
            onCropper();
          }}
        >
          <Icon name="plus" size={24} />
          <span>{t('add_image')}</span>
        </div>
      )}

      <div className={styles.imagesWrap}>
        <DndProvider backend={HTML5Backend}>
          {imageList.map((item, index) => (
            <div className={styles.imageItem} key={`${item + index}`}>
              <ImageDragDrop
                id={`${item + index}`}
                index={index}
                changePosition={changeImageListPosition}
              >
                {item ? (
                  <div
                    role="button"
                    tabIndex={0}
                    className={styles.imageItem}
                    onClick={() => {
                      onCropper();
                    }}
                    onMouseEnter={() => {
                      currentIndex.current = index;
                      if (imageList[index]) {
                        onSetState({ image: imageList[index] });
                      }
                    }}
                  >
                    <div className={styles.smallImage}>
                      <img src={item} alt="" />
                    </div>
                    <Icon
                      name="close-solid"
                      size={12}
                      className={styles.removeImage}
                      onClick={(e) => {
                        e.stopPropagation();
                        const images = imageList;
                        images[index] = '';
                        onSetState({
                          imageList: [...images],
                          image: '',
                        });
                      }}
                    />
                  </div>
                ) : (
                  <div
                    className={styles.uploadImage}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      currentIndex.current = index;
                      onCropper();
                    }}
                  >
                    <Icon name="plus" size={12} />
                  </div>
                )}
              </ImageDragDrop>
            </div>
          ))}
        </DndProvider>
      </div>

      <div className={styles.message}>{t('image_suggestion')}</div>

      <ImageCropper
        ref={imageCropperRef}
        aspectType={0}
        onSuccess={(value) => {
          const images = imageList;
          images[currentIndex.current] = value.url;
          onSetState({
            imageList: [...images],
            image: value.url,
          });
        }}
      />
    </div>
  );
}

MajorImagesComponent.displayName = 'MajorImagesComponent';

MajorImagesComponent.defaultProps = {};

export default MajorImagesComponent;
