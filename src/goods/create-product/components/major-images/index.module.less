.bigImage {
  display: flex;
  border-radius: 10px;
  width: 195px;
  height: 195px;
  align-items: center;
  justify-content: center;
}

.uploadBtn {
  color: #008cff;
  font-size: 12px;
  display: flex;
  width: 195px;
  height: 195px;
  justify-content: center;
  border-radius: 10px;
  border: 1px dashed #008cff;
  background: rgb(217 238 255 / 30%);
  flex-direction: column;
  align-items: center;
  cursor: pointer;
}

.imagesWrap {
  display: flex;
  width: 195px;
  height: 24px;
  margin-top: 10px;
  justify-content: space-between;
}

.imageItem {
  width: 24px;
  height: 24px;
  position: relative;
  cursor: pointer;
}

.smallImage {
  display: flex;
  width: 24px;
  height: 24px;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  border-radius: 6px;
  border: 1px dashed #b1b3be;
}

.removeImage {
  color: #888b98;
  width: 12px;
  height: 12px;
  position: absolute;
  top: -4px;
  right: -4px;
  z-index: 1;
  background-color: #fff;
  border-radius: 50%;
}

.uploadImage {
  color: #b1b3be;
  display: flex;
  width: 24px;
  height: 24px;
  line-height: 12px;
  justify-content: center;
  border-radius: 6px;
  border: 1px dashed #b1b3be;
  cursor: pointer;
  align-items: center;
}

.message {
  color: #888b98;
  font-size: 12px;
  line-height: 20px;
  margin-top: 8px;
}
