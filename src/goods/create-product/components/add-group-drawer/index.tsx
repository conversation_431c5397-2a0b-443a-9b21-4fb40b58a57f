/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Form, Input, Divider, Radio, message, Button } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, Icon } from '@/components';
// import classNames from 'classnames';
import styles from './index.module.less';

const { TextArea } = Input;
// type： add-新增分组  editGgroup-编辑分组  addTag-添加值

interface ItemType {
  id: number;
  standardDetail: any;
}

const AddGroupDrawer = forwardRef(({ onOK, type, title, height }: any, ref: any) => {
  const [visible, setVisible] = useState(false);
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [groupType, setGroupType] = useState(1);
  // const [active, setActive] = useState(0);
  const [active] = useState(0);
  const [detailNameList, setDetailNameList] = useState<string[]>([]);
  const [dataInfo, setDataInfo] = useState<ItemType>();
  const [text, setText] = useState('');
  const [groupName, setGroupName] = useState('');

  const onClose = () => {
    setVisible(false);
    form.resetFields();
    setText('');
  };

  // const checkDetailNameList = ()=>{
  //   const regIsNum = /^[0-9][0-9]*([\.][0-9]{1,2})?$/;
  //   const check = (val:string)=>{

  //   }
  // }

  // eslint-disable-next-line consistent-return
  const onWriteDetailName = (val: string) => {
    const tag = val.replace(/ /g, '');
    // eslint-disable-next-line no-useless-escape
    const regIsNum = /^[0-9][0-9]*([\.][0-9]{1,6})?$/;
    if (tag.indexOf('\n') !== -1) {
      const attrbuteArr = tag.split('\n').filter((item) => item !== '');

      for (let i = 0; i < attrbuteArr.length; i += 1) {
        const element = attrbuteArr[i];
        if (element.length > 25 && groupType === 1) {
          message.warn(t('addGroupDrawer_specExceed25Chars', { index: i + 1 }));
          break;
        }
        if (!regIsNum.test(String(Number(element))) && groupType === 2) {
          message.warn(t('addGroupDrawer_invalidNumber', { index: i + 1 }));
          break;
        }
      }
      setDetailNameList(attrbuteArr);
    } else if (tag.length > 25) {
      return message.warn(t('addGroupDrawer_specExceed25CharsSingle'));
    } else if (!regIsNum.test(String(Number(tag))) && groupType === 2) {
      if (tag) {
        message.warn(t('addGroupDrawer_invalidNumberSingle'));
      }
      setDetailNameList([tag]);
    } else if (tag) {
      setDetailNameList([tag]);
    } else if (!tag) {
      setDetailNameList([]);
    }
    form.setFieldsValue({ attributeValue: tag });
    setText(tag);
  };

  const onChange = (e: RadioChangeEvent) => {
    setGroupType(e.target.value);
    form.setFieldsValue({
      attributeValue: null,
    });
  };

  const onPreserve = () => {
    const formInfo = form.getFieldsValue();

    formInfo.valueType = groupType;
    if (type === 'add') {
      if (formInfo?.attrbute) {
        onOK(formInfo, active, detailNameList);
      } else {
        message.warning(t('addGroupDrawer_enterGroupName'));
      }
    } else if (type === 'editGgroup') {
      if (formInfo?.attrbute) {
        onOK(formInfo, dataInfo?.id);
      } else {
        message.warning(t('addGroupDrawer_enterGroupName2'));
      }
    } else if (type === 'addTag') {
      onOK(dataInfo, active, formInfo, detailNameList);
    }
  };

  const attributeShow = () => (
    <div>
      <div className={styles.attributeLabel}>
        {t('addGroupDrawer_attributeValue')}
        <span className={styles.labelNotice}>{t('addGroupDrawer_specificationLimit')}</span>
      </div>
      <Form.Item name="attributeValue">
        <TextArea
          autoSize={{ minRows: 6, maxRows: 6 }}
          placeholder={t('addGroupDrawer_enterAttributeValue')}
          bordered={false}
          style={{ height: 120, fontSize: '16px' }}
          value={text}
          onChange={(val) => onWriteDetailName(val.target.value)}
        />
        <div className={styles.enterText}>{t('addGroupDrawer_enterKey')}</div>
      </Form.Item>
    </div>
  );

  useImperativeHandle(ref, () => ({
    openDrawer() {
      setVisible(true);
    },
    closeDrawer() {
      setVisible(false);
      form.resetFields();
    },
    // 编辑分组信息
    setFormData(info: any) {
      setDataInfo(info);
      form.setFieldsValue({
        attrbute: info?.attrbute,
        valueType: info.valueType,
      });

      setGroupType(info?.valueType);
    },
    addTagItem(info: any) {
      setDataInfo(info);
      setGroupType(info?.valueType);
    },
  }));

  useEffect(() => {
    if (visible) {
      setDetailNameList([]);
      // setGroupType(1);
      // form.resetFields();
      setText('');
      if (type === 'add') {
        setGroupType(1);
      }
    }
  }, [visible, type, form]);

  return (
    <Drawer
      title={title}
      placement="bottom"
      height={height}
      closable={false}
      visible={visible}
      getContainer={false}
      maskStyle={{ background: 'rgba(0, 0, 0, .45)' }}
      extra={
        <div style={{ cursor: 'pointer' }}>
          <Icon name="close" size={20} role="button" tabIndex={-1} onClick={onClose} />
        </div>
      }
      footer={
        <div className={styles.footerBox}>
          <Button
            className={styles.footerBoxBtn}
            style={{ color: '#040919', width: 160, background: '#F3F3F3' }}
            onClick={onClose}
          >
            {t('public_cancel')}
          </Button>
          <Button
            type="primary"
            className={styles.footerBoxBtn}
            onClick={onPreserve}
            style={{ width: 160, marginLeft: 15 }}
          >
            {t('public_sure')}
          </Button>
        </div>
        // <Drawer.Footer okText="确定" cancelText="取消" onOk={onPreserve} onCancel={onClose} />
      }
    >
      <Form
        layout="vertical"
        form={form}
        initialValues={{ valueType: 1 }}
        className={styles.groupForm}
      >
        {type === 'add' || type === 'editGgroup' ? (
          <div className={styles.cardBox}>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.attrbute !== currentValues.attrbute
              }
            >
              {({ getFieldValue }) => (
                <Form.Item
                  label={
                    <div style={{ fontWeight: 'normal' }}>
                      {t('addGroupDrawer_groupName')}
                      <span style={{ color: '#888B98' }}>
                        ({getFieldValue('attrbute')?.length || 0}/25)
                      </span>
                    </div>
                  }
                  name="attrbute"
                  rules={[{ required: true, message: t('addGroupDrawer_enterGroupName') }]}
                >
                  <TextArea
                    value={groupName}
                    placeholder={t('addGroupDrawer_groupName')}
                    bordered={false}
                    autoSize={{ minRows: 1, maxRows: 2 }}
                    onChange={(val) => {
                      const name = val.target.value.replace(/ /g, '');
                      setGroupName(name);
                      form.setFieldsValue({
                        attrbute: name,
                      });
                    }}
                  />

                  {/* <Input placeholder="请输入分组名称" maxLength={25} bordered={false} /> */}
                </Form.Item>
              )}
            </Form.Item>

            <div className={styles.divider}>
              <Divider />
            </div>

            <Form.Item
              label={<div style={{ fontWeight: 'normal' }}>{t('addGroupDrawer_groupType')}</div>}
              name="valueType"
              rules={[{ required: true, message: t('addGroupDrawer_selectGroupType') }]}
            >
              <Radio.Group
                onChange={onChange}
                value={groupType}
                disabled={dataInfo?.standardDetail?.length > 0}
                className={styles.radioSelect}
                // defaultValue={1}
              >
                <Radio value={1}>{t('addGroupDrawer_textType')}</Radio>
                <Radio value={2}>{t('addGroupDrawer_numberType')}</Radio>
              </Radio.Group>
            </Form.Item>
          </div>
        ) : null}

        {type === 'add' || type === 'addTag' ? (
          <div className={styles.cardBox}>
            {groupType === 2 ? (
              <div>
                {/* <div className={styles.tabsBox}>
                  <div
                    className={classNames(styles.leftNoTabs, active === 0 && styles.selectLeftTabs)}
                    role="button"
                    tabIndex={0}
                    onClick={() => {
                      if (!dataInfo?.id) {
                        setActive(0);
                        form.setFieldsValue({
                          attributeValue: null,
                        });
                      }
                    }}
                  >
                    <div>添加属性值</div>
                  </div>
                  <div
                    className={classNames(
                      styles.rightNoTabs,
                      active === 1 && styles.selectRightTabs
                    )}
                    role="button"
                    tabIndex={0}
                    aria-disabled
                    onClick={() => {
                      // if (!dataInfo?.id) {
                      //   setActive(1);
                      //   form.setFieldsValue({
                      //     attributeValue: null,
                      //   });
                      // }
                    }}
                  >
                    <div>添加定制值</div>
                  </div>
                </div> */}
                <div>
                  {active ? (
                    <div className={styles.madeInputBox}>
                      <div>
                        <div className={styles.inputLable}>定制值</div>
                        <div className={styles.madeInput}>
                          <Form.Item name="minValue">
                            <Input
                              placeholder={t('addGroupDrawer_minPrice')}
                              className={styles.minMaxInput}
                              bordered={false}
                            />
                          </Form.Item>
                          &nbsp;-&nbsp;
                          <Form.Item name="maxValue">
                            <Input
                              placeholder={t('addGroupDrawer_maxPrice')}
                              className={styles.minMaxInput}
                              bordered={false}
                            />
                          </Form.Item>
                        </div>
                      </div>
                      <div className={styles.inputLable}>最小梯度值</div>
                      <Form.Item name="minStepValue">
                        <Input
                          placeholder={t('addGroupDrawer_enterMinStepValue')}
                          className={styles.minInput}
                          bordered={false}
                        />
                      </Form.Item>
                    </div>
                  ) : (
                    <div>{attributeShow()}</div>
                  )}
                </div>
              </div>
            ) : (
              <div>{attributeShow()}</div>
            )}
          </div>
        ) : null}
      </Form>
    </Drawer>
  );
});

export default AddGroupDrawer;
