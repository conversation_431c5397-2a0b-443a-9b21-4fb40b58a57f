.cardBox {
  background-color: #fff;
  margin-bottom: 20px;
  padding: 16px 20px 16px 10px;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global .ant-form-item {
    margin-bottom: 0;

    .ant-form-item-label {
      font-weight: bold;
    }
  }

  :global .ant-divider-horizontal {
    margin: 10px 0 16px;
  }

  .radioSelect {
    margin-left: 11px;
  }
}

.tabsBox {
  display: flex;
  margin-bottom: 10px;
  margin-left: 8px;
  align-items: center;
  justify-content: flex-start;
  cursor: pointer;

  .leftNoTabs {
    width: 88px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 10px 0 0 10px;
    // border-radius: 10px;
    background-color: #f5f6fa;
  }

  .rightNoTabs {
    width: 88px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 0 10px 10px 0;
    background-color: #f5f6fa;
  }

  .selectLeftTabs {
    color: #008cff;
    width: 88px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 0 10 10px 0;
    background-color: #d9eeff;
  }

  .selectRightTabs {
    color: #008cff;
    width: 88px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    border-radius: 0 10 10px 0;
    background-color: #d9eeff;
  }
}

.divider {
  padding: 0 10px;
}

.attributeLabel {
  margin: 0 0 10px 11px;

  .labelNotice {
    color: #b1b3be;
  }
}

.madeInputBox {
  margin-left: 9px;
}

.inputLable {
  height: 52px;
  line-height: 52px;
}

.madeInput {
  color: #888b98;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .minMaxInput {
    background-color: #f5f6fa;
    text-align: center;
    border-radius: 10px;
    height: 28px;
  }
}

.minInput {
  background-color: #f5f6fa;
  border-radius: 10px;
  height: 28px;
}

.enterText {
  color: #b1b3be;
  font-size: 12px;
  text-align: right;
}

.groupForm {
  :global {
    .ant-form-item-explain-error {
      padding-left: 12px;
    }

    .ant-form-item-explain-error {
      font-size: 12px;
    }
    .ant-input-borderless {
      font-size: 16px;
    }
  }
}

.footerBox {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 36%) 12%,
    rgb(255 255 255 / 58%) 25%,
    rgb(255 255 255 / 72%) 36%
  );
}
