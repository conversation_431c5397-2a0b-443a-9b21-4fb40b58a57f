.card {
  margin-top: 20px;
  padding: 20px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
}

.cardTitle {
  font-size: 16px;
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  margin-bottom: 16px;
}

.lineBtn {
  font-size: 14px;
  font-weight: 400;
  float: right;
  cursor: pointer;
}

.addBlockBtn {
  color: #008cff;
  font-weight: 500;
  display: flex;
  height: 60px;
  justify-content: center;
  border: 1px dashed #008cff;
  border-radius: 10px;
  background-color: rgb(217 238 255 / 30%);
  align-items: center;
  cursor: pointer;
}

.list {
  max-width: 452px;
}

.item {
  margin-top: 8px;

  &:first-child {
    margin-top: 0;
  }
}

.video,
.image {
  width: 100%;
  height: auto;
  border-radius: 10px;
}

.text {
  font-size: 14px;
  line-height: 22px;
  word-wrap: break-word;
  word-break: break-all;
}
