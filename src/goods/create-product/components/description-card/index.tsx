import { useEffect, useState } from 'react';
import { GraphicDescDrawer, Icon } from '@/components';
import { useTranslation } from 'react-i18next';
import type { GraphicDescDrawerPropsDataItem } from '@/components';
import { DetailItemType, StateType } from '@/apis/pms/type';
import styles from './index.module.less';

interface PropsType {
  detailList: DetailItemType[];
  onSetState: SimpleFn<Partial<StateType>>;
}

function DescriptionCardComponent({ detailList, onSetState }: PropsType) {
  const { t } = useTranslation();
  const [visible, setVisible] = useState(false);
  const [preview, setPreview] = useState(false);
  const [graphicDescData, setGraphicDescData] = useState<GraphicDescDrawerPropsDataItem[]>([]);

  const onUpdateState = (values: GraphicDescDrawerPropsDataItem[]) => {
    onSetState({
      detailList: values.map((item) => {
        if (item.type === 'image') {
          return { imgUrl: item.value, text: '', videoUrl: '' };
        }
        if (item.type === 'video') {
          return { videoUrl: item.value, text: '', imgUrl: '' };
        }
        return { text: item.value, imgUrl: '', videoUrl: '' };
      }),
    });
  };

  useEffect(() => {
    const descList =
      detailList?.map((item) => {
        if (item.imgUrl) {
          return { type: 'image', value: item.imgUrl };
        }
        if (item.videoUrl) {
          return { type: 'video', value: item.videoUrl };
        }
        return { type: 'text', value: item.text };
      }) || [];
    setGraphicDescData([...descList]);
  }, [detailList]);

  return (
    <div className={styles.card}>
      <div className={styles.cardTitle}>
        {t('desc_graphicDescText')}
        {!!detailList.length && (
          <span
            className={styles.lineBtn}
            role="button"
            tabIndex={0}
            onClick={() => {
              setVisible(true);
            }}
          >
            {t('desc_edit')}
          </span>
        )}
      </div>
      {!detailList.length && (
        <div
          className={styles.addBlockBtn}
          role="button"
          tabIndex={0}
          onClick={() => {
            setVisible(true);
          }}
        >
          <Icon name="plus" size={16} />
          <span className="ml-1">{t('desc_add_graphic')}</span>
        </div>
      )}
      <div className={styles.list}>
        {detailList.map((item) => {
          if (item.imgUrl) {
            return (
              <div className={styles.item}>
                <img src={item.imgUrl} alt="" className={styles.image} />
              </div>
            );
          }
          if (item.text) {
            return (
              <div className={styles.item}>
                <span className={styles.text}>{item.text}</span>
              </div>
            );
          }
          if (item.videoUrl) {
            return (
              <div className={styles.item}>
                <video src={item.videoUrl} muted controls className={styles.video} />
              </div>
            );
          }
          return null;
        })}
      </div>

      <GraphicDescDrawer
        visible={visible}
        permissionList={[{ key: 'uploadVideo', code: 'M_001_002_001_009' }]}
        preview={preview}
        onClose={() => {
          setVisible(false);
        }}
        onPreviewChange={(val) => {
          setPreview(val);
        }}
        onConfirm={(values) => {
          setGraphicDescData(values);
          onUpdateState(values);
        }}
        data={graphicDescData}
      />
    </div>
  );
}

DescriptionCardComponent.displayName = 'DescriptionCardComponent';

DescriptionCardComponent.defaultProps = {};

export default DescriptionCardComponent;
