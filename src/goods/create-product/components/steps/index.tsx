import { useState } from 'react';
import classNames from 'classnames';
import styles from './index.module.less';

export interface RefType {
  setActiveIndex: SimpleFn<number>;
}

interface stepsType {
  title: string;
  titleId: string;
}

interface PropsType {
  steps: stepsType[];
}

function StepsComponent({ steps }: PropsType) {
  const [activeIndex, setActiveIndex] = useState<number>(0);

  // 点击将跳转对应的位置（锚点功能）
  const onAnchor = (anchorName: string) => {
    if (anchorName) {
      const anchorElement = document.getElementById(anchorName);
      if (anchorElement) {
        // block:表示滚动到锚点的顶部或者底部，start/end
        // behavior:表示滚动的效果，auto/smooth(滚动效果)
        anchorElement.scrollIntoView({ block: 'center', behavior: 'smooth', inline: 'nearest' });
      }
    }
  };

  return (
    <div style={{ padding: '16px 0' }}>
      <div className={styles.steps}>
        {steps.map((step, index) => (
          <div
            key={step.title}
            className={classNames(styles.step, { [styles.current]: index === activeIndex })}
            role="button"
            tabIndex={0}
            onClick={() => {
              setActiveIndex(index);
              onAnchor(step.titleId);
            }}
          >
            {step.title}
          </div>
        ))}
      </div>
    </div>
  );
}

StepsComponent.displayName = 'StepsComponent';

StepsComponent.defaultProps = {};

export default StepsComponent;
