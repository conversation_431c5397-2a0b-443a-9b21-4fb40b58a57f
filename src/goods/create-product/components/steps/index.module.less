.steps {
  display: flex;
  width: 100%;
  height: 40px;
  // margin: 16px 0;
  overflow: hidden;
  border-radius: 10px;
}

.step {
  color: #888b98;
  line-height: 40px;
  position: relative;
  flex: 1;
  text-align: center;
  background-color: #f5f6fa;
  cursor: pointer;

  & + & {
    margin-left: 24px;
  }

  &::before,
  &::after {
    content: '';
    width: 20px;
    height: 40px;
    position: absolute;
    top: 0;
    right: -20px;
    z-index: 1;
    background-image: url('https://img.huahuabiz.com/user_files/2023421/1682063759537538.png');
    background-repeat: no-repeat;
    background-size: cover;
  }

  &::after {
    right: auto;
    left: -20px;
    background-image: url('https://img.huahuabiz.com/user_files/2023421/1682063759537429.png');
  }
}

.current {
  border: 0;
  background-color: #008cff;
  color: #fff;

  &::before {
    background-image: url('https://img.huahuabiz.com/user_files/2023421/1682063759537721.png');
    top: -0;
  }

  &::after {
    background-image: url('https://img.huahuabiz.com/user_files/2023421/1682063759534892.png');
    top: -0;
  }
}
