.cardBox {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-input-number-handler-wrap {
      display: none !important;
    }
  }
}

.cardTitle {
  font-size: 16px;
  font-weight: bolder;
  margin-bottom: 13px;

  .titleNotice {
    color: #888b98;
    font-size: 12px;
    font-weight: 400;
  }
}

.input {
  font-size: 16px;
  width: 100%;
  padding: 0;
  // text-align: right;
}

.batchBox {
  display: flex;
  flex-direction: column;
  // justify-content: space-between;
  // align-items: center;

  .batchLabel {
    width: 100%;
  }

  .batchValue {
    width: 100%;
    margin-top: 8px;
  }

  :global {
    // .ant-input-number-handler-wrap {
    //   display: none !important;
    // }

    // .ant-input-number-input {
    //   // text-align: right;
    //   padding: 0;
    // }
    .ant-input-affix-wrapper {
      padding: 0;

      .ant-input {
        font-size: 16px;
        height: 30px;
      }
    }
  }
}

.cardName {
  display: -webkit-box;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.line {
  margin: 16px 0;
}

.errorMsg {
  color: #ea1c26;
  line-height: 24px;
}
