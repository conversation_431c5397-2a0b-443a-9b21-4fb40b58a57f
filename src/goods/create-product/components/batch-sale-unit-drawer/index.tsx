import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import { Divider } from 'antd';
import { Drawer, NumericInput } from '@/components';
import { TableRowType } from '@/apis/pms/type';
import { useTranslation } from 'react-i18next';
// import { limitDecimalsF, limitDecimalsP } from '../../utils/input';
import styles from './index.module.less';

interface UnitDrawerType {
  isShow: boolean;
  unitNum: number;
  dataList: TableRowType[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[values: (number | null | string)[]]>;
}

function BatchSaleUnitDrawer({
  isShow,
  unitNum,
  dataList,
  closeDrawer,
  onConfirm,
}: UnitDrawerType) {
  const [batchVal, setBatchVal] = useState('');
  const [list, setList] = useState<(TableRowType & { saleGroupVal: number | null | string })[]>([]);
  // const [errorIndexList, setErrorIndexList] = useState<number[]>([]);
  const { t } = useTranslation();

  // const checkError = (arr = list) => {
  //   const errorList = arr.filter(
  //     (item) => item.saleGroupVal === null || item.saleGroupVal === undefined
  //   );
  //   const errIndexList = errorList.map((_item, index) => index);
  //   setErrorIndexList(errIndexList);
  //   return errIndexList;
  // };

  // const validateFields = () =>
  //   new Promise((resolve, reject) => {
  //     const errIndexList = checkError();
  //     if (errIndexList.length) {
  //       reject(errIndexList);
  //     } else {
  //       resolve(list);
  //     }
  //   });

  // 批量设置修改最小销售单元
  const onBatchSetSellUnit = (val: string) => {
    const batchList = list.map((item) => ({
      ...item,
      saleGroupVal: val,
    }));
    setList(batchList);
    setBatchVal(val);
    // checkError(batchList);
  };

  // 单个设置修改最小销售单元
  const onSingleSetSellUnit = (val: string, index: number) => {
    list[index].saleGroupVal = val === '' ? null : val;
    setList([...list]);
    // checkError(list);
  };

  const onOk = () => {
    // validateFields().then(() => {
    onConfirm(list.map((item) => item.saleGroupVal));
    closeDrawer(false);
    // });
  };

  const onclose = () => {
    closeDrawer(false);
  };

  useEffect(() => {
    setList(dataList.map((item) => ({ ...item, saleGroupVal: item.saleGroup })));
  }, [dataList]);

  useEffect(() => {
    if (isShow) {
      setBatchVal('');
    }
  }, [isShow]);

  return (
    <Drawer
      title={t('batchSaleUnitDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
      className={styles.batchSaleUnitDrawer}
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>
          {t('batchSaleUnitDrawer_batchSet')}
          <span className={styles.titleNotice}>{t('batchSaleUnitDrawer_titleNotice')}</span>
        </div>
        <div className={styles.batchBox}>
          <div className={styles.batchLabel}>{t('batchSaleUnitDrawer_minSaleUnit')}</div>
          <div className={styles.batchValue}>
            <NumericInput
              className={styles.input}
              bordered={false}
              precision={unitNum || 0}
              maxLength={(unitNum ? unitNum + 1 : 0) + 8}
              placeholder={t('batchSaleUnitDrawer_placeholder')}
              value={batchVal ? `${batchVal}` : ''}
              onChange={(val) => onBatchSetSellUnit(val)}
            />
            {/* <InputNumber
              className={styles.input}
              bordered={false}
              placeholder="请输入最小销售单元"
              onChange={(val) => onBatchSetSellUnit(val as number | null)}
              formatter={(val) => limitDecimalsF(val, unitNum)}
              parser={(val) => limitDecimalsP(val, unitNum, 8) as number}
              // maxLength={8}
            /> */}
          </div>
        </div>
      </div>

      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchCostPriceDrawer_singleSet')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>
              {(item?.standardList.map((standItem) => standItem.value) || []).join('/') || ''}
            </div>
            <div className={styles.batchBox}>
              <div className={styles.batchLabel}>{t('batchSaleUnitDrawer_minSaleUnit')}</div>
              <div className={styles.batchValue}>
                {/* <InputNumber
                  className={styles.input}
                  bordered={false}
                  placeholder="请输入最小销售单元"
                  value={item.saleGroupVal}
                  formatter={(val) => limitDecimalsF(val, unitNum)}
                  parser={(val) => limitDecimalsP(val, unitNum, 8) as number}
                  // min={1}
                  onChange={(val) => onSingleSetSellUnit(val, index)}
                /> */}
                <NumericInput
                  className={styles.input}
                  bordered={false}
                  precision={unitNum || 0}
                  maxLength={(unitNum ? unitNum + 1 : 0) + 8}
                  placeholder={t('specification_single_enter_minimum_sales_unit')}
                  value={item.saleGroupVal ? `${item.saleGroupVal}` : ''}
                  onChange={(val) => onSingleSetSellUnit(val, index)}
                />
                {/* <div
                  className={styles.errorMsg}
                  style={{ display: errorIndexList.includes(index) ? 'block' : 'none' }}
                >
                  请输入最小销售单元
                </div> */}
              </div>
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchSaleUnitDrawer;
