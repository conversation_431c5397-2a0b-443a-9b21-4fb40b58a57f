import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Drawer, SimpleUpload } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import { Form, Input, message, Radio, Select } from 'antd';
import { customCategoryAdd, customCategoryListCount } from '@/apis';
import classNames from 'classnames';
import styles from './index.module.less';

interface CustomCategoryCreatePops {
  visible: boolean;
  // parentId: number;
  onClose: () => void;
  onConfirm: () => void;
}

function CustomCategoryCreate({
  visible,
  // parentId,
  onClose,
  onConfirm,
  ...props
}: CustomCategoryCreatePops) {
  const { t } = useTranslation();
  const drawerRef = useRef<DrawerRefType>(null);
  const [grade, setGrade] = useState(1);
  const [form] = Form.useForm();
  const [imageUrl, setImageUrl] = useState('');
  const [isValueChange, setIsValueChange] = useState(true);
  const [firstCategoryList, setFirstCategoryList] = useState<{ label: string; value: number }[]>(
    []
  );

  const getCateList = () => {
    customCategoryListCount().then((res) => {
      const list = res.list?.map((item) => ({ label: item.name, value: item.id })) || [];
      setFirstCategoryList(list);
    });
  };

  const onValuesChange = () => {
    const { categoryName, parentId } = form.getFieldsValue();
    let isRequired = false;
    if (grade === 2) {
      isRequired = !parentId;
    }
    isRequired = !categoryName;

    if (isRequired) {
      drawerRef.current?.setIsChange(false);
      setIsValueChange(true);
      return;
    }
    drawerRef.current?.setIsChange(true);
    setIsValueChange(false);
  };

  const onSubmit = () => {
    const { categoryName, parentId } = form.getFieldsValue();
    customCategoryAdd({
      categoryName,
      image: imageUrl,
      grade,
      parentId: parentId || '',
    })
      .then(() => {
        message.success(t('customCategoryCreate_addSuccess'));
        onConfirm();
      })
      .finally(() => {});
  };

  useEffect(() => {
    if (visible) {
      setIsValueChange(true);
      getCateList();
    } else {
      form.resetFields();
      setImageUrl('');
      setGrade(1);
    }
  }, [visible]); // eslint-disable-line

  return (
    <Drawer
      title={t('customCategoryCreate_drawerTitle')}
      visible={visible}
      {...props}
      onClose={onClose}
      ref={drawerRef}
      footer={
        <Drawer.Footer
          okText={t('customCategoryCreate_okText')}
          showCancel={false}
          onOk={onSubmit}
          disabled={isValueChange}
        />
      }
    >
      <div className={classNames(styles.card, styles.cardForm)}>
        <div className={styles.formItem}>
          <div className={styles.formItemLabel}>{t('customCategoryCreate_level')}</div>
          <div className={styles.formItemValue}>
            <Radio.Group
              value={grade}
              onChange={(val) => {
                setGrade(val.target.value);
                form.setFieldsValue({ parentId: null, categoryName: '' });
              }}
            >
              <Radio value={1}>{t('customCategoryCreate_levelOne')}</Radio>
              <Radio value={2}>{t('customCategoryCreate_levelTwo')}</Radio>
            </Radio.Group>
          </div>
        </div>

        <Form layout="vertical" form={form} className={styles.form} onValuesChange={onValuesChange}>
          {grade === 2 && (
            <Form.Item
              label={t('customCategoryCreate_firstCategory')}
              required
              name="parentId"
              extra={<div className={styles.line} />}
            >
              <Select
                options={firstCategoryList}
                bordered={false}
                placeholder={t('customCategoryCreate_selectFirstCategory')}
              />
            </Form.Item>
          )}
          <Form.Item
            label={`${
              grade === 1
                ? t('customCategoryCreate_levelOneName')
                : t('customCategoryCreate_levelTwoName')
            }`}
            required
            name="categoryName"
            // extra={<div className={styles.line} />}
          >
            <Input
              className={styles.input}
              maxLength={20}
              bordered={false}
              placeholder={t('customCategoryCreate_enterName')}
            />
          </Form.Item>
        </Form>
      </div>
      <div className={styles.card}>
        <div className={styles.title}>
          {grade === 1
            ? t('customCategoryCreate_levelOneImage')
            : t('customCategoryCreate_levelTwoImage')}
        </div>
        <SimpleUpload
          className={styles.upload}
          accept={['.gif', '.pjp', '.jpg', '.png', '.jpeg', '.pjpeg', '.PNG'].join(',')}
          beforeUpload={(file) => {
            const fileSuffixes = ['.gif', '.pjp', '.jpg', '.png', '.jpeg', '.pjpeg', '.PNG'];
            const suffixesStatus = fileSuffixes.includes(
              file.name.substring(file.name.lastIndexOf('.'))
            );

            if (!suffixesStatus) {
              message.error(
                t('customCategoryCreate_invalidFileType', { types: fileSuffixes.join(',') })
              );
              return false;
            }

            return true;
          }}
          onChange={(e) => setImageUrl(e.file.url)}
        >
          {imageUrl ? (
            <img src={imageUrl} alt="" />
          ) : (
            <img
              src="https://img.huahuabiz.com/user_files/20221216/1671184511040177.png"
              alt={t('customCategoryCreate_addImage')}
            />
          )}
        </SimpleUpload>
      </div>
    </Drawer>
  );
}

export default CustomCategoryCreate;
