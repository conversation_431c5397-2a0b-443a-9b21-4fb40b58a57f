@import 'styles/mixins/mixins';

.card,
.cardForm {
  margin-bottom: 20px;
  padding: 16px;
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px rgb(0 0 0 / 8%);

  :global {
    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-input {
      font-size: @font-size-lg;
    }

    .ant-form-item-label {
      label {
        padding: 0 12px;
      }

      .ant-form-item-required {
        padding: 0;
      }
    }
  }
}

.cardForm {
  padding: 0 8px 8px;
}

.line {
  margin: 8px 12px 16px;
  border-bottom: 1px solid #e5e5e5;
}

.title {
  margin-bottom: 16px;
}

.upload {
  display: flex;
  height: 140px;
  justify-content: center;
  align-items: center;
  border: 1px dashed #b1b3be;
  border-radius: 10px;
  cursor: pointer;
}

.formItem {
  display: flex;
  height: 52px;
  margin-bottom: 16px;
  position: relative;
  align-items: center;

  &Label {
    width: 70px;

    &::before {
      content: '*';
      color: #ff4d4f;
      font-size: 14px;
      display: inline-block;
      line-height: 1;
      margin-right: 4px;
      font-family: SimSun, sans-serif;
    }
  }

  &Value {
    flex: 1;
  }

  &::after {
    content: '';
    width: 303px;
    height: 1px;
    background: #e5e5e5;
    position: absolute;
    bottom: 0;
    left: 10px;
  }
}
