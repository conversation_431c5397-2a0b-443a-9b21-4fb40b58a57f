import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Switch, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer } from '@/components';
import { TableRowType } from '@/apis/pms/type';
import { testPerm } from '@/utils/permission';
import styles from './index.module.less';

interface StatusDrawerType {
  isShow: boolean;
  dataList: TableRowType[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[values: number[]]>;
}

function BatchUnitStatusDrawer({ isShow, dataList, closeDrawer, onConfirm }: StatusDrawerType) {
  const { t } = useTranslation();
  const [list, setList] = useState<TableRowType[]>([]);
  const [batchStatus, setBatchStatus] = useState(false);
  const [checkedList, setCheckedList] = useState<number[]>([]);

  // 批量打开/关闭多单位状态
  const onBatchSetUnify = (checked: boolean) => {
    if (!testPerm('M_001_002_001_003')) {
      return;
    }
    let ids: number[] = [];
    if (checked) {
      ids = list.map((item) => item.id);
    } else {
      ids = [];
    }
    setCheckedList(ids);
    setBatchStatus(checked);
  };

  // 单个打开/关闭多单位状态
  const onSingleSetUnify = (checked: boolean, index: number) => {
    if (!testPerm('M_001_002_001_003')) {
      return;
    }
    const item = list[index];
    if (checked) {
      if (!checkedList.includes(item.id)) {
        checkedList.push(item.id);
      }
    } else {
      const spliceIndex = checkedList.indexOf(item.id);
      if (spliceIndex !== -1) {
        checkedList.splice(spliceIndex, 1);
      }
    }
    setCheckedList([...checkedList]);
    // setBatchStatus(checkedList.length === list.length);
  };

  const onOk = () => {
    closeDrawer(false);
    onConfirm(checkedList);
  };

  const onclose = () => {
    closeDrawer(false);
    setBatchStatus(false);
  };

  useEffect(() => {
    const checkedItems = dataList.filter((item) => item.hasUnitTemplate);
    setCheckedList(checkedItems.map((item) => item.id));
    // setBatchStatus(checkedItems.length === dataList.length);
    setList([...dataList]);
  }, [dataList]);

  useEffect(() => {
    if (isShow) {
      setBatchStatus(false);
    }
  }, [isShow]);

  return (
    <Drawer
      title={t('batchUnitStatusDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batch_set')}</div>
        <div className={styles.batchBox}>
          <div>{t('base_multiUnitStatus2')}</div>
          <Switch checked={batchStatus} onChange={onBatchSetUnify} />
        </div>
      </div>
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('single_set')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>
              {item.standardList.map((standItem) => standItem.value).join('/')}
            </div>
            <div className={styles.batchBox}>
              <div>{t('base_multiUnitStatus2')}</div>
              <Switch
                checked={checkedList.includes(item.id)}
                onChange={(checked) => onSingleSetUnify(checked, index)}
              />
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchUnitStatusDrawer;
