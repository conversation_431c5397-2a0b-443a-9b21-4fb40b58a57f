import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import { InputNumber, Divider } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer } from '@/components';
import { TableRowType } from '@/apis/pms/type';
import styles from './index.module.less';

interface UnitDrawerType {
  isShow: boolean;
  dataList: TableRowType[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[values: (number | null)[]]>;
}

function BatchValidTimeDrawer({ isShow, dataList, closeDrawer, onConfirm }: UnitDrawerType) {
  const { t } = useTranslation();
  const [list, setList] = useState<(TableRowType & { expirationDateVal: number | null })[]>([]);
  const [batchVal, setBatchVal] = useState<number | null>(null);
  // const [errorIndexList, setErrorIndexList] = useState<number[]>([]);

  // const checkError = (arr = list) => {
  //   const errorList = arr.filter(
  //     (item) => item.expirationDateVal === null || item.expirationDateVal === undefined
  //   );
  //   const errIndexList = errorList.map((_item, index) => index);
  //   setErrorIndexList(errIndexList);
  //   return errIndexList;
  // };

  // const validateFields = () =>
  //   new Promise((resolve, reject) => {
  //     const errIndexList = checkError();
  //     if (errIndexList.length) {
  //       reject(errIndexList);
  //     } else {
  //       resolve(list);
  //     }
  //   });

  // 批量设置修改最小销售单元
  const onBatchSetTime = (val: number | null) => {
    const batchList = list.map((item) => ({
      ...item,
      expirationDateVal: val,
    }));
    setList(batchList);
    setBatchVal(val);
  };

  // 单个设置修改最小销售单元
  const onSingleSetTime = (val: number | null, index: number) => {
    list[index].expirationDateVal = val;
    // const ids = Array.from(new Set(list.map((item) => item.expirationDateVal) || []));
    // setBatchVal(ids.length > 1 ? null : ids[0]);
    setList([...list]);
    // checkError(list);
  };

  const onOk = () => {
    // validateFields().then(() => {
    onConfirm(list.map((item) => item.expirationDateVal));
    closeDrawer(false);
    // });
  };

  const onclose = () => {
    closeDrawer(false);
    setBatchVal(null);
    // setErrorIndexList([]);
  };

  useEffect(() => {
    const arr = dataList.map((item) => ({ ...item, expirationDateVal: item.expirationDate.value }));
    // const ids = Array.from(new Set(arr.map((item) => item.expirationDateVal) || []));
    // setBatchVal(ids.length > 1 ? null : ids[0]);
    setList(arr);
  }, [dataList]);

  useEffect(() => {
    if (isShow) {
      setBatchVal(null);
    }
  }, [isShow]);

  return (
    <Drawer
      title={t('batchValidTimeDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batch_set')}</div>
        <div className={styles.batchBox}>
          <div className={styles.batchLabel}>{t('base_shelfLife2')}</div>
          <div className={styles.batchValue}>
            <InputNumber
              className={styles.input}
              bordered={false}
              placeholder={t('enter_shelf_life')}
              value={batchVal}
              addonAfter={<div className={styles.addonAfter}>{t('days')}</div>}
              maxLength={8}
              min={1}
              precision={0}
              onChange={(val) => onBatchSetTime(val)}
            />
          </div>
        </div>
      </div>

      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('single_set')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>
              {(item?.standardList.map((standItem) => standItem.value) || []).join('/') || ''}
            </div>
            <div className={styles.batchBox}>
              <div className={styles.batchLabel}>{t('base_shelfLife2')}</div>
              <div className={styles.batchValue}>
                <InputNumber
                  className={styles.input}
                  bordered={false}
                  placeholder={t('enter_shelf_life')}
                  value={item.expirationDateVal}
                  addonAfter={<div className={styles.addonAfter}>{t('days')}</div>}
                  maxLength={8}
                  min={1}
                  precision={0}
                  onChange={(val) => onSingleSetTime(val, index)}
                />
                {/* <div
                  className={styles.errorMsg}
                  style={{ display: errorIndexList.includes(index) ? 'block' : 'none' }}
                >
                  请输入保质期
                </div> */}
              </div>
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchValidTimeDrawer;
