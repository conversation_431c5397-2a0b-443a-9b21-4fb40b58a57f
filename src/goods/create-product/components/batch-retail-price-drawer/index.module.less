.cardBox {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    // .ant-input-number-handler-wrap {
    //   display: none !important;
    // }

    // .ant-input-number-group-addon {
    //   border: 0;
    //   background: white;
    // }
    .ant-input-affix-wrapper {
      padding: 0;

      .ant-input {
        font-size: 16px;
        height: 30px;
      }
    }
  }
}

.cardTitle {
  font-size: 16px;
  font-weight: bolder;
  margin-bottom: 13px;
}

.input {
  font-size: 16px;
  width: 100%;
  padding: 0;

  // :global {
  //   input {
  //     text-align: right;
  //   }
  // }
}

.batchItem {
  font-size: 16px;
  display: flex;
  margin-top: 16px;
  align-items: center;
  justify-content: space-between;

  .batchItemLabel {
    font-size: 14px;
    max-width: 120px;
  }

  :global {
    .ant-input-number-input {
      padding-right: 0;
    }

    .ant-input-number-group-addon {
      padding-left: 4px;
    }
  }
}

.bottomBorder {
  border-bottom: 1px solid #f3f3f3;
  margin-bottom: 0;
  padding-bottom: 16px;
}

.cardName {
  display: -webkit-box;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.line {
  margin: 16px 0;
}

.errorMsg {
  color: #ea1c26;
  line-height: 24px;
  margin-left: 15px;
}

.price {
  color: #ea1c26;
}

.batchItemColumn {
  display: flex;
  flex-direction: column;
  margin-top: 16px;

  .batchItemColumnLabel {
    font-size: 14px;
  }

  .batchItemColumnValue {
    font-size: 14px;
    margin-top: 8px;

    :global {
      .ant-input-number-input {
        padding: 0;
      }
    }
  }
}
