import { Dispatch, SetStateAction, useState, useEffect, useRef } from 'react';
import {
  Divider,
  message,
  // InputNumber
} from 'antd';
import classNames from 'classnames';
import { useRequest } from 'ahooks';
import { getGoodsPriceSimpleShelf } from '@/apis';
import { useTranslation } from 'react-i18next';
import { Drawer, NumericInput } from '@/components';
import { TableRowType, VarListType } from '@/apis/pms/type';
// import { limitDecimalsF, limitDecimalsP } from '../../utils/input';
import styles from './index.module.less';

interface UnitDrawerType {
  isShow: boolean;
  categoryId: number;
  unit: string;
  dataList: TableRowType[];
  formulaId: number;
  formulaName: string;
  formatFormula: string;
  formulaList: VarListType[];
  standardList: { name: string; value: string }[][];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[values: (string | null | number)[], formulaList: VarListType[][]]>;
}

function BatchRetailPriceDrawer({
  isShow,
  categoryId,
  unit,
  dataList,
  formulaId,
  formulaName,
  formatFormula,
  formulaList,
  standardList,
  closeDrawer,
  onConfirm,
}: UnitDrawerType) {
  const { t } = useTranslation();

  const [formulaData, setFormulaData] = useState<VarListType[]>([]);
  const [list, setList] = useState<(TableRowType & { marketPriceVal: string | null | number })[]>(
    []
  );
  const [batchPrice, setBatchPrice] = useState<number | null | string>(null);
  const [batchVal, setBatchVal] = useState<string | null | string>(null);
  // const [errorIndexList, setErrorIndexList] = useState<number[]>([]);
  const currIndex = useRef({ index: -1, formulaIndex: -1 });
  const formulaDataRef = useRef<(TableRowType & { marketPriceVal: number | null | string })[]>([]);
  const priceLoading = useRef(false);
  const batchPriceLoading = useRef(false);

  const { run: batchRun } = useRequest(getGoodsPriceSimpleShelf, {
    debounceWait: 300,
    manual: true,
    onSuccess: (res) => {
      if (formulaData.filter((item) => item.value === null || item.value === '').length === 0) {
        setBatchPrice(res.priceList[0]);
      } else {
        setBatchPrice(0);
      }
      if (list.length) {
        setList(
          list.map((item) => ({
            ...item,
            marketPriceVal: res.priceList[0],
            formulaList: item.formulaList.map((formulaItem, index) => ({
              ...formulaItem,
              value: formulaData[index].value,
            })),
          }))
        );
      }
    },
    onError: () => {
      setBatchPrice(0);
    },
    onFinally: () => {
      batchPriceLoading.current = false;
    },
  });

  const { run } = useRequest(getGoodsPriceSimpleShelf, {
    debounceWait: 300,
    manual: true,
    onSuccess: (res) => {
      if (list.length) {
        const isSetVar =
          list[currIndex.current.index]?.formulaList?.filter(
            (formItem) => formItem.value === null || formItem.value === ''
          ).length === 0;
        // eslint-disable-next-line prefer-destructuring
        list[currIndex.current.index].marketPriceVal = isSetVar ? res.priceList[0] : 0;
        setList([...list]);
      }
    },
    onFinally: () => {
      priceLoading.current = false;
    },
  });

  // const checkError = (arr = list) => {
  //   const errorList = arr.filter(
  //     (item) => item.marketPriceVal === null || item.marketPriceVal === undefined
  //   );
  //   const errIndexList = errorList.map((_item, index) => index);
  //   setErrorIndexList(errIndexList);
  //   return errIndexList;
  // };

  // const validateFields = () =>
  //   new Promise((resolve, reject) => {
  //     const errIndexList = checkError();
  //     if (errIndexList.length) {
  //       reject(errIndexList);
  //     } else {
  //       resolve(list);
  //     }
  //   });

  //
  const onBatchSetRetailPrice = (val: string) => {
    const value = val || null;
    const batchList = list.map((item) => ({
      ...item,
      marketPriceVal: value,
    }));
    setList(batchList);
    setBatchVal(value);
  };

  //
  const onBatchFormulaSetRetailPrice = (val: string | null, index: number) => {
    const oldVal = formulaData[index].value;
    formulaData[index].value = val;
    setFormulaData([...formulaData]);
    const formulaArr = formulaData.map((item) => ({ ...item }));
    if (formulaArr.filter((item) => item.value === null || item.value === '').length === 0) {
      if (oldVal !== val) {
        batchPriceLoading.current = true;
        batchRun({
          formulaId,
          formulaList: formulaArr,
          standardList,
          catId: categoryId,
          unit,
        });
      }
    } else {
      setList(
        list.map((item) => ({
          ...item,
          // marketPriceVal: res.priceList[0],
          formulaList: item.formulaList.map((formulaItem, index2) => ({
            ...formulaItem,
            value: formulaData[index2].value,
          })),
        }))
      );
    }
  };

  // 单个设置修改最小销售单元
  const onSingleSetRetailPrice = (val: string | null | number, index: number) => {
    list[index].marketPriceVal = val === '' ? null : val;
    // const ids = Array.from(new Set(list.map((item) => item.marketPriceVal) || []));
    // setBatchVal(ids.length > 1 ? null : ids[0]);
    setList([...list]);
    // checkError(list);
  };

  const onOk = () => {
    if (batchPriceLoading.current || priceLoading.current) {
      message.error(t('multiple_priceCalculationNotComplete'));
      return;
    }
    // console.log('list', list);
    // const priceList = list.map((item) => item.marketPriceVal);
    // if (priceList.filter((item) => !item).length) {
    //   message.error('商品价格不能为0');
    //   return;
    // }

    // validateFields().then(() => {
    const priceArr: (null | string | number)[] = [];
    const formulaListValues: VarListType[][] = [];
    list.forEach((item) => {
      formulaListValues.push(item.formulaList);
      const nullArr = item.formulaList.filter(
        (formItem) => formItem.value === null || formItem.value === ''
      );
      if (nullArr.length) {
        priceArr.push(null);
      } else {
        priceArr.push(item.marketPriceVal);
      }
    });
    onConfirm(priceArr, formulaListValues);
    closeDrawer(false);
    // });
  };

  const onclose = () => {
    closeDrawer(false);
    setBatchVal(null);
    // setErrorIndexList([]);
  };

  // useEffect(() => {
  //   const arr = dataList.map((item) => ({
  //     ...item,
  //     marketPriceVal: item.marketPrice,
  //     formulaList: item.formulaList || [],
  //   }));
  //   // const ids = Array.from(new Set(arr.map((item) => item.marketPriceVal) || []));
  //   // setBatchVal(ids.length > 1 ? null : ids[0]);
  //   setList(arr);
  // }, [dataList]);

  useEffect(() => {
    setFormulaData(formulaList?.map((item) => ({ ...item })) || []);
  }, [formulaList]);

  useEffect(() => {
    if (!isShow) {
      setList([]);
      setBatchPrice(0);
      setFormulaData((item) => item.map((item2) => ({ ...item2, value: null })));
      setBatchVal(null);
    } else {
      const arr = dataList.map((item) => ({
        ...item,
        marketPriceVal: item.marketPrice,
        formulaList: item.formulaList?.map((item2) => ({ ...item2 })) || [],
      }));
      // const ids = Array.from(new Set(arr.map((item) => item.marketPriceVal) || []));
      // setBatchVal(ids.length > 1 ? null : ids[0]);
      // if (!formulaData.length) {
      //   setBatchPrice(arr?.[0]?.marketPriceVal || 0);
      // }
      setList(arr);
    }
  }, [isShow, dataList]);

  const createBatchPrice = () => {
    let val = batchPrice;
    if (!formulaData.length) {
      val = list?.[0]?.marketPriceVal || 0;
    }
    return val;
  };

  return (
    <Drawer
      title={t('multiple_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      {formulaId ? (
        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>{t('batchFreightTemplateDrawer_batchSet')}</div>
          <div className={styles.batchItemColumn}>
            <div className={styles.batchItemColumnLabel}>{t('multiple_formulaName')}</div>
            <div className={styles.batchItemColumnValue}>{formulaName}</div>
          </div>
          <div className={classNames(styles.batchItemColumn, styles.bottomBorder)}>
            <div className={styles.batchItemColumnLabel}>{t('multiple_formula')}</div>
            <div className={styles.batchItemColumnValue}>{formatFormula}</div>
          </div>
          {formulaData?.map((item, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <div className={styles.batchItemColumn} key={index}>
              <div className={styles.batchItemColumnLabel}>{item.name}</div>
              <div className={styles.batchItemColumnValue}>
                <NumericInput
                  className={styles.input}
                  bordered={false}
                  placeholder={t('multiple_enterVariableValue')}
                  precision={15}
                  maxLength={20}
                  value={item.value ? `${item.value}` : ''}
                  onChange={(val) => onBatchFormulaSetRetailPrice(val as string | null, index)}
                />
              </div>
            </div>
          ))}
          <div className={styles.batchItemColumn}>
            <div className={styles.batchItemColumnLabel}>{t('multiple_retailPrice2')}</div>
            <div className={classNames(styles.price, styles.batchItemColumnValue)}>
              ￥{createBatchPrice()}
            </div>
          </div>
        </div>
      ) : (
        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>{t('batch_set')}</div>
          <div className={styles.batchItemColumn}>
            <div className={styles.batchItemLabel}>{t('multiple_retailPrice2')}</div>
            <div className={styles.batchItemColumnValue}>
              <NumericInput
                className={styles.input}
                bordered={false}
                placeholder={t('multiple_enterVariableValue2')}
                suffix={t('public_moneySymbol2')}
                value={batchVal ? `${batchVal}` : ''}
                precision={list?.[0]?.priceNum || 0}
                maxLength={(list?.[0]?.priceNum ? (list?.[0]?.priceNum || 0) + 1 : 0) + 8}
                onChange={(val) => onBatchSetRetailPrice(val)}
              />
            </div>
          </div>
        </div>
      )}

      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('multiple_singleSet')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>
              {(item?.standardList.map((standItem) => standItem.value) || []).join('/') || ''}
            </div>
            {formulaId ? (
              <div>
                {item?.formulaList?.map((formulaItem, formulaIndex) => (
                  // eslint-disable-next-line react/no-array-index-key
                  <div className={styles.batchItemColumn} key={formulaIndex}>
                    <div className={styles.batchItemColumnLabel}>{formulaItem.name}</div>
                    <div className={styles.batchItemColumnValue}>
                      <NumericInput
                        className={styles.input}
                        bordered={false}
                        placeholder={t('multiple_enterVariableValue')}
                        precision={15}
                        maxLength={20}
                        onChange={(val) => {
                          const newFormulaItem = formulaItem;
                          const oldVal = newFormulaItem.value;
                          newFormulaItem.value = String(val ?? '');
                          if (!val) {
                            const newItem = item;
                            newItem.marketPriceVal = 0;
                          }
                          currIndex.current = { index, formulaIndex };
                          formulaDataRef.current = list;
                          setList([...list]);
                          const formulaArr = list[index].formulaList?.map((item2) => ({
                            ...item2,
                          }));
                          const nullLength = formulaArr.filter(
                            (formItem) => formItem.value === null || formItem.value === ''
                          ).length;
                          if (nullLength === 0) {
                            if (oldVal !== val) {
                              priceLoading.current = true;
                              run({
                                formulaId,
                                formulaList: formulaArr || [],
                                standardList: [item.standardList],
                                catId: categoryId,
                                unit,
                              });
                            }
                          }
                        }}
                        value={formulaItem.value ? `${formulaItem.value}` : ''}
                      />
                    </div>
                  </div>
                ))}
                <div className={styles.batchItemColumn}>
                  <div className={styles.batchItemColumnLabel}>{t('multiple_retailPrice2')}</div>
                  <div className={classNames(styles.price, styles.batchItemColumnValue)}>
                    ￥{item.marketPriceVal || 0}
                  </div>
                </div>
              </div>
            ) : (
              <div className={styles.batchItemColumn}>
                <div className={styles.batchItemColumnLabel}>{t('multiple_retailPrice2')}</div>
                <div className={styles.batchItemColumnValue}>
                  <NumericInput
                    bordered={false}
                    placeholder={t('multiple_enterVariableValue2')}
                    suffix={t('public_moneySymbol2')}
                    max={99999999}
                    maxLength={(list?.[0]?.priceNum ? (list?.[0]?.priceNum || 0) + 1 : 0) + 8}
                    precision={list?.[0]?.priceNum || 0}
                    onChange={(val) => onSingleSetRetailPrice(val, index)}
                    value={item.marketPriceVal ? `${item.marketPriceVal}` : ''}
                  />
                  {/* <InputNumber
                    className={styles.input}
                    bordered={false}
                    placeholder="请输入金额"
                    value={item.marketPriceVal}
                    addonAfter="元"
                    maxLength={(list?.[0]?.priceNum || 0) + 9}
                    onChange={(val) => onSingleSetRetailPrice(val, index)}
                    formatter={(val) => limitDecimalsF(val, list?.[0]?.priceNum || 0)}
                    parser={(val) => limitDecimalsP(val, list?.[0]?.priceNum || 0, 8) as number}
                    // max={99999999}
                    // maxLength={9}
                  /> */}

                  {/* <div
                    className={styles.errorMsg}
                    style={{ display: errorIndexList.includes(index) ? 'block' : 'none' }}
                  >
                    请输入零售价
                  </div> */}
                </div>
              </div>
            )}
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchRetailPriceDrawer;
