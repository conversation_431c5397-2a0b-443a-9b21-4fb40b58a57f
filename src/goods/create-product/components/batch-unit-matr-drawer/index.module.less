.cardBox {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.cardTitle {
  font-size: 16px;
  font-weight: bolder;
  margin-bottom: 17px;
}

.basicTitle {
  color: #888b98;
  margin-bottom: 17px;
}

.batchBox {
  display: flex;
  flex-direction: column;
  // align-items: center;
  // justify-content: space-between;
}

.batchLabel {
  width: 100%;
}

.batchValue {
  width: 100%;
  margin-top: 8px;
}

.selectInput {
  width: 100%;

  :global {
    .ant-select-selector {
      font-size: 16px;
      padding: 0 !important;
    }
  }
  // text-align: right;
}

.varTxetBox {
  text-align: left;
}

.cardName {
  display: -webkit-box;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.line {
  margin: 16px 0;
}

.setMutUnitVar,
.setMutUnitVarErr {
  margin-top: 8px;
  margin-right: 16px;
  cursor: pointer;
  text-align: right;
}

.setMutUnitVar {
  color: #008cff;
}

.setMutUnitVarErr {
  color: red;
}
