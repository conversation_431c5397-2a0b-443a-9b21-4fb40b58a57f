import { useEffect, useState, useRef, ReactNode } from 'react';
import { Select, Divider, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, Icon } from '@/components';
import { getUnitFormulaOptionsByUnit } from '@/apis';
import { ResultType as UnitFormulaOptionType } from '@/apis/base/get-unit-formula-options';
import { TableRowType, VarListType } from '@/apis/pms/type';
import SetVariableDrawer from '../set-variable-drawer';
import styles from './index.module.less';

interface MatrDrawerType {
  isShow: boolean;
  unit: string;
  dataList: TableRowType[];
  // closeDrawer: Dispatch<SetStateAction<boolean>>;
  closeDrawer: MultipleParamsFn<[value: boolean]>;
  onConfirm: MultipleParamsFn<[values: TableRowType[]]>;
}

function BatchUnitMatrDrawer({ isShow, unit, dataList, closeDrawer, onConfirm }: MatrDrawerType) {
  const { t } = useTranslation();
  const [showSetVar, setShowSetVar] = useState(false);
  const [variableDefaultValues, setVariableDefaultValues] = useState<VarListType[]>([]);
  const [currUnitInfo, setCurrUnitInfo] = useState({ varList: [] } as UnitFormulaOptionType);
  const [unitFormulaSelect, setUnitFormulaSelect] = useState<UnitFormulaOptionType[]>([]);
  const [list, setList] = useState<TableRowType[]>([]);
  const [batchUnitTemplateId, setBatchUnitTemplateId] = useState<number | null>(null);
  const [batchTemplateFormulaNameList, setBatchTemplateFormulaNameList] = useState<VarListType[]>(
    []
  );
  // const [batchHasUnitTemplateVar, setBatchHasUnitTemplateVar] = useState(false);
  // const [batchTemplateFormulaArgsList, setBatchTemplateFormulaArgsList] = useState<
  //   {
  //     id: number;
  //     value: string;
  //   }[]
  // >([]);
  const currIndex = useRef(-1);

  // const allSet = (arr: TableRowType[]) => {
  //   const ids = Array.from(new Set(arr.map((item2) => item2.unitTemplateId) || []));
  //   const isAll = ids.length === 0;
  //   setBatchUnitTemplateId(isAll ? null : ids[0]);
  // };

  // 批量设置 选择换算关系
  const onBatchSetUnitMatr = (val: number, option: UnitFormulaOptionType) => {
    const varData =
      option?.varList?.map((item: any) => ({
        id: item.id,
        name: item.name,
        value: '',
      })) || [];
    const batchList = list.map((item) => ({
      ...item,
      unitTemplateId: val,
      templateFormulaNameList: varData,
      // ...(varData.length ? { templateFormulaNameList: varData } : {}),
    }));

    if (option?.varList?.length) {
      currIndex.current = -1;
      setCurrUnitInfo(option);
      setVariableDefaultValues([]);
      setShowSetVar(true);
    }
    setBatchTemplateFormulaNameList(varData);
    // setBatchHasUnitTemplateVar(Boolean(option?.varList?.length));
    setList(batchList);
    setBatchUnitTemplateId(val);
  };

  // 单个 设置 选择换算关系
  const onSingleSetUnitMatr = (val: number, index: number, option: UnitFormulaOptionType) => {
    const varData =
      option?.varList?.map((item: any) => ({
        id: item.id,
        name: item.name,
        value: '',
      })) || [];
    currIndex.current = index;
    const item = list[index];
    item.unitTemplateId = val;
    item.templateFormulaNameList = varData;
    if (option?.varList?.length) {
      setCurrUnitInfo(option);
      setVariableDefaultValues([]);
      setShowSetVar(true);
    }
    // allSet(list);
    setBatchUnitTemplateId(null);
    // setBatchHasUnitTemplateVar(false);
    setBatchTemplateFormulaNameList([]);
    setList([...list]);
    // setList(list.map((item,listIndex)=>({...item,...(item.templateFormulaNameList.length?{templateFormulaNameList:item.templateFormulaNameList.map((templateItem)=>({...templateItem,value:}))}:{})})));
  };

  const onOk = () => {
    onConfirm(list);
    closeDrawer(false);
  };

  const onclose = () => {
    closeDrawer(false);
  };

  useEffect(() => {
    if (isShow && unit) {
      setBatchUnitTemplateId(null);
      getUnitFormulaOptionsByUnit({ unitName: unit }).then((res) => {
        setUnitFormulaSelect(res.list);
      });
    }
    if (isShow) {
      setVariableDefaultValues([]);
      setCurrUnitInfo({ varList: [] } as UnitFormulaOptionType);
      setBatchUnitTemplateId(null);
      setBatchTemplateFormulaNameList([]);
      if (dataList) {
        setList([...dataList]);
      }
    } else {
      setList([]);
      setShowSetVar(false);
    }
  }, [isShow, unit, dataList]);

  // useEffect(() => {
  //   setList([...dataList]);
  //   console.log('dataList', dataList);

  //   // const ids = Array.from(new Set(dataList.map((item) => item.unitTemplateId) || []));
  //   // setBatchUnitTemplateId(ids.length > 1 ? null : ids[0]);
  // }, [dataList]);

  const createVar = (templateFormulaNameList: VarListType[]) => {
    const isHasVar = templateFormulaNameList?.length || false;

    const isSetVar =
      templateFormulaNameList?.filter((item) => item.value === null || item.value === '').length ===
      0;
    const varText = isSetVar
      ? templateFormulaNameList
          ?.map((formItem) => `${formItem.name || ''}:${formItem.value}`)
          .join(',')
      : '';
    let varElement: ReactNode | null = null;
    if (isHasVar) {
      if (varText) {
        varElement = (
          <div className={styles.setMutUnitVar}>
            <Tooltip placement="topLeft" title={varText}>
              <div>
                {templateFormulaNameList?.map((formItem, formIndex) =>
                  formIndex < 3 ? (
                    <div key={formItem.id} className={styles.varTxetBox}>
                      {formItem.name}:{formItem.value}
                      {templateFormulaNameList.length > 3 && formIndex === 2 ? (
                        <span>
                          ... <Icon name="right" size={12} />
                        </span>
                      ) : (
                        templateFormulaNameList.length === formIndex + 1 && (
                          <span>
                            <Icon name="right" size={12} />
                          </span>
                        )
                      )}
                    </div>
                  ) : null
                )}
              </div>
            </Tooltip>
          </div>
        );
      } else {
        varElement = (
          <div className={styles.setMutUnitVarErr}>
            {t('setVariableDrawer_enterVariableValue')}
            <Icon name="right" size={12} />
          </div>
        );
      }
    }
    return varElement;
  };

  return (
    <Drawer
      title={t('batchUnitMatrDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      style={{ transform: 'translateX(0) !important' }}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batch_set')}</div>
        <div className={styles.basicTitle}>
          {t('base_basicUnit')}: {list.length ? list[0]?.unit : ''}
        </div>
        <div className={styles.batchBox}>
          <div className={styles.batchLabel}>{t('base_unitConversion')}</div>
          <div className={styles.batchValue}>
            <Select
              className={styles.selectInput}
              bordered={false}
              allowClear
              placeholder={t('select_conversion_relation')}
              options={unitFormulaSelect}
              fieldNames={{
                label: 'templateName',
                value: 'id',
              }}
              onChange={(val, option) => {
                onBatchSetUnitMatr(val, option as UnitFormulaOptionType);
              }}
              value={batchUnitTemplateId}
            />
            {batchTemplateFormulaNameList.length > 0 && (
              <div
                role="presentation"
                onClick={() => {
                  currIndex.current = -1;
                  setVariableDefaultValues(batchTemplateFormulaNameList);
                  setShowSetVar(true);
                }}
                className={styles.varTxetBox}
              >
                {createVar(batchTemplateFormulaNameList)}
              </div>
            )}
          </div>
        </div>
      </div>
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('single_set')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>
              {(item?.standardList.map((standItem) => standItem.value) || []).join('/') || ''}
            </div>
            <div className={styles.batchBox}>
              <div className={styles.batchLabel}>{t('base_unitConversion')}</div>
              <div className={styles.batchValue}>
                <Select
                  className={styles.selectInput}
                  bordered={false}
                  allowClear
                  value={item.unitTemplateId || null}
                  placeholder={t('select_conversion_relation')}
                  options={unitFormulaSelect}
                  fieldNames={{
                    label: 'templateName',
                    value: 'id',
                  }}
                  onChange={(val, option) =>
                    onSingleSetUnitMatr(val, index, option as UnitFormulaOptionType)
                  }
                />
                {item.templateFormulaNameList?.length > 0 && (
                  <div
                    role="presentation"
                    onClick={() => {
                      currIndex.current = index;
                      const unitArr = unitFormulaSelect.filter(
                        (unitItem) => unitItem.id === item.unitTemplateId
                      );
                      if (unitArr.length) {
                        setCurrUnitInfo(unitArr[0]);
                      }
                      setVariableDefaultValues(item.templateFormulaNameList);
                      setShowSetVar(true);
                    }}
                  >
                    {createVar(item.templateFormulaNameList)}
                  </div>
                )}
              </div>
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>

      <SetVariableDrawer
        visible={showSetVar}
        drawerType="bottom"
        defaultValues={variableDefaultValues}
        closeDrawer={setShowSetVar}
        dataInfo={{ ...currUnitInfo }}
        onConfirm={(val, values) => {
          if (currIndex.current === -1) {
            const data = list.map((item) => ({
              ...item,
              unitTemplateId: val.id,
              templateFormulaNameList: values,
            }));
            setList(data);
            setBatchTemplateFormulaNameList(values);
            // console.log('currIndex.current === -1');
          } else {
            list[currIndex.current].unitTemplateId = val.id;
            list[currIndex.current].templateFormulaNameList = values;
            setList([...list]);
            // console.log('list', list);
          }
        }}
      />
    </Drawer>
  );
}

export default BatchUnitMatrDrawer;
