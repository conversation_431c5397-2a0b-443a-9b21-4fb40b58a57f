import { useMemo, useState } from 'react';
import { Collapse } from 'antd';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/components';
import { StandardItemType } from '@/apis/pms/type';
import styles from './index.module.less';

interface PropsType {
  standardList: StandardItemType[];
  total: number;
  onDelete: MultipleParamsFn<[type: number, i1: number, i2: number]>;
  onAdd: () => void;
}

const { Panel } = Collapse;

function SpecificationsViewComponent({ standardList, total, onDelete, onAdd }: PropsType) {
  const { t } = useTranslation();
  const [activeKey, setActiveKey] = useState<string | string[]>([]);
  const [collapseArr, setCollapseArr] = useState<string[]>([]);
  const count = useMemo(
    () => standardList.reduce((sum, item) => sum + item.values.length, 0),
    [standardList]
  );

  return (
    <div>
      <div>{t('specificationsView_specAttributes')}</div>
      <Collapse bordered={false} ghost onChange={setActiveKey}>
        <Panel
          key="default"
          showArrow={false}
          collapsible="header"
          className={styles.panel}
          header={
            <div className={styles.panelHeader}>
              {t('specificationsView_selectedSpecs', {
                groups: standardList.length,
                specs: count,
                products: total,
              })}
              {activeKey.length ? (
                <span className="ml-3">
                  {t('specificationsView_collapse')}
                  <Icon name="up" size={20} />
                </span>
              ) : (
                <span className="ml-3">
                  {t('specificationsView_expand')}
                  <Icon name="down" size={20} />
                </span>
              )}
            </div>
          }
          extra={
            <span
              role="button"
              tabIndex={0}
              onClick={() => {
                onAdd();
              }}
              className={styles.panelExtra}
            >
              <Icon name="plus" size={16} className="mr-1" />
              {t('specificationsView_addSpec')}
            </span>
          }
        >
          {standardList.map((items, i1) => (
            <div key={items.name}>
              <Collapse
                key={items.name}
                bordered={false}
                ghost
                className="mt-4"
                onChange={(val) => {
                  setCollapseArr(val as string[]);
                }}
              >
                <Panel
                  key={items.name}
                  showArrow={false}
                  collapsible="header"
                  className={styles.subPanel}
                  header={
                    <div className={styles.subPanelHeader}>
                      {t('specificationsView_specName', {
                        name: items.name,
                        count: items.values.length,
                      })}
                      <Icon name={!collapseArr.includes(items.name) ? 'down' : 'up'} size={20} />
                    </div>
                  }
                  extra={
                    <span
                      role="button"
                      tabIndex={0}
                      onClick={() => {
                        onDelete(1, i1, -1);
                      }}
                      className={styles.subPanelExtra}
                    >
                      <Icon name="trash" size={16} className="mr-1" />
                      {t('specificationsView_delete')}
                    </span>
                  }
                >
                  <div className={styles.subPanelBody}>
                    {items.values.map((item, i2) => (
                      <div key={item.value} className={styles.item}>
                        <div>{item.value}</div>
                        <div
                          role="button"
                          tabIndex={0}
                          onClick={() => {
                            onDelete(2, i1, i2);
                          }}
                        >
                          <img
                            className={styles.itemDelIcon}
                            src="https://img.huahuabiz.com/user_files/2023323/1679551070564444.png"
                            alt=""
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </Panel>
              </Collapse>
            </div>
          ))}
        </Panel>
      </Collapse>
    </div>
  );
}

SpecificationsViewComponent.displayName = 'SpecificationsViewComponent';

SpecificationsViewComponent.defaultProps = {};

export default SpecificationsViewComponent;
