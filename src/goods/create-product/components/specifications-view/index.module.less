.panel {
  :global {
    .ant-collapse-header {
      height: 38px;
      line-height: 22px !important;
      padding: 8px 0 !important;
    }

    .ant-collapse-content-box {
      padding: 0 !important;
    }
  }
}

.panelHeader {
  color: #888b98;
  font-size: 14px;
  height: 22px;
}

.panelExtra,
.subPanelExtra {
  color: #008cff;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.subPanel {
  background: #f5f6fa;
  border-radius: 10px !important;
  padding: 0 16px;

  :global {
    .ant-collapse-header {
      height: 54px;
      line-height: 22px !important;
      padding: 16px 0 !important;
    }

    .ant-collapse-content-box {
      padding: 16px 0 8px !important;
      border-top: 1px solid rgb(177 179 190 / 20%);
    }
  }
}

.subPanelHeader {
  color: #040919;
  font-size: 14px;
  font-weight: 500;
}

.subPanelExtra {
  color: #999eb2;

  &:hover {
    color: #f90400;
  }
}

.subPanelBody {
  display: flex;
  margin-right: -12px;
  flex-wrap: wrap;
}

.item {
  color: #040919;
  font-size: 14px;
  height: 28px;
  line-height: 26px;
  margin-right: 12px;
  margin-bottom: 12px;
  padding: 0 15px;
  position: relative;
  border-radius: 10px;
  background-color: #fff;
  border: 1px solid #fff;
  cursor: pointer;

  .itemDelIcon {
    content: '';
    display: none;
    width: 12px;
    height: 12px;
    position: absolute;
    top: -4px;
    right: -4px;
    z-index: 1;
    cursor: pointer;
    background: url('https://img.huahuabiz.com/user_files/2023323/1679551070564444.png') no-repeat;
    background-size: 12px 12px;
  }

  &:hover {
    color: #00c6ff;
    border-color: #00c6ff;

    .itemDelIcon {
      display: block;
    }
  }
}
