:global {
  .ant-modal-confirm .ant-modal-body {
    padding: 20px !important;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .searchInput {
    width: 292px;
    box-shadow: initial !important;
  }

  .sortIcon {
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}

.addGroup {
  color: #008cff;
  cursor: pointer;
}

.addGroupEn {
  font-size: 13px;
  max-width: 80px;
  margin-left: auto;
  overflow: hidden;
  float: right;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.noteText {
  color: #888b98;
  height: auto;
  line-height: 40px;

  .noteIcon {
    margin-right: 8px;
  }
}

.nullText {
  margin-bottom: 10px;
}

.collapseCard {
  :global {
    .ant-collapse {
      .ant-collapse-item {
        .ant-collapse-header {
          .ant-collapse-extra {
            padding-left: 0 !important;
          }
        }
      }
    }
  }
}

.titleOperate {
  display: flex;
  justify-content: space-between;

  .titleNameBox {
    display: flex;

    .titleName {
      font-weight: 600;
      max-width: 170px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .nameText {
      color: #05d380;
      font-size: 12px;
      font-weight: bold;
      width: 32px;
      height: 20px;
      line-height: 20px;
      margin-right: 4px;
      text-align: center;
      background-color: #d0efe2;
      border-radius: 2px;
    }

    .nameNumber {
      color: #ea1c26;
      font-size: 12px;
      font-weight: bold;
      width: auto;
      height: 20px;
      line-height: 20px;
      margin-right: 4px;
      text-align: center;
      background-color: #fcdddf;
      border-radius: 2px;
    }

    .editNameIcon {
      color: #000;
      margin-left: 6px;
      cursor: pointer;
    }

    .editNameIcon:hover {
      color: #008cff;
    }
  }

  .addGroup {
    color: #008cff;
    cursor: pointer;
  }
}

.btn {
  text-align: center;
}

.footerBox {
  display: flex;
  align-items: center;
  padding: 24px 20px;
  background: linear-gradient(
    180deg,
    rgb(255 255 255 / 1%) 0%,
    rgb(255 255 255 / 36%) 12%,
    rgb(255 255 255 / 58%) 25%,
    rgb(255 255 255 / 72%) 36%
  );
}
