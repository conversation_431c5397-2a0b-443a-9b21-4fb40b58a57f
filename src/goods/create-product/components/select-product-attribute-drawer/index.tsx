/* eslint-disable no-use-before-define */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dispatch, SetStateAction, useEffect, useMemo, useState, useRef } from 'react';
import { Dropdown, Menu, message, Modal, Button } from 'antd';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import { Drawer, Search, Icon, FilterCard, Empty } from '@/components';
import { FilterCardRefType } from '@/components/filter-card/filter-card';
import {
  getStandardList,
  addStandardBase,
  addStandardDetail,
  updateStandardBase,
  deleteStandardBase,
  deleteStandardDetail,
  updateCustomizeStandardSort,
} from '@/apis';
import { useTranslation } from 'react-i18next';
import { ResultType } from '@/apis/get-standard-list';
import { StandardItemType } from '@/apis/pms/type';
import type { FilterItemType } from '@/components';
import { user } from '@/store';
import { arrayObjectDeduplication } from '@/utils/utils';
import { testPerm } from '@/utils/permission';
import AddGroupDrawer from '../add-group-drawer';
import sortIcon from '../../../assets/img/sort.png';
import styles from './index.module.less';

import SortDrawer from '../sort-drawer';

interface programProps {
  isShow: boolean;
  condition: Record<string, string[]>;
  type: 'name' | 'spec';
  multiple?: boolean; // 名称是否可多选
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  categoryId: number; // 选择所属分类id
  onSelectTag: SimpleFn<Array<StandardItemType>>;
  onSelectedRemoveSuccess: MultipleParamsFn<[type: string, val: number, data: any]>;
}

function SelectProductAttributeDrawer(props: programProps) {
  const {
    isShow,
    condition,
    type,
    multiple,
    closeDrawer,
    categoryId,
    onSelectTag,
    onSelectedRemoveSuccess,
  } = props;
  const { t, i18n } = useTranslation();
  // const { i18n: useI18nI18n } = useI18n();
  const title = useMemo(
    () =>
      type === 'name'
        ? t('selectProductAttributeDrawer_name')
        : t('selectProductAttributeDrawer_spec'),
    [type, t]
  );

  const [productList, setProductList] = useState<FilterItemType[]>([]);
  const [searchName, setSearchName] = useState('');

  const [selectedGroupIds, setSelectedGroupIds] = useState<number[]>([]);
  const sortDrawerRef = useRef<any>(null);
  const addRef = useRef<any>(null);
  const editRef = useRef<any>();
  const addTagRef = useRef<any>();
  const itemTag = useRef<any>({ list: [] });
  const initItemTag = useRef<any>({ list: [] });
  const filterCardListRef = useRef<FilterCardRefType[] | null[]>([]);

  // 生成默认选中的数据，用于item回显
  const createDefaultValues = (val: ResultType) => {
    const defaultValues: number[] = [];
    if (condition) {
      const conditionKeys = Object.keys(condition);
      if (conditionKeys.includes(val.attrbute)) {
        val?.standardDetail.forEach((item) => {
          if (condition?.[val.attrbute]?.includes(item.detailName)) {
            defaultValues.push(item.id);
          }
        });
      }
    }
    if (itemTag.current.list.length) {
      itemTag.current.list.forEach((item: any) => {
        if (item.values.length && val.id === item.standardId) {
          item.values?.forEach((valItem: any) => {
            val?.standardDetail.forEach((detailItem) => {
              if (
                detailItem.detailName === valItem.value &&
                !defaultValues.includes(detailItem.id)
              ) {
                defaultValues.push(detailItem.id);
              }
            });
          });
        }
      });
    }
    return defaultValues;
  };

  // 生成默认选中的数据，用于itemTag初始化
  const createDefaultTagValues = (prodList: FilterItemType[]) => {
    const tags: (StandardItemType & { standardId?: string })[] = [];
    prodList.forEach((proItem) => {
      proItem?.children?.forEach((item) => {
        if (proItem.defaultValues?.length && proItem.defaultValues?.includes(item.value)) {
          tags.push({
            standardId: proItem.id,
            // @ts-ignore
            name: proItem.attrbute,
            useWay: 2,
            values: [
              {
                maxValue: 0,
                minStepValue: 0,
                minValue: 0,
                // @ts-ignore
                type: item.type,
                value: item.label,
              },
            ],
          });
        }
      });
    });
    return tags;
  };

  // 页面初始化数据(获取商品名称分组)
  const initialPageList = (isInit = false) => {
    getStandardList({ catId: categoryId, useWay: type === 'name' ? 1 : 2 }).then((res) => {
      // 商品名称，在这里将数据洗成组件支持的数据结构
      const goodsNameList: any = res.list.map((item) => ({
        ...item,
        value: item.id,
        showMoreItem: item.standardDetail.length > 8,
        moreItemText: t('paymentManage_expand'),
        collapsible: 'disabled',
        key: item.id,
        isShow: true,
        type: multiple ? 'checkbox' : 'radio',
        extra: (
          <div className={styles.titleOperate} key={item.id}>
            <div className={styles.titleNameBox}>
              <div>
                {item.valueType === 1 ? (
                  <div className={styles.nameText}>{t('selectProductAttributeDrawer_text')}</div>
                ) : (
                  <div className={styles.nameNumber}>
                    {t('selectProductAttributeDrawer_number')}
                  </div>
                )}
              </div>
              <div className={styles.titleName} title={item.attrbute}>
                {item.attrbute}
              </div>
              {item.companyId === user.companyId && (
                <Dropdown
                  overlay={
                    <Menu>
                      <div tabIndex={-1} role="button" onClick={(e) => e.stopPropagation()}>
                        <Menu.Item
                          className={styles.btn}
                          onClick={() => {
                            if (type === 'name') {
                              if (!testPerm('M_001_013_001_005')) {
                                return;
                              }
                            } else if (!testPerm('M_001_013_002_005')) {
                              return;
                            }
                            editRef.current.openDrawer();
                            editRef.current.setFormData(item);
                          }}
                        >
                          <span>{t('common_edit')}</span>
                        </Menu.Item>
                        <Menu.Item
                          className={styles.btn}
                          onClick={() => {
                            if (type === 'name') {
                              if (!testPerm('M_001_013_001_004')) {
                                return;
                              }
                            } else if (!testPerm('M_001_013_002_004')) {
                              return;
                            }
                            onDeleteGroup(item.id, item.attrbute);
                          }}
                        >
                          <span>{t('common_deleteGroup')}</span>
                        </Menu.Item>
                      </div>
                    </Menu>
                  }
                  placement="bottom"
                >
                  <Icon name="edit1" size={16} className={styles.editNameIcon} color="#999eb2" />
                </Dropdown>
              )}
            </div>
            <div
              role="button"
              tabIndex={-1}
              className={styles.addGroup}
              onClick={() => {
                if (type === 'name') {
                  if (!testPerm('M_001_013_001_005')) {
                    return;
                  }
                } else if (!testPerm('M_001_013_002_005')) {
                  return;
                }
                addTagRef.current.openDrawer();
                addTagRef.current.addTagItem(item);
              }}
            >
              {t('selectProductAttributeDrawer_addValue')}
            </div>
          </div>
        ),
        children: item?.standardDetail?.map((every, index) => ({
          ...every,
          label: every.detailName,
          value: every.id,
          isHidden: index > 7,
          hiddenClose: every.companyId !== user.companyId,
        })),
        defaultValues: createDefaultValues(item),
      }));

      setProductList(goodsNameList);
      itemTag.current.list = createDefaultTagValues(goodsNameList);
      const tagIds = Array.from(new Set(itemTag.current.list.map((item: any) => item.standardId)));
      setSelectedGroupIds(tagIds.length > 2 ? tagIds : goodsNameList.map((item: any) => item.id));
      if (isInit) {
        initItemTag.current.list = itemTag.current.list;
      }
    });
  };

  // 删除组
  const onDeleteGroup = (delGroupId: number, groupName: string) => {
    Modal.confirm({
      title: t('common_prompt'),
      content: t('selectProductAttributeDrawer_confirmDelete', { groupName }),
      okText: t('public_confirm'),
      cancelText: t('public_cancel'),
      width: 311,
      getContainer: '.ant-drawer-wrapper-body',
      centered: true,
      icon: '',
      onCancel: () => {},
      onOk: () => {
        deleteStandardBase({ id: delGroupId, catId: categoryId }).then(() => {
          message.success(t('common_deleteSuccess'));
          const { list } = initItemTag.current;
          const tagList = list?.filter((item: any) => item.standardId !== delGroupId);
          itemTag.current.list = itemTag.current.list?.filter(
            (item: any) => item.standardId !== delGroupId
          );
          if (tagList.length !== list.length) {
            const newList = mergeArray(tagList, 'standardId', 'values');
            onSelectedRemoveSuccess('group', delGroupId, newList);
            initialPageList(true);
          } else {
            initialPageList();
          }
        });
      },
    });
  };

  // 新增商品名称和规格
  const onAddGroup = (value: any, activeType: number, tagList: any) => {
    addStandardBase({
      source: 1,
      attrbute: value.attrbute,
      useWay: type === 'name' ? 1 : 2,
      valueType: value.valueType || 0,
      catId: categoryId,
      standardDetailParam: {
        detailName: tagList,
        type: activeType,
        minValue: value.minValue,
        maxValue: value.maxValue,
        minStepValue: value.minStepValue,
      },
    }).then(() => {
      message.success(t('common_addSuccess'));
      addRef.current.closeDrawer();
      initialPageList();
    });
  };

  // 编辑商品名称
  const onEditGroup = (value: any, editId: number) => {
    updateStandardBase({
      id: editId,
      attrbute: value.attrbute,
      valueType: value.valueType,
      useWay: type === 'name' ? 1 : 2,
    }).then(() => {
      message.success(t('common_saveSuccess'));
      editRef.current.closeDrawer();
      initialPageList();
    });
  };

  // 添加组中的标签
  const onAddGroupTag = (dataInfo: any, activeType: number, formInfo: any, tagList: any) => {
    addStandardDetail({
      standardId: dataInfo?.id,
      detailName: tagList,
      source: 1,
      type: activeType,
      minValue: formInfo?.standardDetailParamList?.minValue,
      maxValue: formInfo?.standardDetailParamList?.maxValue,
      minStepValue: formInfo?.standardDetailParamList?.minStepValue,
    }).then(() => {
      message.success(t('common_saveSuccess'));
      addTagRef.current.closeDrawer();
      initialPageList();
    });
  };

  // 删除item
  const deleteItem = (value: any, item: any) => {
    const { list } = initItemTag.current;
    const size = list.length;
    for (let index = 0; index < list.length; index += 1) {
      const tag = list[index];
      if (tag.name === item.attrbute && tag.values?.[0]?.value === value?.label) {
        list.splice(index, 1);
        index -= 1;
      }
    }
    for (let index = 0; index < itemTag.current.list.length; index += 1) {
      const tag = itemTag.current.list[index];
      if (tag.name === item.attrbute && tag.values?.[0]?.value === value?.label) {
        itemTag.current.list.splice(index, 1);
        index -= 1;
      }
    }

    Modal.confirm({
      title: t('common_prompt'),
      content: t('selectProductAttributeDrawer_confirmDelete', { groupName: value.detailName }),
      okText: t('public_confirm'),
      cancelText: t('public_cancel'),
      width: 311,
      getContainer: '.ant-drawer-wrapper-body',
      centered: true,
      icon: <div />,
      onCancel: () => {},
      onOk: () => {
        deleteStandardDetail({ id: value.id }).then(() => {
          message.success('删除成功');
          if (size !== list.length) {
            const newList = mergeArray(list, 'standardId', 'values');
            onSelectedRemoveSuccess('item', value.id, newList);
            initialPageList(true);
          } else {
            initialPageList();
          }
        });
      },
    });
  };

  // 选中的值
  const onAddItem = (value: any, active: boolean, info: any) => {
    const selectObj = {
      ...value,
      useWay: info.useWay,
      name: info.attrbute,
      standardId: value.standardId,
      id: value.id,
      values: [
        {
          value: value.detailName,
          type: value.type,
          minStepValue: value?.minStepValue || 0,
          minValue: value?.minValue || 0,
          maxValue: value?.maxValue || 0,
        },
      ],
    };

    // 单选
    if (!multiple) {
      if (active) {
        const index = itemTag.current.list
          .map((item: any) => item.standardId)
          .indexOf(value.standardId);
        if (index > -1) {
          itemTag.current.list.splice(index, 1);
          itemTag.current.list.push(selectObj);
        } else {
          itemTag.current.list.push(selectObj);
        }
      } else {
        const index = itemTag.current.list.map((item: any) => item.id).indexOf(value.id);
        itemTag.current.list.splice(index, 1);
      }
    }

    const checkGroupLength = () =>
      arrayObjectDeduplication(itemTag.current.list || [], 'standardId').length;

    // 多选
    if (multiple) {
      if (active) {
        if (type === 'spec') {
          if (checkGroupLength() < 3) {
            itemTag.current.list.push(selectObj);
          } else {
            if (selectedGroupIds.includes(selectObj.standardId)) {
              itemTag.current.list.push(selectObj);
            }
            setSelectedGroupIds(
              Array.from(new Set(itemTag.current.list.map((item: any) => item.standardId)))
            );
          }
        } else {
          itemTag.current.list.push(selectObj);
        }
      } else {
        // const index = itemTag.current.list
        //   .map((item: any) => item.standardId)
        //   .indexOf(value.standardId);
        // itemTag.current.list.splice(index, 1);

        for (let i = 0; i < itemTag.current.list.length; i += 1) {
          const tagItem = itemTag.current.list[i];
          if (tagItem.standardId === value.standardId) {
            if (tagItem?.values?.length && tagItem?.values?.[0]?.value === value.detailName) {
              itemTag.current.list.splice(i, 1);
            }
          }
        }
      }
      if (checkGroupLength() === 3 && type === 'spec') {
        setSelectedGroupIds(
          Array.from(new Set(itemTag.current.list.map((item: any) => item.standardId)))
        );
      } else {
        setSelectedGroupIds(productList.map((item: any) => item.id));
      }
    }
  };

  // 搜索名称
  const onSearchName = (value: string) => {
    const newProductList: any = productList.map((item) => {
      const children = item?.children?.map((every, index) => ({
        ...every,
        isHidden: value ? every.label.indexOf(value) : index > 7,
      }));
      return {
        ...item,
        children,
        showMoreItem: value === '' && children.filter((child) => !child.isHidden).length > 7,
      };
    });
    setProductList(newProductList);
    setSearchName(value);
  };

  const checkGroupShow = (item: any) =>
    searchName === '' || item?.children?.filter((child: any) => !child.isHidden).length;

  // 根据productList，通过checkGroupShow判断所有item 是否显示
  const checkAllGroupShow = () => productList.filter((item) => checkGroupShow(item)).length === 0;

  // 保存排序
  const okSort = (sortList: number[]) => {
    updateCustomizeStandardSort({
      standardIdList: sortList,
      useWay: type === 'name' ? 1 : 2,
      catId: categoryId,
    }).then(() => {
      sortDrawerRef.current.closeDrawer();
      initialPageList();
    });
  };

  // 关闭选择商品名称侧边栏
  const onClose = () => {
    closeDrawer(false);
    itemTag.current.list = [];
    initItemTag.current.list = [];
  };

  const onCancel = () => {
    itemTag.current.list = [];
    setProductList(productList?.map((item) => ({ ...item, defaultValues: [] })));
    filterCardListRef.current?.forEach((item) => {
      item?.onReset();
    });
    // filterCardRef.current?.onReset();
  };

  const mergeArray = (arr: any[], key: string, mergeKey: string) => {
    // console.log('arr', arr);

    const obj: any = {};
    arr.forEach((item) => {
      if (!obj[item[key]]) {
        obj[item[key]] = item;
      } else {
        obj[item[key]][mergeKey] = obj[item[key]][mergeKey].concat(item[mergeKey]);
      }
    });
    return Object.values(obj).map((every: any) => ({
      useWay: every.useWay,
      name: every.name,
      values: every.values,
    }));
  };

  const onSave = () => {
    const newList = mergeArray(itemTag.current.list, 'standardId', 'values');
    onSelectTag(newList);
    itemTag.current.list = [];
  };

  useEffect(() => {
    if (isShow) {
      initialPageList(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isShow]);

  return (
    <div>
      <Drawer
        title={t('selectProductAttributeDrawer_title', { title })}
        placement="right"
        onClose={onClose}
        visible={isShow}
        // footer={<Drawer.Footer okText="确定" cancelText="重置" onOk={onSave} onCancel={onCancel} />}
        footer={
          <div className={styles.footerBox}>
            <Button
              className={styles.footerBoxBtn}
              style={{ color: '#040919', width: 160 }}
              onClick={onCancel}
            >
              {t('public_reset')}
            </Button>
            <Button
              type="primary"
              className={styles.footerBoxBtn}
              onClick={onSave}
              style={{ width: 160, marginLeft: 15 }}
            >
              {t('public_confirm')}
            </Button>
          </div>
        }
        extra={
          <div
            role="button"
            tabIndex={-1}
            className={`${styles.addGroup} ${i18n.language === 'en' ? styles.addGroupEn : ''}`}
            onClick={() => {
              if (type === 'name') {
                if (!testPerm('M_001_013_001_002')) {
                  return;
                }
              } else if (!testPerm('M_001_013_002_002')) {
                return;
              }

              addRef.current.openDrawer();
            }}
            title={t('selectProductAttributeDrawer_addGroup')}
          >
            {t('selectProductAttributeDrawer_addGroup')}
          </div>
        }
        style={{ transform: 'translateX(0) !important' }}
      >
        <div className={styles.header}>
          <Search
            className={styles.searchInput}
            placeholder={t('selectProductAttributeDrawer_searchPlaceholder', { title })}
            onSearch={(val) => {
              onSearchName(val);
            }}
          />
          <div
            tabIndex={-1}
            role="button"
            onClick={() => {
              if (type === 'name') {
                if (!testPerm('M_001_013_001_007')) {
                  return;
                }
              } else if (!testPerm('M_001_013_002_007')) {
                return;
              }
              sortDrawerRef.current.openDrawer();
            }}
          >
            <img className={styles.sortIcon} src={sortIcon} alt="" />
          </div>
        </div>
        {type === 'name' ? (
          <div className={styles.noteText}>
            <ExclamationCircleOutlined className={styles.noteIcon} />
            <span>{t('selectProductAttributeDrawer_note')}</span>
          </div>
        ) : (
          <div className={styles.noteText}>
            <ExclamationCircleOutlined className={styles.noteIcon} />
            <span style={{ fontSize: '12px' }}>{t('selectProductAttributeDrawer_maxGroups')}</span>
          </div>
        )}

        <div className={styles.collapseCard}>
          {productList.map((item: any, index) =>
            checkGroupShow(item) ? (
              <FilterCard
                list={[item]}
                key={item.id}
                deleteItem={(val) => {
                  if (type === 'name') {
                    if (!testPerm('M_001_013_001_006')) {
                      return;
                    }
                  } else if (!testPerm('M_001_013_002_006')) {
                    return;
                  }
                  deleteItem(val, item);
                }}
                isHover
                isMore
                disabled={type === 'name' ? false : !selectedGroupIds.includes(item.id)}
                disabledText={t('selectProductAttributeDrawer_disabledText')}
                onAddItem={onAddItem}
                ref={(el) => {
                  filterCardListRef.current[index] = el;
                }}
                onMoreItemClick={(val) => {
                  productList[index] = {
                    ...val,
                    moreItemText:
                      val.moreItemText === t('selectProductAttributeDrawer_expand')
                        ? t('selectProductAttributeDrawer_collapse')
                        : t('selectProductAttributeDrawer_expand'),
                    children: val.children?.map((child, childIndex) => ({
                      ...child,
                      isHidden:
                        val.moreItemText === t('selectProductAttributeDrawer_collapse') &&
                        childIndex > 7,
                    })),
                  };

                  // setProductList([...productList]);
                }}
              />
            ) : null
          )}
        </div>
        {checkAllGroupShow() && (
          <Empty
            style={{ position: 'relative', top: '26%' }}
            message={t('selectProductAttributeDrawer_noContent')}
            description={searchName ? t('selectProductAttributeDrawer_tryAnotherWord') : null}
          />
        )}
        <div>
          <AddGroupDrawer
            ref={addRef}
            onOK={onAddGroup}
            type="add"
            title={t('selectProductAttributeDrawer_addGroup')}
            height="80%"
          />
          <AddGroupDrawer
            ref={editRef}
            onOK={onEditGroup}
            type="editGgroup"
            title={t('common_edit')}
            height="50%"
          />
          <AddGroupDrawer
            ref={addTagRef}
            onOK={onAddGroupTag}
            type="addTag"
            title={t('selectProductAttributeDrawer_addValue')}
            height="50%"
          />
        </div>
      </Drawer>
      <SortDrawer ref={sortDrawerRef} categoryId={categoryId} type={type} okSort={okSort} />
    </div>
  );
}

SelectProductAttributeDrawer.defaultProps = {
  multiple: false,
};

export default SelectProductAttributeDrawer;
