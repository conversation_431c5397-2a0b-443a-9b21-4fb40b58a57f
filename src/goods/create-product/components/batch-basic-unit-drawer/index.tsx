import { Dispatch, SetStateAction, useState } from 'react';
import { Select, Divider } from 'antd';
import { Drawer } from '@/components';
import { ResultType as UnitOptionType } from '@/apis/base/get-unit-options';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface UnitDrawerType {
  isShow: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  unitList: UnitOptionType[];
}

function BatchBasicUnitDrawer(props: UnitDrawerType) {
  const { t } = useTranslation();
  const { isShow, closeDrawer, unitList } = props;

  const [list, setList] = useState([
    {
      id: '1',
      name: '300ML/淡黄色/HUMJH-7658300ML/淡黄色/HUMJH-7658/400ML/红色/淡黄色/HUMJH-7658/400ML',
      unit: '',
    },
    { id: '2', name: '400ML/红色/HUMJH-765811300ML/淡', unit: '' },
    { id: '3', name: '500ML/蓝色/HUMJH-765831110ML/淡', unit: '' },
    { id: '4', name: '600ML/白色/HUMJH-76582430ML/淡', unit: '' },
  ]);

  // 批量设置基本单位
  const onBatchSetUnify = (unitName: string) => {
    const batchList = list.map((item) => ({
      ...item,
      unit: unitName,
    }));
    window.console.log(batchList, 'batchList');
    setList(batchList);
  };

  // 单个设置基本单位
  const onSingleSetUnify = (unitName: string, subscript: number) => {
    const singleList = list.map((item, index) => ({
      ...item,
      unit: index === subscript ? unitName : item.unit,
    }));
    setList(singleList);
  };

  const onOk = () => {};

  const onclose = () => {
    closeDrawer(false);
  };

  return (
    <Drawer
      title={t('batchBasicUnitDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchBasicUnitDrawer_batchSet')}</div>
        <div className={styles.batchBox}>
          <div>基本单位</div>
          <Select
            className={styles.selectInput}
            bordered={false}
            placeholder={t('batchBasicUnitDrawer_selectBaseUnit')}
            options={unitList}
            filterOption={(input, option) =>
              (option?.unitName ?? '').toLowerCase().includes(input.toLowerCase())
            }
            fieldNames={{
              label: 'unitName',
              value: 'unitName',
            }}
            onChange={onBatchSetUnify}
          />
        </div>
      </div>
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchBasicUnitDrawer_singleSet')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>{item.name}</div>
            <div className={styles.batchBox}>
              <div>{t('batchBasicUnitDrawer_baseUnit')}</div>
              <Select
                className={styles.selectInput}
                bordered={false}
                value={item.unit || null}
                placeholder={t('batchBasicUnitDrawer_selectBaseUnit')}
                options={unitList}
                fieldNames={{
                  label: 'unitName',
                  value: 'unitName',
                }}
                onChange={(e) => onSingleSetUnify(e, index)}
              />
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchBasicUnitDrawer;
