/* eslint-disable jsx-a11y/media-has-caption */
/* eslint-disable react/jsx-no-comment-textnodes */
import { Dispatch, SetStateAction, useState, useRef, ReactNode, useEffect } from 'react';
import { InputNumber, Image } from 'antd';
import { Drawer, GraphicDescDrawer, Icon } from '@/components';
import type { GraphicDescDrawerPropsDataItem } from '@/components';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface ManagerDrawerType {
  isShow: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<
    [
      expirationDate: number | null,
      graphicDescData: GraphicDescDrawerPropsDataItem[],
      updateKeys: string[]
    ]
  >;
}

function BatchExtendManagerDrawer({ isShow, closeDrawer, onConfirm }: ManagerDrawerType) {
  const { t } = useTranslation();
  const [expirationDate, setExpirationDate] = useState<number | null>(null);
  const [visible, setVisible] = useState(false);
  const [preview, setPreview] = useState(false);
  const [graphicDescData, setGraphicDescData] = useState<GraphicDescDrawerPropsDataItem[]>([]);
  const updateKeys = useRef<string[]>([]);

  // 设置保质期
  const onValidTime = (val: number | null) => {
    if (!updateKeys.current.includes('expirationDate')) {
      updateKeys.current.push('expirationDate');
    }
    setExpirationDate(val);
  };

  const onclose = () => {
    closeDrawer(false);
  };

  const onOk = () => {
    // if (expirationDate.current !== null) {
    onConfirm(expirationDate, graphicDescData, updateKeys.current);
    // }
    closeDrawer(false);
  };

  useEffect(() => {
    if (!isShow) {
      setGraphicDescData([]);
      updateKeys.current = [];
      setExpirationDate(null);
    }
  }, [isShow]);

  return (
    <div>
      <Drawer
        title={t('batchExtendManagerDrawer_title')}
        visible={isShow}
        placement="right"
        onClose={onclose}
        footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
      >
        <div className={styles.noticeText}>{t('batchExtendManagerDrawer_notice')}</div>
        <div className={styles.cardBox}>
          <div className={styles.flexBox}>
            <div>{t('base_shelfLife2')}</div>
            <InputNumber
              className={styles.input}
              placeholder={t('enter_shelf_life')}
              addonAfter={t('specification_shelf_shuru2')}
              bordered={false}
              min={1}
              max={99999999}
              precision={0}
              value={expirationDate}
              onChange={(val) => onValidTime(val as number | null)}
            />
          </div>
        </div>
        <div className={styles.cardBox}>
          <div className={styles.cardTitle}>
            <div className={styles.cardTitleText}>{t('desc_graphicDescText')}</div>
            {graphicDescData.length > 0 && (
              <div
                className={styles.cardTitleOpt}
                role="presentation"
                onClick={() => {
                  setVisible(true);
                }}
              >
                {t('common_edit')}
              </div>
            )}
          </div>
          {graphicDescData.length ? (
            <div className={styles.graphicDesc}>
              {graphicDescData.map((item) => {
                let element: ReactNode | string = '';
                switch (item.type) {
                  case 'image':
                    element = (
                      <div className={styles.graphicDescImage} key={item.uuid}>
                        <Image src={item.value} className={styles.graphicDescImageItem} />
                      </div>
                    );
                    break;
                  case 'text':
                    element = (
                      <div className={styles.graphicDescText} key={item.uuid}>
                        {item.value}
                      </div>
                    );
                    break;
                  case 'video':
                    element = (
                      <div className={styles.graphicDescVideo} key={item.uuid}>
                        // eslint-disable-next-line jsx-a11y/media-has-caption
                        <video src={item.value} controls className={styles.graphicDescVideoItem} />
                      </div>
                    );
                    break;

                  default:
                    break;
                }
                return element;
              })}
            </div>
          ) : (
            <div
              className={styles.addBlockBtn}
              role="button"
              tabIndex={0}
              onClick={() => {
                setVisible(true);
              }}
            >
              <Icon name="plus" size={16} />
              <span className="ml-1">{t('add_graphic2')}</span>
            </div>
          )}
        </div>
      </Drawer>

      <GraphicDescDrawer
        visible={visible}
        preview={preview}
        onClose={() => {
          setVisible(false);
        }}
        onPreviewChange={(val) => {
          setPreview(val);
        }}
        onConfirm={(values) => {
          setGraphicDescData(values);
        }}
        data={graphicDescData}
      />
    </div>
  );
}

export default BatchExtendManagerDrawer;
