.noticeText {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 12px;
}

.cardBox {
  margin-bottom: 24px;
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-input-number-handler-wrap {
      display: none !important;
    }

    .ant-input-number-input {
      text-align: right;
      padding-right: 0;
      background-color: #fff;
    }

    .ant-input-number-group-addon {
      border: 0;
      background: white;
      padding: 0 8px;
    }
  }
}

.flexBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cardTitle {
  display: flex;
  margin-bottom: 12px;
  justify-content: space-between;

  &Text {
    font-weight: bolder;
  }

  &Opt {
    color: #888b98;
    cursor: pointer;
  }
}

.input {
  width: 200px;

  :global {
    .ant-input {
      text-align: right;
    }
  }
}

.addBlockBtn {
  color: #008cff;
  font-weight: 500;
  font-weight: bolder;
  display: flex;
  height: 80px;
  justify-content: center;
  border: 1px dashed #008cff;
  border-radius: 10px;
  background-color: rgb(217 238 255 / 30%);
  align-items: center;
  cursor: pointer;
}

.graphicDescText {
  color: #040919;
  margin-bottom: 12px;
}

.graphicDescImage {
  margin-bottom: 12px;

  &Item {
    // width: 300px;
    // height: 180px;
    border-radius: 18px;
  }
}

.graphicDescVideo {
  width: 300px;
  height: 180px;
  margin-bottom: 12px;

  &Item {
    width: 300px;
    height: 180px;
  }
}
