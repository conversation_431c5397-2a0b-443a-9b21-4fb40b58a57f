import { Dispatch, SetStateAction, useState, useEffect, ReactNode, useRef } from 'react';
import { Switch, Divider, Select, message, Tooltip } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, Icon, NumericInput } from '@/components';
import { getUnitFormulaOptionsByUnit } from '@/apis';
import { ResultType as UnitFormulaOptionType } from '@/apis/base/get-unit-formula-options';
import { VarListType } from '@/apis/pms/type';
import { testPerm } from '@/utils/permission';
// import { limitDecimalsF, limitDecimalsP } from '../../utils/input';
import SetVariableDrawer from '../set-variable-drawer';

import styles from './index.module.less';

interface ManagerDrawerType {
  isShow: boolean;
  baseUnit: string;
  unitNum: number;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<
    [
      saleUnit: string | null,
      multiUnit: boolean,
      unitConversion: number | null,
      varList: VarListType[],
      updateKeys: string[]
    ]
  >;
}

function BatchUnitManagerDrawer({
  isShow,
  baseUnit,
  unitNum,
  closeDrawer,
  onConfirm,
}: ManagerDrawerType) {
  const { t } = useTranslation();

  const [saleUnit, setSaleUnit] = useState<string | null>(null);
  const [unitConversion, setUnitConversion] = useState<number | null>(null);
  const [isUnitState, setIsUnitState] = useState<boolean>(false);
  const [showSetVar, setShowSetVar] = useState<boolean>(false);
  // const [isClickConfirm, setIsClickConfirm] = useState<boolean>(false);
  const [unitFormulaSelect, setUnitFormulaSelect] = useState<UnitFormulaOptionType[]>([]);
  const [currUnitInfo, setCurrUnitInfo] = useState({} as UnitFormulaOptionType);
  const [templateFormulaNameList, setTemplateFormulaNameList] = useState<VarListType[]>([]);
  const updateKeys = useRef<string[]>([]);

  // 是否打开多单位状态
  const onIsOpen = (val: boolean) => {
    if (!testPerm('M_001_002_001_003')) {
      return;
    }
    if (!updateKeys.current.includes('multiUnit')) {
      updateKeys.current.push('multiUnit');
    }
    if (!updateKeys.current.includes('unitConversion')) {
      updateKeys.current.push('unitConversion');
    }
    if (!updateKeys.current.includes('var')) {
      updateKeys.current.push('var');
    }
    setIsUnitState(val);
    if (!val) {
      setUnitConversion(null);
      setTemplateFormulaNameList([]);
    }
  };

  // 设置单位换算
  const onUnitMatrixing = (val: number | null, option: any) => {
    setUnitConversion(val);
    if (!updateKeys.current.includes('unitConversion')) {
      updateKeys.current.push('unitConversion');
    }
    if (!updateKeys.current.includes('var')) {
      updateKeys.current.push('var');
    }
    if (option?.varList?.length) {
      setCurrUnitInfo(option);
      setShowSetVar(true);
      setTemplateFormulaNameList(
        option?.varList?.map((item: any) => ({
          id: item.id,
          name: item.name,
          value: '',
        }))
      );
    } else {
      setTemplateFormulaNameList([]);
    }
  };

  // 设置最小销售单元
  const onSaleUnit = (val: string) => {
    if (!updateKeys.current.includes('saleUnit')) {
      updateKeys.current.push('saleUnit');
    }
    setSaleUnit(val || null);
  };

  const onOk = () => {
    // if (saleUnit === null) {
    //   message.error('最小销售单元不能设置为空');
    //   return;
    // }
    if (isUnitState && !unitConversion) {
      message.error(t('error_noUnitConversion'));
      return;
    }
    if (
      templateFormulaNameList.length &&
      templateFormulaNameList.filter((item) => item.value === null || item.value === '').length
    ) {
      message.error(t('error_unitConversionVariableEmpty'));
      return;
    }
    onConfirm(saleUnit, isUnitState, unitConversion, templateFormulaNameList, updateKeys.current);
    closeDrawer(false);
  };

  const onclose = () => {
    closeDrawer(false);
    setSaleUnit(null);
    setUnitConversion(null);
    setIsUnitState(false);
    setUnitFormulaSelect([]);
  };

  useEffect(() => {
    if (!isShow) {
      setSaleUnit(null);
      setUnitConversion(null);
      setIsUnitState(false);
      setUnitFormulaSelect([]);
      setTemplateFormulaNameList([]);
      updateKeys.current = [];
    }
    if (isShow && baseUnit) {
      getUnitFormulaOptionsByUnit({ unitName: baseUnit }).then((res) => {
        setUnitFormulaSelect(res.list);
      });
    }
  }, [isShow, baseUnit]);

  const createVarBox = () => {
    const isHasVar = templateFormulaNameList?.length || false;
    const isSetVar =
      templateFormulaNameList?.filter((item) => item.value === null || item.value === '').length ===
      0;
    const varText = isSetVar
      ? templateFormulaNameList
          ?.map((formItem) => `${formItem.name || ''}:${formItem.value}`)
          .join(',')
      : '';
    let varElement: ReactNode | null = null;
    const createArrow = (arr: any, index: number) => {
      let arrowElement: ReactNode | null = null;
      if (arr.length > 3 && index === 2) {
        arrowElement = (
          <span>
            ... <Icon name="right" size={12} />
          </span>
        );
      }

      if (arr.length < 3 && index + 1 === arr.length) {
        arrowElement = (
          <span>
            <Icon name="right" size={12} />
          </span>
        );
      }

      return arrowElement;
    };
    if (isHasVar) {
      if (varText) {
        varElement = (
          <div className={styles.setMutUnitVar}>
            <Tooltip placement="top" title={varText}>
              <div>
                {templateFormulaNameList?.map((formItem, formIndex) =>
                  formIndex < 3 ? (
                    <div>
                      {formItem.name}:{formItem.value}
                      {createArrow(templateFormulaNameList, formIndex)}
                    </div>
                  ) : null
                )}
              </div>
            </Tooltip>
          </div>
        );
      } else {
        varElement = (
          <div className={styles.setMutUnitVarErr}>
            请输入变量值
            <Icon name="right" size={12} />
          </div>
        );
      }
    }
    return varElement;
  };

  return (
    <div>
      <Drawer
        title={t('batchUnitManagerDrawer_title')}
        visible={isShow}
        placement="right"
        onClose={onclose}
        footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
      >
        <div className={styles.noticeText}>{t('batchUnitManagerDrawer_notice')}</div>
        <div className={styles.cardBox}>
          <div className={styles.flexBox}>
            <div>{t('base_multiUnitStatus2')}</div>
            <Switch checked={isUnitState} onChange={onIsOpen} />
          </div>
          <div className={styles.switchDesc}>{t('batchUnitManagerDrawer_switchDesc')}</div>
          <Divider className={styles.line} />
          {isUnitState ? (
            <div>
              <div className={styles.basicUnit}>
                {t('base_basicUnit')}: {baseUnit}
              </div>
              <div className={styles.flexColumn}>
                <div className={styles.batchLabelColumn}>{t('base_unitConversion')}</div>
                <div className={styles.batchValueColumn}>
                  <Select
                    className={styles.selectInput}
                    bordered={false}
                    placeholder={t('select_conversion_relation')}
                    options={unitFormulaSelect}
                    fieldNames={{
                      label: 'templateName',
                      value: 'id',
                    }}
                    value={unitConversion}
                    onChange={onUnitMatrixing}
                  />
                  <div
                    role="presentation"
                    onClick={() => {
                      setShowSetVar(true);
                    }}
                  >
                    {createVarBox()}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className={styles.flexBox}>
              <div className={styles.batchLabel}>{t('base_basicUnit')}</div>
              <div className={styles.batchValue}>{baseUnit}</div>
            </div>
          )}
          <Divider className={styles.line} />
          <div className={styles.flexColumn}>
            <div className={styles.batchLabelColumn}>{t('base_minSaleUnit')}</div>
            <div className={styles.batchValueColumn}>
              <NumericInput
                className={styles.input}
                bordered={false}
                placeholder={t('enter_min_sale_unit')}
                max={99999999}
                maxLength={(unitNum ? unitNum + 1 : 0) + 8}
                precision={unitNum || 0}
                onChange={(val) => onSaleUnit(val)}
                value={saleUnit ? `${saleUnit}` : ''}
              />
            </div>
          </div>
        </div>
      </Drawer>
      <SetVariableDrawer
        visible={showSetVar}
        drawerType="default"
        defaultValues={templateFormulaNameList || []}
        closeDrawer={setShowSetVar}
        dataInfo={{ ...currUnitInfo }}
        onConfirm={(_val, values) => {
          setTemplateFormulaNameList(values);
          if (!updateKeys.current.includes('var')) {
            updateKeys.current.push('var');
          }
        }}
      />
    </div>
  );
}

export default BatchUnitManagerDrawer;
