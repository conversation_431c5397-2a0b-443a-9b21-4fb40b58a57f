.noticeText {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 12px;
}

.cardBox {
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-input-number-handler-wrap {
      display: none !important;
    }
  }
}

.flexBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flexColumn {
  display: flex;
  flex-direction: column;
}

.input {
  font-size: 16px;
  width: 100% !important;
  // text-align: right;
  padding: 0;

  :global {
    input {
      height: 30px;
      text-align: left;
      padding: 0;
    }
  }
}

.basicUnit {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 17px;
}

.line {
  margin: 16px 0;
}

.errorMsg {
  color: #ea1c26;
  line-height: 24px;
  margin-left: 15px;
}

.batchLabel {
  width: 90px;
}

.batchValue {
  flex: 1;
  text-align: right;
}

.batchLabelColumn {
  flex: 1;
  margin-bottom: 10px;
}

.batchValueColumn {
  flex: 1;
  text-align: left;
}

.selectInput {
  width: 100%;

  :global {
    .ant-select-selector {
      padding: 0 !important;
    }
  }
}

.setMutUnitVar {
  color: #008cff;
  margin-top: 8px;
  cursor: pointer;
}

.setMutUnitVarErr {
  color: #ea1c26;
  margin-top: 8px;
  cursor: pointer;
}

.switchDesc {
  color: #888b98;
  font-size: 12px;
  margin-top: 10px;
}
