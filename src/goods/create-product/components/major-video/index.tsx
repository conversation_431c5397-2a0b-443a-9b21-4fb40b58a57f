import { useState } from 'react';
import { Spin, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { Icon } from '@/components';
import { UploadVideo } from '@/components/upload/index';
import { StateType, VideoItemType } from '@/apis/pms/type';
import { checkPerm, testPerm } from '@/utils/permission';
import styles from './index.module.less';

interface PropsType {
  videoList: VideoItemType[];
  onSetState: SimpleFn<Partial<StateType>>;
  permCode: string;
}

function MajorVideoComponent({ videoList, permCode, onSetState }: PropsType) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  return (
    <div className="mt-4">
      {videoList.length ? (
        <div className={styles.videoWrap}>
          <video width={195} height={195} src={videoList[0].url} muted controls />
          <img
            src="https://img.huahuabiz.com/user_files/2023323/1679551070564444.png"
            alt=""
            className={styles.closeImage}
            role="presentation"
            onClick={(e) => {
              e.stopPropagation();
              onSetState({ videoList: [] });
            }}
          />
        </div>
      ) : (
        <Spin spinning={loading}>
          {!checkPerm(permCode) ? (
            <div
              className={styles.uploadBtn}
              role="presentation"
              onClick={() => {
                testPerm(permCode);
              }}
            >
              <Icon name="plus" size={24} />
              <span>{t('majorVideo_addVideo')}</span>
            </div>
          ) : (
            <UploadVideo
              videoMaxSize={1024 * 1024 * 500}
              duration={60}
              beforeUpload={() => {
                setLoading(true);
                return true;
              }}
              onSuccess={(val) => {
                onSetState({ videoList: [val] });
                message.success(t('majorVideo_uploadSuccess'));
              }}
              onComplete={() => {
                setLoading(false);
              }}
            >
              <div className={styles.uploadBtn}>
                <Icon name="plus" size={24} />
                <span>{t('majorVideo_addVideo')}</span>
              </div>
            </UploadVideo>
          )}
        </Spin>
      )}
      <div className={styles.message}>{t('majorVideo_suggestion')}</div>
    </div>
  );
}

MajorVideoComponent.displayName = 'MajorVideoComponent';

MajorVideoComponent.defaultProps = {};

export default MajorVideoComponent;
