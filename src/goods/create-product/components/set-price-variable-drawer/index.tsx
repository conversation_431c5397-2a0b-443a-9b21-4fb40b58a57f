/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dispatch, SetStateAction, useState, useEffect, useRef } from 'react';
import { Divider, Form, message } from 'antd';
import { useRequest } from 'ahooks';
import { useTranslation } from 'react-i18next';
import { Drawer, NumericInput } from '@/components';
import { getGoodsPriceSimpleShelf } from '@/apis';
import { VarListType } from '@/apis/pms/type';
import styles from './index.module.less';

interface VariableDrawerType {
  type: 'single' | 'multiple';
  visible: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  categoryId: number;
  unit: string;
  formulaId: number;
  formatFormula: string;
  formulaName: string;
  formulaList: VarListType[];
  standardList: { name: string; value: string }[][];
  // isCheck?: boolean;
  // onSetState: SimpleFn<Partial<StateType>>;
  updateState: MultipleParamsFn<
    [value: { formulaList: VarListType[]; marketPrice: number | null }]
  >;
  onDefine: SimpleFn<number | null>;
}

function SetPriceVariableDrawer(props: VariableDrawerType) {
  const {
    type = 'single',
    visible,
    closeDrawer,
    categoryId,
    unit,
    formulaId,
    formatFormula,
    formulaName,
    formulaList,
    standardList,
    // isCheck,
    // onSetState,
    updateState,
    onDefine,
  } = props;
  const { t } = useTranslation();

  const formulaListRef = useRef<VarListType[]>([]);
  const [formulaData, setFormulaData] = useState<VarListType[]>([]);
  const [varLists, setVarLists] = useState<VarListType[]>([]);
  const [retailPrice, setRetailPrice] = useState<null | number>(null);
  const priceLoading = useRef(false);
  const [btnLoading, setBtnLoading] = useState(false);

  const { run } = useRequest(getGoodsPriceSimpleShelf, {
    debounceWait: 300,
    manual: true,
    onSuccess: (res) => {
      const isSetVar =
        formulaData.filter((item) => item.value === null || item.value === '').length === 0;
      if (isSetVar) {
        setRetailPrice(res.priceList[0]);
      } else {
        setRetailPrice(null);
      }
    },
    onFinally: () => {
      priceLoading.current = false;
      setBtnLoading(false);
    },
  });

  // const checkBtnDisableStatus = () => {
  //   let status = false;
  //   if (isCheck) {
  //     if (
  //       (varLists.length &&
  //         varLists?.filter((item) => item.value === null || item.value === '').length) ||
  //       !varLists.length
  //     ) {
  //       status = true;
  //     }
  //   }
  //   return status;
  // };

  // const checkFormulaList = (arr: VarListType[]) =>
  //   arr.filter((item) => item.value === null || item.value === '').length === 0;

  // 输入变量计算零售价
  const onSetVariables = (val: string | null, index: number) => {
    const oldVal = formulaData[index].value;
    formulaData[index].value = val;
    const formulaListArr = [...formulaData];
    const isSetVar =
      formulaListArr.filter((item) => item.value === null || item.value === '').length === 0;
    setVarLists([...formulaData]);
    if (isSetVar && val !== '' && val !== null) {
      const formulaArr = formulaData?.map((item) => ({ ...item }));
      if (oldVal !== val) {
        priceLoading.current = true;
        setBtnLoading(true);
        run({
          formulaId,
          formulaList: formulaArr,
          standardList:
            type === 'single'
              ? [
                  [
                    {
                      name: t('setPriceVariableDrawer_spec'),
                      value: t('setPriceVariableDrawer_generalSpec'),
                    },
                  ],
                ]
              : standardList,
          catId: categoryId,
          unit,
        });
      }
    } else {
      setRetailPrice(null);
    }
  };

  const onSave = () => {
    if (priceLoading.current) {
      message.error(t('setPriceVariableDrawer_priceCalculationNotComplete'));
      return;
    }

    if (varLists.length) {
      updateState({ formulaList: [...varLists], marketPrice: retailPrice });
      // onSetState({ formulaList: [...varLists], marketPrice: retailPrice });
    }
    onDefine(retailPrice);
    setRetailPrice(0);
    closeDrawer(false);
  };

  const onclose = () => {
    closeDrawer(false);
    setRetailPrice(null);
    setFormulaData([]);
  };

  useEffect(() => {
    if (formulaId && formulaList && visible) {
      const list = formulaList?.filter((item) => item.value !== null && item.value !== '');
      if (list.length === formulaList.length) {
        run({
          formulaId,
          formulaList,
          standardList:
            type === 'single'
              ? [
                  [
                    {
                      name: t('setPriceVariableDrawer_spec'),
                      value: t('setPriceVariableDrawer_generalSpec'),
                    },
                  ],
                ]
              : standardList,
          catId: categoryId,
          unit,
        });
      }
      setFormulaData([...formulaList]);
      formulaListRef.current = formulaList;
    }
    if (!visible) {
      setFormulaData([]);
      setVarLists([]);
    }
  }, [formulaId, formulaList, type, visible, standardList, categoryId, unit, run, t]);

  return (
    <Drawer
      title={t('setPriceVariableDrawer_setVariableValue')}
      visible={visible}
      placement="right"
      onClose={onclose}
      footer={
        <Drawer.Footer
          okText={t('public_confim')}
          showCancel={false}
          onOk={onSave}
          disabled={btnLoading}
        />
      }
    >
      <div className={styles.cardBox}>
        <div className={styles.formulaName}>{formulaName}</div>
        <div className={styles.formatFormula}>
          {t('setPriceVariableDrawer_formula')}: {formatFormula}
        </div>
      </div>

      <div className={styles.cardBox}>
        <Form layout="vertical">
          {formulaData.map((item, index) => (
            <div key={item.id}>
              <Form.Item label={item.name}>
                <NumericInput
                  // className={styles.input}
                  className={styles.variableValue}
                  bordered={false}
                  placeholder={t('setPriceVariableDrawer_enterVariableValue')}
                  min={0}
                  maxLength={20}
                  precision={15}
                  onChange={(val) => onSetVariables(val, index)}
                  value={item.value ? `${item.value}` : ''}
                />
                {/* <InputNumber
                className={styles.variableValue}
                bordered={false}
                placeholder="请输入变量值"
                max="99999999"
                maxLength={15}
                value={item.value}
                onChange={(val) => onSetVariables(val, index)}
              /> */}
              </Form.Item>
              <Divider style={{ marginTop: '0px' }} />
            </div>
          ))}
          {/* <Form.Item label="零售价">
            <div className={styles.priceValue}>¥ {retailPrice}</div>
          </Form.Item> */}
        </Form>

        <div className={styles.priceBox}>
          <div>{t('setPriceVariableDrawer_retailPrice')}</div>
          <div className={styles.priceValue}>¥ {retailPrice || 0}</div>
        </div>
      </div>
    </Drawer>
  );
}

SetPriceVariableDrawer.defaultProps = {
  // isCheck: false,
};

export default SetPriceVariableDrawer;
