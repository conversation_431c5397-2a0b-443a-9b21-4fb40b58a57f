.cardBox {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-input-number-handler-wrap {
      display: none !important;
    }

    .ant-input-number-group-addon {
      border: 0;
      background: white;
    }

    // .ant-input-number-input {
    //   // text-align: right;
    //   // padding-right: 0;
    // }

    .ant-form-item {
      margin-bottom: 10px;
    }
  }
}

.formulaName {
  font-size: 16px;
  font-weight: bolder;
  // height: 24px;
  line-height: 24px;
  // margin-bottom: 8px;
}

.formula {
  color: #888b98;
  height: 22px;
  line-height: 22px;
}

.variableBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.variableValue {
  font-size: 16px;
  width: 100%;
  // text-align: left;
  padding: 0;

  // :global {
  //   .ant-input-number-input {
  //     padding: 0 !important;
  //   }
  // }
}

.priceBox {
  display: flex;
  flex-direction: column;
  justify-content: center;
  // margin-top: 10px;
  // justify-content: space-between;
  // align-items: center;
}

.priceValue {
  color: #ea1c26;
  margin-top: 16px;
}

.formulaBox {
  border-radius: 18px;
  background: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 8%);
}

.formatFormula {
  color: #888b98;
  margin-top: 8px;
}
