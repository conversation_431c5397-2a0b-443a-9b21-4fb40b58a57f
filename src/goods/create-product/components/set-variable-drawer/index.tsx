import { Form } from 'antd';
import { useRef, useEffect, Dispatch, SetStateAction, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Drawer, Icon, NumericInput } from '@/components';
import { ResultType as UnitFormulaOptionType } from '@/apis/base/get-unit-formula-options';
import { VarListType } from '@/apis/pms/type';
import styles from './index.module.less';

type SetVariableDrawerProps = {
  visible: boolean;
  drawerType: string;
  dataInfo: UnitFormulaOptionType;
  defaultValues?: VarListType[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<
    [value: UnitFormulaOptionType, values: { id: number; name: string; value: string }[]]
  >;
};

function SetVariableDrawer({
  visible,
  drawerType,
  closeDrawer,
  dataInfo,
  defaultValues,
  onConfirm,
}: SetVariableDrawerProps) {
  const { t } = useTranslation();
  const [form] = Form.useForm();
  const [varVal, setVarVal] = useState('');
  const variableList = useRef<{ id: number; name: string; value: string }[]>([]);
  const onValuesChange = () => {
    const formInfo = form.getFieldsValue();
    Object.keys(formInfo).forEach((key) => {
      const varKeys = variableList.current?.map((item) => item.id);
      const index = varKeys.indexOf(Number(key));

      if (index === -1) {
        const name = dataInfo?.varList?.filter((item: VarListType) => item.id === Number(key))?.[0]
          ?.name;
        variableList.current.push({ id: Number(key), name, value: formInfo[key] || '' });
      } else {
        variableList.current[index].value = formInfo[key] || '';
      }
    });
  };

  const onSave = () => {
    form.validateFields().then(() => {
      onConfirm(
        dataInfo,
        variableList.current.length
          ? variableList.current
          : dataInfo?.varList?.map((item: VarListType) => ({
              id: item.id,
              name: item.name,
              value: '',
            }))
      );
      closeDrawer(false);
    });
  };

  const onClose = () => {
    closeDrawer(false);
    variableList.current = [];
    form.resetFields();
  };

  useEffect(() => {
    if (visible) {
      const formData: Record<string, string | null> = {};
      defaultValues?.forEach((item) => {
        formData[item.id] = item.value;
      });

      form.setFieldsValue({ ...formData });
      variableList.current =
        defaultValues?.map((item) => ({
          ...item,
          name: item.name || '',
          value: item.value || '',
        })) || [];
    } else {
      form.resetFields();
    }
  }, [defaultValues, visible, form]);

  const formulaBox = (
    <div className={styles.cardBox}>
      <div className={styles.formulaName}>{dataInfo.templateName}</div>
      {/* <div className={styles.formulaTemplate}>{dataInfo.templateName}</div> */}
    </div>
  );

  const VarBox = (
    <div className={styles.cardBox}>
      <Form form={form} onValuesChange={onValuesChange} labelAlign="left">
        {dataInfo?.varList?.map((item: VarListType) => (
          <div className={styles.variableBox} key={item.id}>
            {/* <div className={styles.variableLabel}>{item.name}</div> */}
            <Form.Item
              // name={item.id}
              // required
              // label={item.name}
              // rules={[{ required: true, message: '请输入变量值' }]}
              style={{ width: '100%' }}
              noStyle
            >
              <div className={styles.variableItem}>
                <div className={styles.variableLabel}>{item.name}</div>
                <Form.Item name={item.id} style={{ width: '100%', minHeight: 22 }}>
                  <NumericInput
                    // className={styles.input}
                    className={styles.variableValue}
                    bordered={false}
                    placeholder={t('setVariableDrawer_enterVariableValue')}
                    min={0}
                    maxLength={20}
                    precision={15}
                    onChange={(val) => setVarVal(val)}
                    value={varVal ? `${varVal}` : ''}
                  />
                  {/* <InputNumber
                    className={styles.variableValue}
                    bordered={false}
                    placeholder="请输入变量值"
                    max="99999999"
                    maxLength={15}
                  /> */}
                  {/* <Input
                    className={styles.variableValue}
                    placeholder="请输入变量值"
                    maxLength={9}
                    bordered={false}
                  /> */}
                </Form.Item>
              </div>
            </Form.Item>
          </div>
        ))}
      </Form>
    </div>
  );

  return drawerType === 'default' ? (
    <Drawer
      title={t('setVariableDrawer_setVariableValue')}
      visible={visible}
      placement="right"
      onClose={onClose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onSave} />}
    >
      {formulaBox}
      {VarBox}
    </Drawer>
  ) : (
    <Drawer
      title={t('setVariableDrawer_setVariableValue')}
      visible={visible}
      placement="bottom"
      height="70%"
      closable={false}
      getContainer={false}
      maskStyle={{ background: 'rgba(0, 0, 0, .45)' }}
      footer={<Drawer.Footer showCancel={false} onOk={onSave} />}
      extra={
        <div>
          <Icon name="close" size={20} role="button" tabIndex={-1} onClick={onClose} />
        </div>
      }
    >
      {formulaBox}
      {VarBox}
    </Drawer>
  );
}

SetVariableDrawer.defaultProps = {
  defaultValues: [],
};

export default SetVariableDrawer;
