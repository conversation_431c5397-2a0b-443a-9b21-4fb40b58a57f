.cardBox {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.formulaName {
  font-size: 16px;
  font-weight: bolder;
  // height: 24px;
  // line-height: 24px;
  // margin-bottom: 8px;
}

.formula {
  color: #888b98;
  height: 22px;
  line-height: 22px;
}

.variableBox {
  display: flex;
  width: 100%;
  margin-bottom: 10px;
  justify-content: space-between;

  .variableItem {
    display: flex;
    width: 100%;
    flex-direction: column;
    align-items: center;
  }
  // align-items: center;
  .variableLabel {
    width: 100%;
    // padding-top: 4px;
  }

  .variableValue {
    font-size: 16px;
    width: 100%;
    // width: 220px;
    margin-top: 8px;
    flex: 1;
    text-align: left;
    // padding-right: 0;
    padding: 0;

    // :global {
    //   .ant-input-number-input {
    //     padding: 0;
    //   }

    //   .ant-input-number-handler-wrap {
    //     display: none !important;
    //   }
    // }
  }

  &:last-child {
    margin-bottom: 0;
  }

  :global {
    .ant-form-item:last-child {
      margin-bottom: 0 !important;
    }
  }
}

.priceBox {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .priceValue {
    color: #ea1c26;
    margin-right: 12px;
  }
}

.formulaTemplate {
  color: #888b98;
  margin-top: 8px;
}
