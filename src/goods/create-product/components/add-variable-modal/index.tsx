import React, { Dispatch, SetStateAction, useState } from 'react';
import { Input, Modal, message } from 'antd';
import { useTranslation } from 'react-i18next';
import './index.module.less';

interface AddVariableType {
  isShow: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onAddVariable: SimpleFn<string>;
}

function AddVariableModal(props: AddVariableType) {
  const { t } = useTranslation();
  const { isShow, closeDrawer, onAddVariable } = props;

  const [inputValue, setInputValue] = useState('');

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const onCancel = () => {
    closeDrawer(false);
  };

  const okDefine = () => {
    if (inputValue.length) {
      onAddVariable(inputValue);
    } else {
      message.warning(t('addVariableModal_enterVariableName'));
    }
  };

  return (
    <Modal
      title={t('addVariableModal_addVariable')}
      getContainer={false}
      centered
      visible={isShow}
      maskClosable={false}
      width={312}
      onOk={() => okDefine()}
      onCancel={onCancel}
    >
      <Input placeholder={t('addVariableModal_inputVariableName')} onChange={onChange} />
    </Modal>
  );
}

export default AddVariableModal;
