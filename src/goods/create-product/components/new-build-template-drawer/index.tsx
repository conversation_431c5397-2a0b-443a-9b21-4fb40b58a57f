import { useState, Dispatch, SetStateAction, ReactNode, useEffect, useRef } from 'react';
import { Input, InputNumber, Radio, Switch, Popconfirm, message } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, Icon } from '@/components';
import debounce from 'lodash/debounce';
import { createUuid } from '@/utils/utils';
import { postSaveFreightTemplate } from '@/apis';
import type { FreightTemplateParams } from '@/apis/base/post-save-freight-template';
import SelectCity from '../select-city';
import styles from './index.module.less';

interface NewBuildTemplateType {
  isVisible: boolean;
  onSuccess: MultipleParamsFn<[]>;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
}

function NewBuildTemplateDrawer({ isVisible, onSuccess, closeDrawer }: NewBuildTemplateType) {
  const { t } = useTranslation();
  const [name, setName] = useState('');
  const [isDefault, setIsDefault] = useState(false);
  const [showSelectArea, setShowSelectArea] = useState(false);
  const [markupType, setMarkupType] = useState(0);
  const [freightRuleList, setFreightRuleList] = useState<
    (FreightTemplateParams['freightRuleList'] & { id: string })[]
  >([
    {
      id: createUuid(),
      isDelivered: 1,
      deliveredAreaType: 1,
      markupPrice: null,
      markupRatio: null,
      provinceList: [],
    },
  ]);

  const [currProvinceList, setCurrProvinceList] = useState<
    FreightTemplateParams['freightRuleList']['provinceList']
  >([]);

  const [disabledIds, setDisabledIds] = useState<number[]>([]);

  const currIndex = useRef(-1);

  // 更改切换加价方式
  const onMarkupType = (e: RadioChangeEvent) => {
    setMarkupType(e.target.value);
  };

  // 填写每件产品多少元
  const onWriteInput = (val: number | null, index: number) => {
    if (markupType) {
      freightRuleList[index].markupRatio = val;
    } else {
      freightRuleList[index].markupPrice = val;
    }

    setFreightRuleList([...freightRuleList]);
  };

  // 设置是否为配送区域
  const onSetUpDelivery = (val: number, index: number) => {
    freightRuleList[index].isDelivered = val ? 0 : 1;
    setFreightRuleList([...freightRuleList]);
  };

  // 删除某一条数据
  const onDelete = (index: number) => {
    freightRuleList.splice(index, 1);
    if (freightRuleList.length === 1 && freightRuleList[0].deliveredAreaType === 3) {
      freightRuleList.splice(0, 1);
    }
    if (!freightRuleList.length) {
      freightRuleList.push({
        id: createUuid(),
        isDelivered: 1,
        deliveredAreaType: 1,
        provinceList: [],
      });
    }
    setFreightRuleList([...freightRuleList]);
  };

  const onclose = () => {
    closeDrawer(false);
  };

  // 保存
  const onSave = debounce(() => {
    if (!name) {
      message.error(t('newBuildTemplateDrawer_nameRequired'));
      return;
    }
    const ratioList = freightRuleList?.filter((item) => item.markupRatio !== null);
    const priceList = freightRuleList?.filter((item) => item.markupPrice !== null);
    if (markupType && ratioList.length !== freightRuleList.length) {
      message.error(t('newBuildTemplateDrawer_enterFreightPrice'));
      return;
    }
    if (!markupType && priceList.length !== freightRuleList.length) {
      message.error(t('newBuildTemplateDrawer_enterFreightPrice'));
      return;
    }

    postSaveFreightTemplate({
      name,
      isDefault: Number(isDefault),
      markupType,
      // @ts-ignore
      freightRuleList: freightRuleList.map((item) => ({
        isDelivered: item.isDelivered,
        deliveredAreaType: item.deliveredAreaType,
        markupPrice: item.markupPrice,
        ...(markupType ? { markupRatio: item.markupRatio } : { markupPrice: item.markupPrice }),
        provinceList: item.provinceList,
      })),
    }).then(() => {
      onSuccess();
      message.success(t('newBuildTemplateDrawer_saveSuccess'));
    });
  }, 500);

  useEffect(() => {
    if (!isVisible) {
      setFreightRuleList([
        {
          id: createUuid(),
          isDelivered: 1,
          deliveredAreaType: 1,
          markupPrice: null,
          markupRatio: null,
          provinceList: [],
        },
      ]);
      setMarkupType(0);
      setIsDefault(false);
      setName('');
      currIndex.current = -1;
    }
  }, [isVisible]);

  const limitDecimalsF = (value: number | null) => {
    const reg = /^(-)*(\d+)\.(\d\d).*$/;
    return value === null
      ? null
      : `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',').replace(reg, '$1$2.$3');
  };
  const limitDecimalsP = (value: number | null) => {
    const reg = /^(-)*(\d+)\.(\d\d).*$/;
    return value === null
      ? null
      : String(value)
          .replace(/￥\s?|(,*)/g, '')
          .replace(reg, '$1$2.$3');
  };

  const createAreaText = (
    provinceList: FreightTemplateParams['freightRuleList']['provinceList']
  ) => {
    let areaNode: ReactNode | string = '';
    if (provinceList?.length) {
      areaNode = provinceList.map((item) => (
        <div key={item.provinceId}>
          {item.provinceName}:{item.cityList.map((city) => city.cityName).join('、')}
        </div>
      ));
    }
    return areaNode;
  };

  const createCityArea = (val: FreightTemplateParams['freightRuleList']) => {
    let areaNode: ReactNode | string = '';
    if (val.deliveredAreaType === 1) {
      areaNode = <div className={styles.bodyRegion}>{t('newBuildTemplateDrawer_allCities')}</div>;
    } else if (val.deliveredAreaType === 2) {
      areaNode = <div className={styles.bodyRegion}>{createAreaText(val.provinceList)}</div>;
    } else {
      areaNode = <div className={styles.bodyRegion}>{t('newBuildTemplateDrawer_otherCities')}</div>;
    }
    return areaNode;
  };

  return (
    <div>
      <Drawer
        title={t('newBuildTemplateDrawer_title')}
        placement="right"
        visible={isVisible}
        onClose={onclose}
        size="large"
        footer={
          <Drawer.Footer
            okText={t('newBuildTemplateDrawer_saveAndFinish')}
            cancelText={t('public_cancel')}
            onOk={onSave}
            onCancel={onclose}
          />
        }
      >
        <div className={styles.templateCard}>
          <div className={styles.flexBox}>
            <div className={styles.bigLabel}>{t('newBuildTemplateDrawer_templateName')}</div>
            <div className={styles.flexBox}>
              <div className={styles.smallLabel}>{t('newBuildTemplateDrawer_setAsDefault')}</div>
              <Switch size="small" checked={isDefault} onChange={(val) => setIsDefault(val)} />
            </div>
          </div>
          <Input
            className={styles.formValue}
            placeholder={t('newBuildTemplateDrawer_enterTemplateName')}
            value={name}
            onChange={(e) => setName(e.target.value.replace(/ /g, ''))}
          />
          <div className={styles.bigLabel}>{t('newBuildTemplateDrawer_markupType')}</div>
          <Radio.Group onChange={onMarkupType} value={markupType} className={styles.formValue}>
            <Radio value={0}>{t('newBuildTemplateDrawer_fixedPriceMarkup')}</Radio>
            <Radio value={1}>{t('newBuildTemplateDrawer_fixedRatioMarkup')}</Radio>
          </Radio.Group>
          <div className={styles.bigLabel}>{t('newBuildTemplateDrawer_citySettings')}</div>
          {/* 城市设置-表格 */}
          <div className={styles.tableBox}>
            <div className={styles.tableTitle}>
              <div className={styles.titleRegion}>{t('newBuildTemplateDrawer_region')}</div>
              <div className={styles.titleCount}>
                {t('newBuildTemplateDrawer_freightCalculationRule')}
              </div>
              <div className={styles.titleOperate}>{t('newBuildTemplateDrawer_operation')}</div>
            </div>
            {freightRuleList?.map((item, index: number) => (
              <div
                className={styles.tableBody}
                key={item.id}
                style={{
                  borderBottom: freightRuleList.length === index + 1 ? '' : '1px solid #f3f3f3',
                }}
              >
                {createCityArea(item)}
                <div>
                  {item.isDelivered ? (
                    <div className={styles.bodyCount}>
                      {t('newBuildTemplateDrawer_perProduct')} +
                      {markupType ? (
                        <InputNumber
                          placeholder={t('newBuildTemplateDrawer_enter')}
                          style={{ width: '70px', margin: '0 8px' }}
                          value={item.markupRatio}
                          min={0}
                          // @ts-ignore
                          formatter={limitDecimalsF}
                          // @ts-ignore
                          parser={limitDecimalsP}
                          onChange={(val) => onWriteInput(val as number | null, index)}
                        />
                      ) : (
                        <InputNumber
                          placeholder={t('newBuildTemplateDrawer_enter')}
                          style={{ width: '70px', margin: '0 8px' }}
                          value={item.markupPrice}
                          min={0}
                          // @ts-ignore
                          formatter={limitDecimalsF}
                          // @ts-ignore
                          parser={limitDecimalsP}
                          onChange={(val) => onWriteInput(val as number | null, index)}
                        />
                      )}
                      {!markupType
                        ? t('newBuildTemplateDrawer_currency')
                        : t('newBuildTemplateDrawer_percentage')}
                    </div>
                  ) : (
                    <div className={styles.bodyCount}>
                      {t('newBuildTemplateDrawer_notDelivered')}
                    </div>
                  )}
                </div>
                <div className={styles.bodyOperate}>
                  {item.deliveredAreaType === 2 && (
                    <div
                      className={styles.blueBtn}
                      tabIndex={-1}
                      role="button"
                      onClick={() => {
                        setCurrProvinceList(item.provinceList || []);
                        setShowSelectArea(true);
                        currIndex.current = index;
                        const newDisabledIds: number[] = [];
                        freightRuleList?.forEach((ruleItem, ruleIndex) => {
                          if (ruleIndex !== index) {
                            ruleItem.provinceList?.forEach((proItem) => {
                              proItem.cityList?.forEach((city) => {
                                newDisabledIds.push(city.cityId);
                              });
                            });
                          }
                        });
                        setDisabledIds(newDisabledIds);
                      }}
                    >
                      {t('newBuildTemplateDrawer_modifyCityInfo')}
                    </div>
                  )}

                  <div
                    className={styles.blueBtn}
                    tabIndex={-1}
                    role="button"
                    onClick={() => onSetUpDelivery(item.isDelivered, index)}
                  >
                    {t('newBuildTemplateDrawer_setAsDeliveryArea', {
                      isDelivered: item.isDelivered
                        ? t('newBuildTemplateDrawer_not')
                        : t('newBuildTemplateDrawer_can'),
                    })}
                  </div>
                  {item.deliveredAreaType === 2 && (
                    <div className={styles.redBtn} tabIndex={-1} role="button">
                      <Popconfirm
                        placement="bottom"
                        title={t('newBuildTemplateDrawer_confirmDelete')}
                        onConfirm={() => onDelete(index)}
                      >
                        <span className={styles.delete}>{t('public_delete')}</span>
                      </Popconfirm>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
          <div
            className={styles.addGraphicDescBtn}
            role="presentation"
            onClick={() => {
              setCurrProvinceList([]);
              currIndex.current = -1;
              setShowSelectArea(true);
              const cityList = freightRuleList
                ?.map((ruleItem) =>
                  ruleItem.provinceList?.map((proItem) =>
                    proItem.cityList?.map((city) => city.cityId)
                  )
                )
                .flat()
                .flat() as number[];
              setDisabledIds(cityList);
            }}
          >
            <Icon name="plus" /> {t('newBuildTemplateDrawer_selectCityToSetFreight')}
          </div>
        </div>
      </Drawer>
      <SelectCity
        isVisible={showSelectArea}
        selectedData={currProvinceList || []}
        disabledIds={disabledIds}
        closeDrawer={setShowSelectArea}
        onConfirm={(values) => {
          if (currIndex.current !== -1) {
            freightRuleList[currIndex.current].provinceList = values;
          } else {
            const allAreaList = freightRuleList?.filter((item) => item.deliveredAreaType === 1);
            if (allAreaList.length) {
              setFreightRuleList([
                {
                  id: createUuid(),
                  isDelivered: 1,
                  deliveredAreaType: 2,
                  provinceList: values,
                },
                {
                  id: createUuid(),
                  isDelivered: 1,
                  deliveredAreaType: 3,
                  provinceList: [],
                },
              ]);
            } else {
              const otherItem = freightRuleList[freightRuleList.length - 1];
              freightRuleList.splice(freightRuleList.length - 1, 1);
              freightRuleList.push({
                id: createUuid(),
                isDelivered: 1,
                deliveredAreaType: 2,
                provinceList: values,
              });
              freightRuleList.push(otherItem);
              setFreightRuleList([...freightRuleList]);
            }
          }

          setShowSelectArea(false);
        }}
      />
    </div>
  );
}

export default NewBuildTemplateDrawer;
