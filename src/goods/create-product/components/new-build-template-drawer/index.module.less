.templateCard {
  padding: 20px;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.flexBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.bigLabel {
  font-size: 16px;
  font-weight: 500;
}

.smallLabel {
  font-size: 12px;
  margin-right: 8px;
}

.formValue {
  margin: 16px 0 28px;
}

.tableBox {
  margin: 16px 0;
  border-radius: 10px;
  border: 1px solid #f3f3f3;

  .tableTitle {
    display: flex;
    height: 40px;
    line-height: 40px;
    border-radius: 10px 10px 0 0;
    background-color: #f5f6fa;

    .titleRegion {
      width: 202px;
      text-align: center;
      border-right: 1px solid #f3f3f3;
    }

    .titleCount {
      width: 195px;
      text-align: center;
      border-right: 1px solid #f3f3f3;
    }

    .titleOperate {
      width: 131px;
      text-align: center;
    }
  }

  .tableBody {
    display: flex;

    .bodyRegion {
      width: 202px;
      padding: 18px 5px;
      border-right: 1px solid #f3f3f3;
    }

    .bodyCount {
      width: 195px;
      padding: 18px 5px;
      text-align: center;

      :global {
        .ant-input-number-handler-wrap {
          display: none !important;
        }
      }
    }

    .bodyOperate {
      width: 131px;
      border-left: 1px solid #f3f3f3;
      text-align: center;
      padding: 18px 0;
    }
  }
}

.blueBtn {
  color: #008cff;
  cursor: pointer;
  margin: 8px 0;
}

.redBtn {
  color: #ea1c26;
  cursor: pointer;
  margin-bottom: 4px;
}

.addGraphicDescBtn {
  color: @primary-color;
  display: flex;
  width: 100%;
  height: 60px;
  justify-content: center;
  align-items: center;
  border-radius: 10px;
  border: 1px dashed @primary-color;
  background: rgb(217 238 255 / 30%);
  cursor: pointer;
}
