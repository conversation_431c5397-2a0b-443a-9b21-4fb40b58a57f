.noticeText {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 12px;
}

.cardBox {
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-input-number-handler-wrap {
      display: none !important;
    }

    .ant-input-number-group-addon {
      border: 0;
      background: white;
    }

    .ant-input-number-input {
      // text-align: right;
      padding: 0;
    }

    // .ant-select-selection-search-input {
    //   // text-align: right;
    // }
  }
}

.cardTitle {
  font-size: 16px;
  font-weight: bolder;
  margin-bottom: 13px;
}

.flexBox {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input {
  font-size: 16px;
  width: 100%;
  padding: 0;
  text-align: left;

  // :global {

  //   padding: 0;
  // }
}

.basicUnit {
  color: #888b98;
  font-size: 12px;
  margin-bottom: 17px;
}

.line {
  margin: 16px 0;
}

.selectInput {
  font-size: 16px;
  width: 100%;
  text-align: left;
}

.batchItem {
  font-size: 16px;

  .batchItemLabel {
    font-size: 14px;
    margin-bottom: 16px;
  }

  :global {
    .ant-input-number-input {
      padding: 0;
    }

    .ant-input-number-group-addon {
      padding-left: 4px;
    }
  }

  .price {
    color: #ea1c26;
  }
}

.bottomBorder {
  padding-bottom: 18px;
  border-bottom: 1px solid #f3f3f3;
}

.flexColumn {
  display: flex;
  flex-direction: column;
}

.batchLabelColumn {
  flex: 1;
  word-break: break-all;
  margin-bottom: 10px;
}

.batchValueColumn {
  flex: 1;
  text-align: left;

  :global {
    .ant-select-selector {
      padding: 0 !important;
    }
  }
}
