import { Dispatch, SetStateAction, useState, useEffect, useRef } from 'react';
import { Divider, Select } from 'antd';
import classNames from 'classnames';
import { useRequest } from 'ahooks';
import { Drawer, NumericInput } from '@/components';
import { getGoodsPriceSimpleShelf } from '@/apis';
import { ResultType as FreightOptionType } from '@/apis/base/get-freight-options';
import { VarListType } from '@/apis/pms/type';
import { useTranslation } from 'react-i18next';
// import { limitDecimalsF, limitDecimalsP } from '../../utils/input';
import styles from './index.module.less';

interface ManagerDrawerType {
  isShow: boolean;
  categoryId: number;
  unit: string;
  priceNum: number;
  formulaId: number;
  formulaName: string;
  formatFormula: string;
  formulaList: VarListType[];
  standardList: { name: string; value: string }[][];
  freightOptions: FreightOptionType[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<
    [
      marketPrice: string | null,
      costPrice: string | null,
      templateId: number | null,
      values: VarListType[],
      updateKeys: string[]
    ]
  >;
}

function BatchPriceManagerDrawer({
  isShow,
  categoryId,
  unit,
  priceNum,
  formulaId,
  formulaName,
  formatFormula,
  formulaList,
  standardList,
  freightOptions,
  closeDrawer,
  onConfirm,
}: ManagerDrawerType) {
  const { t } = useTranslation();
  const [formulaData, setFormulaData] = useState<VarListType[]>([]);
  const [batchPrice, setBatchPrice] = useState<string | null>(null);

  const [marketPrice, setMarketPrice] = useState<string | null>(null);
  const [costPrice, setCostPrice] = useState<string | null>(null);
  const [freightTemplateId, setFreightTemplateId] = useState<number | null>(null);
  const updateKeys = useRef<string[]>([]);

  const onUpdateKeys = (key: string) => {
    if (!updateKeys.current.includes(key)) {
      updateKeys.current.push(key);
    }
  };

  // 设置零售价
  const onRetailPrice = (val: string) => {
    onUpdateKeys('marketPrice');
    setMarketPrice(val || null);
  };

  // 设置成本价
  const onCostPrice = (val: string) => {
    onUpdateKeys('costPrice');
    setCostPrice(val || null);
  };

  // 设置运费模板
  const onFreightTemplate = (val: number | null) => {
    onUpdateKeys('freightTemplateId');
    setFreightTemplateId(val);
  };

  const onclose = () => {
    closeDrawer(false);
  };

  const onOk = () => {
    // if (marketPrice === null || costPrice === null || freightTemplateId === null) {
    //   message.error('零售价、成本价和运费模板都不能为空');
    //   return;
    // }
    // if (
    //   formulaData &&
    //   formulaData.filter((item) => item.value === null || item.value === '').length > 0
    // ) {
    //   message.error('请设置公式变量');
    //   return;
    // }
    // if (formulaId && !batchPrice) {
    //   message.error('商品价格不能为0');
    //   return;
    // }
    // if (!formulaId && !batchPrice) {
    //   message.error('商品价格不能为0');
    //   return;
    // }
    // console.log(' updateKeys.current', updateKeys.current);

    onConfirm(
      formulaId ? batchPrice : marketPrice,
      costPrice,
      freightTemplateId,
      formulaData,
      updateKeys.current
    );
    closeDrawer(false);
  };

  const { run: batchRun } = useRequest(getGoodsPriceSimpleShelf, {
    debounceWait: 300,
    manual: true,
    onSuccess: (res) => {
      if (
        formulaData.length ===
        formulaData.filter((item) => item.value !== null && item.value !== '').length
      ) {
        setBatchPrice(res.priceList[0]);
      } else {
        setBatchPrice(null);
      }
    },
    onError: () => {
      setBatchPrice(null);
    },
  });

  const onBatchFormulaSetRetailPrice = (val: string, index: number) => {
    onUpdateKeys('var');
    onUpdateKeys('marketPrice');
    formulaData[index].value = val || null;
    setFormulaData([...formulaData]);
    if (
      formulaData.length ===
      formulaData.filter((item) => item.value !== null && item.value !== '').length
    ) {
      batchRun({
        formulaId,
        formulaList: formulaData,
        standardList,
        catId: categoryId,
        unit,
      });
    }
  };

  useEffect(() => {
    if (isShow) {
      setBatchPrice(null);
      setFreightTemplateId(null);
      if (formulaList && formulaList.length) {
        setFormulaData(formulaList?.map((item) => ({ ...item, value: null })));
      } else if (formulaId) {
        batchRun({
          formulaId,
          formulaList: [],
          standardList,
          catId: categoryId,
          unit,
        });
      }
    } else {
      setFormulaData([]);
      setBatchPrice(null);
      // setFreightTemplateId(null);
      setMarketPrice(null);
      setCostPrice(null);
      updateKeys.current = [];
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formulaList, isShow]);

  return (
    <Drawer
      title={t('batchBasicUnitDrawer_batchSet')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.noticeText}>{t('multiple_notice')}</div>

      {formulaId ? (
        <div className={styles.cardBox} style={{ marginBottom: 20 }}>
          <div className={styles.cardTitle}>{t('batchBasicUnitDrawer_batchSet')}</div>
          <div className={styles.batchItem}>
            <div className={styles.batchItemLabel}>{t('multiple_formulaName')}</div>
            <div>{formulaName}</div>
          </div>
          <Divider className={styles.line} />
          <div className={classNames(styles.batchItem, styles.bottomBorder)}>
            <div className={styles.batchItemLabel}>{t('multiple_formula')}</div>
            <div>{formatFormula}</div>
          </div>
          {formulaData?.map((item, index) => (
            // eslint-disable-next-line react/no-array-index-key
            <div className={styles.flexColumn} key={index}>
              <div className={styles.batchLabelColumn} style={{ marginTop: 16 }}>
                {item.name}
              </div>
              <div className={styles.batchValueColumn}>
                <NumericInput
                  className={styles.input}
                  bordered={false}
                  placeholder={t('multiple_enterVariableValue')}
                  min={0}
                  maxLength={20}
                  precision={15}
                  onChange={(val) => onBatchFormulaSetRetailPrice(val, index)}
                  value={item.value ? `${item.value}` : ''}
                />
                {/* <InputNumber
                  className={styles.input}
                  bordered={false}
                  placeholder="请输入变量值"
                  formatter={(val) => limitDecimalsF(val, priceNum || 0)}
                  // @ts-ignore
                  parser={(val) => limitDecimalsP(val, priceNum || 0, 8) as number}
                  max={99999999}
                  // maxLength={8}
                  min={0}
                  onChange={(val) => onBatchFormulaSetRetailPrice(val as number | null, index)}
                /> */}
              </div>
              <Divider className={styles.line} />
            </div>
          ))}
          <div className={styles.batchItem}>
            <div className={styles.batchItemLabel}>{t('multiple_retailPrice')}</div>
            <div className={styles.price}>￥{batchPrice || 0}</div>
          </div>
        </div>
      ) : null}

      <div className={styles.cardBox}>
        {formulaId ? null : (
          <>
            <div className={styles.flexColumn}>
              <div className={styles.batchLabelColumn}>{t('multiple_retailPrice')}</div>
              <div className={styles.batchValueColumn}>
                <NumericInput
                  className={styles.input}
                  bordered={false}
                  placeholder={t('multiple_enterVariableValue2')}
                  max={99999999}
                  min={0}
                  maxLength={(priceNum ? priceNum + 1 : 0) + 8}
                  precision={priceNum || 0}
                  onChange={(val) => onRetailPrice(val)}
                  value={marketPrice ? `${marketPrice}` : ''}
                />
                {/* <InputNumber
                  className={styles.input}
                  bordered={false}
                  placeholder="请输入金额"
                  onChange={(val) => onRetailPrice(val)}
                  value={marketPrice}
                  formatter={(val) => limitDecimalsF(val, priceNum || 0)}
                  // @ts-ignore
                  parser={(val) => limitDecimalsP(val, priceNum || 0, 8) as number}
                  // maxLength={9}
                  max={99999999}
                  min={0}
                /> */}
              </div>
            </div>
            <Divider className={styles.line} />
          </>
        )}

        <div className={styles.flexColumn}>
          <div className={styles.batchLabelColumn}>{t('base_initialCost')}</div>
          <div className={styles.batchValueColumn}>
            <NumericInput
              className={styles.input}
              bordered={false}
              placeholder={t('multiple_enterVariableValue2')}
              max={99999999}
              min={0}
              maxLength={(priceNum ? priceNum + 1 : 0) + 8}
              precision={priceNum || 0}
              onChange={(val) => onCostPrice(val)}
              value={costPrice ? `${costPrice}` : ''}
            />
            {/* <InputNumber
              className={styles.input}
              bordered={false}
              placeholder="请输入金额"
              // addonAfter="元"
              onChange={(val) => onCostPrice(val)}
              value={costPrice}
              formatter={(val) => limitDecimalsF(val, priceNum || 0)}
              // @ts-ignore
              parser={(val) => limitDecimalsP(val, priceNum || 0, 8) as number}
              max={99999999}
              maxLength={15}
            /> */}
          </div>
        </div>
        <Divider className={styles.line} />
        <div className={styles.flexColumn}>
          <div className={styles.batchLabelColumn}>{t('base_freightTemplate')}</div>
          <div className={styles.batchValueColumn}>
            <Select
              allowClear
              showSearch
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
              className={styles.selectInput}
              bordered={false}
              placeholder={t('batchFreightTemplateDrawer_selectTemplate')}
              options={freightOptions}
              fieldNames={{
                label: 'name',
                value: 'id',
              }}
              value={freightTemplateId}
              onChange={(e) => onFreightTemplate(e)}
            />
          </div>
        </div>
      </div>
    </Drawer>
  );
}

export default BatchPriceManagerDrawer;
