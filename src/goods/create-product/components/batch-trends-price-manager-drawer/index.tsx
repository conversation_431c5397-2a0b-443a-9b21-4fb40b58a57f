import { Dispatch, SetStateAction } from 'react';
import { Divider, Select, Input } from 'antd';
import { Drawer } from '@/components';
import { ResultType as FreightOptionType } from '@/apis/base/get-freight-options';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface ManagerDrawerType {
  isShow: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  freightOptions: FreightOptionType[];
}

function BatchTrendsPriceManagerDrawer(props: ManagerDrawerType) {
  const { isShow, closeDrawer, freightOptions } = props;
  const { t } = useTranslation();

  // 设置成本价
  const onCostPrice = (val: string) => {
    window.console.log(val, 'val');
  };

  // 设置运费模板
  const onfreightTemplate = (val: string) => {
    window.console.log(val, 'val');
  };

  const onOk = () => {};

  const onclose = () => {
    closeDrawer(false);
  };

  return (
    <Drawer
      title={t('batchTrendsPriceManagerDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.noticeText}>{t('batchTrendsPriceManagerDrawer_notice')}</div>
      <div className={styles.cardBox}>
        <div className={styles.flexBox}>
          <div>{t('batchTrendsPriceManagerDrawer_formulaName')}</div>
          <div>{t('orange_juice_import_price_formula')}</div>
        </div>
        <div className={styles.flexBox}>
          <div>{t('batchTrendsPriceManagerDrawer_formula')}</div>
          <div>{t('100_plus_base_price')}</div>
        </div>
        <Divider className={styles.line} />
        <div className={styles.flexBox}>
          <div>{t('batchTrendsPriceManagerDrawer_variableName')}</div>
          <Input
            className={styles.input}
            placeholder={t('batchTrendsPriceManagerDrawer_enterVariableValue')}
            bordered={false}
            // onChange={(e) => onRetailPrice(e.target.value)}
          />
        </div>
        <div className={styles.flexBoxBottom}>
          <div>{t('batchTrendsPriceManagerDrawer_retailPrice')}</div>
          <div className={styles.redPrice}>¥ 200.00</div>
        </div>
      </div>

      <div className={styles.cardBox}>
        <div className={styles.flexBox}>
          <div>{t('batchTrendsPriceManagerDrawer_costPrice')}</div>
          <Input
            className={styles.input}
            placeholder={t('batchTrendsPriceManagerDrawer_enterAmount')}
            suffix={t('public_moneySymbol2')}
            bordered={false}
            onChange={(e) => onCostPrice(e.target.value)}
          />
        </div>
        <Divider className={styles.line} />
        <div className={styles.flexBoxBottom}>
          <div>{t('batchTrendsPriceManagerDrawer_freightTemplate')}</div>
          <Select
            className={styles.selectInput}
            bordered={false}
            placeholder={t('batchTrendsPriceManagerDrawer_selectFreightTemplate')}
            options={freightOptions}
            fieldNames={{
              label: 'name',
              value: 'id',
            }}
            onChange={(e) => onfreightTemplate(e)}
          />
        </div>
      </div>
    </Drawer>
  );
}

export default BatchTrendsPriceManagerDrawer;
