import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import { Divider } from 'antd';
import { Drawer, NumericInput } from '@/components';
import { TableRowType } from '@/apis/pms/type';
// import { limitDecimalsF, limitDecimalsP } from '../../utils/input';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface UnitDrawerType {
  isShow: boolean;
  dataList: TableRowType[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[values: (number | string | null)[]]>;
}

function BatchCostPriceDrawer({ isShow, dataList, closeDrawer, onConfirm }: UnitDrawerType) {
  const { t } = useTranslation();
  const [list, setList] = useState<(TableRowType & { costPriceVal: number | null | string })[]>([]);
  const [batchVal, setBatchVal] = useState<string | null>(null);
  // const [errorIndexList, setErrorIndexList] = useState<number[]>([]);

  // const checkError = (arr = list) => {
  //   const errorList = arr.filter(
  //     (item) => item.costPriceVal === null || item.costPriceVal === undefined
  //   );
  //   const errIndexList = errorList.map((_item, index) => index);
  //   setErrorIndexList(errIndexList);
  //   return errIndexList;
  // };

  // const validateFields = () =>
  //   new Promise((resolve, reject) => {
  //     const errIndexList = checkError();
  //     if (errIndexList.length) {
  //       reject(errIndexList);
  //     } else {
  //       resolve(list);
  //     }
  //   });

  // 批量设置修改期初成本
  const onBatchSetCostPrice = (val: string) => {
    const batchList = list.map((item) => ({
      ...item,
      costPriceVal: val || null,
    }));
    setList(batchList);
    setBatchVal(val || null);
  };

  // 单个设置修改期初成本
  const onSingleSetCostPrice = (val: string, index: number) => {
    list[index].costPriceVal = val || null;
    setList([...list]);
    // checkError(list);
    // const ids = Array.from(new Set(list.map((item) => item.costPriceVal) || []));
    // setBatchVal(ids.length > 1 ? null : ids[0]);
  };

  const onOk = () => {
    // validateFields().then(() => {
    onConfirm(list.map((item) => item.costPriceVal));
    closeDrawer(false);
    // });
  };

  const onclose = () => {
    closeDrawer(false);
    setBatchVal(null);
    // setErrorIndexList([]);
  };

  useEffect(() => {
    const arr = dataList.map((item) => ({ ...item, costPriceVal: item.costPrice }));
    // const ids = Array.from(new Set(arr.map((item) => item.costPriceVal) || []));
    // setBatchVal(ids.length > 1 ? null : ids[0]);
    setList(arr);
  }, [dataList]);

  useEffect(() => {
    if (isShow) {
      setBatchVal(null);
    }
  }, [isShow]);

  return (
    <Drawer
      title={t('batchCostPriceDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={
        <Drawer.Footer
          okText={t('public_confirm')}
          cancelText={t('public_cancel')}
          onOk={onOk}
          onCancel={onclose}
        />
      }
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchCostPriceDrawer_batchSet')}</div>
        <div className={styles.batchBox}>
          <div className={styles.batchLabel}>{t('initial_cost')}</div>
          <div className={styles.batchValue}>
            <NumericInput
              className={styles.input}
              bordered={false}
              placeholder={t('batchCostPriceDrawer_enterCost2')}
              suffix={t('public_moneySymbol2')}
              max={99999999}
              min={0}
              maxLength={(list?.[0]?.priceNum ? (list?.[0]?.priceNum || 0) + 1 : 0) + 8}
              precision={list?.[0]?.priceNum || 0}
              onChange={(val) => onBatchSetCostPrice(val)}
              value={batchVal ? `${batchVal}` : ''}
            />
            {/* <InputNumber
              className={styles.input}
              bordered={false}
              placeholder="请输入金额"
              addonAfter="元"
              value={batchVal}
              onChange={(val) => onBatchSetCostPrice(val)}
              formatter={(val) => limitDecimalsF(val, list?.[0]?.priceNum || 0)}
              parser={(val) => limitDecimalsP(val, list?.[0]?.priceNum || 0, 8) as number}
              max={99999999}
              maxLength={15}
              min={0}
            /> */}
          </div>
        </div>
      </div>

      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchCostPriceDrawer_singleSet')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>
              {(item?.standardList.map((standItem) => standItem.value) || []).join('/') || ''}
            </div>
            <div className={styles.batchBox}>
              <div className={styles.batchLabel}>{t('base_initialCost2')}</div>
              <div className={styles.batchValue}>
                <NumericInput
                  className={styles.input}
                  bordered={false}
                  placeholder={t('batchCostPriceDrawer_enterCost2')}
                  suffix={t('public_moneySymbol2')}
                  max={99999999}
                  min={0}
                  maxLength={(item.priceNum ? item.priceNum + 1 : 0) + 8}
                  precision={item.priceNum || 0}
                  onChange={(val) => onSingleSetCostPrice(val, index)}
                  value={item.costPriceVal ? `${item.costPriceVal}` : ''}
                />
                {/* <InputNumber
                  className={styles.input}
                  bordered={false}
                  placeholder="请输入金额"
                  addonAfter="元"
                  value={item.costPriceVal}
                  maxLength={8 + item.priceNum}
                  onChange={(val) => onSingleSetCostPrice(val, index)}
                  formatter={(val) => limitDecimalsF(val, item.priceNum)}
                  parser={(val) => limitDecimalsP(val, item.priceNum) as number}
                /> */}
                {/* <div
                  className={styles.errorMsg}
                  style={{ display: errorIndexList.includes(index) ? 'block' : 'none' }}
                >
                  请输入期初成本
                </div> */}
              </div>
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchCostPriceDrawer;
