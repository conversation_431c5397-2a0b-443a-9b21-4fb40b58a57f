.cardBox {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  :global {
    .ant-input-number-handler-wrap {
      display: none !important;
    }

    .ant-input-number-group-addon {
      border: 0;
      background: white;
    }

    .ant-input-number-input {
      padding: 0;
      // text-align: right;
    }
  }
}

.cardTitle {
  font-size: 16px;
  font-weight: bolder;
  margin-bottom: 13px;
}

.input {
  font-size: 16px;
  width: 100%;
  padding: 0;

  :global {
    .ant-input {
      font-size: 16px;
    }
  }
}

.batchBox {
  display: flex;
  flex-direction: column;
}

.batchValue {
  margin-top: 8px;
}

.cardName {
  display: -webkit-box;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.line {
  margin: 16px 0;
}

.errorMsg {
  color: #ea1c26;
  line-height: 24px;
  margin-left: 15px;
}
