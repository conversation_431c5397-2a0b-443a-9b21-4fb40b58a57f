.cardBox {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.cardTitle {
  font-size: 16px;
  font-weight: bolder;
  margin-bottom: 13px;
}

.selectInput {
  font-size: 16px;
  width: 100%;
  // text-align: right;
}

.batchBox {
  display: flex;
  flex-direction: column;
  // align-items: center;
  // justify-content: space-between;
  .batchLabel {
    width: 100%;
  }

  .batchValue {
    width: 100%;
    margin-top: 10px;

    :global {
      .ant-select-selector {
        padding: 0 !important;
      }
    }
  }
}

.cardName {
  display: -webkit-box;
  margin-bottom: 15px;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.line {
  margin: 16px 0;
}

.errorMsg {
  color: #ea1c26;
  line-height: 24px;
}
