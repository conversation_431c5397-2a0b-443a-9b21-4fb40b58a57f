import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import { Select, Divider } from 'antd';
import { Drawer } from '@/components';
import { ResultType as FreightOptionType } from '@/apis/base/get-freight-options';
import { TableRowType } from '@/apis/pms/type';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface UnitDrawerType {
  isShow: boolean;
  freightOptions: FreightOptionType[];
  dataList: TableRowType[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<[values: (number | null)[]]>;
}

function BatchFreightTemplateDrawer({
  isShow,
  dataList,
  freightOptions,
  closeDrawer,
  onConfirm,
}: UnitDrawerType) {
  const { t } = useTranslation();
  const [list, setList] = useState<(TableRowType & { freightTemplateIdVal: number | null })[]>([]);
  const [batchVal, setBatchVal] = useState<number | null>(null);
  // const [errorIndexList, setErrorIndexList] = useState<number[]>([]);

  // const checkError = (arr = list) => {
  //   const errorList = arr.filter((item) => !item.freightTemplateIdVal);
  //   const errIndexList = errorList.map((_item, index) => index);
  //   setErrorIndexList(errIndexList);
  //   return errIndexList;
  // };

  // const validateFields = () =>
  //   new Promise((resolve, reject) => {
  //     const errIndexList = checkError();
  //     if (errIndexList.length) {
  //       reject(errIndexList);
  //     } else {
  //       resolve(list);
  //     }
  //   });

  // 批量设置基本单位
  const onBatchSetUnify = (val: number | null) => {
    const batchList = list.map((item) => ({
      ...item,
      freightTemplateIdVal: val,
    }));
    setList(batchList);
    setBatchVal(val);
  };

  // 单个设置基本单位
  const onSingleSetUnify = (val: number | null, index: number) => {
    list[index].freightTemplateIdVal = val;
    const ids = Array.from(new Set(list.map((item) => item.freightTemplateIdVal) || []));
    setBatchVal(ids.length > 1 ? null : ids[0]);
    setList([...list]);
    // checkError(list);
  };

  const onOk = () => {
    // validateFields().then(() => {
    onConfirm(list.map((item) => item.freightTemplateIdVal));
    closeDrawer(false);
    // });
  };

  const onclose = () => {
    closeDrawer(false);
    setBatchVal(null);
    // setErrorIndexList([]);
  };

  useEffect(() => {
    const arr = dataList.map((item) => ({ ...item, freightTemplateIdVal: item.freightTemplateId }));
    // const ids = Array.from(new Set(arr.map((item) => item.freightTemplateIdVal) || []));
    // setBatchVal(ids.length > 1 ? null : ids[0] || null);
    setList(arr);
  }, [dataList]);

  useEffect(() => {
    if (!isShow) {
      setBatchVal(null);
    }
  }, [isShow]);

  return (
    <Drawer
      title={t('batchFreightTemplateDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchFreightTemplateDrawer_batchSet')}</div>
        <div className={styles.batchBox}>
          <div className={styles.batchLabel}>{t('base_freightTemplate')}</div>
          <div className={styles.batchValue}>
            <Select
              showSearch
              allowClear
              className={styles.selectInput}
              bordered={false}
              placeholder={t('batchFreightTemplateDrawer_selectTemplate')}
              options={freightOptions}
              filterOption={(input, option) =>
                (option?.name ?? '').toLowerCase().includes(input.toLowerCase())
              }
              fieldNames={{
                label: 'name',
                value: 'id',
              }}
              onChange={onBatchSetUnify}
              value={batchVal}
            />
          </div>
        </div>
      </div>
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchFreightTemplateDrawer_singleSet')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>
              {(item?.standardList.map((standItem) => standItem.value) || []).join('/') || ''}
            </div>
            <div className={styles.batchBox}>
              <div className={styles.batchLabel}>{t('base_freightTemplate')}</div>
              <div className={styles.batchValue}>
                <Select
                  className={styles.selectInput}
                  bordered={false}
                  allowClear
                  value={item.freightTemplateIdVal || null}
                  placeholder={t('batchFreightTemplateDrawer_selectTemplate')}
                  options={freightOptions}
                  fieldNames={{
                    label: 'name',
                    value: 'id',
                  }}
                  onChange={(val) => onSingleSetUnify(val, index)}
                />
                {/* <div
                  className={styles.errorMsg}
                  style={{ display: errorIndexList.includes(index) ? 'block' : 'none' }}
                >
                  请选择运费模版
                </div> */}
              </div>
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchFreightTemplateDrawer;
