import { useState, Dispatch, SetStateAction, useEffect, ReactNode, useRef } from 'react';
import { Checkbox, Popover, message } from 'antd';
import { useMount } from 'ahooks';
import { Modal } from '@/components';
import { useTranslation } from 'react-i18next';

import { getProvinceCityCounty } from '@/apis';
import type { ProvinceCityCounty } from '@/apis/get-province-city-county';
import styles from './index.module.less';

type DataItem = {
  value: number;
  label: string;
};

type ProCityItem = {
  provinceId: number;
  provinceName: string;
  cityList: { cityId: number; cityName: string }[];
};

interface NewBuildTemplateType {
  isVisible: boolean;
  selectedData: ProCityItem[];
  disabledIds: number[];
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  onConfirm: MultipleParamsFn<
    [data: ProCityItem[], provinceData: DataItem[], cityData: DataItem[]]
  >;
}

// type ProCityDataItem = {
//   value: number;
//   label: string;
//   indeterminate: boolean;
//   checked: boolean;
//   children: ProCityDataItem[];
// };

function SelectCity({
  isVisible,
  selectedData,
  disabledIds,
  onConfirm,
  closeDrawer,
}: NewBuildTemplateType) {
  const { t } = useTranslation();
  const [provinceCityData, setProvinceCityData] = useState<ProvinceCityCounty[]>([]);
  const provinceData = useRef<DataItem[]>([]);
  const cityData = useRef<DataItem[]>([]);
  // const proCityData = useRef<ProCityDataItem[]>([]);
  const proCityList = useRef<ProCityItem[]>([]);
  const [checkedIds, setCheckedIds] = useState<number[]>([]);
  const [checkedProvinceIds, setCheckedProvinceIds] = useState<number[]>([]);
  const [indeterminateIds, setIndeterminateIds] = useState<number[]>([]);

  const [provinceDisabled, setProvinceDisabled] = useState<number[]>([]);

  const getData = () => {
    getProvinceCityCounty().then((res) => {
      setProvinceCityData(res?.list || []);
      const newProvinceDisabled: number[] = [];
      res?.list?.forEach((item) => {
        provinceData.current.push({ value: item.value, label: item.label });
        // const cityList: ProCityDataItem[] = [];
        if (item.children?.length) {
          if (
            item.children.filter((child) => disabledIds.includes(child.value)).length ===
            item.children.length
          ) {
            newProvinceDisabled.push(item.value);
          }
          item.children?.forEach((child) => {
            cityData.current.push({ value: child.value, label: child.label });
            // cityList.push({
            //   value: item.value,
            //   label: item.label,
            //   indeterminate: false,
            //   checked: false,
            //   children: [],
            // });
          });
        }
        // proCityData.current.push({
        //   value: item.value,
        //   label: item.label,
        //   indeterminate: false,
        //   checked: false,
        //   children: cityList,
        // });
      });
      setProvinceDisabled(newProvinceDisabled);
    });
  };

  const onProvinceChange = (val: boolean, item: ProvinceCityCounty) => {
    const childIds = item.children
      ?.map((child) => child.value)
      .filter((childItem) => !disabledIds.includes(childItem));
    if (val) {
      const indIdx = indeterminateIds.indexOf(item.value);
      if (indIdx !== -1) {
        indeterminateIds.splice(indIdx, 1);
      }
      const newCheckedIds = Array.from(new Set([...checkedIds, ...childIds]));
      const newCheckedProvinceIds = Array.from(new Set([...checkedProvinceIds, item.value]));
      setCheckedIds(newCheckedIds);
      setCheckedProvinceIds(newCheckedProvinceIds);
      if (proCityList.current?.filter((proItem) => proItem.provinceId === item.value).length) {
        proCityList.current?.forEach((proItem) => {
          const newProItem = proItem;
          if (proItem.provinceId === item.value) {
            newProItem.cityList = item.children
              ?.filter((child) => !disabledIds.includes(child.value))
              .map((child) => ({
                cityId: child.value,
                cityName: child.label,
              }));
          }
        });
      } else {
        proCityList.current.push({
          provinceId: item.value,
          provinceName: item.label,
          cityList: item.children
            ?.filter((child) => !disabledIds.includes(child.value))
            .map((child) => ({ cityId: child.value, cityName: child.label })),
        });
      }
    } else {
      for (let i = 0; i < checkedIds.length; i += 1) {
        const checkedItem = checkedIds[i];
        if (checkedItem === item.value || childIds.includes(checkedItem)) {
          checkedIds.splice(i, 1);
          i -= 1;
        }
      }
      const proIdx = checkedProvinceIds.indexOf(item.value);
      checkedProvinceIds.splice(proIdx, 1);
      setCheckedProvinceIds([...checkedProvinceIds]);
      setCheckedIds([...checkedIds]);
      setCheckedIds([...indeterminateIds]);
      const proIds = proCityList.current?.map((proItem) => proItem.provinceId);
      const proCityIndex = proIds.indexOf(item.value);
      if (proCityIndex !== -1) {
        proCityList.current.splice(proCityIndex, 1);
      }
    }
  };

  const onCityChange = (val: boolean, parentItem: ProvinceCityCounty, item: ProvinceCityCounty) => {
    const cityIds = parentItem.children?.map((child) => child.value);
    const proIds = proCityList.current?.map((proItem) => proItem.provinceId);
    const proCityIndex = proIds.indexOf(parentItem.value);

    if (val) {
      if (!checkedIds.includes(item.value) && !disabledIds.includes(item.value)) {
        checkedIds.push(item.value);
      }
      const isAllCityChecked =
        checkedIds?.filter((checkedItem) => cityIds.includes(checkedItem)).length ===
        cityIds.length;
      if (isAllCityChecked) {
        if (!checkedProvinceIds.includes(parentItem.value)) {
          checkedProvinceIds.push(parentItem.value);
        }
        const cityList = item.children?.map((child) => ({
          cityId: child.value,
          cityName: child.label,
        }));
        if (proCityIndex !== -1) {
          proCityList.current[proCityIndex].cityList = cityList;
        } else {
          proCityList.current.push({
            provinceId: parentItem.value,
            provinceName: parentItem.label,
            cityList,
          });
        }
      } else {
        const { value } = parentItem;
        if (!indeterminateIds.includes(value)) {
          indeterminateIds.push(value);
        }
        const obj = {
          cityId: item.value,
          cityName: item.label,
        };
        if (proCityIndex !== -1) {
          proCityList.current[proCityIndex].cityList.push(obj);
        } else {
          proCityList.current.push({
            provinceId: parentItem.value,
            provinceName: parentItem.label,
            cityList: [{ ...obj }],
          });
        }
      }
      setCheckedIds([...checkedIds]);
      setCheckedProvinceIds([...checkedProvinceIds]);
      setIndeterminateIds([...indeterminateIds]);
    } else {
      const index = checkedIds.indexOf(item.value);
      const proIndex = checkedProvinceIds.indexOf(parentItem.value);
      const proIdx = proIds.indexOf(parentItem.value);
      if (index !== -1) {
        checkedIds.splice(index, 1);
        const cityIndex =
          proCityList.current[proCityIndex].cityList
            ?.map((cityItem) => cityItem.cityId)
            .indexOf(item.value) || -1;
        if (cityIndex !== -1) {
          proCityList.current[proCityIndex].cityList.splice(cityIndex, 1);
        }
      }
      if (proIndex !== -1) {
        checkedProvinceIds.splice(proIndex, 1);
        // if (proCityIndex !== -1) {
        //   proCityList.current.splice(proIdx, 1);
        // }
      }
      const selectedCityData = checkedIds?.filter((checkedItem) => cityIds.includes(checkedItem));
      const isCityChecked = selectedCityData.length > 0;
      if (isCityChecked) {
        if (!indeterminateIds.includes(parentItem.value)) {
          indeterminateIds.push(parentItem.value);
        }
        if (proIdx === -1) {
          proCityList.current.push({
            provinceId: parentItem.value,
            provinceName: parentItem.label,
            cityList: parentItem.children
              ?.filter((child) => checkedIds.includes(child.value))
              .map((item2) => ({ cityId: item2.value, cityName: item2.label })),
          });
        }
      } else {
        const indIdx = indeterminateIds.indexOf(parentItem.value);
        if (indIdx !== -1) {
          indeterminateIds.splice(indIdx, 1);
        }
        if (proCityIndex !== -1) {
          proCityList.current.splice(proIdx, 1);
        }
      }
    }
    setCheckedIds([...checkedIds]);
    setCheckedProvinceIds([...checkedProvinceIds]);
    setIndeterminateIds([...indeterminateIds]);
  };

  const onOk = () => {
    const proList = provinceData.current?.filter((item) => checkedProvinceIds.includes(item.value));
    const cityList = cityData.current?.filter((item) => checkedIds.includes(item.value));
    // console.log(proList, cityList);

    if (!proCityList.current.length) {
      message.error(t('selectCity_selectAtLeastOneCity'));
      return;
    }
    onConfirm(proCityList.current, proList, cityList);
  };

  useEffect(() => {
    if (isVisible && selectedData && provinceCityData) {
      proCityList.current = selectedData;
      const cityIds = selectedData
        .map((item) => item.cityList?.map((child) => child.cityId))
        .flat();
      setCheckedIds(cityIds);
      const checkProIds: number[] = [];
      const indeterminateProIds: number[] = [];
      provinceCityData?.forEach((proItem) => {
        selectedData?.forEach((selectedItem) => {
          if (proItem.value === selectedItem.provinceId) {
            if (proItem.children.length === selectedItem.cityList.length) {
              checkProIds.push(proItem.value);
            } else {
              indeterminateProIds.push(proItem.value);
            }
          }
        });
      });
      setCheckedProvinceIds(checkProIds);
      setIndeterminateIds(indeterminateProIds);
    } else {
      setCheckedIds([]);
      setCheckedProvinceIds([]);
      setIndeterminateIds([]);
      proCityList.current = [];
    }
  }, [isVisible, selectedData, provinceCityData]);

  useMount(() => {
    getData();
  });

  // 保存

  const createCityItem = (item: ProvinceCityCounty) => {
    let element: ReactNode | string = '';
    if (item.children?.length) {
      element = (
        <div className={styles.cityBox}>
          {item.children?.map((child) => (
            <div className={styles.cityItem} key={child.value}>
              <Checkbox
                checked={checkedIds.includes(child.value)}
                onChange={(val) => {
                  onCityChange(val.target.checked, item, child);
                }}
                disabled={disabledIds.includes(child.value)}
              >
                {child.label}
              </Checkbox>
            </div>
          ))}
        </div>
      );
    }
    return element;
  };

  return (
    <div>
      <Modal
        title={t('selectCity_selectProvinceCity')}
        visible={isVisible}
        onCancel={() => {
          closeDrawer(false);
        }}
        onOk={onOk}
      >
        <div>{t('selectCity_selectedCities', { count: checkedIds.length })}</div>
        <div className={styles.provinceBox}>
          {provinceCityData?.map((item) => (
            <div className={styles.provinceItem} key={item.value}>
              <Popover content={createCityItem(item)} trigger="hover" placement="bottom">
                <Checkbox
                  indeterminate={indeterminateIds.includes(item.value)}
                  checked={checkedProvinceIds.includes(item.value)}
                  disabled={provinceDisabled.includes(item.value)}
                  onChange={(val) => {
                    onProvinceChange(val.target.checked, item);
                  }}
                >
                  {item.label}
                </Checkbox>
              </Popover>
            </div>
          ))}
        </div>
      </Modal>
    </div>
  );
}

export default SelectCity;
