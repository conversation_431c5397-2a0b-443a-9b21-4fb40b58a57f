.boxCard {
  margin-bottom: 20px;
  padding: 15px 20px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
}

.formulaNameBox {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .formulaName {
    display: flex;
    align-items: center;

    .required {
      color: #ea1c26;
      font-size: 20px;
      margin-top: 5px;
      margin-right: 2px;
    }

    .formulaLabel {
      font-size: 16px;
      font-weight: bolder;
    }
  }

  .formulaValue {
    width: 237px;
    text-align: right;
  }
}

.operateFormulaBox {
  display: flex;
  align-items: center;

  .operateFormula {
    font-size: 16px;
    font-weight: bolder;
    margin-right: 12px;
  }

  .redIcon {
    width: 8px;
    height: 8px;
    background-color: #ea1c26;
    border-radius: 50%;
  }

  .greenIcon {
    width: 8px;
    height: 8px;
    background-color: #05d380;
    border-radius: 50%;
  }

  .isFeasible {
    margin-left: 4px;
  }
}

.operateCharacter {
  font-size: 16px;
  font-weight: bolder;
}

.fingerboardBox {
  display: flex;
  flex-wrap: wrap;

  .fingerboardTag {
    width: auto;
    height: 28px;
    line-height: 28px;
    margin-top: 16px;
    margin-right: 10px;
    text-align: center;
    background-color: #f5f6fa;
    border-radius: 10px;
    cursor: pointer;
  }

  .fingerboardTag:hover {
    color: #00c6ff;
  }

  .fingerboardTag:active {
    color: #006eff;
  }
}

.formulaBox {
  display: flex;
  flex-wrap: wrap;

  .formula {
    display: flex;

    .tagInput {
      width: 100px;
      height: 28px;
      line-height: 28px;
      margin-top: 16px;
      background-color: #f5f6fa;
      border-radius: 10px;
      cursor: pointer;
    }

    .nullInput {
      width: 20px;
      height: 28px;
      line-height: 28px;
      margin-top: 16px;
      margin-right: 10px;
      background-color: #fff;
      border-radius: 10px;
      cursor: pointer;
    }
  }

  :global {
    .ant-input {
      text-align: center;
      padding: 0 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.nullformula {
  color: #888b98;
  width: 100%;
  margin-top: 23px;
  text-align: center;
}

.variableBox {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .variableTitle {
    font-size: 16px;
    font-weight: bolder;
  }

  .addVariable {
    color: #008cff;
    cursor: pointer;
  }
}

.variableTagBox {
  display: flex;
  flex-wrap: wrap;

  .variableTag {
    min-height: 28px;
    line-height: 28px;
    margin-top: 16px;
    margin-right: 10px;
    padding: 0 16px;
    text-align: left;
    background-color: #f5f6fa;
    border-radius: 10px;
    cursor: pointer;
  }
}

:global {
  .ant-drawer-content {
    background-color: rgb(245 246 250 / 100%) !important;
  }
}
