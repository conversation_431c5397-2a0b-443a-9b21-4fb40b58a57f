/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { createUuid } from '@/src/building-manage/pages/wx-applets/mp/pay-settings/utils';
import { Input, message } from 'antd';
import { Drawer } from '@/components';
import classNames from 'classnames';
import debounce from 'lodash/debounce';
import cloneDeep from 'lodash/cloneDeep';
import { checkFormula, createVariable, getVariableList } from '@/apis';
import { VariableType } from '@/apis/base/get-variable-list';
import { useTranslation } from 'react-i18next';
import { fingerboard } from '../../utils/index';
import AddVariableModal from '../add-variable-modal';
import styles from './index.module.less';

interface NewFormulaType {
  isShow: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  categoryId: number;
  onKeep: MultipleParamsFn<[value: string, values: string]>;
}

interface formulaListType {
  id: number;
  label: string | undefined;
  value: string | undefined;
  name?: string;
}

function NewFormulaDrawer(props: NewFormulaType) {
  const { isShow, closeDrawer, categoryId, onKeep } = props;
  const { t } = useTranslation();

  const [formulaName, setFormulaName] = useState('');
  const [isFunction, setIsFunction] = useState(false);
  const [variablePop, setVariablePop] = useState(false);
  const [variableList, setVariableList] = useState<VariableType[]>([]);
  const [formulaList, setFormulaList] = useState<formulaListType[]>([]);
  const formulaIndex = useRef({ subscript: -1, isInsert: false });

  // 页面初始化变量
  const onGetVariableList = () => {
    getVariableList({
      catalogId: categoryId,
      pageSize: 9999,
      pageNo: 1,
    }).then((res) => {
      setVariableList(res.list);
    });
  };

  // 验证公式是否可以运行
  const testFormula = (dataList: formulaListType[]) => {
    checkFormula({ formula: dataList.map((item: formulaListType) => item.value).join('') })
      .then(() => {
        setIsFunction(true);
      })
      .catch(() => {
        setIsFunction(false);
      });
  };

  // 点击选择变量博并且校验公式是否可以运行, 修改公式根据下标进行修改,
  const addVariable = (variableObj: formulaListType) => {
    if (formulaIndex.current.subscript >= 0) {
      if (formulaIndex.current.isInsert) {
        const insertFormulaList = cloneDeep(formulaList);
        insertFormulaList.splice(formulaIndex.current.subscript + 1, 0, variableObj);
        setFormulaList(insertFormulaList);
        testFormula(insertFormulaList);
      } else {
        const editFormulaList = formulaList.map((item, index) =>
          index === formulaIndex.current.subscript ? variableObj : item
        );
        setFormulaList(editFormulaList);
        testFormula(editFormulaList);
      }
      formulaIndex.current.subscript = -1;
      formulaIndex.current.isInsert = false;
    } else {
      const addFormulaList = formulaList.concat([variableObj]);
      setFormulaList(addFormulaList);
      testFormula(addFormulaList);
    }
  };

  // 删除运算公式中的某一个值
  const delFormulaTag = (e: any, value: number) => {
    if (e.keyCode === 8 || e.keyCode === 46) {
      const delFormulaList = formulaList.filter((item, index) => index !== value);
      setFormulaList(delFormulaList);
      testFormula(delFormulaList);
      formulaIndex.current.subscript = -1;
      formulaIndex.current.isInsert = false;
    }
  };

  // 选中运算公式的下标
  const selectFocusTag = (index: number, insert: boolean) => {
    window.console.log(index, insert, 'insert');
    formulaIndex.current.subscript = index;
    formulaIndex.current.isInsert = insert;
  };

  // 关闭新建动态价格公式弹窗
  const onclose = () => {
    closeDrawer(false);
  };

  // 添加变量
  const onAddVariable = debounce((value: string) => {
    createVariable({ name: value, type: 2 }).then(() => {
      message.success(t('unitMatrixingDrawer_addSuccess'));
      setVariablePop(false);
      onGetVariableList();
    });
  }, 500);

  // 保存动态价格公示
  const onSave = debounce(() => {
    if (formulaName.length && isFunction) {
      onKeep(formulaName, formulaList.map((item: formulaListType) => item.value).join(''));
    } else {
      message.warning(
        !formulaName.length
          ? t('priceSettingDrawer_enterFormulaName')
          : t('priceSettingDrawer_formulaNotRunnable')
      );
    }
  }, 500);

  useEffect(() => {
    onGetVariableList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Drawer
      title={t('priceSettingDrawer_newFormula')}
      placement="right"
      visible={isShow}
      onClose={onclose}
      size="large"
      footer={
        <Drawer.Footer
          okText={t('public_save')}
          cancelText={t('public_cancel')}
          onOk={onSave}
          onCancel={onclose}
        />
      }
    >
      <div className={styles.boxCard}>
        <div className={styles.formulaNameBox}>
          <div className={styles.formulaName}>
            <div className={styles.required}>*</div>
            <div className={styles.formulaLabel}>{t('priceSettingDrawer_formulaName')}</div>
          </div>
          <Input
            className={styles.formulaValue}
            placeholder={t('priceSettingDrawer_enterFormulaName')}
            maxLength={30}
            bordered={false}
            onChange={(e) => setFormulaName(e.target.value)}
          />
        </div>
      </div>
      <div className={styles.boxCard}>
        <div className={styles.operateFormulaBox}>
          <div className={styles.operateFormula}>{t('priceSettingDrawer_formula')}</div>
          <div className={classNames(styles.redIcon, isFunction && styles.greenIcon)} />
          <div className={styles.isFeasible}>
            {isFunction ? t('priceSettingDrawer_runnable') : t('priceSettingDrawer_notRunnable')}
          </div>
        </div>
        <div>
          {formulaList.length > 0 ? (
            <div className={styles.formulaBox}>
              {formulaList.map((item, index: number) => (
                <div className={styles.formula} key={createUuid()}>
                  <div className={styles.tagInput} title={item.label}>
                    <Input
                      bordered={false}
                      value={item.label}
                      onFocus={() => selectFocusTag(index, false)}
                      onKeyDown={(e) => delFormulaTag(e, index)}
                    />
                  </div>
                  <div className={styles.nullInput}>
                    <Input bordered={false} onFocus={() => selectFocusTag(index, true)} />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.nullformula}>{t('priceSettingDrawer_clickToOperate')}</div>
          )}
        </div>
      </div>

      <div className={styles.boxCard}>
        <div className={styles.operateCharacter}>{t('priceSettingDrawer_operator')}</div>
        <div className={styles.fingerboardBox}>
          {fingerboard.map((item) => (
            <div
              className={styles.fingerboardTag}
              key={item.id}
              tabIndex={-1}
              role="button"
              onClick={() => addVariable({ id: item.id, label: item.label, value: item.value })}
            >
              {item.label}
            </div>
          ))}
        </div>
      </div>

      <div className={styles.boxCard}>
        <div className={styles.variableBox}>
          <div className={styles.variableTitle}>{t('priceSettingDrawer_variable')}</div>
          <div
            className={styles.addVariable}
            tabIndex={-1}
            role="button"
            onClick={() => setVariablePop(true)}
          >
            {t('addVariableModal_addVariable')}
          </div>
        </div>
        <div className={styles.variableTagBox}>
          {variableList.map((item) => (
            <div
              className={styles.variableTag}
              key={item.id}
              tabIndex={-1}
              role="button"
              onClick={() =>
                addVariable({
                  id: item.id,
                  label: item.name,
                  value: `#${item.id}#`,
                })
              }
            >
              {item.name}
            </div>
          ))}
        </div>
      </div>

      <AddVariableModal
        isShow={variablePop}
        closeDrawer={setVariablePop}
        onAddVariable={onAddVariable}
      />
    </Drawer>
  );
}

export default NewFormulaDrawer;
