/* eslint-disable @typescript-eslint/no-explicit-any */
import { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface dataType {
  valueType: number;
  attrbute: string;
}

interface CardType {
  id: number;
  dataObj: dataType;
  index: number;
  moveCard: MultipleParamsFn<[value: number, values: number]>;
}

function Card({ id, dataObj, index, moveCard }: CardType) {
  const ref = useRef<HTMLDivElement>(null);
  const { t } = useTranslation();
  const [{ handlerId }, drop] = useDrop({
    accept: 'card',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: any, monitor: any) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item?.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) {
        return;
      }

      const hoverBoundingRect = ref.current?.getBoundingClientRect();
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const clientOffset = monitor.getClientOffset();
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      moveCard(dragIndex, hoverIndex);
      // eslint-disable-next-line no-param-reassign
      item.index = hoverIndex;
    },
  });
  const [{ isDragging }, drag] = useDrag({
    type: 'card',
    item: () => ({ id, index }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  const opacity = isDragging ? 0 : 1;
  drag(drop(ref));
  return (
    <div className={styles.contentBox} ref={ref} style={{ opacity }} data-handler-id={handlerId}>
      <div className={styles.nameBox}>
        {dataObj?.valueType === 1 ? (
          <div className={styles.nameText}>{t('selectProductAttributeDrawer_text')}</div>
        ) : (
          <div className={styles.nameNumber}>{t('selectProductAttributeDrawer_number')}</div>
        )}
        <div className={styles.attrText}>{dataObj?.attrbute}</div>
      </div>
      <div className={styles.moveItem} />
    </div>
  );
}

export default Card;
