.contentBox {
  display: flex;
  min-height: 60px;
  margin-bottom: 20px;
  padding: 0 20px;
  justify-content: space-between;
  align-items: center;
  border-radius: 18px;
  background-color: #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);

  .nameBox {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 10px 0;

    .nameText {
      color: #05d380;
      font-size: 12px;
      font-weight: 500;
      width: 32px;
      height: 20px;
      line-height: 20px;
      margin-right: 2px;
      text-align: center;
      background-color: #d0efe2;
      border-radius: 2px;
    }

    .nameNumber {
      color: #ea1c26;
      font-size: 12px;
      font-weight: 500;
      width: auto;
      height: 20px;
      line-height: 20px;
      margin-right: 2px;
      text-align: center;
      background-color: #fcdddf;
      border-radius: 2px;
    }
  }

  .moveItem {
    cursor: pointer;
    display: inline-block;
    width: 30px;
    height: 18px;
    background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA8AAAAPCAYAAAA71pVKAAAAAXNSR0IArs4c6QAAAARzQklUCAgICHwIZIgAAADwSURBVCiRlZI9UsMwEIU/WYtGGlNQ4EFyRekLUXIX7kBBkSY3gI50OkbSQUdLSeF4KZKZQPyDUPm0b9/bfQszz7m2k5AydRPnapaIHxKSik/buQbVCKmbOFhdK3wBqMHLXtbQXP7LgYSkS/8n5eLZTg4qOM44yEsJ1YaLFa7tACrn2m6wmhVuSsgGjdZqxrWdDFZXQDTwKT4+nBefYwq3BqJU+ih91d/JXjKGa4X33ypTylyh7Hrb3x+Quoni02akOrFt8WkzsdxxjtNRFeZdnvOPzuLTqw3p7WAzZuvjEyBFiscdbP+67eUGIT0vEb8BnmI4373x1DQAAAAASUVORK5CYII=');
    background-repeat: no-repeat;
    background-position: center;
  }
}

.attrText {
  font-weight: 500;
  width: calc(100% - 65px);
  word-break: break-all;
}
