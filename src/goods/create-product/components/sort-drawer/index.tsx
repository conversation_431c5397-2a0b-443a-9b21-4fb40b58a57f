/* eslint-disable @typescript-eslint/no-explicit-any */
import { forwardRef, useCallback, useState, useImperativeHandle, useEffect } from 'react';
import update from 'immutability-helper';
import { useTranslation } from 'react-i18next';
import { Drawer } from '@/components';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { getStandardList } from '@/apis';
import { ResultType } from '@/apis/get-standard-list';
import Card from './card';

interface SortDrawerType {
  okSort: MultipleParamsFn<any>;
  categoryId: number;
  type: string;
}

const SortDrawer = forwardRef(({ categoryId, okSort, type }: SortDrawerType, ref: any) => {
  const [visible, setVisible] = useState(false);
  const [dataLsit, setDataLsit] = useState<ResultType[]>([]);
  const { t } = useTranslation();

  const saveSort = () => {
    const newDataLsit = dataLsit.map((item) => item.id);
    okSort(newDataLsit);
  };

  const moveCard = useCallback((dragIndex: number, hoverIndex: number) => {
    setDataLsit((prevCards: ResultType[]) =>
      update(prevCards, {
        $splice: [
          [dragIndex, 1],
          [hoverIndex, 0, prevCards[dragIndex]],
        ],
      })
    );
  }, []);

  const renderCard = useCallback(
    (item, index) => (
      <Card key={item.id} index={index} id={item.id} dataObj={item} moveCard={moveCard} />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  useImperativeHandle(
    ref,
    () => ({
      openDrawer() {
        setVisible(true);
      },
      closeDrawer() {
        setVisible(false);
      },
    }),
    []
  );

  useEffect(() => {
    if (visible && type) {
      getStandardList({ catId: categoryId, useWay: type === 'name' ? 1 : 2 }).then((res) => {
        setDataLsit(res.list);
      });
    }
  }, [categoryId, type, visible]);

  return (
    <Drawer
      title={t('sortDrawer_title')}
      placement="right"
      visible={visible}
      onClose={() => setVisible(false)}
      footer={
        <Drawer.Footer okText={t('sortDrawer_okText')} showCancel={false} onOk={() => saveSort()} />
      }
    >
      <DndProvider backend={HTML5Backend}>
        <div>{dataLsit.map((item, index) => renderCard(item, index))}</div>
      </DndProvider>
    </Drawer>
  );
});

export default SortDrawer;
