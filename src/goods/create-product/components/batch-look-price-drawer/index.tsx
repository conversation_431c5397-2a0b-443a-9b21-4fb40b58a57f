import { Dispatch, SetStateAction } from 'react';
import { Divider } from 'antd';
import { Drawer } from '@/components';
import { useTranslation } from 'react-i18next';
import styles from './index.module.less';

interface PriceDrawerType {
  isShow: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
}

function BatchLookPriceDrawer(props: PriceDrawerType) {
  const { isShow, closeDrawer } = props;
  const { t } = useTranslation();

  const list = [
    {
      id: '1',
      name: '300ML/淡黄色/HUMJH-7658300ML/淡黄色/HUMJH-7658/400ML/红色/淡黄色/HUMJH-7658/400ML',
      price: '200.00',
    },
    { id: '2', name: '400ML/红色/HUMJH-765811300ML/淡', price: '200.00' },
    { id: '3', name: '500ML/蓝色/HUMJH-765831110ML/淡', price: '200.00' },
    { id: '4', name: '600ML/白色/HUMJH-76582430ML/淡', price: '200.00' },
  ];

  const onOk = () => {};

  const onclose = () => {
    closeDrawer(false);
  };

  return (
    <Drawer
      title={t('batchLookPriceDrawer_title')}
      visible={isShow}
      placement="right"
      onClose={onclose}
      footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onOk} />}
    >
      <div className={styles.cardBox}>
        <div className={styles.cardTitle}>{t('batchLookPriceDrawer_batchSet')}</div>
        <div className={styles.batchBox}>
          <div>{t('batchLookPriceDrawer_formulaName')}</div>
          <div>{t('orange_juice_import_price_formula')}</div>
        </div>
        <div className={styles.batchBox}>
          <div>{t('batchLookPriceDrawer_formula')}</div>
          <div>{t('100_plus_base_price')}</div>
        </div>
        <div className={styles.batchBox}>
          <div>{t('specification_single_retail_price')}</div>
          <div className={styles.redPrice}>¥ 200.00</div>
        </div>
      </div>
      <div className={styles.cardBox}>
        <div className={styles.cardSingle}>{t('batchLookPriceDrawer_singleSet')}</div>
        {list.map((item, index) => (
          <div key={item.id}>
            <div className={styles.cardName}>{item.name}</div>
            <div className={styles.batchBox}>
              <div>{t('specification_single_retail_price')}</div>
              <div className={styles.redPrice}>¥ {item.price}</div>
            </div>
            {index === list.length - 1 ? null : <Divider className={styles.line} />}
          </div>
        ))}
      </div>
    </Drawer>
  );
}

export default BatchLookPriceDrawer;
