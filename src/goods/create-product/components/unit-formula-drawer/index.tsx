import { useCallback, useEffect, useRef, useState } from 'react';
import { DrawerProps, Radio, Select } from 'antd';
import type { RadioChangeEvent } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, Empty } from '@/components';
import { ResultType as UnitOptionType } from '@/apis/base/get-unit-options';
import { ResultType as UnitFormulaOptionType } from '@/apis/base/get-unit-formula-options';
import { testPerm } from '@/utils/permission';
import classNames from 'classnames';
import UnitMatrixingDrawer from '../unit-matrixing-drawer';
import SetVariableDrawer from '../set-variable-drawer';
import styles from './index.module.less';

interface PropsType extends DrawerProps {
  unit: string;
  unitOptions: UnitOptionType[];
  unitTemplateId: number;
  unitFormulaOptions: UnitFormulaOptionType[];
  templateFormulaArgsList: {
    id: number;
    name: string;
    value: string;
  }[];
  onConfirm: MultipleParamsFn<[value: UnitFormulaOptionType, values: any, unit: string]>;
  onGetUnitFormulaOptions: MultipleParamsFn<[value: string]>; // 新增后获取列表
  categoryId: number;
}

function UnitFormulaDrawer({
  unitOptions,
  visible,
  onConfirm,
  categoryId,
  unitTemplateId,
  unitFormulaOptions,
  templateFormulaArgsList,
  onGetUnitFormulaOptions,
  ...props
}: PropsType) {
  const { t } = useTranslation();
  const [unit, setUnit] = useState(props.unit);
  const [unitId, setUnitId] = useState(0);
  const [isUnitShow, setIsUnitShow] = useState(false);
  const [isVariableShow, setIsVariableShow] = useState(false);
  const [variableInfo, setVariableInfo] = useState({} as UnitFormulaOptionType);
  const [templateFormulaArgsData, setTemplateFormulaArgsData] = useState<
    {
      id: number;
      name: string;
      value: string;
    }[]
  >([]);
  const formula = useRef<UnitFormulaOptionType | null>(null);

  // 筛选符合基本单位的单位换算关系
  // const unitFormulaOptions = useMemo(() => {
  //   if (!unit || !props.unitFormulaOptions.length) return [];
  //   return props.unitFormulaOptions.filter((item) => item.unitName === unit);
  // }, [props.unitFormulaOptions, unit]);

  const onChange = useCallback(
    (e: RadioChangeEvent) => {
      const { value } = e.target;
      const option = unitFormulaOptions.filter((item) => item.id === value)[0];
      formula.current = option;
      setUnitId(value);
      setVariableInfo(option);
      setTemplateFormulaArgsData(
        option.varList?.map((item: any) => ({ id: item.id, value: '', name: item.name }))
      );
    },
    [unitFormulaOptions]
  );

  useEffect(() => {
    if (visible) {
      setUnit(props.unit);
      const option = unitFormulaOptions.filter((item) => item.id === unitTemplateId)[0];
      formula.current = option;
      setUnitId(unitTemplateId);
      setTemplateFormulaArgsData(templateFormulaArgsList?.map((item) => ({ ...item })));
      // if (templateFormulaArgsList.length) {
      //   setIsVariableShow(true);
      // }
      onGetUnitFormulaOptions(props.unit);
    } else {
      setIsUnitShow(false);
      setIsVariableShow(false);
    }
  }, [visible, unitTemplateId, templateFormulaArgsList]); // eslint-disable-line

  // useEffect(() => {
  //   setUnitId(unitTemplateId);
  //   if (templateFormulaArgsList.length) {
  //     setIsVariableShow(true);
  //   }
  // }, [unitTemplateId, templateFormulaArgsList]);

  return (
    <Drawer
      visible={visible}
      footer={
        <Drawer.Footer
          showCancel={false}
          okText={t('public_sure')}
          disabled={!unit || !unitId}
          onOk={() => {
            if (formula.current?.varList?.length) {
              setIsVariableShow(true);
              setVariableInfo({ ...formula.current } as UnitFormulaOptionType);
            } else {
              onConfirm(formula.current as UnitFormulaOptionType, [], unit);
            }
          }}
        />
      }
      extra={
        <span
          className={styles.extra}
          role="button"
          tabIndex={0}
          onClick={() => {
            if (!testPerm('L_001_004_002')) {
              return;
            }
            setIsUnitShow(true);
          }}
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            width: '61px',
            display: 'inline-block',
          }}
          title={t('unitFormulaDrawer_addConversion')}
        >
          {t('unitFormulaDrawer_addConversion')}
        </span>
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <div className={classNames(styles.message, 'mt-2', 'mb-3')}>
        {t('unitFormulaDrawer_selectUnitMessage')}
      </div>
      <div className={classNames(styles.card, styles.unitCell)}>
        <span className={styles.unitLabel}>{t('unitFormulaDrawer_baseUnit')}</span>
        <Select
          allowClear
          showSearch
          bordered={false}
          placeholder={t('unitFormulaDrawer_selectBaseUnit')}
          className={styles.select}
          value={unit}
          options={unitOptions}
          filterOption={(input, option) =>
            (option?.unitName ?? '').toLowerCase().includes(input.toLowerCase())
          }
          fieldNames={{
            label: 'unitName',
            value: 'unitName',
          }}
          onChange={(value) => {
            setUnit(value);
            setUnitId(0);
            formula.current = null;
            onGetUnitFormulaOptions(value);
          }}
        />
      </div>

      {!!unit && (
        <div className={classNames(styles.card, 'py-5')}>
          <div className={styles.message}>{t('unitFormulaDrawer_selectConversion')}</div>
          <Radio.Group onChange={onChange} value={unitId}>
            {unitFormulaOptions.map((option) => (
              <Radio value={option.id} className={classNames('mt-4', styles.radio)} key={option.id}>
                {option.templateName}
              </Radio>
            ))}
          </Radio.Group>
          {!unitFormulaOptions.length && (
            <Empty
              style={{ marginBottom: '20px' }}
              message={t('unitFormulaDrawer_noConversion')}
              description={t('unitFormulaDrawer_tryAnotherUnit')}
            />
          )}
        </div>
      )}
      {isUnitShow ? (
        <UnitMatrixingDrawer
          isShow={isUnitShow}
          drawerType="bottom"
          closeDrawer={setIsUnitShow}
          unitOptions={unitOptions}
          unit={unit}
          onGetUnitFormulaOptions={onGetUnitFormulaOptions}
          categoryId={categoryId}
        />
      ) : null}
      <SetVariableDrawer
        drawerType="bottom"
        visible={isVariableShow}
        defaultValues={templateFormulaArgsData}
        closeDrawer={setIsVariableShow}
        dataInfo={{ ...variableInfo }}
        onConfirm={(dataInfo, variableList) => {
          onConfirm(dataInfo, variableList, unit);
        }}
      />
    </Drawer>
  );
}

UnitFormulaDrawer.displayName = 'UnitFormulaDrawer';

UnitFormulaDrawer.defaultProps = {};

export default UnitFormulaDrawer;
