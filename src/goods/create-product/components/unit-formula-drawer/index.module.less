.message {
  color: #888b98;
  font-size: 14px;
  line-height: 22px;
}

.card {
  margin-bottom: 20px;
  padding: 0 20px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  background: #fff;
  border-radius: 18px;
}

.unitCell {
  display: flex;
  height: 54px;
  align-items: center;
}

.unitLabel {
  color: #040919;
  font-size: 14px;
  width: 76px;
}

.select {
  width: 0;
  flex: 1;
  text-align: right;

  :global {
    .ant-select-selector {
      border: none !important;
      padding: 0 !important;
    }

    .ant-select-selection-item {
      line-height: 32px !important;
    }

    .ant-select-arrow,
    .ant-select-clear {
      right: 3px !important;
    }
  }
}

.radio {
  display: block;
  line-height: 1.3;
  margin-right: 0;
}

.emptyText {
  text-align: center;
}

.extra {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}
