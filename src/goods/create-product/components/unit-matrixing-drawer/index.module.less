.dynamic_form_complex {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  padding-top: 2px;
}

.iconHand {
  cursor: pointer;
  margin-bottom: 2px;
}

.formBox {
  display: flex;
  margin-top: 10px;
  position: relative;
  flex-wrap: wrap;
}

.equalIcon {
  margin-top: 7px;
}

.removeIcon {
  color: #fff;
  width: 16px;
  height: 16px;
  line-height: 16px;
  position: absolute;
  top: -6px;
  right: 4px;
  background-color: #b5b5b5;
  opacity: 0.7;
  text-align: center;
  border-radius: 50%;
  cursor: pointer;
}

.formDrawer {
  :global {
    div.ant-drawer-content-wrapper {
      .ant-drawer-wrapper-body {
        .ant-drawer-body {
          padding: 0 14px;
        }
      }
    }

    .ant-input-affix-wrapper {
      padding: 6px;
    }

    .ant-form-item {
      margin-bottom: 0;
    }

    .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
      padding: 0 6px;
    }

    .ant-select-arrow {
      right: 8px;
    }

    .ant-input-prefix > *:not(:last-child),
    .ant-input-suffix > *:not(:last-child) {
      margin-right: 0;
    }
  }
}

:global {
  .ant-drawer-content {
    background-color: rgb(245 246 250 / 100%);
  }
}

// .fieldsItem {
//   display: flex;
//   flex-wrap: wrap;
// }
