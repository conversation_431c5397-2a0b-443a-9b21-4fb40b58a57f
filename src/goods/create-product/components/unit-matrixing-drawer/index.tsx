/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dispatch, SetStateAction, useState, useEffect } from 'react';
import { Drawer, Icon } from '@/components';
import debounce from 'lodash/debounce';
import { ResultType as UnitOptionType } from '@/apis/base/get-unit-options';
import { Form, Input, Select, Space, message } from 'antd';
import { addUnitConversion } from '@/apis';
import { useTranslation } from 'react-i18next';
import PriceSettingDrawer from '../price-setting-drawer';
import fxSvg from '../../../assets/svg/fx.svg';
import styles from './index.module.less';

interface UnitMatrixingType {
  isShow: boolean;
  drawerType: string;
  unit: string;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  unitOptions: UnitOptionType[];
  onGetUnitFormulaOptions: MultipleParamsFn<[value: string]>;
  categoryId: number;
}

interface formListType {
  mainUum: string;
  mainUnit: string;
  type?: number;
  mainUumId?: number | string;
}

function UnitMatrixingDrawer({
  isShow,
  drawerType,
  unit,
  closeDrawer,
  unitOptions,
  onGetUnitFormulaOptions,
  categoryId,
}: UnitMatrixingType) {
  const { t } = useTranslation();
  const [form] = Form.useForm();

  const [isPriceShow, setIsPriceShow] = useState(false);
  const [mainUumIndex, setMainUumIndex] = useState(0);
  const [dataList, setDataList] = useState<any>();

  const onFinish = debounce(() => {
    const formInfo = form.getFieldsValue();
    const formList = formInfo.mainBox;
    const isError = formList.filter((item: any) => !item).length > 0;
    if (isError) {
      message.error(t('unitMatrixingDrawer_fillConversionTemplate'));
      return;
    }
    const other = formList.map((item: formListType) => ({
      num: item?.mainUumId ? item?.mainUumId : item?.mainUum,
      unitName: item?.mainUnit,
      type: item.type ? 1 : 0,
      ...(item?.mainUumId ? { formatFormula: item?.mainUum } : {}),
    }));
    const formItem = formList[0];

    if (
      formItem?.mainUnit &&
      (formItem?.mainUumId || formItem?.mainUum) &&
      !other?.filter((item: any) => !item.num || !item.unitName).length
    ) {
      addUnitConversion({
        mainUnit: formItem?.mainUnit,
        mainUum: formItem?.mainUumId ? Number(formItem?.mainUumId) : Number(formItem?.mainUum),
        other,
      }).then(() => {
        message.success(t('unitMatrixingDrawer_addSuccess'));
        closeDrawer(false);
        onGetUnitFormulaOptions(unit);
      });
    } else {
      message.error(t('unitMatrixingDrawer_fillConversionTemplate'));
    }
  }, 500);

  const onSelect = (value: any, index: number) => {
    const fields = form.getFieldsValue();
    const { mainBox } = fields;

    setDataList(mainBox);
    mainBox[index] = {};
    Object.assign(mainBox[index], { mainUum: value.formatFormula, mainUumId: value.id, type: 1 });
    form.setFieldsValue({ mainBox });

    setIsPriceShow(false);
  };

  const onClose = () => {
    closeDrawer(false);
  };

  const onOpenPriceSet = (value: number) => {
    setIsPriceShow(true);
    setMainUumIndex(value);
  };

  const changeForm = () => {
    const formInfo = form.getFieldsValue();
    setDataList(formInfo?.mainBox);
  };

  useEffect(() => {
    if (!isShow) {
      form.resetFields();
      setDataList(undefined);
    }
  }, [isShow, form]);

  const createWidthSize = (length: number) => {
    let width = 16 * length;
    if (width > 345) {
      width = 345;
    }
    if (width < 75) {
      width = 75;
    }
    return width;
  };

  const contentElement = (
    <Form
      form={form}
      name="dynamic_form_complex"
      autoComplete="off"
      layout="horizontal"
      onValuesChange={changeForm}
    >
      <Form.List name="mainBox" initialValue={[{}, {}]}>
        {(fields, { add, remove }) => (
          <>
            {fields.map((field, index) => (
              <Space key={field.key} align="baseline" size={3} wrap className={styles.fieldsItem}>
                <div className={styles.formBox}>
                  <Form.Item {...field} name={[field.name, 'mainUum']} required>
                    <Input
                      title={dataList?.[index]?.mainUum}
                      style={{
                        width: createWidthSize(dataList?.[index]?.mainUum?.length || 0),
                        minWidth: 75,
                        height: 32,
                        marginLeft: '2px',
                      }}
                      placeholder={t('unitMatrixingDrawer_value')}
                      allowClear={dataList?.[index]?.mainUum && dataList?.[index]?.type}
                      suffix={
                        <div>
                          {dataList?.[index]?.mainUum && dataList?.[index]?.type ? null : (
                            <div tabIndex={-1} role="button" onClick={() => onOpenPriceSet(index)}>
                              <img className={styles.iconHand} src={fxSvg} alt="" />
                            </div>
                          )}
                        </div>
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    noStyle
                    shouldUpdate={(prevValues, curValues) =>
                      prevValues.mainUum !== curValues.mainUum ||
                      prevValues.mainUnit !== curValues.mainUnit
                    }
                  >
                    {() => (
                      <Form.Item {...field} name={[field.name, 'mainUnit']} required>
                        <Select
                          style={{ minWidth: 75, marginLeft: '2px', marginRight: '2px' }}
                          placeholder={t('unitMatrixingDrawer_unit')}
                          showSearch
                          options={unitOptions}
                          filterOption={(input, option) =>
                            (option?.unitName ?? '').toLowerCase().includes(input.toLowerCase())
                          }
                          fieldNames={{
                            label: 'unitName',
                            value: 'unitName',
                          }}
                        />
                      </Form.Item>
                    )}
                  </Form.Item>
                  {fields.length !== index + 1 ? (
                    <div className={styles.equalIcon}>=</div>
                  ) : (
                    <div className={styles.equalIcon}>&nbsp;</div>
                  )}
                  {index !== 0 && index !== 1 ? (
                    <div
                      role="button"
                      tabIndex={-1}
                      onClick={() => remove(field.name)}
                      className={styles.removeIcon}
                    >
                      x
                    </div>
                  ) : null}
                </div>
              </Space>
            ))}
            <Icon
              name="plus-outline"
              size={22}
              color="#008cff"
              onClick={() => add()}
              className={styles.iconHand}
            />
          </>
        )}
      </Form.List>
    </Form>
  );

  return (
    <div>
      {drawerType === 'default' ? (
        <Drawer
          title={t('unitMatrixingDrawer_newConversionTemplate')}
          visible={isShow}
          placement="right"
          onClose={onClose}
          footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onFinish} />}
        >
          {contentElement}
        </Drawer>
      ) : (
        <Drawer
          title={t('unitMatrixingDrawer_newConversionTemplate')}
          placement="bottom"
          visible={isShow}
          closable={false}
          getContainer={false}
          maskStyle={{ background: 'rgba(0, 0, 0, .45)' }}
          extra={
            <div>
              <Icon
                name="close"
                size={22}
                role="button"
                tabIndex={-1}
                onClick={onClose}
                className={styles.iconHand}
              />
            </div>
          }
          footer={<Drawer.Footer okText={t('public_confirm')} showCancel={false} onOk={onFinish} />}
          className={styles.formDrawer}
        >
          {contentElement}
        </Drawer>
      )}

      {isPriceShow ? (
        <PriceSettingDrawer
          defaultId={0}
          isShow={isPriceShow}
          closeDrawer={setIsPriceShow}
          mainUumIndex={mainUumIndex}
          onSelect={onSelect}
          categoryId={categoryId}
          onClose={() => {}}
        />
      ) : null}
    </div>
  );
}

export default UnitMatrixingDrawer;
