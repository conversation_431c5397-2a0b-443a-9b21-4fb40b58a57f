/* eslint-disable @typescript-eslint/no-explicit-any */
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Spin, message } from 'antd';
import InfiniteScroll from 'react-infinite-scroll-component';
import debounce from 'lodash/debounce';
import { Drawer, Search, Empty } from '@/components';
import classNames from 'classnames';
import { addPriceFormula, getPriceFormulaList, getFormulaList } from '@/apis';
import { PriceFormulaType } from '@/apis/base/get-price-formula-list';
import { testPerm } from '@/utils/permission';
import { useTranslation } from 'react-i18next';

import NewFormulaDrawer from '../new-formula-drawer';
import styles from './index.module.less';

interface PriceSettingDrawerType {
  isShow: boolean;
  closeDrawer: Dispatch<SetStateAction<boolean>>;
  mainUumIndex?: number;
  onSelect: MultipleParamsFn<[val: PriceFormulaType, index: number]>;
  categoryId: number;
  defaultId: number;
  onClose: MultipleParamsFn<[val: number]>;
  List?: {
    standardList1: any;
  };
}

const lang = localStorage.getItem('lang');

function PriceSettingDrawer({
  isShow,
  closeDrawer,
  onSelect,
  mainUumIndex = 0,
  categoryId,
  defaultId,
  onClose,
  List,
}: PriceSettingDrawerType) {
  const { t } = useTranslation();
  const [load, setLoad] = useState(false);
  const [pageCount, setPageCount] = useState(1);
  const [page, setPage] = useState(1);
  const [searchName, setSearchName] = useState('');
  const [formulaList, setFormulaList] = useState<PriceFormulaType[]>([]);
  const [isNewFormula, setIsNewFormula] = useState(false);
  const [formulaInfo, setFormulaInfo] = useState<PriceFormulaType>({ id: 0 } as PriceFormulaType);

  const initSelectedFormula = (list: PriceFormulaType[]) => {
    if (defaultId) {
      const selectedItems = list?.filter((item) => item.id === defaultId);
      if (selectedItems.length) {
        setFormulaInfo({ ...selectedItems[0] });
      }
    } else {
      setFormulaInfo({ id: 0 } as PriceFormulaType);
    }
  };

  // 页面初始化公式列表数据
  const initialPageList = () => {
    getFormulaList({
      pageNo: 1,
      pageSize: 10,
      orderByFieldId: defaultId,
      standardNameList: List ? List.standardList1?.map((item: any) => item.name) || [] : undefined,
    }).then((res) => {
      setPageCount(res.pagination.total);
      initSelectedFormula(res.list || []);
      setFormulaList(res.list || []);
    });
  };

  const onGetFormulaList = () => {
    setLoad(true);
    getPriceFormulaList({
      pageNo: page + 1,
      pageSize: 10,
      searchKey: searchName,
      orderByFieldId: defaultId,
    }).then((res) => {
      setLoad(false);
      setPageCount(res.pagination.total);
      setPage(page + 1);
      setFormulaList((val) => val?.concat(res.list));
      initSelectedFormula(res.list || []);
    });
  };

  // 搜索帅选
  const onSearchName = debounce((value: string) => {
    const par = {
      pageNo: 1,
      pageSize: 10,
      searchKey: value,
      orderByFieldId: defaultId,
    };
    setSearchName(value);
    setLoad(true);
    getPriceFormulaList(par).then((res) => {
      setLoad(false);
      setPageCount(res.pagination.total);
      setPage(2);
      setFormulaList(res.list || []);
      initSelectedFormula(res.list || []);
    });
  }, 500);

  // 下拉加载数据
  const loadMore = () => {
    onGetFormulaList();
  };

  // 点击选中动态价格公式
  const onSelectFormula = (value: any) => {
    setFormulaInfo(value);
  };

  // 点击保存
  const onSelectformula = () => {
    if (formulaInfo.id) {
      onSelect(formulaInfo, mainUumIndex);
    } else {
      message.error(t('priceSettingDrawer_selectFormula'));
    }
  };

  // 保存新建动态价格公式
  const onKeep = (name: string, formula: string) => {
    addPriceFormula({ name, formula, isDefault: 0 }).then(() => {
      message.success(t('priceSettingDrawer_createSuccess'));
      setIsNewFormula(false);
      initialPageList();
    });
  };

  // 关闭弹窗
  const onclose = () => {
    onClose(formulaInfo.id);
    closeDrawer(false);
    setFormulaList([]);
    setFormulaInfo({ id: 0 } as PriceFormulaType);
  };

  useEffect(() => {
    if (isShow) {
      initialPageList();
    } else {
      setPage(1);
      setPageCount(1);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isShow]);

  return (
    <div>
      <Drawer
        title={t('priceSettingDrawer_title')}
        placement="right"
        visible={isShow}
        onClose={onclose}
        widthSize={lang === 'zh' ? 375 : 600}
        extra={
          <div
            className={styles.builtFormula}
            tabIndex={-1}
            role="button"
            onClick={() => {
              if (!testPerm('L_001_003_002')) {
                return;
              }
              setIsNewFormula(true);
            }}
            style={{
              width: lang === 'zh' ? '80px' : '150px',
              whiteSpace: 'nowrap' /* 防止文本换行 */,
              overflow: 'hidden' /* 隐藏超出区域的文本 */,
              textOverflow: 'ellipsis' /* 使用省略号显示超出的部分 */,
            }}
            title={t('priceSettingDrawer_newFormula')}
          >
            {t('priceSettingDrawer_newFormula')}
          </div>
        }
        footer={
          <Drawer.Footer
            okText={t('public_confirm')}
            showCancel={false}
            onOk={() => onSelectformula()}
          />
        }
      >
        <Search
          className={styles.searchInput}
          placeholder={t('priceSettingDrawer_searchPlaceholder')}
          onSearch={(val) => {
            onSearchName(val);
          }}
        />
        <div id="formulaList" className={styles.formulaListBox}>
          <InfiniteScroll
            dataLength={formulaList.length}
            hasMore={page <= pageCount}
            loader={
              load ? (
                <div className="text-center">
                  <Spin tip={t('priceSettingDrawer_loading')} />
                </div>
              ) : undefined
            }
            next={loadMore}
            scrollableTarget="formulaList"
          >
            {formulaList.map((item) => (
              <div
                className={classNames(
                  styles.formulaCard,
                  item.id === formulaInfo?.id && styles.selectFormulaCard
                )}
                key={item.id}
                tabIndex={-1}
                role="button"
                onClick={() => onSelectFormula(item)}
              >
                <div>
                  <div className={styles.formulaName}>
                    {t('priceSettingDrawer_formulaName')}: {item.name}
                  </div>
                  <div className={styles.formulaText}>
                    {t('priceSettingDrawer_formula')}: {item.formatFormula}
                  </div>
                  <div className={styles.formulaText}>
                    {t('priceSettingDrawer_variable')}:{' '}
                    {item.varList?.map((every) => every.name).join('，')}
                  </div>
                </div>
                <div className={styles.selectPhoto}>
                  <img
                    src="https://img.huahuabiz.com/user_files/202347/1680837620099350.png"
                    alt=""
                  />
                </div>
              </div>
            ))}
          </InfiniteScroll>
          {formulaList.length === 0 ? (
            <Empty
              style={{ position: 'relative', top: '26%' }}
              message={t('priceSettingDrawer_noContent')}
              description={t('priceSettingDrawer_tryAnother')}
            />
          ) : null}
        </div>
      </Drawer>
      {isNewFormula ? (
        <NewFormulaDrawer
          isShow={isNewFormula}
          closeDrawer={setIsNewFormula}
          categoryId={categoryId}
          onKeep={onKeep}
        />
      ) : null}
    </div>
  );
}

PriceSettingDrawer.defaultProps = {
  mainUumIndex: undefined,
  List: {
    standardList1: [],
  },
};

export default PriceSettingDrawer;
