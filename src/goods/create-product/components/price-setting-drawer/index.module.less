.builtFormula {
  color: #008cff;
  font-size: 16px;
  cursor: pointer;
}

.formulaList {
  flex: 1;
  width: 100%;
  height: 100%;
  overflow: auto;

  :global {
    .infinite-scroll-component {
      overflow-x: hidden !important;
    }
  }
}

.searchInput {
  margin-top: 2px;
}

.formulaListBox {
  height: calc(100% - 8%);
  margin-top: 20px;
  overflow: auto;
}

.formulaCard {
  display: flex;
  margin-bottom: 20px;
  padding: 20px 20px 12px;
  align-items: center;
  background-color: #fff;
  border-radius: 18px;
  border: 1px solid #fff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  cursor: pointer;

  .formulaName {
    font-size: 16px;
    font-weight: bolder;
    margin-bottom: 8px;
  }

  .formulaText {
    color: #888b98;
    word-break: break-all;
    margin-bottom: 8px;
  }

  .selectPhoto {
    display: none;
  }
}

.selectFormulaCard {
  display: flex;
  border: 1px solid #008cff;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  position: relative;
  cursor: pointer;

  .formulaName {
    font-size: 16px;
    font-weight: bolder;
    margin-bottom: 8px;
  }

  .formulaText {
    color: #888b98;
    word-break: break-all;
    margin-bottom: 8px;
  }

  .selectPhoto {
    display: block;
    width: 33px;
    height: 31px;
    position: absolute;
    top: 0;
    right: 0;
  }
}
