import { useEffect, useState } from 'react';
import { Switch, Radio, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { Context } from '@/components';
import { getCompanyConfig, updateCompanyConfig } from '@/apis';
import type { GetPmsCompanyConfig } from '@/apis';
import styles from './index.module.less';

function SettingsProductShow() {
  const { t } = useTranslation();
  const [setUpInfo, setSetUpInfo] = useState<GetPmsCompanyConfig>({
    id: null,
    isNameJoinBrand: 1,
    orderByType: 4,
    isShareSkuAutoUp: 0,
  });

  useEffect(() => {
    getCompanyConfig().then((res) => {
      setSetUpInfo({
        id: res.id,
        isNameJoinBrand: res.id ? res.isNameJoinBrand : 1,
        orderByType: res.orderByType || 4,
        isShareSkuAutoUp: res.isShareSkuAutoUp,
      });
    });
  }, []);

  return (
    <Context
      container
      theme="default"
      head={
        <Context.Head
          title={[
            {
              title: t('service_product_management'),
              to: '/goods/manage/index',
            },
            t('service_settings'),
          ]}
        />
      }
    >
      <div className={styles.setUpPage}>
        <div className={styles.oneBox}>
          <div className={styles.setUpTitle}>{t('service_display_settings')}</div>
          <div className={styles.flexBox}>
            <div className={styles.flexLabel}>
              {t('service_show_brand_name_before_product_name')}:
            </div>
            <div>
              <Switch
                checked={Boolean(setUpInfo.isNameJoinBrand)}
                onChange={(val) => {
                  updateCompanyConfig({
                    ...setUpInfo,
                    isNameJoinBrand: Number(val),
                  }).then((res) => {
                    setSetUpInfo({ ...setUpInfo, isNameJoinBrand: Number(val), id: res.id });
                    message.success(t('service_edit_success'));
                  });
                }}
              />
              <div className={styles.describe}>{t('service_brand_name_display_description')}</div>
            </div>
          </div>
          <div className={styles.flexBox}>
            <div className={styles.flexLabel}>{t('service_product_management_list_sorting')}:</div>
            <Radio.Group
              value={setUpInfo.orderByType}
              onChange={(val) => {
                updateCompanyConfig({
                  ...setUpInfo,
                  orderByType: val.target.value,
                }).then((res) => {
                  setSetUpInfo({ ...setUpInfo, orderByType: val.target.value, id: res.id });
                  message.success(t('service_edit_success'));
                });
              }}
            >
              <Radio value={1}>{t('service_sort_by_creation_time_asc')}</Radio>
              <Radio value={2}>{t('service_sort_by_creation_time_desc')}</Radio>
              <br />
              <div className={styles.nullBox} />
              <Radio value={3}>{t('service_sort_by_update_time_asc')}</Radio>
              <Radio value={4}>{t('service_sort_by_update_time_desc')}</Radio>
            </Radio.Group>
          </div>
        </div>
        <div className={styles.twoBox}>
          <div className={styles.setUpTitle}>{t('service_shared_product_settings')}</div>
          <div className={styles.flexBox}>
            <div className={styles.flexLabel}>{t('service_auto_list_shared_products')}:</div>
            <div>
              <Switch
                checked={Boolean(setUpInfo.isShareSkuAutoUp)}
                onChange={(val) => {
                  updateCompanyConfig({
                    ...setUpInfo,
                    isShareSkuAutoUp: Number(val),
                  }).then((res) => {
                    setSetUpInfo({ ...setUpInfo, isShareSkuAutoUp: Number(val), id: res.id });
                    message.success(t('service_edit_success'));
                  });
                }}
              />
              <div className={styles.describe}>
                {t('service_auto_list_shared_products_description')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </Context>
  );
}

export default SettingsProductShow;
