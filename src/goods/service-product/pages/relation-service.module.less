.body {
  width: 100%;
  height: 100%;
  padding: 20px 20px 0;
}

.header {
  display: flex;
  height: 38px;
  padding-left: 20px;
  border-radius: 8px;
  background: #fafafc;
  align-items: center;
}

.titles {
  display: flex;
  align-items: center;
  flex-grow: 1;
  justify-content: space-between;
}

.list {
  width: 100%;
  height: calc(100% - 90px);
  overflow-y: auto;

  :global {
    .ant-checkbox-wrapper-checked {
      background-color: rgb(217 238 255 / 50%);
    }
  }
}

.item {
  display: flex;
  height: 96px;
  padding-left: 20px;
  justify-content: space-between;
  align-items: center;
  border-radius: 10px;

  &:hover {
    background-color: rgb(217 238 255 / 30%);
  }
}

.itemList {
  display: flex;
  align-items: center;
  height: 96px;
}

.info {
  display: flex;
  width: 300px;
  padding-left: 8px;
  cursor: pointer;
}

.service {
  width: 200px;
  text-align: center;
}

.serviceName {
  max-width: 130px !important;
}

.handle {
  width: 68px;
  text-align: center;
}

.title {
  display: flex;
  width: 220px;
  padding-left: 8px;
  justify-content: space-between;
  flex-direction: column;
}

.standard {
  color: #888b98;
  font-size: 12px;
}

.tag {
  width: 28px;
  height: 15px;
  vertical-align: text-top;
  margin-right: 4px;
}

.image {
  width: 64px;
  height: 64px;
  object-fit: contain;
  border-radius: 12px;
}

.serviceItem + .serviceItem {
  margin-top: 4px;
}

.noService {
  color: #888b98;
}

.serviceTotal {
  color: #888b98;
  margin-left: 4px;
}

.moreService {
  color: #888b98;
  font-size: 12px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.imageWrap {
  overflow: hidden;
  position: relative;

  &::before {
    color: #fff;
    font-size: 12px;
    display: flex;
    width: 100%;
    height: 30%;
    padding: 6px 2px 2px;
    justify-content: center;
    position: absolute;
    top: 4%;
    left: -30%;
    transform: scale(0.8) rotate(-45deg);
    align-items: center;
  }
}

.sale::before {
  content: '出售中';
  background: linear-gradient(119.47deg, rgb(50 209 108 / 85%), rgb(17 212 173 / 85%));
}

.stay::before {
  content: '待上架';
  background: linear-gradient(118.96deg, rgb(242 143 44 / 85%), rgb(250 205 57 / 85%));
}

.mystay {
  background: linear-gradient(118.96deg, rgb(242 143 44 / 85%), rgb(250 205 57 / 85%));
  color: #fff;
  font-size: 12px;
  display: flex;
  width: 100%;
  height: 30%;
  padding: 6px 2px 2px;
  justify-content: center;
  position: absolute;
  top: 4%;
  left: -30%;
  transform: scale(0.8) rotate(-45deg);
  align-items: center;
}

.mysale {
  background: linear-gradient(119.47deg, rgb(50 209 108 / 85%), rgb(17 212 173 / 85%));
  color: #fff;
  font-size: 12px;
  display: flex;
  width: 100%;
  height: 30%;
  padding: 6px 2px 2px;
  justify-content: center;
  position: absolute;
  top: 4%;
  left: -30%;
  transform: scale(0.8) rotate(-45deg);
  align-items: center;
}

.footer {
  display: flex;
  height: 48px;
  padding: 10px 0 10px 20px;
  justify-content: space-between;
  align-items: center;
}

.checkedCount {
  font-size: 14px;
  margin: 0 12px;
}

.linkBtn {
  cursor: pointer;
  color: #008cff;
  font-size: 14px;
}

.empty {
  padding-top: 150px;
}
