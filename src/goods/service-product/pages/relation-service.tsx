import { useState, useCallback, useReducer, useMemo, useRef } from 'react';
import { message, Pagination, Checkbox, Dropdown, Menu, Typography } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';
import { useMount, useDebounceFn } from 'ahooks';
import cloneDeep from 'lodash/cloneDeep';
import uniq from 'lodash/uniq';
import { useTranslation } from 'react-i18next';
import i18n from 'i18next';
import { Context, Empty, Icon } from '@/components';
import { usePermission } from '@/hooks';
import { filterServiceProduct, updateGoodsService, batchAddGoodsService } from '@/apis';
import type {
  SkuGoodsListParams,
  GoodsListInfo,
  RelationCustomizeType,
} from '@/apis/sku-goods-list';
import classNames from 'classnames';
import EditDrawer from '../components/edit-drawer';
import RelationDrawer from '../components/relation-drawer';
import FilterDrawer from '../components/filter-drawer';
import styles from './relation-service.module.less';
import shareTag from '../images/share-tag.png';

interface StateType {
  list: GoodsListInfo[];
  total: number;
  pageSize: number;
  pageNo: number;
  status: 50 | 90 | '';
  keyWord: string;
  isServiceProduct: 1 | 2 | '';
  customizeCategoryIdSet: number[];
  standardList: {
    name: string;
    value: string[];
  }[];
  filterDrawer: boolean;
  editDrawer: boolean;
  relationDrawer: boolean;
  drawerIds: number[];
  defaultServices: RelationCustomizeType[];
  editRelation: boolean;
  handleBatchType: string;
  onlyRead: boolean;
  checkedList: CheckboxValueType[];
  indeterminate: boolean;
  checkAll: boolean;
}
type ActionType = {
  type: 'setState';
  payload: Partial<StateType>;
};

const initialState: StateType = {
  list: [],
  total: 1,
  pageSize: 10,
  pageNo: 1,
  status: '',
  keyWord: '',
  isServiceProduct: '',
  customizeCategoryIdSet: [],
  standardList: [],
  filterDrawer: false,
  editDrawer: false,
  relationDrawer: false,
  drawerIds: [],
  defaultServices: [],
  editRelation: false,
  handleBatchType: '',
  onlyRead: false,
  checkedList: [],
  indeterminate: false,
  checkAll: false,
};
const reducer = (state: StateType, action: ActionType): StateType => {
  switch (action.type) {
    case 'setState':
      return { ...state, ...action.payload };
    default:
      return state;
  }
};

const { Head } = Context;
const { Paragraph, Text } = Typography;
const CheckboxGroup = Checkbox.Group;

const titles = [
  { title: i18n.t('service_product_management'), to: '/goods/manage/index' },
  i18n.t('service_related_services2'),
];
const menuItems = [
  { label: i18n.t('service_edit'), key: 'edit' },
  { label: i18n.t('service_related_services'), key: 'relation' },
];
const batchItems = [
  { label: i18n.t('service_batch_add'), key: 'add' },
  { label: i18n.t('service_batch_replace'), key: 'replace' },
];

function GoodsRelationService() {
  const { t } = useTranslation();
  const editDrawerEl = useRef<any>(null);
  const [loading, setLoading] = useState(false);
  const [state, dispatch] = useReducer(reducer, initialState);
  const isFilterActive = useMemo(
    () =>
      !!(
        state.standardList.length ||
        state.customizeCategoryIdSet.length ||
        state.status ||
        state.isServiceProduct
      ),
    [
      state.customizeCategoryIdSet.length,
      state.isServiceProduct,
      state.standardList.length,
      state.status,
    ]
  );

  // 获取数据
  const getList = useCallback((param: SkuGoodsListParams = {}) => {
    setLoading(true);
    filterServiceProduct({
      type: 4,
      sort: 'desc',
      ...param,
    })
      .then((res) => {
        dispatch({
          type: 'setState',
          payload: {
            list: res.list,
            total: res.total,
            checkedList: [],
            indeterminate: false,
            checkAll: false,
            ...param,
          },
        });
      })
      .finally(() => {
        setLoading(false);
      });
  }, []);

  // 选中
  const onCheckedList = useCallback(
    (ids: CheckboxValueType[]) => {
      dispatch({
        type: 'setState',
        payload: {
          checkedList: ids,
          indeterminate: !!ids.length && ids.length < state.list.length,
          checkAll: ids.length === state.list.length,
        },
      });
    },
    [state.list.length]
  );

  // 全选
  const onCheckAll = useCallback(
    (e: CheckboxChangeEvent) => {
      const { checked } = e.target;
      dispatch({
        type: 'setState',
        payload: {
          checkedList: checked ? state.list.map((item) => item.id) : [],
          indeterminate: false,
          checkAll: checked,
        },
      });
    },
    [state.list]
  );

  // 前往商品详情
  const toDetail = useCallback(({ id, skuSource }) => {
    window.open(`/shop/goods/v1/detail?skuId=${id}&type=${skuSource}&shop=true`);
  }, []);

  // 关联服务
  const onRelation = useCallback(
    (relationShopSkuIdSet: number[], services: RelationCustomizeType[]) => {
      const api = state.handleBatchType === 'add' ? batchAddGoodsService : updateGoodsService;
      api({
        shopSkuIds: state.drawerIds,
        relationShopSkuIdSet: uniq(relationShopSkuIdSet),
      }).then(() => {
        message.success(t('service_goods_related_success'));
        const payload: any = {
          relationDrawer: false,
          defaultServices: [],
        };
        if (state.handleBatchType === 'add') {
          payload.list = state.list.map((item) => {
            const nowItem = item;
            if (state.drawerIds.includes(item.id)) {
              if (item.relationCustomizeVOList.length) {
                const relationCustomizeObj: any = {};
                services.forEach((service) => {
                  relationCustomizeObj[service.customizeCatId] = service;
                });
                item.relationCustomizeVOList.forEach((relationCustomize) => {
                  if (relationCustomizeObj[relationCustomize.customizeCatId]) {
                    const nowRelationCustomize = relationCustomize;
                    nowRelationCustomize.shopSkuIdList = uniq([
                      ...relationCustomize.shopSkuIdList,
                      ...relationCustomizeObj[relationCustomize.customizeCatId].shopSkuIdList,
                    ]);
                    delete relationCustomizeObj[relationCustomize.customizeCatId];
                  }
                });
                Object.keys(relationCustomizeObj).forEach((customizeCatId) => {
                  item.relationCustomizeVOList.push(relationCustomizeObj[customizeCatId]);
                });
              } else {
                nowItem.relationCustomizeVOList = services;
              }
            }
            return nowItem;
          });
        } else {
          payload.list = state.list.map((item) => {
            const nowItem = item;
            if (state.drawerIds.includes(item.id)) {
              nowItem.relationCustomizeVOList = services;
            }
            return nowItem;
          });
        }
        if (state.editRelation) {
          payload.editRelation = false;
          editDrawerEl.current.updateData();
        } else {
          payload.drawerIds = [];
        }
        if (state.drawerIds.length > 1) {
          payload.checkedList = [];
          payload.checkAll = false;
          payload.indeterminatet = false;
          payload.handleBatchType = '';
        }
        dispatch({
          type: 'setState',
          payload,
        });
      });
    },
    [state.drawerIds, state.editRelation, state.handleBatchType, state.list, t]
  );

  // 编辑服务商品
  const onEdit = useCallback(
    (relationShopSkuIdSet: number[], services: RelationCustomizeType[], notRequest = false) => {
      if (notRequest) {
        message.success(t('service_edit_success'));
        dispatch({
          type: 'setState',
          payload: {
            editDrawer: false,
            drawerIds: [],
            defaultServices: [],
          },
        });
        return;
      }
      updateGoodsService({
        shopSkuIds: state.drawerIds,
        relationShopSkuIdSet,
      }).then(() => {
        message.success(t('service_edit_success'));
        dispatch({
          type: 'setState',
          payload: {
            list: state.list.map((item) => {
              const nowItem = item;
              if (state.drawerIds.includes(item.id)) {
                nowItem.relationCustomizeVOList = services;
              }
              return nowItem;
            }),
            editDrawer: false,
            drawerIds: [],
            defaultServices: [],
          },
        });
      });
    },
    [state.drawerIds, state.list, t]
  );

  // 筛选
  const onFilter = useCallback(
    (params) => {
      getList({
        ...params,
        pageNo: 1,
        pageSize: state.pageSize,
      });
      dispatch({
        type: 'setState',
        payload: { filterDrawer: false },
      });
    },
    [getList, state.pageSize]
  );

  // 搜索 防抖
  const onSearch = useDebounceFn(
    (keyWord) => {
      getList({
        keyWord,
        pageNo: 1,
        pageSize: state.pageSize,
      });
    },
    { wait: 500 }
  ).run;

  // 查看更多服务
  const onShowAllService = useCallback((id: number) => {
    dispatch({
      type: 'setState',
      payload: {
        onlyRead: true,
        drawerIds: [id],
        editDrawer: true,
      },
    });
  }, []);

  // 编辑中关联服务
  const onEditExtra = usePermission('M_001_009_003', (services: RelationCustomizeType[]) => {
    dispatch({
      type: 'setState',
      payload: {
        relationDrawer: true,
        defaultServices: services,
        editRelation: true,
      },
    });
  });

  const onRelationPerm = usePermission('M_001_009_003', (item: GoodsListInfo) => {
    dispatch({
      type: 'setState',
      payload: {
        drawerIds: [item.id],
        defaultServices: cloneDeep(item.relationCustomizeVOList),
        relationDrawer: true,
        onlyRead: false,
      },
    });
  });

  const onEditPerm = usePermission('M_001_009_004', (item: GoodsListInfo) => {
    dispatch({
      type: 'setState',
      payload: {
        drawerIds: [item.id],
        defaultServices: cloneDeep(item.relationCustomizeVOList),
        editDrawer: true,
        onlyRead: false,
      },
    });
  });

  const onBatchAdd = usePermission('M_001_009_005_001', () => {
    dispatch({
      type: 'setState',
      payload: {
        drawerIds: state.checkedList as number[],
        defaultServices: [],
        relationDrawer: true,
        editRelation: false,
        handleBatchType: 'add',
      },
    });
  });

  const onBatchReplace = usePermission('M_001_009_005_002', () => {
    dispatch({
      type: 'setState',
      payload: {
        drawerIds: state.checkedList as number[],
        defaultServices: [],
        relationDrawer: true,
        editRelation: false,
        handleBatchType: 'replace',
      },
    });
  });

  const onFilterPerm = usePermission('M_001_009_002', () => {
    dispatch({
      type: 'setState',
      payload: { filterDrawer: true },
    });
  });

  useMount(() => {
    getList();
  });

  // 规格
  const standardStr = useCallback((arr) => {
    let str = '';
    arr.forEach((item: any) => {
      str += `；${item.name}：${item.value}`;
    });
    return str.slice(1);
  }, []);

  // 服务
  const getServiceEl = useCallback(
    ({ id, relationCustomizeVOList }: GoodsListInfo) => {
      const len = relationCustomizeVOList?.length || 0;
      if (!len) return <div className={styles.noService}>{t('payment_cannot_relation')}</div>;
      if (len < 4)
        return relationCustomizeVOList.map((service) => (
          <div className={styles.serviceItem} key={service.customizeCatId}>
            <Text ellipsis title={service.customizeCategoryName} className={styles.serviceName}>
              {service.customizeCategoryName}
            </Text>
            <span className={styles.serviceTotal}>
              {t('payment_enable_select', { num: service.shopSkuIdList?.length })}
            </span>
          </div>
        ));
      return (
        <>
          {relationCustomizeVOList
            .filter((_, index) => index < 2)
            .map((service) => (
              <div className={styles.serviceItem} key={service.customizeCatId}>
                <Text ellipsis title={service.customizeCategoryName} className={styles.serviceName}>
                  {service.customizeCategoryName}
                </Text>
                <span className={styles.serviceTotal}>
                  {t('payment_enable_select', { num: service.shopSkuIdList?.length })}
                </span>
              </div>
            ))}
          <div className={styles.serviceItem}>
            <span
              role="button"
              tabIndex={0}
              className={styles.moreService}
              onClick={(e) => {
                e.stopPropagation();
                onShowAllService(id);
              }}
            >
              <span>{t('service_view_more')}</span>
              <Icon name="right" size={16} color="#999EB2" />
            </span>
          </div>
        </>
      );
    },
    [onShowAllService, t]
  );

  return (
    <Context
      permission={{ code: 'M_001_009_001', newLogic: true }}
      head={
        <Head
          title={titles}
          isFilterActive={isFilterActive}
          onFilter={onFilterPerm}
          onSearch={onSearch}
          placeholder={t('distributionSearchPlaceholder')}
        />
      }
      loading={loading}
    >
      {state.list.length ? (
        <div className={styles.body}>
          <div className={styles.header}>
            <Checkbox
              indeterminate={state.indeterminate}
              checked={state.checkAll}
              onChange={onCheckAll}
              className={styles.checkbox}
            />
            <div className={styles.titles}>
              <div className={styles.info}>{t('service_product_info')}</div>
              <div className={styles.service}>{t('service_product_service')}</div>
              <div className={styles.handle}>{t('service_operation')}</div>
            </div>
          </div>
          <CheckboxGroup value={state.checkedList} className={styles.list} onChange={onCheckedList}>
            {state.list.map((item) => (
              <div key={item.id} className={styles.item}>
                <div className={styles.itemList}>
                  <Checkbox value={item.id} />
                  <div
                    role="button"
                    tabIndex={0}
                    className={styles.info}
                    onClick={() => {
                      toDetail(item);
                    }}
                  >
                    <div
                      className={classNames(
                        styles.imageWrap,
                        item.status === 50 ? styles.sale : styles.stay
                      )}
                    >
                      <div
                        className={classNames(
                          styles.imageWrap,
                          item.status === 50 ? styles.mysale : styles.mystay
                        )}
                      >
                        {item.status === 50 ? t('service_on_sale') : t('building_pending_shelf')}
                      </div>
                      <img src={item.images} alt="" className={styles.image} />
                    </div>
                    <div className={styles.title}>
                      <Paragraph ellipsis={{ rows: 2 }} className={styles.name}>
                        {item.skuSource === 1 && (
                          <img src={shareTag} alt="" className={styles.tag} />
                        )}
                        {item.name}
                      </Paragraph>
                      <Paragraph ellipsis className={styles.standard}>
                        {standardStr(item.standardList)}
                      </Paragraph>
                    </div>
                  </div>
                </div>
                <div className={styles.service}>{getServiceEl(item)}</div>
                <div className={styles.handle}>
                  <Dropdown
                    overlay={
                      <Menu
                        items={menuItems}
                        onClick={({ key, domEvent }) => {
                          domEvent.stopPropagation();
                          if (key === 'edit') onEditPerm(item);
                          else if (key === 'relation') onRelationPerm(item);
                        }}
                      />
                    }
                    placement="bottom"
                  >
                    <Icon
                      name="zu13366"
                      size={24}
                      color="#999EB2"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.preventDefault();
                      }}
                    />
                  </Dropdown>
                </div>
              </div>
            ))}
          </CheckboxGroup>
          <div className={styles.footer}>
            <div>
              <Checkbox checked={state.checkAll} onChange={onCheckAll}>
                {t('service_select_all')}
              </Checkbox>
              {!!state.checkedList.length && (
                <>
                  <span className={styles.checkedCount}>
                    {t('service_selected_count', { count: state.checkedList.length })}
                  </span>
                  <Dropdown
                    overlay={
                      <Menu
                        items={batchItems}
                        onClick={({ key, domEvent }) => {
                          domEvent.stopPropagation();
                          if (key === 'add') onBatchAdd();
                          else if (key === 'replace') onBatchReplace();
                        }}
                      />
                    }
                    placement="top"
                  >
                    <span className={styles.linkBtn}>{t('service_batch_operations')}</span>
                  </Dropdown>
                </>
              )}
            </div>
            <Pagination
              current={state.pageNo}
              total={state.total}
              pageSize={state.pageSize}
              showQuickJumper
              showTotal={(total) => t('service_total_count', { total })}
              onChange={(pageNo, pageSize) => {
                getList({
                  pageNo,
                  pageSize,
                  status: state.status,
                  isServiceProduct: state.isServiceProduct,
                  customizeCategoryIdSet: state.customizeCategoryIdSet,
                  standardList: state.standardList,
                  keyWord: state.keyWord,
                });
              }}
            />
          </div>
        </div>
      ) : (
        <Empty message={t('service_no_data')} className={styles.empty} />
      )}

      <RelationDrawer
        visible={state.relationDrawer}
        defaultServices={state.defaultServices}
        onClose={() => {
          const payload: any = {
            relationDrawer: false,
            defaultServices: [],
            handleBatchType: '',
          };
          if (state.editRelation) {
            payload.editRelation = false;
          } else {
            payload.drawerIds = [];
          }
          dispatch({
            type: 'setState',
            payload,
          });
        }}
        onOk={onRelation}
      />

      <EditDrawer
        ref={editDrawerEl}
        visible={state.editDrawer}
        id={state.drawerIds[0]}
        onlyRead={state.onlyRead}
        onExtra={onEditExtra}
        onClose={() => {
          dispatch({
            type: 'setState',
            payload: {
              onlyRead: false,
              editDrawer: false,
              drawerIds: [],
              defaultServices: [],
            },
          });
        }}
        onOk={onEdit}
      />

      <FilterDrawer
        visible={state.filterDrawer}
        defaulteFilters={{
          status: state.status || '',
          isServiceProduct: state.isServiceProduct || '',
          customizeCategoryIdSet: state.customizeCategoryIdSet || [],
          standardList: state.standardList || [],
        }}
        onClose={() => {
          dispatch({
            type: 'setState',
            payload: { filterDrawer: false },
          });
        }}
        onOk={onFilter}
      />
    </Context>
  );
}

GoodsRelationService.displayName = 'GoodsRelationService';

export default GoodsRelationService;
