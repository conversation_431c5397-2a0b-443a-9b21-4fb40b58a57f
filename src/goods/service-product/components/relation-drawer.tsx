import { useEffect, useState, useRef, useCallback } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import classNames from 'classnames';
import flattenDeep from 'lodash/flattenDeep';
import findIndex from 'lodash/findIndex';
import uniq from 'lodash/uniq';
import { Button, Checkbox, Spin, Image, Divider, Typography } from 'antd';
import type { DrawerProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, Empty, Search, Icon, Price } from '@/components';
import { checkCharge, checkPermission } from '@/utils/permission';
import { getCategoryCustomeList, getServiceProduct, shopSkuLoginCompany } from '@/apis';
import type { GetCategoryCustomeItemResult } from '@/apis';
import type { RelationCustomizeType } from '@/apis/sku-goods-list';
import type { ResultType } from '@/apis/mcs/get-service-product';
import i18n from '@/entry/i18n';
import styles from './relation-drawer.module.less';

export interface NewRelationCustomizeType extends RelationCustomizeType {
  id?: number;
  shopSkuId?: number;
  skuId?: number;
  imageList?: string[];
  name?: string;
  marketPrice?: number;
  marketPriceStr?: string;
  standardList?: { name: string; value: string }[];
  unit?: string;
}
interface PropsType extends DrawerProps {
  isRadio?: boolean;
  apiType?: number;
  zIndex?: number;
  notIsAloneBuy?: boolean;
  defaultServices: RelationCustomizeType[];
  // eslint-disable-next-line no-unused-vars
  onOk: (relationShopSkuIdSet: number[], services: NewRelationCustomizeType[]) => void;
}

interface GoodsParams {
  pageNo: number;
  pageSize: number;
  customizeCategoryIdSet: number[];
  customizeCategoryCodeSet: string[];
  searchKey: string;
  totalPages: number;
}

const { Paragraph } = Typography;

function RelationDrawer({
  visible,
  defaultServices,
  isRadio,
  apiType,
  zIndex,
  notIsAloneBuy,
  onOk,
  ...props
}: PropsType) {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [categoryList, setCategoryList] = useState<GetCategoryCustomeItemResult[]>([]);
  const [subCategoryList, setSubCategoryList] = useState<GetCategoryCustomeItemResult[]>([]);
  const [categoryId, setCategoryId] = useState(0);
  const [subCategoryId, setSubCategoryId] = useState(0);
  const [categoryHasMore, setCategoryHasMore] = useState(false);
  const [showSubCategory, setShowSubCategory] = useState(false);
  const [goodsList, setGoodsList] = useState<ResultType[]>([]);
  const [goodsHasMore, setGoodsHasMore] = useState(false);
  const [checkedIds, setCheckedIds] = useState<number[]>([]);
  const [checkedItems, setCheckedItems] = useState<NewRelationCustomizeType[]>([]);
  const categoryParams = useRef({ grade: 1, pageNo: 1, pageSize: 20, totalPages: 1 });
  const goodsParams = useRef<GoodsParams>({
    pageNo: 1,
    pageSize: 10,
    totalPages: 1,
    customizeCategoryIdSet: [],
    customizeCategoryCodeSet: [],
    searchKey: '',
  });

  // 获取分类
  const getCategoryData = () => {
    getCategoryCustomeList({ ...categoryParams.current }).then((res) => {
      if (categoryParams.current.pageNo === 1) {
        res.list.unshift({
          id: 0,
          categoryName: i18n.t('channel_all'),
          code: '',
        });
      }
      categoryParams.current.totalPages = Math.ceil(
        res.pagination.count / categoryParams.current.pageSize
      );
      setCategoryHasMore(categoryParams.current.pageNo < categoryParams.current.totalPages);
      setCategoryList([...categoryList, ...res.list]);
    });
  };

  // 滚动加载分类
  const loadMoreCategory = () => {
    categoryParams.current.pageNo += 1;
    if (categoryParams.current.pageNo > categoryParams.current.totalPages) return;
    getCategoryData();
  };

  // 获取服务商品
  const getGoodsData = (data: ResultType[] = []) => {
    if (apiType) {
      // 供应商后台-有效期管理   勿删
      const goods = goodsParams.current;
      const newGoodsParams = {
        ...goods,
        customizeCategorySet: goods.customizeCategoryCodeSet,
      };
      shopSkuLoginCompany({ ...newGoodsParams, skuType: 30, notIsAloneBuy }).then((res) => {
        goodsParams.current.totalPages = res.pagination.total;
        setGoodsHasMore(goodsParams.current.pageNo < goodsParams.current.totalPages);
        setGoodsList([...data, ...res.list]);
      });
    } else {
      getServiceProduct({ ...goodsParams.current }).then((res) => {
        goodsParams.current.totalPages = res.pagination.total;
        setGoodsHasMore(goodsParams.current.pageNo < goodsParams.current.totalPages);
        setGoodsList([...data, ...res.list]);
      });
    }
  };

  // 滚动加载服务商品
  const loadMoreGoodsData = () => {
    goodsParams.current.pageNo += 1;
    if (goodsParams.current.pageNo > goodsParams.current.totalPages) return;
    getGoodsData(goodsList);
  };

  // 搜索
  const onSearch = (val: string) => {
    goodsParams.current.searchKey = val;
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryIdSet = [];
    goodsParams.current.customizeCategoryCodeSet = [];
    setShowSubCategory(false);
    setSubCategoryId(0);
    setCategoryId(0);
    setSubCategoryList([]);
    getGoodsData();
  };

  // 选择一级分类
  const onFirstCategory = (item: GetCategoryCustomeItemResult) => {
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryIdSet = item.id ? [item.id] : [];
    goodsParams.current.customizeCategoryCodeSet = item.code ? [item.code] : [];
    setShowSubCategory(false);
    setSubCategoryId(0);
    getGoodsData();
    if (!item.id) {
      setCategoryId(0);
      setSubCategoryList([]);
      return;
    }
    setCategoryId(item.id);
    getCategoryCustomeList({
      pageNo: 1,
      pageSize: 999,
      parentId: item.id,
    }).then((res) => {
      setSubCategoryList(res.list);
    });
  };

  // 选择二级分类
  const onSecondaryCategory = (item: GetCategoryCustomeItemResult) => {
    goodsParams.current.pageNo = 1;
    goodsParams.current.customizeCategoryIdSet = [item.id as number];
    goodsParams.current.customizeCategoryCodeSet = [item.code as string];
    setSubCategoryId(item.id as number);
    getGoodsData();
  };

  // 选择
  const onChecked = (item: ResultType) => {
    const {
      customCatId,
      customizeCategoryName,
      id,
      unit,
      standardList,
      marketPriceStr,
      marketPrice,
      name,
      skuId,
    } = item;
    const i = findIndex(checkedItems, ['customizeCatId', customCatId]);
    if (!isRadio) {
      if (i !== -1) {
        const checkedItem = checkedItems[i];
        const { shopSkuIdList } = checkedItem;
        const j = shopSkuIdList.indexOf(id);
        if (j !== -1) {
          shopSkuIdList.splice(j, 1);
          if (!shopSkuIdList.length) {
            checkedItems.splice(i, 1);
          }
        } else {
          shopSkuIdList.push(id);
        }
      } else {
        checkedItems.push({
          customizeCatId: customCatId,
          customizeCategoryName,
          shopSkuIdList: [id],
        });
      }
      setCheckedIds(flattenDeep(checkedItems.map((e) => e.shopSkuIdList)));
      setCheckedItems(checkedItems);
    } else if (checkedItems.length) {
      // 供应商后台-有效期管理   勿删
      const { shopSkuIdList } = checkedItems[0];
      // 勾选已存在的，取消选中
      if (shopSkuIdList.indexOf(id) !== -1) {
        shopSkuIdList.splice(0, 1);
        checkedItems.splice(0, 1);
        setCheckedIds([]);
        setCheckedItems([]);
        return;
      }
      // 勾选不存在的，进行替换
      shopSkuIdList.splice(0, 1, id);
      checkedItems.splice(0, 1, {
        customizeCatId: customCatId,
        customizeCategoryName,
        shopSkuIdList: [id],
        id,
        skuId,
        shopSkuId: id,
        imageList: [item.images[0]],
        name,
        marketPriceStr,
        marketPrice,
        standardList,
        unit,
      });
      setCheckedIds(shopSkuIdList);
      setCheckedItems(checkedItems);
    } else {
      // 首次选择商品
      setCheckedIds([id]);
      setCheckedItems([
        {
          customizeCatId: customCatId,
          customizeCategoryName,
          shopSkuIdList: [id],
          id,
          shopSkuId: id,
          skuId,
          imageList: [item.images[0]],
          name,
          marketPriceStr,
          marketPrice,
          standardList,
          unit,
        },
      ]);
    }
  };

  useEffect(() => {
    if (visible) {
      if (checkCharge('M_001_012_001') && checkPermission('M_001_012_001')) {
        getCategoryData();
      } else {
        setCategoryList([
          {
            id: 0,
            categoryName: i18n.t('channel_all'),
            code: '',
          },
        ]);
      }
      getGoodsData();
      setCheckedIds(uniq(flattenDeep(defaultServices.map((item) => item.shopSkuIdList))));
      setCheckedItems(defaultServices);
    } else {
      setLoading(false);
      setCategoryList([]);
      setSubCategoryList([]);
      setGoodsList([]);
      setCategoryId(0);
      setSubCategoryId(0);
      setShowSubCategory(false);
      categoryParams.current = { grade: 1, pageNo: 1, pageSize: 20, totalPages: 1 };
      goodsParams.current = {
        pageNo: 1,
        pageSize: 10,
        totalPages: 1,
        customizeCategoryIdSet: [],
        customizeCategoryCodeSet: [],
        searchKey: '',
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 规格
  const standardStr = useCallback((arr) => {
    let str = '';
    arr.forEach((item: any) => {
      str += ` ${item.value}`;
    });
    return str.slice(1);
  }, []);

  return (
    <Drawer
      zIndex={zIndex || 1000}
      visible={visible}
      title={t('service_related_services')}
      className={styles.addChildGoodsDrawer}
      footer={
        <div className={styles.drawerFooter}>
          <div className={styles.drawerFooterText}>
            {t('service_selected_services')}
            <span className={styles.drawerFooterNumber}>{checkedIds.length}</span>
          </div>
          <Button
            type="primary"
            disabled={!checkedIds.length}
            onClick={() => {
              onOk(checkedIds, checkedItems);
            }}
          >
            {t('service_done')}
          </Button>
        </div>
      }
      {...props}
    >
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <div className={styles.drawerHead}>
          <Search
            realTimeSearch
            placeholder={t('service_search_product_name')}
            className={styles.searchInput}
            onSearch={onSearch}
          />
        </div>
        <div className={styles.drawerContent}>
          <div className={styles.drawerContentLeft} id="drawerContentLeft">
            <InfiniteScroll
              dataLength={categoryList.length}
              className={styles.drawerContentLeftScroll}
              hasMore={categoryHasMore}
              loader={
                <div className="text-center">
                  <Spin tip={t('service_loading')} />
                </div>
              }
              next={loadMoreCategory}
              scrollableTarget="drawerContentLeft"
            >
              {categoryList.map((item) => (
                <div
                  role="button"
                  tabIndex={0}
                  key={item.id}
                  className={classNames(categoryId === item.id ? styles.itemActive : styles.item)}
                  onClick={() => {
                    onFirstCategory(item);
                  }}
                >
                  {item.categoryName}
                </div>
              ))}
            </InfiniteScroll>
          </div>
          <div className={styles.drawerContentRight}>
            {!!subCategoryList.length && (
              <div className={styles.secondaryCategory}>
                <div className={styles.secondaryCategoryContent}>
                  {subCategoryList.slice(0, 2).map((item) => (
                    <span
                      role="button"
                      tabIndex={item.id || 0}
                      key={item.id}
                      title={item.categoryName}
                      className={classNames(
                        styles.secondaryCategoryItem,
                        subCategoryId === item.id ? styles.secondaryCategoryItemActive : ''
                      )}
                      onClick={() => {
                        onSecondaryCategory(item);
                      }}
                    >
                      {item.categoryName}
                    </span>
                  ))}
                </div>
                {subCategoryList.length > 2 ? (
                  <div
                    className={styles.secondaryCategoryOperate}
                    role="presentation"
                    onClick={() => {
                      setShowSubCategory(!showSubCategory);
                    }}
                  >
                    <Icon name={showSubCategory ? 'up' : 'down'} size={16} />
                  </div>
                ) : (
                  ''
                )}
                {showSubCategory ? (
                  <div className={styles.secondaryCategoryMore}>
                    <div className={styles.secondaryCategoryContent}>
                      {subCategoryList.slice(2).map((item) => (
                        <span
                          role="button"
                          tabIndex={item.id || 0}
                          key={item.id}
                          title={item.categoryName}
                          className={classNames(
                            styles.secondaryCategoryItem,
                            subCategoryId === item.id ? styles.secondaryCategoryItemActive : '',
                            showSubCategory ? 'mt-2' : ''
                          )}
                          onClick={() => {
                            onSecondaryCategory(item);
                          }}
                        >
                          {item.categoryName}
                        </span>
                      ))}
                    </div>
                  </div>
                ) : (
                  ''
                )}
              </div>
            )}
            <div className={styles.drawerContentRightList} id="drawerContentRightList">
              {goodsList.length ? (
                <InfiniteScroll
                  dataLength={goodsList.length}
                  className={styles.drawerContentRightScroll}
                  hasMore={goodsHasMore}
                  loader={
                    <div className="text-center">
                      <Spin tip={t('service_loading')} />
                    </div>
                  }
                  endMessage={
                    <div className={styles.divider}>
                      <Divider plain>
                        <span className={styles.endMessage}>{t('channel_load_end')}</span>
                      </Divider>
                    </div>
                  }
                  next={loadMoreGoodsData}
                  scrollableTarget="drawerContentRightList"
                >
                  {goodsList.map((item) => (
                    <div className={styles.goodsItem} key={item.id}>
                      <Image
                        // @ts-ignore
                        src={item.images || ''}
                        className={styles.goodsItemImage}
                        preview={false}
                      />
                      <div className={styles.goodsItemInfo}>
                        <div>
                          <Paragraph ellipsis={{ rows: 2 }} className={styles.goodsItemInfoName}>
                            {item.name}
                          </Paragraph>
                          <Paragraph ellipsis className={styles.goodsItemInfoStandard}>
                            {standardStr(item.standardList)}
                          </Paragraph>
                        </div>
                        <div className={styles.goodsItemInfoStandard}>
                          <Price value={item.marketPrice} />
                          <span> /{item.unit}</span>
                        </div>
                      </div>
                      <Checkbox
                        checked={checkedIds.includes(item.id)}
                        onChange={() => {
                          onChecked(item);
                        }}
                      />
                    </div>
                  ))}
                </InfiniteScroll>
              ) : (
                <Empty />
              )}
            </div>
          </div>
        </div>
      </Spin>
    </Drawer>
  );
}

RelationDrawer.displayName = 'RelationDrawer';

RelationDrawer.defaultProps = {
  isRadio: false,
  apiType: 0,
  zIndex: 1000,
  notIsAloneBuy: false,
};

export default RelationDrawer;
