.drawer {
  z-index: 998;
}

.linkButton {
  font-size: 14px !important;
  width: auto !important;
  min-width: auto !important;
  height: 20px !important;
  line-height: 20px;
  margin-left: 0;
  padding: 0 !important;

  &:active,
  &:hover,
  &:focus {
    color: #008cff;
  }

  span {
    width: 76px !important;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.spin {
  width: 100%;
  height: 100%;

  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}

.cardList {
  width: 100%;
}

.card {
  width: 100%;
  padding: 20px;
  box-shadow: 2px 4px 12px 0 rgb(0 0 0 / 8%);
  border-radius: 18px;
  background: #fff;

  & + .card {
    margin-top: 20px;
  }

  &:first-child {
    margin-top: 8px;
  }

  &:last-child {
    margin-bottom: 8px;
  }
}

.cardTitle {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-bottom: 20px;
}

.goodsList {
  display: block;
}

.goodsItem {
  display: flex;
  align-items: center;
  height: 90px;
  margin-left: 0 !important;

  & + .goodsItem {
    margin-top: 24px;
  }

  :global {
    .ant-checkbox {
      margin-right: 8px;
    }

    .ant-checkbox + span {
      display: flex;
      height: 90px;
      padding-right: 0;
      padding-left: 0;
      flex-grow: 1;
      align-items: center;
    }
  }
}

.goodsImg {
  width: 90px;
  min-width: 90px;
  height: 90px;
  border-radius: 10px;
}

.goodsInfo {
  display: flex;
  height: 90px;
  padding-left: 12px;
  justify-content: space-between;
  flex-grow: 1;
  flex-direction: column;
}

div.goodsName {
  font-weight: 600;
  max-height: 44px;
  line-height: 22px;
  margin-bottom: 4px !important;
}

.goodsStandard {
  color: #999eb2;
  font-size: 12px;
  max-width: 177px;
  line-height: 20px;
}

.footer {
  height: 86px;
  padding: 0 20px;
}

.footer,
.footerLeft {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.checkedCount {
  margin: 0 20px;
}

.onlyRead {
  cursor: default;

  :global {
    .ant-checkbox {
      display: none;
    }

    .ant-checkbox + span {
      padding-left: 0;
    }
  }
}

.empty {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}
