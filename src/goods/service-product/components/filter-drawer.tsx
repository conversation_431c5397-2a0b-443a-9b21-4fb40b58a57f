import { useCallback, useEffect, useRef, useState } from 'react';
import { useThrottleFn } from 'ahooks';
import { Spin } from 'antd';
import type { DrawerProps } from 'antd';
import { useTranslation } from 'react-i18next';
import { Drawer, FilterCard } from '@/components';
import type { FilterItemType, FilterCardRefType } from '@/components';
import { DrawerRefType } from '@/components/drawer/drawer';
import CategoryFilter from '@/src/stock/pages/query/filter-category';
import { testPerm } from '@/utils/permission';
import { getGoodsFilterOptions } from '@/apis';
import styles from './filter-drawer.module.less';

interface FilterParamsType {
  status: '' | 50 | 90;
  isServiceProduct: '' | 1 | 2;
  customizeCategoryIdSet: number[];
  standardList: {
    name: string;
    value: string[];
  }[];
}

interface PropsType extends DrawerProps {
  defaulteFilters?: FilterParamsType;
  onOk: SimpleFn<Partial<FilterParamsType>>;
}

function FilterDrawer({ visible, defaulteFilters, onOk, ...props }: PropsType) {
  const { t } = useTranslation();
  const drawerRef = useRef(null as unknown as DrawerRefType);
  const filterRef1 = useRef(null as unknown as FilterCardRefType);
  const filterRef2 = useRef(null as unknown as FilterCardRefType);
  const [loading, setLoading] = useState(false);
  const [showCategoryFilter, setShowCategoryFilter] = useState(false);
  const [categoryFilterWrap, setCategoryFilterWrap] = useState<HTMLElement | null>(null);
  const [filterOptions1, setFilterOptions1] = useState<FilterItemType[]>([
    {
      label: t('paymentManage_goodsStatus'),
      defaultValues: [''],
      key: 'status',
      type: 'radio',
      collapsible: 'disabled',
      children: [
        { label: t('service_all'), value: '' },
        { label: t('service_pending_listing'), value: 90 },
        { label: t('service_on_sale'), value: 50 },
      ],
    },
    {
      label: t('service_related_services'),
      defaultValues: [''],
      key: 'isServiceProduct',
      type: 'radio',
      collapsible: 'disabled',
      children: [
        { label: t('service_all'), value: '' },
        { label: t('service_related_services'), value: 1 },
        { label: t('service_not_related_services'), value: 2 },
      ],
    },
    {
      label: t('service_product_category'),
      defaultValues: [],
      key: 'customizeCategoryIdSet',
      type: 'select',
      collapsible: 'disabled',
      showCheckedLabels: true,
      hideChildren: true,
      placeholder: t('service_please_select_category'),
      children: [],
    },
  ]);
  const [filterOptions2, setFilterOptions2] = useState<FilterItemType[]>([]);

  // 设置默认选中
  const setDefaulteValues = () => {
    if (defaulteFilters) {
      filterOptions1[0].defaultValues = [defaulteFilters.status];
      filterOptions1[1].defaultValues = [defaulteFilters.isServiceProduct];
      setFilterOptions1([...filterOptions1]);

      if (filterOptions2.length) {
        const standardObj: Record<string, string[]> = {};
        defaulteFilters.standardList.forEach((item) => {
          standardObj[item.name] = item.value;
        });
        filterOptions2.forEach((item) => {
          const nowItem = item;
          nowItem.defaultValues = standardObj[item.key];
        });
        setFilterOptions2([...filterOptions2]);
      }
    }
  };

  // 获取服务商品的名称、规格选项
  const getData = () => {
    setLoading(true);
    getGoodsFilterOptions()
      .then((res) => {
        const standardObj: Record<string, string[]> = {};
        if (defaulteFilters) {
          defaulteFilters.standardList.forEach((item) => {
            standardObj[item.name] = item.value;
          });
        }
        const arr: FilterItemType[] = [
          {
            label: '名称',
            defaultValues: standardObj['名称'] || [],
            key: '名称',
            type: 'checkbox',
            showCheckedLabels: true,
            children: res.shopSkuNames.map((item) => ({
              label: item,
              value: item,
            })),
          },
        ];
        res.standardList.forEach((item) => {
          arr.push({
            label: item.name,
            defaultValues: standardObj[item.name] || [],
            key: item.name,
            type: 'checkbox',
            showCheckedLabels: true,
            children: item.value.map((item1) => ({
              label: item1,
              value: item1,
            })),
          });
        });
        setFilterOptions2(arr);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // 重置筛选条件
  const onReset = useCallback(() => {
    filterRef1.current.onReset();
    filterRef2.current.onReset();
    filterOptions1[2].defaultValues = [];
    filterOptions1[2].children = [];
    setFilterOptions1([...filterOptions1]);
  }, [filterOptions1]);

  // 确定筛选 节流
  const onConfirm = useThrottleFn(
    () => {
      const data = filterRef1.current.getValues();
      const standardObj = filterRef2.current.getValues();
      const standardList = Object.keys(standardObj).map((key) => ({
        name: key,
        value: standardObj[key],
      }));
      onOk({
        status: '',
        isServiceProduct: '',
        customizeCategoryIdSet: [],
        ...data,
        standardList: standardList || [],
      });
    },
    { wait: 3000 }
  ).run;

  // 显示自定义分类
  const onShowCategoryFilter = useCallback(() => {
    setShowCategoryFilter(true);
  }, []);

  // 自定义分类
  const onSetCustomizeCategory = useCallback(
    (ids, list) => {
      filterOptions1[2].defaultValues = ids;
      filterOptions1[2].children = list.map((item: any) => ({
        label: item.categoryName,
        value: item.id,
      }));
      setFilterOptions1([...filterOptions1]);
      setShowCategoryFilter(false);
    },
    [filterOptions1]
  );

  useEffect(() => {
    if (visible) {
      if (!testPerm('M_001_012_001')) return;
      setDefaulteValues();
      if (!filterOptions2.length) getData();
      setTimeout(() => {
        setCategoryFilterWrap(drawerRef.current.getBodyElem());
      }, 0);
    } else setShowCategoryFilter(false);
  }, [visible, filterOptions2.length]); // eslint-disable-line

  return (
    <Drawer
      ref={drawerRef}
      title={t('service_filter')}
      visible={visible}
      footer={
        <Drawer.Footer
          cancelText={t('service_reset')}
          okText={t('service_confirm')}
          onOk={() => {
            onConfirm();
          }}
          onCancel={() => {
            onReset();
          }}
        />
      }
      style={{ transform: 'translateX(0) !important' }}
      {...props}
    >
      <Spin spinning={loading} wrapperClassName={styles.spin}>
        <FilterCard ref={filterRef1} list={filterOptions1} onSelectClick={onShowCategoryFilter} />

        <FilterCard ref={filterRef2} list={filterOptions2} />

        {!!categoryFilterWrap && (
          <CategoryFilter
            visible={showCategoryFilter}
            defaultChecked={filterOptions1[2].children.map((item) => ({
              id: item.value,
              categoryName: item.label,
            }))}
            cateGoryId={filterOptions1[2].defaultValues || []}
            getContainer={categoryFilterWrap}
            onCloseCategory={() => {
              setShowCategoryFilter(false);
            }}
            setCateGoryId={onSetCustomizeCategory}
          />
        )}
      </Spin>
    </Drawer>
  );
}

FilterDrawer.displayName = 'FilterDrawer';

FilterDrawer.defaultProps = {
  defaulteFilters: {},
};

export default FilterDrawer;
