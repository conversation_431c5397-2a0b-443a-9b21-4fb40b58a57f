import { useCallback, useEffect, useMemo, useState, forwardRef, useImperativeHandle } from 'react';
import { useThrottleFn } from 'ahooks';
import classNames from 'classnames';
import { Button, Checkbox, Spin, Typography } from 'antd';
import { useTranslation } from 'react-i18next';
import type { DrawerProps } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import type { CheckboxValueType } from 'antd/es/checkbox/Group';
import { Drawer, Empty, Price } from '@/components';
import { getGoodsService } from '@/apis';
import type { RelationCustomizeType } from '@/apis/sku-goods-list';
import { ResultType } from '@/apis/mcs/get-goods-service';

import styles from './edit-drawer.module.less';

interface PropsType extends DrawerProps {
  id: number;
  onlyRead: boolean;
  onExtra: SimpleFn<RelationCustomizeType[]>;
  onOk: MultipleParamsFn<
    [relationShopSkuIdSet: number[], services: RelationCustomizeType[], notRequest: boolean]
  >;
}

interface ItemType extends ResultType {
  indeterminate: boolean;
  checkedIds: CheckboxValueType[];
  checkAll: boolean;
}

const CheckboxGroup = Checkbox.Group;
const { Paragraph } = Typography;

const EditDrawer = forwardRef(
  ({ id, visible, onlyRead, onClose, onOk, onExtra, ...props }: PropsType, ref) => {
    const [loading, setLoading] = useState(false);
    const { t } = useTranslation();
    const [list, setList] = useState<ItemType[]>([]);
    const [checkedIds, setCheckedIds] = useState<CheckboxValueType[]>([]);
    const [indeterminate, setIndeterminate] = useState(false);
    const [checkAll, setCheckAll] = useState(false);
    const [haveDeleted, setHaveDeleted] = useState(false);
    const [notRequest, setNotRequest] = useState(false); // 控制确定后是否请求接口
    const checkedCount = useMemo(
      () => list.reduce((count, item) => count + item.checkedIds.length, 0),
      [list]
    );

    // 获取数据
    const getData = useCallback((shopSkuId: number, btnState = false) => {
      setLoading(true);
      getGoodsService({ shopSkuId })
        .then((res) => {
          setList(
            res.list.map((item) => ({
              indeterminate: false,
              checkedIds: [],
              checkAll: false,
              ...item,
            }))
          );
          setCheckedIds([]);
          setIndeterminate(false);
          setCheckAll(false);
          setHaveDeleted(btnState);
          setNotRequest(btnState);
        })
        .finally(() => {
          setLoading(false);
        });
    }, []);

    // 全选
    const onCheckAll = useCallback(
      (e: CheckboxChangeEvent) => {
        const { checked } = e.target;
        setCheckedIds(checked ? list.map((item) => item.customizeCategoryId) : []);
        setCheckAll(checked);
        setIndeterminate(false);
        setList(
          list.map((item) => ({
            ...item,
            checkedIds: checked ? item.relationSkuVOList.map((goods) => goods.id) : [],
            indeterminate: false,
            checkAll: checked,
          }))
        );
      },
      [list]
    );

    // 分类全选
    const onCheckCategory = useCallback(
      (e: CheckboxChangeEvent, item: ItemType) => {
        const { checked } = e.target;
        const nowItem = item;
        nowItem.checkedIds = checked ? item.relationSkuVOList.map((goods) => goods.id) : [];
        nowItem.indeterminate = false;
        nowItem.checkAll = checked;
        setList([...list]);
      },
      [list]
    );

    // 选中分类
    const onCheckedCategory = useCallback(
      (ids: CheckboxValueType[]) => {
        setCheckedIds(ids);
        setIndeterminate(!!ids.length && ids.length < list.length);
        setCheckAll(ids.length === list.length);
      },
      [list.length]
    );

    // 选中商品
    const onCheckedGoods = useCallback(
      (ids: CheckboxValueType[], item: ItemType) => {
        const nowItem = item;
        nowItem.checkedIds = ids;
        nowItem.indeterminate = !!ids.length && ids.length < item.relationSkuVOList.length;
        nowItem.checkAll = ids.length === item.relationSkuVOList.length;
        setList([...list]);

        if (nowItem.checkAll && !checkedIds.includes(item.customizeCategoryId)) {
          onCheckedCategory([...checkedIds, item.customizeCategoryId]);
        } else if (!nowItem.checkAll && checkedIds.includes(item.customizeCategoryId)) {
          checkedIds.splice(checkedIds.indexOf(item.customizeCategoryId), 1);
          onCheckedCategory([...checkedIds]);
        }
      },
      [list, checkedIds, onCheckedCategory]
    );

    // 删除服务
    const onDelete = useCallback(() => {
      for (let i = 0; i < list.length; i += 1) {
        const item = list[i];
        if (checkedIds.includes(item.customizeCategoryId)) {
          list.splice(i, 1);
          i -= 1;
        } else {
          for (let j = 0; j < item.relationSkuVOList.length; j += 1) {
            const goods = item.relationSkuVOList[j];
            if (item.checkedIds.includes(goods.id)) {
              item.relationSkuVOList.splice(j, 1);
              j -= 1;
            }
          }
          item.checkedIds = [];
          item.indeterminate = false;
          item.checkAll = false;
        }
      }
      setList([...list]);
      setCheckedIds([]);
      setIndeterminate(false);
      setCheckAll(false);
      setHaveDeleted(true);
      setNotRequest(false);
    }, [checkedIds, list]);

    // 节流
    const onConfirm = useThrottleFn(
      () => {
        const relationShopSkuIdSet: number[] = [];
        const services: RelationCustomizeType[] = [];
        list.forEach((item) => {
          services.push({
            customizeCatId: item.customizeCategoryId,
            customizeCategoryName: item.customizeCategoryName,
            shopSkuIdList: item.relationSkuVOList.map((goods) => {
              relationShopSkuIdSet.push(goods.id);
              return goods.id;
            }),
          });
        });
        onOk(relationShopSkuIdSet, services, notRequest);
      },
      { wait: 3000 }
    ).run;

    const onExtraBefore = useThrottleFn(
      () => {
        const services: RelationCustomizeType[] = [];
        list.forEach((item) => {
          services.push({
            customizeCatId: item.customizeCategoryId,
            customizeCategoryName: item.customizeCategoryName,
            shopSkuIdList: item.relationSkuVOList.map((goods) => goods.id),
          });
        });
        onExtra(services);
      },
      { wait: 3000 }
    ).run;

    // 规格
    const standardStr = useCallback((arr: Array<{ name: string; value: string }>) => {
      let str = '';
      arr.forEach((item) => {
        str += `；${item.name}：${item.value}`;
      });
      return str.slice(1);
    }, []);

    useImperativeHandle(ref, () => ({ updateData: () => getData(id, true) }), [getData, id]);

    useEffect(() => {
      if (visible) {
        getData(id);
      } else {
        setList([]);
        setCheckedIds([]);
        setIndeterminate(false);
        setCheckAll(false);
        setHaveDeleted(false);
        setNotRequest(false);
      }
    }, [getData, visible, id]);

    return (
      <Drawer
        visible={visible}
        title={onlyRead ? t('service_all_service_products') : t('service_edit_related_services')}
        extra={
          !onlyRead && (
            <Button
              type="link"
              onClick={onExtraBefore}
              className={styles.linkButton}
              title={t('service_related_services')}
            >
              {t('service_related_services')}
            </Button>
          )
        }
        footer={
          !onlyRead && (
            <div className={styles.footer}>
              <div className={styles.footerLeft}>
                <Checkbox indeterminate={indeterminate} checked={checkAll} onChange={onCheckAll}>
                  {t('service_select_all')}
                </Checkbox>
                <span className={styles.checkedCount}>
                  {t('service_selected_count', { count: checkedCount })}
                </span>
                <Button
                  type="link"
                  danger
                  disabled={!checkedCount}
                  onClick={onDelete}
                  className={styles.linkButton}
                >
                  {t('service_delete')}
                </Button>
              </div>
              <Button type="primary" disabled={!haveDeleted} onClick={onConfirm}>
                {t('service_confirm')}
              </Button>
            </div>
          )
        }
        className={styles.drawer}
        onClose={onClose}
        {...props}
      >
        <Spin spinning={loading} wrapperClassName={styles.spin}>
          <CheckboxGroup
            value={checkedIds}
            onChange={onCheckedCategory}
            className={styles.cardList}
          >
            {list.map((item) => (
              <div className={styles.card} key={item.customizeCategoryId}>
                <Checkbox
                  value={item.customizeCategoryId}
                  indeterminate={item.indeterminate}
                  checked={item.checkAll}
                  className={classNames(styles.cardTitle, onlyRead ? styles.onlyRead : '')}
                  onChange={(e) => {
                    onCheckCategory(e, item);
                  }}
                >
                  {item.customizeCategoryName}
                </Checkbox>
                <CheckboxGroup
                  value={item.checkedIds}
                  onChange={(e) => {
                    onCheckedGoods(e, item);
                  }}
                  className={styles.goodsList}
                >
                  {item.relationSkuVOList.map((goods) => (
                    <Checkbox
                      value={goods.id}
                      key={goods.id}
                      className={classNames(styles.goodsItem, onlyRead ? styles.onlyRead : '')}
                    >
                      <img src={goods.images} alt="" className={styles.goodsImg} />
                      <div className={styles.goodsInfo}>
                        <div>
                          <Paragraph ellipsis={{ rows: 2 }} className={styles.goodsName}>
                            {goods.name}
                          </Paragraph>
                          <Paragraph ellipsis className={styles.goodsStandard}>
                            {standardStr(goods.standardList)}
                          </Paragraph>
                        </div>
                        <div>
                          <Price separate value={goods.marketPrice} />
                          <span className={styles.goodsStandard}> /{goods.unit}</span>
                        </div>
                      </div>
                    </Checkbox>
                  ))}
                </CheckboxGroup>
              </div>
            ))}
          </CheckboxGroup>

          {!loading && !list.length && <Empty className={styles.empty} />}
        </Spin>
      </Drawer>
    );
  }
);

EditDrawer.displayName = 'EditDrawer';

EditDrawer.defaultProps = {};

export default EditDrawer;
