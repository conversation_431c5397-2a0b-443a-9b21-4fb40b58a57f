import { PropsWithChildren, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import isString from 'lodash/isString';
import { AuthResult } from '@/apis';
import { saveTokens } from '@/utils/auth';
import { sessions, user } from '@/store';
import { initPermission } from '@/utils/permission';

export interface AuthSuccessProps {
  result: AuthResult | null;
}

function AuthSuccess({ result, children }: PropsWithChildren<AuthSuccessProps>) {
  const navigate = useNavigate();

  useEffect(() => {
    (async () => {
      if (result && result.accessToken) {
        const to = await saveTokens(result as unknown as Record<string, unknown>);
        await user.setUser(result.user as Record<string, unknown>);
        user.getPersonalization();
        user.getFileUsePermission();
        sessions.init(true);
        await initPermission(true);
        let nextPath = result.redirect;
        if (nextPath?.includes('?isModify')) {
          [nextPath] = nextPath.split('?isModify');
        }
        // 分销管理相关路由，退出再登录进入分销管理首页。
        const disPath = [
          '/distribution/recruit-setting',
          '/distributor/manage',
          '/distribution/join-audit',
          '/distribution/level-manage',
          '/distribution/program',
          'distribution/order',
        ];
        // 建站装修页面，退出再登录进入建站管理首页。
        const buildSitePath = [
          '/website/manage',
          '/build-web-site/file/template-market',
          '/build-web-site/file/enterprise-template',
          '/build-web-site/file',
          '/build-web-site/file/space-setup',
          '/build-web-site/file/template-setting',
        ];
        const determinePath = (nextPath || to).split('?')?.[0];
        const redirect = () => {
          if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
            window.userInfo = result.user;
            localStorage.setItem('UM_USER_INFO', JSON.stringify(result.user));
            localStorage.setItem('UM_USER_TOKEN', `${result.tokenType} ${result.accessToken}`);
            if (isString(nextPath)) {
              nextPath += `${nextPath.includes('?') ? '&' : '?'}prevpage=auth`;
              if (/^https?:\/\//.test(nextPath)) {
                window.location.replace(nextPath);
              } else {
                navigate(nextPath || to, { replace: true });
              }
            }
          } else if (disPath.indexOf(determinePath) > -1) {
            navigate('/distribution/home');
          } else if (buildSitePath.indexOf(determinePath) > -1) {
            navigate('/build/manage');
          } else {
            if (nextPath) {
              nextPath += `${nextPath.includes('?') ? '&' : '?'}prevpage=auth`;
            }
            navigate(nextPath || to, { replace: true });
          }
        };
        if (import.meta.env.BASE_TRANSITION) {
          setTimeout(redirect, 100);
        } else {
          redirect();
        }
      }
    })();
  }, [navigate, result]);

  // eslint-disable-next-line
  return <>{children}</>;
}

export default AuthSuccess;
