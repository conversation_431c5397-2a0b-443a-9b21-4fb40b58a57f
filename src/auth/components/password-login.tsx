import { forwardRef, PropsWithChildren, useRef, useState } from 'react';
import { FormInstance, Form, Input, FormProps } from 'antd';
import { useTranslation } from 'react-i18next';
import styles from './fields.module.less';

export interface PasswordForm {
  username: string;
  password: string;
}

export interface FieldsProps extends FormProps {
  // eslint-disable-next-line no-unused-vars
  onConfirm?: (form: PasswordForm) => void;
}

const PasswordLogin = forwardRef(({ onConfirm }: PropsWithChildren<FieldsProps>) => {
  const [disabled, setDisabled] = useState(true);
  const [message, setMessage] = useState('');
  const form = useRef(null as unknown as FormInstance);
  // 多语言配置对象 t
  const { t } = useTranslation();
  const validateFields = () => {
    form.current
      ?.validateFields()
      .then(() => {
        setDisabled(false);
      })
      .catch(() => {
        setMessage('');
        setDisabled(true);
      });
  };
  return (
    <div>
      <Form ref={form} layout="vertical" className={styles.box} onFinish={onConfirm}>
        <Form.Item
          label={t('auth_login_accPho')}
          rules={[{ required: true, message: t('auth_login_noAccPhoEmp') }]}
          name="username"
          className={styles.field}
        >
          <Input
            placeholder={t('auth_login_pleAccPho')}
            className={styles.input}
            onBlur={validateFields}
          />
        </Form.Item>
        <Form.Item
          label={t('auth_login_pas')}
          rules={[
            { required: true, message: t('auth_login_noPasEmp') },
            { type: 'string', min: 6, max: 20, message: t('auth_login_pasDig') },
            { validator: () => (message ? Promise.reject(new Error(message)) : Promise.resolve()) },
          ]}
          name="password"
          className={styles.field}
        >
          <Input.Password
            placeholder={t('auth_login_plePas')}
            className={styles.input}
            onBlur={validateFields}
          />
        </Form.Item>
        <button type="submit" disabled={disabled} className={styles.button}>
          {t('auth_login_confPriv')}
        </button>
      </Form>
    </div>
  );
});

PasswordLogin.defaultProps = {
  onConfirm: undefined,
};

export default PasswordLogin;
