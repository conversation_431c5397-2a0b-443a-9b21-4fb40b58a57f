import { ForwardedRef, forwardRef, PropsWithChildren, useImperativeHandle, useState } from 'react';
import { Form, FormProps, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import isFunction from 'lodash/isFunction';
import styles from './fields.module.less';

export interface AuthPasswordInstance {
  // eslint-disable-next-line no-unused-vars
  setMessage(msg: string): void;
}

export interface SendPasswordForm {
  password: string;
  surePassword: string;
}

export interface AuthPasswordProps extends FormProps {
  buttonText: string;
  // eslint-disable-next-line no-unused-vars
  onConfirmRegister?: (form: SendPasswordForm) => void | Promise<void>;
}

const AuthPassword = forwardRef(
  (
    { onConfirmRegister, className, buttonText, children }: PropsWithChildren<AuthPasswordProps>,
    ref: ForwardedRef<AuthPasswordInstance>
  ) => {
    const [submitting, setSubmitting] = useState(false);
    const [disabled, setDisabled] = useState(true);
    const [form] = Form.useForm();
    // eslint-disable-next-line no-unused-vars
    let message: string;

    const validateFields = () => {
      form.validateFields();
    };
    const onChangeInput = () => {
      const values = form.getFieldsValue();
      setDisabled(!values.password || !values.surePassword);
    };

    useImperativeHandle(ref, () => ({
      setMessage: (msg: string) => {
        message = msg;
        form.validateFields(['surePassword']);
      },
    }));
    // 多语言配置对象 t
    const { t } = useTranslation();
    return (
      <Form
        form={form}
        layout="vertical"
        validateTrigger={['onBlur']}
        className={className}
        onFinish={(values) => {
          if (isFunction(onConfirmRegister)) {
            setSubmitting(true);
            try {
              Promise.resolve(onConfirmRegister(values))
                .catch(() => {})
                .finally(() => {
                  setSubmitting(false);
                });
            } catch (e) {
              console.warn(e);
              setSubmitting(false);
            }
          }
        }}
      >
        <Form.Item
          label={t('auth_login_pas')}
          validateFirst
          rules={[
            { required: true, message: `${t('auth_login_noPasEmp')}` },
            { type: 'string', min: 8, max: 20, message: `${t('auth_login_pasDig')}` },
            {
              pattern: /^[\w`~@#$%^&*()+-=\\/<>{}[\]?]{8,20}$/,
              message: `${t('auth_login_pasFmt')}`,
            },
          ]}
          className={styles.field}
          name="password"
        >
          <Input.Password
            placeholder={t('auth_login_plePas')}
            className={styles.input}
            onChange={onChangeInput}
          />
        </Form.Item>
        <Form.Item
          label={t('auth_login_confPas')}
          validateFirst
          rules={[
            { required: true, message: `${t('auth_login_pleConfPas')}` },
            { type: 'string', min: 8, max: 20, message: `${t('auth_login_pasDig')}` },
            {
              pattern: /^[\w`~@#$%^&*()+-=\\/<>{}[\]?]{8,20}$/,
              message: `${t('auth_login_pasFmt')}`,
            },
            { validator: () => (message ? Promise.reject(new Error(message)) : Promise.resolve()) },
          ]}
          className={styles.field}
          name="surePassword"
        >
          <Input.Password
            placeholder={t('auth_login_pleConfPas')}
            className={styles.input}
            onChange={onChangeInput}
            onBlur={validateFields}
          />
        </Form.Item>
        <button type="submit" disabled={disabled || submitting} className={styles.button}>
          {submitting ? <LoadingOutlined className="mr-1" /> : null}
          {buttonText}
        </button>
        {children}
      </Form>
    );
  }
);

AuthPassword.defaultProps = {
  onConfirmRegister: undefined,
};

export default AuthPassword;
