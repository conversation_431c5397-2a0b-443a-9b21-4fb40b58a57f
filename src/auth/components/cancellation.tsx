import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Modal, Button, message } from 'antd';
import { useTranslation } from 'react-i18next';
import { cancellationCancel } from '@/apis';
import { user } from '@/store';
import styles from './cancellation.module.less';

interface CancellationProps {
  visibleCancellation: boolean;
}

function Cancellation({ visibleCancellation }: CancellationProps) {
  const navigate = useNavigate();
  const [show, setShow] = useState(visibleCancellation); // 多语言配置对象 t
  const { t } = useTranslation();
  const goContinue = () => {
    setShow(false);
    window.sessionStorage.clear();
    user.logout().then(() => {
      navigate('/auth/login');
    });
  };
  const cancel = () => {
    try {
      const memberInfo = JSON.parse(sessionStorage.getItem('memberInfo') || '');
      cancellationCancel({
        memberId: memberInfo.memberId,
        userId: memberInfo.userId,
      }).then(() => {
        setShow(false);
        sessionStorage.clear();
        user.logout().then(() => {
          navigate('/auth/login');
        });
      });
    } catch (e) {
      message.error(t('auth_login_cancelErr'));
    }
  };
  useEffect(() => {
    setShow(visibleCancellation);
  }, [visibleCancellation]);
  return (
    <Modal
      title={t('auth_login_cancelTitle')}
      visible={show}
      centered
      footer={null}
      width={700}
      closable={false}
    >
      <div className={styles.cancellation}>
        <div>{t('auth_login_cancel_one')}</div>
        <div>{t('auth_login_cancel_two')}</div>
        <div>{t('auth_login_cancel_three')}</div>
        <div>{t('auth_login_cancel_four')}</div>
      </div>
      <div className={styles.btn}>
        <Button type="primary" onClick={goContinue}>
          {t('auth_login_cancelNext')}
        </Button>
        <Button type="primary" className="ml-4" onClick={cancel}>
          {t('auth_login_cancel')}
        </Button>
      </div>
    </Modal>
  );
}

export default Cancellation;
