.field {
  position: relative;

  :global {
    .ant-form-item-required {
      &::before {
        display: none !important;
      }
    }

    .ant-form-item-label {
      & > label {
        color: @text-color-secondary;
        font-size: @font-size-sm;
        line-height: 12px;
        position: static;
      }
    }

    .ant-form-item-explain-error {
      font-size: @font-size-sm;
    }
  }
}

.fieldLabel {
  font-size: @font-size-sm;
  line-height: 22px;
  position: absolute;
  top: 0;
  right: 0;
}

.input {
  height: 42px;
  line-height: 16px;
  padding: 7px 17px;
  border-radius: 10px;

  :global {
    &,
    .ant-input {
      font-size: @font-size-sm;
    }
  }
}

.button {
  color: #fff;
  font-size: @font-size-lg;
  display: inline-block;
  min-width: 122px;
  height: 42px;
  border: 1px solid #040919;
  border-radius: 11px;
  background-color: #040919;
  cursor: pointer;

  &[disabled] {
    border-color: #bfc0c1;
    background-color: #bfc0c1;
    cursor: not-allowed;
  }
}

.time {
  color: @text-color-secondary;
  font-size: @font-size-sm;
}

.codeBtn {
  color: #5e9ff8;
  font-size: @font-size-sm;
  height: 32px;
  padding: 0;
  border: 1px solid transparent;
  background-color: transparent;
  cursor: pointer;
}
