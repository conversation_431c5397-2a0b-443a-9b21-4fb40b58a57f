import {
  ForwardedRef,
  forwardRef,
  PropsWithChildren,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';
import { Form, FormProps, Input } from 'antd';
import isNil from 'lodash/isNil';
import isFunction from 'lodash/isFunction';
import { useTranslation } from 'react-i18next';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import { Icon, VerifySlide } from '@/components';
import { isPhone } from '@/utils/utils';
import { authStore } from '@@/auth/store';
import { CaptchaRespType, userLoginGetCaptchaImg, VerifyCodeParamsType } from '@/apis';
import styles from './fields.module.less';

type SendCodeFn = (
  // eslint-disable-next-line no-unused-vars
  phone: string,
  // eslint-disable-next-line no-unused-vars
  params?: unknown
) => Promise<{ time: number; captchaResp?: CaptchaRespType; code: number }>;
export interface FieldInstance {
  // eslint-disable-next-line no-unused-vars
  setMessage(msg: string): void;
}

export interface SendCodeProps {
  time: number;
  loading: boolean;
  onSend: () => void;
}

export interface SendCodeForm {
  phone: string;
  code: string;
}

export interface FieldsProps extends FormProps {
  sendCode: SendCodeFn;
  buttonText: string;
  // eslint-disable-next-line react/require-default-props
  phone?: string;
  // eslint-disable-next-line no-unused-vars
  onConfirm?: (form: SendCodeForm) => void | Promise<void>;
}

function SendCode({ time, loading, onSend }: SendCodeProps) {
  // 多语言配置对象 t
  const { t } = useTranslation();
  if (time > 0) {
    return <div className={styles.time}>{time}s</div>;
  }
  return (
    <button disabled={loading} type="button" className={styles.codeBtn} onClick={onSend}>
      {loading ? <Icon name="loading" className="mr-1" /> : null}
      {t('auth_login_getVer')}
    </button>
  );
}

const Fields = forwardRef(
  (
    { sendCode, onConfirm, className, buttonText, children, phone }: PropsWithChildren<FieldsProps>,
    ref: ForwardedRef<FieldInstance>
  ) => {
    const [timeCount, setTimeCount] = useState(0);
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [showSlide, setShowSlide] = useState(false);
    const [disabled, setDisabled] = useState(true);
    const [messagePhone, setMessagePhone] = useState('');
    // 多语言配置对象 t
    const { t } = useTranslation();
    // @ts-ignore
    const phoneEl = useRef(null as unknown as Input);
    const [form] = Form.useForm();
    let interval: number | null;
    let message: string;
    const validateFields = () => {
      form.validateFields();
    };
    // eslint-disable-next-line consistent-return
    const onSendCode = (params?: VerifyCodeParamsType) => {
      if (phoneEl.current && isNil(interval)) {
        return new Promise<void>((resolve, reject) => {
          const { value } = phoneEl.current.input;
          if (isPhone(value)) {
            setLoading(true);
            sendCode(value, params)
              .then(({ time, code }) => {
                if (code === 1) {
                  setShowSlide(true);
                  return;
                }
                let count = time || 60;
                setTimeCount(count);
                const countdown = () => {
                  count -= 1;
                  setTimeCount(count);
                  if (count <= 0) {
                    clearInterval(interval as number);
                    interval = null;
                  }
                };
                // @ts-ignore
                interval = setInterval(countdown, 1000);
                setMessagePhone('');
                form.validateFields(['phone']);
                resolve();
              })
              .catch((error) => {
                if (error.message === '手机号已注册') {
                  setShowSlide(false);
                  reject(error);
                }
                setMessagePhone(error.message);
                form.validateFields(['phone']);
                reject(error);
              })
              .finally(() => {
                setLoading(false);
              });
          }
        });
      }
      return Promise.reject();
    };
    const onChangeInput = () => {
      const values = form.getFieldsValue();
      authStore.setPhone(values.phone);
      setMessagePhone('');
      setDisabled(!values.phone || !values.code);
    };
    // eslint-disable-next-line consistent-return
    const waitTime = () => {
      if (phoneEl.current && isNil(interval)) {
        const { value } = phoneEl.current.input;
        if (isPhone(value)) {
          return userLoginGetCaptchaImg(value);
        }
      }
      return Promise.reject();
    };

    useEffect(
      () => {
        form.setFieldsValue({ phone });
        return () => {
          if (interval) {
            clearInterval(interval);
          }
        };
      },
      // @ts-ignore
      [form, interval, phone]
    );
    useImperativeHandle(ref, () => ({
      setMessage: (msg: string) => {
        message = msg;
        form.validateFields(['code']);
      },
    }));

    return (
      <Form
        form={form}
        layout="vertical"
        validateTrigger={['onBlur']}
        className={className}
        onFinish={(values) => {
          if (isFunction(onConfirm)) {
            setSubmitting(true);
            try {
              const promise = onConfirm(values);
              if (promise) {
                promise.finally(() => {
                  setSubmitting(false);
                });
              }
            } catch (e) {
              console.warn(e);
              setSubmitting(false);
            }
          }
        }}
      >
        <Form.Item
          label={t('auth_login_pho')}
          rules={[
            { required: true, message: `${t('auth_login_noPhoEmp')}` },
            {
              validator: () =>
                messagePhone ? Promise.reject(new Error(messagePhone)) : Promise.resolve(),
            },
            {
              validator: (_, value) =>
                !value || isPhone(value)
                  ? Promise.resolve()
                  : Promise.reject(new Error(`${t('auth_login_phoErr')}`)),
            },
          ]}
          name="phone"
          className={styles.field}
        >
          <Input
            ref={phoneEl}
            placeholder={t('auth_login_plePho')}
            className={styles.input}
            onChange={onChangeInput}
            onBlur={validateFields}
          />
        </Form.Item>
        <Form.Item
          label={t('auth_login_verCode')}
          tooltip={t('auth_login_verCode')}
          rules={[
            { required: true, message: `${t('auth_login_pleVer')}` },
            { min: 6, message: `${t('auth_login_verErr')}` },
            { validator: () => (message ? Promise.reject(new Error(message)) : Promise.resolve()) },
          ]}
          name="code"
          className={styles.field}
        >
          <Input
            placeholder={t('auth_login_pleVer')}
            suffix={
              <SendCode
                loading={loading}
                time={timeCount}
                onSend={() => {
                  const { value } = phoneEl.current.input;
                  if (value && isNil(interval) && isPhone(value)) onSendCode();
                }}
              />
            }
            maxLength={6}
            className={styles.input}
            onChange={onChangeInput}
            onBlur={validateFields}
          />
        </Form.Item>
        <button type="submit" disabled={disabled || submitting} className={styles.button}>
          {submitting ? <LoadingOutlined className="mr-1" /> : null}
          {buttonText}
        </button>
        {children}
        {showSlide && (
          <VerifySlide
            visible={showSlide}
            onCancel={() => setShowSlide(false)}
            waitTime={waitTime}
            senCode={onSendCode}
          />
        )}
      </Form>
    );
  }
);

Fields.defaultProps = {
  onConfirm: undefined,
};

export default Fields;
