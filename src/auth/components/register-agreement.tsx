import { Keyboard<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ventHand<PERSON>, useState } from 'react';
import { Checkbox, CheckboxProps } from 'antd';
import { useTranslation } from 'react-i18next';
import classNames from 'classnames';
import { Icon } from '@/components';
import styles from './register-agreement.module.less';

export interface RegisterAgreementProps extends CheckboxProps {
  allowed?: boolean;
  // eslint-disable-next-line react/require-default-props
  isChecked?: boolean;
}

const isHnc: boolean = import.meta.env.BIZ_APP_PLATFORM_NO === '1';
function RegisterAgreement({
  allowed,
  onChange,
  className,
  style,
  isChecked,
}: RegisterAgreementProps) {
  const [visible, setVisible] = useState(allowed);

  const onClick: MouseEventHandler = (e) => {
    e?.preventDefault();
    setVisible(!visible);
  };
  const onKeyDown: KeyboardEventHandler<HTMLSpanElement> = (e) => {
    if (e.keyCode === 13) {
      e?.preventDefault();
      setVisible(!visible);
    }
  };

  const classes = classNames(className, styles.checkbox);
  const boxClass = classNames(styles.box, {
    [styles.visible]: visible,
  });
  const headClass = classNames('text-right', 'pt-3', 'px-4');
  // 多语言配置对象 t
  const { t } = useTranslation();
  return (
    <>
      {isChecked ? (
        <Checkbox checked={allowed} className={classes} style={style} onChange={onChange}>
          <span className={styles.text}>{t('auth_login_logConf')}</span>
          <span
            role="button"
            tabIndex={0}
            className={styles.agreement}
            onClick={onClick}
            onKeyDown={onKeyDown}
          >
            {t('auth_login_userAgreePriv')}
          </span>
        </Checkbox>
      ) : (
        <span
          role="button"
          tabIndex={0}
          className={styles.agreement}
          onClick={onClick}
          onKeyDown={onKeyDown}
        >
          {t('auth_login_userAgreePriv')}
        </span>
      )}
      <div className={boxClass}>
        <div className={headClass}>
          <Icon name="close" className={styles.close} onClick={onClick} />
        </div>
        <iframe
          title="1"
          src={
            isHnc
              ? 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/csc86/%E7%94%A8%E6%88%B7%E5%92%8C%E9%9A%90%E7%A7%81%E6%9D%83%E5%8D%8F%E8%AE%AE.html'
              : 'https://epoimages.obs.cn-south-1.myhuaweicloud.com/agreement/%E7%94%A8%E6%88%B7%E6%B3%A8%E5%86%8C%E5%8D%8F%E8%AE%AE%E5%92%8C%E5%8D%8E%E4%B8%96%E7%95%8C%E9%9A%90%E7%A7%81%E6%9D%83%E6%94%BF%E7%AD%96.html'
          }
          className={styles.iframe}
        />
      </div>
    </>
  );
}

RegisterAgreement.defaultProps = {
  allowed: false,
};

export default RegisterAgreement;
