import { useEffect, useRef, useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { AuthResult, sendLoginVerifyCode, phoneVerifyCodeLogin } from '@/apis';
import { saveTokens } from '@/utils/auth';
import { authStore } from '../store';
import Fields, { FieldInstance, SendCodeForm } from '../components/fields';
import RegisterAgreement from '../components/register-agreement';
import AuthSuccess from '../components/auth-success';
import useNext from '../hooks/use-next';
import styles from './login.module.less';

function VerifyCodeLogin() {
  const [next] = useNext();
  const [searchParams] = useSearchParams({ next: import.meta.env.BIZ_APP_HOME_URL });
  const [allowed, setAllowed] = useState(false);
  const [result, setResult] = useState(null as AuthResult | null);
  const [phone, setPhone] = useState(String);
  const fieldEl = useRef(null as unknown as FieldInstance);
  // 多语言配置对象 t
  const { t } = useTranslation();
  const onConfirm = (form: SendCodeForm) => {
    if (allowed) {
      return phoneVerifyCodeLogin(form.phone, form.code)
        .then((res) => {
          if (res.user.status === 3) {
            // @ts-ignore
            saveTokens(res);
            authStore.setCancellation(true);
            sessionStorage.setItem(
              'memberInfo',
              JSON.stringify({
                memberId: res.user.memberId,
                userId: res.user.id,
              })
            );
            return;
          }
          setResult({
            ...res,
            redirect: searchParams.get('next'),
          });
        })
        .catch((err) => {
          fieldEl.current.setMessage(err.message);
        });
    }
    if (fieldEl.current) {
      fieldEl.current.setMessage(`${t('auth_login_readConfAgree')}`);
    }
    return Promise.reject();
  };

  useEffect(() => {
    setPhone(authStore.phone);
    authStore.setTitle(`${t('auth_login_verLog')}`);
  }, []); // eslint-disable-line

  return (
    <AuthSuccess result={result}>
      <Link to={next('/auth/login')} className={styles.link}>
        {t('auth_login_accPasLog')}
      </Link>
      <Fields
        buttonText={t('public_sure')}
        ref={fieldEl}
        sendCode={sendLoginVerifyCode}
        onConfirm={onConfirm}
        className={styles.box}
        phone={phone}
      />
      <RegisterAgreement
        isChecked
        allowed={allowed}
        style={{ marginTop: '66px' }}
        onChange={(e) => {
          setAllowed(e.target.checked);
        }}
      />
    </AuthSuccess>
  );
}

export default VerifyCodeLogin;
