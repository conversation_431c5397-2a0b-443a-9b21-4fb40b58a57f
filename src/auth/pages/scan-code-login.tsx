import { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { AuthResult, checkScanQrCode, generateQrCode } from '@/apis';
import { saveTokens } from '@/utils/auth';
import { authStore } from '../store';
import AuthSuccess from '../components/auth-success';
import useNext from '../hooks/use-next';
import pc from '../assets/img/pc.png';
import styles from './scan-code-login.module.less';

function ScanCodeLogin() {
  const [next, searchParams] = useNext({ type: '', next: '' });
  const [type, setType] = useState('');
  const qrCodeRef = useRef('');
  const [qrCodeImage, setQrCodeImage] = useState('');
  const [result, setResult] = useState(null as AuthResult | null);
  const [logging, setLogging] = useState(false);
  const timeoutRef = useRef(null as null | number);
  // 多语言配置对象 t
  const { t } = useTranslation();
  const createQrCode = () => {
    if (searchParams.get('type') === 'wx') {
      // eslint-disable-next-line no-use-before-define
      appendScript('WxLogin', 'https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js').then(
        () => {
          // eslint-disable-next-line
          new (window as any).WxLogin({
            id: 'wxLogin',
            appid: import.meta.env.BIZ_WECHAT_QR_CODE_APP_ID,
            scope: 'snsapi_login',
            state: 'ObrsGLsZaHX7p2UOX6PM71uvCK5ghMojXidETmzR6bQY3e08uNMYiseA3SFuKZEj',
            redirect_uri: encodeURIComponent(
              `${
                import.meta.env.BIZ_WECHAT_REDIRECT_URI || window.location.origin
              }/auth/login/wechat-scan?next=${searchParams.get('next') || '/'}`
            ),
            response_type: 'echronos',
            href: `${import.meta.env.BIZ_ORIGIN_PUBLIC_URL}/static/css/wx-qr-code.css`,
          });
        }
      );
    } else {
      setLogging(true);
      generateQrCode(1)
        .then((res) => {
          qrCodeRef.current = res.code;
          setQrCodeImage(`data:image/png;base64,${res.pictureBase}`);
          // eslint-disable-next-line no-use-before-define
          checkQrCode();
        })
        .finally(() => {
          setLogging(false);
        });
    }
  };
  const checkQrCode = () => {
    checkScanQrCode(qrCodeRef.current)
      .then((res) => {
        timeoutRef.current = null;
        setResult(res);
        if (res.user.status === 3) {
          // @ts-ignore
          saveTokens(res);
          authStore.setCancellation(true);
          sessionStorage.setItem(
            'memberInfo',
            JSON.stringify({
              memberId: res.user.memberId,
              userId: res.user.id,
            })
          );
        }
      })
      .catch(() => {
        // @ts-ignore
        timeoutRef.current = setTimeout(checkQrCode, 3000);
      });
  };
  const appendScript = (name: string, uri: string) =>
    new Promise((resolve) => {
      // @ts-ignore
      if (!window || !window[name]) {
        const script = document.createElement('script');
        script.src = uri;
        script.async = true;
        script.onload = resolve;

        document.head.append(script);
      } else {
        // @ts-ignore
        resolve();
      }
    });

  useEffect(() => {
    authStore.setTitle('');
    // @ts-ignore
    setType(searchParams.get('type'));
    createQrCode();
    return () => {
      // @ts-ignore
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    };
  }, []); // eslint-disable-line

  return (
    <AuthSuccess result={result}>
      <Link to={next('/auth/login')} className={styles.rightLink}>
        <img src={pc} alt="登录" />
      </Link>
      {type === 'wx' ? (
        <div id="wxLogin" className={styles.wxQrcodeBox} />
      ) : (
        <>
          {/* <div className={styles.codeTitle}>手机echOS扫码登录</div> */}
          <div className={styles.codeTitle}>echOS {t('auth_login_scanLog')}</div>
          <div className={styles.qrcodeBox}>
            {!logging ? <img src={qrCodeImage} alt="echOS扫码登录" /> : null}
          </div>
        </>
      )}
    </AuthSuccess>
  );
}

export default ScanCodeLogin;
