import { useEffect, useRef, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { AuthResult, getWeChatQrCode, sendLoginVerifyCode, weChatRegister } from '@/apis';
import { Avatar, Spin } from 'antd';
import { authStore } from '../store';
import AuthSuccess from '../components/auth-success';
import RegisterAgreement from '../components/register-agreement';
import Fields, { SendCodeForm } from '../components/fields';
import PasswordLogin, { PasswordForm } from '../components/password-login';
import useNext from '../hooks/use-next';
import styles from './wechat-scan-login.module.less';

function WeChatScanLogin() {
  const [next, searchParams] = useNext({ code: '', next: '' });
  const [loading, setLoading] = useState(false);
  const [allowed, setAllowed] = useState(false);
  const [type, setType] = useState('code');
  const [typeText, setTypeText] = useState('手机号');
  const [contraryTitle, setContraryTitle] = useState('账号');
  const [result, setResult] = useState(null as AuthResult | null);
  const tokensRef = useRef('');
  const navigate = useNavigate();
  const [userInfo, setUserInfo] = useState(Object);
  const getWeChatQrCodeInfo = () => {
    setLoading(true);
    getWeChatQrCode({
      code: searchParams.get('code') || '',
      state: searchParams.get('state') || '',
    })
      .then((res) => {
        if (res.tokenType === 'bearer') {
          setResult(res);
        } else {
          tokensRef.current = res.accessToken;
          setUserInfo(res.userInfo);
        }
      })
      .catch(() => {
        navigate(-1);
      })
      .finally(() => {
        setLoading(false);
      });
  };
  const handleToggleType = () => {
    setType(type === 'login' ? 'code' : 'login');
    setTypeText(type === 'login' ? '账号' : '手机号');
    setContraryTitle(type === 'login' ? '手机号' : '账号');
  };
  const onConfirm = (form: SendCodeForm) => {
    weChatRegister({ wechatToken: tokensRef.current, phone: form.phone, code: form.code }).then(
      (res) => {
        setResult(res);
      }
    );
  };
  const onConfirmPassword = (form: PasswordForm) => {
    weChatRegister({
      wechatToken: tokensRef.current,
      username: form.username,
      password: form.password,
    }).then((res) => {
      setResult(res);
    });
  };
  useEffect(() => {
    if (!searchParams.get('code')) {
      navigate(next('/auth/login/scan-code?type=wx'));
    }
    authStore.setTitle('微信联合登陆');
    getWeChatQrCodeInfo();
  }, []); // eslint-disable-line
  return (
    <AuthSuccess result={result}>
      <Link to="/auth/login/scan-code?type=wx" className={styles.link}>
        扫码登录
      </Link>
      <Spin spinning={loading}>
        <div className={styles.userInfo}>
          <Avatar src={userInfo.headimgurl} size={60} className={styles.avatar} />
          <div className={styles.title}>
            <span>尊敬的微信用户：</span>
            <span title={userInfo.nickname} className={styles.textRed}>
              {userInfo.nickname}
            </span>
          </div>
        </div>
        {type === 'code' ? (
          <Fields
            buttonText="同意协议并绑定"
            sendCode={sendLoginVerifyCode}
            onConfirm={onConfirm}
          />
        ) : (
          <PasswordLogin onConfirm={onConfirmPassword} />
        )}
        <div className="mt-3">
          <div className="clearfix mb-3">
            {/* eslint-disable-next-line jsx-a11y/no-static-element-interactions */}
            <span className={styles.binddingLing} onClick={handleToggleType}>
              绑定{contraryTitle}
            </span>
            <RegisterAgreement
              isChecked={false}
              allowed={allowed}
              onChange={(e) => {
                setAllowed(e.target.checked);
              }}
            />
          </div>
          <div className={styles.text}>
            为了给您更好的服务，请绑定一个{typeText} ，便于您下次快捷登录
          </div>
        </div>
      </Spin>
    </AuthSuccess>
  );
}
export default WeChatScanLogin;
