import { useEffect, useRef, useState } from 'react';
import { authStore } from '@@/auth/store';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  forgetPassword,
  sendForgetPasswordVerifyCode,
  validateForgetPasswordVerifyCode,
} from '@/apis';
import Fields, { FieldInstance, SendCodeForm } from '../components/fields';
import AuthPassword, { AuthPasswordInstance, SendPasswordForm } from '../components/auth-password';
import useNext from '../hooks/use-next';
import styles from './register.module.less';

function ForgetPassword() {
  const [next] = useNext();
  const [phone, setPhone] = useState('');
  const [token, setToken] = useState('');
  const fieldEl = useRef(null as unknown as FieldInstance);
  const authPassword = useRef(null as unknown as AuthPasswordInstance);
  // 多语言配置对象 t
  const { t } = useTranslation();
  const navigate = useNavigate();
  const onConfirm = (form: SendCodeForm) =>
    validateForgetPasswordVerifyCode(form.phone, form.code)
      .then((res) => {
        setPhone(form.phone);
        setToken(res.token);
      })
      .catch((err) => {
        fieldEl.current.setMessage(err.message);
      });
  const onConfirmRegister = (form: SendPasswordForm) => {
    if (form.password !== form.surePassword) {
      authPassword.current.setMessage(`${t('auth_login_twoPasDif')}`);
      return Promise.reject();
    }
    return forgetPassword(phone, form.password, token).then(() => {
      navigate(next('/auth/login'));
    });
  };
  useEffect(() => {
    authStore.setTitle(`${t('auth_login_forPwd')}`);
  });
  return (
    <div>
      <Link to={next('/auth/login')} className={styles.link}>
        {t('auth_login_accPasLog')}
      </Link>
      {!token ? (
        <Fields
          className={styles.box}
          ref={fieldEl}
          sendCode={sendForgetPasswordVerifyCode}
          onConfirm={onConfirm}
          buttonText={t('public_next')}
        />
      ) : (
        <AuthPassword
          ref={authPassword}
          onConfirmRegister={onConfirmRegister}
          buttonText={t('public_confim')}
        />
      )}
    </div>
  );
}

export default ForgetPassword;
