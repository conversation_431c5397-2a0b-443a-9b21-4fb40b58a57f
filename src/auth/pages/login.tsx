import { useEffect, useRef, useState } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { FormInstance, Form, Input, Modal } from 'antd';
import classNames from 'classnames';
import JSEncrypt from 'jsencrypt';
import { useTranslation } from 'react-i18next';
import LoadingOutlined from '@ant-design/icons/LoadingOutlined';
import { AuthResult, passwordLogin, getPasswordRandom } from '@/apis';
import { MOBILE_BROWSER } from '@echronos/core';
import { saveTokens } from '@/utils/auth';
import isString from 'lodash/isString';
import { authStore } from '../store';
import AuthSuccess from '../components/auth-success';
import useNext from '../hooks/use-next';
import styles from './login.module.less';
import shield from '../assets/img/shield.png';
import wechat from '../assets/img/wx.png';
import qrCode from '../assets/img/qrcode.png';

export interface AccountForm {
  account: string;
  password: string;
}

const entry = new JSEncrypt();
entry.setPublicKey(
  'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDHArToWyy3K8+0hJcwslCriWDgxguymQgo9H4pdjbeiMVOoArEuC68xpzAvQTy1tSZNa0ENNg75nmUpleVvGtWM00A2HB7lLqKn9mdug9OLR1D7SyF2/A9INQhmjo9/JpUkJnmnxDr0it7rqFsGCiNopZD5muH5Zu4sOdXFp2HwQIDAQAB'
);

function Login() {
  const [next] = useNext();
  const [searchParams] = useSearchParams({ next: import.meta.env.BIZ_APP_HOME_URL });
  const [disabled, setDisabled] = useState(true);
  const [logging, setLogging] = useState(false);
  const [result, setResult] = useState(null as AuthResult | null);
  const [message, setMessage] = useState('');
  const form = useRef(null as unknown as FormInstance);
  // 多语言配置对象 t
  const { t } = useTranslation();
  const validateFields = () => {
    form.current?.validateFields();
  };
  const onFinish = (e: AccountForm) => {
    if (logging) return;
    setLogging(true);
    getPasswordRandom()
      .then((res) =>
        passwordLogin(
          e.account,
          entry.encrypt(e.password + (isString(res) ? res : res.data || '')) as string,
          '1'
        )
      )
      .then((res) => {
        if (res.user.status === 3) {
          // @ts-ignore
          saveTokens(res);
          authStore.setCancellation(true);
          sessionStorage.setItem(
            'memberInfo',
            JSON.stringify({
              memberId: res.user.memberId,
              userId: res.user.id,
            })
          );
          return;
        }
        setResult({
          ...res,
          redirect: searchParams.get('next'),
        });
      })
      .catch((err) => {
        setLogging(false);
        setMessage(err.message);
        form.current.validateFields(['password']);
      });
  };
  const onChangeInput = () => {
    const values = form.current.getFieldsValue();
    authStore.setPhone(values.account);
    setDisabled(!values.account || !values.password);
    if (message) {
      setMessage('');
    }
  };

  useEffect(() => {
    Modal.destroyAll();
    form.current.setFieldsValue({ account: authStore.phone });
    authStore.setTitle(`${t('auth_login')}`);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // eslint-disable-line
  useEffect(() => {
    if (MOBILE_BROWSER) {
      window.location.href =
        'https://epoimages.obs.cn-south-1.myhuaweicloud.com/static/download.html';
    }
  });

  const btnClass = classNames(styles.bigBtn, 'mr-2');
  return (
    <AuthSuccess result={result}>
      <Link to={next('/auth/login/scan-code')} className={styles.rightLink}>
        <img src={qrCode} alt="扫码登录" />
      </Link>
      <Link to={next('/auth/register')} className={styles.link}>
        {t('auth_register')}
      </Link>
      <div className={styles.box}>
        <Link to={next('/auth/login/verify-code')} className={btnClass}>
          <img src={shield} alt="验证码登录" style={{ width: '16px' }} />
          <span className="ml-1">{`${t('auth_login_phoVerLog')}`}</span>
        </Link>
        <Link to={next('/auth/login/scan-code?type=wx')} className={styles.smallBtn}>
          <img src={wechat} alt="微信" />
        </Link>
      </div>
      <Form ref={form} layout="vertical" className={styles.box} onFinish={onFinish}>
        <Form.Item
          label={t('auth_login_accPho')}
          rules={[{ required: true, message: `${t('auth_login_noAccPhoEmp')}` }]}
          name="account"
          className={styles.field}
        >
          <Input
            placeholder={`${t('auth_login_pleAccPho')}`}
            className={styles.input}
            onChange={onChangeInput}
            onBlur={validateFields}
          />
        </Form.Item>
        <Form.Item
          label={
            <>
              {t('auth_login_pas')}
              <Link to={next('/auth/forget-password')} className={styles.fieldLabel}>
                {t('auth_login_forPwd')}
              </Link>
            </>
          }
          rules={[
            { required: true, message: `${t('auth_login_noPasEmp')}` },
            { type: 'string', min: 8, max: 20, message: `${t('auth_login_pasDig')}` },
            { validator: () => (message ? Promise.reject(new Error(message)) : Promise.resolve()) },
          ]}
          name="password"
          className={styles.field}
        >
          <Input.Password
            placeholder={`${t('auth_login_plePas')}`}
            className={styles.input}
            onChange={onChangeInput}
            onBlur={validateFields}
          />
        </Form.Item>
        <button type="submit" disabled={disabled} className={styles.button}>
          {logging ? <LoadingOutlined className="mr-1" /> : null}
          {t('auth_login')}
        </button>
      </Form>
    </AuthSuccess>
  );
}

export default Login;
