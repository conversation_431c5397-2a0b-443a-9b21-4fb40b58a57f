import { useRef, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { AuthResult, register, sendRegisterVerifyCode, validateRegisterVerifyCode } from '@/apis';
import { authStore } from '@@/auth/store';
import { message } from 'antd';
import Fields, { FieldInstance, SendCodeForm } from '../components/fields';
import AuthPassword, { AuthPasswordInstance, SendPasswordForm } from '../components/auth-password';
import RegisterAgreement from '../components/register-agreement';
import AuthSuccess from '../components/auth-success';
import useNext from '../hooks/use-next';
import styles from './register.module.less';

function Register() {
  const [next] = useNext();
  const [allowed, setAllowed] = useState(false);
  const fieldEl = useRef(null as unknown as FieldInstance);
  const authPassword = useRef(null as unknown as AuthPasswordInstance);
  const [phone, setPhone] = useState('');
  const [token, setToken] = useState('');
  const [result, setResult] = useState(null as AuthResult | null);
  // 多语言配置对象 t
  const { t } = useTranslation();
  const onConfirm = (form: SendCodeForm) => {
    if (allowed) {
      return validateRegisterVerifyCode(form.phone, form.code)
        .then((res) => {
          setPhone(form.phone);
          setToken(res.token);
        })
        .catch((err) => {
          fieldEl.current.setMessage(err.message);
        });
    }
    if (fieldEl.current) {
      fieldEl.current.setMessage(`${t('auth_login_readConfAgree')}`);
    }
    return Promise.reject();
  };
  const onConfirmRegister = (form: SendPasswordForm) => {
    if (form.password !== form.surePassword) {
      authPassword.current.setMessage(`${t('auth_login_twoPasDif')}`);
      return;
    }
    register(phone, form.password, token, 1, 1, 1, 1)
      .then((res) => {
        setResult(res);
      })
      .catch((err) => {
        message.error(err?.message || String(err));
      });
  };
  useEffect(() => {
    authStore.setTitle(`${t('auth_register')}`);
  }, []); // eslint-disable-line
  // @ts-ignore
  // @ts-ignore
  return (
    <AuthSuccess result={result}>
      <Link to={next('/auth/login')} className={styles.link}>
        {t('auth_login_accPasLog')}
      </Link>
      {!token ? (
        <Fields
          className={styles.box}
          ref={fieldEl}
          sendCode={sendRegisterVerifyCode}
          onConfirm={onConfirm}
          buttonText={t('public_next')}
        />
      ) : (
        <AuthPassword
          ref={authPassword}
          onConfirmRegister={onConfirmRegister}
          buttonText={t('auth_register')}
        />
      )}
      {!token ? (
        <RegisterAgreement
          isChecked
          allowed={allowed}
          style={{ marginTop: '66px' }}
          onChange={(e) => {
            setAllowed(e.target.checked);
          }}
        />
      ) : null}
    </AuthSuccess>
  );
}

export default Register;
