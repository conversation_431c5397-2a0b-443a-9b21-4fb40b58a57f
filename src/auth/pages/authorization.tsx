import { getAccessTokenByCode, AuthResult } from '@/apis';
import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import AuthSuccess from '../components/auth-success';

declare global {
  interface Window {
    userInfo: Record<string, unknown> | null;
  }
}

let clientId: string;
if (import.meta.env.BIZ_APP_PLATFORM_NO === '1') {
  clientId = 'hnc-web';
} else if (import.meta.env.BIZ_APP_PLATFORM_NO === '2') {
  clientId = 'hongyun-web';
} else if (import.meta.env.BIZ_APP_PLATFORM_NO === '3') {
  clientId = `web-${window.location.hostname}`;
} else {
  clientId = '';
}

function Authorization() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams({ authorization: '', next: '', clientId: '' });
  const [result, setResult] = useState(null as AuthResult | null);

  useEffect(() => {
    if (!searchParams.get('authorization')) {
      navigate(-1);
    } else {
      getAccessTokenByCode({
        clientId: searchParams.get('clientId') || clientId,
        redirectUri: `${window.location.host}`,
        code: searchParams.get('authorization') || '',
      })
        .then((res: AuthResult) => {
          setResult({
            ...res,
            redirect: searchParams.get('next'),
          });
        })
        .catch((err) => {
          console.error(err);
          navigate(-1);
        });
    }
  }, [navigate, searchParams]);

  return <AuthSuccess result={result} />;
}

Authorization.displayName = 'Authorization';

export default Authorization;
