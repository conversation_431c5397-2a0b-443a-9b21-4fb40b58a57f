@import '../components/fields.module.less';

.box {
  padding-top: 35px !important;
}

.bigBtn {
  width: 200px;
  line-height: 42px;
  background-color: @primary-color;
}

.smallBtn {
  width: 42px;
  padding: 10px;
  background-color: #fff;
}

.rightLink {
  width: 63px;
  height: 63px;
  position: absolute;
  top: 0;
  right: 0;
}

.bigBtn,
.smallBtn,
.rightLink {
  color: #fff;
  display: inline-block;
  height: 42px;
  border-radius: 10px;
  text-align: center;
  vertical-align: top;

  &:hover,
  &:active {
    color: #fff;
    opacity: 0.8;
  }
}

.link {
  color: #5e9ff8;
  font-size: @font-size-sm;
  line-height: 1;
  position: absolute;
  top: 63px;
  right: 42px;
  z-index: 9;
}
