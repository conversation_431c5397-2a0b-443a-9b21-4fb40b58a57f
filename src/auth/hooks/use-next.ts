import { useCallback } from 'react';
import { To, URLSearchParamsInit, useSearchParams } from 'react-router-dom';
import isString from 'lodash/isString';

function useNext(defaultInit?: URLSearchParamsInit): [MultipleParamsFn<[To], To>, URLSearchParams] {
  const [searchParams] = useSearchParams(defaultInit);
  const next = useCallback(
    (to: To) => {
      const nextUrl = encodeURIComponent(searchParams.get('next') || '');
      if (nextUrl) {
        if (isString(to)) {
          return `${to}${to.indexOf('?') > 0 ? '&' : '?'}next=${nextUrl}`;
        }
        return {
          ...to,
          search: `${to.search ? `${to.search}&` : '?'}next=${nextUrl}`,
        };
      }
      return to;
    },
    [searchParams]
  );

  return [next, searchParams];
}

export default useNext;
