import { Navigate, RouteObject } from 'react-router-dom';
import { lazy, Redirect } from '@/components';

const routes: RouteObject[] =
  import.meta.env.BIZ_APP_PLATFORM_NO === '0'
    ? [
        {
          path: 'login/verify-code',
          element: lazy(() => import('./pages/verify-code')),
        },
        {
          path: 'login/scan-code',
          element: lazy(() => import('./pages/scan-code-login')),
        },
        {
          path: 'login/wechat-scan',
          element: lazy(() => import('./pages/wechat-scan-login')),
        },
        {
          path: 'login',
          element: lazy(() => import('./pages/login')),
        },
        {
          path: 'register',
          element: lazy(() => import('./pages/register')),
        },
        {
          path: 'forget-password',
          element: lazy(() => import('./pages/forget-password')),
        },
        {
          path: 'authorization',
          element: lazy(() => import('./pages/authorization')),
        },
        {
          path: '',
          element: <Navigate to="/auth/login" />,
        },
      ]
    : [
        {
          path: 'authorization',
          element: lazy(() => import('./pages/authorization')),
        },
        {
          path: '*',
          element: <Redirect to={import.meta.env.BIZ_APP_LOGOUT_REDIRECT_URL} />,
        },
      ];

export default routes;

export const basePath = '/auth';
// 布局组件
export const layout = lazy(() => import('./layouts/layouts'));
