@import 'styles/mixins/mixins';

@font-face {
  font-family: animation-text;
  font-display: swap;
  src: url('./fonts/webfont.eot'), /* IE9 */ url('./fonts/webfont.woff2') format('woff2'),
    url('./fonts/webfont.woff') format('woff'),
    /* chrome、firefox */ url('./fonts/webfont.ttf') format('truetype'); /* chrome、firefox、opera、Safari, Android, iOS 4.2+ */
}

.box {
  display: flex;
  width: 100vw;
  height: 100vh;
  justify-content: center;
  align-items: center;
  background: #000 url('https://img.huahuabiz.com/static/background/image.jpg') no-repeat center /
    cover;
}

.center {
  display: flex;
  width: 351px;
  min-height: 558px;
  margin: 0 auto;
  justify-content: center;
  border-radius: 18px;
  background-color: rgb(245 246 250 / 80%);
  backdrop-filter: blur(50px);
  align-items: center;
}

.layout {
  width: 100vw;
  height: 100vh;
  background: url('https://img.huahuabiz.com/static/background/image.jpg') no-repeat center / cover;
}

.main {
  display: flex;
  width: 100%;
  max-width: 1037px;
  height: 100%;
  min-height: 555px;
  margin: 0 auto;
  padding: 14px;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 9;
}

.wrap {
  .flex-column(350px);

  height: 555px;
  padding: 55px 47px;
  border-radius: 33px;
  background-color: rgb(245 246 250 / 80%);
  backdrop-filter: blur(30px);
  overflow: hidden;
  position: relative;
  z-index: 9;
}

.wrapHead {
  height: 98px;
  overflow: hidden;
}

.headTitle {
  font-size: 17px;
  padding-right: 50px;

  &::before {
    content: '';
    display: inline-block;
    width: 27.3px;
    height: 27.3px;
    margin-right: 2.8px;
    background: url('../assets/img/logo.png') no-repeat 50% / cover;
    vertical-align: -6px;
  }
}

.wrapTitle {
  font-size: 32px;
  font-weight: 500;
  line-height: 54px;
  margin-top: 14px;
}

.textBox {
  .flex-column(562px);

  margin-left: 8px;
}

.animation {
  color: #fff;
  font-size: 48px;
  font-family: animation-text, sans-serif;
  line-height: 63px;
  margin: 0 auto;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  z-index: 9;
}

.animationWrap,
.animationBox {
  &::before,
  &::after {
    content: '';
    display: block;
    width: 11px;
    height: 11px;
    border: 3px solid #fff;
    position: absolute;
  }
}

.animationWrap {
  width: 562px;
  padding: 14px 35px;

  &::before {
    border-right: none;
    border-bottom: none;
    top: 0;
    left: 0;
  }

  &::after {
    border-bottom: none;
    border-left: none;
    top: 0;
    right: 0;
  }
}

.animationBox {
  display: inline-block;
  height: 63px;
  overflow: hidden;
  vertical-align: top;

  &::before {
    border-top: none;
    border-right: none;
    bottom: 0;
    left: 0;
  }

  &::after {
    right: 0;
    bottom: 0;
    border-top: none;
    border-left: none;
  }
}

.animationText {
  transform: translateY(-126px);
}

.play {
  .animationText {
    animation: translate-text 17.75s ease;
  }
}

.video {
  display: block;
  width: 100vw;
  height: 100vh;
  object-fit: cover;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 0;
}

//@keyframes scale-text {
//  6% {
//    width: 0;
//    opacity: .6;
//    transform: scale(0, 0);
//    visibility: hidden;
//  }
//  10% {
//    width: 561px;
//    opacity: 1;
//    transform: scale(1, 1);
//  }
//  95.8% {
//    width: 561px;
//    opacity: 1;
//    transform: scale(1, 1);
//  }
//  98% {
//    width: 0;
//    opacity: .6;
//    transform: scale(0, 0);
//    visibility: visible;
//  }
//  100% {
//    width: 0;
//    visibility: hidden;
//  }
//}

//@keyframes move-text {
//  6% {
//    transform: translateX(-280px);
//  }
//  10% {
//    transform: translateX(0);
//  }
//  95.8% {
//    transform: translateX(0);
//  }
//  98% {
//    transform: translateX(-280px);
//  }
//}

@keyframes translate-text {
  0% {
    transform: translateY(-126px);
  }

  31.33% {
    transform: translateY(-126px);
    opacity: 0.9;
  }

  33.33% {
    transform: translateY(-63px);
  }

  64.66% {
    transform: translateY(-63px);
    opacity: 0.9;
  }

  66.66% {
    transform: translateY(0);
  }

  100% {
    transform: translateY(0);
  }
}

.TextEn {
  .flex-column(685px);
}

.animationTextEn {
  min-width: 200px;
}
