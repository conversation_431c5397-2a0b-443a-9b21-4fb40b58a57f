import { useRef, useState } from 'react';
import { Outlet, Navigate, useSearchParams, useLocation } from 'react-router-dom';
import { observer } from 'mobx-react-lite';
import classNames from 'classnames';
import { Helmet } from 'react-helmet';
import { useTranslation } from 'react-i18next';
import { user } from '@/store';
import { cleanPermission } from '@/utils/permission';
import Footer from '@@/home/<USER>/footer';
import useLayout from '@/hooks/use-layout';
import { authStore } from '../store';
import Cancellation from '../components/cancellation';
import styles from './layouts.module.less';

const AuthLayouts = observer(() => {
  const [play, setPlay] = useState(false);
  const videoEl = useRef(null as null | HTMLVideoElement);
  // 多语言配置对象 t
  const { t, i18n } = useTranslation();
  const onPlaying = () => {
    setPlay(true);
  };
  const onEnded = () => {
    setPlay(false);
    videoEl.current?.play();
  };
  const textBoxClass = classNames(styles.textBox, i18n.language !== 'zh' && styles.TextEn, {
    [styles.play]: play,
  });
  return import.meta.env.BIZ_APP_PLATFORM_NO === '0' ? (
    <section className={styles.layout}>
      <main className={styles.main}>
        <div className={styles.wrap}>
          <div className={styles.wrapHead}>
            <div className={styles.headTitle}>echOS</div>
            <div className={styles.wrapTitle}>{authStore.title}</div>
          </div>
          <Outlet />
        </div>
        <div className={textBoxClass}>
          <div className={styles.animation}>
            <div className={styles.animationWrap}>
              <span>
                {t('auth_login_echOs')}
                {i18n.language !== 'zh' ? ' ' : ''}
              </span>
              <span className={styles.animationBox}>
                <div
                  className={classNames(
                    styles.animationText,
                    i18n.language !== 'zh' && styles.animationTextEn
                  )}
                >
                  <div>{t('auth_login_digit')}</div>
                  <div>{t('auth_login_industry')}</div>
                  <div>{t('auth_login_business')}</div>
                </div>
              </span>
              <span>{t('auth_login_method')}</span>
            </div>
          </div>
        </div>
      </main>
      <Footer>{/* <p style={{ color: 'red' }}>华世界·科技创造新流通</p> */}</Footer>
      <video
        ref={videoEl}
        muted
        autoPlay
        className={styles.video}
        onPlaying={onPlaying}
        onEnded={onEnded}
      >
        <source src="https://img.huahuabiz.com/static/background/video.mp4" type="video/mp4" />
      </video>
      <Cancellation visibleCancellation={authStore.isCancellation} />
    </section>
  ) : (
    <div className={styles.box}>
      <div className={styles.center}>
        <div>{t('auth_login_loading')}</div>
        <Outlet />
      </div>
    </div>
  );
});

function Layout() {
  // 多语言配置对象 t
  const { t } = useTranslation();
  const location = useLocation();
  const [searchParams] = useSearchParams({
    next: encodeURIComponent(import.meta.env.BIZ_APP_HOME_URL || '/'),
  });

  useLayout(() => {
    if (!import.meta.env.SSR && !user.isLogin()) {
      user.clean();
      cleanPermission();
    }
  });

  if (user.isLogin()) {
    if (import.meta.env.BIZ_APP_PLATFORM_NO !== '3' || location.pathname !== '/auth/authorization')
      return <Navigate to={decodeURIComponent(searchParams.get('next') || '%2F')} />;
  }
  return (
    <>
      {import.meta.env.BIZ_APP_PLATFORM_NO === '0' && (
        // @ts-ignore
        <Helmet>
          <title>{t('auth_login_title')}</title>
        </Helmet>
      )}
      <AuthLayouts />
    </>
  );
}

export default Layout;
