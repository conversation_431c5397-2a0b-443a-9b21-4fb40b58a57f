import { makeAutoObservable, runInAction } from 'mobx';

export class AuthStore {
  title = '';

  phone = '';

  isCancellation = false;

  constructor() {
    makeAutoObservable(this);
  }

  setTitle(title: string): void {
    this.title = title;
  }

  setPhone(phone: string): void {
    this.phone = phone;
  }

  setCancellation(isCancellation: boolean): void {
    runInAction(() => {
      this.isCancellation = isCancellation;
    });
  }
}

export const authStore = new AuthStore();
