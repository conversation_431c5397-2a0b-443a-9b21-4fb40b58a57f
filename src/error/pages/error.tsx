import { useMemo } from 'react';
import { useParams } from 'react-router-dom';
import { Context } from '@/components';
import { Button } from 'antd';
import { useBack } from '@/hooks';
import styles from './error.module.less';

function Error() {
  const { statusCode = '404' } = useParams<{ statusCode: string }>();
  const [onBack] = useBack();
  const message = useMemo(() => {
    const messages: Record<string, string> = {
      '404': '页面不存在',
      '500': '服务器错误',
    };
    return messages[statusCode] || '未知错误';
  }, [statusCode]);

  return (
    <Context container className={styles.body}>
      <div className="text-center">
        <h1 className={styles.title}>{statusCode}</h1>
        <div className="mb-2">{message}</div>
        <Button type="primary" size="small" onClick={onBack}>
          返回上一页
        </Button>
      </div>
    </Context>
  );
}

export default Error;
